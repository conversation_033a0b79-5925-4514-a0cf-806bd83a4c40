package common

import (
	v1 "asdsec.com/asec/platform/api/ipc/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"bufio"
	"bytes"
	"encoding/binary"
	"fmt"
	"github.com/golang/protobuf/proto"
	"io"
	"net"
	"os"
	"strconv"
)

var (
	localHostConn net.Conn
	hostErr       error
	port          int
)

func Connect(port int) bool {
	strport := strconv.Itoa(port)
	localHostConn, hostErr = net.Dial("tcp", "127.0.0.1:"+strport)
	if hostErr != nil {
		global.Logger.Sugar().Errorf("connect to host 127.0.0.1:%v failed", strport)
		return false
	}
	SendOnlineState(v1.HostMessage_SIDECAR_ONLINE, v1.HostMessage_HT_CLIENTANDSIDECAR)
	go handleMsg()
	return true
}

func Disconnect() {
	localHostConn.Close()
}

func SendOnlineState(state v1.HostMessage_State, hostType v1.HostMessage_HostType) {
	msg := v1.HostMessage{
		HostType:     hostType,
		ProcessState: state,
	}

	out, err := proto.Marshal(&msg)
	if err != nil {
		fmt.Println("failed to encode msg")
		return
	}

	sendMsg(out)
}

func SendAct(act v1.HostMessage_Actions, hostType v1.HostMessage_HostType) {
	msg := v1.HostMessage{
		HostType: hostType,
		Action:   act,
	}

	out, err := proto.Marshal(&msg)
	if err != nil {
		fmt.Println("failed to encode msg")
		return
	}

	sendMsg(out)
}

func sendMsg(msg []byte) {

	// 读取消息长度以当做信息包头 统一定义数据包前4个字节为包头 int32
	var packageLength = int32(len(msg))
	var pkg = new(bytes.Buffer)

	// 写入消息头
	err := binary.Write(pkg, binary.LittleEndian, packageLength)
	if err != nil {
		return
	}

	// 写入消息实体
	err = binary.Write(pkg, binary.LittleEndian, msg)
	if err != nil {
		return
	}

	localHostConn.Write(pkg.Bytes())
}

func handleMsg() {
	for {
		reader := bufio.NewReader(localHostConn)

		// 读取包头,获取pkg大小
		lengthByte, _ := reader.Peek(4)
		lengthbuffer := bytes.NewBuffer(lengthByte)

		var length int32
		err := binary.Read(lengthbuffer, binary.LittleEndian, &length)
		if err == io.EOF {
			global.Logger.Sugar().Errorf("shut down...%v", err)
			os.Exit(0)
			return
		}

		if err != nil {
			continue
		}

		if int32(reader.Buffered()) < length+4 {
			// 数据包不合法，不做操作
			continue
		}

		pkg := make([]byte, int(length+4))
		_, err = reader.Read(pkg)
		if err != nil {
			// 读取pkg失败，不做操作
			continue
		}

		msg := v1.HostMessage{}
		proto.Unmarshal(pkg[4:], &msg)
		if msg.Action == v1.HostMessage_ACT_SHUTDOWN_SIDECAR {
			global.Logger.Sugar().Infof("shut down sidecar")
			os.Exit(0)
			return
		}
	}
}

func HandSync() {
	if !Connect(global.RpcPort) {
		return
	}
}
