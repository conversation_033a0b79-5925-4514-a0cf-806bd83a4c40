// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbIdpGroupMapper = "tb_idp_group_mapper"

// TbIdpGroupMapper mapped from table <tb_idp_group_mapper>
type TbIdpGroupMapper struct {
	ProviderID string    `gorm:"column:provider_id;not null" json:"provider_id"` // 认证服务器 id
	GroupID    string    `gorm:"column:group_id;not null" json:"group_id"`       // 顶层目录id
	CorpID     string    `gorm:"column:corp_id" json:"corp_id"`
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbIdpGroupMapper's table name
func (*TbIdpGroupMapper) TableName() string {
	return TableNameTbIdpGroupMapper
}
