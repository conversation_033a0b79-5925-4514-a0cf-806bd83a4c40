// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: accesslog/v1/accesslog.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AccessLogService_AccessLog_FullMethodName = "/api.accesslog.AccessLogService/AccessLog"
)

// AccessLogServiceClient is the client API for AccessLogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccessLogServiceClient interface {
	// AccessLog 网关接入日志
	AccessLog(ctx context.Context, opts ...grpc.CallOption) (AccessLogService_AccessLogClient, error)
}

type accessLogServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccessLogServiceClient(cc grpc.ClientConnInterface) AccessLogServiceClient {
	return &accessLogServiceClient{cc}
}

func (c *accessLogServiceClient) AccessLog(ctx context.Context, opts ...grpc.CallOption) (AccessLogService_AccessLogClient, error) {
	stream, err := c.cc.NewStream(ctx, &AccessLogService_ServiceDesc.Streams[0], AccessLogService_AccessLog_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &accessLogServiceAccessLogClient{stream}
	return x, nil
}

type AccessLogService_AccessLogClient interface {
	Send(*AccessLogMsgList) error
	CloseAndRecv() (*AccessLogReply, error)
	grpc.ClientStream
}

type accessLogServiceAccessLogClient struct {
	grpc.ClientStream
}

func (x *accessLogServiceAccessLogClient) Send(m *AccessLogMsgList) error {
	return x.ClientStream.SendMsg(m)
}

func (x *accessLogServiceAccessLogClient) CloseAndRecv() (*AccessLogReply, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(AccessLogReply)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// AccessLogServiceServer is the server API for AccessLogService service.
// All implementations must embed UnimplementedAccessLogServiceServer
// for forward compatibility
type AccessLogServiceServer interface {
	// AccessLog 网关接入日志
	AccessLog(AccessLogService_AccessLogServer) error
	mustEmbedUnimplementedAccessLogServiceServer()
}

// UnimplementedAccessLogServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccessLogServiceServer struct {
}

func (UnimplementedAccessLogServiceServer) AccessLog(AccessLogService_AccessLogServer) error {
	return status.Errorf(codes.Unimplemented, "method AccessLog not implemented")
}
func (UnimplementedAccessLogServiceServer) mustEmbedUnimplementedAccessLogServiceServer() {}

// UnsafeAccessLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccessLogServiceServer will
// result in compilation errors.
type UnsafeAccessLogServiceServer interface {
	mustEmbedUnimplementedAccessLogServiceServer()
}

func RegisterAccessLogServiceServer(s grpc.ServiceRegistrar, srv AccessLogServiceServer) {
	s.RegisterService(&AccessLogService_ServiceDesc, srv)
}

func _AccessLogService_AccessLog_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AccessLogServiceServer).AccessLog(&accessLogServiceAccessLogServer{stream})
}

type AccessLogService_AccessLogServer interface {
	SendAndClose(*AccessLogReply) error
	Recv() (*AccessLogMsgList, error)
	grpc.ServerStream
}

type accessLogServiceAccessLogServer struct {
	grpc.ServerStream
}

func (x *accessLogServiceAccessLogServer) SendAndClose(m *AccessLogReply) error {
	return x.ServerStream.SendMsg(m)
}

func (x *accessLogServiceAccessLogServer) Recv() (*AccessLogMsgList, error) {
	m := new(AccessLogMsgList)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// AccessLogService_ServiceDesc is the grpc.ServiceDesc for AccessLogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccessLogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.accesslog.AccessLogService",
	HandlerType: (*AccessLogServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "AccessLog",
			Handler:       _AccessLogService_AccessLog_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "accesslog/v1/accesslog.proto",
}
