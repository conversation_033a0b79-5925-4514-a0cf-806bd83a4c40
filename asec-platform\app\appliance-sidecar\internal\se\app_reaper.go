package se

import (
	"context"
	"math/rand"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/service"
	"asdsec.com/asec/platform/app/appliance-sidecar/service/app_matcher"
	"asdsec.com/asec/platform/pkg/utils"
	"google.golang.org/grpc"
)

// AppReaper 定时更新网关应用缓存
func AppReaper(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()
	defer utils.RecoverSideCarPanic(global.Logger, AppReaper, ctx, wg)
	for {
		conn, err := connection.GetPlatformConnection(ctx)
		if err != nil {
			select {
			case <-ctx.Done():
				return
			case <-time.After(time.Second * 5):
				continue
			}
		}
		refreshApp(ctx, conn)
		connection.CloseConnection(conn)
	}
}

func refreshApp(ctx context.Context, conn *grpc.ClientConn) {
	// 网关取30s间隔，3s的标准正态随机差错开请求时间
	randTime := rand.NormFloat64()*3 + 30
	for {
		getAppReq := pb.SeGetAppReq{
			ApplianceId: global.ApplianceID,
		}
		appResp, err := pb.NewAppClient(conn).SeGetApp(ctx, &getAppReq)
		if err == nil {
			service.IpAppMatcher = service.IpAppMatcher[:0]
			service.DomainAppMatcher = service.DomainAppMatcher[:0]
			// 每次重新覆写缓存
			for _, info := range appResp.GetApps() {
				info.Address = strings.ToLower(info.Address)
				// 缓存 url-应用 匹配器
				matcher, err := app_matcher.GetAppMatcher(info)
				if err != nil {
					global.Logger.Sugar().Errorf("getAppMatcher err:%v", err)
				}
				if matcher.GetMatcherType() == app_matcher.IpMatcher || matcher.GetMatcherType() == app_matcher.IpCidrMatcher {
					service.IpAppMatcher = append(service.IpAppMatcher, matcher)
				}
				if matcher.GetMatcherType() == app_matcher.DomainMatcher || matcher.GetMatcherType() == app_matcher.DomainWildcardMatcher {
					service.DomainAppMatcher = append(service.DomainAppMatcher, matcher)
				}
			}
			global.Logger.Debug("ipAppMatcher cache", zap.Any("cache", service.IpAppMatcher))
			global.Logger.Debug("ipAppMatcher cache", zap.Any("cache", service.DomainAppMatcher))
		}
		select {
		case <-ctx.Done():
			return
		case <-time.After(time.Second * time.Duration(randTime)):
			continue
		}
	}
}

// RouteTimer 定时更新网关应用缓存
func RouteTimer(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()
	defer utils.RecoverSideCarPanic(global.Logger, AppReaper, ctx, wg)
	for {
		conn, err := connection.GetPlatformConnection(ctx)
		if err != nil {
			select {
			case <-ctx.Done():
				return
			case <-time.After(time.Second * 5):
				continue
			}
		}
		refreshRouter(ctx, conn)
	}
}

func refreshRouter(ctx context.Context, conn *grpc.ClientConn) {
	// 网关取30s间隔，3s的标准正态随机差错开请求时间
	randTime := rand.NormFloat64()*3 + 30
	for {
		getAppReq := pb.SERouteReq{
			//CorpId: global.
			ApplianceId: global.ApplianceID,
		}
		//拉取路由
		appResp, err := pb.NewAppClient(conn).SERoute(ctx, &getAppReq)
		if err == nil {
			// 每次重新覆写缓存
			service.SERouteCache = appResp
		}

		select {
		case <-ctx.Done():
			return
		case <-time.After(time.Second * time.Duration(randTime)):
			continue
		}
	}
}
