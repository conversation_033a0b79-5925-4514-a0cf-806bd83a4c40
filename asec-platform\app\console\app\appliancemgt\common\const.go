package common

import (
	"fmt"
	"strconv"
	"strings"
)

const (
	OptName             = "otp"
	MacPlatformType     = "darwin"
	LinuxPlatformType   = "linux"
	WindowsPlatformType = "windows"
	AndroidPlatformType = "android"
	IosPlatformType     = "ios"
	AllPlatform         = "all"
	VersionInfoFile     = "config.json"
	ProductMode         = "product"
	DeployModePublic    = "public"
	DeployModePrivate   = "private"
	AgentsDir           = "agents"

	CommonTimeFormat = "2006-01-02 15:04:05"
	TimeZero         = "1970-01-01 08:00:00"

	DefaultTenantID = "0"
	DefaultVersion  = "wait_to_set"
	VersionUnknown  = "unknown"

	DurationUnitWeek   = "week"
	DurationUnitDay    = "day"
	DurationUnitHour   = "hour"
	DurationUnitMinute = "minute"

	UpdateLogFailed     = "failed"
	UpdateLogSuccess    = "success"
	UpdateLogPending    = "pending"
	UpdateLogProcessing = "processing"

	GrayModeRandom = "random"
	GrayModeCustom = "custom"
	SourceServer   = "server"
	SourceClient   = "client"

	OtherVersion = "others"

	IOSAppStoreUrl = "https://apps.apple.com/us/app/asec/id6475376303"

	AgentBindUserConfType = "agent_bind_user_conf"
)

const UninstallTaskType = "UNINSTALL"

const OnlineLogType = "ONLINE"

const OfflineLogType = "OFFLINE"

const UpgradeLogType = "UPGRADE"

var QueryList = []string{UninstallTaskType, OnlineLogType, OfflineLogType, UpgradeLogType}

const UninstallTimeout = 600
const StatusStarted = "started"
const StatusFailed = "failed"
const StatusSuccess = "success"

const OfflineStatus = "offline"

const OnlineStatus = "online"

const OfflineCheckInterval = 10

// AgentUserTypeTemp 终端临时用户类型
const AgentUserTypeTemp = 0

// AgentUserTypeBind 手动绑定用户类型
const AgentUserTypeBind = 1

func GetAgentNameByPlatform(platform string) string {
	agentFileName := ""
	if platform == MacPlatformType {
		agentFileName = AgentDarwinFileName
	} else if platform == WindowsPlatformType {
		agentFileName = AgentWinFileName
	} else if platform == AndroidPlatformType {
		agentFileName = AgentAndroidFileName
	}

	return agentFileName
}

func FormatWebFileName(platform, version, ip string, port int) string {
	var addr string
	if ip == "" {
		addr = ""
	} else {
		addr = ip + "@" + strconv.Itoa(port)
	}

	mainVersion := strings.Split(version, " ")[0]

	if platform == MacPlatformType {
		return fmt.Sprintf(AgentDarwinFileDownloadTemp, mainVersion, addr)
	} else if platform == WindowsPlatformType {
		return fmt.Sprintf(AgentWinFileDownloadTemp, mainVersion, addr)
	} else if platform == AndroidPlatformType {
		return fmt.Sprintf(AgentAndroidFileDownloadTemp, mainVersion, addr)
	} else {
		return ""
	}

}
