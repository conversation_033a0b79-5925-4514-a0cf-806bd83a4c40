package dto

import (
	"asdsec.com/asec/platform/pkg/model"
	"github.com/lib/pq"
)

type ApplicationResp struct {
	model.Application
	BindSE []SEInfo `json:"bind_se"`
}

// SEInfo 页面上绑定的设备ID,实际为网关ID或者connectorID
type SEInfo struct {
	ApplianceID   uint64 `gorm:"column:appliance_id" json:"appliance_id,string"`
	ApplianceName string `gorm:"column:app_name" json:"appliance_name"`
}

type ApplicationReq struct {
	AppName             string             `json:"app_name" binding:"required"`
	AppDescribe         string             `json:"app_desc" binding:"omitempty"`
	GroupID             uint64             `json:"group_id" binding:"required"`
	AppSites            []model.AppAddress `json:"app_sites" binding:"required"`
	WebUrl              string             `json:"web_url" binding:"omitempty"`
	SeApp               []SeApps           `json:"se_app" binding:"omitempty"`
	IconURL             string             `json:"icon_url"`
	AppStatus           int                `json:"app_status" `
	TcpPort             string             `json:"tcp_port"`
	UdpPort             string             `json:"udp_port"`
	GroupIds            pq.Int64Array      `json:"group_ids"`
	WebCompatibleConfig CompatibleConfig   `json:"web_compatible_config"`
	HealthConfig        HealthConfig       `json:"health_config"`
	PortalDesc          string             `json:"portal_desc"`
	PortalShowName      string             `json:"portal_show_name"`
	ShowStatus          int                `json:"show_status"`
	FormFillEnabled     int                `json:"form_fill_enabled"`
	CertificateId       string             `json:"certificate_id"`
	OpenConfig          OpenConfig         `json:"open_config"`
}

type PortalAppReq struct {
	AppName        string        `json:"app_name" binding:"required"`
	AppStatus      int           `json:"app_status" `
	GroupIds       pq.Int64Array `json:"group_ids"`
	ShowStatus     int           `json:"show_status"`
	IconURL        string        `json:"icon_url"`
	WebUrl         string        `json:"web_url" binding:"omitempty"`
	PortalShowName string        `json:"portal_show_name"`
	PortalDesc     string        `json:"portal_desc"`
	OpenConfig     OpenConfig    `json:"open_config"`
}

type UpdateApplicationReq struct {
	ID uint64 `json:"id,string" binding:"required"`
	ApplicationReq
}

type SeApps struct {
	AppId       uint64 `json:"app_id"`
	SeId        string `json:"se_id"`
	AppType     int32  `json:"app_type"`
	ConnectorId string `json:"connector_id"`
}

type ApplicationCount struct {
	OnlineCount int `gorm:"column:online_count" json:"online_count"`
	TotalCount  int `gorm:"column:total_count" json:"total_count"`
}

// GroupSort 分组排序
type GroupSort struct {
	ID    string `json:"id" binding:"required"`    // 分组ID
	Order int    `json:"order" binding:"required"` // 排序序号
}

// UpdateGroupSortReq 更新分组排序请求
type UpdateGroupSortReq struct {
	Groups []GroupSort `json:"groups" binding:"required"`
}

// GroupMoveReq 分组移动请求
type GroupMoveReq struct {
	GroupID  uint64 `json:"group_id" binding:"required"`                               // 要移动的分组ID
	TargetID uint64 `json:"target_id"`                                                 // 目标位置的分组ID
	Position string `json:"position" binding:"required,oneof=first last before after"` // 移动位置
}

type GetApplicationByCategoryRsp struct {
	Categories []CategoryApplications `json:"categories"`
}

type CategoryApplications struct {
	Category string         `json:"category"`
	Apps     []AppBasicInfo `json:"apps"`
}

type AppBasicInfo struct {
	ID              string             `json:"id"` // 改为字符串类型以避免JavaScript大整数精度丢失
	Name            string             `json:"app_name"`
	AppType         string             `json:"app_type"`
	AppStatus       int                `json:"app_status"`
	Icon            string             `json:"icon"`
	PortalShowName  string             `json:"portal_show_name"`
	PortalDesc      string             `json:"portal_desc"`
	GroupName       string             `json:"GroupName"`
	ServerAddress   string             `json:"ServerAddress"`
	WebUrl          string             `json:"WebUrl"`
	HealthStatus    int                `json:"health_status"`
	AppAddresses    []model.AppAddress `json:"app_sites" binding:"required"`
	FormFillEnabled int                `json:"form_fill_enabled"`
	OpenConfig      OpenConfig         `json:"open_config"`
}
