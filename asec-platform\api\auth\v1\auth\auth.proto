syntax = "proto3";

package api.auth.v1.auth;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "asdsec.com/asec/platform/api/auth/v1/auth;auth";
option java_multiple_files = true;
option java_package = ".api.auth.v1.auth";

service Auth {
  rpc Login (LoginRequest) returns (LoginReply){
    option (google.api.http) = {
      post: "/auth/login/v1/user",
      body: "*"
    };
  }

  rpc GetToken(GetTokenRequest) returns(GetTokenReply){
    option (google.api.http) = {
      get: "/auth/login/v1/user/token",
    };
  }

  rpc ListMainIDP (ListMainIDPRequest) returns(ListMainIDPReply){
    option (google.api.http) = {
      get: "/auth/login/v1/user/main_idp/list"
    };
  };

  rpc RefreshToken (RefreshTokenRequest) returns(GetTokenReply){
    option (google.api.http) = {
      get: "/auth/authz/v1/user/refresh_token"
    };
  };

  rpc ThirdLogin (ThirdLoginRequest) returns(LoginReply){
    option (google.api.http) = {
      post: "/auth/login/v1/user/third",
      body: "*"
    };
  };

  rpc Cache (CacheRequest) returns(CacheReply){
    option (google.api.http) = {
      post: "/auth/login/v1/cache",
      body: "*"
    };
  };

  rpc SendSms (SendSmsRequest) returns(SendSmsReply){
    option (google.api.http) = {
      post: "/auth/login/v1/send_sms",
      body: "*"
    };
  };

  rpc SmsVerify (SmsVerifyRequest) returns(LoginReply){
    option (google.api.http) = {
      post: "/auth/login/v1/sms_verify",
      body: "*"
    };
  };

  rpc TokenVerify (TokenVerifyRequest) returns(TokenVerifyReply){
    option (google.api.http) = {
      get: "/auth/login/v1/token_verify",
    };
  };

  rpc GetSendSmsKey (SendSmsKeyRequest) returns(SendSmsKeyReply){
    option (google.api.http) = {
      post: "/auth/login/v1/sms_key",
      body: "*"
    };
  };

  rpc AdmsLogin (AdmsLoginRequest) returns(AdmsLoginReply){
    option (google.api.http) = {
      post: "/auth/login/v1/adms",
      body: "*"
    };
  };

  rpc UserBind (UserBindRequest) returns (UserBindReply) {
    option (google.api.http) = {
      post: "/auth/login/v1/userbind",
      body: "*"
    };
  }

  rpc OAuth2Callback (OAuth2CallbackRequest) returns (LoginReply) {
    option (google.api.http) = {
      get: "/auth/login/v1/callback/{idp_id}",
      additional_bindings {
        post: "/auth/login/v1/callback/{idp_id}",
        body: "*"
      }
    };
  }
  
  // 通用认证回调统一入口
  rpc AuthCallback (AuthCallbackRequest) returns (LoginReply) {
    option (google.api.http) = {
      post: "/auth/login/v1/callback",
      body: "*"
    };
  }
};

enum StatusCode {
  SUCCESS = 0;
  FAILED = 1;
}
message AdmsLoginRequest{
  string server_addr = 1;
  string entry = 2;
  string user_name = 3;
}
message TokenInfo {
  string token = 1;
  string refresh_token = 2;
  string realm = 3;
}
message AdmsLoginReply{
  TokenInfo token_info = 1;
  bool adms_check = 2;
}
message TokenVerifyRequest{
  string redirect_url = 1;
  string host_url = 2;
}
message TokenVerifyReply{
  string user_id = 1;
  string user_name = 2;
  repeated string sms_idp = 3;
}

message SendSmsKeyRequest{
  string user_id = 1;
  string idp_id = 2;
}

message SendSmsKeyReply{
    SmsInfo data = 1;
}
message SmsInfo{
  bool not_phone = 1;
  string uniq_key = 2;
  string user_name = 3;
}
message LoginRequest {
  string user_name = 1;
  string password = 2;
  string idp_id = 3;
  string redirect_uri = 4;
  string client_id = 5; // 客户端应用id
  string grant_type = 6; // 授权类型
  string scope = 7; // 访问范围
  string activation_code = 8; // 激活码
  string encryption = 9;
}

message LoginReply {
  google.protobuf.Struct data = 1; // 根据不同的grant_type返回不同的结构
}

message GetTokenRequest{
  string code = 1;
  string grant_type = 2;
  string client_id = 3;
  string scope = 4;
}

message GetTokenReply{
  string access_token = 1;
  int64 expire_in = 2;
  string refresh_token = 3;
  int64 refresh_expire_in = 4;
  string token_type = 5;
}

message ListMainIDPRequest{
}

message ListMainIDPReply{
  message IDP{
    string id = 1;
    string name = 2;
    string avatar = 3;
    string type = 4;
    google.protobuf.Struct attrs = 5;
    string template_type = 6;
  }
  repeated IDP idp_list = 1;
}

message RefreshTokenRequest{

}

message ThirdLoginRequest{
  message AuthWebLogin{
    string auth_web_code = 1;
    string auth_web_token = 2;
  }
  string redirect_uri = 1; // 重定向uri
  string client_id = 2; // 客户端应用id
  string grant_type = 3; // 授权类型
  string scope = 4; // 访问范围
  string idp_id = 5; // 认证源配置id
  AuthWebLogin auth_web = 6; //第三方网页登录的登录参数
  string ad_username = 7;
  string ad_pwd = 8;
  string encryption = 9;
}

message CacheRequest{
  string type = 1;
  google.protobuf.Struct data = 2;
}

message CacheReply{
  string uniq_key = 1;
}

message SendSmsRequest{
  string uniq_key = 1;
  string idp_id = 2;
}

message SendSmsReply{
  StatusCode status = 1;
}

message SmsVerifyRequest{
  string uniq_key = 1;
  string auth_code = 2;
  string user_name = 3;
  string idp_id = 4;
  string redirect_uri = 5;
  string client_id = 6; // 客户端应用id
  string grant_type = 7; // 授权类型
  string scope = 8; // 访问范围
  string verify_type = 9;  // 可选值: "sms"、"email"如果为空则根据idp_id判断
  string totp_key = 10;
}

message UserBindRequest {
  string user_id = 1;  // 第三方用户ID
}

message UserBindReply {
  bool success = 1;
  string local_user_id = 2;   // 平台用户ID
  string local_user_name = 3; // 平台用户名
  string message = 4;
}

message OAuth2CallbackRequest {
  string code = 1;         // OAuth2授权码
  string idp_id = 2;       // 身份提供者ID
  string corp_id = 3;      // 企业ID
  string redirect_uri = 4; // 重定向URI
  string client_id = 5;    // 客户端ID
  string state = 6;        // 状态值
  string ticket = 7;       // Cas授权码
  string redirect = 8;     // 回跳地址
}

// 认证回调请求（通用）
message AuthCallbackRequest {
  string auth_code = 1;           // 认证授权码
  string auth_type = 2;           // 认证类型 (oauth2/micro_app/cas/saml等)
  string auth_provider = 3;       // 认证提供商 (dingtalk/wechat_work/feishu/google/github等)
  string corp_id = 4;             // 企业ID
  string idp_id = 5;              // 身份提供者ID
  string app_id = 6;              // 应用ID
  string redirect_uri = 7;        // 重定向URI
  string state = 8;               // 状态值
  string client_id = 9;           // 客户端ID
  int64 timestamp = 10;           // 时间戳
  map<string, string> extra_params = 11;  // 扩展参数
}
