package cas

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-cas/cas"
	"github.com/go-kratos/kratos/v2/log"
)

// CasProvider 实现多步Cas认证
type CasProvider struct {
	providerId string
	name       string
	logger     *log.Helper

	// Cas配置
	authConfig  CasConfig
	fieldMap    []dto.KV
	redirectUri string
	// 测试模式
	isTestMode bool // 是否为测试模式

	// CAS 客户端
	casClient *cas.Client
}

// Step 表示一个HTTP请求步骤
type CasConfig struct {
	AuthUrl string `json:"authUrl"`
	Version string `json:"version"`
}

// NewCasProvider 创建Cas提供商实例
func NewCasProvider(
	providerId string,
	name string,
	authData string,
	fieldMap []dto.KV,
	redirectUri string,
	logger log.Logger,
) (*CasProvider, error) {
	// 解析认证配置
	var authConfig CasConfig
	if err := json.Unmarshal([]byte(authData), &authConfig); err != nil {
		return nil, fmt.Errorf("解析配置失败: %v", err)
	}

	// 初始化 CAS 客户端
	url, _ := url.Parse(authConfig.AuthUrl)
	casClient := cas.NewClient(&cas.Options{URL: url})

	return &CasProvider{
		providerId:  providerId,
		name:        name,
		logger:      log.NewHelper(logger),
		authConfig:  authConfig,
		fieldMap:    fieldMap,
		redirectUri: redirectUri,
		casClient:   casClient,
	}, nil
}

// GetAuthorizationURL 获取授权URL
func (p *CasProvider) GetAuthorizationURL(ctx context.Context, state string, redirectURI string, isTest bool) (string, error) {
	// 设置测试模式标记
	p.isTestMode = isTest

	httpRequest, err := common.GetHttpRequest(ctx)
	if err != nil {
		return "", fmt.Errorf("无法从上下文中获取 HTTP 请求")
	}

	// 使用 CAS 客户端生成授权 URL
	parsedURL, err := url.Parse(redirectURI)
	if err != nil {
		return "", fmt.Errorf("解析重定向URI失败: %w", err)
	}
	httpRequest.URL = parsedURL
	authURL, err := p.casClient.LoginUrlForRequest(httpRequest)
	if err != nil {
		p.logger.Errorf("生成授权URL失败: %v", err)
		return "", fmt.Errorf("生成授权URL失败: %w", err)
	}

	if isTest {
		p.logger.Infof("测试模式,生成测试专用授权URL: %s", authURL)
	} else {
		p.logger.Infof("生成授权URL: %s", authURL)
	}

	return authURL, nil
}

// HandleCallback 处理Cas回调
func (p *CasProvider) HandleCallback(ctx context.Context, code string, state string) (*dto.ExternalUser, error) {
	httpRequest, err := common.GetHttpRequest(ctx)
	if err != nil {
		return nil, fmt.Errorf("无法从上下文中获取 HTTP 请求%w", err)
	}
	parsedURL, err := url.Parse(p.redirectUri)
	if err != nil {
		return nil, fmt.Errorf("解析重定向URI失败: %w", err)
	}
	httpRequest.URL = parsedURL
	var validateURL string
	switch p.authConfig.Version {
	case "1":
		// 处理1.0版本的回调
		validateURL, err = p.casClient.ValidateUrlForRequest(code, httpRequest)
	case "2":
		// 处理2.0版本的回调
		validateURL, err = p.casClient.ServiceValidateUrlForRequest(code, httpRequest)
	case "3":
		// 处理3.0版本的回调
		validateURL, err = p.casClient.ServiceValidateUrlForRequest(code, httpRequest)
		validateURL = strings.Replace(validateURL, "serviceValidate", "p3/proxyValidate", 1)
	default:
		return nil, fmt.Errorf("不支持的版本: %s", p.authConfig.Version)
	}
	if err != nil {
		return nil, fmt.Errorf("获取验证地址%w", err)
	}
	p.logger.Infof("获取验证地址: %s", validateURL)
	client := &http.Client{
		Timeout: time.Duration(10) * time.Second,
	}

	req, rerr := http.NewRequest("GET", validateURL, nil)
	if rerr != nil {
		return nil, fmt.Errorf("获取验证请求失败:%w", rerr)
	}
	resp, perr := client.Do(req)
	if perr != nil {
		return nil, fmt.Errorf("获取验证请求失败:%w", perr)
	}
	defer resp.Body.Close()

	// 5. 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("│ [响应] 读取响应失败: %v", err)
	}
	fmt.Printf("│ [响应] 响应内容: %s\n", string(respBody))
	// 修改解析响应部分以处理时间格式
	response, resErr := cas.ParseServiceResponse(respBody)
	if resErr != nil {
		// 处理时间解析错误
		if strings.Contains(resErr.Error(), "parsing time") {
			// 替换掉时区信息后再解析
			respBodyStr := string(respBody)
			respBodyStr = strings.ReplaceAll(respBodyStr, "[Asia/Shanghai]", "")
			respBody = []byte(respBodyStr)
			response, resErr = cas.ParseServiceResponse(respBody)
		}
		if resErr != nil {
			return nil, fmt.Errorf("│ [响应] 解析响应失败: %v", resErr)
		}
	}

	// 创建外部用户对象
	externalUser := &dto.ExternalUser{}
	userName := response.User
	attributes := response.Attributes
	if userName == "" {
		return nil, fmt.Errorf("│ [响应] 认证失败: %v", err)
	}
	userInfo := make(map[string]interface{})
	// 用户变量
	for k, v := range attributes {
		fmt.Printf("│ [响应] 用户属性: %s = %s\n", k, v[0])
	}
	for _, v := range p.fieldMap {
		// fmt.Println("│ [响应] 处理用户变量: ", v.Key, v.Value)
		if val, exists := attributes[v.Key]; exists {
			userInfo[v.Value] = val[0]
		}
	}

	// 尝试获取不同格式的字段值
	externalUser.Userid = userName
	externalUser.Name = userName
	name := p.extractStringField(userInfo, "Name")
	if name != "" {
		externalUser.Name = name
	}
	nickname := p.extractStringField(userInfo, "Nickname")
	if nickname != "" {
		externalUser.NickName = nickname
	}
	displayName := p.extractStringField(userInfo, "DisplayName")
	if displayName != "" {
		externalUser.DisplayName = displayName
	}
	trueName := p.extractStringField(userInfo, "RealName")
	if trueName != "" {
		externalUser.TrueName = trueName
	}
	email := p.extractStringField(userInfo, "Email")
	if email != "" {
		externalUser.Email = email
	}
	mobile := p.extractStringField(userInfo, "Mobile")
	if mobile != "" {
		externalUser.Mobile = mobile
	}
	mainDepartment := p.extractStringField(userInfo, "MainDepartment")
	if mainDepartment != "" {
		externalUser.MainDepartment = mainDepartment
	}
	fmt.Printf("│ [响应] Userid: %s\n", externalUser.Userid)
	fmt.Printf("│ [响应] Name: %s\n", externalUser.Name)
	fmt.Printf("│ [响应] Nickname: %s\n", externalUser.NickName)
	fmt.Printf("│ [响应] DisplayName: %s\n", externalUser.DisplayName)
	fmt.Printf("│ [响应] TrueName: %s\n", externalUser.TrueName)
	fmt.Printf("│ [响应] Email: %s\n", externalUser.Email)
	fmt.Printf("│ [响应] Mobile: %s\n", externalUser.Mobile)
	fmt.Printf("│ [响应] MainDepartment: %s\n", externalUser.MainDepartment)

	// 生成唯一键
	externalUser.UniqKey = generateUniqueKey(externalUser)

	// p.logger.Infof("转换后的用户信息: %v", externalUser)
	return externalUser, nil
}

// TestAuth 专用于测试Cas配置的方法
func (p *CasProvider) TestAuth(ctx context.Context) (map[string]interface{}, error) {
	// 设置为测试模式
	p.isTestMode = true
	testId, _ := common.GetTestIDFromCtx(ctx)
	idpId, _ := common.GetIdpIDFromCtx(ctx)

	// 创建包含测试标识的state
	testState := dto.SsoState{
		IsTest: "true",
		TestId: testId,
		Time:   fmt.Sprintf("%d", time.Now().Unix()),
	}
	jsonData, err := json.Marshal(testState)
	if err != nil {
		p.logger.Errorf("测试构建state失败: %v", err)
		return nil, fmt.Errorf("测试构建state失败: %v", err)
	}
	encodedState := base64.StdEncoding.EncodeToString([]byte(jsonData))

	// 生成授权URL
	if p.redirectUri == "" {
		scheme := common.GetClientScheme(ctx)
		hostPort := common.GetClientHostPortWithConfig(ctx)
		p.redirectUri = fmt.Sprintf("%s://%s/auth/login/v1/callback/%s", scheme, hostPort, idpId)
		p.logger.Infof("测试模式生成重定向URI: %s", p.redirectUri)
	}
	authURL, err := p.GetAuthorizationURL(ctx, encodedState, p.redirectUri, true)
	if err != nil {
		p.logger.Errorf("测试构建授权URL失败: %v", err)
		return nil, fmt.Errorf("测试授权URL构建失败: %v", err)
	}

	// 构建测试结果
	testResult := map[string]interface{}{
		"auth_url": authURL,
		"state":    encodedState,
		"test_id":  testId,
		"success":  false, // 初始为false，回调成功后设置为true
		"logs": []string{
			"┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
			"┃ 测试已开始,已生成授权URL,等待用户授权...",
			"┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
		},
	}

	return testResult, nil
}

// extractStringField 以统一方式从userInfo中提取字符串字段
func (p *CasProvider) extractStringField(userInfo map[string]interface{}, fieldName string) string {
	// p.logger.Infof("正在从userInfo中提取字段: %s", fieldName)

	// 首先尝试标准格式 {User.XXX}
	key := "{User." + fieldName + "}"
	if val, exists := userInfo[key]; exists {
		// 类型转换为字符串
		return p.convertToString(val)
	}

	// 然后尝试不带花括号的格式 (有些实现可能直接用字段名)
	if val, exists := userInfo[fieldName]; exists {
		// 类型转换为字符串
		return p.convertToString(val)
	}

	return ""
}

// generateUniqueKey 生成用户唯一标识
func generateUniqueKey(user *dto.ExternalUser) string {
	// 实现生成用户唯一标识的逻辑
	return user.Userid
}

// convertToString 将任意类型转换为字符串
func (p *CasProvider) convertToString(val interface{}) string {
	if val == nil {
		return ""
	}

	switch v := val.(type) {
	case string:
		return v
	case float64:
		// 对于整数形式的浮点数，去掉小数部分
		if v == float64(int64(v)) {
			return fmt.Sprintf("%d", int64(v))
		}
		return fmt.Sprintf("%g", v)
	case int, int64, int32, int8:
		return fmt.Sprintf("%d", v)
	case bool:
		return fmt.Sprintf("%t", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// 打印对象
func (p *CasProvider) Print() {
	fmt.Printf("CasProvider: %s\n", p.providerId)
	fmt.Printf("AuthUrl: %s\n", p.authConfig.AuthUrl)
	fmt.Printf("Version: %s\n", p.authConfig.Version)
	fmt.Printf("FieldMap: %v\n", p.fieldMap)
	fmt.Printf("RedirectUri: %s\n", p.redirectUri)
	fmt.Printf("IsTestMode: %t\n", p.isTestMode)
}
