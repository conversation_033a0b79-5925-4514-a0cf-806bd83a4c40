package se

import (
	"context"
	"fmt"
	"sync"
	"time"

	"asdsec.com/asec/platform/pkg/apisix"

	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"google.golang.org/grpc"
)

// SSO配置缓存结构
type SSOConfigCache struct {
	mu         sync.RWMutex
	configs    map[string]*SSOIDPConfig // key: idp_type, value: config
	lastUpdate time.Time
}

// SSO IDP配置结构
type SSOIDPConfig struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Type      string `json:"type"`
	CorpId    string `json:"corp_id"`
	AppId     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
	AppKey    string `json:"app_key"`
	Enable    bool   `json:"enable"`
}

// 全局SSO配置缓存
var ssoConfigCache = &SSOConfigCache{
	configs: make(map[string]*SSOIDPConfig),
}

// SSOConfig SSO配置结构
type SSOConfig struct {
	CorpId    string `json:"corp_id"`
	AppId     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
	IdpType   string `json:"idp_type"`
	IdpId     string `json:"idp_id"`
}

// getSSOConfigFromAPI 从API动态获取SSO配置
func getSSOConfigFromAPI(conn *grpc.ClientConn, ctx context.Context, appId string) (*SSOConfig, error) {
	// 调用WebGatewayRsSSOIDP接口获取SSO配置
	req := &pb.WebGatewayRsSSOIDPReq{
		Type: "", // 不指定类型，获取所有类型
	}

	resp, err := pb.NewAppClient(conn).WebGatewayRsSSOIDP(ctx, req)
	if err != nil {
		global.Logger.Sugar().Errorf("调用WebGatewayRsSSOIDP失败: %v", err)
		return nil, err
	}

	if len(resp.Idps) == 0 {
		return nil, fmt.Errorf("未找到可用的SSO IDP配置")
	}

	// 使用第一个可用的IDP配置
	idp := resp.Idps[0]

	config := &SSOConfig{
		CorpId:    idp.CorpId,
		AppId:     idp.AppId,
		AppSecret: idp.AppSecret,
		IdpType:   idp.Type,
		IdpId:     idp.Id,
	}

	global.Logger.Sugar().Debugf("从API获取SSO配置成功: type=%s, corp_id=%s, app_id=%s, idp_id=%s",
		config.IdpType, config.CorpId, config.AppId, config.IdpId)

	return config, nil
}

// 获取微应用SSO配置（根据指定的IDP类型）
func getSSOConfigForMicroApp(webConf apisix.CompatibleConfig) *SSOConfig {
	// 获取配置中指定的IDP类型
	var targetIdpType string
	if webConf.SingleSignOn != nil && webConf.SingleSignOn.Config.IdpType != "" {
		targetIdpType = webConf.SingleSignOn.Config.IdpType
	}

	// 如果未指定IDP类型，直接返回nil
	if targetIdpType == "" {
		global.Logger.Sugar().Warnf("微应用配置中未指定idp_type")
		return nil
	}

	// 从缓存获取指定类型的配置
	if config, exists := getSSOConfigFromCache(targetIdpType); exists && config.Enable {
		global.Logger.Sugar().Debugf("从缓存获取SSO配置: type=%s, name=%s", targetIdpType, config.Name)
		return &SSOConfig{
			CorpId:    config.CorpId,
			AppId:     config.AppId,
			IdpType:   config.Type,
			IdpId:     config.ID,
			AppSecret: config.AppSecret,
		}
	}

	// 缓存中未找到指定类型的配置
	global.Logger.Sugar().Warnf("缓存中未找到指定类型的SSO配置: type=%s", targetIdpType)
	return nil
}

// 启动 SSO 配置同步
func StartSSOConfigSync(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   syncSSOConfig,
		RunType:      common.SimpleSend,
		WaitSecond:   300, // 每5分钟同步一次
		RandomOffset: 10,
	}
	common.Send(param)
}

// 同步 SSO 配置的实际函数
func syncSSOConfig(conn *grpc.ClientConn, ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	global.Logger.Sugar().Debugf("开始同步 SSO 配置")

	// 调用 WebGatewayRsSSOIDP API
	req := &pb.WebGatewayRsSSOIDPReq{
		Type: "", // 获取所有类型
	}

	resp, err := pb.NewAppClient(conn).WebGatewayRsSSOIDP(ctx, req)
	if err != nil {
		global.Logger.Sugar().Errorf("同步 SSO 配置失败: %v", err)
		return err
	}

	// 更新缓存
	ssoConfigCache.mu.Lock()
	defer ssoConfigCache.mu.Unlock()

	// 清空旧配置
	ssoConfigCache.configs = make(map[string]*SSOIDPConfig)

	// 添加新配置
	for _, idp := range resp.Idps {
		config := &SSOIDPConfig{
			ID:        idp.Id,
			Name:      idp.Name,
			Type:      idp.Type,
			CorpId:    idp.CorpId,
			AppId:     idp.AppId,
			AppSecret: idp.AppSecret,
			AppKey:    idp.AppKey,
			Enable:    idp.Enable,
		}
		ssoConfigCache.configs[idp.Type] = config
	}

	ssoConfigCache.lastUpdate = time.Now()

	global.Logger.Sugar().Debugf("SSO 配置同步成功，共 %d 个配置: %v",
		len(ssoConfigCache.configs),
		func() []string {
			var types []string
			for t := range ssoConfigCache.configs {
				types = append(types, t)
			}
			return types
		}())

	return nil
}

// 获取缓存的 SSO 配置
func getSSOConfigFromCache(idpType string) (*SSOIDPConfig, bool) {
	ssoConfigCache.mu.RLock()
	defer ssoConfigCache.mu.RUnlock()

	config, exists := ssoConfigCache.configs[idpType]
	return config, exists
}
