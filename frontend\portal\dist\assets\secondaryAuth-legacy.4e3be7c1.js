/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
System.register(["./verifyCode-legacy.4f3db1c6.js","./index-legacy.b871e767.js"],(function(t,e){"use strict";var a,n,r,i,u,o,c,d,l,s,f,h,p,v,m,y,x=document.createElement("style");return x.textContent='@charset "UTF-8";.secondary-auth-overlay[data-v-fdda650f]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.5);z-index:1000;display:flex;justify-content:center;align-items:center}.secondary-auth-container[data-v-fdda650f]{background:#fff;padding:40px;border-radius:4px;box-shadow:0 2px 12px rgba(0,0,0,.1);min-width:340px;max-width:90%}.auth-selector .title[data-v-fdda650f]{height:60px;font-size:24px;text-align:center;margin-bottom:20px}.auth-selector .auth-methods[data-v-fdda650f]{display:flex;justify-content:center;flex-wrap:wrap;gap:20px;margin-bottom:20px}.auth-selector .auth-method-card[data-v-fdda650f]{width:120px;height:120px;cursor:pointer;transition:all .3s}.auth-selector .auth-method-card[data-v-fdda650f]:hover{transform:translateY(-5px);box-shadow:0 5px 15px rgba(0,0,0,.1)}.auth-selector .auth-method-content[data-v-fdda650f]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.auth-selector .auth-method-name[data-v-fdda650f]{margin-top:10px;font-size:14px}.auth-selector .selector-footer[data-v-fdda650f]{text-align:center;margin-top:20px}\n',document.head.appendChild(x),{setters:[function(t){a=t.default},function(t){n=t._,r=t.r,i=t.c,u=t.h,o=t.a,c=t.b,d=t.d,l=t.F,s=t.A,f=t.k,h=t.w,p=t.j,v=t.t,m=t.i,y=t.l}],execute:function(){var e={class:"secondary-auth-overlay"},x={class:"secondary-auth-container"},g={key:0,class:"auth-selector"},b={class:"auth-methods"},k={class:"auth-method-content"},w={class:"icon","aria-hidden":"true"},j=["xlink:href"],_={class:"auth-method-name"},C={class:"selector-footer"},I=Object.assign({name:"SecondaryAuth"},{props:{authMethods:{type:Array,default:function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:!0},{type:"email",name:"邮箱验证",icon:"email",available:!0}]}},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup:function(t,n){var I=n.emit,S=t,q=r(!0),z=r(null),A=i((function(){return S.authMethods.filter((function(t){return t.available}))})),F=function(t){z.value=t,q.value=!1};logger.log("双因子认证方式个数:"+A.value.length),1===A.value.length&&F(A.value[0]);var M=I,N=function(){M("cancel")},O=function(t){"client"===route.query.type&&(t.clientParams={type:"client",wp:route.query.wp||"50001"}),M("verification-success",t)};return function(n,r){var i=u("base-avatar"),I=u("base-card"),S=u("base-button");return o(),c("div",e,[d("div",x,[q.value?(o(),c("div",g,[r[3]||(r[3]=d("h2",{class:"title"},"请选择二次认证方式",-1)),d("div",b,[(o(!0),c(l,null,s(A.value,(function(t){return o(),f(I,{key:t.type,class:"auth-method-card",onClick:function(e){return F(t)}},{default:h((function(){return[d("div",k,[p(i,null,{default:h((function(){return[(o(),c("svg",w,[d("use",{"xlink:href":"#icon-auth-"+t.icon},null,8,j)]))]})),_:2},1024),d("div",_,v(t.name),1)])]})),_:2},1032,["onClick"])})),128))]),d("div",C,[p(S,{type:"info",onClick:r[0]||(r[0]=function(){return N()})},{default:h((function(){return r[2]||(r[2]=[m("取消")])})),_:1,__:[2]})])])):y("",!0),!q.value&&z.value?(o(),f(a,{key:1,"auth-info":t.authInfo,"auth-id":t.authId,"user-name":t.userName,"last-id":t.lastId,"secondary-type":z.value.type,onVerificationSuccess:O,onBack:r[1]||(r[1]=function(t){return q.value=!0}),onCancel:N},null,8,["auth-info","auth-id","user-name","last-id","secondary-type"])):y("",!0)])])}}});t("default",n(I,[["__scopeId","data-v-fdda650f"]]))}}}));
