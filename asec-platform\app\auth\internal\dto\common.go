package dto

import "asdsec.com/asec/platform/app/auth/internal/data/model"

const (
	CommonTimeFormat = "2006-01-02 15:04:05"
	TimeZero         = "1970-01-01 08:00:00"
	DefaultDay       = "2006-01-02"
)

const (
	DefaultLimit  = 50
	DefaultOffset = 0
)

const (
	TimeFactor            = "time"
	RegionFactor          = "region"
	AbnormalRegion        = "abnormal_region"
	EveryDayIntervalType  = 1
	EveryWeekIntervalType = 2
)

type BindIDPInfo struct {
	Id   string
	Name string
	Type string
}

type BindGroupInfo struct {
	ID         string
	Name       string
	Path       string
	SourceType string
}

type BindUserInfo struct {
	ID          string
	Name        string
	DisplayName string
	Path        string
}

type ListUserRsp struct {
	model.TbUserEntity
	SourceType string
}

type KV struct {
	Key   string `json:"key"`
	Value string `json:"value"`
	Type  string `json:"type"`
}

type BindIDPInfoMap struct {
	MainIdpList   []BindIDPInfo
	AssistIdpList []BindIDPInfo
}
