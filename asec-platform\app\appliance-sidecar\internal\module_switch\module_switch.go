package module_switch

import (
	"context"
	"encoding/json"
	"os"
	"path/filepath"
	"sync"
	"time"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/constants"
)

var (
	defaultPullModuleSwitchInterval = time.Minute
	pullModuleSwitchMutex           sync.Mutex
	pullModuleSwitchTicker          *time.Ticker
	stopChan                        chan struct{}
)

type moduleSwitchResp struct {
	Code int `json:"code"`
	Data []struct {
		AgentModule string `json:"agentModule"`
		ModuleValue bool   `json:"moduleValue"`
	} `json:"data"`
}

// ModuleSwitchConfig 模块开关配置结构，按照 app_config_sync 的模式
type ModuleSwitchConfig struct {
	Switches     map[string]bool `json:"switches"`
	LastSyncTime time.Time       `json:"last_sync_time"`
}

func StartPullModuleSwitchTimer() {
	if pullModuleSwitchTicker != nil {
		pullModuleSwitchTicker.Stop()
	}
	interval := getPullModuleSwitchInterval()
	pullModuleSwitchTicker = time.NewTicker(interval)
	stopChan = make(chan struct{})
	go func() {
		for {
			select {
			case <-pullModuleSwitchTicker.C:
				pullModuleSwitch()
			case <-stopChan:
				pullModuleSwitchTicker.Stop()
				return
			}
		}
	}()
}

func StopPullModuleSwitchTimer() {
	if stopChan != nil {
		close(stopChan)
	}
}

func getPullModuleSwitchInterval() time.Duration {
	// TODO: 从配置读取间隔，暂用默认
	return defaultPullModuleSwitchInterval
}

func pullModuleSwitch() {
	pullModuleSwitchMutex.Lock()
	defer pullModuleSwitchMutex.Unlock()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	url := connection.GetPlatformURL(constants.ModuleSwitchEndpoint)
	params := map[string]interface{}{
		"agent_id": global.ApplianceID,
	}
	body, _ := json.Marshal(params)

	requester := connection.GetPlatformRequester("https")
	if requester == nil {
		global.Logger.Sugar().Warnf("no platform requester available for https")
		return
	}
	respBody, err := requester.Do(ctx, "POST", url, body, map[string]string{"Content-Type": "application/json"})
	if err != nil {
		global.Logger.Sugar().Warnf("pull module switch request failed: %v", err)
		return
	}
	parseModuleSwitchResponse(respBody)
}

func parseModuleSwitchResponse(respBody []byte) {
	var resp moduleSwitchResp

	err := json.Unmarshal(respBody, &resp)
	if err != nil {
		global.Logger.Sugar().Errorf("parse module switch response failed: %v, body: %s", err, string(respBody))
		return
	}
	if resp.Code != 0 {
		global.Logger.Sugar().Errorf("pull module switch result code failed, code: %d", resp.Code)
		return
	}

	// 按照 app_config_sync 的模式，将模块开关配置写入 JSON 文件
	// 而不是直接写入 service_config.ini
	config := ModuleSwitchConfig{
		Switches:     make(map[string]bool),
		LastSyncTime: time.Now(),
	}

	for _, item := range resp.Data {
		config.Switches[item.AgentModule] = item.ModuleValue
		global.Logger.Sugar().Debugf("module switch: %s = %v", item.AgentModule, item.ModuleValue)
	}

	if err := writeModuleSwitchConfigToFile(config); err != nil {
		global.Logger.Sugar().Errorf("Failed to write module switch config to file: %v", err)
	} else {
		global.Logger.Sugar().Infof("Successfully saved module switch config to %s", getModuleSwitchConfigFilePath())
	}
}

// getModuleSwitchConfigFilePath 获取模块开关配置文件路径
func getModuleSwitchConfigFilePath() string {
	return constants.GetModuleSwitchConfigPath()
}

// writeModuleSwitchConfigToFile 将模块开关配置写入文件
func writeModuleSwitchConfigToFile(config ModuleSwitchConfig) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	// 检查文件是否存在，如果存在则比较内容
	configFilePath := getModuleSwitchConfigFilePath()
	if _, err := os.Stat(configFilePath); err == nil {
		// 文件存在，读取现有内容进行比较
		existingData, err := os.ReadFile(configFilePath)
		if err != nil {
			global.Logger.Sugar().Warnf("Failed to read existing module switch config file: %v", err)
		} else {
			// 解析现有配置
			var existingConfig ModuleSwitchConfig
			if err := json.Unmarshal(existingData, &existingConfig); err != nil {
				global.Logger.Sugar().Warnf("Failed to parse existing module switch config file: %v", err)
			} else {
				// 比较配置内容（忽略时间字段）
				if moduleSwitchConfigsEqual(config, existingConfig) {
					global.Logger.Sugar().Debug("Module switch configuration unchanged, skipping file write")
					return nil
				}
			}
		}
	}

	dir := filepath.Dir(configFilePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 原子写入：先写临时文件，再重命名
	tempFile := configFilePath + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return err
	}

	return os.Rename(tempFile, configFilePath)
}

// moduleSwitchConfigsEqual 比较两个模块开关配置是否相同（忽略时间字段）
func moduleSwitchConfigsEqual(a, b ModuleSwitchConfig) bool {
	if len(a.Switches) != len(b.Switches) {
		return false
	}
	for key, value := range a.Switches {
		if b.Switches[key] != value {
			return false
		}
	}
	return true
}

func boolToStr(b bool) string {
	if b {
		return "true"
	}
	return "false"
}
