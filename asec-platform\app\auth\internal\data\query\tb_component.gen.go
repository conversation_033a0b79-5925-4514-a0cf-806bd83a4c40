// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbComponent(db *gorm.DB, opts ...gen.DOOption) tbComponent {
	_tbComponent := tbComponent{}

	_tbComponent.tbComponentDo.UseDB(db, opts...)
	_tbComponent.tbComponentDo.UseModel(&model.TbComponent{})

	tableName := _tbComponent.tbComponentDo.TableName()
	_tbComponent.ALL = field.NewAsterisk(tableName)
	_tbComponent.ID = field.NewString(tableName, "id")
	_tbComponent.Name = field.NewString(tableName, "name")
	_tbComponent.ProviderID = field.NewString(tableName, "provider_id")
	_tbComponent.CorpID = field.NewString(tableName, "corp_id")

	_tbComponent.fillFieldMap()

	return _tbComponent
}

type tbComponent struct {
	tbComponentDo tbComponentDo

	ALL        field.Asterisk
	ID         field.String
	Name       field.String
	ProviderID field.String
	CorpID     field.String

	fieldMap map[string]field.Expr
}

func (t tbComponent) Table(newTableName string) *tbComponent {
	t.tbComponentDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbComponent) As(alias string) *tbComponent {
	t.tbComponentDo.DO = *(t.tbComponentDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbComponent) updateTableName(table string) *tbComponent {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.ProviderID = field.NewString(table, "provider_id")
	t.CorpID = field.NewString(table, "corp_id")

	t.fillFieldMap()

	return t
}

func (t *tbComponent) WithContext(ctx context.Context) *tbComponentDo {
	return t.tbComponentDo.WithContext(ctx)
}

func (t tbComponent) TableName() string { return t.tbComponentDo.TableName() }

func (t tbComponent) Alias() string { return t.tbComponentDo.Alias() }

func (t *tbComponent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbComponent) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 4)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["provider_id"] = t.ProviderID
	t.fieldMap["corp_id"] = t.CorpID
}

func (t tbComponent) clone(db *gorm.DB) tbComponent {
	t.tbComponentDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbComponent) replaceDB(db *gorm.DB) tbComponent {
	t.tbComponentDo.ReplaceDB(db)
	return t
}

type tbComponentDo struct{ gen.DO }

func (t tbComponentDo) Debug() *tbComponentDo {
	return t.withDO(t.DO.Debug())
}

func (t tbComponentDo) WithContext(ctx context.Context) *tbComponentDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbComponentDo) ReadDB() *tbComponentDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbComponentDo) WriteDB() *tbComponentDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbComponentDo) Session(config *gorm.Session) *tbComponentDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbComponentDo) Clauses(conds ...clause.Expression) *tbComponentDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbComponentDo) Returning(value interface{}, columns ...string) *tbComponentDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbComponentDo) Not(conds ...gen.Condition) *tbComponentDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbComponentDo) Or(conds ...gen.Condition) *tbComponentDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbComponentDo) Select(conds ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbComponentDo) Where(conds ...gen.Condition) *tbComponentDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbComponentDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbComponentDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbComponentDo) Order(conds ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbComponentDo) Distinct(cols ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbComponentDo) Omit(cols ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbComponentDo) Join(table schema.Tabler, on ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbComponentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbComponentDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbComponentDo) Group(cols ...field.Expr) *tbComponentDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbComponentDo) Having(conds ...gen.Condition) *tbComponentDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbComponentDo) Limit(limit int) *tbComponentDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbComponentDo) Offset(offset int) *tbComponentDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbComponentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbComponentDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbComponentDo) Unscoped() *tbComponentDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbComponentDo) Create(values ...*model.TbComponent) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbComponentDo) CreateInBatches(values []*model.TbComponent, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbComponentDo) Save(values ...*model.TbComponent) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbComponentDo) First() (*model.TbComponent, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponent), nil
	}
}

func (t tbComponentDo) Take() (*model.TbComponent, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponent), nil
	}
}

func (t tbComponentDo) Last() (*model.TbComponent, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponent), nil
	}
}

func (t tbComponentDo) Find() ([]*model.TbComponent, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbComponent), err
}

func (t tbComponentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbComponent, err error) {
	buf := make([]*model.TbComponent, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbComponentDo) FindInBatches(result *[]*model.TbComponent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbComponentDo) Attrs(attrs ...field.AssignExpr) *tbComponentDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbComponentDo) Assign(attrs ...field.AssignExpr) *tbComponentDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbComponentDo) Joins(fields ...field.RelationField) *tbComponentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbComponentDo) Preload(fields ...field.RelationField) *tbComponentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbComponentDo) FirstOrInit() (*model.TbComponent, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponent), nil
	}
}

func (t tbComponentDo) FirstOrCreate() (*model.TbComponent, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponent), nil
	}
}

func (t tbComponentDo) FindByPage(offset int, limit int) (result []*model.TbComponent, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbComponentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbComponentDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbComponentDo) Delete(models ...*model.TbComponent) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbComponentDo) withDO(do gen.Dao) *tbComponentDo {
	t.DO = *do.(*gen.DO)
	return t
}
