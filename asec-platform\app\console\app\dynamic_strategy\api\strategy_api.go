package api

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/service"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/app/user/dto"
	userService "asdsec.com/asec/platform/app/console/app/user/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strings"
)

// AddStrategy godoc
// @Summary 添加动态策略
// @Schemes
// @Description 添加动态策略
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       req body vo.AddStrategyReq true "添加动态策略参数"
// @Success     200
// @Router      /v1/dynamic_strategy/strategy [POST]
// @success     200 {object} common.Response{} "ok"
func AddStrategy(c *gin.Context) {
	req := vo.AddStrategyReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateCreate,
			Representation: req.StrategyName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetStrategyService().AddStrategy(c, req)
	if aError != nil {
		errorLog = aError.Error()
		global.SysLog.Error("addStrategy err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// UpdateStrategy godoc
// @Summary 修改动态策略
// @Schemes
// @Description 修改动态策略
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       req body vo.UpdateStrategyReq true "修改动态策略参数"
// @Success     200
// @Router      /v1/dynamic_strategy/strategy [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateStrategy(c *gin.Context) {
	req := vo.UpdateStrategyReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateUpdate,
			Representation: req.StrategyName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetStrategyService().UpdateStrategy(c, req)
	if aError != nil {
		errorLog = aError.Error()
		global.SysLog.Error("updateStrategy err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// DelStrategy godoc
// @Summary 批量删除策略
// @Schemes
// @Description 批量删除策略
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       req body vo.DelStrategyReq true "批量删除策略参数"
// @Success     200
// @Router      /v1/dynamic_strategy/strategy [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelStrategy(c *gin.Context) {
	req := vo.DelStrategyReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateDelete,
			Representation: strings.Join(req.Name, ","),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetStrategyService().DelStrategy(c, req)
	if aError != nil {
		errorLog = aError.Error()
		global.SysLog.Error("delStrategy err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// EnableStrategy godoc
// @Summary 批量启停策略
// @Schemes
// @Description 批量启停策略
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       req body vo.EnableStrategyReq true "批量启停策略参数"
// @Success     200
// @Router      /v1/dynamic_strategy/enable [POST]
// @success     200 {object} common.Response{} "ok"
func EnableStrategy(c *gin.Context) {
	req := vo.EnableStrategyReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateEnable,
			Representation: strings.Join(req.Name, ","),
			Error:          errorLog,
		}
		if req.Enable == 2 {
			oplog.OperationType = common.OperateDisable
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetStrategyService().EnableStrategy(c, req)
	if aError != nil {
		errorLog = aError.Error()
		global.SysLog.Error("enableStrategy err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// StrategyDetail godoc
// @Summary 策略详情
// @Schemes
// @Description 策略详情
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       id query string true "策略id"
// @Success     200
// @Router      /v1/dynamic_strategy/strategy [GET]
// @success     200 {object} common.Response{data=vo.StrategyDetailResp} "ok"
func StrategyDetail(c *gin.Context) {
	id := c.Query("id")
	strategyDetail, aError := service.GetStrategyService().StrategyDetail(c, id)
	if aError != nil {
		global.SysLog.Error("get strategyDetail err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}

	echo, err := userService.GetUserService().UserComponentEcho(c, dto.UserComponentEchoReq{
		UserIds:  strategyDetail.UserIds,
		GroupIds: strategyDetail.UserGroupIds,
		RoleIds:  strategyDetail.RoleIds,
	})
	if err != nil {
		common.Fail(c, common.OperateError)
		return
	}
	exEcho, err := userService.GetUserService().UserComponentEcho(c, dto.UserComponentEchoReq{
		UserIds:  strategyDetail.ExcludeUserIds,
		GroupIds: strategyDetail.ExcludeUserGroupIds,
		RoleIds:  strategyDetail.ExcludeUserRoleIds,
	})
	if err != nil {
		common.Fail(c, common.OperateError)
		return
	}
	strategyDetail.UserInfo = echo
	strategyDetail.ExcludeUserInfo = exEcho
	common.OkWithData(c, strategyDetail)
}

// ClearStrategyMatch godoc
// @Summary 清空策略匹配次数
// @Schemes
// @Description 清空策略匹配次数
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       req body vo.DelStrategyReq true "清空策略匹配次数参数"
// @Success     200
// @Router      /v1/dynamic_strategy/clear_match [POST]
// @success     200 {object} common.Response{} "ok"
func ClearStrategyMatch(c *gin.Context) {
	req := vo.DelStrategyReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	aError := service.GetStrategyService().ClearStrategyMatch(c, req)
	if aError != nil {
		global.SysLog.Error("clearStrategyMatch err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// StrategyMove godoc
// @Summary 动态策略优先级移动
// @Schemes
// @Description 动态策略优先级移动
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       req body vo.StrategyMoveReq true "动态策略优先级移动参数"
// @Success     200
// @Router      /v1/dynamic_strategy/strategy_move [POST]
// @success     200 {object} common.Response{} "ok"
func StrategyMove(c *gin.Context) {
	req := vo.StrategyMoveReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	aError := service.GetStrategyService().StrategyMove(c, req)
	if aError != nil {
		global.SysLog.Error("strategyMove err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// StrategySimpleList godoc
// @Summary 通过策略分组获取简易策略列表
// @Schemes
// @Description 通过策略分组获取简易策略列表
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       strategy_group_id query string true "分组id"
// @Success     200
// @Router      /v1/dynamic_strategy/strategy_simple_list [GET]
// @success     200 {object} common.Response{data=[]vo.StrategySimpleListResp} "ok"
func StrategySimpleList(c *gin.Context) {
	groupId := c.Query("strategy_group_id")
	simpleList, aError := service.GetStrategyService().StrategySimpleList(c, groupId)
	if aError != nil {
		global.SysLog.Error("get strategySimpleList err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, simpleList)
}

// StrategyList godoc
// @Summary 动态策略列表查询
// @Schemes
// @Description 动态策略列表查询
// @Tags        DynamicStrategy
// @Produce     application/json
// @Param       req body vo.StrategyListReq true "动态策略列表查询参数"
// @Success     200
// @Router      /v1/dynamic_strategy/strategy_list [POST]
// @success     200 {object} common.Response{data=vo.StrategyListResp} "ok"
func StrategyList(c *gin.Context) {
	req := vo.StrategyListReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	resp, aError := service.GetStrategyService().StrategyList(c, req)
	if aError != nil {
		global.SysLog.Error("get strategyList err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, resp)
}
