// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                          db,
		TbAccessConfig:              newTbAccessConfig(db, opts...),
		TbAuthAccountPolicy:         newTbAuthAccountPolicy(db, opts...),
		TbAuthPolicyIdpMapper:       newTbAuthPolicyIdpMapper(db, opts...),
		TbComponent:                 newTbComponent(db, opts...),
		TbComponentConfig:           newTbComponentConfig(db, opts...),
		TbCorp:                      newTbCorp(db, opts...),
		TbCredential:                newTbCredential(db, opts...),
		TbExternalDepartment:        newTbExternalDepartment(db, opts...),
		TbExternalUser:              newTbExternalUser(db, opts...),
		TbIdentityProvider:          newTbIdentityProvider(db, opts...),
		TbIdentityProviderAttribute: newTbIdentityProviderAttribute(db, opts...),
		TbIdentityProviderTemplate:  newTbIdentityProviderTemplate(db, opts...),
		TbIdpGroupMapper:            newTbIdpGroupMapper(db, opts...),
		TbIdpTemplateAttribute:      newTbIdpTemplateAttribute(db, opts...),
		TbRole:                      newTbRole(db, opts...),
		TbSpecialConfig:             newTbSpecialConfig(db, opts...),
		TbUserEntity:                newTbUserEntity(db, opts...),
		TbUserGroup:                 newTbUserGroup(db, opts...),
		TbUserGroupSync:             newTbUserGroupSync(db, opts...),
		TbUserGroupSyncConfig:       newTbUserGroupSyncConfig(db, opts...),
		TbUserGroupSyncLog:          newTbUserGroupSyncLog(db, opts...),
		TbUserLoginLog:              newTbUserLoginLog(db, opts...),
		TbUserRole:                  newTbUserRole(db, opts...),
		TbUserSessionTracker:        newTbUserSessionTracker(db, opts...),
		TbUserSource:                newTbUserSource(db, opts...),
		TbWxDepartment:              newTbWxDepartment(db, opts...),
		TbWxUser:                    newTbWxUser(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	TbAccessConfig              tbAccessConfig
	TbAuthAccountPolicy         tbAuthAccountPolicy
	TbAuthPolicyIdpMapper       tbAuthPolicyIdpMapper
	TbComponent                 tbComponent
	TbComponentConfig           tbComponentConfig
	TbCorp                      tbCorp
	TbCredential                tbCredential
	TbExternalDepartment        tbExternalDepartment
	TbExternalUser              tbExternalUser
	TbIdentityProvider          tbIdentityProvider
	TbIdentityProviderAttribute tbIdentityProviderAttribute
	TbIdentityProviderTemplate  tbIdentityProviderTemplate
	TbIdpGroupMapper            tbIdpGroupMapper
	TbIdpTemplateAttribute      tbIdpTemplateAttribute
	TbRole                      tbRole
	TbSpecialConfig             tbSpecialConfig
	TbUserEntity                tbUserEntity
	TbUserGroup                 tbUserGroup
	TbUserGroupSync             tbUserGroupSync
	TbUserGroupSyncConfig       tbUserGroupSyncConfig
	TbUserGroupSyncLog          tbUserGroupSyncLog
	TbUserLoginLog              tbUserLoginLog
	TbUserRole                  tbUserRole
	TbUserSessionTracker        tbUserSessionTracker
	TbUserSource                tbUserSource
	TbWxDepartment              tbWxDepartment
	TbWxUser                    tbWxUser
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                          db,
		TbAccessConfig:              q.TbAccessConfig.clone(db),
		TbAuthAccountPolicy:         q.TbAuthAccountPolicy.clone(db),
		TbAuthPolicyIdpMapper:       q.TbAuthPolicyIdpMapper.clone(db),
		TbComponent:                 q.TbComponent.clone(db),
		TbComponentConfig:           q.TbComponentConfig.clone(db),
		TbCorp:                      q.TbCorp.clone(db),
		TbCredential:                q.TbCredential.clone(db),
		TbExternalDepartment:        q.TbExternalDepartment.clone(db),
		TbExternalUser:              q.TbExternalUser.clone(db),
		TbIdentityProvider:          q.TbIdentityProvider.clone(db),
		TbIdentityProviderAttribute: q.TbIdentityProviderAttribute.clone(db),
		TbIdentityProviderTemplate:  q.TbIdentityProviderTemplate.clone(db),
		TbIdpGroupMapper:            q.TbIdpGroupMapper.clone(db),
		TbIdpTemplateAttribute:      q.TbIdpTemplateAttribute.clone(db),
		TbRole:                      q.TbRole.clone(db),
		TbSpecialConfig:             q.TbSpecialConfig.clone(db),
		TbUserEntity:                q.TbUserEntity.clone(db),
		TbUserGroup:                 q.TbUserGroup.clone(db),
		TbUserGroupSync:             q.TbUserGroupSync.clone(db),
		TbUserGroupSyncConfig:       q.TbUserGroupSyncConfig.clone(db),
		TbUserGroupSyncLog:          q.TbUserGroupSyncLog.clone(db),
		TbUserLoginLog:              q.TbUserLoginLog.clone(db),
		TbUserRole:                  q.TbUserRole.clone(db),
		TbUserSessionTracker:        q.TbUserSessionTracker.clone(db),
		TbUserSource:                q.TbUserSource.clone(db),
		TbWxDepartment:              q.TbWxDepartment.clone(db),
		TbWxUser:                    q.TbWxUser.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                          db,
		TbAccessConfig:              q.TbAccessConfig.replaceDB(db),
		TbAuthAccountPolicy:         q.TbAuthAccountPolicy.replaceDB(db),
		TbAuthPolicyIdpMapper:       q.TbAuthPolicyIdpMapper.replaceDB(db),
		TbComponent:                 q.TbComponent.replaceDB(db),
		TbComponentConfig:           q.TbComponentConfig.replaceDB(db),
		TbCorp:                      q.TbCorp.replaceDB(db),
		TbCredential:                q.TbCredential.replaceDB(db),
		TbExternalDepartment:        q.TbExternalDepartment.replaceDB(db),
		TbExternalUser:              q.TbExternalUser.replaceDB(db),
		TbIdentityProvider:          q.TbIdentityProvider.replaceDB(db),
		TbIdentityProviderAttribute: q.TbIdentityProviderAttribute.replaceDB(db),
		TbIdentityProviderTemplate:  q.TbIdentityProviderTemplate.replaceDB(db),
		TbIdpGroupMapper:            q.TbIdpGroupMapper.replaceDB(db),
		TbIdpTemplateAttribute:      q.TbIdpTemplateAttribute.replaceDB(db),
		TbRole:                      q.TbRole.replaceDB(db),
		TbSpecialConfig:             q.TbSpecialConfig.replaceDB(db),
		TbUserEntity:                q.TbUserEntity.replaceDB(db),
		TbUserGroup:                 q.TbUserGroup.replaceDB(db),
		TbUserGroupSync:             q.TbUserGroupSync.replaceDB(db),
		TbUserGroupSyncConfig:       q.TbUserGroupSyncConfig.replaceDB(db),
		TbUserGroupSyncLog:          q.TbUserGroupSyncLog.replaceDB(db),
		TbUserLoginLog:              q.TbUserLoginLog.replaceDB(db),
		TbUserRole:                  q.TbUserRole.replaceDB(db),
		TbUserSessionTracker:        q.TbUserSessionTracker.replaceDB(db),
		TbUserSource:                q.TbUserSource.replaceDB(db),
		TbWxDepartment:              q.TbWxDepartment.replaceDB(db),
		TbWxUser:                    q.TbWxUser.replaceDB(db),
	}
}

type queryCtx struct {
	TbAccessConfig              *tbAccessConfigDo
	TbAuthAccountPolicy         *tbAuthAccountPolicyDo
	TbAuthPolicyIdpMapper       *tbAuthPolicyIdpMapperDo
	TbComponent                 *tbComponentDo
	TbComponentConfig           *tbComponentConfigDo
	TbCorp                      *tbCorpDo
	TbCredential                *tbCredentialDo
	TbExternalDepartment        *tbExternalDepartmentDo
	TbExternalUser              *tbExternalUserDo
	TbIdentityProvider          *tbIdentityProviderDo
	TbIdentityProviderAttribute *tbIdentityProviderAttributeDo
	TbIdentityProviderTemplate  *tbIdentityProviderTemplateDo
	TbIdpGroupMapper            *tbIdpGroupMapperDo
	TbIdpTemplateAttribute      *tbIdpTemplateAttributeDo
	TbRole                      *tbRoleDo
	TbSpecialConfig             *tbSpecialConfigDo
	TbUserEntity                *tbUserEntityDo
	TbUserGroup                 *tbUserGroupDo
	TbUserGroupSync             *tbUserGroupSyncDo
	TbUserGroupSyncConfig       *tbUserGroupSyncConfigDo
	TbUserGroupSyncLog          *tbUserGroupSyncLogDo
	TbUserLoginLog              *tbUserLoginLogDo
	TbUserRole                  *tbUserRoleDo
	TbUserSessionTracker        *tbUserSessionTrackerDo
	TbUserSource                *tbUserSourceDo
	TbWxDepartment              *tbWxDepartmentDo
	TbWxUser                    *tbWxUserDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		TbAccessConfig:              q.TbAccessConfig.WithContext(ctx),
		TbAuthAccountPolicy:         q.TbAuthAccountPolicy.WithContext(ctx),
		TbAuthPolicyIdpMapper:       q.TbAuthPolicyIdpMapper.WithContext(ctx),
		TbComponent:                 q.TbComponent.WithContext(ctx),
		TbComponentConfig:           q.TbComponentConfig.WithContext(ctx),
		TbCorp:                      q.TbCorp.WithContext(ctx),
		TbCredential:                q.TbCredential.WithContext(ctx),
		TbExternalDepartment:        q.TbExternalDepartment.WithContext(ctx),
		TbExternalUser:              q.TbExternalUser.WithContext(ctx),
		TbIdentityProvider:          q.TbIdentityProvider.WithContext(ctx),
		TbIdentityProviderAttribute: q.TbIdentityProviderAttribute.WithContext(ctx),
		TbIdentityProviderTemplate:  q.TbIdentityProviderTemplate.WithContext(ctx),
		TbIdpGroupMapper:            q.TbIdpGroupMapper.WithContext(ctx),
		TbIdpTemplateAttribute:      q.TbIdpTemplateAttribute.WithContext(ctx),
		TbRole:                      q.TbRole.WithContext(ctx),
		TbSpecialConfig:             q.TbSpecialConfig.WithContext(ctx),
		TbUserEntity:                q.TbUserEntity.WithContext(ctx),
		TbUserGroup:                 q.TbUserGroup.WithContext(ctx),
		TbUserGroupSync:             q.TbUserGroupSync.WithContext(ctx),
		TbUserGroupSyncConfig:       q.TbUserGroupSyncConfig.WithContext(ctx),
		TbUserGroupSyncLog:          q.TbUserGroupSyncLog.WithContext(ctx),
		TbUserLoginLog:              q.TbUserLoginLog.WithContext(ctx),
		TbUserRole:                  q.TbUserRole.WithContext(ctx),
		TbUserSessionTracker:        q.TbUserSessionTracker.WithContext(ctx),
		TbUserSource:                q.TbUserSource.WithContext(ctx),
		TbWxDepartment:              q.TbWxDepartment.WithContext(ctx),
		TbWxUser:                    q.TbWxUser.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
