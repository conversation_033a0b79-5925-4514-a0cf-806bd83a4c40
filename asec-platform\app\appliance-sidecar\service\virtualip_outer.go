package service

import (
	"context"

	"asdsec.com/asec/platform/app/appliance-sidecar/internal/virtualip_log"
)

// VirtualIPAllocationEvent 适配器类型
type VirtualIPAllocationEvent = virtualip_log.VirtualIPAllocationEvent

// TrafficStatsEntry 适配器类型
type TrafficStatsEntry = virtualip_log.TrafficStatsEntry

// ========== 核心收集接口 ==========

// CollectVirtualIPAllocation 适配器函数
func CollectVirtualIPAllocation(event *VirtualIPAllocationEvent) {
	virtualip_log.CollectVirtualIPAllocation(event)
}

// CollectTrafficStats 适配器函数
func CollectTrafficStats(entry *TrafficStatsEntry) {
	virtualip_log.CollectTrafficStats(entry)
}

// ========== 对象池管理 ==========

// GetVirtualIPAllocationEventPool 适配器函数
func GetVirtualIPAllocationEventPool() *VirtualIPAllocationEvent {
	return virtualip_log.GetVirtualIPAllocationEventPool()
}

// ReleaseVirtualIPAllocationEvent 适配器函数
func ReleaseVirtualIPAllocationEvent(event *VirtualIPAllocationEvent) {
	virtualip_log.ReleaseVirtualIPAllocationEvent(event)
}

// ========== 兼容性接口（为了 tun-server 中的调用）==========

// ReportVirtualIPAllocationEvent 兼容性函数 - 实际上是收集而不是立即上报
func ReportVirtualIPAllocationEvent(ctx context.Context, event *VirtualIPAllocationEvent) error {
	// 直接收集到缓冲区，让批量上报机制处理
	CollectVirtualIPAllocation(event)
	return nil
}

// CacheFailedAllocationEvent 兼容性函数 - 失败的事件也收集到缓冲区
func CacheFailedAllocationEvent(event *VirtualIPAllocationEvent) {
	// 失败的事件也通过正常的收集机制处理
	CollectVirtualIPAllocation(event)
}
