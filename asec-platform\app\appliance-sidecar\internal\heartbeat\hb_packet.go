//go:build !android && !ios

package heartbeat

import (
	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/host"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/resource"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/mem"
	"go.uber.org/zap"
	"strconv"
	"time"
)

func GetHeartbeatPacket(now time.Time, pids []int32) *v1.HeartbeatReq {

	rec := v1.HeartbeatReq{}
	procstatlist := []*v1.ProcStat{}
	rec.KernelVersion = host.KernelVersion
	rec.Type = global.ApplianceType
	rec.Arch = host.Arch
	rec.Platform = host.Platform
	rec.PlatformFamily = host.PlatformFamily
	rec.PlatformVersion = host.PlatformVersion
	rec.ApplianceId = global.ApplianceID
	if global.ApplianceType == v1.ApplianceType_AGENT {
		curUser := user.GetUserInfo()
		rec.UserId = curUser.UserId
	}
	if global.Version != "" {
		rec.Version = global.Version
	}
	if global.UpgradeTime != "" {
		rec.UpgradeTime = global.UpgradeTime
	}
	//rec.Data.Fields["net_mode"] = connection.NetMode.Load().(string)
	//s := connection.DefaultStatsHandler.GetStats(now)
	// for all grpc
	//rec.Data.Fields["rx_speed"] = strconv.FormatFloat(s.RxSpeed, 'f', 8, 64)
	//rec.Data.Fields["tx_speed"] = strconv.FormatFloat(s.TxSpeed, 'f', 8, 64)'
	if global.Conf.Heartbeat.ProcStatEnable {
		for _, pid := range pids {
			procstat := v1.ProcStat{}
			cpuPercent, rss, readSpeed, writeSpeed, fds, startAt, err := resource.GetProcResouce(int(pid))
			if err != nil {
				global.Logger.Sugar().Error(err)
			} else {
				procstat.Cpu = strconv.FormatFloat(cpuPercent, 'f', 8, 64)
				procstat.Rss = strconv.FormatUint(rss, 10)
				procstat.ReadSpeed = strconv.FormatFloat(readSpeed, 'f', 8, 64)
				procstat.WriteSpeed = strconv.FormatFloat(writeSpeed, 'f', 8, 64)
				procstat.Pid = pid
				procstat.FdCnt = fds
				procstat.StartAt = startAt
			}
			procstatlist = append(procstatlist, &procstat)
		}
		rec.ProcsStat = procstatlist
	}

	//txTPS, rxTPX := transport.GetState(now)
	// for transfer service
	//rec.Data.Fields["tx_tps"] = strconv.FormatFloat(txTPS, 'f', 8, 64)
	//rec.Data.Fields["rx_tps"] = strconv.FormatFloat(rxTPX, 'f', 8, 64)
	//rec.Data.Fields["du"] = strconv.FormatUint(GetDirSize(agent.WorkingDirectory, "plugin"), 10)
	//rec.Data.Fields["nproc"] = strconv.Itoa(runtime.NumCPU())
	//TODO 增加linux采集
	//if runtime.GOOS == "linux" {
	//	loadavgBytes, err := os.ReadFile("/proc/loadavg")
	//	if err == nil {
	//		fields := strings.Fields(string(loadavgBytes))
	//		if len(fields) > 3 {
	//			rec.Data.Fields["load_1"] = fields[0]
	//			rec.Data.Fields["load_5"] = fields[1]
	//			rec.Data.Fields["load_15"] = fields[2]
	//			subFields := strings.Split(fields[3], "/")
	//			if len(subFields) > 1 {
	//				rec.Data.Fields["running_procs"] = subFields[0]
	//				rec.Data.Fields["total_procs"] = subFields[1]
	//			}
	//		}
	//	}
	//}
	//rec. = strconv.FormatUint(GetBootTime(), 10)
	cpuPercents, err := cpu.Percent(0, false)
	if err == nil {
		rec.SysCpu = strconv.FormatFloat(cpuPercents[0], 'f', 8, 64)
	}
	_mem, err := mem.VirtualMemory()
	if err == nil {
		rec.SysMem = strconv.FormatFloat(_mem.UsedPercent, 'f', 8, 64)
	}
	zap.S().Debugf("agent heartbeat completed")
	return &rec
}
