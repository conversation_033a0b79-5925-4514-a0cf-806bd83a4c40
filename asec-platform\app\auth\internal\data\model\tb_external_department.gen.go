// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbExternalDepartment = "tb_external_department"

// TbExternalDepartment mapped from table <tb_external_department>
type TbExternalDepartment struct {
	LocalRootGroupID string    `gorm:"column:local_root_group_id;not null" json:"local_root_group_id"`
	LocalGroupID     string    `gorm:"column:local_group_id;not null" json:"local_group_id"`
	ID               string    `gorm:"column:id;not null" json:"id"`
	Name             string    `gorm:"column:name;not null" json:"name"`
	Parentid         string    `gorm:"column:parentid;not null" json:"parentid"`
	Order            int64     `gorm:"column:order;not null" json:"order"`
	CreatedAt        time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	NameEn           string    `gorm:"column:name_en" json:"name_en"`
	Type             string    `gorm:"column:type" json:"type"`
	UniqKey          string    `gorm:"column:uniq_key" json:"uniq_key"`
}

// TableName TbExternalDepartment's table name
func (*TbExternalDepartment) TableName() string {
	return TableNameTbExternalDepartment
}
