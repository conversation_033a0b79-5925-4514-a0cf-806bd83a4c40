package bamboocloud

const AuthorizeUrl = "/idp/oauth2/authorize"
const AuthorizeMethod = "GET"
const ClientId = "VPN"
const ClientSecret = "74c9181d484a467ca4c1a23c889de6b6"

const RedirectUri = ""
const ResponseType = "code"

type Response struct {
	ErrorCode string `json:"error_code"`
	Msg       string `json:"msg"`
	Code      string `json:"code"`
}

const GetTokenUrl = "/idp/oauth2/getToken"
const GetTokenMethod = "POST"

const GrantType = "authorization_code"

type TokenResp struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    uint64 `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Uid          string `json:"uid"`
	ErrorCode    string `json:"errcode"`
	Msg          string `json:"msg"`
}

const GetUserInfoUrl = "/idp/oauth2/getUserInfo"

// {"spRoleList":["0001Q410000000B3OJVV"],"displayName":"周建波","_loginName":null,"loginName":"0001Q410000000B3OJVV"}
type UserInfoResp struct {
	ErrorCode  string   `json:"errcode"`
	Msg        string   `json:"msg"`
	SpRoleList []string `json:"spRoleList"`
}
