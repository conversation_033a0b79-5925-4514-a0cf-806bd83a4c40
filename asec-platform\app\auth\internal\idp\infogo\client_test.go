package infogo

import (
	"reflect"
	"testing"
)

func Test_fetchDepartments(t *testing.T) {
	type args struct {
		endpoint string
		login    string
		pass     string
		page     int
		pageSize int
	}
	tests := []struct {
		name    string
		args    args
		want    *ApiResponse[Dept]
		wantErr bool
	}{
		{name: "test", args: args{endpoint: "https://freeztp.hz.infogo.net.cn:17443", page: 0, pageSize: 10, login: "thirdadmin ", pass: "888888"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := fetchDepartments(tt.args.endpoint, tt.args.login, tt.args.pass, tt.args.page, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("fetchDepartments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rrorf("fetchDepartments() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_fetchAccounts(t *testing.T) {
	type args struct {
		endpoint string
		login    string
		pass     string
		page     int
		pageSize int
	}
	tests := []struct {
		name    string
		args    args
		want    *ApiResponse[Account]
		wantErr bool
	}{
		{name: "test", args: args{endpoint: "https://freeztp.hz.infogo.net.cn:17443", page: 0, pageSize: 10, login: "thirdadmin ", pass: "888888"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := fetchAccounts(tt.args.endpoint, tt.args.login, tt.args.pass, tt.args.page, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("fetchAccounts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fetchAccounts() got = %v, want %v", got, tt.want)
			}
		})
	}
}
