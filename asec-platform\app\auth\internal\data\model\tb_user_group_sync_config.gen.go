// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserGroupSyncConfig = "tb_user_group_sync_config"

// TbUserGroupSyncConfig mapped from table <tb_user_group_sync_config>
type TbUserGroupSyncConfig struct {
	SyncID    string    `gorm:"column:sync_id" json:"sync_id"`
	Key       string    `gorm:"column:key;not null" json:"key"`
	Value     string    `gorm:"column:value" json:"value"`
	CreatedAt time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbUserGroupSyncConfig's table name
func (*TbUserGroupSyncConfig) TableName() string {
	return TableNameTbUserGroupSyncConfig
}
