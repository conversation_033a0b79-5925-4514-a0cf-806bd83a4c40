
<template>
  <base-form
    ref="loginForm"
    :model="loginFormData"
    :rules="rules"
    :validate-on-rule-change="false"
    class="login-form"
    @keyup.enter="submitForm"
  >
    <base-form-item class="form-item" prop="user_name">
      <div class="input-wrapper">
        <svg class="input-icon-left" aria-hidden="true">
          <use :xlink:href="'#icon-username'" />
        </svg>
        <base-input
          id="ui-fake-username"
          v-model="loginFormData.user_name"
          autocapitalize="off"
          autocomplete="off"
          autocorrect="off"
          class="login-input"
          name="fake-username"
          placeholder="请输入用户名"
          spellcheck="false"
        />
      </div>
    </base-form-item>

    <base-form-item class="form-item" prop="password">
      <div class="input-wrapper">
        <svg class="input-icon-left" aria-hidden="true">
          <use :xlink:href="'#icon-password'" />
        </svg>
        <base-input
          id="ui-fake-password"
          v-model="loginFormData.password"
          :type="showPassword ? 'text' : 'password'"
          autocapitalize="off"
          autocomplete="new-password"
          autocorrect="off"
          class="login-input password-input"
          name="fake-password"
          placeholder="请输入密码"
          spellcheck="false"
        />
        <svg v-if="!showPassword" class="input-icon-right password-toggle" aria-hidden="true" @click="togglePassword">
          <use :xlink:href="'#icon-password-show'" />
        </svg>
        <svg v-if="showPassword" class="input-icon-right password-toggle" aria-hidden="true" @click="togglePassword">
          <use :xlink:href="'#icon-password-hidden'" />
        </svg>
      </div>
    </base-form-item>

    <base-form-item class="form-item">
      <base-button
        id="ui-fake-submit-login"
        class="login-submit-button"
        type="primary"
        @click="submitForm"
      >
        安全登录
      </base-button>
    </base-form-item>
  </base-form>
</template>

<script>
export default {
  name: 'LocalLogin',
}
</script>

<script setup>
import { reactive, ref } from 'vue'
import { Message } from '@/components/base'
import { useUserStore } from '@/pinia/modules/user'
import { getIdpList } from '@/api/config'
import { Loading } from '@/components/base'
import JSEncrypt from 'jsencrypt'
import _ from 'lodash'
import { useSecondaryAuth } from '@/utils/secondaryAuth'
// 双因子验证
const { handleSecondaryAuthResponse } = useSecondaryAuth()

// 获取id用于登录接口参数
const props = defineProps({
  authId: {
    type: String,
    default: function() {
      return ''
    },
  },
  authInfo: {
    type: Object,
    default: function() {
      return []
    },
  }
})

// 登录相关操作
const loginForm = ref(null)
const showPassword = ref(false)

const loginFormData = reactive({
  user_name: '',
  password: '',
  idp_id: props.authId,
  redirect_uri: 'hello world',
  grant_type: 'implicit',
  client_id: 'client_portal'
})

const rules = reactive({
  user_name: [{ required: true, trigger: 'change', message: '用户名不能为空' }],
  password: [{ required: true, trigger: 'change', message: '密码不能为空' }],
})

// 切换密码显示状态
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const userStore = useUserStore()
const login = async() => {
  logger.log({ idp_id: props.authId })

  // 处理默认本地认证源ID
  let actualAuthId = props.authId
  if (props.authId === 'default-local') {
    const loadingInstance = Loading.service({
      fullscreen: true,
      text: '',
    })
    logger.log('检测到默认本地认证源，尝试获取真实的认证源ID')
    try {
      const idpListData = await getIdpList()
      if (idpListData.status === 200 && idpListData.data && idpListData.data.idpList.length > 0) {
        // 查找本地认证源
        const localIdp = idpListData.data.idpList.find(idp => idp.type === 'local')
        if (localIdp) {
          actualAuthId = localIdp.id
          logger.log('获取到真实的本地认证源ID:', actualAuthId)
        } else {
          throw new Error('服务器配置错误，未找到本地认证源！')
        }
      } else {
        throw new Error('服务器不通，请检查网络！')
      }
    } catch (error) {
      logger.log('获取认证源列表失败:', error)
      Message.error(error.message)
      return
    } finally {
      loadingInstance?.close()
    }
  }

  loginFormData.idp_id = actualAuthId
  const crypt = new JSEncrypt()
  crypt.setPublicKey(`-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA52nU2J3CmT/UsKy2oKYp
g7GyY/wn6T/cymNFrHFGjwpdzYQ0W+wZS75JNPOVvUPYu5zLFsr3FnfddXrBpxo7
ctNYaPAO9maCqo8WfmE5lA04av4trueA0Qd31OVVeBOfxvSkZxMevOneioxFqVh5
yO9meOc01oKzpQ6m8qLYh3Ru4/GUus9XABkV1ue7Ll1Owxj4h0ovXTZN2rVpyrNU
vr+OZeaKA+aMqv2t4woehMuj9hDU9t79mjmVCEJVTPjf051cBFpQawAPUzmMIDWU
Ez3OalPwD03+pHubn80+x+FN94wNK2VV5KtXxwx2g7ZfHGWfY3AwPaJ/uh7cDg/z
WQIDAQAB
-----END PUBLIC KEY-----`)
  const loginData = _.cloneDeep(loginFormData)

  loginData.password = crypt.encrypt(loginFormData.password)
  loginData.user_name = crypt.encrypt(loginFormData.user_name)
  loginData.encryption = 'rsa'

  if (props.authInfo.authType === 'msad' || props.authInfo.authType === 'ldap') {
    loginData['ad_pwd'] = loginData.password
    loginData['ad_username'] = loginData.user_name
    delete loginData.password
    delete loginData.user_name
  }
  const res = await userStore.LoginIn(loginData, props.authInfo.authType, actualAuthId)
  // 双因子验证
  const needSecondaryAuth = await handleSecondaryAuthResponse(res)
  if (needSecondaryAuth) {
    logger.log('用户名密码登录成功，进入双因子验证')
    return
  }
}

const submitForm = () => {
  loginForm.value.validate(async(v) => {
    if (v) {
      await login()
    } else {
      Message({
        type: 'error',
        message: '用户名密码不能为空',
        showClose: true,
      })
      return false
    }
  })
}

</script>

<style lang="scss" scoped>
@import "@/style/index.css";

.input-icon-left {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
  color: #999999;
  z-index: 2;
  pointer-events: none;
}

.input-icon-right {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
  color: #999999;
  z-index: 2;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #666666;
  }
}

.password-toggle {
  &:hover {
    color: #626aef;
  }
}

:deep(.base-input) {
  padding-left: 40px !important;
  height: 40px;
}

.login-form {
  margin-left: 5px;
  margin-right: 5px;
}

</style>
