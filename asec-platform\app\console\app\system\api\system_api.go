package api

import (
	"fmt"
	"net"
	"strconv"

	"asdsec.com/asec/platform/app/console/app/application/api"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	subcommon "asdsec.com/asec/platform/app/console/app/sensitive_elem/common"
	currentMode "asdsec.com/asec/platform/app/console/app/system/common"
	"asdsec.com/asec/platform/app/console/app/system/model"
	"asdsec.com/asec/platform/app/console/app/system/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	opModel "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CreateFakeIpPool godoc
// @Summary 新增客户端流量代理策略
// @Schemes
// @Description 新增客户端流量代理策略
// @Tags        SystemSetting
// @Produce     application/json
// @Param       req body model.FakeIpPoolReq true "新增客户端流量代理策略"
// @Success     200
// @Router      /v1/system/fake_ip_pool [POST]
// @success     200 {object} common.Response{} "ok"
func CreateFakeIpPool(c *gin.Context) {
	req := model.FakeIpPoolReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 判断是否为网段
	_, cidr, err := net.ParseCIDR(req.ProxyNet)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	m, _ := cidr.Mask.Size()
	if m > 24 {
		common.Fail(c, common.CidrInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  common.OperateUpdate,
			Representation: "客户端流量代理",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	if api.IsIPConflactBase(common.GetClientIp(c), req.ProxyNet) {
		common.Fail(c, common.OperateError)
		errorLog = err.Error()
		return
	}

	global.SysLog.Info("insert or update fake-ip", zap.Any("net:", req))
	err = service.GetSystemService().CreateFakeIpPool(c, req)
	if err != nil {
		global.SysLog.Error("CreateClientProxy err", zap.Error(err))
		common.Fail(c, common.OperateError)
		errorLog = err.Error()
		return
	}
	common.Ok(c)
}

// GetFakeIpPool godoc
// @Summary 获取客户端流量代理策略
// @Schemes
// @Description 获取客户端流量代理策略
// @Tags        SystemSetting
// @Produce     application/json
// @Success     200
// @Router      /v1/system/fake_ip_pool [GET]
// @success     200 {object} common.Response{data=model.NetSeg} "ok"
func GetFakeIpPool(c *gin.Context) {
	data, err := service.GetSystemService().GetFakeIpPool(c)
	if err != nil {
		global.SysLog.Error("GetClientProxy err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

func QueryPlatformIPNet(c *gin.Context) {
	data, err := service.GetSystemService().QueryPlatformIPNet(c)
	if err == gorm.ErrRecordNotFound {
		param := currentMode.IPNet{
			IPV4: web.GetServerHost(c),
			Port: 4430,
		}

		var errstr string
		err = service.GetSystemService().CreatePlatformIPNet(c, param)
		if err != nil {
			global.SysLog.Error("CreatePlatformIPNet err", zap.Error(err))
			common.Fail(c, common.CreatePlatformIPInfoErr)
			errstr = err.Error()
			return
		}
		defer subcommon.RecordLog(c, common.SystemType, common.OperateCreate, param.IPV4+":"+strconv.Itoa(param.Port), errstr)
		common.OkWithData(c, param)
	}
	if err != nil {
		global.SysLog.Error("QueryPlatformIPNet err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	common.OkWithData(c, data)
}

func ChangePlatformIPNet(c *gin.Context) {
	req := model.AppliancePartialDB{}
	err := c.ShouldBind(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	var errstr string
	err = service.GetSystemService().ChangePlatformIPNet(c, req)
	if err != nil {
		global.SysLog.Error("QueryPlatformIPNet err", zap.Error(err))
		common.Fail(c, common.ChangePlatformIPInfoErr)
		errstr = err.Error()
		return
	}

	defer subcommon.RecordLog(c, common.SystemType, common.OperateUpdate, strconv.Itoa(req.ApplianceID), errstr)
	common.Ok(c)
}

func DeletePlatformIPNet(c *gin.Context) {
	req := currentMode.ID{}
	err := c.ShouldBind(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	var errstr string
	err = service.GetSystemService().DeletePlatformIPNet(c, req.ID)
	if err != nil {
		global.SysLog.Error("QueryPlatformIPNet err", zap.Error(err))
		common.Fail(c, common.DeletePlatformIPInfoErr)
		errstr = err.Error()
		return
	}

	defer subcommon.RecordLog(c, common.SystemType, common.OperateDelete, strconv.Itoa(req.ID), errstr)
	common.Ok(c)
}

func QueryES256Key(c *gin.Context) {
	data, err := service.GetSystemService().QueryES256Key(c)
	if err != nil {
		global.SysLog.Error("QueryPlatformIPNet err", zap.Error(err))
		common.Fail(c, common.QueryES256KeyErr)
		return
	}
	common.OkWithData(c, data)
}

func QuerySystemVersion(ctx *gin.Context) {
	data, err := service.GetSystemService().QuerySystemVersion(ctx)
	if err != nil {
		global.SysLog.Error("QuerySystemVersionErr err", zap.Error(err))
		common.Fail(ctx, common.QuerySystemVersionErr)
		return
	}
	common.OkWithData(ctx, data)
}

func GetLicenseInfo(ctx *gin.Context) {
	corpId := web.GetAdminCorpId(ctx)
	uuid, err := service.GetSystemService().GetFirstUUID(ctx)
	if err != nil {
		global.SysLog.Error("获取 UUID 失败:", zap.Error(err))
	}
	data, err := service.GetSystemService().GetLicenseInfo(ctx, corpId, uuid)
	if err != nil {
		global.SysLog.Error("QueryLicenseInfoErr err", zap.Error(err))
		common.Fail(ctx, common.QueryLicenseInfoErr)
		return
	}
	common.OkWithData(ctx, data)
}

func AddLicenseInfo(ctx *gin.Context) {
	req := model.LicenseReq{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  common.OperateUpdate,
			Representation: "授权导入",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	data, err := service.GetSystemService().LicenseImport(ctx, req.License, req.PublicKey, req.PrivateKey)
	if err != nil {
		global.SysLog.Error("addLicenseInfoErr err", zap.Error(err))
		common.Fail(ctx, common.QueryLicenseInfoErr)
		return
	}
	common.OkWithData(ctx, data)
}

func CreateHosts(ctx *gin.Context) {
	req := model.CreateHostsRequest{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	if req.Host == "" || len(req.Address) == 0 {
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	// 判断Host是否为ipv4或者ipv6
	ip := net.ParseIP(req.Host)
	if ip == nil {
		common.Fail(ctx, common.ParamInvalidError)
		return
	}

	// 检查主机是否已存在
	exists, err := service.GetSystemService().CheckHostExists(ctx, req.Host)
	if err != nil {
		global.SysLog.Error("CheckHostExists err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	if exists {
		errorMsg := fmt.Sprintf("主机 %s 已存在", req.Host)
		global.SysLog.Error("CreateHosts error: host already exists", zap.String("host", req.Host))
		common.FailWithMessage(ctx, common.OperateFailCode, errorMsg)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  common.OperateCreate,
			Representation: "新增hosts",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	id, err := service.GetSystemService().CreateHosts(ctx, model.Hosts{
		Host:    req.Host,
		Address: req.Address,
		Remark:  req.Remark,
	})
	if err != nil {
		global.SysLog.Error("CreateHosts err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		errorLog = err.Error()
		return
	}
	common.OkWithData(ctx, model.CreateHostsResponse{
		ID:      id,
		Host:    req.Host,
		Address: req.Address,
		Remark:  req.Remark,
	})
}

func GetHosts(ctx *gin.Context) {
	data, err := service.GetSystemService().GetHosts(ctx)
	if err != nil {
		global.SysLog.Error("GetHosts err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, data)
}

func UpdateHosts(ctx *gin.Context) {
	req := model.UpdateHostsRequest{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	if req.ID == 0 || req.Host == "" || len(req.Address) == 0 {
		common.Fail(ctx, common.ParamInvalidError)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  common.OperateUpdate,
			Representation: "更新hosts",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	id, err := service.GetSystemService().UpdateHosts(ctx, model.Hosts{
		ID:      req.ID,
		Host:    req.Host,
		Address: req.Address,
		Remark:  req.Remark,
	})
	if err != nil {
		global.SysLog.Error("UpdateHosts err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		errorLog = err.Error()
		return
	}
	common.OkWithData(ctx, model.UpdateHostsRequest{
		ID:      id,
		Host:    req.Host,
		Address: req.Address,
		Remark:  req.Remark,
	})
}

func DeleteHosts(ctx *gin.Context) {
	var ids []int
	err := ctx.ShouldBindJSON(&ids)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	if len(ids) == 0 {
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  common.OperateDelete,
			Representation: "删除hosts",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetSystemService().DeleteHosts(ctx, ids)
	if err != nil {
		global.SysLog.Error("DeleteHosts err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		errorLog = err.Error()
		return
	}
	common.Ok(ctx)
}

// GetAccessConfig 获取接入配置
// @Tags AccessConfig
// @Summary 获取接入配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} common.Response{data=model.AccessConfig} "获取成功"
// @Router /system/access-config [get]
func GetAccessConfig(c *gin.Context) {
	config, err := service.GetSystemService().GetAccessConfig(c.Request.Context())
	if err != nil {
		global.SysLog.Error("GetAccessConfig err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, config)
}

// SaveAccessConfig 保存接入配置
// @Tags AccessConfig
// @Summary 保存接入配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AccessConfig true "接入配置信息"
// @Success 200 {object} common.Response{} "保存成功"
// @Router /system/access-config [post]
func SaveAccessConfig(c *gin.Context) {
	var config model.AccessConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := opModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  common.OperateUpdate,
			Representation: "保存接入配置",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	if err := service.GetSystemService().SaveAccessConfig(c.Request.Context(), &config); err != nil {
		global.SysLog.Error("SaveAccessConfig err", zap.Error(err))
		common.Fail(c, common.OperateError)
		errorLog = err.Error()
		return
	}

	common.Ok(c)
}
