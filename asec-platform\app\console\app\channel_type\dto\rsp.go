package dto

import (
	pkgModel "asdsec.com/asec/platform/pkg/model"
	"github.com/lib/pq"
)

type GetChannelTypeListRsp struct {
	pkgModel.CommonPage
	ChannelTypeList []ChannelTypeItem `json:"channel_type_list"`
}

type ChannelTypeItem struct {
	Id      string `gorm:"column:id" json:"id"`
	Name    string `gorm:"column:name" json:"name"`
	BuiltIn int    `gorm:"column:built_in" json:"built_in"`
	Count   int    `gorm:"column:count" json:"count"`
}
type ChannelItem struct {
	Id                  string                  `gorm:"column:id" json:"id"`
	Name                string                  `gorm:"column:name" json:"name"`
	Pid                 string                  `gorm:"column:pid" json:"pid"`
	ChannelTypeName     string                  `gorm:"column:channel_type_name" json:"channel_type_name"`
	ProcessList         pq.StringArray          `gorm:"column:process_list;type:[]varchar" json:"process_list"`
	IncludeFilePath     string                  `gorm:"column:include_file_path;type:varchar" json:"-"`
	ExcludeFilePath     string                  `gorm:"column:exclude_file_path;type:varchar" json:"-"`
	IncludeFilePathInfo []pkgModel.FilePathRule `gorm:"-" json:"include_file_path_info"`
	ExcludeFilePathInfo []pkgModel.FilePathRule `gorm:"-" json:"exclude_file_path_info"`
	Status              int                     `gorm:"column:status" json:"status"`
	BuiltIn             int                     `gorm:"column:built_in" json:"built_in"`
}
type GetChannelListRsp struct {
	pkgModel.CommonPage
	ChannelList []ChannelItem `json:"channel_list"`
}
