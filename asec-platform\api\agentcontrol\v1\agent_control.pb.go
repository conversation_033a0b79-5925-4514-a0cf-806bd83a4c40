// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v5.28.0
// source: agentcontrol/v1/agent_control.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 任务类型
type TaskType int32

const (
	TaskType_UnknownType TaskType = 0
	TaskType_LogUpload   TaskType = 1 // 日志上报
)

// Enum value maps for TaskType.
var (
	TaskType_name = map[int32]string{
		0: "UnknownType",
		1: "LogUpload",
	}
	TaskType_value = map[string]int32{
		"UnknownType": 0,
		"LogUpload":   1,
	}
)

func (x TaskType) Enum() *TaskType {
	p := new(TaskType)
	*p = x
	return p
}

func (x TaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[0].Descriptor()
}

func (TaskType) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[0]
}

func (x TaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskType.Descriptor instead.
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{0}
}

// 任务状态
type TaskState int32

const (
	TaskState_UnknownState   TaskState = 0
	TaskState_NotPulledState TaskState = 1
	TaskState_PulledState    TaskState = 2
	TaskState_ExecutingState TaskState = 3
	TaskState_FinishedState  TaskState = 4
	TaskState_FailedState    TaskState = 5
	TaskState_TimeOutState   TaskState = 6
)

// Enum value maps for TaskState.
var (
	TaskState_name = map[int32]string{
		0: "UnknownState",
		1: "NotPulledState",
		2: "PulledState",
		3: "ExecutingState",
		4: "FinishedState",
		5: "FailedState",
		6: "TimeOutState",
	}
	TaskState_value = map[string]int32{
		"UnknownState":   0,
		"NotPulledState": 1,
		"PulledState":    2,
		"ExecutingState": 3,
		"FinishedState":  4,
		"FailedState":    5,
		"TimeOutState":   6,
	}
)

func (x TaskState) Enum() *TaskState {
	p := new(TaskState)
	*p = x
	return p
}

func (x TaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[1].Descriptor()
}

func (TaskState) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[1]
}

func (x TaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskState.Descriptor instead.
func (TaskState) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{1}
}

type AgentModule int32

const (
	AgentModule_UnknownAgentModule AgentModule = 0
	AgentModule_All                AgentModule = 1
	AgentModule_Ddr                AgentModule = 2
	AgentModule_Sidecar            AgentModule = 3
	AgentModule_Tun                AgentModule = 4
	AgentModule_UnloadVerify       AgentModule = 5
	AgentModule_HideUI             AgentModule = 6
	AgentModule_Plugin             AgentModule = 7
)

// Enum value maps for AgentModule.
var (
	AgentModule_name = map[int32]string{
		0: "UnknownAgentModule",
		1: "All",
		2: "Ddr",
		3: "Sidecar",
		4: "Tun",
		5: "UnloadVerify",
		6: "HideUI",
		7: "Plugin",
	}
	AgentModule_value = map[string]int32{
		"UnknownAgentModule": 0,
		"All":                1,
		"Ddr":                2,
		"Sidecar":            3,
		"Tun":                4,
		"UnloadVerify":       5,
		"HideUI":             6,
		"Plugin":             7,
	}
)

func (x AgentModule) Enum() *AgentModule {
	p := new(AgentModule)
	*p = x
	return p
}

func (x AgentModule) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentModule) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[2].Descriptor()
}

func (AgentModule) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[2]
}

func (x AgentModule) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentModule.Descriptor instead.
func (AgentModule) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{2}
}

// 地址类型
type AddrType int32

const (
	AddrType_UnknownAddr AddrType = 0
	AddrType_LogAddr     AddrType = 1 // 日志地址
	AddrType_AuthAddr    AddrType = 2 // 认证地址
)

// Enum value maps for AddrType.
var (
	AddrType_name = map[int32]string{
		0: "UnknownAddr",
		1: "LogAddr",
		2: "AuthAddr",
	}
	AddrType_value = map[string]int32{
		"UnknownAddr": 0,
		"LogAddr":     1,
		"AuthAddr":    2,
	}
)

func (x AddrType) Enum() *AddrType {
	p := new(AddrType)
	*p = x
	return p
}

func (x AddrType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddrType) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[3].Descriptor()
}

func (AddrType) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[3]
}

func (x AddrType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddrType.Descriptor instead.
func (AddrType) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{3}
}

type Response int32

const (
	Response_UnknownResponse Response = 0
	Response_RespOK          Response = 1
	Response_RespFailed      Response = 2
)

// Enum value maps for Response.
var (
	Response_name = map[int32]string{
		0: "UnknownResponse",
		1: "RespOK",
		2: "RespFailed",
	}
	Response_value = map[string]int32{
		"UnknownResponse": 0,
		"RespOK":          1,
		"RespFailed":      2,
	}
)

func (x Response) Enum() *Response {
	p := new(Response)
	*p = x
	return p
}

func (x Response) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Response) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[4].Descriptor()
}

func (Response) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[4]
}

func (x Response) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Response.Descriptor instead.
func (Response) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{4}
}

// 失败原因
type TaskFailedReason int32

const (
	TaskFailedReason_UnknownReason  TaskFailedReason = 0
	TaskFailedReason_TaskTimeOut    TaskFailedReason = 1 // 任务超时
	TaskFailedReason_TaskExecFailed TaskFailedReason = 2 // 终端执行失败
	TaskFailedReason_NetTimeOut     TaskFailedReason = 3 // 网络超时
	TaskFailedReason_FileMiss       TaskFailedReason = 4 // 文件丢失
	TaskFailedReason_FileCorruption TaskFailedReason = 5 // 文件损坏
	TaskFailedReason_NoAuthority    TaskFailedReason = 6 // 终端无访问权限
)

// Enum value maps for TaskFailedReason.
var (
	TaskFailedReason_name = map[int32]string{
		0: "UnknownReason",
		1: "TaskTimeOut",
		2: "TaskExecFailed",
		3: "NetTimeOut",
		4: "FileMiss",
		5: "FileCorruption",
		6: "NoAuthority",
	}
	TaskFailedReason_value = map[string]int32{
		"UnknownReason":  0,
		"TaskTimeOut":    1,
		"TaskExecFailed": 2,
		"NetTimeOut":     3,
		"FileMiss":       4,
		"FileCorruption": 5,
		"NoAuthority":    6,
	}
)

func (x TaskFailedReason) Enum() *TaskFailedReason {
	p := new(TaskFailedReason)
	*p = x
	return p
}

func (x TaskFailedReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskFailedReason) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[5].Descriptor()
}

func (TaskFailedReason) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[5]
}

func (x TaskFailedReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskFailedReason.Descriptor instead.
func (TaskFailedReason) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{5}
}

// 上报类型
type UploadType int32

const (
	UploadType_UnknownReport UploadType = 0
	UploadType_LogFileUpload UploadType = 1 // 日志文件上报
)

// Enum value maps for UploadType.
var (
	UploadType_name = map[int32]string{
		0: "UnknownReport",
		1: "LogFileUpload",
	}
	UploadType_value = map[string]int32{
		"UnknownReport": 0,
		"LogFileUpload": 1,
	}
)

func (x UploadType) Enum() *UploadType {
	p := new(UploadType)
	*p = x
	return p
}

func (x UploadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadType) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[6].Descriptor()
}

func (UploadType) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[6]
}

func (x UploadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadType.Descriptor instead.
func (UploadType) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{6}
}

type ConfigType int32

const (
	ConfigType_UnknownConfig ConfigType = 0
)

// Enum value maps for ConfigType.
var (
	ConfigType_name = map[int32]string{
		0: "UnknownConfig",
	}
	ConfigType_value = map[string]int32{
		"UnknownConfig": 0,
	}
)

func (x ConfigType) Enum() *ConfigType {
	p := new(ConfigType)
	*p = x
	return p
}

func (x ConfigType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfigType) Descriptor() protoreflect.EnumDescriptor {
	return file_agentcontrol_v1_agent_control_proto_enumTypes[7].Descriptor()
}

func (ConfigType) Type() protoreflect.EnumType {
	return &file_agentcontrol_v1_agent_control_proto_enumTypes[7]
}

func (x ConfigType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfigType.Descriptor instead.
func (ConfigType) EnumDescriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{7}
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{0}
}

// 任务内容
type TaskContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        TaskType    `protobuf:"varint,1,opt,name=type,proto3,enum=api.asdsec.agentcontrol.TaskType" json:"type,omitempty"`
	TaskId      string      `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	AgentModule AgentModule `protobuf:"varint,3,opt,name=agent_module,json=agentModule,proto3,enum=api.asdsec.agentcontrol.AgentModule" json:"agent_module,omitempty"`
	TaskInfo    []byte      `protobuf:"bytes,4,opt,name=task_info,json=taskInfo,proto3" json:"task_info,omitempty"`
}

func (x *TaskContext) Reset() {
	*x = TaskContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskContext) ProtoMessage() {}

func (x *TaskContext) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskContext.ProtoReflect.Descriptor instead.
func (*TaskContext) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{1}
}

func (x *TaskContext) GetType() TaskType {
	if x != nil {
		return x.Type
	}
	return TaskType_UnknownType
}

func (x *TaskContext) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskContext) GetAgentModule() AgentModule {
	if x != nil {
		return x.AgentModule
	}
	return AgentModule_UnknownAgentModule
}

func (x *TaskContext) GetTaskInfo() []byte {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

// 地址内容
type AddrContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type AddrType `protobuf:"varint,1,opt,name=type,proto3,enum=api.asdsec.agentcontrol.AddrType" json:"type,omitempty"`
	Addr []string `protobuf:"bytes,2,rep,name=addr,proto3" json:"addr,omitempty"`
}

func (x *AddrContext) Reset() {
	*x = AddrContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddrContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddrContext) ProtoMessage() {}

func (x *AddrContext) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddrContext.ProtoReflect.Descriptor instead.
func (*AddrContext) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{2}
}

func (x *AddrContext) GetType() AddrType {
	if x != nil {
		return x.Type
	}
	return AddrType_UnknownAddr
}

func (x *AddrContext) GetAddr() []string {
	if x != nil {
		return x.Addr
	}
	return nil
}

// 拉取任务请求
type TaskPullReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId uint64 `protobuf:"varint,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
}

func (x *TaskPullReq) Reset() {
	*x = TaskPullReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskPullReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskPullReq) ProtoMessage() {}

func (x *TaskPullReq) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskPullReq.ProtoReflect.Descriptor instead.
func (*TaskPullReq) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{3}
}

func (x *TaskPullReq) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

// 拉取任务回复
type TaskPullResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultCode  uint32         `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
	TaskContext []*TaskContext `protobuf:"bytes,2,rep,name=task_context,json=taskContext,proto3" json:"task_context,omitempty"`
}

func (x *TaskPullResp) Reset() {
	*x = TaskPullResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskPullResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskPullResp) ProtoMessage() {}

func (x *TaskPullResp) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskPullResp.ProtoReflect.Descriptor instead.
func (*TaskPullResp) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{4}
}

func (x *TaskPullResp) GetResultCode() uint32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

func (x *TaskPullResp) GetTaskContext() []*TaskContext {
	if x != nil {
		return x.TaskContext
	}
	return nil
}

// 上报任务状态请求
type TaskStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId    string           `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	AgentId   uint64           `protobuf:"varint,2,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	TaskState TaskState        `protobuf:"varint,3,opt,name=task_state,json=taskState,proto3,enum=api.asdsec.agentcontrol.TaskState" json:"task_state,omitempty"`
	Reason    TaskFailedReason `protobuf:"varint,4,opt,name=reason,proto3,enum=api.asdsec.agentcontrol.TaskFailedReason" json:"reason,omitempty"`
}

func (x *TaskStateReq) Reset() {
	*x = TaskStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStateReq) ProtoMessage() {}

func (x *TaskStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStateReq.ProtoReflect.Descriptor instead.
func (*TaskStateReq) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{5}
}

func (x *TaskStateReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskStateReq) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *TaskStateReq) GetTaskState() TaskState {
	if x != nil {
		return x.TaskState
	}
	return TaskState_UnknownState
}

func (x *TaskStateReq) GetReason() TaskFailedReason {
	if x != nil {
		return x.Reason
	}
	return TaskFailedReason_UnknownReason
}

// 上报任务状态回复
type TaskStateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultCode uint32 `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
}

func (x *TaskStateResp) Reset() {
	*x = TaskStateResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskStateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStateResp) ProtoMessage() {}

func (x *TaskStateResp) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStateResp.ProtoReflect.Descriptor instead.
func (*TaskStateResp) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{6}
}

func (x *TaskStateResp) GetResultCode() uint32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

// 平台地址请求回复
type PlatAddrResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultCode uint32         `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
	AddrAry    []*AddrContext `protobuf:"bytes,2,rep,name=addr_ary,json=addrAry,proto3" json:"addr_ary,omitempty"`
}

func (x *PlatAddrResp) Reset() {
	*x = PlatAddrResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatAddrResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatAddrResp) ProtoMessage() {}

func (x *PlatAddrResp) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatAddrResp.ProtoReflect.Descriptor instead.
func (*PlatAddrResp) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{7}
}

func (x *PlatAddrResp) GetResultCode() uint32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

func (x *PlatAddrResp) GetAddrAry() []*AddrContext {
	if x != nil {
		return x.AddrAry
	}
	return nil
}

// 日志文件上传请求
type FileUploadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName    string     `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	AgentId     uint64     `protobuf:"varint,2,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	TotalChunk  int32      `protobuf:"varint,3,opt,name=total_chunk,json=totalChunk,proto3" json:"total_chunk,omitempty"`
	ChunkIndex  int32      `protobuf:"varint,4,opt,name=chunk_index,json=chunkIndex,proto3" json:"chunk_index,omitempty"`
	ChunkSize   uint64     `protobuf:"varint,5,opt,name=chunk_size,json=chunkSize,proto3" json:"chunk_size,omitempty"`
	FileName    string     `protobuf:"bytes,6,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Type        UploadType `protobuf:"varint,7,opt,name=type,proto3,enum=api.asdsec.agentcontrol.UploadType" json:"type,omitempty"`
	FileSize    uint64     `protobuf:"varint,8,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileMd5     string     `protobuf:"bytes,9,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	IsLastChunk bool       `protobuf:"varint,10,opt,name=is_last_chunk,json=isLastChunk,proto3" json:"is_last_chunk,omitempty"`
	FileContext []byte     `protobuf:"bytes,11,opt,name=file_context,json=fileContext,proto3" json:"file_context,omitempty"`
}

func (x *FileUploadReq) Reset() {
	*x = FileUploadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileUploadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadReq) ProtoMessage() {}

func (x *FileUploadReq) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadReq.ProtoReflect.Descriptor instead.
func (*FileUploadReq) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{8}
}

func (x *FileUploadReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *FileUploadReq) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *FileUploadReq) GetTotalChunk() int32 {
	if x != nil {
		return x.TotalChunk
	}
	return 0
}

func (x *FileUploadReq) GetChunkIndex() int32 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *FileUploadReq) GetChunkSize() uint64 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

func (x *FileUploadReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileUploadReq) GetType() UploadType {
	if x != nil {
		return x.Type
	}
	return UploadType_UnknownReport
}

func (x *FileUploadReq) GetFileSize() uint64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileUploadReq) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *FileUploadReq) GetIsLastChunk() bool {
	if x != nil {
		return x.IsLastChunk
	}
	return false
}

func (x *FileUploadReq) GetFileContext() []byte {
	if x != nil {
		return x.FileContext
	}
	return nil
}

// 日志文件上传回复
type FileUploadResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultCode uint32 `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
	ChunkIndex int32  `protobuf:"varint,2,opt,name=chunk_index,json=chunkIndex,proto3" json:"chunk_index,omitempty"`
}

func (x *FileUploadResp) Reset() {
	*x = FileUploadResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileUploadResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadResp) ProtoMessage() {}

func (x *FileUploadResp) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadResp.ProtoReflect.Descriptor instead.
func (*FileUploadResp) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{9}
}

func (x *FileUploadResp) GetResultCode() uint32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

func (x *FileUploadResp) GetChunkIndex() int32 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

type ModuleSwitchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId uint64 `protobuf:"varint,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
}

func (x *ModuleSwitchReq) Reset() {
	*x = ModuleSwitchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleSwitchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleSwitchReq) ProtoMessage() {}

func (x *ModuleSwitchReq) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleSwitchReq.ProtoReflect.Descriptor instead.
func (*ModuleSwitchReq) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{10}
}

func (x *ModuleSwitchReq) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

type ModuleSwitchResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Data []*ModuleData `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ModuleSwitchResp) Reset() {
	*x = ModuleSwitchResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleSwitchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleSwitchResp) ProtoMessage() {}

func (x *ModuleSwitchResp) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleSwitchResp.ProtoReflect.Descriptor instead.
func (*ModuleSwitchResp) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{11}
}

func (x *ModuleSwitchResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ModuleSwitchResp) GetData() []*ModuleData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ModuleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentModule AgentModule `protobuf:"varint,1,opt,name=agent_module,json=agentModule,proto3,enum=api.asdsec.agentcontrol.AgentModule" json:"agent_module,omitempty"`
	ModuleValue bool        `protobuf:"varint,2,opt,name=module_value,json=moduleValue,proto3" json:"module_value,omitempty"`
}

func (x *ModuleData) Reset() {
	*x = ModuleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleData) ProtoMessage() {}

func (x *ModuleData) ProtoReflect() protoreflect.Message {
	mi := &file_agentcontrol_v1_agent_control_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleData.ProtoReflect.Descriptor instead.
func (*ModuleData) Descriptor() ([]byte, []int) {
	return file_agentcontrol_v1_agent_control_proto_rawDescGZIP(), []int{12}
}

func (x *ModuleData) GetAgentModule() AgentModule {
	if x != nil {
		return x.AgentModule
	}
	return AgentModule_UnknownAgentModule
}

func (x *ModuleData) GetModuleValue() bool {
	if x != nil {
		return x.ModuleValue
	}
	return false
}

var File_agentcontrol_v1_agent_control_proto protoreflect.FileDescriptor

var file_agentcontrol_v1_agent_control_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x07, 0x0a, 0x05,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xc3, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x58, 0x0a, 0x0b, 0x41,
	0x64, 0x64, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x61, 0x64, 0x64, 0x72, 0x22, 0x28, 0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x75, 0x6c,
	0x6c, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x78, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x47, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x74, 0x61,
	0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xc8, 0x01, 0x0a, 0x0c, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x41,
	0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x41, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x22, 0x30, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x70, 0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x5f,
	0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x41, 0x72, 0x79, 0x22, 0xfd, 0x02, 0x0a, 0x0d, 0x46, 0x69, 0x6c,
	0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x37, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64,
	0x35, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x4c, 0x61, 0x73, 0x74,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x52, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2c, 0x0a, 0x0f,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x10, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x78, 0x0a, 0x0a, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x0c, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x2a, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x10,
	0x01, 0x2a, 0x8c, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x10, 0x0a, 0x0c, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10,
	0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x6f, 0x74, 0x50, 0x75, 0x6c, 0x6c, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x75, 0x6c, 0x6c, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x04, 0x12, 0x0f, 0x0a,
	0x0b, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x05, 0x12, 0x10,
	0x0a, 0x0c, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x06,
	0x2a, 0x77, 0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x16, 0x0a, 0x12, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x6c, 0x6c, 0x10, 0x01,
	0x12, 0x07, 0x0a, 0x03, 0x44, 0x64, 0x72, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x69, 0x64,
	0x65, 0x63, 0x61, 0x72, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x75, 0x6e, 0x10, 0x04, 0x12,
	0x10, 0x0a, 0x0c, 0x55, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x10,
	0x05, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x69, 0x64, 0x65, 0x55, 0x49, 0x10, 0x06, 0x12, 0x0a, 0x0a,
	0x06, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x10, 0x07, 0x2a, 0x36, 0x0a, 0x08, 0x41, 0x64, 0x64,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x41, 0x64, 0x64, 0x72, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x6f, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x41, 0x64, 0x64, 0x72, 0x10,
	0x02, 0x2a, 0x3b, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x13, 0x0a,
	0x0f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x70, 0x4f, 0x4b, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x52, 0x65, 0x73, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x2a, 0x8d,
	0x01, 0x0a, 0x10, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x69,
	0x6d, 0x65, 0x4f, 0x75, 0x74, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x45,
	0x78, 0x65, 0x63, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x4e,
	0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x46,
	0x69, 0x6c, 0x65, 0x4d, 0x69, 0x73, 0x73, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x69, 0x6c,
	0x65, 0x43, 0x6f, 0x72, 0x72, 0x75, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x05, 0x12, 0x0f, 0x0a,
	0x0b, 0x4e, 0x6f, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x10, 0x06, 0x2a, 0x32,
	0x0a, 0x0a, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x10, 0x00, 0x12,
	0x11, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x10, 0x01, 0x2a, 0x1f, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x11, 0x0a, 0x0d, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x10, 0x00, 0x32, 0xfe, 0x04, 0x0a, 0x0c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x12, 0x59, 0x0a, 0x08, 0x50, 0x75, 0x6c, 0x6c, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50,
	0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x5e, 0x0a, 0x0b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x25,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73,
	0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x5f, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73,
	0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x57, 0x0a, 0x0c, 0x50, 0x75, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x10, 0x50, 0x75, 0x6c,
	0x6c, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73,
	0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x8d, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x42, 0x31, 0x5a, 0x2f, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agentcontrol_v1_agent_control_proto_rawDescOnce sync.Once
	file_agentcontrol_v1_agent_control_proto_rawDescData = file_agentcontrol_v1_agent_control_proto_rawDesc
)

func file_agentcontrol_v1_agent_control_proto_rawDescGZIP() []byte {
	file_agentcontrol_v1_agent_control_proto_rawDescOnce.Do(func() {
		file_agentcontrol_v1_agent_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_agentcontrol_v1_agent_control_proto_rawDescData)
	})
	return file_agentcontrol_v1_agent_control_proto_rawDescData
}

var file_agentcontrol_v1_agent_control_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_agentcontrol_v1_agent_control_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_agentcontrol_v1_agent_control_proto_goTypes = []interface{}{
	(TaskType)(0),            // 0: api.asdsec.agentcontrol.TaskType
	(TaskState)(0),           // 1: api.asdsec.agentcontrol.TaskState
	(AgentModule)(0),         // 2: api.asdsec.agentcontrol.AgentModule
	(AddrType)(0),            // 3: api.asdsec.agentcontrol.AddrType
	(Response)(0),            // 4: api.asdsec.agentcontrol.Response
	(TaskFailedReason)(0),    // 5: api.asdsec.agentcontrol.TaskFailedReason
	(UploadType)(0),          // 6: api.asdsec.agentcontrol.UploadType
	(ConfigType)(0),          // 7: api.asdsec.agentcontrol.ConfigType
	(*Empty)(nil),            // 8: api.asdsec.agentcontrol.Empty
	(*TaskContext)(nil),      // 9: api.asdsec.agentcontrol.TaskContext
	(*AddrContext)(nil),      // 10: api.asdsec.agentcontrol.AddrContext
	(*TaskPullReq)(nil),      // 11: api.asdsec.agentcontrol.TaskPullReq
	(*TaskPullResp)(nil),     // 12: api.asdsec.agentcontrol.TaskPullResp
	(*TaskStateReq)(nil),     // 13: api.asdsec.agentcontrol.TaskStateReq
	(*TaskStateResp)(nil),    // 14: api.asdsec.agentcontrol.TaskStateResp
	(*PlatAddrResp)(nil),     // 15: api.asdsec.agentcontrol.PlatAddrResp
	(*FileUploadReq)(nil),    // 16: api.asdsec.agentcontrol.FileUploadReq
	(*FileUploadResp)(nil),   // 17: api.asdsec.agentcontrol.FileUploadResp
	(*ModuleSwitchReq)(nil),  // 18: api.asdsec.agentcontrol.ModuleSwitchReq
	(*ModuleSwitchResp)(nil), // 19: api.asdsec.agentcontrol.ModuleSwitchResp
	(*ModuleData)(nil),       // 20: api.asdsec.agentcontrol.ModuleData
}
var file_agentcontrol_v1_agent_control_proto_depIdxs = []int32{
	0,  // 0: api.asdsec.agentcontrol.TaskContext.type:type_name -> api.asdsec.agentcontrol.TaskType
	2,  // 1: api.asdsec.agentcontrol.TaskContext.agent_module:type_name -> api.asdsec.agentcontrol.AgentModule
	3,  // 2: api.asdsec.agentcontrol.AddrContext.type:type_name -> api.asdsec.agentcontrol.AddrType
	9,  // 3: api.asdsec.agentcontrol.TaskPullResp.task_context:type_name -> api.asdsec.agentcontrol.TaskContext
	1,  // 4: api.asdsec.agentcontrol.TaskStateReq.task_state:type_name -> api.asdsec.agentcontrol.TaskState
	5,  // 5: api.asdsec.agentcontrol.TaskStateReq.reason:type_name -> api.asdsec.agentcontrol.TaskFailedReason
	10, // 6: api.asdsec.agentcontrol.PlatAddrResp.addr_ary:type_name -> api.asdsec.agentcontrol.AddrContext
	6,  // 7: api.asdsec.agentcontrol.FileUploadReq.type:type_name -> api.asdsec.agentcontrol.UploadType
	20, // 8: api.asdsec.agentcontrol.ModuleSwitchResp.data:type_name -> api.asdsec.agentcontrol.ModuleData
	2,  // 9: api.asdsec.agentcontrol.ModuleData.agent_module:type_name -> api.asdsec.agentcontrol.AgentModule
	11, // 10: api.asdsec.agentcontrol.AgentControl.PullTask:input_type -> api.asdsec.agentcontrol.TaskPullReq
	13, // 11: api.asdsec.agentcontrol.AgentControl.ReportState:input_type -> api.asdsec.agentcontrol.TaskStateReq
	16, // 12: api.asdsec.agentcontrol.AgentControl.FileUpload:input_type -> api.asdsec.agentcontrol.FileUploadReq
	8,  // 13: api.asdsec.agentcontrol.AgentControl.PullPlatAddr:input_type -> api.asdsec.agentcontrol.Empty
	18, // 14: api.asdsec.agentcontrol.AgentControl.PullModuleSwitch:input_type -> api.asdsec.agentcontrol.ModuleSwitchReq
	18, // 15: api.asdsec.agentcontrol.AgentControl.GetModuleSwitch:input_type -> api.asdsec.agentcontrol.ModuleSwitchReq
	12, // 16: api.asdsec.agentcontrol.AgentControl.PullTask:output_type -> api.asdsec.agentcontrol.TaskPullResp
	14, // 17: api.asdsec.agentcontrol.AgentControl.ReportState:output_type -> api.asdsec.agentcontrol.TaskStateResp
	17, // 18: api.asdsec.agentcontrol.AgentControl.FileUpload:output_type -> api.asdsec.agentcontrol.FileUploadResp
	15, // 19: api.asdsec.agentcontrol.AgentControl.PullPlatAddr:output_type -> api.asdsec.agentcontrol.PlatAddrResp
	19, // 20: api.asdsec.agentcontrol.AgentControl.PullModuleSwitch:output_type -> api.asdsec.agentcontrol.ModuleSwitchResp
	19, // 21: api.asdsec.agentcontrol.AgentControl.GetModuleSwitch:output_type -> api.asdsec.agentcontrol.ModuleSwitchResp
	16, // [16:22] is the sub-list for method output_type
	10, // [10:16] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_agentcontrol_v1_agent_control_proto_init() }
func file_agentcontrol_v1_agent_control_proto_init() {
	if File_agentcontrol_v1_agent_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_agentcontrol_v1_agent_control_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddrContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskPullReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskPullResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskStateResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatAddrResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileUploadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileUploadResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleSwitchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleSwitchResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agentcontrol_v1_agent_control_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agentcontrol_v1_agent_control_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_agentcontrol_v1_agent_control_proto_goTypes,
		DependencyIndexes: file_agentcontrol_v1_agent_control_proto_depIdxs,
		EnumInfos:         file_agentcontrol_v1_agent_control_proto_enumTypes,
		MessageInfos:      file_agentcontrol_v1_agent_control_proto_msgTypes,
	}.Build()
	File_agentcontrol_v1_agent_control_proto = out.File
	file_agentcontrol_v1_agent_control_proto_rawDesc = nil
	file_agentcontrol_v1_agent_control_proto_goTypes = nil
	file_agentcontrol_v1_agent_control_proto_depIdxs = nil
}
