// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: application/v1/app_agent.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	GetApp_AgentGetApp_FullMethodName = "/asdsec.core.api.app.GetApp/AgentGetApp"
)

// GetAppClient is the client API for GetApp service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GetAppClient interface {
	AgentGetApp(ctx context.Context, in *AgentGetAppReq, opts ...grpc.CallOption) (*GetAgentAppResp, error)
}

type getAppClient struct {
	cc grpc.ClientConnInterface
}

func NewGetAppClient(cc grpc.ClientConnInterface) GetAppClient {
	return &getAppClient{cc}
}

func (c *getAppClient) AgentGetApp(ctx context.Context, in *AgentGetAppReq, opts ...grpc.CallOption) (*GetAgentAppResp, error) {
	out := new(GetAgentAppResp)
	err := c.cc.Invoke(ctx, GetApp_AgentGetApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GetAppServer is the server API for GetApp service.
// All implementations must embed UnimplementedGetAppServer
// for forward compatibility
type GetAppServer interface {
	AgentGetApp(context.Context, *AgentGetAppReq) (*GetAgentAppResp, error)
	mustEmbedUnimplementedGetAppServer()
}

// UnimplementedGetAppServer must be embedded to have forward compatible implementations.
type UnimplementedGetAppServer struct {
}

func (UnimplementedGetAppServer) AgentGetApp(context.Context, *AgentGetAppReq) (*GetAgentAppResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AgentGetApp not implemented")
}
func (UnimplementedGetAppServer) mustEmbedUnimplementedGetAppServer() {}

// UnsafeGetAppServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GetAppServer will
// result in compilation errors.
type UnsafeGetAppServer interface {
	mustEmbedUnimplementedGetAppServer()
}

func RegisterGetAppServer(s grpc.ServiceRegistrar, srv GetAppServer) {
	s.RegisterService(&GetApp_ServiceDesc, srv)
}

func _GetApp_AgentGetApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AgentGetAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GetAppServer).AgentGetApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GetApp_AgentGetApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GetAppServer).AgentGetApp(ctx, req.(*AgentGetAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GetApp_ServiceDesc is the grpc.ServiceDesc for GetApp service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GetApp_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "asdsec.core.api.app.GetApp",
	HandlerType: (*GetAppServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AgentGetApp",
			Handler:    _GetApp_AgentGetApp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "application/v1/app_agent.proto",
}
