package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
)

// sessionTrackerRepo 会话跟踪Repository实现
type sessionTrackerRepo struct {
	data *Data
	log  *log.Helper
}

// NewSessionTrackerRepo 创建会话跟踪Repository
func NewSessionTrackerRepo(data *Data, logger log.Logger) biz.SessionTrackerRepo {
	return &sessionTrackerRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// CreateSessionTracker 创建会话跟踪记录
func (r *sessionTrackerRepo) CreateSessionTracker(ctx context.Context, param dto.CreateSessionTrackerParam) error {
	tracker := &model.TbUserSessionTracker{
		ID:             param.ID,
		CorpID:         param.CorpID,
		UserID:         param.UserID,
		ClientType:     string(param.ClientType),
		ClientCategory: string(param.ClientCategory),
		JwtID:          param.JWTId,
		RefreshJwtID:   param.RefreshJWTId, // 新增：保存刷新令牌的JWT ID
		DeviceID:       param.DeviceID,
		IPAddress:      param.IPAddress, // 保存IP地址
		Status:         "active",        // 默认状态为活跃
		LoginTime:      time.Now(),
		LastActiveTime: time.Now(),
		ExpiresAt:      param.ExpiresAt,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	err := r.data.db.WithContext(ctx).Create(tracker).Error
	if err != nil {
		r.log.Errorf("CreateSessionTracker failed: %v", err)
		return err
	}

	return nil
}

// GetUserActiveJWTIds 获取用户活跃的JWT IDs（按登录时间排序）
func (r *sessionTrackerRepo) GetUserActiveJWTIds(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) ([]string, error) {
	var jwtIds []string

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND user_id = ? AND client_category = ? AND status = ? AND expires_at > ?",
			corpId, userId, string(clientCategory), "active", time.Now()).
		Order("login_time ASC").
		Pluck("jwt_id", &jwtIds).Error

	if err != nil {
		r.log.Errorf("GetUserActiveJWTIds failed: %v", err)
		return nil, err
	}

	return jwtIds, nil
}

// UpdateLastActiveTime 更新最后活跃时间
func (r *sessionTrackerRepo) UpdateLastActiveTime(ctx context.Context, jwtId string) error {
	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("jwt_id = ?", jwtId).
		Update("last_active_time", time.Now()).
		Update("updated_at", time.Now()).Error

	if err != nil {
		r.log.Errorf("UpdateLastActiveTime failed: %v", err)
		return err
	}

	return nil
}

// DeleteSessionTracker 删除会话跟踪记录
func (r *sessionTrackerRepo) DeleteSessionTracker(ctx context.Context, jwtId string) error {
	err := r.data.db.WithContext(ctx).
		Where("jwt_id = ?", jwtId).
		Delete(&model.TbUserSessionTracker{}).Error

	if err != nil {
		r.log.Errorf("DeleteSessionTracker failed: %v", err)
		return err
	}

	return nil
}

// DeleteExpiredTrackers 删除过期的会话跟踪记录
func (r *sessionTrackerRepo) DeleteExpiredTrackers(ctx context.Context) error {
	err := r.data.db.WithContext(ctx).
		Where("expires_at < ?", time.Now()).
		Delete(&model.TbUserSessionTracker{}).Error

	if err != nil {
		r.log.Errorf("DeleteExpiredTrackers failed: %v", err)
		return err
	}

	return nil
}

// GetActiveSessionCount 获取活跃会话数量
func (r *sessionTrackerRepo) GetActiveSessionCount(ctx context.Context, corpId, userId string, clientType dto.ClientType) (int, error) {
	var count int64

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND user_id = ? AND client_type = ? AND expires_at > ?",
			corpId, userId, string(clientType), time.Now()).
		Count(&count).Error

	if err != nil {
		r.log.Errorf("GetActiveSessionCount failed: %v", err)
		return 0, err
	}

	return int(count), nil
}

// GetActiveSessionCountByCategory 按客户端分类获取活跃会话数量
func (r *sessionTrackerRepo) GetActiveSessionCountByCategory(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) (int, error) {
	var count int64

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND user_id = ? AND client_category = ? AND status = ? AND expires_at > ?",
			corpId, userId, string(clientCategory), "active", time.Now()).
		Count(&count).Error

	if err != nil {
		r.log.Errorf("GetActiveSessionCountByCategory failed: %v", err)
		return 0, err
	}

	return int(count), nil
}

// GetOldestJWTId 获取最旧的JWT ID
func (r *sessionTrackerRepo) GetOldestJWTId(ctx context.Context, corpId, userId string, clientType dto.ClientType) (string, error) {
	var jwtId string

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND user_id = ? AND client_type = ? AND expires_at > ?",
			corpId, userId, string(clientType), time.Now()).
		Order("login_time ASC").
		Limit(1).
		Pluck("jwt_id", &jwtId).Error

	if err != nil {
		r.log.Errorf("GetOldestJWTId failed: %v", err)
		return "", err
	}

	return jwtId, nil
}

// GetOldestJWTIdByCategory 按客户端分类获取最旧的JWT ID
func (r *sessionTrackerRepo) GetOldestJWTIdByCategory(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) (string, error) {
	var jwtId string

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND user_id = ? AND client_category = ? AND status = ? AND expires_at > ?",
			corpId, userId, string(clientCategory), "active", time.Now()).
		Order("login_time ASC").
		Limit(1).
		Pluck("jwt_id", &jwtId).Error

	if err != nil {
		r.log.Errorf("GetOldestJWTIdByCategory failed: %v", err)
		return "", err
	}

	return jwtId, nil
}

// GetInactiveJWTId 获取最不活跃的JWT ID
func (r *sessionTrackerRepo) GetInactiveJWTId(ctx context.Context, corpId, userId string, clientType dto.ClientType) (string, error) {
	var jwtId string

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND user_id = ? AND client_type = ? AND expires_at > ?",
			corpId, userId, string(clientType), time.Now()).
		Order("last_active_time ASC").
		Limit(1).
		Pluck("jwt_id", &jwtId).Error

	if err != nil {
		r.log.Errorf("GetInactiveJWTId failed: %v", err)
		return "", err
	}

	return jwtId, nil
}

// GetInactiveJWTIdByCategory 按客户端分类获取最不活跃的JWT ID
func (r *sessionTrackerRepo) GetInactiveJWTIdByCategory(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) (string, error) {
	var jwtId string

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND user_id = ? AND client_category = ? AND status = ? AND expires_at > ?",
			corpId, userId, string(clientCategory), "active", time.Now()).
		Order("last_active_time ASC").
		Limit(1).
		Pluck("jwt_id", &jwtId).Error

	if err != nil {
		r.log.Errorf("GetInactiveJWTIdByCategory failed: %v", err)
		return "", err
	}

	return jwtId, nil
}

// GetSessionByJWTId 根据JWT ID获取会话详情
func (r *sessionTrackerRepo) GetSessionByJWTId(ctx context.Context, jwtId string) (*dto.UserSessionInfo, error) {
	var tracker model.TbUserSessionTracker

	err := r.data.db.WithContext(ctx).
		Where("jwt_id = ? AND status = ? AND expires_at > ?", jwtId, "active", time.Now()).
		First(&tracker).Error

	if err != nil {
		r.log.Errorf("GetSessionByJWTId failed: %v", err)
		return nil, err
	}

	// 获取用户信息
	var user model.TbUserEntity
	err = r.data.db.WithContext(ctx).
		Where("id = ?", tracker.UserID).
		First(&user).Error

	if err != nil {
		r.log.Errorf("GetUser for session failed: %v", err)
		return nil, err
	}

	// 转换为DTO
	sessionInfo := &dto.UserSessionInfo{
		ID:             tracker.ID,
		CorpID:         tracker.CorpID,
		UserID:         tracker.UserID,
		UserName:       user.Name,
		DisplayName:    user.DisplayName,
		ClientType:     dto.ClientType(tracker.ClientType),
		SessionToken:   tracker.JwtID,     // 使用JWT ID作为session token
		DeviceInfo:     tracker.DeviceID,  // 简化处理，使用设备ID作为设备信息
		IPAddress:      tracker.IPAddress, // 从数据库中读取IP地址
		UserAgent:      "",                // UserAgent不从数据库获取
		LoginTime:      tracker.LoginTime,
		LastActiveTime: tracker.LastActiveTime,
		ExpiresAt:      tracker.ExpiresAt,
		Status:         tracker.Status, // 使用实际状态
		JWTId:          tracker.JwtID,
		RefreshJWTId:   tracker.RefreshJwtID, // 新增：刷新令牌的JWT ID
		PolicyID:       "",                   // TODO: 需要从认证策略获取
	}

	return sessionInfo, nil
}

// KickSession 踢出会话（软删除）
func (r *sessionTrackerRepo) KickSession(ctx context.Context, jwtId, reason string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":      "kicked",
		"logout_time": now,
		"updated_at":  now,
	}

	// 如果提供了踢出原因，则更新
	if reason != "" {
		updates["kick_reason"] = reason
	}

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("jwt_id = ? AND status = ?", jwtId, "active").
		Updates(updates).Error

	if err != nil {
		r.log.Errorf("KickSession failed: %v", err)
		return err
	}

	r.log.Infof("Session kicked successfully: jwtId=%s, reason=%s", jwtId, reason)
	return nil
}

// LogoutSession 用户主动登出（更新状态为logout）
func (r *sessionTrackerRepo) LogoutSession(ctx context.Context, jwtId, reason string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":      "logout",
		"logout_time": now,
		"updated_at":  now,
	}

	// 如果提供了登出原因，则更新kick_reason字段（复用该字段存储原因）
	if reason != "" {
		updates["kick_reason"] = reason
	}

	err := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("jwt_id = ? AND status = ?", jwtId, "active").
		Updates(updates).Error

	if err != nil {
		r.log.Errorf("LogoutSession failed: %v", err)
		return err
	}

	r.log.Infof("Session logout successfully: jwtId=%s, reason=%s", jwtId, reason)
	return nil
}

// GetAllActiveJWTIdsByCorpId 获取企业下所有活跃的JWT ID（支持按用户名和客户端类型过滤）
func (r *sessionTrackerRepo) GetAllActiveJWTIdsByCorpId(ctx context.Context, corpId, userName, clientType string, limit, offset int) ([]string, int, error) {
	var jwtIds []string
	var total int64

	// 构建查询条件
	query := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("corp_id = ? AND status = ? AND expires_at > ?", corpId, "active", time.Now())

	// 如果指定了用户名，需要join用户表进行过滤
	if userName != "" {
		query = query.Joins("JOIN tb_user_entity ON tb_user_session_tracker.user_id = tb_user_entity.id").
			Where("(tb_user_entity.name LIKE ? OR tb_user_entity.display_name LIKE ?)", "%"+userName+"%", "%"+userName+"%")
	}

	// 如果指定了客户端类型
	if clientType != "" && clientType != "all" {
		query = query.Where("client_type = ?", clientType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		r.log.Errorf("GetAllActiveJWTIdsByCorpId count failed: %v", err)
		return nil, 0, err
	}

	// 获取JWT IDs（按登录时间倒序排列）
	err = query.Order("login_time DESC").
		Offset(offset).
		Limit(limit).
		Pluck("jwt_id", &jwtIds).Error

	if err != nil {
		r.log.Errorf("GetAllActiveJWTIdsByCorpId failed: %v", err)
		return nil, 0, err
	}

	return jwtIds, int(total), nil
}

// GetAllActiveSessionsByCorpId 获取企业下所有活跃会话（支持按用户名和客户端类型过滤）
func (r *sessionTrackerRepo) GetAllActiveSessionsByCorpId(ctx context.Context, corpId, userName, clientType string, limit, offset int) ([]*dto.UserSessionInfo, int, error) {
	var trackers []model.TbUserSessionTracker
	var total int64

	// 构建查询条件
	query := r.data.db.WithContext(ctx).
		Model(&model.TbUserSessionTracker{}).
		Where("tb_user_session_tracker.corp_id = ? AND tb_user_session_tracker.status = ? AND tb_user_session_tracker.expires_at > ?", corpId, "active", time.Now())

	// 如果指定了用户名，需要join用户表进行过滤
	if userName != "" {
		query = query.Joins("JOIN tb_user_entity ON tb_user_session_tracker.user_id = tb_user_entity.id").
			Where("(tb_user_entity.name LIKE ? OR tb_user_entity.display_name LIKE ?)", "%"+userName+"%", "%"+userName+"%")
	}

	// 如果指定了客户端类型
	if clientType != "" && clientType != "all" {
		query = query.Where("tb_user_session_tracker.client_type = ?", clientType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		r.log.Errorf("GetAllActiveSessionsByCorpId count failed: %v", err)
		return nil, 0, err
	}

	// 获取会话记录（按登录时间倒序排列）
	err = query.Order("tb_user_session_tracker.login_time DESC").
		Offset(offset).
		Limit(limit).
		Find(&trackers).Error

	if err != nil {
		r.log.Errorf("GetAllActiveSessionsByCorpId failed: %v", err)
		return nil, 0, err
	}

	// 转换为DTO格式
	var sessions []*dto.UserSessionInfo
	for _, tracker := range trackers {
		// 获取用户信息
		var user model.TbUserEntity
		err = r.data.db.WithContext(ctx).
			Where("id = ?", tracker.UserID).
			First(&user).Error

		if err != nil {
			r.log.Warnf("GetUser for session failed, userId=%s: %v", tracker.UserID, err)
			// 如果获取用户信息失败，跳过这个会话
			continue
		}

		// 转换为DTO
		sessionInfo := &dto.UserSessionInfo{
			ID:             tracker.ID,
			CorpID:         tracker.CorpID,
			UserID:         tracker.UserID,
			UserName:       user.Name,
			DisplayName:    user.DisplayName,
			ClientType:     dto.ClientType(tracker.ClientType),
			SessionToken:   tracker.JwtID,     // 使用JWT ID作为session token
			DeviceInfo:     tracker.DeviceID,  // 使用设备ID作为设备信息
			IPAddress:      tracker.IPAddress, // 从数据库中读取IP地址
			UserAgent:      "",                // UserAgent不从数据库获取，需要时从请求上下文中获取
			LoginTime:      tracker.LoginTime,
			LastActiveTime: tracker.LastActiveTime,
			ExpiresAt:      tracker.ExpiresAt,
			Status:         tracker.Status,
			JWTId:          tracker.JwtID,
			RefreshJWTId:   tracker.RefreshJwtID, // 新增：刷新令牌的JWT ID
			PolicyID:       "",                   // TODO: 需要从认证策略获取
		}

		sessions = append(sessions, sessionInfo)
	}

	return sessions, int(total), nil
}

// GetSessionByRefreshJWTId 根据刷新令牌JWT ID获取会话详情
func (r *sessionTrackerRepo) GetSessionByRefreshJWTId(ctx context.Context, refreshJwtId string) (*dto.UserSessionInfo, error) {
	var tracker model.TbUserSessionTracker

	err := r.data.db.WithContext(ctx).
		Where("refresh_jwt_id = ? AND status = ? AND expires_at > ?", refreshJwtId, "active", time.Now()).
		First(&tracker).Error

	if err != nil {
		r.log.Errorf("GetSessionByRefreshJWTId failed: %v", err)
		return nil, err
	}

	// 获取用户信息
	var user model.TbUserEntity
	err = r.data.db.WithContext(ctx).
		Where("id = ?", tracker.UserID).
		First(&user).Error

	if err != nil {
		r.log.Errorf("GetUser for session failed: %v", err)
		return nil, err
	}

	// 转换为DTO
	sessionInfo := &dto.UserSessionInfo{
		ID:             tracker.ID,
		CorpID:         tracker.CorpID,
		UserID:         tracker.UserID,
		UserName:       user.Name,
		DisplayName:    user.DisplayName,
		ClientType:     dto.ClientType(tracker.ClientType),
		SessionToken:   tracker.JwtID,     // 使用JWT ID作为session token
		DeviceInfo:     tracker.DeviceID,  // 简化处理，使用设备ID作为设备信息
		IPAddress:      tracker.IPAddress, // 从数据库中读取IP地址
		UserAgent:      "",                // UserAgent不从数据库获取
		LoginTime:      tracker.LoginTime,
		LastActiveTime: tracker.LastActiveTime,
		ExpiresAt:      tracker.ExpiresAt,
		Status:         tracker.Status, // 使用实际状态
		JWTId:          tracker.JwtID,
		RefreshJWTId:   tracker.RefreshJwtID, // 新增：刷新令牌的JWT ID
		PolicyID:       "",                   // TODO: 需要从认证策略获取
	}

	return sessionInfo, nil
}

// UpdateSessionAccessToken 更新会话的access token JTI
func (r *sessionTrackerRepo) UpdateSessionAccessToken(ctx context.Context, sessionID, newAccessTokenJTI string) error {
	now := time.Now()

	// 更新数据库记录
	result := r.data.db.WithContext(ctx).Model(&model.TbUserSessionTracker{}).
		Where("id = ? AND status = ?", sessionID, "active").
		Updates(map[string]interface{}{
			"jwt_id":           newAccessTokenJTI,
			"last_active_time": now,
			"updated_at":       now,
		})

	if result.Error != nil {
		r.log.Errorf("UpdateSessionAccessToken failed: %v", result.Error)
		return result.Error
	}

	// 检查是否有记录被更新
	if result.RowsAffected == 0 {
		r.log.Warnf("No active session found for sessionID: %s", sessionID)
		return nil // 不返回错误，因为可能会话已过期
	}

	r.log.Infof("Session access token updated successfully: sessionID=%s, newJTI=%s", sessionID, newAccessTokenJTI)
	return nil
}

// GetSessionBySessionId 根据会话ID获取会话详情
func (r *sessionTrackerRepo) GetSessionBySessionId(ctx context.Context, sessionId string) (*dto.UserSessionInfo, error) {
	var tracker model.TbUserSessionTracker

	err := r.data.db.WithContext(ctx).
		Where("id = ? AND status = ? AND expires_at > ?", sessionId, "active", time.Now()).
		First(&tracker).Error

	if err != nil {
		r.log.Errorf("GetSessionBySessionId failed: %v", err)
		return nil, err
	}

	// 获取用户信息
	var user model.TbUserEntity
	err = r.data.db.WithContext(ctx).
		Where("id = ?", tracker.UserID).
		First(&user).Error

	if err != nil {
		r.log.Errorf("GetUser for session failed: %v", err)
		return nil, err
	}

	// 转换为DTO
	sessionInfo := &dto.UserSessionInfo{
		ID:             tracker.ID,
		CorpID:         tracker.CorpID,
		UserID:         tracker.UserID,
		UserName:       user.Name,
		DisplayName:    user.DisplayName,
		ClientType:     dto.ClientType(tracker.ClientType),
		SessionToken:   tracker.JwtID,     // 使用JWT ID作为session token
		DeviceInfo:     tracker.DeviceID,  // 简化处理，使用设备ID作为设备信息
		IPAddress:      tracker.IPAddress, // 从数据库中读取IP地址
		UserAgent:      "",                // UserAgent不从数据库获取
		LoginTime:      tracker.LoginTime,
		LastActiveTime: tracker.LastActiveTime,
		ExpiresAt:      tracker.ExpiresAt,
		Status:         tracker.Status, // 使用实际状态
		JWTId:          tracker.JwtID,
		RefreshJWTId:   tracker.RefreshJwtID, // 新增：刷新令牌的JWT ID
		PolicyID:       "",                   // TODO: 需要从认证策略获取
	}

	return sessionInfo, nil
}
