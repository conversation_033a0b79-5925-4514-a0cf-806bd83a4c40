package agent_conf

import (
	pb "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"context"
	"database/sql"
	"google.golang.org/grpc"
	"sync"
)

func GetAgentConf(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   GetAgentConfReal,
		RunType:      common.SimpleSend,
		WaitSecond:   30,
		RandomOffset: 3,
	}
	common.Send(param)
}

func GetAgentConfReal(conn *grpc.ClientConn, ctx context.Context) error {
	// todo cl 后续做配置版本/模块等精细化处理
	req := pb.ConfigReq{
		CfgApplianceType: pb.CfgApplianceType_AGENT_CFG,
		CfgType:          pb.CfgType_PROCESS_FILTER_CFG,
	}
	client := pb.NewApplianceCfgClient(conn)
	cfg, err := client.GetCfg(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("getCfg from platform err:%v", err)
		return err
	}
	if cfg == nil {
		return nil
	}
	data := cfg.GetConfigData()

	sqlite, err := global.InitSqlite()
	defer global.CloseSqlite(sqlite)
	if len(data) <= 0 {
		removeExistCfg(sqlite)
		return nil
	}

	if err != nil {
		global.Logger.Sugar().Errorf("initSqlite err: %v", err)
		return err
	}
	_, err = sqlite.Exec("insert into tb_agent_config (id, config_type, config_data,config_text,data_format) values (? ,?,?,?,?) on CONFLICT(id)"+
		" do update set config_type=excluded.config_type, config_data=excluded.config_data",
		cfg.CfgId, "process_file_mon", data)
	if err != nil {
		global.Logger.Sugar().Errorf("insert tb_client_policy err: %v", err)
		return nil
	}
	return nil
}

func removeExistCfg(sqlite *sql.DB) {
	_, _ = sqlite.Exec("DELETE FROM tb_agent_config WHERE config_type = ? ", "process_file_mon")
}
