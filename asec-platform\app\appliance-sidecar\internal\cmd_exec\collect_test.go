package cmd_exec

import (
	"fmt"
	"os"
	"os/exec"
	"testing"
)

func Test_collectLog(t *testing.T) {
	// 生成批处理脚本内容
	batContent := `
	@echo off
	wevtutil epl System %s /q:"*[System[TimeCreated[timediff(@SystemTime) <= 259200000]]]" /ow:true
	wevtutil epl Application %s "/q:*[System[TimeCreated[timediff(@SystemTime) <= 259200000]]]" /ow:true
	`
	batContent = fmt.Sprintf(batContent, sysLogPath, appLogPath)
	// 将脚本内容写入临时文件
	tempBatFile, err := os.CreateTemp("", "script*.bat")
	defer os.Remove(tempBatFile.Name()) // 确保程序结束时删除临时文件
	if err != nil {
		panic(err)
	}

	if _, err := tempBatFile.WriteString(batContent); err != nil {
		panic(err)
	}
	if err := tempBatFile.Close(); err != nil {
		panic(err)
	}

	// 执行批处理脚本
	cmd := exec.Command("cmd.exe", "/C", tempBatFile.Name())
	fmt.Println(cmd.String())

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	err = cmd.Run()
	if err != nil {
		panic(err)
	}
}
