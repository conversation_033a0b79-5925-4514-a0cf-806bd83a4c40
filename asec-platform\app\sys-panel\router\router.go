package router

import (
	"asdsec.com/asec/platform/app/sys-panel/handler"
	"asdsec.com/asec/platform/app/sys-panel/middleware"
	"asdsec.com/asec/platform/app/sys-panel/service"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func RegisterRoutes(r *gin.Engine, db *gorm.DB) {
	backupService := service.NewBackupService(db)
	backupHandler := handler.NewBackupHandler(backupService)
	nginxHandler := handler.NewNginxHandler(service.NewNginxService())
	wechatVerifyHandler := handler.NewWechatVerifyHandler(service.NewWechatVerifyService(db))

	// 为微信验证文件列表API创建特殊路由（支持API Key认证）
	r.GET("/system/v1/wechat-verify", middleware.APIKeyOrJWT(), wechatVerifyHandler.GetWechatVerifyList)

	// 其他路由继续使用JWT认证
	r.Use(Cors(), middleware.IsAdmin())

	// 基础v1路由组
	v1 := r.Group("/system/v1")
	{
		// 备份管理路由组
		backup := v1.Group("/backup")
		{
			backup.GET("", backupHandler.GetBackupList)
			backup.POST("", backupHandler.CreateBackup)
			backup.POST("/:id/restore", backupHandler.RestoreBackup)
			backup.DELETE("/:id", backupHandler.DeleteBackup)

			// 添加备份配置相关路由
			backup.GET("/config", backupHandler.GetBackupConfig)
			backup.PUT("/config", backupHandler.UpdateBackupConfig)

			// 下载和上传备份路由
			backup.GET("/download/:id", backupHandler.DownloadBackup)
			backup.POST("/upload", backupHandler.UploadBackup)

			// 清理备份路由
			backup.POST("/cleanup", backupHandler.CleanupBackups)
		}

		// Nginx SSL证书管理路由组
		nginx := v1.Group("/nginx")
		{
			nginx.GET("/ssl", nginxHandler.GetSSLConfig)
			nginx.POST("/ssl", nginxHandler.UpdateCert)
		}

		// 企业微信验证文件管理路由组
		wechatVerify := v1.Group("/wechat-verify")
		{
			wechatVerify.POST("", wechatVerifyHandler.CreateWechatVerify)
			wechatVerify.GET("/:id", wechatVerifyHandler.GetWechatVerify)
			wechatVerify.PUT("/:id", wechatVerifyHandler.UpdateWechatVerify)
			wechatVerify.DELETE("/:id", wechatVerifyHandler.DeleteWechatVerify)
		}
	}
}
