// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: specialconfig/v1/special_config.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SpecialConfig_GetSpecialConfig_FullMethodName = "/api.specialconfig.SpecialConfig/GetSpecialConfig"
)

// SpecialConfigClient is the client API for SpecialConfig service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SpecialConfigClient interface {
	// 获取特殊配置通用Grpc接口
	GetSpecialConfig(ctx context.Context, in *GetSpecialConfigReq, opts ...grpc.CallOption) (*Reply, error)
}

type specialConfigClient struct {
	cc grpc.ClientConnInterface
}

func NewSpecialConfigClient(cc grpc.ClientConnInterface) SpecialConfigClient {
	return &specialConfigClient{cc}
}

func (c *specialConfigClient) GetSpecialConfig(ctx context.Context, in *GetSpecialConfigReq, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, SpecialConfig_GetSpecialConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpecialConfigServer is the server API for SpecialConfig service.
// All implementations must embed UnimplementedSpecialConfigServer
// for forward compatibility
type SpecialConfigServer interface {
	// 获取特殊配置通用Grpc接口
	GetSpecialConfig(context.Context, *GetSpecialConfigReq) (*Reply, error)
	mustEmbedUnimplementedSpecialConfigServer()
}

// UnimplementedSpecialConfigServer must be embedded to have forward compatible implementations.
type UnimplementedSpecialConfigServer struct {
}

func (UnimplementedSpecialConfigServer) GetSpecialConfig(context.Context, *GetSpecialConfigReq) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSpecialConfig not implemented")
}
func (UnimplementedSpecialConfigServer) mustEmbedUnimplementedSpecialConfigServer() {}

// UnsafeSpecialConfigServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SpecialConfigServer will
// result in compilation errors.
type UnsafeSpecialConfigServer interface {
	mustEmbedUnimplementedSpecialConfigServer()
}

func RegisterSpecialConfigServer(s grpc.ServiceRegistrar, srv SpecialConfigServer) {
	s.RegisterService(&SpecialConfig_ServiceDesc, srv)
}

func _SpecialConfig_GetSpecialConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSpecialConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpecialConfigServer).GetSpecialConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SpecialConfig_GetSpecialConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpecialConfigServer).GetSpecialConfig(ctx, req.(*GetSpecialConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SpecialConfig_ServiceDesc is the grpc.ServiceDesc for SpecialConfig service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SpecialConfig_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.specialconfig.SpecialConfig",
	HandlerType: (*SpecialConfigServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSpecialConfig",
			Handler:    _SpecialConfig_GetSpecialConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specialconfig/v1/special_config.proto",
}
