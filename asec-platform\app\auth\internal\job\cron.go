package job

import (
	"asdsec.com/asec/platform/app/auth/internal/conf"
	"context"
	"sync/atomic"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"github.com/robfig/cron/v3"
)

var running int32 = 0

func RootGroupAutoSync(conf *conf.Server, userUsecase *biz.UserUsecase) *cron.Cron {
	c := cron.New()
	//非主节点不执行定时任务
	if !conf.Primary {
		return c
	}
	// 添加定时任务逻辑
	c.AddFunc("* 3 * * *", func() {
		if !atomic.CompareAndSwapInt32(&running, 0, 1) {
			// 上次任务还未完成
			return
		}
		defer atomic.StoreInt32(&running, 0)

		ctx := context.Background()
		userUsecase.RootGroupSync(ctx)
	})
	return c
}
