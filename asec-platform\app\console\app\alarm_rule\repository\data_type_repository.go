package repository

import (
	"asdsec.com/asec/platform/app/console/app/alarm_rule/common"
	"asdsec.com/asec/platform/app/console/app/alarm_rule/model"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"time"
)

// public

type DataTypeSum struct {
	Id    string
	Name  string
	Count int
}

type DataTypeRepositoryImpl interface {
	GetDataType(ctx context.Context) ([]model.SensitiveStrategyDB, error)
	GetDataTypeSummary(ctx context.Context, startT, endT time.Time) ([]model.SensitiveStrategyDB, error)
	GetDataTypeCount(ctx context.Context, startT, endT time.Time) (map[string]int64, error)
}

func NewDataTypeRepository() DataTypeRepositoryImpl {
	return &dataTypeRepositoryPriv{}
}

var senLevel = map[int]string{
	1: "L1",
	2: "L2",
	3: "L3",
	4: "L4",
}

type dataTypeRepositoryPriv struct {
}

func (d *dataTypeRepositoryPriv) GetDataTypeSummary(ctx context.Context, startT, endT time.Time) ([]model.SensitiveStrategyDB, error) {
	result := make([]model.SensitiveStrategyDB, 0)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	senCount, err := d.GetDataTypeCount(ctx, startT, endT)
	if err != nil {
		return result, err
	}
	for k, v := range senLevel {
		var strategy []model.SensitiveStrategyDB
		err = db.Model(&model.SensitiveStrategyDB{}).Where("sensitive_level=?", k).Find(&strategy).Error
		if err != nil {
			return result, err
		}
		var tmpCount int64
		var tmpStrategy []model.SensitiveStrategyDB
		for m, n := range strategy {
			strategy[m].Count = senCount[n.Id]
			tmpCount += senCount[n.Id]
			if senCount[n.Id] > 0 {
				tmpStrategy = append(tmpStrategy, strategy[m])
			}
		}
		if tmpCount > 0 {
			tmp := model.SensitiveStrategyDB{Id: v, RuleName: v, SensitiveLevel: int16(k)}
			tmp.Children = tmpStrategy
			tmp.Count = tmpCount
			result = append(result, tmp)
		}
	}
	return result, err
}

func (d *dataTypeRepositoryPriv) GetDataType(ctx context.Context) ([]model.SensitiveStrategyDB, error) {
	result := make([]model.SensitiveStrategyDB, len(senLevel)+1)
	for k, v := range senLevel {
		tmp := model.SensitiveStrategyDB{Id: v, RuleName: v, SensitiveLevel: int16(k)}
		result[k] = tmp
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	for k, _ := range senLevel {
		var strategy []model.SensitiveStrategyDB
		err = db.Model(&model.SensitiveStrategyDB{}).Where("sensitive_level=? and built_in != 1", k).Find(&strategy).Error
		if err != nil {
			return result, err
		}
		result[k].Children = strategy
		// 用户前端判断是否有子类
		result[k].Disable = len(strategy) == 0
	}
	return result[1:], err
}

type RuleIdCountStru struct {
	RuleId string `gorm:"column:sensitive_rule_id" json:"sensitive_rule_id"`
	Count  int64  `json:"count"`
}

func (d *dataTypeRepositoryPriv) GetDataTypeCount(ctx context.Context, startT, endT time.Time) (map[string]int64, error) {
	ckDB, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}
	var RuleIdCount []RuleIdCountStru
	err = common.GetFeildCountByDB(ckDB, &model.AlertEventDB{}, "sensitive_rule_id", &RuleIdCount, startT, endT)
	if err != nil {
		return nil, err
	}
	dataMap := make(map[string]int64, 0)
	for _, v := range RuleIdCount {
		dataMap[v.RuleId] = v.Count
	}
	return dataMap, nil
}
