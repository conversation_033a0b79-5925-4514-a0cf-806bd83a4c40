// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserSource = "tb_user_source"

// TbUserSource mapped from table <tb_user_source>
type TbUserSource struct {
	ID           string    `gorm:"column:id;primaryKey;comment:用户来源id" json:"id"`                     // 用户来源id
	Name         string    `gorm:"column:name;not null;comment:用户来源名称，租户下唯一" json:"name"`             // 用户来源名称，租户下唯一
	SourceType   string    `gorm:"column:source_type;not null;comment:来源类型，租户下唯一" json:"source_type"` // 来源类型，租户下唯一
	CorpID       string    `gorm:"column:corp_id;comment:租户id外键，维护租户-用户来源的1对多关系" json:"corp_id"`      // 租户id外键，维护租户-用户来源的1对多关系
	CreatedAt    time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	TemplateType string    `gorm:"column:template_type" json:"template_type"`
}

// TableName TbUserSource's table name
func (*TbUserSource) TableName() string {
	return TableNameTbUserSource
}
