package filter

import (
	alertComm "asdsec.com/asec/platform/app/console/app/alert/common"
	"asdsec.com/asec/platform/app/console/app/alert/model"
	"asdsec.com/asec/platform/app/console/app/alert/plat/dingtalk"
	"asdsec.com/asec/platform/app/console/app/alert/plat/dingtalk/robot"
	"asdsec.com/asec/platform/app/console/app/alert/plat/wechat"
	"asdsec.com/asec/platform/app/console/app/alert/plat/wechat/message"
	"asdsec.com/asec/platform/app/console/app/alert/repository"
	"asdsec.com/asec/platform/app/console/app/alert/service"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io/ioutil"
	"strconv"
	"sync"
	"time"
)

var sendInstance SendService

var SendInit sync.Once

type SendDB struct {
	db repository.RecordRepository
}

func GetSendService() SendService {
	SendInit.Do(func() {
		sendInstance = &SendDB{db: repository.NewRecordRepository()}
	})
	return sendInstance
}

type MsgType struct {
	AssetName     string `json:"assetName"`
	HostIp        string `json:"hostIp"`
	ThreatSubType string `json:"threatSubType"`
	RiskLevel     string `json:"riskLevel"`
	EventDesc     string `json:"eventDesc"`
	OccurTime     int64  `json:"occurTime"`
}

var LevelType = map[string]string{
	"high":   "高危",
	"middle": "中危",
	"low":    "低危",
}

func (s *SendDB) FilterMsgAndSend(ctx context.Context, msg []byte) error {
	var msgType MsgType
	var threshold model.LimitSetting
	isSendWx := false
	isSendDingTalk := false
	err := json.Unmarshal(msg, &msgType)
	if err != nil {
		global.SysLog.Error(err.Error())
		return err
	}
	if _, ok := alertComm.AlertType[msgType.ThreatSubType]; !ok {
		return nil
	}
	alertType := alertComm.AlertType[msgType.ThreatSubType]
	//根据用户设置的setting过滤
	setting, err := service.GetSettingService().GetSettingByName(ctx, alertComm.TenantId, alertType)
	if err != nil {
		global.SysLog.Error(err.Error())
		return err
	}
	if setting.ID > 0 {
		err := json.Unmarshal([]byte(setting.Threshold), &threshold)
		if err != nil {
			global.SysLog.Error(err.Error())
			return err
		}
		if (msgType.RiskLevel == "high" && threshold.High == 0) ||
			(msgType.RiskLevel == "middle" && threshold.Middle == 0) ||
			(msgType.RiskLevel == "low" && threshold.Low == 0) {
			return nil
		}
		if setting.IsQyWx == 0 && setting.IsDingTalk == 0 {
			return nil
		}
		if setting.IsQyWx == 1 {
			isSendWx = true
		}
		if setting.IsDingTalk == 1 {
			isSendDingTalk = true
		}
	}
	occTime := time.Unix(msgType.OccurTime/1000, 0).Format("2006-01-02 15:04:05")
	kafkaMsg := fmt.Sprintf("安全事件告警\n\n>**告警级别**:【%s】\n\n>**告警类型**:【%s】\n\n>**影响主机**:%s\n\n"+
		">**告警详情**:【%s】\n\n>**发现时间**:%s",
		LevelType[msgType.RiskLevel], alertType, msgType.HostIp, strconv.Quote(msgType.EventDesc), occTime)
	data, err := service.GetNotifyService().GetRobots(ctx, alertComm.TenantId)
	if err != nil {
		global.SysLog.Error(err.Error())
		return err
	}
	if len(data) > 0 {
		for _, v := range data {
			record := model.AlertRecord{
				SendMsg:    kafkaMsg,
				UpdateTime: time.Now(),
			}
			if v.PlatName == alertComm.WeChat && isSendWx {
				err := s.SendWxMsg(ctx, v.WebhookAddr, kafkaMsg)
				record.SendPlat = alertComm.WeChat
				record.WebhookAddr = v.WebhookAddr
				if err != nil {
					record.SendResult = -1
					global.SysLog.Error(err.Error())
					dbErr := s.db.AddRecord(ctx, &record)
					if dbErr != nil {
						global.SysLog.Error(dbErr.Error())
					}
					continue
				}
			} else if v.PlatName == alertComm.DingTalk && isSendDingTalk {
				err := s.SendDingMsg(ctx, v.WebhookAddr, v.ApiSecret, kafkaMsg)
				record.SendPlat = alertComm.DingTalk
				record.WebhookAddr = v.WebhookAddr
				record.ApiSecret = v.ApiSecret
				if err != nil {
					record.SendResult = -1
					global.SysLog.Error(err.Error())
					dbErr := s.db.AddRecord(ctx, &record)
					if dbErr != nil {
						global.SysLog.Error(dbErr.Error())
					}
					continue
				}
			} else {
				continue
			}
			dbErr := s.db.AddRecord(ctx, &record)
			if dbErr != nil {
				global.SysLog.Error(dbErr.Error())
			}
		}
	}
	return nil
}

func (s *SendDB) SendDingMsg(ctx context.Context, webHook, secret, kafkaMsg string) error {
	// markdown类型
	markdownTitle := "平台告警"
	dt := dingtalk.New(webHook, dingtalk.WithSecret(secret))
	atMobiles := robot.SendWithIsAtAll(false)
	if err := dt.RobotSendMarkdown(markdownTitle, kafkaMsg, atMobiles); err != nil {
		global.SysLog.Error(err.Error())
		return err
	}
	return printResult(dt)
}

func printResult(dt *dingtalk.DingTalk) error {
	response, err := dt.GetResponse()
	if err != nil {
		return err
	}
	reqBody, err := response.Request.GetBody()
	if err != nil {
		return err
	}
	reqData, err := ioutil.ReadAll(reqBody)
	if err != nil {
		return err
	}
	global.SysLog.Debug("发送消息成功", zap.String("message:", string(reqData)))
	return nil
}

func (s *SendDB) SendWxMsg(ctx context.Context, hook, kafkaMsg string) error {
	bot := wechat.QyBot{
		WebHook: hook,
	}
	msg := message.Message{
		MsgType: message.MarkdownStr,
		Markdown: message.Markdown_{
			Content: kafkaMsg,
		},
	}
	send, err := bot.Send(msg)
	if err != nil {
		return err
	}
	global.SysLog.Debug("发送消息成功", zap.Any("message:", send))
	return nil
}

func (s *SendDB) CleanRecord(ctx context.Context) {
	err := s.db.DeleteRecord(ctx, time.Now().AddDate(0, 0, -7))
	if err != nil {
		global.SysLog.Error(err.Error())
		return
	}
}

type SendService interface {
	FilterMsgAndSend(ctx context.Context, msg []byte) error
	SendDingMsg(ctx context.Context, webHook, secret, kafkaMsg string) error
	SendWxMsg(ctx context.Context, hook, kafkaMsg string) error
	CleanRecord(ctx context.Context)
}
