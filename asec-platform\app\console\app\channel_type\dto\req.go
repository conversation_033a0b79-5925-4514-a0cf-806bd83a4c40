package dto

import (
	pkgModel "asdsec.com/asec/platform/pkg/model"
	"github.com/lib/pq"
)

type CreateChannelTypeReq struct {
	Name            string                  `json:"name"  binding:"required"`
	Pid             string                  `json:"pid"`
	ProcessList     pq.StringArray          `json:"process_list"`
	IncludeFilePath []pkgModel.FilePathRule `json:"include_file_path"`
	ExcludeFilePath []pkgModel.FilePathRule `json:"exclude_file_path"`
}

type UpdateChannelTypeReq struct {
	CreateChannelTypeReq
	Id     string `json:"id"`
	Status int    `gorm:"column:status" json:"status"`
}

type GetChannelListReq struct {
	pkgModel.CommonPageReq
	BuiltIn int    `form:"built_in" json:"built_in"`
	Pid     string `form:"pid" json:"pid"`
}

type DeleteChannelReq struct {
	Id   string `json:"id"  binding:"required"`
	Name string `json:"name"`
	Pid  string `json:"pid"`
}

type GetChannelTypeListReq struct {
	pkgModel.CommonPageReq
	CName   string `json:"c_name"`
	BuiltIn int    `json:"built_in"`
}
