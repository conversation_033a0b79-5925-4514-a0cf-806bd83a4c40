package dto

import (
	"github.com/lib/pq"
	"time"
)

type DomainCertificate struct {
	Id                     string         `json:"id" gorm:"id"`
	Name                   string         `json:"name" gorm:"name"`
	Certificate            string         `json:"certificate" gorm:"certificate"`
	PrivateKey             string         `json:"private_key" gorm:"private_key"`
	Domain                 pq.StringArray `json:"domain" gorm:"domain;type:varchar[]"`
	SignatureAlgorithm     string         `json:"signature_algorithm" gorm:"signature_algorithm"`
	CertificateFingerprint string         `json:"certificate_fingerprint" gorm:"certificate_fingerprint"`
	IssueAgency            string         `json:"issue_agency" gorm:"issue_agency"`
	IssueTime              time.Time      `json:"issue_time" gorm:"issue_time"`
	ExpireTime             time.Time      `json:"expire_time" gorm:"expire_time"`
	CreatedAt              time.Time      `gorm:"column:created_at;comment:创建时间"`
	UpdatedAt              time.Time      `gorm:"column:updated_at;comment:更新时间"`
	PublicKeyAlgorithm     string         `json:"public_key_algorithm" gorm:"public_key_algorithm"`
	Status                 int            `json:"status" gorm:"status"`
	GatewayRs              []byte         `json:"gateway_rs" gorm:"gateway_rs;type:jsonb"`
}

func (DomainCertificate) TableName() string {
	return "tb_certificate"
}

type ApisixCertificateReq struct {
	Cert          string   `json:"cert"`
	Key           string   `json:"key"`
	Snis          []string `json:"snis"`
	ValidityStart int64    `json:"validity_start"`
	ValidityEnd   int64    `json:"validity_end"`
}
