package service

import (
	"context"
	"sort"

	"github.com/jinzhu/copier"

	"asdsec.com/asec/platform/app/auth/internal/common"

	v1 "asdsec.com/asec/platform/api/auth/v1"
	pb "asdsec.com/asec/platform/api/auth/v1/admin"

	"asdsec.com/asec/platform/app/auth/internal/dto"
)

func (s *AdminService) ListUserSourceType(ctx context.Context, req *pb.ListUserSourceTypeRequest) (*pb.ListUserSourceTypeReply, error) {
	var entries []*pb.ListUserSourceTypeReply_UserSourceType
	for k, v := range dto.UserSourceMap {
		entries = append(entries, &pb.ListUserSourceTypeReply_UserSourceType{
			Key: string(k),
			Val: v,
		})
	}
	sort.Slice(entries, func(i, j int) bool {
		return entries[i].Key < entries[j].Key
	})
	return &pb.ListUserSourceTypeReply{UserSourceTypeList: entries}, nil
}

func (s *AdminService) CreateUserSource(ctx context.Context, req *pb.CreateUserSourceRequest) (*pb.CreateUserSourceReply, error) {
	if _, ok := dto.UserSourceMap[dto.UserSourceType(req.SourceType)]; !ok {
		return &pb.CreateUserSourceReply{Status: pb.StatusCode_FAILED}, v1.ErrorSourceTypeNotSupport("sourceType=%v not support.", req.SourceType)
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateUserSourceReply{Status: pb.StatusCode_FAILED}, err
	}

	err = s.userSource.CreateUserSource(ctx, req.Name, req.SourceType, corpId)
	if err != nil {
		return &pb.CreateUserSourceReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateUserSourceReply{Status: pb.StatusCode_SUCCESS}, nil
}
func (s *AdminService) UpdateUserSource(ctx context.Context, req *pb.UpdateUserSourceRequest) (*pb.UpdateUserSourceReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateUserSourceReply{Status: pb.StatusCode_FAILED}, err
	}

	err = s.userSource.UpdateUserSource(ctx, req.Id, req.Name, corpId)
	if err != nil {
		return &pb.UpdateUserSourceReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateUserSourceReply{Status: pb.StatusCode_SUCCESS}, nil
}
func (s *AdminService) DeleteUserSource(ctx context.Context, req *pb.DeleteUserSourceRequest) (*pb.DeleteUserSourceReply, error) {
	return &pb.DeleteUserSourceReply{}, nil
}
func (s *AdminService) GetUserSource(ctx context.Context, req *pb.GetUserSourceRequest) (*pb.GetUserSourceReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.GetUserSourceReply{}, err
	}

	userSource, err := s.userSource.GetUserSource(ctx, corpId, req.Id)
	if err != nil {
		return &pb.GetUserSourceReply{}, err
	}
	return &pb.GetUserSourceReply{UserSource: &pb.UserSource{
		Id:         userSource.ID,
		Name:       userSource.Name,
		SourceType: userSource.SourceType,
	}}, nil
}
func (s *AdminService) ListUserSource(ctx context.Context, req *pb.ListUserSourceRequest) (*pb.ListUserSourceReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListUserSourceReply{}, err
	}

	userSourceList, err := s.userSource.ListUserSource(ctx, corpId)
	if err != nil {
		return &pb.ListUserSourceReply{}, err
	}
	var customResult []*pb.UserSource
	var thirdResult []*pb.UserSource
	for _, u := range userSourceList {
		sourceInfo, ok := dto.UserSourceClassify[dto.UserSourceType(u.SourceType)]
		if ok || u.TemplateType != "" {
			var temp pb.UserSource
			if err := copier.Copy(&temp, &u); err != nil {
				return &pb.ListUserSourceReply{}, err
			}
			temp.Id = u.ID
			temp.Enable = sourceInfo.Enable
			if sourceInfo.Type == dto.CustomSource {
				customResult = append(customResult, &temp)
			}
			if sourceInfo.Type == dto.ThirdSource || u.TemplateType != "" {
				thirdResult = append(thirdResult, &temp)
			}
		}
	}

	return &pb.ListUserSourceReply{CustomSources: customResult, ThirdSources: thirdResult}, nil
}
