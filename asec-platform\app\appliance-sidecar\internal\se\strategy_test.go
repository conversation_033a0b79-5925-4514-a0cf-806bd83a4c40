package se

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/service/policy"
	"context"
	"fmt"
	"github.com/open-policy-agent/opa/rego"
	"github.com/open-policy-agent/opa/storage/inmem"
	"github.com/stretchr/testify/require"
	"log"
	"testing"
)

func TestStrategy(t *testing.T) {
	data := make(map[string]interface{})
	data["app_ids"] = []uint64{437659899615248388}
	data["user_ids"] = []string{"85f6bfb8-2760-40bc-b0c8-4e58fc4711c9", "70f94c5d-3b6d-4799-85e6-422d5dddbf43"}
	input := make(map[string]interface{})
	input["user_id"] = "70f94c5d-3b6d-4799-85e6-422d5dddbf43"
	input["app_id"] = 437659899615248388
	input["time"] = -62167190159
	r := rego.New(
		rego.Query("data.asd.allow"),
		rego.Module("asd", policy.HeadersRego),
		rego.Store(inmem.NewFromObject(data)),
	)
	prepareForEval, err := r.PrepareForEval(context.Background())
	if err != nil {
		log.Fatal(err)
	}
	eval, err := prepareForEval.Eval(context.Background(), rego.EvalInput(input))
	if err != nil {
		log.Fatal(err)
	}
	require.True(t, eval.Allowed())
	fmt.Println(eval)
}
