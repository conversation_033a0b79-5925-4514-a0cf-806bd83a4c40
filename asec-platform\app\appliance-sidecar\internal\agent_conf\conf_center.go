package agent_conf

import (
	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"database/sql"
	"fmt"
	"google.golang.org/grpc"
	"strconv"
	"strings"
	"sync"
)

func ConfCenter(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   ConfCenterTask,
		RunType:      common.SimpleSend,
		WaitSecond:   60,
		RandomOffset: 2,
	}
	common.Send(param)
}

// 保留一份初始配置,后续无需添加
var confTypes = []string{"dlp", "sensitive_strategy", "source", "evidence_screenshot", "evidence_retainfile",
	"sensitive_element_built", "sensitive_element_custom", "watermark_conf", "dark_watermark", "notification", "sidecar_conf"}

type ConfVersion struct {
	ConfType string
	Version  int
}

var (
	lastUserId      string
	lastApplianceId string
	nowUserId       string
	nowApplianceId  string
	syncTimes       = 12
)

func ConfCenterTask(conn *grpc.ClientConn, ctx context.Context) error {
	global.Logger.Debug(" --------------------------start update config task --------------------------")
	// 获取grpc client
	client := v1.NewConfCenterClient(conn)
	// 每2分钟同步一次配置类型
	syncTimes++
	if syncTimes >= 12 {
		syncTimes = 0
		global.Logger.Debug(" start to sync config type ")
		syncConfType(client)
		global.Logger.Debug(" sync config type done ")
	}
	// 获取数据库连接,开启事务
	db, openDbErr := global.InitSqliteByName(global.ConfDbName)
	defer global.CloseSqlite(db)
	if openDbErr != nil {
		global.Logger.Sugar().Errorf("openDb err :%v", openDbErr)
	}
	tx, beginTxErr := db.BeginTx(context.TODO(), &sql.TxOptions{Isolation: sql.LevelSerializable})
	if beginTxErr != nil {
		global.Logger.Sugar().Errorf("beginTx err:%v", beginTxErr)
		return beginTxErr
	}

	// 获取当前agent的配置大版本
	versionMap, err := getConfVersion(tx)
	if err != nil {
		tx.Rollback()
		global.Logger.Sugar().Errorf("getConfVersion err:%v", err)
		return err
	}
	nowUserId = user.GetUserInfo().UserId
	nowApplianceId = strconv.FormatUint(global.ApplianceID, 10)

	// 加上本地有的配置类型
	for cType := range versionMap {
		confTypes = append(confTypes, cType)
	}
	confTypes = utils.SliceUniqueWithMap(confTypes)

	for _, confType := range confTypes {
		// 嗅探版本是否更新
		req := getSniffUpdateReq(confType, versionMap)
		global.Logger.Sugar().Debugf("config Type:[%s] start to sniff update  ", confType)
		sniffUpdateResp, e := client.SniffUpdate(ctx, req)
		if e != nil {
			tx.Rollback()
			global.Logger.Sugar().Warnf("get sniffUpdate err:%v", e)
			return e
		}

		// 处理切换平台或者平台没有某种配置时的处理
		unnecessary, e := ifUnnecessary(tx, sniffUpdateResp, confType)

		if e != nil {
			tx.Rollback()
			global.Logger.Sugar().Errorf("del unnecessary err:%v", e)
			return e
		}
		if unnecessary {
			global.Logger.Sugar().Debugf(" config Type:[%s] sniff update done , but no need update,skiped! ", confType)
			continue
		}
		if !judgeUpdate(sniffUpdateResp) {
			global.Logger.Sugar().Debugf(" config Type:[%s] sniff update done , but no need update,skiped! ", confType)
			continue
		}
		global.Logger.Sugar().Debugf("config Type:[%s] pull update start... ", confType)
		// 拉取配置
		pollConfResp, e := pollConf(tx, confType, client)
		if e != nil {
			tx.Rollback()
			global.Logger.Sugar().Errorf("poll conf err:%v", e)
			return e
		}
		global.Logger.Sugar().Debugf("config Type:[%s]  pull update  success, start to check update local db ...", confType)

		// 平台返回不需要更新,则同步本地版本
		if !pollConfResp.NeedUpdate {
			uvE := updateVersion(uint32(sniffUpdateResp.ConfVersion), confType, tx)
			if uvE != nil {
				tx.Rollback()
				global.Logger.Sugar().Errorf("update version err:%v", uvE)
				return uvE
			}
			continue
		}
		global.Logger.Sugar().Infof(" config Type:[%s] start to update local config ...", confType)
		// 更新本地配置
		e = changeLocalConf(pollConfResp, tx)
		if e != nil {
			tx.Rollback()
			global.Logger.Sugar().Errorf("change local conf err:%v", e)
			return e
		}
		global.Logger.Sugar().Infof("config Type:[%s] start to update local config  version ...", confType)
		// 更新本地版本
		e = updateVersion(uint32(sniffUpdateResp.ConfVersion), confType, tx)
		if e != nil {
			tx.Rollback()
			global.Logger.Sugar().Errorf("update version err:%v", e)
			return e
		}

	}
	// 提交事务
	commitErr := tx.Commit()
	if commitErr != nil {
		global.Logger.Sugar().Errorf("commit err : %v", commitErr)
		return commitErr
	}
	global.Logger.Debug(" --------------------------update  config task done---------------------------")
	lastApplianceId = nowApplianceId
	lastUserId = nowUserId

	return nil
}

func ifUnnecessary(tx *sql.Tx, resp *v1.SniffUpdateResp, confType string) (bool, error) {
	if resp.ConfVersion != -1 {
		return false, nil
	}
	_, err := tx.Exec("DELETE FROM tb_conf_agent WHERE conf_type=?", confType)
	if err != nil {
		return false, err
	}
	_, err = tx.Exec("DELETE FROM tb_conf_agent_version WHERE conf_type=?", confType)
	if err != nil {
		return false, err
	}
	return true, nil
}

func syncConfType(client v1.ConfCenterClient) {
	req := v1.SyncConfTypeReq{}

	confType, err := client.SyncConfType(context.TODO(), &req)
	if err != nil {
		global.Logger.Sugar().Warnf("sync conf type err:%v", err)
		return
	}
	confTypes = confType.ConfTypes
}

// 判断是否需要走到poll逻辑
func judgeUpdate(sniffUpdateResp *v1.SniffUpdateResp) bool {
	// 判断user Id 或者设备id变更的情况,需要强制重新刷新配置
	if lastUserId != nowUserId {
		return true
	}
	if lastApplianceId != nowApplianceId {
		return true
	}
	return sniffUpdateResp.UpdateSignal
}

func changeLocalConf(pollConfResp *v1.PollConfResp, tx *sql.Tx) error {
	err := addConf(pollConfResp.AddConfList, tx)
	if err != nil {
		global.Logger.Sugar().Errorf("addConf err:%v", err)
		return err
	}
	err = updateConf(pollConfResp.UpdateConfList, tx)
	if err != nil {
		global.Logger.Sugar().Errorf("updateConf err:%v", err)
		return err
	}
	err = delConf(pollConfResp.DelConfIds, tx)
	if err != nil {
		global.Logger.Sugar().Errorf("delConf err:%v", err)
		return err
	}
	return nil
}

func pollConf(tx *sql.Tx, confType string, client v1.ConfCenterClient) (*v1.PollConfResp, error) {
	// 拿取本地存在的配置
	conf, err := getExistConf(tx, confType)
	if err != nil {
		global.Logger.Sugar().Errorf("get existConf err:%v", err)
		return nil, err
	}
	// 用户信息
	userInfo := user.GetUserInfo()
	pollConfReq := v1.PollConfReq{
		ApplianceId: strconv.FormatUint(global.ApplianceID, 10),
		UserId:      userInfo.UserId,
		ConfType:    confType,
		ExistConf:   conf,
	}

	pollConfResp, err := client.PollConf(context.TODO(), &pollConfReq)
	if err != nil {
		global.Logger.Sugar().Errorf("get pollConf err:%v", err)
		return nil, err
	}
	return pollConfResp, nil
}

func updateVersion(version uint32, confType string, tx *sql.Tx) error {
	_, err := tx.Exec("INSERT into main.tb_conf_agent_version(conf_type,version) VALUES (?,?) "+
		"on CONFLICT(conf_type) do UPDATE SET version=excluded.version", confType, version)
	if err != nil {
		return err
	}
	return nil
}

func updateConf(updateConfList []*v1.AgentConf, tx *sql.Tx) error {
	if len(updateConfList) <= 0 {
		return nil
	}
	for _, conf := range updateConfList {
		_, err := tx.Exec("UPDATE tb_conf_agent set conf_data=? ,conf_md5=? ,data_format=?,conf_text=? WHERE conf_id=?",
			conf.ConfData, conf.ConfMd5, conf.DataFormat, conf.ConfText, conf.ConfId)
		if err != nil {
			return err
		}
	}
	return nil
}

func addConf(addConfList []*v1.AgentConf, tx *sql.Tx) error {
	if len(addConfList) <= 0 {
		return nil
	}
	for _, conf := range addConfList {
		_, err := tx.Exec("INSERT INTO tb_conf_agent ( conf_id, conf_type, conf_data, conf_md5,conf_text,data_format) VALUES ( ?, ?, ?, ?,?, ?);",
			conf.ConfId, conf.ConfType, conf.ConfData, conf.ConfMd5, conf.ConfText, conf.DataFormat)
		if err != nil {
			return err
		}
	}
	return nil
}

func chunkArray(input []*v1.AgentConf, chunkSize int) [][]*v1.AgentConf {
	var result [][]*v1.AgentConf

	for i := 0; i < len(input); i += chunkSize {
		end := i + chunkSize
		if end > len(input) {
			end = len(input)
		}
		result = append(result, input[i:end])
	}

	return result
}

func delConf(delIds []string, tx *sql.Tx) error {
	if len(delIds) <= 0 {
		return nil
	}

	placeholders := make([]string, len(delIds))
	for i, id := range delIds {
		placeholders[i] = "'" + id + "'"
	}
	inClause := strings.Join(placeholders, ", ")

	stmt := fmt.Sprintf("DELETE FROM tb_conf_agent WHERE conf_id IN (%s)", inClause)

	_, err := tx.Exec(stmt)
	if err != nil {
		return err
	}
	return nil
}

func getExistConf(tx *sql.Tx, confType string) ([]*v1.ExistConf, error) {
	rows, err := tx.Query("SELECT conf_id,conf_md5 from tb_conf_agent WHERE conf_type=?", confType)
	if err != nil {
		return nil, err
	}
	if rows != nil {
		defer rows.Close()
	}
	var confList []*v1.ExistConf
	for rows.Next() {
		conf := v1.ExistConf{}
		err := rows.Scan(&conf.ConfId, &conf.ConfMd5)
		if err != nil {
			return nil, err
		}
		confList = append(confList, &conf)

	}
	return confList, nil
}

func getSniffUpdateReq(confType string, versionMap map[string]int) *v1.SniffUpdateReq {
	req := v1.SniffUpdateReq{ConfType: confType}
	v, ok := versionMap[confType]
	if ok {
		req.ConfVersion = uint32(v)
	} else {
		req.ConfVersion = 0
	}
	return &req

}

func getConfVersion(tx *sql.Tx) (map[string]int, error) {
	rows, err := tx.Query("select conf_type,version from tb_conf_agent_version")
	if err != nil {
		return nil, err
	}
	if rows != nil {
		defer rows.Close()
	}
	versionMap := make(map[string]int)
	for rows.Next() {
		var t string
		var v int
		err := rows.Scan(&t, &v)
		if err != nil {
			return nil, err
		}
		versionMap[t] = v
	}
	return versionMap, nil
}
