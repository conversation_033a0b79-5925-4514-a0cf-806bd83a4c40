package data

import (
	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"github.com/go-kratos/kratos/v2/log"
)

type roleRepo struct {
	data *Data
	log  *log.Helper
}

func (r roleRepo) DeleteRole(ctx context.Context, corpId, roleId, name string) error {
	return r.data.db.Transaction(func(tx *gorm.DB) error {
		err := tx.WithContext(ctx).Model(model.TbUserRole{}).
			Where("role_id = ? and corp_id = ?", roleId, corpId).
			Delete(model.TbUserRole{}).Error
		if err != nil {
			return err
		}
		err = tx.WithContext(ctx).Model(model.TbRole{}).
			Where("id = ? and corp_id = ?", roleId, corpId).
			Delete(model.TbRole{}).Error
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.RoleMangerType,
				OperationType:  common.OperateDelete,
				Representation: name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			r.data.db.Create(&oplog)

		}()
		return err
	})

}

func (r roleRepo) UpdateRole(ctx context.Context, param dto.Role) error {
	return r.data.db.Transaction(func(tx *gorm.DB) error {
		err := tx.WithContext(ctx).Model(model.TbRole{}).
			Where("id = ? and corp_id = ?", param.ID, param.CorpID).
			Updates(model.TbRole{ID: param.ID, Name: param.Name, CorpID: param.CorpID, Description: param.Description}).Error
		if err != nil {
			return err
		}
		err = tx.WithContext(ctx).Model(model.TbUserRole{}).
			Where("role_id = ? and corp_id = ?", param.ID, param.CorpID).
			Delete(model.TbUserRole{}).Error

		var userRoles []*model.TbUserRole
		for _, user := range param.Users {
			userRoles = append(userRoles, &model.TbUserRole{
				UserID: user.ID,
				RoleID: user.RoleID,
				CorpID: param.CorpID,
			})
		}
		if err := tx.WithContext(ctx).Model(model.TbUserRole{}).CreateInBatches(userRoles, len(userRoles)).Error; err != nil {
			return err
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         param.CorpID,
				ResourceType:   common.RoleMangerType,
				OperationType:  common.OperateUpdate,
				Representation: param.Name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			r.data.db.Create(&oplog)

		}()
		return nil
	})
}

func (r roleRepo) CreateUserRolesMap(ctx context.Context, corpId, userId string, roleIds []string) error {
	userRole := query.Use(r.data.db).TbUserRole
	var userRoles []*model.TbUserRole
	for _, role := range roleIds {
		userRoles = append(userRoles, &model.TbUserRole{
			UserID: userId,
			RoleID: role,
			CorpID: corpId,
		})
	}

	return userRole.WithContext(ctx).Create(userRoles...)
}

func (r roleRepo) CreateRole(ctx context.Context, id, name, corpId, description string, userIds []string) error {
	q := query.Use(r.data.db)
	return q.Transaction(func(tx *query.Query) error {
		if err := tx.TbRole.WithContext(ctx).Create(&model.TbRole{
			ID:          id,
			Name:        name,
			Description: description,
			CorpID:      corpId,
		}); err != nil {
			return err
		}
		var userRoles []*model.TbUserRole
		for _, uid := range userIds {
			userRoles = append(userRoles, &model.TbUserRole{
				UserID: uid,
				RoleID: id,
				CorpID: corpId,
			})
		}
		err := tx.TbUserRole.WithContext(ctx).Create(userRoles...)
		if err != nil {
			return err
		}

		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.RoleMangerType,
				OperationType:  common.OperateCreate,
				Representation: name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			r.data.db.Create(&oplog)

		}()
		return nil
	})
}

func (r roleRepo) GetRoleByName(ctx context.Context, name, corpId string) (*model.TbRole, error) {
	role := query.Use(r.data.db).TbRole
	result, err := role.WithContext(ctx).Where(role.Name.Eq(name), role.CorpID.Eq(corpId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbRole{}, pb.ErrorRecordNotFound("corpId=%v not found", corpId)
		}
		return &model.TbRole{}, err
	}
	return result, nil
}

func (r roleRepo) ListRole(ctx context.Context, corpId string, limit, offset int, search string, isAll bool) ([]*dto.Role, error) {
	role := query.Use(r.data.db).TbRole
	var result []*dto.Role
	if isAll {
		err := role.WithContext(ctx).Where(role.CorpID.Eq(corpId)).Order(role.CreatedAt.Desc()).Scan(&result)
		return result, err
	}
	search = "%" + search + "%"
	err := role.WithContext(ctx).
		Where(role.CorpID.Eq(corpId), role.Name.Like(search)).
		Limit(limit).Offset(offset).Order(role.CreatedAt.Desc()).Scan(&result)
	return result, err
}

func (r roleRepo) CountRoles(ctx context.Context, corpId string, search string, isAll bool) (int64, error) {
	role := query.Use(r.data.db).TbRole
	if isAll {
		return role.WithContext(ctx).Where(role.CorpID.Eq(corpId)).Count()
	}
	search = "%" + search + "%"
	return role.WithContext(ctx).Where(role.CorpID.Eq(corpId), role.Name.Like(search)).Count()
}

func (r roleRepo) QueryRolesUsers(ctx context.Context, corpId string, roleIds []string) ([]dto.UserInfo, error) {
	var result []dto.UserInfo
	err := r.data.db.Table("tb_user_role").
		Select("tb_user_role.role_id,tb_user_entity.id,tb_user_entity.name,tb_user_entity.display_name,CASE WHEN tb_user_group.PATH = '/' THEN concat ( tb_user_group.PATH, tb_user_group.NAME ) ELSE concat ( tb_user_group.PATH, '/', tb_user_group.NAME ) \nEND AS PATH ").
		Joins("LEFT JOIN tb_user_entity on tb_user_entity.id = tb_user_role.user_id").
		Joins("INNER JOIN tb_user_group on tb_user_entity.group_id = tb_user_group.id").
		Where("tb_user_role.corp_id = ? AND tb_user_role.role_id in (?)", corpId, roleIds).
		Scan(&result)

	//var result []dto.UserInfo
	//userRole := query.Use(r.data.db).TbUserRole
	//user := query.Use(r.data.db).TbUserEntity
	//userGroup := query.Use(r.data.db).TbUserGroup
	//err := userRole.WithContext(ctx).Debug().
	//	LeftJoin(user, user.ID.EqCol(userRole.UserID)).
	//	Join(userGroup, user.GroupID.EqCol(userGroup.ID)).
	//	Select(
	//		userRole.RoleID,
	//		user.ID,
	//		user.Name,
	//		user.DisplayName,
	//		userGroup.Path,
	//	).
	//	Where(userRole.CorpID.Eq(corpId), userRole.RoleID.In(roleIds...)).
	//	Scan(&result)
	return result, err.Error
}

func NewRoleRepo(data *Data, logger log.Logger) biz.RoleRepo {
	return &roleRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
