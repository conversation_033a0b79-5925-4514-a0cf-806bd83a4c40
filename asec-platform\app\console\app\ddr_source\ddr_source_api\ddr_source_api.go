package ddr_source_api

import (
	"asdsec.com/asec/platform/app/console/app/ddr_source/ddr_const"
	"asdsec.com/asec/platform/app/console/app/ddr_source/service"
	"asdsec.com/asec/platform/app/console/app/ddr_source/vo"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"github.com/jackc/pgconn"
	"go.uber.org/zap"
	"strings"
)

// CreateSource godoc
// @Summary 创建来源策略
// @Schemes
// @Description 创建来源策略
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body vo.CreateSourceReq true "创建来源策略参数"
// @Success     200
// @Router      /v1/ddr_source/source [POST]
// @success     200 {object} common.Response{} "ok"
func CreateSource(c *gin.Context) {
	req := vo.CreateSourceReq{}
	err := c.ShouldBind(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	req.IncludeFilePath = commonApi.CheckPath(req.IncludeFilePath)
	req.ExcludeFilePath = commonApi.CheckPath(req.ExcludeFilePath)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.DataSourceTYpe,
			OperationType:  common.OperateCreate,
			Representation: req.SourceName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err = service.GetDdrSourceService().CreateSource(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("CreateSource err:%v,req:%v", err, req)
		pgErr, ok := err.(*pgconn.PgError)
		if ok && pgErr.Code == "23505" {
			common.Fail(c, ddr_const.CreateDdrSourceDuplicateErr)
		} else {
			common.Fail(c, ddr_const.CreateDdrSourceErr)
		}
		return
	}
	common.Ok(c)
}

// UpdateSource godoc
// @Summary 修改来源策略
// @Schemes
// @Description 修改来源策略
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body vo.UpdateSourceReq true "修改来源策略参数"
// @Success     200
// @Router      /v1/ddr_source/source [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateSource(c *gin.Context) {
	req := vo.UpdateSourceReq{}
	err := c.ShouldBind(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	req.IncludeFilePath = commonApi.CheckPath(req.IncludeFilePath)
	req.ExcludeFilePath = commonApi.CheckPath(req.ExcludeFilePath)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.DataSourceTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.SourceName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	if err := service.GetDdrSourceService().UpdateSource(c, req); err != nil {
		global.SysLog.Error("UpdateSource err", zap.Error(err))
		common.Fail(c, ddr_const.UpdateSourceErr)
		return
	}
	common.Ok(c)
}

// DelSource godoc
// @Summary 删除来源策略
// @删除来源策略
// @Description 创建来源策略
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body vo.DelSourceReq true "删除来源策略参数"
// @Success     200
// @Router      /v1/ddr_source/source [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelSource(c *gin.Context) {
	req := vo.DelSourceReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 删除前检查敏感策略引用
	quote, err := service.GetDdrSourceService().CheckSensitiveQuote(c, req)
	if err != nil && len(quote) == 0 {
		global.SysLog.Error("DelSource err", zap.Error(err))
		common.Fail(c, ddr_const.DelSourceErr)
		return
	}
	if err != nil && len(quote) != 0 {
		global.SysLog.Warn("checkSensitiveQuote err", zap.Error(err))
		common.FailFormat(c, ddr_const.CheckSensitiveQuoteErr, "rule_names", quote)
		return
	}
	if err := service.GetDdrSourceService().DelSource(c, req); err != nil {
		global.SysLog.Error("DelSource err", zap.Error(err))
		common.Fail(c, ddr_const.DelSourceErr)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		representationVal := strings.Join(req.Name, ",")
		oplog := modelTable.Oprlog{
			ResourceType:   common.DataSourceTYpe,
			OperationType:  common.OperateDelete,
			Representation: representationVal,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	common.Ok(c)
}

// SourceList godoc
// @Summary 获取来源策略列表
// @Schemes
// @Description 获取来源策略列表
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body vo.SourceListReq true "获取来源策略列表参数"
// @Success     200
// @Router      /v1/ddr_source/source_list [POST]
// @success     200 {object} common.Response{data=vo.SourceListResp} "ok"
func SourceList(c *gin.Context) {
	req := vo.SourceListReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	resp, err := service.GetDdrSourceService().SourceList(c, req)
	if err != nil {
		global.SysLog.Error("get SourceList err", zap.Error(err))
		common.Fail(c, ddr_const.SourceListErr)
		return
	}
	common.OkWithData(c, resp)
}

// DetailSource godoc
// @Summary 查询单个来源详情
// @Schemes
// @Description 获取来源策略列表
// @Tags        DdrSource
// @Produce     application/json
// @Param       source_id query string true "来源id"
// @Success     200
// @Router      /v1/ddr_source/source [GET]
// @success     200 {object} common.Response{data=vo.DetailSourceResp} "ok"
func DetailSource(c *gin.Context) {
	sourceId := c.Query("source_id")
	if sourceId == "" {
		global.SysLog.Error("param err")
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if resp, err := service.GetDdrSourceService().DetailSource(c, sourceId); err != nil {
		global.SysLog.Error("param err")
		common.Fail(c, ddr_const.DetailSourceErr)
		return
	} else {
		common.OkWithData(c, resp)
	}

}

// SourceListQuote godoc
// @Summary 外部全量获取来源策略列表
// @Schemes
// @Description 外部全量获取来源策略列表
// @Tags        DdrSource
// @Produce     application/json
// @Param       search query string false "搜索条件"
// @Success     200
// @Router      /v1/ddr_source/source_list_quote [GET]
// @success     200 {object} common.Response{data=[]vo.SourceListQuote} "ok"
func SourceListQuote(c *gin.Context) {
	search := c.Query("search")

	quote, err := service.GetDdrSourceService().SourceListQuote(c, search)
	if err != nil {
		global.SysLog.Error("get SourceListQuote err:", zap.Error(err))
		common.Fail(c, ddr_const.SourceListErr)
		return
	}
	common.OkWithData(c, quote)
}
