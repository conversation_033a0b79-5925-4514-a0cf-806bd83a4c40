package service

import (
	"context"

	pb "asdsec.com/asec/platform/api/auth/v1/admin"
)

func (s *AdminService) CreateCorp(ctx context.Context, req *pb.CreateCorpRequest) (*pb.CreateCorpReply, error) {
	err := s.corp.CreateCorp(ctx, req.Name)
	if err != nil {
		return &pb.CreateCorpReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateCorpReply{Status: pb.StatusCode_SUCCESS}, nil
}
func (s *AdminService) UpdateCorp(ctx context.Context, req *pb.UpdateCorpRequest) (*pb.UpdateCorpReply, error) {
	err := s.corp.UpdateCorp(ctx, req.Id, req.Name)
	if err != nil {
		return &pb.UpdateCorpReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateCorpReply{Status: pb.StatusCode_SUCCESS}, nil
}
func (s *AdminService) DeleteCorp(ctx context.Context, req *pb.DeleteCorpRequest) (*pb.DeleteCorpReply, error) {
	panic("todo")
	return &pb.DeleteCorpReply{}, nil
}
func (s *AdminService) GetCorp(ctx context.Context, req *pb.GetCorpRequest) (*pb.GetCorpReply, error) {
	corp, err := s.corp.GetCorpById(ctx, req.Id)
	if err != nil {
		return &pb.GetCorpReply{}, err
	}

	return &pb.GetCorpReply{Corp: &pb.Corp{Id: corp.ID, Name: corp.Name}}, nil
}
func (s *AdminService) ListCorp(ctx context.Context, req *pb.ListCorpRequest) (*pb.ListCorpReply, error) {
	corps, err := s.corp.ListCorp(ctx)
	if err != nil {
		return &pb.ListCorpReply{}, err
	}

	var result []*pb.Corp
	for _, t := range corps {
		result = append(result, &pb.Corp{
			Id:   t.ID,
			Name: t.Name,
		})
	}
	if err != nil {
		return &pb.ListCorpReply{}, err
	}
	return &pb.ListCorpReply{Corps: result}, nil
}
