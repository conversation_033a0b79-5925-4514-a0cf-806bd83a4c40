package app_matcher

import (
	pb "asdsec.com/asec/platform/api/application/v1"
	"net"
)

type IpCidrAdapter struct {
	baseMatcher BaseMatcher
	IpNet       *net.IPNet
}

func (i IpCidrAdapter) GetAppInfo() *pb.AppInfo {
	return i.baseMatcher.AppInfo
}

func (i IpCidrAdapter) GetMatcherType() UrlMatcherType {
	return IpCidrMatcher
}

func (i IpCidrAdapter) DoMatcher(srcUrl string, srcPort int) error {
	ip := net.ParseIP(srcUrl)
	if ip == nil {
		return i.baseMatcher.NoMatchError(srcUrl, srcPort, "ip parse error")
	}
	if !i.IpNet.Contains(ip) {
		return i.baseMatcher.NoMatchError(srcUrl, srcPort, "")
	}
	matchPort, detail := i.baseMatcher.CommonMatchPort(srcPort)
	if !matchPort {
		return i.baseMatcher.NoMatchError(srcUrl, srcPort, detail)
	}
	return nil
}

func NewIpCidrAdapter(appInfo *pb.AppInfo, ipNet *net.IPNet) (IpCidrAdapter, error) {
	ps, err := GetPortMatchers(appInfo.Port)
	if err != nil {
		return IpCidrAdapter{}, err
	}

	return IpCidrAdapter{
		baseMatcher: BaseMatcher{
			UrlRule:  appInfo.Address,
			PortRule: ps,
			AppInfo:  appInfo,
		},
		IpNet: ipNet,
	}, nil

}
