package biz

import (
	"asdsec.com/asec/platform/app/auth/internal/common"
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"
	"time"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

type IdpRepo interface {
	CreateIDP(ctx context.Context, param dto.CreateIDPDaoParam) error
	UpdateIDP(ctx context.Context, param dto.UpdateIDPDaoParam, corpId string) error
	DeleteIDP(ctx context.Context, corpId, idpId, name string) error
	TemplateAttr(ctx context.Context) ([]*model.TbIdpTemplateAttribute, error)
	GetIDPByName(ctx context.Context, corpId, name string) (*model.TbIdentityProvider, error)
	GetIDP(ctx context.Context, corpId, id string) (*model.TbIdentityProvider, error)
	GetIDPByType(ctx context.Context, corpId, idpType string) (*model.TbIdentityProvider, error)
	CountIDPInSource(ctx context.Context, corpId, sourceId string) (int64, error)
	CountIDPInType(ctx context.Context, corpId, idpType string) (int64, error)
	ListIDPWithGroup(ctx context.Context, corpId string, limit, offset int) ([]dto.IDPWithGroup, error)
	CountIDP(ctx context.Context, corpId string) (int64, error)
	ListIDPTemplate(ctx context.Context, corpId string) ([]*model.TbIdentityProviderTemplate, error)
	ListIDPWithoutGroup(ctx context.Context, corpId string) ([]*model.TbIdentityProvider, error)
	ListBindIDPWithAttrs(ctx context.Context, corpId string) ([]dto.IDPWithAttr, error)
	GetAllSourceOfBind(ctx context.Context, corpId, idpId string) ([]dto.SourceGroupResult, error)
	GetIDPBindGroup(ctx context.Context, corpId, idpId string) ([]*model.TbUserGroup, error)
	GetDefaultLocalIDP(ctx context.Context, corpId string) (*model.TbIdentityProvider, error)
	QueryAuthPolicy(ctx context.Context, corpId, idpId string) ([]*model.TbAuthPolicy, error)
	GetIDPAttr(ctx context.Context, idpId string) ([]dto.KV, error)
	GetIDPAttrValue(ctx context.Context, idpId, key string) (*model.TbIdentityProviderAttribute, error)
	GetWxWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error)
	SetWxWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error
	GetFsWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error)
	SetFsWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error
	BindIDPAndGroups(ctx context.Context, corpId, idpId string, groupIds []string) error
	//QuerySecondaryAuth(ctx context.Context, policies []string) ([]*model.TbIdentityProvider, error)
}

type IdpUsecase struct {
	repo           IdpRepo
	usRepo         UserSourceRepo
	ugRepo         UserGroupRepo
	authPolicyRepo AuthPolicyRepo

	log *log.Helper
}

func NewIdpUsecase(repo IdpRepo, usRepo UserSourceRepo, ugRepo UserGroupRepo, authPolicyRepo AuthPolicyRepo, logger log.Logger) *IdpUsecase {
	return &IdpUsecase{
		repo:           repo,
		usRepo:         usRepo,
		ugRepo:         ugRepo,
		authPolicyRepo: authPolicyRepo,
		log:            log.NewHelper(logger),
	}
}

func (i IdpUsecase) ListIDPType(ctx context.Context, corpId string) (dto.IDPTypeResp, error) {
	userSources, err := i.usRepo.ListUserSource(ctx, corpId)
	if err != nil {
		i.log.Errorf("ListUserSource failed. err=%v", err)
		return dto.IDPTypeResp{}, err
	}
	userSourceTemplates, terr := i.repo.ListIDPTemplate(ctx, corpId)
	if terr != nil {
		i.log.Errorf("ListIDPTemplate failed. err=%v", terr)
		return dto.IDPTypeResp{}, err
	}

	//模板的默认属性
	attr, aErr := i.repo.TemplateAttr(ctx)
	if aErr != nil {
		i.log.Errorf("TemplateAttr failed. err=%v", aErr)
		return dto.IDPTypeResp{}, err
	}

	userSourceToTemplate := make(map[string]string)
	for _, us := range userSources {
		if dto.IDPType(us.SourceType) == dto.IDPTypeLocal {
			continue
		}
		userSourceToTemplate[us.SourceType] = us.TemplateType
	}

	attrMaps := make(map[string]map[string]string)
	for _, us := range userSourceTemplates {
		attrMap := make(map[string]string)
		for _, v := range attr {
			if v.ProviderID != us.ID {
				continue
			}
			switch v.Key {
			case dto.AttrKeyOAuth2GlobalData:
				attrMap["GlobalData"] = v.Value
			case dto.AttrKeyOAuth2CodeData:
				attrMap["CodeData"] = v.Value
			case dto.AttrKeyOAuth2UserData:
				attrMap["UserData"] = v.Value
			case dto.AttrKeyOAuth2LogoutData:
				attrMap["LogoutData"] = v.Value
			case dto.AttrKeyOAuth2LogoutOpen:
				attrMap["LogoutOpen"] = v.Value
			case dto.AttrKeyOAuth2OpenType:
				attrMap["OpenType"] = v.Value
			}
		}
		attrMaps[us.Type] = attrMap
		userSourceToTemplate[us.Type] = us.TemplateType
		dto.MainIDPTypeName[dto.IDPType(us.Type)] = us.Name
	}

	var result dto.IDPTypeResp
	for t, n := range dto.MainIDPTypeName {
		key := string(t)
		if templateType, ok := userSourceToTemplate[key]; ok {
			tempMap := map[string]string{
				"GlobalData": attrMaps[key]["GlobalData"],
				"CodeData":   attrMaps[key]["CodeData"],
				"UserData":   attrMaps[key]["UserData"],
				"LogoutData": attrMaps[key]["LogoutData"],
				"LogoutOpen": attrMaps[key]["LogoutOpen"],
				"OpenType":   attrMaps[key]["OpenType"],
			}
			result.MainIdp = append(result.MainIdp, dto.IDPTypeObject{
				Type:         t,
				Name:         n,
				TemplateType: templateType,
				TemplateAttr: tempMap,
			})
		}
	}
	totpCount, err := i.repo.CountIDPInType(ctx, corpId, string(dto.IDPTypeTotp))
	if err != nil {
		return dto.IDPTypeResp{}, err
	}
	smsCount, err := i.repo.CountIDPInType(ctx, corpId, string(dto.IDPTypeVerifyCode))
	if err != nil {
		return dto.IDPTypeResp{}, err
	}
	for t, n := range dto.AssistIDPTypeName {
		if totpCount > 0 && t == dto.IDPTypeTotp {
			continue
		}
		if smsCount >= 2 && t == dto.IDPTypeVerifyCode {
			continue
		}
		result.AssistIdp = append(result.AssistIdp, dto.IDPTypeObject{
			Type: t,
			Name: n,
		})
	}
	return result, nil
}

func (i IdpUsecase) CreateIDP(ctx context.Context, param dto.CreateIDPParam) error {
	_, err := i.repo.GetIDPByName(ctx, param.CorpId, param.Name)
	if err != nil && !pb.IsRecordNotFound(err) {
		i.log.Errorf("GetFirstIDPByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", param.Name)
	}

	// 创建idp
	sourceId := dto.AssistIDPSourceID
	if _, ok := dto.AssistIDPTypeName[dto.IDPType(param.Type)]; ok { // 辅助认证服务器
		i.log.Debugf("create assist idp. type=%v", param)
		if err = i.createAssistIDPCheck(ctx, param.CorpId, param.Type); err != nil {
			return err
		}
		assistSource, err := i.usRepo.GetUserSourceByType(ctx, param.CorpId, param.Type, "")
		if err != nil {
			i.log.Errorf("GetUserSourceByType failed. err=%v", err)
			return err
		}
		sourceId = assistSource.ID
		// todo 新增辅助认证服务器逻辑
		var attrs []dto.KV
		if param.TemplateType == "email" {
			// 邮箱渠道的验证码认证
			attrs, err = i.formatEmailAttrs(param.AuthConfig)
			if err != nil {
				i.log.Errorf("formatAssistEmailAttrs failed. err=%v", err)
				return err
			}
		} else {
			// 常规辅助认证处理
			attrs, err = i.formatIDPAttrs(dto.IDPType(param.Type), param.TemplateType, param.AuthConfig, param.FieldMap)
			if err != nil {
				i.log.Errorf("formatIDPAttrs failed. err=%v", err)
				return err
			}
		}
		var daoParam dto.CreateIDPDaoParam
		if err := copier.Copy(&daoParam, &param); err != nil {
			i.log.Errorf("Copy failed. err=%v", err)
			return err
		}
		idpId := uuid.New().String()
		daoParam.Id = idpId
		daoParam.Attr = attrs
		daoParam.SourceId = sourceId
		if err = i.repo.CreateIDP(ctx, daoParam); err != nil {
			i.log.Errorf("CreateIDP failed. err=%v", err)
			return err
		}
	} else { // 主认证服务器
		source, err := i.usRepo.GetUserSourceByType(ctx, param.CorpId, param.Type, param.TemplateType)
		if err != nil {
			i.log.Errorf("GetUserSourceByType failed. err=%v", err)
			return err
		}
		sourceId = source.ID
		if err = i.createMainIDPCheck(ctx, source.ID, param.CorpId, param.Type, param.BindRootGroupId); err != nil {
			i.log.Errorf("createMainIDPCheck failed. err=%v", err)
			return err
		}
		attrs, err := i.formatIDPAttrs(dto.IDPType(param.Type), param.TemplateType, param.AuthConfig, param.FieldMap)
		if err != nil {
			i.log.Errorf("formatIDPAttrs failed. err=%v", err)
			return err
		}
		var daoParam dto.CreateIDPDaoParam
		if err := copier.Copy(&daoParam, &param); err != nil {
			i.log.Errorf("Copy failed. err=%v", err)
			return err
		}
		idpId := uuid.New().String()
		daoParam.Id = idpId
		daoParam.Attr = attrs
		daoParam.SourceId = sourceId
		if err = i.repo.CreateIDP(ctx, daoParam); err != nil {
			i.log.Errorf("CreateIDP failed. err=%v", err)
			return err
		}

		// 绑定idp和组
		if err = i.bindIdpAndGroups(ctx, param.CorpId, idpId, param.BindRootGroupId); err != nil {
			i.log.Errorf("bindIdpAndGroups failed. err=%v", err)
			return err
		}
		for _, group := range param.BindRootGroupId {
			// 这里用循环应该没多大问题，因为绑定的rootGroupId不会太多，为方便理解，使用循环
			ug, err := i.ugRepo.GetUserGroup(ctx, param.CorpId, group)
			if err != nil {
				i.log.Errorf("GetUserGroup failed. err=%v", err)
				return err
			}

			//查找当前用户目录是否存在默认策略
			var defaultAuthPolicy *model.TbAuthPolicy
			defaultAuthPolicy, err = i.authPolicyRepo.GetDefaultPolicyInRootGroup(ctx, param.CorpId, ug.ID)
			if err != nil {
				i.log.Errorf("GetDefaultPolicy failed. err=%v", err)
				return err
			}

			if defaultAuthPolicy.ID != "" {
				var authPolicyIdpMapper = model.TbAuthPolicyIdpMapper{
					PolicyID:  defaultAuthPolicy.ID,
					IdpID:     idpId,
					CorpID:    param.CorpId,
					CreatedAt: time.Time{},
					UpdatedAt: time.Time{},
				}
				if err := i.authPolicyRepo.AddAuthPolicyIdpMapper(ctx, authPolicyIdpMapper); err != nil {
					i.log.Errorf("CreatePolicy failed. err=%v", err)
					return err
				}
			} else {
				// 绑定默认认证策略
				var authPolicyParam = dto.CreateAuthPolicyDaoParam{
					CorpId:        param.CorpId,
					Id:            uuid.New().String(),
					Name:          common.GetDefaultUniqName(ug.Name),
					GroupIds:      []string{},
					UserIds:       []string{},
					IdpList:       []string{idpId},
					RootGroupId:   ug.ID,
					EnableAllUser: true,
					Enable:        true,
					IsDefault:     true,
				}

				if err := i.authPolicyRepo.CreatePolicy(ctx, authPolicyParam); err != nil {
					i.log.Errorf("CreatePolicy failed. err=%v", err)
					return err
				}
			}
		}
	}

	return nil
}

func (i IdpUsecase) bindIdpAndGroupsCheck(ctx context.Context, corpId, idpId string, rootGroupIds []string) error {
	// 获取idp
	idp, err := i.repo.GetIDP(ctx, corpId, idpId)
	if err != nil {
		i.log.Errorf("GetIDP failed. err=%v", err)
		return err
	}
	if idp.SourceID == dto.AssistIDPSourceID {
		return pb.ErrorOnlyMainIdpCanBind("only main idp can bind")
	}

	// idpId 绑定的根目录组必须是同一来源
	bindSources, err := i.repo.GetAllSourceOfBind(ctx, corpId, idpId)
	if err != nil {
		i.log.Errorf("GetAllSourceOfBind failed. err=%v", err)
		return err
	}
	if len(bindSources) > 1 {
		return pb.ErrorMainIdpOnlyBindOneSource("bind sources=%v error", bindSources)
	}
	rootCount, err := i.ugRepo.CountRootGroupInGroupIds(ctx, corpId, rootGroupIds)
	if err != nil {
		i.log.Errorf("CountRootGroupInGroupIds failed. err=%v", err)
		return err
	}
	if int(rootCount) != len(rootGroupIds) {
		i.log.Errorf("group in rootGoupIds=%v is not root group", rootGroupIds)
		return pb.ErrorParamError("rootGroup param error")
	}
	groupSources, err := i.ugRepo.GetAllSourceInGroupIds(ctx, corpId, rootGroupIds)
	if err != nil {
		i.log.Errorf("GetAllSourceInGroupIds failed. err=%v", err)
		return err
	}
	if len(groupSources) != 1 {
		return pb.ErrorRootSourceError("root groups should in one source")
	}
	if len(bindSources) == 1 && bindSources[0].SourceID != groupSources[0] {
		return pb.ErrorMainIdpOnlyBindOneSource("main idp only bind one source")
	}
	return nil
}

func (i IdpUsecase) bindIdpAndGroups(ctx context.Context, corpId, idpId string, rootGroupIds []string) error {
	// 获取idp
	idp, err := i.repo.GetIDP(ctx, corpId, idpId)
	if err != nil {
		i.log.Errorf("GetIDP failed. err=%v", err)
		return err
	}
	if idp.SourceID == dto.AssistIDPSourceID {
		return pb.ErrorOnlyMainIdpCanBind("only main idp can bind")
	}

	// idpId 绑定的根目录组必须是同一来源
	bindSources, err := i.repo.GetAllSourceOfBind(ctx, corpId, idpId)
	if err != nil {
		i.log.Errorf("GetAllSourceOfBind failed. err=%v", err)
		return err
	}
	if len(bindSources) > 1 {
		return pb.ErrorMainIdpOnlyBindOneSource("bind sources=%v error", bindSources)
	}
	rootCount, err := i.ugRepo.CountRootGroupInGroupIds(ctx, corpId, rootGroupIds)
	if err != nil {
		i.log.Errorf("CountRootGroupInGroupIds failed. err=%v", err)
		return err
	}
	if int(rootCount) != len(rootGroupIds) {
		i.log.Errorf("group in rootGoupIds=%v is not root group", rootGroupIds)
		return pb.ErrorParamError("rootGroup param error")
	}
	groupSources, err := i.ugRepo.GetAllSourceInGroupIds(ctx, corpId, rootGroupIds)
	if err != nil {
		i.log.Errorf("GetAllSourceInGroupIds failed. err=%v", err)
		return err
	}
	if len(groupSources) != 1 {
		return pb.ErrorRootSourceError("root groups should in one source")
	}
	if len(bindSources) == 1 && bindSources[0].SourceID != groupSources[0] {
		return pb.ErrorMainIdpOnlyBindOneSource("main idp only bind one source")
	}

	if err = i.repo.BindIDPAndGroups(ctx, corpId, idpId, rootGroupIds); err != nil {
		i.log.Errorf("BindIDPAndGroups failed. err=%v", err)
		return err
	}
	return nil
}

func (i IdpUsecase) createMainIDPCheck(ctx context.Context, sourceId, corpId, idpType string, bindRootGroups []string) error {
	// 来源下可创建的IDP数量校验，本地只有一个默认的，其它三方可以创建多个
	if limit, ok := dto.UserSourceMainIDPNumLimit[dto.UserSourceType(idpType)]; ok {
		count, err := i.repo.CountIDPInSource(ctx, corpId, sourceId)
		if err != nil {
			i.log.Errorf("CountFirstIDPInSource failed. err=%v, sourceId=%v", err, sourceId)
			return err
		}
		if count >= limit {
			return pb.ErrorIdpSourceTypeError("IDP of user source=%v limit(<=%v)", dto.UserSourceType(idpType), limit)
		}
	}
	// IDP可绑定的根目录校验，本地可绑定多个，三方只能绑定一个（因为会出现同名问题）
	if dto.IDPType(idpType) != dto.IDPTypeLocal && len(bindRootGroups) != 1 {
		i.log.Errorf("third idp type=%v only bind one root group. bindRootGroups=%v", idpType, bindRootGroups)
		return pb.ErrorParamError("third idp type=%v only bind one root group", idpType)
	}
	return nil
}

func (i IdpUsecase) createAssistIDPCheck(ctx context.Context, corpId, idpType string) error {
	if _, ok := dto.AssistIDPTypeName[dto.IDPType(idpType)]; !ok {
		return pb.ErrorIdpSourceTypeError("idpType=%v not support.", idpType)
	}
	if limit, ok := dto.AssistIDPNumLimit[dto.IDPType(idpType)]; ok {
		count, err := i.repo.CountIDPInType(ctx, corpId, idpType)
		if err != nil {
			i.log.Errorf("CountIDPInType failed. err=%v, idpType=%v", err, idpType)
			return err
		}
		if count >= limit {
			return pb.ErrorIdpSourceTypeError("IDP of type=%v limit(<=%v)", idpType, limit)
		}
	}
	return nil
}

func (i IdpUsecase) formatIDPAttrs(idpType dto.IDPType, templateType string, authConfig dto.AuthConfig, fieldMap []dto.KV) ([]dto.KV, error) {
	currIdpType := dto.GetIdpType(string(idpType), templateType)
	switch dto.IDPType(currIdpType) {
	case dto.IDPTypeLocal:
		return dto.IDPLocalAttr, nil
	case dto.IDPTypeQiYeWx:
		return i.formatWxAttrs(authConfig, fieldMap)
	case dto.IDPTypeFeiShu:
		return i.formatFeiShuAttrs(authConfig, fieldMap)
	case dto.IDPTypeSMS:
		return i.formatSmsAttrs(authConfig)
	case dto.IDPTypeDingtalk:
		return i.formatDingtalkAttrs(authConfig, fieldMap)
	case dto.IDPTypeLdap:
		return i.formatAdAttrs(authConfig, fieldMap)
	case dto.IDPTypeMsad:
		return i.formatAdAttrs(authConfig, fieldMap)
	case dto.IDPTypeInfogo:
		return i.formatInfogoAttrs(authConfig, fieldMap)
	case dto.IDPTypeOAuth2:
		return i.formatOAuth2Attrs(authConfig, fieldMap)
	case dto.IDPTypeCas:
		return i.formatCasAttrs(authConfig)
	case dto.IDPTypeWeb:
		return i.formatWebAttrs(authConfig)
	case dto.IDPTypeEmail:
		return i.formatEmailAttrs(authConfig)
	case dto.IDPTypeTotp:
		return i.formatTOTPAttrs(authConfig)
	default:
		return []dto.KV{}, pb.ErrorIdpSourceTypeError("idpType=%v not support", idpType)
	}
}

func (i IdpUsecase) formatTOTPAttrs(authConfig dto.AuthConfig) ([]dto.KV, error) {
	var totpConfig dto.TotpConfig
	if err := copier.Copy(&totpConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, totpConfig.ToKVs()...)
	return result, nil
}

// 新增方法：处理web类型的IDP属性
func (i IdpUsecase) formatWebAttrs(authConfig dto.AuthConfig) ([]dto.KV, error) {
	var webConfig dto.WebConfig
	if err := copier.Copy(&webConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, webConfig.ToKVs()...)
	return result, nil
}

// 新增方法：处理email类型的IDP属性
func (i IdpUsecase) formatEmailAttrs(authConfig dto.AuthConfig) ([]dto.KV, error) {
	var emailConfig dto.EmailConfig
	if err := copier.Copy(&emailConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, emailConfig.ToKVs()...)
	return result, nil
}

// 新增方法：处理cas类型的IDP属性
func (i IdpUsecase) formatCasAttrs(authConfig dto.AuthConfig) ([]dto.KV, error) {
	var casConfig dto.CasConfig
	if err := copier.Copy(&casConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, casConfig.ToKVs()...)
	return result, nil
}

// 新增方法：处理OAuth2类型的IDP属性
func (i IdpUsecase) formatOAuth2Attrs(authConfig dto.AuthConfig, fieldMap []dto.KV) ([]dto.KV, error) {
	var oauth2Config dto.OAuth2Config
	if err := copier.Copy(&oauth2Config, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, oauth2Config.ToKVs()...)

	// 添加字段映射
	fieldMapBytes, err := json.Marshal(fieldMap)
	if err != nil {
		i.log.Errorf("Marshal failed. err=%v", err)
		return []dto.KV{}, err
	}
	result = append(result, dto.KV{
		Key:   dto.AttrKeyOAuth2FieldMap, // 需要确认此常量是否已定义
		Value: string(fieldMapBytes),
	})

	return result, nil
}

func (i IdpUsecase) formatInfogoAttrs(authConfig dto.AuthConfig, fieldMap []dto.KV) ([]dto.KV, error) {
	var infogoConfig dto.InfogoConfig
	if err := copier.Copy(&infogoConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, infogoConfig.ToKVs()...)
	fieldMapBytes, err := json.Marshal(fieldMap)
	if err != nil {
		i.log.Errorf("Marshal failed. err=%v", err)
		return []dto.KV{}, err
	}
	result = append(result, dto.KV{
		Key:   dto.AttrKeyInfogoFieldMap,
		Value: string(fieldMapBytes),
	})
	return result, nil

}

func (i IdpUsecase) formatWxAttrs(authConfig dto.AuthConfig, fieldMap []dto.KV) ([]dto.KV, error) {
	var wxConfig dto.WxConfig
	if err := copier.Copy(&wxConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, wxConfig.ToKVs()...)
	fieldMapBytes, err := json.Marshal(fieldMap)
	if err != nil {
		i.log.Errorf("Marshal failed. err=%v", err)
		return []dto.KV{}, err
	}
	result = append(result, dto.KV{
		Key:   dto.AttrKeyWxFieldMap,
		Value: string(fieldMapBytes),
	})
	return result, nil
}

func (i IdpUsecase) formatAdAttrs(authConfig dto.AuthConfig, fieldMap []dto.KV) ([]dto.KV, error) {
	var idpConfig dto.IdpConfig
	if err := copier.Copy(&idpConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, idpConfig.ToKVs()...)
	fieldMapBytes, err := json.Marshal(fieldMap)
	if err != nil {
		i.log.Errorf("Marshal failed. err=%v", err)
		return []dto.KV{}, err
	}
	result = append(result, dto.KV{
		Key:   dto.AttrKeyAdFieldMap,
		Value: string(fieldMapBytes),
	})
	return result, nil
}

func (i IdpUsecase) formatDingtalkAttrs(authConfig dto.AuthConfig, fieldMap []dto.KV) ([]dto.KV, error) {
	var dingtalkConfig dto.DingtalkConfig
	if err := copier.Copy(&dingtalkConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	// 手动映射钉钉企业ID字段
	dingtalkConfig.DingtalkCorpId = authConfig.DdCorpId

	var result []dto.KV
	result = append(result, dingtalkConfig.ToKVs()...)
	fieldMapBytes, err := json.Marshal(fieldMap)
	if err != nil {
		i.log.Errorf("Marshal failed. err=%v", err)
		return []dto.KV{}, err
	}
	result = append(result, dto.KV{
		Key:   dto.AttrKeyDingtalkFieldMap,
		Value: string(fieldMapBytes),
	})
	return result, nil
}

func (i IdpUsecase) formatFeiShuAttrs(authConfig dto.AuthConfig, fieldMap []dto.KV) ([]dto.KV, error) {
	var feishuConfig dto.FeishuConfig
	if err := copier.Copy(&feishuConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, feishuConfig.ToKVs()...)
	fieldMapBytes, err := json.Marshal(fieldMap)
	if err != nil {
		i.log.Errorf("Marshal failed. err=%v", err)
		return []dto.KV{}, err
	}
	result = append(result, dto.KV{
		Key:   dto.AttrKeyFeiShuFieldMap,
		Value: string(fieldMapBytes),
	})
	return result, nil
}

func (i IdpUsecase) formatSmsAttrs(authConfig dto.AuthConfig) ([]dto.KV, error) {
	var smsConfig dto.SmsConfig
	if err := copier.Copy(&smsConfig, authConfig); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return []dto.KV{}, err
	}
	var result []dto.KV
	result = append(result, smsConfig.ToKVs()...)
	return result, nil
}

func (i IdpUsecase) ListIDP(ctx context.Context, corpId string, limit, offset int) (dto.ListIDPResp, error) {
	idpWithGroupList, err := i.repo.ListIDPWithGroup(ctx, corpId, limit, offset)
	if err != nil {
		i.log.Errorf("ListIDPWithGroup failed. err=%v.", err)
		return dto.ListIDPResp{}, err
	}
	idpInfos, err := dto.IDPWithGroups(idpWithGroupList).ToIDPInfo()
	if err != nil {
		i.log.Errorf("ToIDPInfo failed. err=%v", err)
		return dto.ListIDPResp{}, err
	}
	count, err := i.repo.CountIDP(ctx, corpId)
	if err != nil {
		i.log.Errorf("CountIDP failed. err=%v", err)
		return dto.ListIDPResp{}, err
	}

	return dto.ListIDPResp{IdpInfos: idpInfos, Count: uint32(count)}, nil
}

func (i IdpUsecase) ListMainIdpWithAttrs(ctx context.Context, corpId string) ([]dto.IDPInfo, error) {
	bindIdpWithAttrs, err := i.repo.ListBindIDPWithAttrs(ctx, corpId)
	if err != nil {
		i.log.Errorf("ListBindIDPWithAttrs failed. err=%v", err)
		return []dto.IDPInfo{}, err
	}
	return dto.IDPWithAttrs(bindIdpWithAttrs).ToIDPInfo(true), nil
}

func (i IdpUsecase) DeleteIDP(ctx context.Context, corpId, idpId, name string) error {
	idp, err := i.repo.GetIDP(ctx, corpId, idpId)
	if err != nil {
		i.log.Errorf("GetIDP failed. err=%v", err)
		return err
	}
	if idp.IsDefault {
		return pb.ErrorDefaultDataConflict("idpId=%v is default")
	}

	if err := i.repo.DeleteIDP(ctx, corpId, idpId, name); err != nil {
		i.log.Errorf("DeleteIDP failed. corpId=%v, idpId=%v", corpId, idpId)
		return err
	}
	return nil
}

func (i IdpUsecase) GetIDPDetail(ctx context.Context, corpId, id string) (dto.IDPDetail, error) {
	var idpDetail dto.IDPDetail
	// 获取基本信息
	idpBasic, err := i.repo.GetIDP(ctx, corpId, id)
	if err != nil {
		i.log.Errorf("GetIDP failed. err=%v", err)
		return dto.IDPDetail{}, err
	}
	if err := copier.Copy(&idpDetail, idpBasic); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return dto.IDPDetail{}, err
	}

	if dto.IDPType(idpBasic.Type) == dto.IDPTypeLocal {
		return idpDetail, nil
	}
	key := []byte(id[:32])
	var ciphertext string

	// 获取属性
	attrs, err := i.repo.GetIDPAttr(ctx, id)
	if err != nil {
		i.log.Errorf("GetIDPAttr failed. err=%v", err)
		return dto.IDPDetail{}, err
	}
	idpAttr := dto.KVsToIdpAttr(attrs)
	switch dto.IDPType(idpBasic.Type) {
	case dto.IDPTypeQiYeWx:
		if idpAttr.Secret != "" {
			plaintext := []byte(idpAttr.Secret)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.Secret = ciphertext
		}
	case dto.IDPTypeFeiShu:
		if idpAttr.FeishuConfig.AppSecret != "" {
			plaintext := []byte(idpAttr.FeishuConfig.AppSecret)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.FeishuConfig.AppSecret = ciphertext
			idpDetail.AuthConfig.AppSecret = ciphertext
		}
	case dto.IDPTypeDingtalk:
		if idpAttr.DingtalkConfig.AppSecret != "" {
			plaintext := []byte(idpAttr.DingtalkConfig.AppSecret)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.DingtalkConfig.AppSecret = ciphertext
			idpDetail.AuthConfig.AppSecret = ciphertext
		}
		if idpAttr.DingtalkConfig.AppKey != "" {
			idpDetail.AuthConfig.AppKey = idpAttr.DingtalkConfig.AppKey
		}
		if idpAttr.DingtalkConfig.DingtalkCorpId != "" {
			idpDetail.AuthConfig.DdCorpId = idpAttr.DingtalkConfig.DingtalkCorpId
		}
	case dto.IDPTypeSMS:
		if idpAttr.SmsConfig.AppSecret != "" {
			plaintext := []byte(idpAttr.SmsConfig.AppSecret)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.SmsConfig.AppSecret = ciphertext
			idpDetail.AuthConfig.AppSecret = ciphertext
		}
		if idpAttr.AccessKeySecret != "" {
			plaintext := []byte(idpAttr.AccessKeySecret)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.AccessKeySecret = ciphertext
		}
		if idpAttr.SecretKey != "" {
			plaintext := []byte(idpAttr.SecretKey)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.SecretKey = ciphertext
		}
		if idpAttr.SmsConfig.AppKey != "" {
			idpDetail.AuthConfig.AppKey = idpAttr.SmsConfig.AppKey
		}
	case dto.IDPTypeLdap:
		if idpAttr.AdministratorPassword != "" {
			plaintext := []byte(idpAttr.AdministratorPassword)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.AdministratorPassword = ciphertext
		}
	case dto.IDPTypeMsad:
		if idpAttr.AdministratorPassword != "" {
			plaintext := []byte(idpAttr.AdministratorPassword)
			ciphertext, err = common.Encrypt(key, plaintext)
			if err != nil {
				i.log.Errorf("Encrypt err=%v", err)
				return dto.IDPDetail{}, err
			}
			idpAttr.AdministratorPassword = ciphertext
		}
	}

	if err := copier.Copy(&idpDetail.AuthConfig, &idpAttr); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return dto.IDPDetail{}, err
	}
	casAttr := dto.KVsIdpAttrCas(attrs)
	if err := copier.Copy(&idpDetail.CasConfig, &casAttr); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return dto.IDPDetail{}, err
	}

	idpDetail.FieldMap = idpAttr.FieldMap

	idpDetail.AuthConfig.TokenDeviation = idpAttr.TokenDeviation

	// 获取绑定组信息
	bindGroups, err := i.repo.GetIDPBindGroup(ctx, corpId, id)
	if err != nil {
		i.log.Errorf("GetIDPBindGroup failed. err=%v", err)
		return dto.IDPDetail{}, err
	}
	var bindGroupList []dto.BindGroupInfo
	if err := copier.Copy(&bindGroupList, &bindGroups); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return dto.IDPDetail{}, err
	}
	idpDetail.BindGroupList = bindGroupList
	return idpDetail, nil
}

func (i IdpUsecase) UpdateIDP(ctx context.Context, param dto.UpdateIDPParam) error {
	idp, err := i.repo.GetIDP(ctx, param.CorpId, param.Id)
	if err != nil {
		i.log.Errorf("GetIDP failed. err=%v", err)
		return err
	}
	if param.Name != idp.Name {
		_, err := i.repo.GetIDPByName(ctx, param.CorpId, param.Name)
		if err != nil && !pb.IsRecordNotFound(err) {
			i.log.Errorf("GetIDPByName failed. err=%v", err)
			return err
		}
		if err == nil {
			return pb.ErrorNameConflict("idp name conflict. corpId=%v, name=%v", param.CorpId, param.Name)
		}
	}
	//secret解密
	key := []byte(param.Id[:32])
	// 解密用于保存，能正常解密则用解密后的，不能则用解密前的
	decSecret, err := common.Decrypt(key, param.AuthConfig.AccessKeySecret)
	if err == nil {
		param.AuthConfig.AccessKeySecret = string(decSecret)
	}
	decSecret, err = common.Decrypt(key, param.AuthConfig.AppSecret)
	if err == nil {
		param.AuthConfig.AppSecret = string(decSecret)
	}
	decSecret, err = common.Decrypt(key, param.AuthConfig.SecretKey)
	if err == nil {
		param.AuthConfig.SecretKey = string(decSecret)
	}
	decSecret, err = common.Decrypt(key, param.AuthConfig.Secret)
	if err == nil {
		param.AuthConfig.Secret = string(decSecret)
	}
	decSecret, err = common.Decrypt(key, param.AuthConfig.AdministratorPassword)
	if err == nil {
		param.AuthConfig.AdministratorPassword = string(decSecret)
	}
	var daoParam dto.UpdateIDPDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		i.log.Errorf("Copy failed. err=%v", err)
		return err
	}

	var attrs []dto.KV
	if param.TemplateType == "email" {
		// 邮箱渠道的验证码认证
		attrs, err = i.formatEmailAttrs(param.AuthConfig)
		if err != nil {
			i.log.Errorf("formatAssistEmailAttrs failed. err=%v", err)
			return err
		}
	} else {
		// 常规认证处理
		attrs, err = i.formatIDPAttrs(dto.IDPType(idp.Type), idp.TemplateType, param.AuthConfig, param.FieldMap)
		if err != nil {
			i.log.Errorf("formatIDPAttrs failed. err=%v", err)
			return err
		}
	}
	daoParam.Attr = attrs
	if err := i.repo.UpdateIDP(ctx, daoParam, param.CorpId); err != nil {
		i.log.Errorf("UpdateIDP failed. err=%v", err)
		return err
	}
	return nil
}

func (i IdpUsecase) QuerySecondaryAuth(ctx context.Context, policies []string) ([]*model.TbIdentityProvider, error) {
	return []*model.TbIdentityProvider{}, nil
}

// GetIDPById 根据ID获取IDP配置
func (i *IdpUsecase) GetIDPById(ctx context.Context, corpId, idpId string) (*dto.IDPBasic, error) {
	// 调用repo层的GetIDP方法获取数据库中的IDP记录
	idpModel, err := i.repo.GetIDP(ctx, corpId, idpId)
	if err != nil {
		i.log.Errorf("获取IDP配置失败: %v", err)
		return nil, err
	}

	// 转换为DTO对象返回
	idpDto := &dto.IDPBasic{
		ID:           idpModel.ID,
		Name:         idpModel.Name,
		Type:         idpModel.Type,
		TemplateType: idpModel.TemplateType,
		Enable:       idpModel.Enable,
		Avatar:       idpModel.Avatar,
		SourceID:     idpModel.SourceID,
	}

	return idpDto, nil
}

// GetIDPAttrs 获取IDP属性
func (i *IdpUsecase) GetIDPAttrs(ctx context.Context, corpId, idpId string) (map[string]string, error) {
	// 调用repo层的GetIDPAttr方法获取IDP属性列表
	attrs, err := i.repo.GetIDPAttr(ctx, idpId)
	if err != nil {
		i.log.Errorf("获取IDP属性失败: %v", err)
		return nil, err
	}

	// 将KV列表转换为map结构
	attrMap := make(map[string]string)
	for _, attr := range attrs {
		attrMap[attr.Key] = attr.Value
	}

	return attrMap, nil
}
