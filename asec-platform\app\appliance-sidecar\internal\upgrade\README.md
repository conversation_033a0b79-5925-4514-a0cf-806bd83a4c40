# 升级模块网络请求迁移文档

## 📋 概述

本文档记录了ASEC平台升级模块从LightSentinel直接网络请求迁移到Sidecar统一管理的完整架构设计和实现细节。

## 🎯 迁移目标

- **统一网络管理**: 将所有网络请求集中到Sidecar，便于SPA模式和TLS协议的统一处理
- **职责分离**: Sidecar负责网络通信，LightSentinel专注业务逻辑
- **向后兼容**: 保持LightSentinel接口不变，实现透明迁移

## 🔄 架构对比

### 迁移前架构
```
LightSentinel (C++) ──直接CURL请求──> 平台API
├── AsecUpgradeService::check_upgrade()     ──> /console/v1/agents/version_check
└── AsecPlatformHelper::reportUpgradeStatus() ──> /console/v1/agent/upgrade_status
```

### 迁移后架构
```
LightSentinel (C++) ──文件通信──> Sidecar (Go) ──网络请求──> 平台API
├── 读取 upgrade_check.json                    ├── 定时查询升级信息
└── 写入 upgrade_status_request.json          └── 监控状态上报请求
```

## 📁 文件结构

```
asec-platform/app/appliance-sidecar/internal/upgrade/
├── upgrade.go              # 统一升级服务模块
└── README.md              # 本文档

相关文件:
├── constants/api_endpoints.go    # API端点和配置文件路径管理
├── cmd/start/start.go           # 主程序启动集成
└── Service/LightSentinel/src/biz/
    ├── AsecUpgradeService.cpp   # 升级服务（已修改为文件读取）
    └── AsecPlatformHelper.cpp   # 平台助手（已修改为文件写入）
```

## 🔧 核心组件

### 1. 统一升级服务 (`upgrade.go`)

#### 主要功能
- **升级查询服务**: 30分钟间隔定时查询升级信息
- **状态上报监控**: 5秒间隔监控状态上报请求文件
- **文件通信**: 通过JSON文件与LightSentinel交换数据

#### 关键函数
```go
// 统一服务入口
func StartUpgradeService()

// 升级查询服务
func startUpgradeCheckService()

// 状态上报监控服务  
func startUpgradeStatusService()

// 执行升级查询网络请求
func checkUpgrade() (*UpgradeCheckInfo, error)

// 执行状态上报网络请求
func reportUpgradeStatus(lastVersion, nextVersion, status, failedReason string) error
```

### 2. 数据结构

#### 升级查询结果 (`UpgradeCheckInfo`)
```go
type UpgradeCheckInfo struct {
    UpgradeVersion string `json:"upgrade_version"` // 最新版本
    UpgradeEnabled bool   `json:"upgrade_enabled"` // 是否需要升级
    ForceUpgrade   bool   `json:"force_upgrade"`   // 是否强制升级
    DownloadURL    string `json:"download_url"`    // 下载地址
    PackageMD5     string `json:"package_md5"`     // MD5校验值
    PackageName    string `json:"package_name"`    // 安装包文件名
    PackagePath    string `json:"package_path"`    // 本地路径
    Timestamp      int64  `json:"timestamp"`       // 查询时间戳
}
```

## 🔄 运行流程

### 升级查询流程

```mermaid
sequenceDiagram
    participant LS as LightSentinel
    participant SC as Sidecar
    participant API as 平台API
    participant File as upgrade_check.json

    Note over SC: 启动时立即执行，然后30分钟间隔
    SC->>API: POST /console/v1/agents/version_check
    API-->>SC: 返回升级信息
    SC->>File: 写入升级查询结果
    
    Note over LS: 升级检查线程
    LS->>File: 读取升级信息
    File-->>LS: 返回CheckUpgradeInfo
    LS->>LS: 执行业务逻辑（下载、安装等）
```

### 状态上报流程

```mermaid
sequenceDiagram
    participant LS as LightSentinel
    participant SC as Sidecar
    participant API as 平台API
    participant ReqFile as upgrade_status_request.json

    Note over LS: 升级过程中需要上报状态
    LS->>ReqFile: 写入状态上报请求
    
    Note over SC: 5秒间隔检查请求文件
    SC->>ReqFile: 检查并读取请求
    ReqFile-->>SC: 返回上报参数
    SC->>API: POST /console/v1/agent/upgrade_status
    API-->>SC: 返回上报结果
    SC->>ReqFile: 删除已处理的请求文件
```

## 📊 文件通信接口

### 升级查询结果文件 (`upgrade_check.json`)
```json
{
  "upgrade_version": "1.2.3",
  "upgrade_enabled": true,
  "force_upgrade": false,
  "download_url": "https://platform.example.com/download/installer.exe",
  "package_md5": "abc123def456...",
  "package_name": "installer.exe",
  "package_path": "",
  "timestamp": 1703123456
}
```

### 状态上报请求文件 (`upgrade_status_request.json`)
```json
{
  "last_version": "1.2.2",
  "next_version": "1.2.3", 
  "status": "success",
  "failed_reason": "",
  "timestamp": 1703123456,
  "request_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

## ⚡ 技术特性

### 1. 配置缓存与变更检测
- **内容比较**: 使用`upgradeCheckInfoEqual()`避免相同内容的重复文件写入
- **原子操作**: 使用临时文件+重命名确保数据一致性
- **变更检测**: 只有配置真正变化时才触发文件写入

### 2. 错误处理与容错
- **文件不存在**: 优雅处理，记录警告但不中断服务
- **JSON解析错误**: 删除无效文件，避免重复处理
- **网络请求失败**: 记录错误，支持扩展重试机制

### 3. 并发安全
- **LightSentinel**: 使用mutex保护升级检查逻辑
- **Sidecar**: 使用独立goroutine处理不同服务
- **文件操作**: 原子写入避免并发冲突

## 🎯 业务逻辑保持

### 升级流程完整性
1. **定时检查**: 30分钟间隔查询升级信息
2. **下载验证**: MD5校验确保安装包完整性  
3. **强制升级**: 立即执行安装流程
4. **状态上报**: 升级各阶段状态实时上报
5. **进程管理**: 协调所有ASEC组件的重启

### 兼容性保证
- **数据结构**: `UpgradeCheckInfo`与LightSentinel的`CheckUpgradeInfo`完全匹配
- **接口签名**: `reportUpgradeStatus()`参数保持不变
- **业务逻辑**: 下载、安装、验证逻辑完全保留在LightSentinel

## 🚀 架构优势

1. **统一网络管理**: 所有网络请求集中在Sidecar，便于SPA模式和TLS协议统一处理
2. **进程解耦**: LightSentinel专注业务逻辑，Sidecar专注网络通信
3. **容错能力**: 网络问题不影响本地业务逻辑执行
4. **可维护性**: 集中的API端点管理和配置文件路径管理
5. **可扩展性**: 文件通信模式可轻松扩展到其他网络请求迁移

## 📝 使用说明

### 启动服务
升级服务在Sidecar启动时自动启动：
```go
// 在 cmd/start/start.go 中
go upgrade.StartUpgradeService()
```

### 配置文件位置
- 升级查询结果: `{程序目录}/config/upgrade_check.json`
- 状态上报请求: `{程序目录}/config/upgrade_status_request.json`

### 日志监控
- Sidecar日志: 升级查询和状态上报的网络请求日志
- LightSentinel日志: 升级业务逻辑和文件读写日志

## 🔍 故障排查

### 常见问题
1. **升级检查失败**: 检查Sidecar网络连接和平台API可用性
2. **状态上报失败**: 检查请求文件格式和网络连接
3. **文件读写错误**: 检查配置目录权限和磁盘空间

### 调试方法
1. 查看Sidecar日志中的网络请求状态
2. 检查配置文件内容和格式
3. 验证LightSentinel的文件读写逻辑

---

*本文档记录了升级模块迁移的完整设计和实现，为后续的网络请求迁移提供参考模式。*
