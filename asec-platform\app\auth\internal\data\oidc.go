package data

import (
	"context"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type oidcRepo struct {
	data *Data
	log  *log.Helper
}

func NewOIDCRepo(data *Data, logger log.Logger) biz.OIDCRepo {
	return &oidcRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// OAuth2Client相关操作

// GetOAuth2ClientByID 根据ID获取OAuth2客户端
func (r *oidcRepo) GetOAuth2ClientByID(ctx context.Context, clientID string) (*dto.OAuth2Client, error) {
	var client dto.OAuth2Client
	err := r.data.db.WithContext(ctx).Where("client_id = ? AND status = 1", clientID).First(&client).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &client, nil
}

// CreateOAuth2Client 创建OAuth2客户端
func (r *oidcRepo) CreateOAuth2Client(ctx context.Context, client *dto.OAuth2Client) error {
	return r.data.db.WithContext(ctx).Create(client).Error
}

// UpdateOAuth2Client 更新OAuth2客户端
func (r *oidcRepo) UpdateOAuth2Client(ctx context.Context, client *dto.OAuth2Client) error {
	return r.data.db.WithContext(ctx).Save(client).Error
}

// DeleteOAuth2Client 删除OAuth2客户端（软删除，设置状态为0）
func (r *oidcRepo) DeleteOAuth2Client(ctx context.Context, clientID string) error {
	return r.data.db.WithContext(ctx).
		Model(&dto.OAuth2Client{}).
		Where("client_id = ?", clientID).
		Update("status", 0).Error
}

// ListOAuth2Clients 列出OAuth2客户端
func (r *oidcRepo) ListOAuth2Clients(ctx context.Context, corpID int64, limit, offset int) ([]*dto.OAuth2Client, int64, error) {
	var clients []*dto.OAuth2Client
	var total int64

	query := r.data.db.WithContext(ctx).Model(&dto.OAuth2Client{}).Where("corp_id = ? AND status = 1", corpID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Limit(limit).Offset(offset).Find(&clients).Error; err != nil {
		return nil, 0, err
	}

	return clients, total, nil
}

// OIDCAuthorizationCode相关操作

// CreateAuthorizationCode 创建授权码
func (r *oidcRepo) CreateAuthorizationCode(ctx context.Context, code *dto.OIDCAuthorizationCode) error {
	return r.data.db.WithContext(ctx).Create(code).Error
}

// GetAuthorizationCode 获取授权码
func (r *oidcRepo) GetAuthorizationCode(ctx context.Context, code string) (*dto.OIDCAuthorizationCode, error) {
	var authCode dto.OIDCAuthorizationCode
	err := r.data.db.WithContext(ctx).Where("code = ?", code).First(&authCode).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &authCode, nil
}

// DeleteAuthorizationCode 删除授权码（使用后立即删除）
func (r *oidcRepo) DeleteAuthorizationCode(ctx context.Context, code string) error {
	return r.data.db.WithContext(ctx).Where("code = ?", code).Delete(&dto.OIDCAuthorizationCode{}).Error
}

// CleanExpiredAuthorizationCodes 清理过期的授权码
func (r *oidcRepo) CleanExpiredAuthorizationCodes(ctx context.Context) error {
	return r.data.db.WithContext(ctx).
		Where("expires_at < ?", time.Now()).
		Delete(&dto.OIDCAuthorizationCode{}).Error
}

// OIDCAccessToken相关操作

// CreateAccessToken 创建访问令牌
func (r *oidcRepo) CreateAccessToken(ctx context.Context, token *dto.OIDCAccessToken) error {
	return r.data.db.WithContext(ctx).Create(token).Error
}

// GetAccessToken 获取访问令牌
func (r *oidcRepo) GetAccessToken(ctx context.Context, tokenID string) (*dto.OIDCAccessToken, error) {
	var token dto.OIDCAccessToken
	err := r.data.db.WithContext(ctx).Where("token_id = ?", tokenID).First(&token).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &token, nil
}

// GetAccessTokenByToken 根据令牌内容获取访问令牌记录
func (r *oidcRepo) GetAccessTokenByToken(ctx context.Context, token string) (*dto.OIDCAccessToken, error) {
	var accessToken dto.OIDCAccessToken
	err := r.data.db.WithContext(ctx).Where("token = ?", token).First(&accessToken).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &accessToken, nil
}

// RevokeAccessToken 撤销访问令牌
func (r *oidcRepo) RevokeAccessToken(ctx context.Context, tokenID string) error {
	return r.data.db.WithContext(ctx).Where("token_id = ?", tokenID).Delete(&dto.OIDCAccessToken{}).Error
}

// RevokeAccessTokenByClientUser 撤销指定客户端和用户的所有访问令牌
func (r *oidcRepo) RevokeAccessTokenByClientUser(ctx context.Context, clientID, userID string) error {
	return r.data.db.WithContext(ctx).
		Where("client_id = ? AND user_id = ?", clientID, userID).
		Delete(&dto.OIDCAccessToken{}).Error
}

// CleanExpiredAccessTokens 清理过期的访问令牌
func (r *oidcRepo) CleanExpiredAccessTokens(ctx context.Context) error {
	return r.data.db.WithContext(ctx).
		Where("expires_at < ?", time.Now()).
		Delete(&dto.OIDCAccessToken{}).Error
}

// OIDCRefreshToken相关操作

// CreateRefreshToken 创建刷新令牌
func (r *oidcRepo) CreateRefreshToken(ctx context.Context, token *dto.OIDCRefreshToken) error {
	return r.data.db.WithContext(ctx).Create(token).Error
}

// GetRefreshToken 获取刷新令牌
func (r *oidcRepo) GetRefreshToken(ctx context.Context, tokenID string) (*dto.OIDCRefreshToken, error) {
	var token dto.OIDCRefreshToken
	err := r.data.db.WithContext(ctx).Where("token_id = ?", tokenID).First(&token).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &token, nil
}

// GetRefreshTokenByToken 根据令牌内容获取刷新令牌记录
func (r *oidcRepo) GetRefreshTokenByToken(ctx context.Context, token string) (*dto.OIDCRefreshToken, error) {
	var refreshToken dto.OIDCRefreshToken
	err := r.data.db.WithContext(ctx).Where("token = ?", token).First(&refreshToken).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &refreshToken, nil
}

// RevokeRefreshToken 撤销刷新令牌
func (r *oidcRepo) RevokeRefreshToken(ctx context.Context, tokenID string) error {
	return r.data.db.WithContext(ctx).Where("token_id = ?", tokenID).Delete(&dto.OIDCRefreshToken{}).Error
}

// RevokeRefreshTokenByClientUser 撤销指定客户端和用户的所有刷新令牌
func (r *oidcRepo) RevokeRefreshTokenByClientUser(ctx context.Context, clientID, userID string) error {
	return r.data.db.WithContext(ctx).
		Where("client_id = ? AND user_id = ?", clientID, userID).
		Delete(&dto.OIDCRefreshToken{}).Error
}

// CleanExpiredRefreshTokens 清理过期的刷新令牌
func (r *oidcRepo) CleanExpiredRefreshTokens(ctx context.Context) error {
	return r.data.db.WithContext(ctx).
		Where("expires_at < ?", time.Now()).
		Delete(&dto.OIDCRefreshToken{}).Error
}

// 事务操作

// RevokeAllTokensByClientUser 撤销指定客户端和用户的所有令牌（访问令牌和刷新令牌）
func (r *oidcRepo) RevokeAllTokensByClientUser(ctx context.Context, clientID, userID string) error {
	return r.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 撤销访问令牌
		if err := tx.Where("client_id = ? AND user_id = ?", clientID, userID).Delete(&dto.OIDCAccessToken{}).Error; err != nil {
			return err
		}
		// 撤销刷新令牌
		if err := tx.Where("client_id = ? AND user_id = ?", clientID, userID).Delete(&dto.OIDCRefreshToken{}).Error; err != nil {
			return err
		}
		return nil
	})
}

// CreateTokenPair 创建访问令牌和刷新令牌对
func (r *oidcRepo) CreateTokenPair(ctx context.Context, accessToken *dto.OIDCAccessToken, refreshToken *dto.OIDCRefreshToken) error {
	return r.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建访问令牌
		if err := tx.Create(accessToken).Error; err != nil {
			return err
		}
		// 创建刷新令牌
		if err := tx.Create(refreshToken).Error; err != nil {
			return err
		}
		return nil
	})
}
