syntax = "proto3";

package api.appliance;
option go_package = "asdsec.com/asec/platform/api/appliance/v1;v1";

enum CfgApplianceType
{
  UNKNOWN_APPLIANCE_TYPE = 0;
  // 客户端
  AGENT_CFG = 1;
  // 安全边缘代理
  SECURITY_EDGE_CFG = 2;
  // 连接器
  CONNECTOR_CFG = 3;
  // 网关
  GATEWAY_CFG = 4;
}

enum CfgType{
  UNKNOWN_CFG_TYPE = 0;
  // 进程过滤配置
  PROCESS_FILTER_CFG = 1;
}


service ApplianceCfg{
  // 获取配置版本信息 todo cl 暂未实现版本比对获取配置
  rpc GetConfigVersion(ConfigReq) returns (ConfigVersionResp);

  // 获取配置
  rpc GetCfg(ConfigReq) returns (GetConfigResp);
}

// 后续根据需求加上 终端属性\用户属性\配置属性等,先简单处理
message ConfigReq{
  CfgApplianceType cfg_appliance_type = 1;
  CfgType cfg_type = 2;
}

message ConfigVersionResp{
  CfgType cfg_type = 1;
  string cfg_version = 2;
}

// 后续根据需求加上 配置属性\模块属性等
message GetConfigResp{
  CfgType cfg_type = 1;
  string cfg_id = 3;
  bytes config_data = 2;
}
