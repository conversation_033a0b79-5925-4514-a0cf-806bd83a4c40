package dto

import "time"

// WechatVerifyFileDto 微信验证文件DTO
type WechatVerifyFileDto struct {
	ID          string    `json:"id"`           // 文件ID
	FileName    string    `json:"file_name"`    // 文件名，如：WW_verify_xxx.txt
	Content     string    `json:"content"`      // 文件内容
	IsEnabled   bool      `json:"is_enabled"`   // 是否启用
	Description string    `json:"description"`  // 描述信息
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`   // 更新时间
}
