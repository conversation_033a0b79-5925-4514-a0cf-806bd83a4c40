package global

import (
	"context"
	"database/sql"
)

// UpdateBlock 阻断迭代数据变更
func UpdateBlock() error {
	db, err := InitSqliteByName(EventsDbName)
	defer CloseSqlite(db)
	if err != nil {
		return err
	}
	var exists int64
	// 判断依据是列是否存在
	err = db.QueryRow("SELECT EXISTS ( SELECT * FROM pragma_table_info ( 'tbl_file_monitor' ) WHERE name = 'sensitive_rule_id' );").
		Scan(&exists)
	if exists >= 0 {
		return nil
	}
	tx, beginTxErr := db.BeginTx(context.TODO(), &sql.TxOptions{Isolation: sql.LevelSerializable})
	if beginTxErr != nil {
		return beginTxErr
	}

	updateSql := `ALTER TABLE tbl_file_monitor ADD COLUMN sensitive_rule_id TEXT;
		ALTER TABLE tbl_file_monitor ADD COLUMN sensitive_rule_name TEXT;
		ALTER TABLE tbl_file_monitor ADD COLUMN sensitive_level INTEGER;
		ALTER TABLE tbl_file_monitor ADD COLUMN data_category TEXT;
		ALTER TABLE tbl_file_monitor ADD COLUMN source_id TEXT;
		ALTER TABLE tbl_file_monitor ADD COLUMN source_name TEXT;
		ALTER TABLE tbl_file_monitor ADD COLUMN source_type TEXT;
		ALTER TABLE tbl_file_monitor ADD COLUMN sensitive_info TEXT;
		ALTER TABLE tbl_file_monitor ADD COLUMN file_hide_suffix INTEGER;
		CREATE TABLE IF NOT EXISTS "tb_ddr_alert" (
		  "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
		  "uuid" TEXT NOT NULL,
		  "event_type" TEXT NOT NULL,
		  "event_sub_type" TEXT NOT NULL,
		  "event_source" TEXT NOT NULL,
		  "file_name" TEXT NOT NULL,
		  "file_type" TEXT NOT NULL,
		  "file_path" TEXT NOT NULL,
		  "original_file_name" TEXT NOT NULL,
		  "original_file_path" TEXT NOT NULL,
		  "file_size" INTEGER NOT NULL,
		  "owner" TEXT NOT NULL,
		  "file_create_time" INTEGER NOT NULL,
		  "last_change_time" INTEGER NOT NULL,
		  "extension_name" TEXT NOT NULL,
		  "file_category_id" INTEGER NOT NULL,
		  "real_extension_name" INTEGER NOT NULL,
		  "name_match_info" TEXT NOT NULL,
		  "content_match_info" TEXT NOT NULL,
		  "md5" TEXT NOT NULL,
		  "sha256" TEXT NOT NULL,
		  "activity" TEXT NOT NULL,
		  "occur_time" INTEGER NOT NULL,
		  "channel" TEXT NOT NULL,
		  "channel_type" TEXT NOT NULL,
		  "software_path" TEXT NOT NULL,
		  "dst_path" TEXT NOT NULL,
		  "compress_encrypt" INTEGER NOT NULL,
		  "trace_id" TEXT NOT NULL DEFAULT '',
		  "sub_src_trace_id" TEXT NOT NULL DEFAULT '',
		  "src_path" TEXT NOT NULL DEFAULT '',
		  "sensitive_rule_id" TEXT,
		  "sensitive_rule_name" TEXT,
		  "sensitive_level" INTEGER,
		  "data_category" TEXT,
		  "source_id" TEXT,
		  "source_name" TEXT,
		  "source_type" TEXT,
		  "sensitive_info" TEXT,
		  "file_hide_suffix" INTEGER,
		  "file_event_id" text,
		  "policy_id" text,
		  "policy_name" TEXT,
		  "alert_type" TEXT,
		  "engine_name" TEXT,
		  "enable_analysis" integer,
		  "dispose_action" integer
		);`
	_, err = tx.Exec(updateSql)
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}
