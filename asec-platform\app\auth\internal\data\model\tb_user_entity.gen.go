// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserEntity = "tb_user_entity"

// TbUserEntity mapped from table <tb_user_entity>
type TbUserEntity struct {
	ID               string    `gorm:"column:id;primaryKey;comment:用户id" json:"id"`           // 用户id
	Name             string    `gorm:"column:name;not null;comment:用户登陆名" json:"name"`        // 用户登陆名
	DisplayName      string    `gorm:"column:display_name;comment:用户显示名" json:"display_name"` // 用户显示名
	TrueName         string    `gorm:"column:true_name;comment:用户真实姓名" json:"true_name"`      // 用户真实姓名
	NickName         string    `gorm:"column:nick_name" json:"nick_name"`
	Avatar           string    `gorm:"column:avatar" json:"avatar"`
	GroupID          string    `gorm:"column:group_id;not null;comment:分组id" json:"group_id"` // 分组id
	CorpID           string    `gorm:"column:corp_id;comment:租户id" json:"corp_id"`            // 租户id
	SourceID         string    `gorm:"column:source_id;comment:来源id" json:"source_id"`        // 来源id
	Phone            string    `gorm:"column:phone;comment:手机号" json:"phone"`                 // 手机号
	Email            string    `gorm:"column:email;comment:邮箱" json:"email"`                  // 邮箱
	Enable           bool      `gorm:"column:enable;default:true" json:"enable"`
	ExpireType       string    `gorm:"column:expire_type" json:"expire_type"`
	ExpireEnd        time.Time `gorm:"column:expire_end" json:"expire_end"`
	RootGroupID      string    `gorm:"column:root_group_id;not null" json:"root_group_id"`
	Identify         string    `gorm:"column:identify" json:"identify"`
	CreatedAt        time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	AuthType         string    `gorm:"column:auth_type;comment:用户认证类型:password,oauth2等" json:"auth_type"` // 用户认证类型:password,oauth2等
	ActiveTime       time.Time `gorm:"column:active_time;comment:上次活跃时间" json:"active_time"`              // 上次活跃时间
	LockStatus       bool      `gorm:"column:lock_status" json:"lock_status"`
	SecurityCode string   `gorm:"column:security_code" json:"security_code"`
	ActivationSecret string    `gorm:"column:activation_secret;comment:TOTP验证秘钥" json:"activation_secret"` // TOTP验证秘钥
	CurrentSecret    string    `gorm:"column:current_secret;comment:TOTP当前验证秘钥" json:"current_secret"`     // TOTP当前验证秘钥
}

// TableName TbUserEntity's table name
func (*TbUserEntity) TableName() string {
	return TableNameTbUserEntity
}
