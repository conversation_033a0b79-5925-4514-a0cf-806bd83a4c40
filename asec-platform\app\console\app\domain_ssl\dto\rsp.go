package dto

import (
	"asdsec.com/asec/platform/pkg/model"
)

type GetDomainCertificateListRsp struct {
	model.CommonPage
	Data []DomainListItem `json:"data"`
}

type DomainListItem struct {
	Id          string `json:"id" gorm:"id"`
	Name        string `json:"name" gorm:"name"`
	Domain      string `json:"domain" gorm:"domain"`
	ExpireTime  string `json:"expire_time" gorm:"expire_time"`
	Certificate string `json:"certificate" gorm:"certificate"`
	PrivateKey  string `json:"private_key" gorm:"private_key"`
}

type GetCertificateDetailRsp struct {
	IssueAgency            string `json:"issue_agency"`
	PublicKeyAlgorithm     string `json:"public_key_algorithm"`
	Id                     string `json:"id"`
	Name                   string `json:"name"`
	Domain                 string `json:"domain"`
	SignatureAlgorithm     string `json:"signature_algorithm"`
	CertificateFingerprint string `json:"certificate_fingerprint"`
	IssueTime              string `json:"issue_time"`
	ExpireTime             string `json:"expire_time"`
	UpdatedAt              string `json:"updated_at"`
	Status                 int    `json:"status"`
}
