// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameTbComponentConfig = "tb_component_config"

// TbComponentConfig mapped from table <tb_component_config>
type TbComponentConfig struct {
	ID          string `gorm:"column:id;primaryKey" json:"id"`
	ComponentID string `gorm:"column:component_id" json:"component_id"`
	Name        string `gorm:"column:name;not null" json:"name"`
	Value       string `gorm:"column:value;not null" json:"value"`
}

// TableName TbComponentConfig's table name
func (*TbComponentConfig) TableName() string {
	return TableNameTbComponentConfig
}
