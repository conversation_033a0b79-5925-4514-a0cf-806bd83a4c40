package info_collect

import (
	"context"
	"math/rand"
	"os"
	"sync"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/pkg/utils"
	"google.golang.org/grpc"
)

const (
	// 上传等待间隔（30分钟）
	duration = 60 * 30
	// 随机延迟范围1-60分钟
	maxRandomDelay = 60 * 60 // 60分钟
	minRandomDelay = 60      // 1分钟
	// 字符串最大长度限制
	maxStrLength = 512
)

// 添加开关配置
var (
	// 默认关闭信息收集
	isInfoCollectEnabled = false
)

// 添加开关控制函数
func EnableInfoCollect(enable bool) {
	isInfoCollectEnabled = enable
}

// 进程信息
const processQuerySql = `SELECT process_name,proc_path,cmd_line,
						user_name,process_id,parent_pid,mem_used_kb,
						vmmem_used_kb,thread_num,cpu_kernel_used,cpu_user_used
						FROM process_info;`

// 软件信息
const softwareQuerySql = `SELECT software_name,software_version,software_company,
				    	 uninstall_cmd,install_path
						 FROM software_info;`

// 系统信息
const systemQuerySql = `SELECT system_bit, product_name, system_language,system_version
						FROM system_info;`

func SendCollectedInfo(ctx context.Context, wg *sync.WaitGroup) {
	// 如果开关关闭，直接返回
	if !isInfoCollectEnabled {
		global.Logger.Sugar().Debug("info collect is disabled, skip sending collected info")
		return
	}

	randomDelay := float64(minRandomDelay + rand.Intn(maxRandomDelay-minRandomDelay))

	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   doSendCollectedInfo,
		RunType:      common.SimpleSend,
		WaitSecond:   duration,
		RandomOffset: randomDelay,
	}
	common.Send(param)
}

func doSendCollectedInfo(conn *grpc.ClientConn, ctx context.Context) error {
	// 判断文件是否存在
	dbPath := utils.GetConfigDir() + "\\" + global.CollectInfoDbName
	_, err := os.Stat(dbPath)
	if err != nil {
		// 文件不存在
		global.Logger.Sugar().Warnf("collected info sql is not exist :%v", err)
		return err
	}

	// 打开db
	db, err := global.InitSqliteByName(global.CollectInfoDbName)
	defer global.CloseSqlite(db)
	if err != nil {
		global.Logger.Sugar().Errorf("doSendCollectedInfo open db err:%v", err)
		return err
	}

	// 查询进程信息
	processInfoRows, err := db.Query(processQuerySql)
	if processInfoRows != nil {
		defer processInfoRows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorf("process info query err:%v", err)
		return err
	}

	// 查询应用软件信息
	softwareInfoRows, err := db.Query(softwareQuerySql)
	if softwareInfoRows != nil {
		defer softwareInfoRows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorf("software info query err:%v", err)
		return err
	}

	// 查询系统信息
	systemInfoRows, err := db.Query(systemQuerySql)
	if systemInfoRows != nil {
		defer systemInfoRows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorf("system info query err:%v", err)
		return err
	}

	// 构造grpc请求
	var infoCollectReq v1.InfoCollectReq
	infoCollectReq.AgentId = global.ApplianceID

	for processInfoRows.Next() {
		var processInfo v1.ProcessInfo
		err := processInfoRows.Scan(
			&processInfo.ProcessName, &processInfo.ProcPath, &processInfo.CmdLine,
			&processInfo.UserName, &processInfo.ProcessPid, &processInfo.ParentPid,
			&processInfo.MemUsedKb, &processInfo.VmmemUsedKb, &processInfo.ThreadNum,
			&processInfo.CpuKernelUsed, &processInfo.CpuUserUsed,
		)
		if err == nil {
			processInfo.ProcessName = handLongStr(processInfo.ProcessName, maxStrLength)
			processInfo.ProcPath = handLongStr(processInfo.ProcPath, maxStrLength)
			processInfo.CmdLine = handLongStr(processInfo.CmdLine, maxStrLength)
			processInfo.UserName = handLongStr(processInfo.UserName, maxStrLength)
			infoCollectReq.ProcessInfos = append(infoCollectReq.ProcessInfos, &processInfo)
		}
	}

	for softwareInfoRows.Next() {
		var softwareInfo v1.SoftwareInfo
		err := softwareInfoRows.Scan(
			&softwareInfo.SoftwareName, &softwareInfo.SoftwareVersion, &softwareInfo.SoftwareCompany,
			&softwareInfo.UninstallCmd, &softwareInfo.InstallPath,
		)
		if err == nil {
			softwareInfo.SoftwareName = handLongStr(softwareInfo.SoftwareName, maxStrLength)
			softwareInfo.SoftwareVersion = handLongStr(softwareInfo.SoftwareVersion, maxStrLength)
			softwareInfo.SoftwareCompany = handLongStr(softwareInfo.SoftwareCompany, maxStrLength)
			softwareInfo.UninstallCmd = handLongStr(softwareInfo.UninstallCmd, maxStrLength)
			softwareInfo.InstallPath = handLongStr(softwareInfo.InstallPath, maxStrLength)
			infoCollectReq.SoftwareInfos = append(infoCollectReq.SoftwareInfos, &softwareInfo)
		}
	}

	for systemInfoRows.Next() {
		var systemInfo v1.SystemInfo
		err := systemInfoRows.Scan(
			&systemInfo.SystemBit, &systemInfo.ProductName, &systemInfo.SystemLanguage,
			&systemInfo.SystemVersion,
		)
		if err == nil {
			systemInfo.ProductName = handLongStr(systemInfo.ProductName, maxStrLength)
			systemInfo.SystemLanguage = handLongStr(systemInfo.SystemLanguage, maxStrLength)
			systemInfo.SystemVersion = handLongStr(systemInfo.SystemVersion, maxStrLength)
			infoCollectReq.SystemInfos = append(infoCollectReq.SystemInfos, &systemInfo)
		}
	}

	// 发起上报请求
	client := v1.NewReportCollectedInfoClient(conn)
	_, replyErr := client.CollectedInfo(ctx, &infoCollectReq)
	if replyErr != nil {
		global.Logger.Sugar().Warnf("CollectedInfo Report err: %v", replyErr.Error())
		return replyErr
	}
	return nil
}

func handLongStr(src string, retain int) string {
	if len(src) <= retain {
		return src
	}
	cutNum := retain / 2
	head := src[:cutNum]
	tail := src[len(src)-cutNum:]
	return head + "..." + tail
}
