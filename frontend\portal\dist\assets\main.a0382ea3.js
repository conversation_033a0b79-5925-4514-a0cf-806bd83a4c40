/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import s from"./index.63b468e5.js";import{_ as a,h as e,a as c,b as n,d as o,j as p}from"./index.a794166c.js";const t={class:"access-main"},r={class:"content-wrapper"},d={class:"access-app"};const m=a({name:"BowserAccess",components:{AppPage:s},data:()=>({}),async mounted(){},methods:{}},[["render",function(s,a,m,i,l,f){const u=e("AppPage");return c(),n("div",t,[o("ul",r,[o("li",d,[p(u,{class:"access-app-page"})])])])}],["__scopeId","data-v-550f2b74"]]);export{m as default};
