package api

import (
	dashboardCommon "asdsec.com/asec/platform/app/console/app/dashboard/common"
	"asdsec.com/asec/platform/app/console/app/dashboard/service"
	incidentsCommon "asdsec.com/asec/platform/app/console/app/incidents/common"
	incidentsService "asdsec.com/asec/platform/app/console/app/incidents/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)
import agentService "asdsec.com/asec/platform/app/console/app/appliancemgt/service"
import appService "asdsec.com/asec/platform/app/console/app/application/service"
import accessService "asdsec.com/asec/platform/app/console/app/access/service"
import agentdto "asdsec.com/asec/platform/app/console/app/appliancemgt/dto"
import appDto "asdsec.com/asec/platform/app/console/app/application/dto"
import accessDto "asdsec.com/asec/platform/app/console/app/access/dto"

const defaultCountDays = 30
const defaultTop = 10

type DashboardCount struct {
	AgentCount       []agentdto.DeviceCount
	ApplicationCount appDto.ApplicationCount
	AccessCount      accessDto.AccessCount
}

// GetCount godoc
// @Summary 查询首页聚合统计
// @Schemes
// @Description 查询首页聚合统计
// @Tags        Dashboard
// @Param       days query uint64 true "统计天数"
// @Produce     application/json
// @Success     200
// @Router      /v1/dashboard/counts [GET]
// @success     200 {object} common.Response{data=DashboardCount} "聚合count"
func GetCount(c *gin.Context) {

	var days uint64
	daysStr := c.Query("days")
	days, err := strconv.ParseUint(daysStr, 10, 64)
	if err != nil {
		days = defaultCountDays
	}
	var dashCount DashboardCount
	agentCount, err := agentService.GetAppService().DeviceCount(c)
	//首页这里聚合统计查询失败接口不报错,打印错误日志即可,保障首页的体验
	if err != nil {
		global.SysLog.Error("query count err ", zap.Error(err))
	}
	appCount, err := appService.GetApplicationService().Count(c)
	if err != nil {
		global.SysLog.Error("query count err ", zap.Error(err))
	}
	accessCount, err := accessService.GetAccessLogService().AccessCount(c, int(days))
	if err != nil {
		global.SysLog.Error("query count err ", zap.Error(err))
	}
	dashCount.AgentCount = agentCount
	dashCount.ApplicationCount = appCount
	dashCount.AccessCount = accessCount
	common.OkWithData(c, dashCount)
}

// AccessLogTopN godoc
// @Summary 查询首页聚合统计
// @Schemes
// @Description 查询首页聚合统计
// @Tags        Dashboard
// @Param       top  query uint64 false "统计top个数"
// @Param       days query uint64 false "统计天数"
// @Produce     application/json
// @Success     200
// @Router      /v1/dashboard/access_log/trend [GET]
// @success     200 {object} common.Response{data=DashboardCount} "聚合count"
func AccessLogTopN(c *gin.Context) {
	var top uint64
	topStr := c.Query("top")
	top, err := strconv.ParseUint(topStr, 10, 64)
	if err != nil {
		top = defaultTop
	}

	var days uint64
	daysStr := c.Query("days")
	days, err = strconv.ParseUint(daysStr, 10, 64)
	if err != nil {
		days = defaultCountDays
	}
	data, err := accessService.GetAccessLogService().AccessTopN(c, int(top), int(days))
	if err != nil {
		global.SysLog.Error("query AccessLogTopN err ", zap.Error(err))
	}
	common.OkWithData(c, data)
}

// IncidentTypeTopN godoc
// @Summary 事件类型排行
// @Schemes
// @Description 事件类型排行
// @Tags        Dashboard
// @Param       req body incidentsCommon.QueryTimeParam true "事件类型排行"
// @Produce     application/json
// @Success     200
// @Router      /v1/dashboard/event_type [GET]
// @success     200 {object} common.Response{data=[]incidentsCommon.IncidentTypeTopItem}
func IncidentTypeTopN(ctx *gin.Context) {
	var request incidentsCommon.QueryTimeParam
	if err := ctx.ShouldBind(&request); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := incidentsService.GetIncidentService().QueryIncidentTypeTop(ctx, request)
	if err != nil {
		global.SysLog.Error("query Incident top error", zap.Error(err))
		common.Fail(ctx, common.IncidentTypeTopNErr)
		return
	}
	common.OkWithData(ctx, resp)
}

// SendTopN godoc
// @Summary 数据外发TOP
// @Schemes
// @Description 数据外发TOP
// @Tags        Dashboard
// @Param       req body dashboardCommon.QueryTimeParam true "数据外发TOP"
// @Produce     application/json
// @Success     200
// @Router      /v1/dashboard/outgoing [GET]
// @success     200 {object} common.Response{data=[]dashboardCommon.SendTopItem}
func SendTopN(ctx *gin.Context) {
	var request dashboardCommon.QueryTimeParam
	if err := ctx.ShouldBind(&request); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetFileEventsService().QuerySendTop(ctx, request)
	if err != nil {
		global.SysLog.Error("query Incident top error", zap.Error(err))
		common.Fail(ctx, common.SendTopNErr)
		return
	}
	common.OkWithData(ctx, resp)
}

// SensitiveSendTopN godoc
// @Summary 敏感数据外发TOP
// @Schemes
// @Description 敏感数据外发TOP
// @Tags        Dashboard
// @Param       req body dashboardCommon.QueryTimeParam true "敏感数据外发TOP"
// @Produce     application/json
// @Success     200
// @Router      /v1/dashboard/sensitive_outgoing [GET]
// @success     200 {object} common.Response{data=[]dashboardCommon.SensitiveSendTopItem}
func SensitiveSendTopN(ctx *gin.Context) {
	var request dashboardCommon.QueryTimeParam
	if err := ctx.ShouldBind(&request); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetFileEventsService().QuerySensitiveSendTop(ctx, request)
	if err != nil {
		global.SysLog.Error("query Incident top error", zap.Error(err))
		common.Fail(ctx, common.SendTopNErr)
		return
	}
	common.OkWithData(ctx, resp)
}

// DataAccessLogTopN godoc
// @Summary 查询首页数据访问聚合统计
// @Schemes
// @Description 查询首页数据访问聚合统计
// @Tags        Dashboard
// @Param       top  query uint64 false "统计top个数"
// @Param       days query uint64 false "统计天数"
// @Produce     application/json
// @Success     200
// @Router      /v1/dashboard/access_log/data_trend [GET]
// @success     200 {object} common.Response{data=DashboardCount} "聚合count"
func DataAccessLogTopN(ctx *gin.Context) {
	var request dashboardCommon.QueryTimeParam
	if err := ctx.ShouldBind(&request); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	resp, err := service.GetFileEventsService().QueryDataAccessLogTop(ctx, request)
	if err != nil {
		global.SysLog.Error("query Incident top error", zap.Error(err))
		common.Fail(ctx, common.SendTopNErr)
		return
	}
	common.OkWithData(ctx, resp)
}
