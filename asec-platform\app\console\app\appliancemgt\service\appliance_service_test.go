package service

import (
	"testing"

	"asdsec.com/asec/platform/app/console/app/appliancemgt/common"

	moduleDto "asdsec.com/asec/platform/app/console/app/module_switch/dto"
)

// console项目的init函数会导致没法直接执行单侧，需要注释掉

func TestTraverseNormal(t *testing.T) {
	// 构造测试数据
	agent1 := moduleDto.GroupAndAgents{
		Id:       "1",
		IsDir:    false,
		Children: nil,
	}
	agent2 := moduleDto.GroupAndAgents{
		Id:       "2",
		IsDir:    false,
		Children: nil,
	}
	agent3 := moduleDto.GroupAndAgents{
		Id:       "3",
		IsDir:    false,
		Children: nil,
	}
	dirGroup := moduleDto.GroupAndAgents{
		Id:       "dir1",
		IsDir:    true,
		Children: []moduleDto.GroupAndAgents{agent2, agent3},
	}
	rootGroup := moduleDto.GroupAndAgents{
		Id:       "root",
		IsDir:    true,
		Children: []moduleDto.GroupAndAgents{agent1, dirGroup},
	}

	// 执行函数
	result, err := traverseAgentsFromModuleSwitchGroup([]moduleDto.GroupAndAgents{rootGroup})

	// 验证结果
	if err != nil {
		t.Errorf("Error occurred: %v", err)
	}

	expected := []int64{1, 2, 3}
	if len(result) != len(expected) {
		t.Errorf("Expected %d elements, but got %d", len(expected), len(result))
	}

	for i := range expected {
		if result[i] != expected[i] {
			t.Errorf("Expected element %d to be %d, but got %d", i, expected[i], result[i])
		}
	}
}

func TestNoAgentsGroup(t *testing.T) {
	// 构造测试数据
	dirGroup1 := moduleDto.GroupAndAgents{
		Id:       "dir1",
		IsDir:    true,
		Children: []moduleDto.GroupAndAgents{},
	}
	dirGroup2 := moduleDto.GroupAndAgents{
		Id:       "dir2",
		IsDir:    true,
		Children: []moduleDto.GroupAndAgents{},
	}
	rootGroup := moduleDto.GroupAndAgents{
		Id:       "root",
		IsDir:    true,
		Children: []moduleDto.GroupAndAgents{dirGroup1, dirGroup2},
	}

	// 执行函数
	result, err := traverseAgentsFromModuleSwitchGroup([]moduleDto.GroupAndAgents{rootGroup})

	// 验证结果
	if err != nil {
		t.Errorf("Error occurred: %v", err)
	}

	var expected []int64
	if len(result) != len(expected) {
		t.Errorf("Expected %d elements, but got %d", len(expected), len(result))
	}

}

func TestGetModuleSwitchGroupByID(t *testing.T) {
	// 构造测试数据
	group1 := moduleDto.GroupAndAgents{
		Id:       "group1",
		IsDir:    true,
		Children: nil,
	}
	group2 := moduleDto.GroupAndAgents{
		Id:       "group2",
		IsDir:    true,
		Children: nil,
	}
	group3 := moduleDto.GroupAndAgents{
		Id:       "group3",
		IsDir:    true,
		Children: nil,
	}
	rootGroup := moduleDto.GroupAndAgents{
		Id:       "root",
		IsDir:    true,
		Children: []moduleDto.GroupAndAgents{group1, group2, group3},
	}

	t.Run("ExistingGroup", func(t *testing.T) {
		// 存在目标组
		result, err := getModuleSwitchGroupByID([]moduleDto.GroupAndAgents{rootGroup}, "group2")
		if err != nil {
			t.Errorf("Error occurred: %v", err)
		}
		expected := group2
		if result.Id != expected.Id {
			t.Errorf("Expected group %s, but got %s", expected.Id, result.Id)
		}
	})

	t.Run("NonExistingGroup", func(t *testing.T) {
		// 不存在目标组
		_, err := getModuleSwitchGroupByID([]moduleDto.GroupAndAgents{rootGroup}, "nonexistent")
		if err != common.ErrRecordNotFound {
			t.Errorf("Expected error ErrRecordNotFound, but got %v", err)
		}
	})

	t.Run("EmptyInput", func(t *testing.T) {
		// 空输入列表
		_, err := getModuleSwitchGroupByID([]moduleDto.GroupAndAgents{}, "group1")
		if err != common.ErrRecordNotFound {
			t.Errorf("Expected error ErrRecordNotFound, but got %v", err)
		}
	})
}
