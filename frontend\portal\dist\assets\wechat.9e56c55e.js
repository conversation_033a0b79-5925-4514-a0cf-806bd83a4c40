/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import{_ as e,f as t,r as o,o as a,P as r,v as n,a as d,b as i,d as s,Q as c}from"./index.a794166c.js";import{u as l}from"./secondaryAuth.7effdc08.js";const g=e(Object.assign({name:"Wechat"},{props:{authInfo:{type:Array,default:function(){return[]}},authId:{type:String,default:function(){return""}}},setup(e){const g=t(),{handleSecondaryAuthResponse:u}=l(),w=o(0),p=o("https://open.work.weixin.qq.com/wwopen/sso/qrConnect"),h=o(""),m=e,f=()=>{logger.log("开始绘制企业微信二维码"),h.value=(new Date).getTime();const e=m.authInfo.wxCorpId,t=m.authInfo.wxAgentId;if(!e||!t)return console.error("企业微信配置缺失:",{appid:e,agentid:t}),void I("企业微信配置缺失");const o={uid:v(),time:h.value,state:m.authId},a=`${c()}/?${new URLSearchParams(o).toString()}`;logger.log("企业微信认证参数:",{appid:e,agentid:t,callbackUrl:a,time:h.value});const r=document.getElementById("wework_qrcode_login");r&&(r.innerHTML=""),x({id:"wework_qrcode_login",appid:e,agentid:t,redirect_uri:encodeURIComponent(a),state:m.authId,href:"",urlid:"wework"}),void 0!==window.addEventListener?window.addEventListener("message",y,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",y)},v=()=>"wechat_"+Date.now(),y=async e=>{logger.log("收到企业微信消息:",e);if(e.origin.indexOf("work.weixin.qq.com")>-1)if(logger.log("企业微信消息数据:",e.data),"string"==typeof e.data&&e.data.includes("code="))try{const t=new URL(e.data),o=t.searchParams.get("code"),a=t.searchParams.get("state");logger.log("解析到的参数:",{code:o,state:a}),o&&a?(logger.log("企业微信认证成功，调用handleAuthSuccess"),await _(o,a)):(console.error("未能解析到有效的code或state参数"),I("认证参数解析失败"))}catch(t){console.error("解析企业微信URL失败:",t),I("认证数据解析失败")}else console.error("解析企业微信URL失败");else e.data&&"wechat_auth_success"===e.data.type?(logger.log("收到iframe认证成功消息:",e.data),await _(e.data.code,e.data.state)):e.data&&"wechat_auth_error"===e.data.type&&(console.error("收到iframe认证失败消息:",e.data.error),I("认证失败: "+e.data.error))},_=async(e,t)=>{try{logger.log("处理企业微信认证成功:",{code:e,state:t});const o={clientId:"client_portal",grantType:"implicit",redirect_uri:`${c()}/`,idpId:Array.isArray(t)?t[0]:t,authWeb:{authWebCode:Array.isArray(e)?e[0]:e}};logger.log("调用登录接口，参数:",o);const a=await g.LoginIn(o,"qiyewx",m.authId);if(a&&-1!==a.code){if(await u(a))return void logger.log("企业微信登录成功，进入双因子验证");logger.log("企业微信登录成功")}else console.error("企业微信登录失败"),I("登录失败，请重试"),f()}catch(o){console.error("企业微信认证处理失败:",o),I("认证处理失败: "+o.message),f()}},x=e=>{logger.log("创建企业微信登录iframe:",e);const t=document.createElement("iframe"),o=(new Date).getTime();let a=p.value+"?appid="+e.appid+"&agentid="+e.agentid+"&redirect_uri="+e.redirect_uri+"&state="+e.state+"&login_type=jssdk&t="+o;a+=e.style?"&style="+e.style:"",a+=e.href?"&href="+e.href:"",t.src=a,t.frameBorder="0",t.allowTransparency="true",t.scrolling="no",t.width="300px",t.height="400px";const r=document.getElementById(e.id);r&&(r.innerHTML="",r.appendChild(t),t.onload=function(){t.contentWindow&&t.contentWindow.postMessage&&t.contentWindow.postMessage("ask_usePostMessage","*")}),logger.log("企业微信iframe创建完成:",a)},I=e=>{const t=document.getElementById("wework_qrcode_login");t&&(t.innerHTML=`\n      <div style="text-align: center; padding: 20px; color: #f56c6c;">\n        <div style="margin-bottom: 10px;">企业微信认证失败</div>\n        <div style="font-size: 12px; color: #909399;">${e}</div>\n        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">\n          重试\n        </button>\n      </div>\n    `)};return a((()=>{logger.log("企业微信认证组件挂载"),f()})),r((()=>{logger.log("企业微信认证组件卸载"),void 0!==window.addEventListener?window.removeEventListener("message",y,!1):void 0!==window.attachEvent&&window.detachEvent("onmessage",y)})),n(m,(()=>{logger.log("企业微信认证props变化，重新绘制二维码"),w.value++,f()})),(e,t)=>(d(),i("div",{key:w.value},t[0]||(t[0]=[s("div",{id:"wework_qrcode_login",class:"wechat-class"},null,-1)])))}}),[["__scopeId","data-v-0182e4a7"]]);export{g as default};
