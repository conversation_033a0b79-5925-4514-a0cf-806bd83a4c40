/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function u(e,a,o,i){var u=a&&a.prototype instanceof l?a:l,s=Object.create(u.prototype);return t(s,"_invoke",function(e,t,a){var o,i,u,l=0,s=a||[],f=!1,p={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(e,t){return o=e,i=0,u=n,p.n=t,c}};function d(e,t){for(i=e,u=t,r=0;!f&&l&&!a&&r<s.length;r++){var a,o=s[r],d=p.p,y=o[2];e>3?(a=y===t)&&(u=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=n):o[0]<=d&&((a=e<2&&d<o[1])?(i=0,p.v=t,p.n=o[1]):d<y&&(a=e<3||o[0]>t||t>y)&&(o[4]=e,o[5]=t,p.n=y,i=0))}if(a||e>1)return c;throw f=!0,t}return function(a,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),i=s,u=y;(r=i<2?n:u)||!f;){o||(i?i<3?(i>1&&(p.n=-1),d(i,u)):p.n=u:p.v=u);try{if(l=2,o){if(i||(a="next"),r=o[a]){if(!(r=r.call(o,u)))throw TypeError("iterator result is not an object");if(!r.done)return r;u=r.value,i<2&&(i=0)}else 1===i&&(r=o.return)&&r.call(o),i<2&&(u=TypeError("The iterator does not provide a '"+a+"' method"),i=1);o=n}else if((r=(f=p.n<0)?u:e.call(t,p))!==c)break}catch(r){o=n,i=1,u=r}finally{l=1}}return{value:r,done:f}}}(e,o,i),!0),s}var c={};function l(){}function s(){}function f(){}r=Object.getPrototypeOf;var p=[][o]?r(r([][o]())):(t(r={},o,(function(){return this})),r),d=f.prototype=l.prototype=Object.create(p);function y(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,t(e,i,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=f,t(d,"constructor",f),t(f,"constructor",s),s.displayName="GeneratorFunction",t(f,i,"GeneratorFunction"),t(d),t(d,i,"Generator"),t(d,o,(function(){return this})),t(d,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:u,m:y}})()}function t(e,n,r,a){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,n,r,a){if(n)o?o(e,n,{value:r,enumerable:!a,configurable:!a,writable:!a}):e[n]=r;else{var i=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,n,r,a)}function n(e,t,n,r,a,o,i){try{var u=e[o](i),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,a)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var i=e.apply(t,r);function u(e){n(i,a,o,u,c,"next",e)}function c(e){n(i,a,o,u,c,"throw",e)}u(void 0)}))}}System.register(["./index-legacy.b871e767.js"],(function(t,n){"use strict";var a,o,i,u,c,l,s,f,p,d,y,v,h,m,g,_,b,x,k,I,C=document.createElement("style");return C.textContent='@charset "UTF-8";.verify-code .title[data-v-1c306988]{height:60px;font-size:24px;text-align:center}.verify-code .message-text[data-v-1c306988]{margin-bottom:40px;text-align:center;line-height:1.5}\n',document.head.appendChild(C),{setters:[function(e){a=e.r,o=e.a2,i=e.c,u=e.f,c=e.h,l=e.a,s=e.b,f=e.d,p=e.t,d=e.n,y=e.y,v=e.j,h=e.w,m=e.i,g=e.a3,_=e.l,b=e.k,x=e._,k=e.p,I=e.M}],execute:function(){var n={class:"verify-code"},C={class:"title"},T={key:0},w={key:0,class:"message-text"},j={key:1,class:"message-text"},O={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},S={style:{"text-align":"center"}},P={key:1,style:{width:"450px"}},V={key:0},G=["src"],N={key:1},q={style:{"text-align":"center","margin-top":"30px"}},E=Object.assign({name:"VerifyCode"},{props:{authInfo:{type:Object,default:function(){return{}}},authId:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}},secondaryType:{type:String,default:"sms"}},emits:["verification-success","back","cancel"],setup:function(t,x){var E=x.emit,F=a(""),K=a(""),U=o("userName"),A=a(!1),z=t,B=function(){A.value=!0};console.log("verifyCode组件接收到的属性:",{secondaryType:z.secondaryType,authInfo:z.authInfo,canVerify:"email"===z.secondaryType?!1!==z.authInfo.hasEmail:!1!==z.authInfo.notPhone});var L,M=E,D=i((function(){return void 0!==z.authInfo.hasContactInfo?z.authInfo.hasContactInfo:(z.secondaryType,!1!==z.authInfo.hasContactInfo)})),H=a(60),J=function(){clearInterval(L)},Q=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:if(D.value){e.n=1;break}return e.a(2);case 1:return n={uniq_key:z.authInfo.uniqKey,idp_id:z.authId},e.p=2,e.n=3,k(n);case 3:200===(r=e.v).status&&-1!==r.data.code?(H.value=60,L=setInterval((function(){H.value--,0===H.value&&J()}),1e3)):(I({showClose:!0,message:r.data.msg,type:"error"}),H.value=0),e.n=5;break;case 4:e.p=4,e.v,I({showClose:!0,message:"发送验证码失败",type:"error"}),H.value=0;case 5:return e.a(2)}}),t,null,[[2,4]])})));return function(){return t.apply(this,arguments)}}();Q();var R=u(),W=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:return n={uniq_key:z.authInfo.uniqKey,auth_code:K.value,user_name:z.userName||U.value,idp_id:z.authId,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal",totp_key:F.value},e.n=1,R.LoginIn(n,"accessory");case 1:if(-1!==(r=e.v).code){e.n=2;break}return e.a(2);case 2:M("verification-success",r);case 3:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),X=function(){M("cancel")},Y=i((function(){switch(z.secondaryType){case"email":return"邮件认证";case"sms":return"短信认证";case"totp":return"令牌口令"}return"令牌口令"}));return function(e,r){var a=c("base-button"),o=c("base-input");return l(),s("div",n,[f("div",{style:d({top:"10px",textAlign:"center",marginBottom:"totp"===t.secondaryType?"0px":"25px"})},[f("span",C,p(Y.value),1)],4),"totp"!==t.secondaryType?(l(),s("div",T,[D.value?(l(),s("div",w,"验证码已发送至您账号("+p(t.userName||y(U))+")关联的"+p("email"===t.secondaryType?"邮箱":"手机")+"，请注意查收",1)):(l(),s("div",j,"您的账号("+p(t.userName||y(U))+")未关联"+p("email"===t.secondaryType?"邮箱":"手机号码")+"，请联系管理员！",1)),D.value?(l(),s("div",O,[v(o,{modelValue:K.value,"onUpdate:modelValue":r[0]||(r[0]=function(e){return K.value=e}),placeholder:"email"===t.secondaryType?"邮箱验证码":"短信验证码",class:"input-with-select",onKeyup:g(W,["enter"])},{append:h((function(){return[v(a,{disabled:H.value>0,type:"info",onClick:Q},{default:h((function(){return[m("重新发送 "+p(H.value>0?"(".concat(H.value,"秒)"):""),1)]})),_:1},8,["disabled"])]})),_:1},8,["modelValue","placeholder"])])):_("",!0),f("div",S,[D.value?(l(),b(a,{key:0,disabled:!K.value,style:{"margin-right":"10px"},type:"primary",onClick:W},{default:h((function(){return r[3]||(r[3]=[m("确 定 ")])})),_:1,__:[3]},8,["disabled"])):_("",!0),v(a,{type:"info",onClick:X},{default:h((function(){return r[4]||(r[4]=[m("取 消 ")])})),_:1,__:[4]})])])):_("",!0),"totp"===t.secondaryType?(l(),s("div",P,[A.value||!0===t.authInfo.CurrentSecret?_("",!0):(l(),s("div",V,[r[5]||(r[5]=f("p",null,"本系统要求使用令牌生成的口令完成双因子认证。您还没有激活令牌，可以使用第三方应用生成口令，如Google Authenticator、腾讯身份验证器等。",-1)),f("img",{src:"data:image/png;base64,"+t.authInfo.qrCode,alt:"TOTP口令",style:{display:"block",margin:"0 auto"}},null,8,G),f("p",{style:{"text-align":"center","margin-top":"10px"}},[f("a",{style:{cursor:"pointer",color:"#0d84ff"},onClick:B},"已扫描"),f("a",{style:{cursor:"pointer",color:"#0d84ff","margin-left":"20px"},onClick:X},"取消")])])),A.value||t.authInfo.CurrentSecret?(l(),s("div",N,[r[9]||(r[9]=f("p",{style:{"text-align":"center","margin-top":"20px","margin-bottom":"30px"}},"请输入口令",-1)),v(o,{modelValue:F.value,"onUpdate:modelValue":r[1]||(r[1]=function(e){return F.value=e}),class:"input-with-select",placeholder:"请输入令牌口令",onKeyup:g(W,["enter"])},null,8,["modelValue"]),f("div",q,[v(a,{style:{"margin-right":"10px"},type:"primary",onClick:W},{default:h((function(){return r[6]||(r[6]=[m("确 定 ")])})),_:1,__:[6]}),!0!==t.authInfo.CurrentSecret?(l(),b(a,{key:0,style:{"margin-right":"10px"},type:"info",onClick:r[2]||(r[2]=function(e){return A.value=!A.value})},{default:h((function(){return r[7]||(r[7]=[m("重 扫 ")])})),_:1,__:[7]})):_("",!0),v(a,{type:"info",onClick:X},{default:h((function(){return r[8]||(r[8]=[m("取 消 ")])})),_:1,__:[8]})])])):_("",!0)])):_("",!0)])}}});t("default",x(E,[["__scopeId","data-v-1c306988"]]))}}}))}();
