// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/conf"
	"asdsec.com/asec/platform/app/auth/internal/data"
	"asdsec.com/asec/platform/app/auth/internal/job"
	"asdsec.com/asec/platform/app/auth/internal/server"
	"asdsec.com/asec/platform/app/auth/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	authRepo := data.NewAuthRepo(dataData, logger)
	idpRepo := data.NewIdpRepo(dataData, logger)
	userRepo := data.NewUserRepo(dataData, logger)
	userGroupRepo := data.NewUserGroupRepo(dataData, logger)
	userSourceRepo := data.NewUserSourceRepo(dataData, logger)
	corpRepo := data.NewCorpRepo(dataData, logger)
	authPolicyRepo := data.NewAuthPolicyRepo(dataData, logger)
	commonRepo := data.NewCommonRepo(dataData, logger)
	authAccountPolicyRepo := data.NewAuthAccountPolicyRepo(dataData, logger)
	jwtBlacklistRepo := data.NewJWTBlacklistRepo(dataData, logger)
	sessionTrackerRepo := data.NewSessionTrackerRepo(dataData, logger)
	clientLimitUsecase := biz.NewClientLimitUsecase(sessionTrackerRepo, jwtBlacklistRepo, authPolicyRepo, userRepo, userGroupRepo, logger)
	authUsecase := biz.NewAuthUsecase(authRepo, idpRepo, userRepo, userGroupRepo, userSourceRepo, corpRepo, authPolicyRepo, commonRepo, authAccountPolicyRepo, jwtBlacklistRepo, sessionTrackerRepo, clientLimitUsecase, logger)
	idpUsecase := biz.NewIdpUsecase(idpRepo, userSourceRepo, userGroupRepo, authPolicyRepo, logger)
	authService := service.NewAuthService(authUsecase, idpUsecase, logger)
	roleRepo := data.NewRoleRepo(dataData, logger)
	userUsecase := biz.NewUserUsecase(userRepo, userGroupRepo, roleRepo, userSourceRepo, logger)
	userService := service.NewUserService(authUsecase, userUsecase, logger)
	corpUsecase := biz.NewCorpUsecase(corpRepo, logger)
	authPolicyUsecase := biz.NewAuthPolicyUsecase(authPolicyRepo, userRepo, userGroupRepo, sessionTrackerRepo, jwtBlacklistRepo, logger)
	authAccountPolicyUsecase := biz.NewAuthAccountPolicyUsecase(authAccountPolicyRepo, logger)
	userGroupUsecase := biz.NewUserGroupUsecase(userGroupRepo, idpRepo, userSourceRepo, authPolicyRepo, userRepo, logger)
	userSourceUsecase := biz.NewUserSourceUsecase(userSourceRepo, logger)
	roleUsecase := biz.NewRoleUsecase(roleRepo, logger)
	adminService := service.NewAdminService(corpUsecase, authPolicyUsecase, authAccountPolicyUsecase, idpUsecase, userUsecase, userGroupUsecase, userSourceUsecase, roleUsecase, authUsecase, logger)
	oidcRepo := data.NewOIDCRepo(dataData, logger)
	appRepo := data.NewAppRepo(dataData, logger)
	oidcUsecase := biz.NewOIDCUsecase(oidcRepo, userRepo, appRepo, logger)
	oidcService := service.NewOIDCService(oidcUsecase, authUsecase, logger)
	httpServer := server.NewHTTPServer(confServer, authService, userService, adminService, oidcService, logger)
	cron := job.RootGroupAutoSync(confServer, userUsecase)
	app := newApp(logger, httpServer, cron)
	return app, func() {
		cleanup()
	}, nil
}
