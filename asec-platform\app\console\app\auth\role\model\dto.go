package model

import "time"

type Role struct {
	Id       string    `gorm:"column:id" json:"id"`
	RoleName string    `gorm:"column:role_name" json:"role_name"`
	CorpId   string    `gorm:"column:corp_id" json:"corp_id"`
	Desc     string    `gorm:"phone" json:"phone"`
	RoleType string    `gorm:"role_type" json:"role_type"`
	Status   int       `gorm:"status" json:"status"`
	CreateAt time.Time `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"create_at"`
	UpdateAt time.Time `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"update_at"`
}

func (Role) TableName() string {
	return "tb_admin_role"
}

type RolePolicy struct {
	RoleId    string    `gorm:"column:role_id" json:"role_id"`
	PolicyUrl string    `gorm:"column:policy_url" json:"policy_url"`
	PolicyOpt string    `gorm:"column:policy_opt" json:"policy_opt"`
	CorpId    string    `gorm:"column:corp_id" json:"corp_id"`
	Desc      string    `gorm:"desc" json:"desc"`
	CreateAt  time.Time `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"create_at"`
	UpdateAt  time.Time `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"update_at"`
}

func (RolePolicy) TableName() string {
	return "tb_role_policy"
}
