package service

import (
	"asdsec.com/asec/platform/app/console/app/agent_task/dto"
	"asdsec.com/asec/platform/app/console/app/agent_task/repository"
	appliancedto "asdsec.com/asec/platform/app/console/app/appliancemgt/dto"
	"asdsec.com/asec/platform/pkg/model/agent_model"
	"context"
	"sync"
)

var AgentTaskServiceImpl AgentTaskService

var AgentTaskServiceInit sync.Once

type agentTaskService struct {
	db repository.AgentTaskRepository
}

func (a agentTaskService) UpdateAgentTaskStatus(ctx context.Context, req appliancedto.SatusReq) error {
	return a.db.UpdateAgentTaskStatus(ctx, req)
}

func (a agentTaskService) GetAgentTaskList(ctx context.Context, agentId string, status string) ([]agent_model.AgentEscapeTask, error) {
	return a.db.GetAgentTaskList(ctx, agentId, status)
}

func (a agentTaskService) CreateAgentTask(ctx context.Context, req dto.CreateAgentTaskReq) error {
	return a.db.CreateAgentTask(ctx, req)
}

func (a agentTaskService) GetAgentTaskDetail(ctx context.Context, id string) (agent_model.AgentEscapeTask, error) {
	return a.db.GetAgentTaskDetail(ctx, id)
}

type AgentTaskService interface {
	CreateAgentTask(ctx context.Context, req dto.CreateAgentTaskReq) error
	GetAgentTaskDetail(ctx context.Context, id string) (agent_model.AgentEscapeTask, error)
	GetAgentTaskList(ctx context.Context, agentId string, status string) ([]agent_model.AgentEscapeTask, error)
	UpdateAgentTaskStatus(ctx context.Context, req appliancedto.SatusReq) error
}

func GetAgentTaskService() AgentTaskService {
	AgentTaskServiceInit.Do(func() {
		AgentTaskServiceImpl = &agentTaskService{db: repository.NewAgentTaskRepository()}
	})
	return AgentTaskServiceImpl
}
