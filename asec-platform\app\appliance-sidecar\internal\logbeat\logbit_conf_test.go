package logbeat

import (
	"fmt"
	"os"
	"testing"
	"text/template"
)

func Test_gen(t *testing.T) {
	// 定义示例配置
	config := FluentBitConfig{
		Flush:       1,
		LogLevel:    "info",
		ParsersFile: "parsers.conf",
		PluginsFile: "plugins.conf",
		Inputs: []InputConfig{
			{
				Path:   "/var/log/nginx/access.log",
				Parser: "nginx",
				Tag:    "nginx.access",
			},
			{
				Path:   "/var/log/nginx/error.log",
				Parser: "nginx",
				Tag:    "nginx.error",
			},
		},
		Outputs: []OutputConfig{
			{
				Host:  "***********",
				Port:  3100,
				Match: "nginx.*",
				Labels: struct {
					Job    string
					Stream string
					Extra  map[string]string
				}{
					Job:    "nginx",
					Stream: "${hostname}",
					Extra:  map[string]string{"env": "prod"},
				},
			},
		},
	}

	// 创建模板并解析配置文件
	tpl, err := template.New("fluentbit").Parse(fluentBitTemplate)
	if err != nil {
		fmt.Printf("Failed to parse template: %v\n", err)
		os.Exit(1)
	}
	err = tpl.Execute(os.Stdout, config)
	if err != nil {
		fmt.Printf("Failed to execute template: %v\n", err)
		os.Exit(1)
	}
}
