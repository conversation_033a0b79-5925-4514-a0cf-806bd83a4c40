syntax = "proto3";
import public "google/protobuf/any.proto";

package api.conf;
option go_package = "asdsec.com/asec/platform/api/conf/v1;v1";

// 内置敏感元素和自定义敏感元素 共享该pb定义,以不同配置类型区分
// 敏感元素配置
message SensitiveElement{
  // 敏感元素id
  uint32 id = 1;
  // 敏感元素类型(1关键词 Value;2正则 Value ;3算法 AlgorithmValue;4字典 DictWordValue)
  uint32 type = 2;
  // 关键词,正则类型配置
  Value kr_value = 3;
  // 算法敏感
  AlgorithmValue algorithm_value = 4;
  // 字典敏感数据
  DictWordValue dict_word_value = 5;
}

// 算法类型值
message AlgorithmValue{
  // 匹配阈值
  uint32 threshold = 1;
  // 值类型(用于算法类型敏感:1关键词;2正则)
  uint32 value_type = 2;
  // 敏感元素值
  repeated string value = 3;
}

// 字典类型配置 本期暂不实现
message DictWordValue{
  repeated DictWord dict_word = 1;
  // 匹配阈值
  uint32 threshold = 2;
}

message DictWord{
  // 字典值
  string dict_word = 1;
  // 权重
  uint32 weight = 2;
}

// 关键词,正则类型配置
message Value{
  // 字典值
  repeated string value = 1;
}
