// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbAuthAccountPolicy(db *gorm.DB, opts ...gen.DOOption) tbAuthAccountPolicy {
	_tbAuthAccountPolicy := tbAuthAccountPolicy{}

	_tbAuthAccountPolicy.tbAuthAccountPolicyDo.UseDB(db, opts...)
	_tbAuthAccountPolicy.tbAuthAccountPolicyDo.UseModel(&model.TbAuthAccountPolicy{})

	tableName := _tbAuthAccountPolicy.tbAuthAccountPolicyDo.TableName()
	_tbAuthAccountPolicy.ALL = field.NewAsterisk(tableName)
	_tbAuthAccountPolicy.ID = field.NewString(tableName, "id")
	_tbAuthAccountPolicy.CorpID = field.NewString(tableName, "corp_id")
	_tbAuthAccountPolicy.Name = field.NewString(tableName, "name")
	_tbAuthAccountPolicy.Description = field.NewString(tableName, "description")
	_tbAuthAccountPolicy.PasswordMinLength = field.NewInt32(tableName, "password_min_length")
	_tbAuthAccountPolicy.PasswordMaxLength = field.NewInt32(tableName, "password_max_length")
	_tbAuthAccountPolicy.PasswordValidityDays = field.NewInt32(tableName, "password_validity_days")
	_tbAuthAccountPolicy.PasswordHistoryCount = field.NewInt32(tableName, "password_history_count")
	_tbAuthAccountPolicy.PasswordComplexityUppercase = field.NewBool(tableName, "password_complexity_uppercase")
	_tbAuthAccountPolicy.PasswordComplexityLowercase = field.NewBool(tableName, "password_complexity_lowercase")
	_tbAuthAccountPolicy.PasswordComplexityNumbers = field.NewBool(tableName, "password_complexity_numbers")
	_tbAuthAccountPolicy.PasswordComplexitySpecialChars = field.NewBool(tableName, "password_complexity_special_chars")
	_tbAuthAccountPolicy.PasswordComplexityNoUsername = field.NewBool(tableName, "password_complexity_no_username")
	_tbAuthAccountPolicy.PasswordFailureEnabled = field.NewBool(tableName, "password_failure_enabled")
	_tbAuthAccountPolicy.PasswordMaxFailureCount = field.NewInt32(tableName, "password_max_failure_count")
	_tbAuthAccountPolicy.PasswordLockoutDurationSec = field.NewInt32(tableName, "password_lockout_duration_sec")
	_tbAuthAccountPolicy.PasswordResetFailureWindowSec = field.NewInt32(tableName, "password_reset_failure_window_sec")
	_tbAuthAccountPolicy.IPFailureEnabled = field.NewBool(tableName, "ip_failure_enabled")
	_tbAuthAccountPolicy.IPMaxFailureCount = field.NewInt32(tableName, "ip_max_failure_count")
	_tbAuthAccountPolicy.IPLockoutDurationSec = field.NewInt32(tableName, "ip_lockout_duration_sec")
	_tbAuthAccountPolicy.IPResetFailureWindowSec = field.NewInt32(tableName, "ip_reset_failure_window_sec")
	_tbAuthAccountPolicy.Enable = field.NewBool(tableName, "enable")
	_tbAuthAccountPolicy.IsDefault = field.NewBool(tableName, "is_default")
	_tbAuthAccountPolicy.Priority = field.NewInt32(tableName, "priority")
	_tbAuthAccountPolicy.CreatedAt = field.NewTime(tableName, "created_at")
	_tbAuthAccountPolicy.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbAuthAccountPolicy.fillFieldMap()

	return _tbAuthAccountPolicy
}

type tbAuthAccountPolicy struct {
	tbAuthAccountPolicyDo tbAuthAccountPolicyDo

	ALL                            field.Asterisk
	ID                             field.String // 策略ID
	CorpID                         field.String // 租户ID
	Name                           field.String // 策略名称
	Description                    field.String // 策略描述
	PasswordMinLength              field.Int32  // 密码最小位数
	PasswordMaxLength              field.Int32  // 密码最大位数
	PasswordValidityDays           field.Int32  // 密码有效期（天）
	PasswordHistoryCount           field.Int32  // 密码历史个数
	PasswordComplexityUppercase    field.Bool   // 密码复杂度-大写字母
	PasswordComplexityLowercase    field.Bool   // 密码复杂度-小写字母
	PasswordComplexityNumbers      field.Bool   // 密码复杂度-数字
	PasswordComplexitySpecialChars field.Bool   // 密码复杂度-特殊字符
	PasswordComplexityNoUsername   field.Bool   // 密码复杂度-不包含账户名
	PasswordFailureEnabled         field.Bool   // 是否启用密码错误次数限制
	PasswordMaxFailureCount        field.Int32  // 连续密码错误最大次数
	PasswordLockoutDurationSec     field.Int32  // 锁定时长（秒）
	PasswordResetFailureWindowSec  field.Int32  // 密码错误次数重置窗口（秒）
	IPFailureEnabled               field.Bool   // 是否启用IP错误次数限制
	IPMaxFailureCount              field.Int32  // 同IP连续错误最大次数
	IPLockoutDurationSec           field.Int32  // IP锁定时长（秒）
	IPResetFailureWindowSec        field.Int32  // IP错误次数重置窗口（秒）
	Enable                         field.Bool   // 是否启用该策略
	IsDefault                      field.Bool   // 是否为默认策略
	Priority                       field.Int32  // 策略优先级，数值越大优先级越高
	CreatedAt                      field.Time   // 创建时间
	UpdatedAt                      field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (t tbAuthAccountPolicy) Table(newTableName string) *tbAuthAccountPolicy {
	t.tbAuthAccountPolicyDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbAuthAccountPolicy) As(alias string) *tbAuthAccountPolicy {
	t.tbAuthAccountPolicyDo.DO = *(t.tbAuthAccountPolicyDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbAuthAccountPolicy) updateTableName(table string) *tbAuthAccountPolicy {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.CorpID = field.NewString(table, "corp_id")
	t.Name = field.NewString(table, "name")
	t.Description = field.NewString(table, "description")
	t.PasswordMinLength = field.NewInt32(table, "password_min_length")
	t.PasswordMaxLength = field.NewInt32(table, "password_max_length")
	t.PasswordValidityDays = field.NewInt32(table, "password_validity_days")
	t.PasswordHistoryCount = field.NewInt32(table, "password_history_count")
	t.PasswordComplexityUppercase = field.NewBool(table, "password_complexity_uppercase")
	t.PasswordComplexityLowercase = field.NewBool(table, "password_complexity_lowercase")
	t.PasswordComplexityNumbers = field.NewBool(table, "password_complexity_numbers")
	t.PasswordComplexitySpecialChars = field.NewBool(table, "password_complexity_special_chars")
	t.PasswordComplexityNoUsername = field.NewBool(table, "password_complexity_no_username")
	t.PasswordFailureEnabled = field.NewBool(table, "password_failure_enabled")
	t.PasswordMaxFailureCount = field.NewInt32(table, "password_max_failure_count")
	t.PasswordLockoutDurationSec = field.NewInt32(table, "password_lockout_duration_sec")
	t.PasswordResetFailureWindowSec = field.NewInt32(table, "password_reset_failure_window_sec")
	t.IPFailureEnabled = field.NewBool(table, "ip_failure_enabled")
	t.IPMaxFailureCount = field.NewInt32(table, "ip_max_failure_count")
	t.IPLockoutDurationSec = field.NewInt32(table, "ip_lockout_duration_sec")
	t.IPResetFailureWindowSec = field.NewInt32(table, "ip_reset_failure_window_sec")
	t.Enable = field.NewBool(table, "enable")
	t.IsDefault = field.NewBool(table, "is_default")
	t.Priority = field.NewInt32(table, "priority")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbAuthAccountPolicy) WithContext(ctx context.Context) *tbAuthAccountPolicyDo {
	return t.tbAuthAccountPolicyDo.WithContext(ctx)
}

func (t tbAuthAccountPolicy) TableName() string { return t.tbAuthAccountPolicyDo.TableName() }

func (t tbAuthAccountPolicy) Alias() string { return t.tbAuthAccountPolicyDo.Alias() }

func (t tbAuthAccountPolicy) Columns(cols ...field.Expr) gen.Columns {
	return t.tbAuthAccountPolicyDo.Columns(cols...)
}

func (t *tbAuthAccountPolicy) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbAuthAccountPolicy) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 26)
	t.fieldMap["id"] = t.ID
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["name"] = t.Name
	t.fieldMap["description"] = t.Description
	t.fieldMap["password_min_length"] = t.PasswordMinLength
	t.fieldMap["password_max_length"] = t.PasswordMaxLength
	t.fieldMap["password_validity_days"] = t.PasswordValidityDays
	t.fieldMap["password_history_count"] = t.PasswordHistoryCount
	t.fieldMap["password_complexity_uppercase"] = t.PasswordComplexityUppercase
	t.fieldMap["password_complexity_lowercase"] = t.PasswordComplexityLowercase
	t.fieldMap["password_complexity_numbers"] = t.PasswordComplexityNumbers
	t.fieldMap["password_complexity_special_chars"] = t.PasswordComplexitySpecialChars
	t.fieldMap["password_complexity_no_username"] = t.PasswordComplexityNoUsername
	t.fieldMap["password_failure_enabled"] = t.PasswordFailureEnabled
	t.fieldMap["password_max_failure_count"] = t.PasswordMaxFailureCount
	t.fieldMap["password_lockout_duration_sec"] = t.PasswordLockoutDurationSec
	t.fieldMap["password_reset_failure_window_sec"] = t.PasswordResetFailureWindowSec
	t.fieldMap["ip_failure_enabled"] = t.IPFailureEnabled
	t.fieldMap["ip_max_failure_count"] = t.IPMaxFailureCount
	t.fieldMap["ip_lockout_duration_sec"] = t.IPLockoutDurationSec
	t.fieldMap["ip_reset_failure_window_sec"] = t.IPResetFailureWindowSec
	t.fieldMap["enable"] = t.Enable
	t.fieldMap["is_default"] = t.IsDefault
	t.fieldMap["priority"] = t.Priority
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbAuthAccountPolicy) clone(db *gorm.DB) tbAuthAccountPolicy {
	t.tbAuthAccountPolicyDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbAuthAccountPolicy) replaceDB(db *gorm.DB) tbAuthAccountPolicy {
	t.tbAuthAccountPolicyDo.ReplaceDB(db)
	return t
}

type tbAuthAccountPolicyDo struct{ gen.DO }

func (t tbAuthAccountPolicyDo) Debug() *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Debug())
}

func (t tbAuthAccountPolicyDo) WithContext(ctx context.Context) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbAuthAccountPolicyDo) ReadDB() *tbAuthAccountPolicyDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbAuthAccountPolicyDo) WriteDB() *tbAuthAccountPolicyDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbAuthAccountPolicyDo) Session(config *gorm.Session) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbAuthAccountPolicyDo) Clauses(conds ...clause.Expression) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbAuthAccountPolicyDo) Returning(value interface{}, columns ...string) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbAuthAccountPolicyDo) Not(conds ...gen.Condition) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbAuthAccountPolicyDo) Or(conds ...gen.Condition) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbAuthAccountPolicyDo) Select(conds ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbAuthAccountPolicyDo) Where(conds ...gen.Condition) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbAuthAccountPolicyDo) Order(conds ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbAuthAccountPolicyDo) Distinct(cols ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbAuthAccountPolicyDo) Omit(cols ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbAuthAccountPolicyDo) Join(table schema.Tabler, on ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbAuthAccountPolicyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbAuthAccountPolicyDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbAuthAccountPolicyDo) Group(cols ...field.Expr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbAuthAccountPolicyDo) Having(conds ...gen.Condition) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbAuthAccountPolicyDo) Limit(limit int) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbAuthAccountPolicyDo) Offset(offset int) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbAuthAccountPolicyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbAuthAccountPolicyDo) Unscoped() *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbAuthAccountPolicyDo) Create(values ...*model.TbAuthAccountPolicy) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbAuthAccountPolicyDo) CreateInBatches(values []*model.TbAuthAccountPolicy, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbAuthAccountPolicyDo) Save(values ...*model.TbAuthAccountPolicy) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbAuthAccountPolicyDo) First() (*model.TbAuthAccountPolicy, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthAccountPolicy), nil
	}
}

func (t tbAuthAccountPolicyDo) Take() (*model.TbAuthAccountPolicy, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthAccountPolicy), nil
	}
}

func (t tbAuthAccountPolicyDo) Last() (*model.TbAuthAccountPolicy, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthAccountPolicy), nil
	}
}

func (t tbAuthAccountPolicyDo) Find() ([]*model.TbAuthAccountPolicy, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbAuthAccountPolicy), err
}

func (t tbAuthAccountPolicyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbAuthAccountPolicy, err error) {
	buf := make([]*model.TbAuthAccountPolicy, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbAuthAccountPolicyDo) FindInBatches(result *[]*model.TbAuthAccountPolicy, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbAuthAccountPolicyDo) Attrs(attrs ...field.AssignExpr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbAuthAccountPolicyDo) Assign(attrs ...field.AssignExpr) *tbAuthAccountPolicyDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbAuthAccountPolicyDo) Joins(fields ...field.RelationField) *tbAuthAccountPolicyDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbAuthAccountPolicyDo) Preload(fields ...field.RelationField) *tbAuthAccountPolicyDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbAuthAccountPolicyDo) FirstOrInit() (*model.TbAuthAccountPolicy, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthAccountPolicy), nil
	}
}

func (t tbAuthAccountPolicyDo) FirstOrCreate() (*model.TbAuthAccountPolicy, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthAccountPolicy), nil
	}
}

func (t tbAuthAccountPolicyDo) FindByPage(offset int, limit int) (result []*model.TbAuthAccountPolicy, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbAuthAccountPolicyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbAuthAccountPolicyDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbAuthAccountPolicyDo) Delete(models ...*model.TbAuthAccountPolicy) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbAuthAccountPolicyDo) withDO(do gen.Dao) *tbAuthAccountPolicyDo {
	t.DO = *do.(*gen.DO)
	return t
}
