package api

import (
	alertComm "asdsec.com/asec/platform/app/console/app/alert/common"
	"asdsec.com/asec/platform/app/console/app/alert/model"
	"asdsec.com/asec/platform/app/console/app/alert/service"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/app/console/common/utils"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/middleware"
	"asdsec.com/asec/platform/app/console/utils/web"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

type SettingReq struct {
	Id         uint64    `form:"id" json:"id" binding:"required,min=1"`
	EventType  string    `form:"event_type" json:"event_type" binding:"required,min=1,max=50,regexEnum=COMMON_NAME"`
	EventName  string    `form:"event_name" json:"event_name" binding:"required,min=1,max=150,regexEnum=COMMON_NAME"`
	Threshold  TsSetting `form:"threshold" json:"threshold" binding:"required,min=1"`
	IsQyWx     int       `form:"is_qy_wx" json:"is_qy_wx" binding:"omitempty,oneof=-1 0 1"`
	IsDingTalk int       `form:"is_ding_talk" json:"is_ding_talk" binding:"omitempty,oneof=-1 0 1"`
}

type TsSetting struct {
	High   int `form:"high" json:"high" binding:"omitempty,oneof=0 1"`
	Middle int `form:"middle" json:"middle" binding:"omitempty,oneof=0 1"`
	Low    int `form:"low" json:"low" binding:"omitempty,oneof=0 1"`
}

func SettingUpdate(c *gin.Context) {
	c.Set(middleware.LogCtx, middleware.MarshalLog(c, common.AlertModuleName, common.AlertUpdateSetting))
	var req SettingReq
	if err := c.ShouldBindJSON(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		global.SysLog.Error(err.Error())
		return
	}
	ts, err := json.Marshal(req.Threshold)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	alertSetting := map[string]interface{}{
		"event_type":   req.EventType,
		"event_name":   req.EventName,
		"threshold":    ts,
		"is_qy_wx":     req.IsQyWx,
		"is_ding_talk": req.IsDingTalk,
		"update_time":  time.Now(),
	}
	err = service.GetSettingService().SaveAlertSetting(c, req.Id, alertSetting)
	if err != nil {
		common.Fail(c, common.EditSettingErr)
		return
	}
	common.Ok(c)
}

func SettingList(c *gin.Context) {
	tenantId, _ := strconv.ParseUint(web.GetCorpId(c), 10, 64)
	res := map[string]interface{}{
		"wx_status": -1,
		"dd_status": -1,
	}
	setting, err := service.GetSettingService().GetAlertSetting(c, tenantId)
	if err != nil {
		common.Fail(c, common.QuerySettingErr)
		global.SysLog.Error(err.Error())
		return
	}
	data := make([]model.SettingResp, 0)
	for _, v := range setting {
		ts := model.LimitSetting{}
		err = json.Unmarshal([]byte(v.Threshold), &ts)
		if err != nil {
			continue
		}
		item := model.SettingResp{
			ID:         v.ID,
			TenantId:   v.TenantId,
			EventType:  v.EventType,
			EventName:  v.EventName,
			Threshold:  ts,
			IsQyWx:     v.IsQyWx,
			IsDingTalk: v.IsDingTalk,
			CreateTime: utils.FrontTime(v.CreateTime),
			UpdateTime: utils.FrontTime(v.UpdateTime),
		}
		data = append(data, item)
	}
	res["data"] = data
	wxStatus, err := service.GetNotifyService().GetRobotByPlat(c, tenantId, alertComm.WeChat)
	if err != nil {
		return
	}
	ddStatus, err := service.GetNotifyService().GetRobotByPlat(c, tenantId, alertComm.DingTalk)
	if err != nil {
		return
	}
	if len(wxStatus) > 0 {
		res["wx_status"] = 1
	}
	if len(ddStatus) > 0 {
		res["dd_status"] = 1
	}
	common.OkWithData(c, res)
}
