// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: auth/v1/auth/auth.proto

package auth

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Auth_Login_FullMethodName          = "/api.auth.v1.auth.Auth/Login"
	Auth_GetToken_FullMethodName       = "/api.auth.v1.auth.Auth/GetToken"
	Auth_ListMainIDP_FullMethodName    = "/api.auth.v1.auth.Auth/ListMainIDP"
	Auth_RefreshToken_FullMethodName   = "/api.auth.v1.auth.Auth/RefreshToken"
	Auth_ThirdLogin_FullMethodName     = "/api.auth.v1.auth.Auth/ThirdLogin"
	Auth_Cache_FullMethodName          = "/api.auth.v1.auth.Auth/Cache"
	Auth_SendSms_FullMethodName        = "/api.auth.v1.auth.Auth/SendSms"
	Auth_SmsVerify_FullMethodName      = "/api.auth.v1.auth.Auth/SmsVerify"
	Auth_TokenVerify_FullMethodName    = "/api.auth.v1.auth.Auth/TokenVerify"
	Auth_GetSendSmsKey_FullMethodName  = "/api.auth.v1.auth.Auth/GetSendSmsKey"
	Auth_AdmsLogin_FullMethodName      = "/api.auth.v1.auth.Auth/AdmsLogin"
	Auth_UserBind_FullMethodName       = "/api.auth.v1.auth.Auth/UserBind"
	Auth_OAuth2Callback_FullMethodName = "/api.auth.v1.auth.Auth/OAuth2Callback"
	Auth_AuthCallback_FullMethodName   = "/api.auth.v1.auth.Auth/AuthCallback"
)

// AuthClient is the client API for Auth service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuthClient interface {
	Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginReply, error)
	GetToken(ctx context.Context, in *GetTokenRequest, opts ...grpc.CallOption) (*GetTokenReply, error)
	ListMainIDP(ctx context.Context, in *ListMainIDPRequest, opts ...grpc.CallOption) (*ListMainIDPReply, error)
	RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc.CallOption) (*GetTokenReply, error)
	ThirdLogin(ctx context.Context, in *ThirdLoginRequest, opts ...grpc.CallOption) (*LoginReply, error)
	Cache(ctx context.Context, in *CacheRequest, opts ...grpc.CallOption) (*CacheReply, error)
	SendSms(ctx context.Context, in *SendSmsRequest, opts ...grpc.CallOption) (*SendSmsReply, error)
	SmsVerify(ctx context.Context, in *SmsVerifyRequest, opts ...grpc.CallOption) (*LoginReply, error)
	TokenVerify(ctx context.Context, in *TokenVerifyRequest, opts ...grpc.CallOption) (*TokenVerifyReply, error)
	GetSendSmsKey(ctx context.Context, in *SendSmsKeyRequest, opts ...grpc.CallOption) (*SendSmsKeyReply, error)
	AdmsLogin(ctx context.Context, in *AdmsLoginRequest, opts ...grpc.CallOption) (*AdmsLoginReply, error)
	UserBind(ctx context.Context, in *UserBindRequest, opts ...grpc.CallOption) (*UserBindReply, error)
	OAuth2Callback(ctx context.Context, in *OAuth2CallbackRequest, opts ...grpc.CallOption) (*LoginReply, error)
	// 通用认证回调统一入口
	AuthCallback(ctx context.Context, in *AuthCallbackRequest, opts ...grpc.CallOption) (*LoginReply, error)
}

type authClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthClient(cc grpc.ClientConnInterface) AuthClient {
	return &authClient{cc}
}

func (c *authClient) Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Auth_Login_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetToken(ctx context.Context, in *GetTokenRequest, opts ...grpc.CallOption) (*GetTokenReply, error) {
	out := new(GetTokenReply)
	err := c.cc.Invoke(ctx, Auth_GetToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ListMainIDP(ctx context.Context, in *ListMainIDPRequest, opts ...grpc.CallOption) (*ListMainIDPReply, error) {
	out := new(ListMainIDPReply)
	err := c.cc.Invoke(ctx, Auth_ListMainIDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc.CallOption) (*GetTokenReply, error) {
	out := new(GetTokenReply)
	err := c.cc.Invoke(ctx, Auth_RefreshToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ThirdLogin(ctx context.Context, in *ThirdLoginRequest, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Auth_ThirdLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) Cache(ctx context.Context, in *CacheRequest, opts ...grpc.CallOption) (*CacheReply, error) {
	out := new(CacheReply)
	err := c.cc.Invoke(ctx, Auth_Cache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) SendSms(ctx context.Context, in *SendSmsRequest, opts ...grpc.CallOption) (*SendSmsReply, error) {
	out := new(SendSmsReply)
	err := c.cc.Invoke(ctx, Auth_SendSms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) SmsVerify(ctx context.Context, in *SmsVerifyRequest, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Auth_SmsVerify_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) TokenVerify(ctx context.Context, in *TokenVerifyRequest, opts ...grpc.CallOption) (*TokenVerifyReply, error) {
	out := new(TokenVerifyReply)
	err := c.cc.Invoke(ctx, Auth_TokenVerify_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GetSendSmsKey(ctx context.Context, in *SendSmsKeyRequest, opts ...grpc.CallOption) (*SendSmsKeyReply, error) {
	out := new(SendSmsKeyReply)
	err := c.cc.Invoke(ctx, Auth_GetSendSmsKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) AdmsLogin(ctx context.Context, in *AdmsLoginRequest, opts ...grpc.CallOption) (*AdmsLoginReply, error) {
	out := new(AdmsLoginReply)
	err := c.cc.Invoke(ctx, Auth_AdmsLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) UserBind(ctx context.Context, in *UserBindRequest, opts ...grpc.CallOption) (*UserBindReply, error) {
	out := new(UserBindReply)
	err := c.cc.Invoke(ctx, Auth_UserBind_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) OAuth2Callback(ctx context.Context, in *OAuth2CallbackRequest, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Auth_OAuth2Callback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) AuthCallback(ctx context.Context, in *AuthCallbackRequest, opts ...grpc.CallOption) (*LoginReply, error) {
	out := new(LoginReply)
	err := c.cc.Invoke(ctx, Auth_AuthCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthServer is the server API for Auth service.
// All implementations must embed UnimplementedAuthServer
// for forward compatibility
type AuthServer interface {
	Login(context.Context, *LoginRequest) (*LoginReply, error)
	GetToken(context.Context, *GetTokenRequest) (*GetTokenReply, error)
	ListMainIDP(context.Context, *ListMainIDPRequest) (*ListMainIDPReply, error)
	RefreshToken(context.Context, *RefreshTokenRequest) (*GetTokenReply, error)
	ThirdLogin(context.Context, *ThirdLoginRequest) (*LoginReply, error)
	Cache(context.Context, *CacheRequest) (*CacheReply, error)
	SendSms(context.Context, *SendSmsRequest) (*SendSmsReply, error)
	SmsVerify(context.Context, *SmsVerifyRequest) (*LoginReply, error)
	TokenVerify(context.Context, *TokenVerifyRequest) (*TokenVerifyReply, error)
	GetSendSmsKey(context.Context, *SendSmsKeyRequest) (*SendSmsKeyReply, error)
	AdmsLogin(context.Context, *AdmsLoginRequest) (*AdmsLoginReply, error)
	UserBind(context.Context, *UserBindRequest) (*UserBindReply, error)
	OAuth2Callback(context.Context, *OAuth2CallbackRequest) (*LoginReply, error)
	// 通用认证回调统一入口
	AuthCallback(context.Context, *AuthCallbackRequest) (*LoginReply, error)
	mustEmbedUnimplementedAuthServer()
}

// UnimplementedAuthServer must be embedded to have forward compatible implementations.
type UnimplementedAuthServer struct {
}

func (UnimplementedAuthServer) Login(context.Context, *LoginRequest) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedAuthServer) GetToken(context.Context, *GetTokenRequest) (*GetTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetToken not implemented")
}
func (UnimplementedAuthServer) ListMainIDP(context.Context, *ListMainIDPRequest) (*ListMainIDPReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMainIDP not implemented")
}
func (UnimplementedAuthServer) RefreshToken(context.Context, *RefreshTokenRequest) (*GetTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedAuthServer) ThirdLogin(context.Context, *ThirdLoginRequest) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThirdLogin not implemented")
}
func (UnimplementedAuthServer) Cache(context.Context, *CacheRequest) (*CacheReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cache not implemented")
}
func (UnimplementedAuthServer) SendSms(context.Context, *SendSmsRequest) (*SendSmsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSms not implemented")
}
func (UnimplementedAuthServer) SmsVerify(context.Context, *SmsVerifyRequest) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmsVerify not implemented")
}
func (UnimplementedAuthServer) TokenVerify(context.Context, *TokenVerifyRequest) (*TokenVerifyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TokenVerify not implemented")
}
func (UnimplementedAuthServer) GetSendSmsKey(context.Context, *SendSmsKeyRequest) (*SendSmsKeyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSendSmsKey not implemented")
}
func (UnimplementedAuthServer) AdmsLogin(context.Context, *AdmsLoginRequest) (*AdmsLoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdmsLogin not implemented")
}
func (UnimplementedAuthServer) UserBind(context.Context, *UserBindRequest) (*UserBindReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserBind not implemented")
}
func (UnimplementedAuthServer) OAuth2Callback(context.Context, *OAuth2CallbackRequest) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OAuth2Callback not implemented")
}
func (UnimplementedAuthServer) AuthCallback(context.Context, *AuthCallbackRequest) (*LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthCallback not implemented")
}
func (UnimplementedAuthServer) mustEmbedUnimplementedAuthServer() {}

// UnsafeAuthServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthServer will
// result in compilation errors.
type UnsafeAuthServer interface {
	mustEmbedUnimplementedAuthServer()
}

func RegisterAuthServer(s grpc.ServiceRegistrar, srv AuthServer) {
	s.RegisterService(&Auth_ServiceDesc, srv)
}

func _Auth_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).Login(ctx, req.(*LoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetToken(ctx, req.(*GetTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ListMainIDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMainIDPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ListMainIDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ListMainIDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ListMainIDP(ctx, req.(*ListMainIDPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_RefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).RefreshToken(ctx, req.(*RefreshTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ThirdLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThirdLoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ThirdLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ThirdLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ThirdLogin(ctx, req.(*ThirdLoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_Cache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).Cache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_Cache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).Cache(ctx, req.(*CacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_SendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).SendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_SendSms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).SendSms(ctx, req.(*SendSmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_SmsVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmsVerifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).SmsVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_SmsVerify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).SmsVerify(ctx, req.(*SmsVerifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_TokenVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TokenVerifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).TokenVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_TokenVerify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).TokenVerify(ctx, req.(*TokenVerifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GetSendSmsKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GetSendSmsKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GetSendSmsKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GetSendSmsKey(ctx, req.(*SendSmsKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_AdmsLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdmsLoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).AdmsLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_AdmsLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).AdmsLogin(ctx, req.(*AdmsLoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_UserBind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).UserBind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_UserBind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).UserBind(ctx, req.(*UserBindRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_OAuth2Callback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OAuth2CallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).OAuth2Callback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_OAuth2Callback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).OAuth2Callback(ctx, req.(*OAuth2CallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_AuthCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).AuthCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_AuthCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).AuthCallback(ctx, req.(*AuthCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Auth_ServiceDesc is the grpc.ServiceDesc for Auth service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Auth_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.auth.v1.auth.Auth",
	HandlerType: (*AuthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Login",
			Handler:    _Auth_Login_Handler,
		},
		{
			MethodName: "GetToken",
			Handler:    _Auth_GetToken_Handler,
		},
		{
			MethodName: "ListMainIDP",
			Handler:    _Auth_ListMainIDP_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _Auth_RefreshToken_Handler,
		},
		{
			MethodName: "ThirdLogin",
			Handler:    _Auth_ThirdLogin_Handler,
		},
		{
			MethodName: "Cache",
			Handler:    _Auth_Cache_Handler,
		},
		{
			MethodName: "SendSms",
			Handler:    _Auth_SendSms_Handler,
		},
		{
			MethodName: "SmsVerify",
			Handler:    _Auth_SmsVerify_Handler,
		},
		{
			MethodName: "TokenVerify",
			Handler:    _Auth_TokenVerify_Handler,
		},
		{
			MethodName: "GetSendSmsKey",
			Handler:    _Auth_GetSendSmsKey_Handler,
		},
		{
			MethodName: "AdmsLogin",
			Handler:    _Auth_AdmsLogin_Handler,
		},
		{
			MethodName: "UserBind",
			Handler:    _Auth_UserBind_Handler,
		},
		{
			MethodName: "OAuth2Callback",
			Handler:    _Auth_OAuth2Callback_Handler,
		},
		{
			MethodName: "AuthCallback",
			Handler:    _Auth_AuthCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "auth/v1/auth/auth.proto",
}
