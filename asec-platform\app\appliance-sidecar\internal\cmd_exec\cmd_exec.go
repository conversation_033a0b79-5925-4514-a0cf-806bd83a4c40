package cmd_exec

import (
	v1 "asdsec.com/asec/platform/api/appliance/v1"
	conf "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/pkg/model/push_model"
	"context"
	"database/sql"
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"google.golang.org/grpc"
	"strconv"
	"sync"
	"time"
)

var cmdExecPath = GetConfigDir() + "\\cmd_exec.log"

func PullCmdExec(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   PullCmdExecReal,
		RunType:      common.SimpleSend,
		WaitSecond:   20,
		RandomOffset: 3,
	}
	common.Send(param)
}

func PullCmdExecReal(conn *grpc.ClientConn, ctx context.Context) error {
	db, openDbErr := global.InitSqliteByName(global.ConfDbName)
	if openDbErr != nil {
		global.Logger.Sugar().Errorf("open cf db err:%v", openDbErr)
	}
	defer global.CloseSqlite(db)

	var confData []byte
	// 判断依据是列是否存在
	err := db.QueryRow("SELECT conf_data FROM tb_conf_agent WHERE conf_type='upload_log';").
		Scan(&confData)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil
		}
		global.Logger.Sugar().Errorf("query task err:%v", err)
		return err
	}
	// 不存在 采集日志命令
	if len(confData) == 0 {
		return nil
	}
	c := conf.PushCmd{}
	err = proto.Unmarshal(confData, &c)
	if err != nil {
		global.Logger.Sugar().Errorf("unmarshal cmdConf err:%v", err)
	}

	if len(c.CmdData) <= 0 {
		err = UploadLog(1, "")
		if err != nil {
			global.Logger.Sugar().Errorf("upload log err:%v", err)
		}
	} else {
		var cmd push_model.CmdExec
		err = json.Unmarshal(c.CmdData, &cmd)
		if err != nil {
			global.Logger.Sugar().Errorf("unmarshal cmd err:%v", err)
		}
		global.Logger.Sugar().Infof("got log up cmd:%v", cmd.Cmd)
		err = ExecuteCmdWriteBat(cmd.Cmd, cmdExecPath)
		if err != nil {
			global.Logger.Sugar().Errorf("exec cmd err:%v", err)
		}
		err = UploadLog(1, cmdExecPath)
		if err != nil {
			global.Logger.Sugar().Errorf("upload log err:%v", err)
		}
	}

	// 处理任务完成,不管任务是否执行成功 删除这条命令信息
	// 通知平台删除命令
	client := v1.NewConfCenterClient(conn)
	req := v1.DelConfReq{
		ConfBizId: strconv.FormatUint(global.ApplianceID, 10),
		ConfType:  "upload_log",
	}

	// 通知平台删除任务 重试3次 失败后删除本地记录
	for i := 0; i < 3; i++ {
		_, err = client.DelConf(ctx, &req)
		if err != nil {
			global.Logger.Sugar().Errorf("del platform task err:%v", err)
			time.Sleep(time.Second * 1)
			continue
		} else {
			break
		}
	}

	// 删除本地记录
	_, err = db.Exec("DELETE from tb_conf_agent WHERE conf_type='upload_log';")
	if err != nil {
		global.Logger.Sugar().Errorf("del local task err:%v", err)
	}
	global.Logger.Sugar().Infof("log up done")
	return nil
}
