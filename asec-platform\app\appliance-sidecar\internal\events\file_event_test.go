package events

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"database/sql"
	"fmt"
	"testing"
)

func TestCleanEvents(t *testing.T) {
	cleanFileEvent(1, 100, 50)
}

func TestSqlite(t *testing.T) {
	sqliteClient, err := global.InitSqlite()
	if err != nil {
		panic(err)
	}
	defer global.CloseSqlite(sqliteClient)

	var res int
	query1 := sqliteClient.QueryRow("SELECT id from tbl_file_monitor ORDER BY id desc limit 1")
	if err != nil {
		panic(err)
	}
	err = query1.Scan(&res)
	if err != nil {
		panic(err)
	}
	fmt.Println(res)

}

func TestInit(t *testing.T) {
	sqlite, err := global.InitSqlite()
	defer sqlite.Close()
	if err != nil {
		panic(err)
	}

	query, err := sqlite.Query("SELECT event_type,current_offset FROM tb_events_offset")
	if err == sql.ErrNoRows || query == nil {
		fmt.Println(1)
		return
	}
	if err != nil {
		panic(err)
	}

	m := make(map[string]int)
	for query.Next() {
		var eventType string
		var offset int
		err := query.Scan(&eventType, &offset)
		if err != nil {
			panic(err)
		}
		if eventType == "" {
			continue
		}
		m[eventType] = offset

	}
	query.Close()

	for k, v := range m {
		sprintf := fmt.Sprintf("INSERT INTO tb_events_offset1 (event_type, current_offset) VALUES ('%v', %v);", k, v)
		fmt.Println(sprintf)
		_, err = sqlite.Exec(sprintf)
		if err != nil {
			panic(err)
		}
	}
}
