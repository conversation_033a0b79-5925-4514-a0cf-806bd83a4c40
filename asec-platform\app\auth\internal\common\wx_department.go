package common

import (
	"fmt"

	"asdsec.com/asec/platform/app/auth/internal/dto"
)

func BuildWxDeptTree(input []*dto.ExternalDepartment) (*dto.WxDepartmentTree, error) {
	nodes := make(map[string]*dto.WxDepartmentTree)
	for _, n := range input {
		nodes[string(n.ID)] = &dto.WxDepartmentTree{
			ExternalDepartment: *n,
		}
	}

	var root = &dto.WxDepartmentTree{ExternalDepartment: dto.ExternalDepartment{ID: dto.WxFakeRootDepartmentId}}
	for _, n := range nodes {
		if n.Parentid == dto.WxFakeRootDepartmentId {
			root.Children = append(root.Children, n)
		} else {
			parent, ok := nodes[string(n.Parentid)]
			if !ok {
				return nil, fmt.Errorf("n's parent not found. node=%+v", n)
			}
			parent.Children = append(parent.Children, n)
		}
	}
	return root, nil
}

func GetRootID(input []*dto.ExternalDepartment) (string, error) {
	root, err := BuildWxDeptTree(input)
	if err != nil {
		return "0", err
	}
	if len(root.Children) != 1 {
		return "0", fmt.Errorf("root not found")
	}
	return root.Children[0].ID, nil
}
