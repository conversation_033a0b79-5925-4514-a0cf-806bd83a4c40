package service

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/consts"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/dto"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/repository"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	userRiskService "asdsec.com/asec/platform/app/console/app/user_risk/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/strategy_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/jackc/pgtype"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"sort"
	"strconv"
	"sync"
	"time"
)

var strategyServiceImpl StrategyService

var StrategyServiceInit sync.Once

type strategyService struct {
	db repository.StrategyRepository
}

func (s strategyService) StrategyList(c *gin.Context, req vo.StrategyListReq) (vo.StrategyListResp, aerrors.AError) {
	resp := vo.StrategyListResp{}
	list, totalRows, err := s.db.StrategyList(c, req)

	if err != nil {
		return resp, aerrors.NewWithError(err, common.OperateError)
	}
	list, err = s.listMatchCount(c, list)
	if err != nil {
		return resp, aerrors.NewWithError(err, common.OperateError)
	}
	riskLevel, err := userRiskService.GetUserRiskService().GetUserRiskSetting(c)
	if err != nil {
		return vo.StrategyListResp{}, aerrors.NewWithError(err, common.OperateError)
	}
	levelMap := make(map[int]int)
	for _, v := range riskLevel {
		levelMap[v.RiskLevel] = v.MinScore
	}
	// 数据库查询对象转前端需要对象
	listVo, err := s.transListVo(list, levelMap)
	if err != nil {
		return resp, aerrors.NewWithError(err, common.OperateError)
	}

	// 聚合组转换
	strategies := s.aggregateGroupStrategies(listVo)
	resp.ListData = strategies
	resp.TotalNum = int(totalRows)
	return resp, nil
}
func (s strategyService) aggregateGroupStrategies(data []vo.GroupStrategyData) []vo.StrategyListData {

	groups := make(map[string]vo.StrategyListData)
	for _, d := range data {
		group, ok := groups[d.StrategyGroupId]
		if !ok {
			var innerList []vo.GroupStrategyData
			innerList = append(innerList, d)
			listData := vo.StrategyListData{
				GroupName:         d.GroupName,
				GroupId:           d.StrategyGroupId,
				GroupCount:        1,
				GroupStrategyData: innerList,
			}
			groups[d.StrategyGroupId] = listData
		} else {
			group.GroupStrategyData = append(group.GroupStrategyData, d)
			group.GroupCount = group.GroupCount + 1
			groups[d.StrategyGroupId] = group
		}

	}
	// 按照原来的顺序拼接Group
	var finalResult []vo.StrategyListData
	match := make(map[string]string)
	for _, d := range data {
		_, ok := match[d.StrategyGroupId]
		if !ok {
			finalResult = append(finalResult, groups[d.StrategyGroupId])
			match[d.StrategyGroupId] = ""
		}
	}
	return finalResult
}

func (s strategyService) StrategyMove(c *gin.Context, moveReq vo.StrategyMoveReq) aerrors.AError {
	nowPriorityList, err := s.db.GerStrategySortPriority(c, moveReq.StrategyId, "", "")
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}

	movePriority, err := s.getMovePriority(c, moveReq)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	nowPriorityList = append(nowPriorityList, movePriority)

	changePrioritys, strategy := GetChangePriority(nowPriorityList, moveReq.StrategyId)

	err = s.db.ResetAccessStrategyCache(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}

	strategy.StrategyGroupId = moveReq.GroupId
	err = s.db.StrategyMove(c, changePrioritys, strategy)

	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s strategyService) StrategySimpleList(c *gin.Context, groupId string) ([]vo.StrategySimpleListResp, aerrors.AError) {
	simpleList, err := s.db.StrategySimpleList(c, groupId)
	if err != nil {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	return simpleList, nil
}

func (s strategyService) ClearStrategyMatch(c *gin.Context, req vo.DelStrategyReq) aerrors.AError {
	delStrategyDto, err := delVotoDto(req)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}
	err = s.db.ClearStrategyMatch(c, delStrategyDto)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func delVotoDto(req vo.DelStrategyReq) (dto.DelStrategyDto, error) {
	var intIds []int64
	strategyDto := dto.DelStrategyDto{}
	for _, id := range req.Ids {
		parseInt, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			return strategyDto, err
		}
		intIds = append(intIds, parseInt)
	}
	strategyDto.Ids = intIds
	return strategyDto, nil
}

func (s strategyService) StrategyDetail(c *gin.Context, id string) (vo.StrategyDetailResp, aerrors.AError) {
	strategyDetail := vo.StrategyDetailResp{}
	intId, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		return strategyDetail, aerrors.NewWithError(err, common.ParamError)
	}
	strategyDetail, err = s.db.StrategyDetail(c, intId)
	if err != nil {
		return strategyDetail, aerrors.NewWithError(err, common.OperateError)
	}
	if strategyDetail.Id == 0 {
		return strategyDetail, aerrors.New("get strategy err,src id not found:"+id, consts.OperatorRecordNotFoundErr)
	}
	if strategyDetail.TimeId != "" {
		strategyDetail.TimeDetail, err = s.db.GetTimeDetail(c, strategyDetail.TimeId)
		if err != nil {
			return strategyDetail, aerrors.New("get strategy err,time id not found:"+strategyDetail.TimeId, consts.OperatorRecordNotFoundErr)
		}
	}
	if len(strategyDetail.UserRiskRuleInfo.Bytes) > 0 {
		var userRiskRule vo.UserRiskRule
		err = json.Unmarshal(strategyDetail.UserRiskRuleInfo.Bytes, &userRiskRule)
		if err != nil {
			return vo.StrategyDetailResp{}, aerrors.NewWithError(err, common.OperateError)
		}
		if userRiskRule.Type == consts.LevelUserRiskType {
			riskLevel, err := userRiskService.GetUserRiskService().GetUserRiskSetting(c)
			if err != nil {
				return vo.StrategyDetailResp{}, aerrors.NewWithError(err, common.OperateError)
			}
			for _, v := range riskLevel {
				if v.RiskLevel == userRiskRule.RiskLevel {
					userRiskRule.Score = v.MinScore
				}
			}
		}
		strategyDetail.UserRiskRule = userRiskRule
	}
	return strategyDetail, nil
}

func (s strategyService) EnableStrategy(c *gin.Context, req vo.EnableStrategyReq) aerrors.AError {
	delStrategyDto, err := delVotoDto(req.DelStrategyReq)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}
	enableStrategyDto := dto.EnableStrategyDto{
		Enable:         req.Enable,
		DelStrategyDto: delStrategyDto,
	}
	err = s.db.ResetAccessStrategyCache(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = s.db.EnableStrategy(c, enableStrategyDto)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s strategyService) DelStrategy(c *gin.Context, req vo.DelStrategyReq) aerrors.AError {
	delStrategyDto, err := delVotoDto(req)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}
	err = s.db.ResetAccessStrategyCache(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = s.db.DelStrategy(c, delStrategyDto)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s strategyService) checkParamAdd(c *gin.Context, id uint64, timeId string, strategyName string) aerrors.AError {
	if timeId != "" {
		timeIdEx, err := s.db.CheckTimeId(c, timeId)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		if !timeIdEx {
			return aerrors.New("timeId not have data,timeId:"+timeId, consts.TimeIdErr)
		}
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
	}
	return nil
}

func (s strategyService) UpdateStrategy(c *gin.Context, req vo.UpdateStrategyReq) aerrors.AError {
	paramErr := s.checkParamAdd(c, req.Id, req.TimeId, req.StrategyName)
	if paramErr != nil {
		return paramErr
	}

	strategy := model.AccessStrategy{}
	err := copier.Copy(&strategy, &req)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}
	appIds, err := utils.StringsToInt64s(req.AppIds)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}
	tagIds, err := utils.StringsToInt64s(req.AppGroupIds)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}

	strategy.AppIds = appIds
	strategy.AppGroupIds = tagIds
	strategy.ID = req.Id

	// 转换rego
	if len(req.DynamicRule.Bytes) > 0 {
		strategy.RegoFile, err = DynamicRuleToRego(req.DynamicRule)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
	} else {
		strategy.RegoFile = nil
	}
	if req.UserRiskRule.Type != "" {
		userRiskBytes, err := json.Marshal(req.UserRiskRule)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		strategy.UserRiskRule = pgtype.JSONB{
			Bytes:  userRiskBytes,
			Status: pgtype.Present,
		}
	}
	err = s.db.ResetAccessStrategyCache(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = s.db.UpdateStrategy(c, strategy)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s strategyService) AddStrategy(c *gin.Context, req vo.AddStrategyReq) aerrors.AError {
	paramErr := s.checkParamAdd(c, 0, req.TimeId, req.StrategyName)
	if paramErr != nil {
		return paramErr
	}

	strategy := model.AccessStrategy{}
	err := copier.Copy(&strategy, &req)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}
	appIds, err := utils.StringsToInt64s(req.AppIds)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}
	tagIds, err := utils.StringsToInt64s(req.AppGroupIds)
	if err != nil {
		return aerrors.NewWithError(err, common.ParamError)
	}

	strategy.AppIds = appIds
	strategy.AppGroupIds = tagIds
	id, _ := snowflake.Sf.GetId()
	strategy.ID = id
	// 转换rego
	if len(req.DynamicRule.Bytes) > 0 {
		strategy.RegoFile, err = DynamicRuleToRego(req.DynamicRule)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
	}

	if req.UserRiskRule.Type != "" {
		userRiskBytes, err := json.Marshal(req.UserRiskRule)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		strategy.UserRiskRule = pgtype.JSONB{
			Bytes:  userRiskBytes,
			Status: pgtype.Present,
		}
	}
	err = s.db.ResetAccessStrategyCache(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	// 开始存储
	// 需要将组内其他的优先级置低一位
	return s.add(c, req.StrategyGroupId, strategy)
}

// add 内部使用的添加策略方法
func (s strategyService) add(c *gin.Context, groupId string, strategy model.AccessStrategy) aerrors.AError {
	// 获取新增策略的优先级
	priority, err := s.db.GetPriorityByGroup(c, groupId)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	strategy.Priority = priority
	if strategy.Priority == 0 {
		strategy.Priority = 1
	}
	// 获取需要更改优先级的策略
	changePriorityList, err := s.db.GetStrategyByPriority(c, priority-1, "higher")
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	// 优先级加一
	for i := range changePriorityList {
		changePriorityList[i].Priority = changePriorityList[i].Priority + 1
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		dbErr := s.db.AddStrategy(tx, strategy)
		if dbErr != nil {
			return dbErr
		}
		if len(changePriorityList) <= 0 {
			return nil
		}
		dbErr = s.db.UpdatesStrategyPriority(tx, changePriorityList)
		if dbErr != nil {
			return dbErr
		}
		return nil
	})
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		return aerrors.New("duplicateNameErr,name:"+strategy.StrategyName, consts.DuplicateNameErr)
	}
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s strategyService) transListVo(listDtos []dto.StrategyListDto, riskLevelMap map[int]int) ([]vo.GroupStrategyData, error) {
	var voList []vo.GroupStrategyData
	if len(listDtos) <= 0 {
		return voList, nil
	}
	now := time.Now()
	for _, d := range listDtos {
		var userRiskRule vo.UserRiskRule
		if len(d.UserRiskRule.Bytes) > 0 {
			err := json.Unmarshal(d.UserRiskRule.Bytes, &userRiskRule)
			if err != nil {
				return nil, err
			}
			if userRiskRule.Type == consts.LevelUserRiskType {
				userRiskRule.Score = riskLevelMap[userRiskRule.RiskLevel]
			}
		}

		source := vo.StrategySource{
			UserIds:               d.UserIds,
			UserGroupIds:          d.UserGroupIds,
			RoleIds:               d.RoleIds,
			UserNames:             d.UserNames,
			UserGroupNames:        d.UserGroupNames,
			UserRoleNames:         d.UserRoleNames,
			EnableAllUser:         d.EnableAllUser,
			DynamicRule:           d.DynamicRule,
			ExcludeUserIds:        d.ExcludeUserIds,
			ExcludeUserNames:      d.ExcludeUserNames,
			ExcludeUserGroupIds:   d.ExcludeUserGroupIds,
			ExcludeUserGroupNames: d.ExcludeUserGroupNames,
			ExcludeUserRoleIds:    d.ExcludeUserRoleIds,
			ExcludeUserRoleNames:  d.ExcludeUserRoleNames,
		}
		if userRiskRule.Type != "" {
			userRiskBytes, err := json.Marshal(userRiskRule)
			if err != nil {
				return nil, err
			}
			source.UserRiskRule = pgtype.JSONB{
				Bytes:  userRiskBytes,
				Status: pgtype.Present,
			}
		} else {
			source.UserRiskRule = pgtype.JSONB{
				Bytes:  []byte("{}"),
				Status: pgtype.Present,
			}
		}
		strategyDest := vo.StrategyDest{
			AppIds:       d.AppIds,
			AppNames:     d.AppNames,
			AppGroupIds:  d.AppGroupIds,
			AppTagNames:  d.AppTagNames,
			EnableAllApp: d.EnableAllApp,
			App:          d.App,
		}
		data := vo.GroupStrategyData{
			Id:              d.Id,
			StrategyName:    d.StrategyName,
			StrategyDetail:  d.StrategyDetail,
			GroupName:       d.GroupName,
			StrategyGroupId: d.StrategyGroupId,
			StrategySource:  source,
			StrategyDest:    strategyDest,
			Action:          d.Action,
			ActionConfig:    d.ActionConfig,
			StrategyStatus:  d.StrategyStatus,
			Priority:        d.Priority,
			TimeId:          d.TimeId,
			CreatedAt:       d.CreatedAt,
			UpdatedAt:       d.UpdatedAt,
			StartTime:       d.StartTime,
			EndTime:         d.EndTime,
			MatchCount:      d.MatchCount,
			MaxCountData:    d.MaxCountData,
		}
		// vo time gap
		data.TimeDetail = timeGap(d)
		// 健康度
		data.HealthTip = healthTip(data, now)
		// 时间是否过期
		data.StrategyTimeExpired = timeExpired(data.EndTime, now)
		voList = append(voList, data)
	}

	return voList, nil
}

func timeGap(data dto.StrategyListDto) *strategy_model.FactorTime {
	if data.TimeId == "" || data.FactorTime.Id == "" {
		return nil
	}
	return &strategy_model.FactorTime{
		Id:             data.FactorTime.Id,
		IntervalType:   data.IntervalType,
		Sunday:         data.Sunday,
		Monday:         data.Monday,
		Tuesday:        data.Tuesday,
		Wednesday:      data.Wednesday,
		Thursday:       data.Thursday,
		Friday:         data.Friday,
		Saturday:       data.Saturday,
		DailyStartTime: data.DailyStartTime,
		DailyEndTime:   data.DailyEndTime,
		AllDayEnable:   data.AllDayEnable,
		GapName:        data.GapName,
	}
}

// timeExpired 当生效的结束时间小于当前时间为过期
func timeExpired(endTime string, now time.Time) bool {
	if endTime == "" {
		return false
	}
	eTime, err := time.Parse(utils.DateLayout, endTime)
	if err != nil {
		return true
	}

	return eTime.Before(now)
}

func healthTip(data vo.GroupStrategyData, now time.Time) vo.HealthTip {
	tip := vo.HealthTip{}
	// 如果没有匹配记录，就把策略的创建时间作为最近一次匹配的时间
	if data.MaxCountData == nil || data.MaxCountData.IsZero() {
		data.MaxCountData = &data.CreatedAt
	}
	// 用显得时间减去匹配的时间 计算天数差
	duration := now.Sub(*data.MaxCountData).Hours() / 24
	if duration < 7 {
		return tip
	}
	if duration >= 7 && duration < 30 {
		tip.TipLevel = 2
		tip.Tip = "超过7天无匹配"
	} else if duration >= 30 {
		tip.TipLevel = 3
		tip.Tip = "超过30天无匹配"
	}
	return tip
}

// getMovePriority 获取移动分组的目标优先级
func (s strategyService) getMovePriority(c *gin.Context, moveReq vo.StrategyMoveReq) (dto.StrategyPriority, error) {
	strategyPriority := dto.StrategyPriority{}
	var destPriority float32
	if moveReq.DestPriority == 0 || moveReq.DestPriority == -1 {
		newPriority, err := s.db.GetStrategyPriorityByGroup(c, moveReq)
		if err != nil {
			return strategyPriority, err
		}
		// 如果组内没有策略,则通过新增的获取优先级方法 按照组所在的优先级分配一个优先级
		if newPriority == 0 {
			newPriority, err = s.db.GetPriorityByGroup(c, moveReq.GroupId)
			if err != nil {
				return strategyPriority, err
			}
		}

		if moveReq.DestPriority == 0 {
			destPriority = float32(newPriority) - 0.5
		} else {
			destPriority = float32(newPriority) + 0.5
		}
	} else {
		if moveReq.Where == consts.Before {
			destPriority = float32(moveReq.DestPriority) - 0.5
		} else {
			destPriority = float32(moveReq.DestPriority) + 0.5
		}
	}
	strategyPriority.Id = moveReq.StrategyId
	strategyPriority.Priority = destPriority
	return strategyPriority, nil
}

func (s strategyService) listMatchCount(c *gin.Context, list []dto.StrategyListDto) ([]dto.StrategyListDto, error) {
	var ids []int64
	for _, listDto := range list {
		id, err := strconv.ParseInt(listDto.Id, 10, 64)
		if err != nil {
			return nil, err
		}
		ids = append(ids, id)
	}
	listMatchCount, err := s.db.ListMatchCount(c, ids)
	if err != nil {
		return nil, err
	}
	if len(listMatchCount) <= 0 {
		return list, nil
	}
	m := make(map[string]dto.MatchCountDto, len(listMatchCount))
	for _, countDto := range listMatchCount {
		m[countDto.Id] = countDto
	}
	for i := range list {
		countDto, ok := m[list[i].Id]
		if ok {
			list[i].MatchCount = countDto.Count
			list[i].MaxCountData = countDto.MaxCountData
		}
	}
	return list, nil
}

func GetChangePriority(list []dto.StrategyPriority, changeGroup uint64) ([]model.AccessStrategy, model.AccessStrategy) {

	nowCopy := make(map[uint64]dto.StrategyPriority)
	for i := range list {
		nowCopy[list[i].Id] = list[i]
	}

	sort.SliceStable(list, func(i, j int) bool {
		return list[i].Priority < list[j].Priority
	})

	var needChangeList []model.AccessStrategy

	// 按照排序重新计算优先级
	for i := range list {
		list[i].Priority = float32(i + 1)
	}

	// 需要移动的id单独拎出来，可能需要改组id
	changeGroupStrategy := model.AccessStrategy{}
	// 计算优先级有变动的策略返回更新
	for i := range list {

		oldPriority := nowCopy[list[i].Id]

		if oldPriority.Priority != list[i].Priority {
			strategy := model.AccessStrategy{}
			strategy.ID = list[i].Id
			strategy.Priority = int(list[i].Priority)
			needChangeList = append(needChangeList, strategy)
			if list[i].Id == changeGroup {
				changeGroupStrategy = strategy
			}
		}

	}

	return needChangeList, changeGroupStrategy
}

func GetStrategyService() StrategyService {
	StrategyServiceInit.Do(func() {
		strategyServiceImpl = strategyService{db: repository.NewStrategyRepository()}
	})
	return strategyServiceImpl
}

type StrategyService interface {
	AddStrategy(c *gin.Context, req vo.AddStrategyReq) aerrors.AError
	UpdateStrategy(c *gin.Context, req vo.UpdateStrategyReq) aerrors.AError
	DelStrategy(c *gin.Context, req vo.DelStrategyReq) aerrors.AError
	EnableStrategy(c *gin.Context, req vo.EnableStrategyReq) aerrors.AError
	StrategyDetail(c *gin.Context, id string) (vo.StrategyDetailResp, aerrors.AError)
	ClearStrategyMatch(c *gin.Context, req vo.DelStrategyReq) aerrors.AError
	StrategySimpleList(c *gin.Context, groupId string) ([]vo.StrategySimpleListResp, aerrors.AError)
	StrategyMove(c *gin.Context, moveReq vo.StrategyMoveReq) aerrors.AError
	StrategyList(c *gin.Context, req vo.StrategyListReq) (vo.StrategyListResp, aerrors.AError)
}
