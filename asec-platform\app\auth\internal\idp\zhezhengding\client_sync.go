package zhezhengding

import (
	"encoding/json"
	"fmt"
	"strings"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
)

// ZhezhengdingClient 浙政钉客户端实现
type ZhezhengdingClient struct {
	host         string
	orgID        string
	clientID     string
	clientSecret string
	logger       *log.Helper
	execClient   *ExecutableClient
}

// GlobalConfigItem 全局配置项结构
type GlobalConfigItem struct {
	Key    string `json:"key"`
	Value  string `json:"value"`
	Remark string `json:"remark"`
}

// NewZhezhengdingClient 创建一个新的浙政钉客户端
func NewZhezhengdingClient(globalData string) (*ZhezhengdingClient, error) {
	var configItems []GlobalConfigItem
	if err := json.Unmarshal([]byte(globalData), &configItems); err != nil {
		return nil, fmt.Errorf("解析浙政钉配置失败: %w", err)
	}

	config := parseConfig(configItems)
	if config.AppURL == "" || config.AppKey == "" || config.AppSecret == "" {
		return nil, fmt.Errorf("浙政钉配置不完整，需要orgAppUrl、orgAppKey和orgAppSecret")
	}

	// 确保URL格式正确
	if !strings.HasPrefix(config.AppURL, "http") {
		config.AppURL = "https://" + config.AppURL
	}
	// 移除尾部斜杠
	config.AppURL = strings.TrimSuffix(config.AppURL, "/")

	logger := log.With(log.GetLogger(),
		"module", "zhezhengding_client",
		"app_url", config.AppURL)

	client := &ZhezhengdingClient{
		host:         config.AppURL,
		orgID:        config.OrgID,
		clientID:     config.AppKey,
		clientSecret: config.AppSecret,
		logger:       log.NewHelper(logger),
	}

	// 初始化ExecutableClient
	client.execClient = NewExecutableClient()
	client.execClient.SetDomain(client.host)
	client.execClient.SetAccessKey(client.clientID)
	client.execClient.SetSecretKey(client.clientSecret)

	return client, nil
}

// Config 包含浙政钉客户端的配置信息
type Config struct {
	AppURL    string
	AppKey    string
	AppSecret string
	OrgID     string
}

// parseConfig 从配置项中提取浙政钉配置
func parseConfig(items []GlobalConfigItem) Config {
	config := Config{}
	for _, item := range items {
		switch item.Key {
		case "appKey":
			config.AppKey = item.Value
		case "appSecret":
			config.AppSecret = item.Value
		case "appUrl":
			config.AppURL = item.Value
		case "orgId":
			config.OrgID = item.Value
		}
	}
	return config
}

// GetDeptsAndUsers 同时获取并关联部门和用户信息
func (c *ZhezhengdingClient) GetDeptsAndUsers(rootGroupId string) ([]*dto.ExternalDepartment, []*dto.ExternalUser, error) {
	// 1. 获取所有部门
	depts, err := c.getAllDepartments()
	if err != nil {
		return nil, nil, fmt.Errorf("获取浙政钉部门失败: %w", err)
	}
	c.logger.Infof("已获取浙政钉部门，总数: %d", len(depts))

	// 2. 获取所有用户
	users, err := c.getAllUsers(depts, rootGroupId)
	if err != nil {
		return nil, nil, fmt.Errorf("获取浙政钉用户失败: %w", err)
	}
	c.logger.Infof("已获取浙政钉用户，总数: %d", len(users))

	return depts, users, nil
}

// getAllDepartments 获取所有部门信息
func (c *ZhezhengdingClient) getAllDepartments() ([]*dto.ExternalDepartment, error) {
	// 设置tenantId参数
	c.execClient.ReSetParams()
	c.execClient.AddParameter("tenantId", c.orgID)

	// 1. 获取应用可见范围
	c.execClient.SetApiName("/auth/scopesV2")
	rootDeptResult, err := c.execClient.EpaasCurlPost(10)
	if err != nil {
		return nil, fmt.Errorf("获取部门可见范围失败: %w", err)
	}

	// 添加调试日志
	c.logger.Debugf("浙政钉API返回数据: %+v", rootDeptResult)

	// 安全地获取部门可见范围列表
	var deptVisibleScopes []interface{}
	if deptVisibleScopesVal, exists := rootDeptResult["deptVisibleScopes"]; exists && deptVisibleScopesVal != nil {
		deptVisibleScopes, _ = deptVisibleScopesVal.([]interface{})
	}

	// 检查是否获取到部门列表
	if deptVisibleScopes == nil || len(deptVisibleScopes) == 0 {
		// 尝试其他可能的字段名或路径
		if contentVal, exists := rootDeptResult["content"]; exists && contentVal != nil {
			content, ok := contentVal.(map[string]interface{})
			if ok && content["deptVisibleScopes"] != nil {
				deptVisibleScopes, _ = content["deptVisibleScopes"].([]interface{})
			}
		}

		// 如果仍然没有找到部门列表，创建一个包含顶级部门的列表
		if deptVisibleScopes == nil || len(deptVisibleScopes) == 0 {
			c.logger.Warnf("未找到部门可见范围，尝试使用根部门ID")

			// 尝试获取根组织列表
			c.execClient.ReSetParams()
			c.execClient.AddParameter("tenantId", c.orgID)
			c.execClient.SetApiName("/mozi/organization/getRootOrganization")

			rootOrgResult, rootErr := c.execClient.EpaasCurlPost(10)
			if rootErr == nil && rootOrgResult != nil && rootOrgResult["success"].(bool) && rootOrgResult["data"] != nil {
				rootData := rootOrgResult["data"].(map[string]interface{})
				if rootCode, exists := rootData["organizationCode"]; exists && rootCode != nil {
					deptVisibleScopes = []interface{}{rootCode}
					c.logger.Infof("使用根组织代码: %s", rootCode)
				}
			}

			// 如果仍然没有找到任何部门，返回错误
			if deptVisibleScopes == nil || len(deptVisibleScopes) == 0 {
				return nil, fmt.Errorf("未获取到可见部门范围且无法获取根部门")
			}
		}
	}

	c.logger.Infof("获取到 %d 个部门可见范围", len(deptVisibleScopes))

	// 2. 获取所有部门的完整信息
	allDeptCodes := make([]string, 0)
	for _, code := range deptVisibleScopes {
		allDeptCodes = append(allDeptCodes, code.(string))

		// c.logger.Infof("获取子部门[%s]开始", code.(string))
		// 获取子部门
		subCodes, err := c.getSubDepartmentCodes(code.(string))
		if err != nil {
			c.logger.Warnf("获取部门 %s 的子部门失败: %v", code, err)
			continue
		}

		allDeptCodes = append(allDeptCodes, subCodes...)
	}

	c.logger.Infof("获取所有部门完成，部门总数: %d", len(allDeptCodes))

	// 删除重复的部门代码
	uniqueCodes := make(map[string]bool)
	uniqueDeptCodes := make([]string, 0)
	for _, code := range allDeptCodes {
		if !uniqueCodes[code] {
			uniqueCodes[code] = true
			uniqueDeptCodes = append(uniqueDeptCodes, code)
		}
	}

	c.logger.Infof("删除重复的部门")

	// 3. 获取部门详细信息
	departments := make([]*dto.ExternalDepartment, 0)

	// 分批获取部门详情，每次最多50个
	batchSize := 50
	for i := 0; i < len(uniqueDeptCodes); i += batchSize {
		end := i + batchSize
		if end > len(uniqueDeptCodes) {
			end = len(uniqueDeptCodes)
		}

		batch := uniqueDeptCodes[i:end]

		// 获取这批部门的详情
		c.execClient.ReSetParams()
		c.execClient.AddParameter("tenantId", c.orgID)
		c.execClient.SetApiName("/mozi/organization/listOrganizationsByCodes")

		// 添加部门代码
		c.execClient.SetParameters("organizationCodes", batch)

		deptDetailResult, err := c.execClient.EpaasCurlPost(10)
		if err != nil {
			c.logger.Warnf("获取部门批次 %d-%d 详情失败: %v", i, end, err)
			continue
		}

		// 将获取到的部门添加到结果中
		if deptDetailResult == nil || !deptDetailResult["success"].(bool) || deptDetailResult["content"] == nil {
			c.logger.Warnf("部门批次 %d-%d 详情返回无效数据: %+v", i, end, deptDetailResult)
			continue
		}

		// 断言 content 为 map[string]interface{}
		content, ok := deptDetailResult["content"].(map[string]interface{})
		if !ok || content["data"] == nil {
			c.logger.Warnf("部门批次 %d-%d 详情中 content 或 data 字段缺失或类型错误", i, end)
			continue
		}

		// 将获取到的部门添加到结果中
		// 断言 data 为 []interface{}
		deptList, ok := content["data"].([]interface{})
		if !ok {
			c.logger.Warnf("部门批次 %d-%d 详情中 data 字段类型错误，无法转换为 []interface{}", i, end)
			continue
		}

		for _, dept := range deptList {
			deptMap := dept.(map[string]interface{})

			// 处理父部门ID，如果不在我们的列表中，设置为0（根部门）
			parentID := deptMap["parentCode"].(string)
			if parentID == "" || !uniqueCodes[parentID] {
				parentID = "0"
			}

			extDept := &dto.ExternalDepartment{
				ID:       deptMap["organizationCode"].(string),
				Name:     deptMap["organizationName"].(string),
				Parentid: parentID,
				UniqKey:  fmt.Sprintf("%s_%s_%s", deptMap["organizationCode"], deptMap["organizationName"], parentID),
			}

			c.logger.Infof("当前部门信息: %s, %s, %s", extDept.ID, extDept.Name, extDept.Parentid)
			departments = append(departments, extDept)
		}

		c.logger.Infof("完成部门本页部门列表: %d", len(deptList))
	}

	return departments, nil
}

// getSubDepartmentCodes 递归获取子部门代码
func (c *ZhezhengdingClient) getSubDepartmentCodes(parentCode string) ([]string, error) {
	allCodes := make([]string, 0)
	pageNo := 1
	pageSize := 100

	for {
		c.execClient.ReSetParams()
		c.execClient.AddParameter("tenantId", c.orgID)
		c.execClient.SetApiName("/mozi/organization/pageSubOrganizationCodes")
		c.execClient.SetParameter("organizationCode", parentCode)
		c.execClient.SetParameter("pageSize", fmt.Sprintf("%d", pageSize))
		c.execClient.SetParameter("pageNo", fmt.Sprintf("%d", pageNo))
		c.execClient.SetParameter("returnTotalSize", "true")

		result, err := c.execClient.EpaasCurlPost(10)
		if err != nil {
			return allCodes, err
		}

		// 将获取到的部门添加到结果中
		if result == nil || !result["success"].(bool) || result["content"] == nil {
			c.logger.Warnf("子部门%s返回无效数据: %+v", parentCode, result)
			break
		}

		// 断言 content 为 map[string]interface{}
		content, ok := result["content"].(map[string]interface{})
		if !ok || content["data"] == nil {
			// c.logger.Warnf("子部门%s批次详情中 content=>data 字段缺失或类型错误：%v", parentCode, content)
			break
		}

		codeList := content["data"].([]interface{})
		if len(codeList) == 0 {
			break
		}

		for _, code := range codeList {
			deptCode := code.(string)
			allCodes = append(allCodes, deptCode)

			// 递归获取子部门
			subCodes, err := c.getSubDepartmentCodes(deptCode)
			if err != nil {
				c.logger.Warnf("获取部门 %s 的子部门失败: %v", deptCode, err)
				continue
			}

			allCodes = append(allCodes, subCodes...)
		}

		// 检查是否还有更多页
		totalSize := int(content["totalSize"].(float64))
		if pageNo*pageSize >= totalSize {
			break
		}

		pageNo++
	}

	return allCodes, nil
}

// getAllUsers 获取所有用户信息
func (c *ZhezhengdingClient) getAllUsers(departments []*dto.ExternalDepartment, rootGroupId string) ([]*dto.ExternalUser, error) {
	allUsers := make([]*dto.ExternalUser, 0)
	// 使用map存储已处理过的用户，避免重复
	processedUsers := make(map[string]*dto.ExternalUser)
	// 存储用户所属的部门映射
	userDeptMap := make(map[string][]string)

	// 遍历每个部门获取用户
	for _, dept := range departments {
		// 获取部门下的用户列表
		userCodes, err := c.getUserCodes(dept.ID)
		if err != nil {
			c.logger.Warnf("获取部门 %s 的用户列表失败: %v", dept.ID, err)
			continue
		}

		// 获取每个用户的详细信息
		for _, userCode := range userCodes {
			// 检查是否已处理过该用户
			if _, exists := processedUsers[userCode]; exists {
				// 仅添加部门关联信息
				userDeptMap[userCode] = append(userDeptMap[userCode], dept.ID)
				continue
			}
			userInfo, err := c.getUserInfo(userCode)
			if err != nil {
				c.logger.Warnf("获取用户 %s 的详细信息失败: %v", userCode, err)
				continue
			}

			if userInfo == nil {
				continue
			}

			// 创建用户对象
			extUser := &dto.ExternalUser{
				Userid:           userInfo["employeeCode"].(string),
				Name:             userInfo["employeeName"].(string),
				MainDepartment:   dept.ID,
				Mobile:           "", // 浙政钉API可能不提供手机号
				Email:            "", // 浙政钉API可能不提供邮箱
				LocalRootGroupID: rootGroupId,
				UniqKey:          fmt.Sprintf("%s_%s", userInfo["employeeCode"], userInfo["employeeName"]),
			}

			// 记录用户所属部门
			userDeptMap[userCode] = []string{dept.ID}
			// 添加到已处理用户映射
			processedUsers[userCode] = extUser
			allUsers = append(allUsers, extUser)
		}
	}

	return allUsers, nil
}

// getUserCodes 获取部门下所有用户的代码
func (c *ZhezhengdingClient) getUserCodes(deptCode string) ([]string, error) {
	userCodes := make([]string, 0)
	pageNo := 1
	pageSize := 100

	for {
		c.execClient.ReSetParams()
		c.execClient.AddParameter("tenantId", c.orgID)
		c.execClient.SetApiName("/mozi/organization/pageOrganizationEmployeeCodes")
		c.execClient.SetParameter("organizationCode", deptCode)
		c.execClient.SetParameter("pageSize", fmt.Sprintf("%d", pageSize))
		c.execClient.SetParameter("pageNo", fmt.Sprintf("%d", pageNo))
		c.execClient.SetParameter("returnTotalSize", "true")
		c.execClient.SetParameter("employeePositionStatus", "A")

		result, err := c.execClient.EpaasCurlPost(10)
		if err != nil {
			return userCodes, err
		}

		// 将获取到的部门添加到结果中
		if result == nil || !result["success"].(bool) || result["content"] == nil {
			c.logger.Warnf("部门下用户返回无效数据: %+v", result)
			break
		}

		// 断言 content 为 map[string]interface{}
		content, ok := result["content"].(map[string]interface{})
		if !ok || content["data"] == nil {
			// c.logger.Warnf("部门下用户中 content 或 data 字段缺失或类型错误%s", deptCode)
			break
		}

		codeList := content["data"].([]interface{})
		for _, code := range codeList {
			userCodes = append(userCodes, code.(string))
		}

		// 检查是否还有更多页
		totalSize := int(content["totalSize"].(float64))
		if pageNo*pageSize >= totalSize {
			break
		}

		pageNo++
	}

	return userCodes, nil
}

// getUserInfo 获取用户详细信息
func (c *ZhezhengdingClient) getUserInfo(userCode string) (map[string]interface{}, error) {
	c.execClient.ReSetParams()
	c.execClient.AddParameter("tenantId", c.orgID)
	c.execClient.SetApiName("/mozi/employee/getEmployeeByCode")
	c.execClient.SetParameter("employeeCode", userCode)

	result, err := c.execClient.EpaasCurlPost(10)
	if err != nil {
		return nil, err
	}

	// 将获取用户信息添加到结果中
	if result == nil || !result["success"].(bool) || result["content"] == nil {
		return nil, fmt.Errorf("获取用户信息返回无效数据")
	}

	// 断言 content 为 map[string]interface{}
	content, ok := result["content"].(map[string]interface{})
	if !ok || content["data"] == nil {
		return nil, fmt.Errorf("获取用户信息返回无效数据  content 或 data 字段缺失或类型错误")
	}

	return content["data"].(map[string]interface{}), nil
}
