// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: appliance/v1/conf_center.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ConfCenterClient is the client API for ConfCenter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfCenterClient interface {
	// 配置是否更新
	SniffUpdate(ctx context.Context, in *SniffUpdateReq, opts ...grpc.CallOption) (*SniffUpdateResp, error)
	// 拉取配置
	PollConf(ctx context.Context, in *PollConfReq, opts ...grpc.CallOption) (*PollConfResp, error)
	// 同步配置类型
	SyncConfType(ctx context.Context, in *SyncConfTypeReq, opts ...grpc.CallOption) (*SyncConfTypeResp, error)
	// 删除某个配置
	DelConf(ctx context.Context, in *DelConfReq, opts ...grpc.CallOption) (*DelConfResp, error)
}

type confCenterClient struct {
	cc grpc.ClientConnInterface
}

func NewConfCenterClient(cc grpc.ClientConnInterface) ConfCenterClient {
	return &confCenterClient{cc}
}

func (c *confCenterClient) SniffUpdate(ctx context.Context, in *SniffUpdateReq, opts ...grpc.CallOption) (*SniffUpdateResp, error) {
	out := new(SniffUpdateResp)
	err := c.cc.Invoke(ctx, "/api.appliance.ConfCenter/SniffUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confCenterClient) PollConf(ctx context.Context, in *PollConfReq, opts ...grpc.CallOption) (*PollConfResp, error) {
	out := new(PollConfResp)
	err := c.cc.Invoke(ctx, "/api.appliance.ConfCenter/PollConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confCenterClient) SyncConfType(ctx context.Context, in *SyncConfTypeReq, opts ...grpc.CallOption) (*SyncConfTypeResp, error) {
	out := new(SyncConfTypeResp)
	err := c.cc.Invoke(ctx, "/api.appliance.ConfCenter/SyncConfType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confCenterClient) DelConf(ctx context.Context, in *DelConfReq, opts ...grpc.CallOption) (*DelConfResp, error) {
	out := new(DelConfResp)
	err := c.cc.Invoke(ctx, "/api.appliance.ConfCenter/DelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfCenterServer is the server API for ConfCenter service.
// All implementations must embed UnimplementedConfCenterServer
// for forward compatibility
type ConfCenterServer interface {
	// 配置是否更新
	SniffUpdate(context.Context, *SniffUpdateReq) (*SniffUpdateResp, error)
	// 拉取配置
	PollConf(context.Context, *PollConfReq) (*PollConfResp, error)
	// 同步配置类型
	SyncConfType(context.Context, *SyncConfTypeReq) (*SyncConfTypeResp, error)
	// 删除某个配置
	DelConf(context.Context, *DelConfReq) (*DelConfResp, error)
	mustEmbedUnimplementedConfCenterServer()
}

// UnimplementedConfCenterServer must be embedded to have forward compatible implementations.
type UnimplementedConfCenterServer struct {
}

func (UnimplementedConfCenterServer) SniffUpdate(context.Context, *SniffUpdateReq) (*SniffUpdateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SniffUpdate not implemented")
}
func (UnimplementedConfCenterServer) PollConf(context.Context, *PollConfReq) (*PollConfResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PollConf not implemented")
}
func (UnimplementedConfCenterServer) SyncConfType(context.Context, *SyncConfTypeReq) (*SyncConfTypeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncConfType not implemented")
}
func (UnimplementedConfCenterServer) DelConf(context.Context, *DelConfReq) (*DelConfResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelConf not implemented")
}
func (UnimplementedConfCenterServer) mustEmbedUnimplementedConfCenterServer() {}

// UnsafeConfCenterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfCenterServer will
// result in compilation errors.
type UnsafeConfCenterServer interface {
	mustEmbedUnimplementedConfCenterServer()
}

func RegisterConfCenterServer(s grpc.ServiceRegistrar, srv ConfCenterServer) {
	s.RegisterService(&ConfCenter_ServiceDesc, srv)
}

func _ConfCenter_SniffUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SniffUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfCenterServer).SniffUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ConfCenter/SniffUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfCenterServer).SniffUpdate(ctx, req.(*SniffUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfCenter_PollConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PollConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfCenterServer).PollConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ConfCenter/PollConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfCenterServer).PollConf(ctx, req.(*PollConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfCenter_SyncConfType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncConfTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfCenterServer).SyncConfType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ConfCenter/SyncConfType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfCenterServer).SyncConfType(ctx, req.(*SyncConfTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfCenter_DelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfCenterServer).DelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ConfCenter/DelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfCenterServer).DelConf(ctx, req.(*DelConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ConfCenter_ServiceDesc is the grpc.ServiceDesc for ConfCenter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConfCenter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.appliance.ConfCenter",
	HandlerType: (*ConfCenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SniffUpdate",
			Handler:    _ConfCenter_SniffUpdate_Handler,
		},
		{
			MethodName: "PollConf",
			Handler:    _ConfCenter_PollConf_Handler,
		},
		{
			MethodName: "SyncConfType",
			Handler:    _ConfCenter_SyncConfType_Handler,
		},
		{
			MethodName: "DelConf",
			Handler:    _ConfCenter_DelConf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "appliance/v1/conf_center.proto",
}
