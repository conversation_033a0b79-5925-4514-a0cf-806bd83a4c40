package access_log

import (
	"sync"

	pb "asdsec.com/asec/platform/api/accesslog/v1"
)

var (
	mu     = &sync.Mutex{}
	buf    = [8192]*pb.AccessLogMsg{}
	offset = 0
)

func WriteLogRecord(rec *pb.AccessLogMsg) {
	mu.Lock()
	if offset < len(buf) {
		buf[offset] = rec
		offset++
	}
	mu.Unlock()
	return
}

func ReadLogRecords() (ret []*pb.AccessLogMsg) {
	mu.Lock()
	ret = make([]*pb.AccessLogMsg, offset)
	copy(ret, buf[:offset])
	offset = 0
	mu.Unlock()
	return
}
