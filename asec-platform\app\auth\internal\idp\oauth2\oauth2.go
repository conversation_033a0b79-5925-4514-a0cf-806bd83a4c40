package oauth2

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp/webauth"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
)

// OAuth2Provider 实现多步OAuth2认证
type OAuth2Provider struct {
	providerId string
	name       string
	executor   *webauth.StepExecutor
	logger     *log.Helper

	// OAuth2配置
	globalVars    map[string]string
	codeConfig    webauth.Step
	userConfigs   []webauth.Step
	logoutConfig  webauth.Step
	logoutEnabled bool
	callbackURL   string
	// 测试模式
	isTestMode bool // 是否为测试模式
}

// NewOAuth2Provider 创建OAuth2提供商实例
func NewOAuth2Provider(
	providerId string,
	name string,
	globalData string,
	codeData string,
	userData string,
	logoutData string,
	logoutEnabled bool,
	callbackURL string,
	logger log.Logger,
) (*OAuth2Provider, error) {
	// 解析全局变量
	var globalVars map[string]string
	if err := parseGlobalData(globalData, &globalVars); err != nil {
		return nil, err
	}

	// 解析授权码配置
	var codeConfig webauth.Step
	if err := json.Unmarshal([]byte(codeData), &codeConfig); err != nil {
		return nil, fmt.Errorf("解析授权码配置失败: %v", err)
	}

	// 解析用户信息配置
	var userConfigs []webauth.Step
	if err := json.Unmarshal([]byte(userData), &userConfigs); err != nil {
		return nil, fmt.Errorf("解析用户信息配置失败: %v", err)
	}

	// 解析登出配置
	var logoutConfig webauth.Step
	if logoutData != "" {
		if err := json.Unmarshal([]byte(logoutData), &logoutConfig); err != nil {
			return nil, fmt.Errorf("解析登出配置失败: %v", err)
		}
	}

	// 创建执行器
	executor := webauth.NewStepExecutor(logger)
	executor.SetGlobalVars(globalVars)

	return &OAuth2Provider{
		providerId:    providerId,
		name:          name,
		executor:      executor,
		logger:        log.NewHelper(logger),
		globalVars:    globalVars,
		codeConfig:    codeConfig,
		userConfigs:   userConfigs,
		logoutConfig:  logoutConfig,
		logoutEnabled: logoutEnabled,
		callbackURL:   callbackURL,
	}, nil
}

// GetAuthorizationURL 获取授权URL
func (p *OAuth2Provider) GetAuthorizationURL(ctx context.Context, state string, redirectURI string, isTest bool) (string, error) {
	// 设置测试模式标记
	p.isTestMode = isTest

	// 设置状态和回调URL
	extraVars := map[string]string{
		"Global.state":        state,
		"Global.redirect_uri": redirectURI,
		"Env.RedirectUri":     redirectURI,
	}

	if isTest {
		extraVars["Global.istest"] = "true" // 添加测试模式标记
	}

	// 添加全局变量
	for k, v := range p.globalVars {
		extraVars["Global."+k] = v
	}
	// 创建codeConfig的副本进行处理
	gets := p.codeConfig.Input.Gets
	if p.codeConfig.Input.OpenScript && p.codeConfig.Input.Script != "" {
		p.logger.Infof("执行获取code阶段预处理脚本")

		globalMap := make(map[string]interface{})
		for k, v := range p.globalVars {
			globalMap[k] = v
		}

		// 构建脚本环境
		env := map[string]interface{}{
			"user":   map[string]interface{}{},
			"global": globalMap,
		}

		// 将extraVars合并到env中
		for k, v := range extraVars {
			parts := strings.SplitN(k, ".", 2)
			if len(parts) == 2 {
				if parts[0] == "Global" {
					env["global"].(map[string]interface{})[parts[1]] = v
				}
			}
		}

		// 执行专门的授权URL参数处理脚本
		updatedGets, err := p.executor.ExecuteAuthUrlScript(p.codeConfig.Input.Script, env, gets)
		if err != nil {
			p.logger.Errorf("执行获取code阶段预处理脚本失败: %v", err)
			return "", fmt.Errorf("执行获取code阶段预处理脚本失败: %w", err)
		}
		gets = updatedGets
	}

	// 先替换基础URL中的变量
	baseURL := common.ReplaceEnvVars(ctx, p.codeConfig.URL, extraVars)

	// 构建查询参数，同时替换其中的变量
	queryParams := make([]string, 0)
	hasStateParam := false
	for _, param := range gets {
		key := param.Key
		value := common.ReplaceEnvVars(ctx, param.Value, extraVars)

		// 检查是否仍包含未替换的变量
		if strings.Contains(value, "{User.") || strings.Contains(value, "{Global.") {
			// 记录原始值用于日志
			originalValue := value

			// 使用正则表达式查找并替换所有未处理的变量
			re := regexp.MustCompile(`\{(User|Global)\.[^}]+\}`)
			matches := re.FindAllString(value, -1)

			if len(matches) > 0 {
				// 记录找到的未替换变量
				for _, match := range matches {
					p.logger.Infof("授权URL参数中检测到未替换的变量: %s", match)
				}

				// 替换所有未替换变量为空字符串
				value = re.ReplaceAllString(value, "")
				p.logger.Infof("已将参数中未替换的变量替换为空值: %s -> %s", originalValue, value)
			}
		}

		queryParams = append(queryParams, fmt.Sprintf("%s=%s", key, url.QueryEscape(value)))

		// 检查是否包含state参数
		if key == "state" {
			hasStateParam = true
		}
	}
	// 如果配置中没有state参数，手动添加它
	if state != "" && !hasStateParam {
		queryParams = append(queryParams, fmt.Sprintf("state=%s", url.QueryEscape(state)))
		p.logger.Infof("手动添加state参数: %s", state)
	}

	// 组合最终URL
	finalURL := baseURL
	if len(queryParams) > 0 {
		if strings.Contains(baseURL, "?") {
			finalURL += "&"
		} else {
			finalURL += "?"
		}
		finalURL += strings.Join(queryParams, "&")
	}

	if isTest {
		p.logger.Infof("测试模式,生成测试专用授权URL: %s", finalURL)
		return finalURL, nil
	}

	p.logger.Infof("生成授权URL: %s", finalURL)
	return finalURL, nil
}

// HandleCallback 处理OAuth2回调
func (p *OAuth2Provider) HandleCallback(ctx context.Context, code string, state string) (*dto.ExternalUser, error) {
	// 1. 获取所有传递的参数
	allParams := make(map[string]string)

	// 从传输上下文中获取AuthWeb.AuthParams
	if tr, ok := transport.FromServerContext(ctx); ok {
		if header := tr.RequestHeader().Get("X-Auth-Params"); header != "" {
			if err := json.Unmarshal([]byte(header), &allParams); err == nil {
				p.logger.Debugf("从请求头获取了%d个参数", len(allParams))
			}
		}
	}

	// 基本参数合并
	if code != "" {
		allParams["code"] = code
	}
	if state != "" {
		allParams["state"] = state
	}

	// 2. 设置全局变量
	globalVars := p.prepareGlobalVariables(code, state)
	p.executor.SetGlobalVars(globalVars)

	// 3. 根据codeConfig.Output.Params映射设置用户变量
	if len(p.codeConfig.Output.Params) > 0 {
		p.logger.Infof("开始根据配置映射OAuth2参数，共%d个映射项", len(p.codeConfig.Output.Params))

		for _, param := range p.codeConfig.Output.Params {
			// 尝试从收集到的参数中获取值
			if value, exists := allParams[param.Key]; exists {
				// 提取映射的目标字段名
				fieldName := strings.TrimPrefix(param.Select, "{User.")
				fieldName = strings.TrimSuffix(fieldName, "}")

				// 设置用户变量
				p.executor.SetUserInfo(fieldName, value)
				p.logger.Infof("设置用户变量: %s = %s (从参数 %s)", fieldName, value, param.Key)

				// 同时更新全局变量
				globalVars[fieldName] = value
			} else {
				p.logger.Warnf("未找到配置的参数: %s", param.Key)
			}
		}
	} else {
		p.logger.Infof("无参数映射配置，使用默认code和state参数")
		// 设置默认用户变量
		p.executor.SetUserInfo("AuthCode", code)
		p.executor.SetUserInfo("State", state)
	}

	// 确保关键参数存在
	if _, hasCode := p.executor.GetUserInfo()["AuthCode"]; !hasCode {
		p.executor.SetUserInfo("AuthCode", code)
	}

	// 4. 更新全局变量
	p.executor.SetGlobalVars(globalVars)

	// 5. 执行所有配置的步骤
	for i, step := range p.userConfigs {
		// 处理变量替换
		processedStep := p.executor.PreprocessStep(step)

		// 执行步骤
		if err := p.executor.ExecuteStepOne(ctx, processedStep, i); err != nil {
			p.logger.Errorf("执行步骤 %d 失败: %v", i+1, err)
			return nil, err
		}
	}

	// 6. 将收集到的用户信息转换为标准格式
	return p.executor.ConvertToExternalUser(), nil
}

// TestAuth 专用于测试OAuth2配置的方法
func (p *OAuth2Provider) TestAuth(ctx context.Context) (map[string]interface{}, error) {
	// 设置为测试模式
	p.isTestMode = true
	testId, _ := common.GetTestIDFromCtx(ctx)
	idpId, _ := common.GetIdpIDFromCtx(ctx)

	// 创建包含测试标识的state
	testState := dto.SsoState{
		IsTest: "true",
		TestId: testId,
		Time:   fmt.Sprintf("%d", time.Now().Unix()),
	}
	jsonData, err := json.Marshal(testState)
	if err != nil {
		p.logger.Errorf("测试构建state失败: %v", err)
		return nil, fmt.Errorf("测试构建state失败: %v", err)
	}
	encodedState := base64.StdEncoding.EncodeToString([]byte(jsonData))

	// 生成授权URL
	if p.callbackURL == "" {
		scheme := common.GetClientScheme(ctx)
		hostPort := common.GetClientHostPortWithConfig(ctx)
		p.callbackURL = fmt.Sprintf("%s://%s/auth/login/v1/callback/%s", scheme, hostPort, idpId)
		p.logger.Infof("测试模式生成重定向URI: %s", p.callbackURL)
	}
	authURL, err := p.GetAuthorizationURL(ctx, encodedState, p.callbackURL, true)
	if err != nil {
		p.logger.Errorf("测试构建授权URL失败: %v", err)
		return nil, fmt.Errorf("测试授权URL构建失败: %v", err)
	}

	// 构建测试结果
	testResult := map[string]interface{}{
		"auth_url": authURL,
		"state":    encodedState,
		"test_id":  testId,
		"success":  false, // 初始为false，回调成功后设置为true
		"logs": []string{
			"┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
			"┃ 测试已开始,已生成授权URL,等待用户授权...",
			"┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
		},
	}

	return testResult, nil
}

// prepareGlobalVariables 准备OAuth2回调所需的全局变量
func (p *OAuth2Provider) prepareGlobalVariables(code string, state string) map[string]string {
	// 创建一个新的映射，复制现有全局变量
	globalVars := make(map[string]string)
	for k, v := range p.globalVars {
		globalVars[k] = v
	}

	// 添加授权码和状态
	globalVars["code"] = code
	globalVars["state"] = state
	globalVars["redirect_uri"] = p.callbackURL

	// 同时将回调地址设置为环境变量
	p.executor.SetEnvVar("RedirectUri", p.callbackURL)

	return globalVars
}

// Logout 注销用户
func (p *OAuth2Provider) Logout(ctx context.Context, token string) error {
	if !p.logoutEnabled {
		return nil
	}

	// 确保登出配置已设置
	if p.logoutConfig.URL == "" {
		p.logger.Errorf("登出配置无效: URL为空")
		return fmt.Errorf("登出配置无效")
	}

	// 设置响应类型为文本处理
	if p.logoutConfig.Output.Type == 0 { // 如果是JSON类型
		// 修改为文本类型，因为许多OAuth2登出响应是HTML页面
		p.logoutConfig.Output.Type = 3 // 3表示文本类型
		p.logger.Infof("将登出响应处理类型修改为文本，以兼容HTML响应")
	}

	// 使用标准的步骤执行流程
	p.executor.SetSteps([]webauth.Step{p.logoutConfig})
	err := p.executor.Execute(ctx)

	return err
}

// RefreshToken 刷新令牌
func (p *OAuth2Provider) RefreshToken(ctx context.Context, refreshToken string) (string, error) {
	// OAuth2刷新令牌实现
	return "", fmt.Errorf("刷新令牌功能未实现")
}

// 同步用户和部门信息的方法
func (p *OAuth2Provider) GetAllDepts() ([]*dto.ExternalDepartment, error) {

	return []*dto.ExternalDepartment{}, nil
}

func (p *OAuth2Provider) GetAllUsers() ([]*dto.ExternalUser, error) {

	return []*dto.ExternalUser{}, nil
}

// parseGlobalData 解析全局变量配置
func parseGlobalData(data string, result *map[string]string) error {
	var items []struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	// fmt.Println("全局变量数据:", data)
	if err := json.Unmarshal([]byte(data), &items); err != nil {
		return fmt.Errorf("解析全局变量失败: %v", err)
	}

	vars := make(map[string]string)
	for _, item := range items {
		vars[item.Key] = item.Value
	}

	*result = vars
	return nil
}

func (p *OAuth2Provider) LogoutWithIdToken(ctx context.Context, token, idToken string) error {
	// 设置请求参数
	p.executor.SetUserInfo("Token", token)
	p.executor.SetUserInfo("IdToken", idToken) // 添加ID Token

	// 执行登出步骤
	return p.executor.Execute(ctx)
}

func (p *OAuth2Provider) SetLogoutCredentials(token string) {
	p.executor.SetUserInfo("Token", token)
	p.executor.SetUserInfo("id_token", token)
}

// convertToString 将任意类型转换为字符串
func (p *OAuth2Provider) convertToString(val interface{}) string {
	if val == nil {
		return ""
	}

	switch v := val.(type) {
	case string:
		return v
	case float64:
		// 对于整数形式的浮点数，去掉小数部分
		if v == float64(int64(v)) {
			return fmt.Sprintf("%d", int64(v))
		}
		return fmt.Sprintf("%g", v)
	case int, int64, int32, int8:
		return fmt.Sprintf("%d", v)
	case bool:
		return fmt.Sprintf("%t", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

func (p *OAuth2Provider) GetExecutor() *webauth.StepExecutor {
	return p.executor
}
