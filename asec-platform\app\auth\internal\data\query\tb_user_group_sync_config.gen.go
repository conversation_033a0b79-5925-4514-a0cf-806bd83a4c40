// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserGroupSyncConfig(db *gorm.DB, opts ...gen.DOOption) tbUserGroupSyncConfig {
	_tbUserGroupSyncConfig := tbUserGroupSyncConfig{}

	_tbUserGroupSyncConfig.tbUserGroupSyncConfigDo.UseDB(db, opts...)
	_tbUserGroupSyncConfig.tbUserGroupSyncConfigDo.UseModel(&model.TbUserGroupSyncConfig{})

	tableName := _tbUserGroupSyncConfig.tbUserGroupSyncConfigDo.TableName()
	_tbUserGroupSyncConfig.ALL = field.NewAsterisk(tableName)
	_tbUserGroupSyncConfig.SyncID = field.NewString(tableName, "sync_id")
	_tbUserGroupSyncConfig.Key = field.NewString(tableName, "key")
	_tbUserGroupSyncConfig.Value = field.NewString(tableName, "value")
	_tbUserGroupSyncConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_tbUserGroupSyncConfig.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbUserGroupSyncConfig.fillFieldMap()

	return _tbUserGroupSyncConfig
}

type tbUserGroupSyncConfig struct {
	tbUserGroupSyncConfigDo tbUserGroupSyncConfigDo

	ALL       field.Asterisk
	SyncID    field.String
	Key       field.String
	Value     field.String
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (t tbUserGroupSyncConfig) Table(newTableName string) *tbUserGroupSyncConfig {
	t.tbUserGroupSyncConfigDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserGroupSyncConfig) As(alias string) *tbUserGroupSyncConfig {
	t.tbUserGroupSyncConfigDo.DO = *(t.tbUserGroupSyncConfigDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserGroupSyncConfig) updateTableName(table string) *tbUserGroupSyncConfig {
	t.ALL = field.NewAsterisk(table)
	t.SyncID = field.NewString(table, "sync_id")
	t.Key = field.NewString(table, "key")
	t.Value = field.NewString(table, "value")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbUserGroupSyncConfig) WithContext(ctx context.Context) *tbUserGroupSyncConfigDo {
	return t.tbUserGroupSyncConfigDo.WithContext(ctx)
}

func (t tbUserGroupSyncConfig) TableName() string { return t.tbUserGroupSyncConfigDo.TableName() }

func (t tbUserGroupSyncConfig) Alias() string { return t.tbUserGroupSyncConfigDo.Alias() }

func (t *tbUserGroupSyncConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserGroupSyncConfig) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["sync_id"] = t.SyncID
	t.fieldMap["key"] = t.Key
	t.fieldMap["value"] = t.Value
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbUserGroupSyncConfig) clone(db *gorm.DB) tbUserGroupSyncConfig {
	t.tbUserGroupSyncConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserGroupSyncConfig) replaceDB(db *gorm.DB) tbUserGroupSyncConfig {
	t.tbUserGroupSyncConfigDo.ReplaceDB(db)
	return t
}

type tbUserGroupSyncConfigDo struct{ gen.DO }

func (t tbUserGroupSyncConfigDo) Debug() *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserGroupSyncConfigDo) WithContext(ctx context.Context) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserGroupSyncConfigDo) ReadDB() *tbUserGroupSyncConfigDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserGroupSyncConfigDo) WriteDB() *tbUserGroupSyncConfigDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserGroupSyncConfigDo) Session(config *gorm.Session) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserGroupSyncConfigDo) Clauses(conds ...clause.Expression) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserGroupSyncConfigDo) Returning(value interface{}, columns ...string) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserGroupSyncConfigDo) Not(conds ...gen.Condition) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserGroupSyncConfigDo) Or(conds ...gen.Condition) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserGroupSyncConfigDo) Select(conds ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserGroupSyncConfigDo) Where(conds ...gen.Condition) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserGroupSyncConfigDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserGroupSyncConfigDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserGroupSyncConfigDo) Order(conds ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserGroupSyncConfigDo) Distinct(cols ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserGroupSyncConfigDo) Omit(cols ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserGroupSyncConfigDo) Join(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserGroupSyncConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserGroupSyncConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserGroupSyncConfigDo) Group(cols ...field.Expr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserGroupSyncConfigDo) Having(conds ...gen.Condition) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserGroupSyncConfigDo) Limit(limit int) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserGroupSyncConfigDo) Offset(offset int) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserGroupSyncConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserGroupSyncConfigDo) Unscoped() *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserGroupSyncConfigDo) Create(values ...*model.TbUserGroupSyncConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserGroupSyncConfigDo) CreateInBatches(values []*model.TbUserGroupSyncConfig, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserGroupSyncConfigDo) Save(values ...*model.TbUserGroupSyncConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserGroupSyncConfigDo) First() (*model.TbUserGroupSyncConfig, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncConfig), nil
	}
}

func (t tbUserGroupSyncConfigDo) Take() (*model.TbUserGroupSyncConfig, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncConfig), nil
	}
}

func (t tbUserGroupSyncConfigDo) Last() (*model.TbUserGroupSyncConfig, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncConfig), nil
	}
}

func (t tbUserGroupSyncConfigDo) Find() ([]*model.TbUserGroupSyncConfig, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserGroupSyncConfig), err
}

func (t tbUserGroupSyncConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserGroupSyncConfig, err error) {
	buf := make([]*model.TbUserGroupSyncConfig, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserGroupSyncConfigDo) FindInBatches(result *[]*model.TbUserGroupSyncConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserGroupSyncConfigDo) Attrs(attrs ...field.AssignExpr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserGroupSyncConfigDo) Assign(attrs ...field.AssignExpr) *tbUserGroupSyncConfigDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserGroupSyncConfigDo) Joins(fields ...field.RelationField) *tbUserGroupSyncConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserGroupSyncConfigDo) Preload(fields ...field.RelationField) *tbUserGroupSyncConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserGroupSyncConfigDo) FirstOrInit() (*model.TbUserGroupSyncConfig, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncConfig), nil
	}
}

func (t tbUserGroupSyncConfigDo) FirstOrCreate() (*model.TbUserGroupSyncConfig, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncConfig), nil
	}
}

func (t tbUserGroupSyncConfigDo) FindByPage(offset int, limit int) (result []*model.TbUserGroupSyncConfig, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserGroupSyncConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserGroupSyncConfigDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserGroupSyncConfigDo) Delete(models ...*model.TbUserGroupSyncConfig) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserGroupSyncConfigDo) withDO(do gen.Dao) *tbUserGroupSyncConfigDo {
	t.DO = *do.(*gen.DO)
	return t
}
