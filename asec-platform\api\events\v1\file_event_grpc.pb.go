// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: events/v1/file_event.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Event_CreateLog_FullMethodName = "/api.asdsec.file_event.Event/CreateLog"
)

// EventClient is the client API for Event service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EventClient interface {
	CreateLog(ctx context.Context, opts ...grpc.CallOption) (Event_CreateLogClient, error)
}

type eventClient struct {
	cc grpc.ClientConnInterface
}

func NewEventClient(cc grpc.ClientConnInterface) EventClient {
	return &eventClient{cc}
}

func (c *eventClient) CreateLog(ctx context.Context, opts ...grpc.CallOption) (Event_CreateLogClient, error) {
	stream, err := c.cc.NewStream(ctx, &Event_ServiceDesc.Streams[0], Event_CreateLog_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &eventCreateLogClient{stream}
	return x, nil
}

type Event_CreateLogClient interface {
	Send(*FileEventReq) error
	CloseAndRecv() (*Reply, error)
	grpc.ClientStream
}

type eventCreateLogClient struct {
	grpc.ClientStream
}

func (x *eventCreateLogClient) Send(m *FileEventReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *eventCreateLogClient) CloseAndRecv() (*Reply, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(Reply)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// EventServer is the server API for Event service.
// All implementations must embed UnimplementedEventServer
// for forward compatibility
type EventServer interface {
	CreateLog(Event_CreateLogServer) error
	mustEmbedUnimplementedEventServer()
}

// UnimplementedEventServer must be embedded to have forward compatible implementations.
type UnimplementedEventServer struct {
}

func (UnimplementedEventServer) CreateLog(Event_CreateLogServer) error {
	return status.Errorf(codes.Unimplemented, "method CreateLog not implemented")
}
func (UnimplementedEventServer) mustEmbedUnimplementedEventServer() {}

// UnsafeEventServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EventServer will
// result in compilation errors.
type UnsafeEventServer interface {
	mustEmbedUnimplementedEventServer()
}

func RegisterEventServer(s grpc.ServiceRegistrar, srv EventServer) {
	s.RegisterService(&Event_ServiceDesc, srv)
}

func _Event_CreateLog_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(EventServer).CreateLog(&eventCreateLogServer{stream})
}

type Event_CreateLogServer interface {
	SendAndClose(*Reply) error
	Recv() (*FileEventReq, error)
	grpc.ServerStream
}

type eventCreateLogServer struct {
	grpc.ServerStream
}

func (x *eventCreateLogServer) SendAndClose(m *Reply) error {
	return x.ServerStream.SendMsg(m)
}

func (x *eventCreateLogServer) Recv() (*FileEventReq, error) {
	m := new(FileEventReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// Event_ServiceDesc is the grpc.ServiceDesc for Event service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Event_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.asdsec.file_event.Event",
	HandlerType: (*EventServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CreateLog",
			Handler:       _Event_CreateLog_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "events/v1/file_event.proto",
}
