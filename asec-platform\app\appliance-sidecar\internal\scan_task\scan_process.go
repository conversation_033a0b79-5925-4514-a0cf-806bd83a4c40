package scan_task

import (
	v1 "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"context"
	"google.golang.org/grpc"
	"strconv"
	"sync"
)

const taskScanQuerySql = `select 
		   tb_task_result.id,
		   tb_task_result.task_id,
		   task_status,
		   host_name,
		   L1,
		   L2,
		   L3,
		   L4,
		   scan_count,
		   CASE WHEN GROUP_CONCAT(ttd.file_result_id) IS NULL THEN '' ELSE GROUP_CONCAT(ttd.file_result_id) END AS file_result_ids 
		from tb_task_result
		left join tb_task_details ttd on ttd.task_id = tb_task_result.task_id
		where tb_task_result.id > ? and task_status in ('file_scanning')
		GROUP by tb_task_result.task_id
		limit ?`

const processScanDuration = 30

func SendScanTaskAlways(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   doSendScanScanTask,
		RunType:      common.SimpleSend,
		WaitSecond:   processScanDuration,
		RandomOffset: 2,
	}
	common.Send(param)
}

func doSendScanScanTask(conn *grpc.ClientConn, ctx context.Context) error {
	if !common.DdrSwitch {
		return nil
	}
	stmt, err := global.TaskSqliteClient.Prepare(taskScanQuerySql)
	if stmt != nil {
		defer stmt.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("doSendScanTask SqliteClient Prepare:%v", err)
		return err
	}
	currentOffset, err := common.GetOffset(common.ScanTaskUploadOffset)
	if err != nil {
		global.Logger.Sugar().Errorln("get current offset for scan task err:%v", err)
		return err
	}

	rows, err := stmt.Query(currentOffset, common.Limit)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("stmt Query scan task err:%v", err)
		return err
	}

	tasks := v1.UpsetScanTaskReq{}
	userInfo := user.GetUserInfo()
	applianceId := strconv.FormatUint(global.ApplianceID, 10)
	for rows.Next() {
		var task v1.ScanTaskResult
		//当前上传扫描任务offset
		var curId int64
		var fileResultIdsStr string
		err = rows.Scan(&curId, &task.TaskId, &task.TaskStatus, &task.AgentName, &task.L1FileCount, &task.L2FileCount, &task.L3FileCount,
			&task.L4FileCount, &task.ScanCount, &fileResultIdsStr)
		if err != nil {
			global.Logger.Sugar().Errorln("doSendTask rows Scan err:%v", err)
		}

		task.UserId = userInfo.UserId
		task.UserName = userInfo.UserName
		task.AgentId = applianceId
		tasks.TaskList = append(tasks.TaskList, &task)
	}
	if len(tasks.TaskList) > 0 {
		_, err := v1.NewScanTaskClient(conn).UpsetScanTask(ctx, &tasks)
		if err != nil {
			global.Logger.Sugar().Errorf("task send close err:%v", err)
		}
	}
	return nil
}
