// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserSessionTracker(db *gorm.DB, opts ...gen.DOOption) tbUserSessionTracker {
	_tbUserSessionTracker := tbUserSessionTracker{}

	_tbUserSessionTracker.tbUserSessionTrackerDo.UseDB(db, opts...)
	_tbUserSessionTracker.tbUserSessionTrackerDo.UseModel(&model.TbUserSessionTracker{})

	tableName := _tbUserSessionTracker.tbUserSessionTrackerDo.TableName()
	_tbUserSessionTracker.ALL = field.NewAsterisk(tableName)
	_tbUserSessionTracker.ID = field.NewString(tableName, "id")
	_tbUserSessionTracker.CorpID = field.NewString(tableName, "corp_id")
	_tbUserSessionTracker.UserID = field.NewString(tableName, "user_id")
	_tbUserSessionTracker.ClientType = field.NewString(tableName, "client_type")
	_tbUserSessionTracker.ClientCategory = field.NewString(tableName, "client_category")
	_tbUserSessionTracker.JwtID = field.NewString(tableName, "jwt_id")
	_tbUserSessionTracker.RefreshJwtID = field.NewString(tableName, "refresh_jwt_id")
	_tbUserSessionTracker.DeviceID = field.NewString(tableName, "device_id")
	_tbUserSessionTracker.IPAddress = field.NewString(tableName, "ip_address")
	_tbUserSessionTracker.Status = field.NewString(tableName, "status")
	_tbUserSessionTracker.KickReason = field.NewString(tableName, "kick_reason")
	_tbUserSessionTracker.LoginTime = field.NewTime(tableName, "login_time")
	_tbUserSessionTracker.LastActiveTime = field.NewTime(tableName, "last_active_time")
	_tbUserSessionTracker.ExpiresAt = field.NewTime(tableName, "expires_at")
	_tbUserSessionTracker.LogoutTime = field.NewTime(tableName, "logout_time")
	_tbUserSessionTracker.CreatedAt = field.NewTime(tableName, "created_at")
	_tbUserSessionTracker.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbUserSessionTracker.fillFieldMap()

	return _tbUserSessionTracker
}

type tbUserSessionTracker struct {
	tbUserSessionTrackerDo tbUserSessionTrackerDo

	ALL            field.Asterisk
	ID             field.String // 会话跟踪记录唯一ID
	CorpID         field.String // 企业ID
	UserID         field.String // 用户ID
	ClientType     field.String // 详细的操作系统类型: windows, macos, linux, android, ios, etc.
	ClientCategory field.String // 抽象客户端分类: pc/mobile
	JwtID          field.String // access token的jti字段，用于关联现有黑名单
	RefreshJwtID   field.String // refresh token的jti字段，用于拉黑refresh token
	DeviceID       field.String // 设备ID，用于标识唯一设备
	IPAddress      field.String // 登录IP地址（支持IPv4和IPv6）
	Status         field.String // 会话状态: active(活跃)/kicked(被踢出)/expired(已过期)/logout(主动登出)
	KickReason     field.String // 踢出原因
	LoginTime      field.Time   // 登录时间
	LastActiveTime field.Time   // 最后活跃时间
	ExpiresAt      field.Time   // JWT过期时间
	LogoutTime     field.Time   // 登出时间（踢出/主动登出）
	CreatedAt      field.Time
	UpdatedAt      field.Time

	fieldMap map[string]field.Expr
}

func (t tbUserSessionTracker) Table(newTableName string) *tbUserSessionTracker {
	t.tbUserSessionTrackerDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserSessionTracker) As(alias string) *tbUserSessionTracker {
	t.tbUserSessionTrackerDo.DO = *(t.tbUserSessionTrackerDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserSessionTracker) updateTableName(table string) *tbUserSessionTracker {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.CorpID = field.NewString(table, "corp_id")
	t.UserID = field.NewString(table, "user_id")
	t.ClientType = field.NewString(table, "client_type")
	t.ClientCategory = field.NewString(table, "client_category")
	t.JwtID = field.NewString(table, "jwt_id")
	t.RefreshJwtID = field.NewString(table, "refresh_jwt_id")
	t.DeviceID = field.NewString(table, "device_id")
	t.IPAddress = field.NewString(table, "ip_address")
	t.Status = field.NewString(table, "status")
	t.KickReason = field.NewString(table, "kick_reason")
	t.LoginTime = field.NewTime(table, "login_time")
	t.LastActiveTime = field.NewTime(table, "last_active_time")
	t.ExpiresAt = field.NewTime(table, "expires_at")
	t.LogoutTime = field.NewTime(table, "logout_time")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbUserSessionTracker) WithContext(ctx context.Context) *tbUserSessionTrackerDo {
	return t.tbUserSessionTrackerDo.WithContext(ctx)
}

func (t tbUserSessionTracker) TableName() string { return t.tbUserSessionTrackerDo.TableName() }

func (t tbUserSessionTracker) Alias() string { return t.tbUserSessionTrackerDo.Alias() }

func (t tbUserSessionTracker) Columns(cols ...field.Expr) gen.Columns {
	return t.tbUserSessionTrackerDo.Columns(cols...)
}

func (t *tbUserSessionTracker) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserSessionTracker) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 17)
	t.fieldMap["id"] = t.ID
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["client_type"] = t.ClientType
	t.fieldMap["client_category"] = t.ClientCategory
	t.fieldMap["jwt_id"] = t.JwtID
	t.fieldMap["refresh_jwt_id"] = t.RefreshJwtID
	t.fieldMap["device_id"] = t.DeviceID
	t.fieldMap["ip_address"] = t.IPAddress
	t.fieldMap["status"] = t.Status
	t.fieldMap["kick_reason"] = t.KickReason
	t.fieldMap["login_time"] = t.LoginTime
	t.fieldMap["last_active_time"] = t.LastActiveTime
	t.fieldMap["expires_at"] = t.ExpiresAt
	t.fieldMap["logout_time"] = t.LogoutTime
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbUserSessionTracker) clone(db *gorm.DB) tbUserSessionTracker {
	t.tbUserSessionTrackerDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserSessionTracker) replaceDB(db *gorm.DB) tbUserSessionTracker {
	t.tbUserSessionTrackerDo.ReplaceDB(db)
	return t
}

type tbUserSessionTrackerDo struct{ gen.DO }

func (t tbUserSessionTrackerDo) Debug() *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserSessionTrackerDo) WithContext(ctx context.Context) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserSessionTrackerDo) ReadDB() *tbUserSessionTrackerDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserSessionTrackerDo) WriteDB() *tbUserSessionTrackerDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserSessionTrackerDo) Session(config *gorm.Session) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserSessionTrackerDo) Clauses(conds ...clause.Expression) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserSessionTrackerDo) Returning(value interface{}, columns ...string) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserSessionTrackerDo) Not(conds ...gen.Condition) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserSessionTrackerDo) Or(conds ...gen.Condition) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserSessionTrackerDo) Select(conds ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserSessionTrackerDo) Where(conds ...gen.Condition) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserSessionTrackerDo) Order(conds ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserSessionTrackerDo) Distinct(cols ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserSessionTrackerDo) Omit(cols ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserSessionTrackerDo) Join(table schema.Tabler, on ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserSessionTrackerDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserSessionTrackerDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserSessionTrackerDo) Group(cols ...field.Expr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserSessionTrackerDo) Having(conds ...gen.Condition) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserSessionTrackerDo) Limit(limit int) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserSessionTrackerDo) Offset(offset int) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserSessionTrackerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserSessionTrackerDo) Unscoped() *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserSessionTrackerDo) Create(values ...*model.TbUserSessionTracker) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserSessionTrackerDo) CreateInBatches(values []*model.TbUserSessionTracker, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserSessionTrackerDo) Save(values ...*model.TbUserSessionTracker) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserSessionTrackerDo) First() (*model.TbUserSessionTracker, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSessionTracker), nil
	}
}

func (t tbUserSessionTrackerDo) Take() (*model.TbUserSessionTracker, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSessionTracker), nil
	}
}

func (t tbUserSessionTrackerDo) Last() (*model.TbUserSessionTracker, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSessionTracker), nil
	}
}

func (t tbUserSessionTrackerDo) Find() ([]*model.TbUserSessionTracker, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserSessionTracker), err
}

func (t tbUserSessionTrackerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserSessionTracker, err error) {
	buf := make([]*model.TbUserSessionTracker, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserSessionTrackerDo) FindInBatches(result *[]*model.TbUserSessionTracker, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserSessionTrackerDo) Attrs(attrs ...field.AssignExpr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserSessionTrackerDo) Assign(attrs ...field.AssignExpr) *tbUserSessionTrackerDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserSessionTrackerDo) Joins(fields ...field.RelationField) *tbUserSessionTrackerDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserSessionTrackerDo) Preload(fields ...field.RelationField) *tbUserSessionTrackerDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserSessionTrackerDo) FirstOrInit() (*model.TbUserSessionTracker, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSessionTracker), nil
	}
}

func (t tbUserSessionTrackerDo) FirstOrCreate() (*model.TbUserSessionTracker, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSessionTracker), nil
	}
}

func (t tbUserSessionTrackerDo) FindByPage(offset int, limit int) (result []*model.TbUserSessionTracker, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserSessionTrackerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserSessionTrackerDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserSessionTrackerDo) Delete(models ...*model.TbUserSessionTracker) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserSessionTrackerDo) withDO(do gen.Dao) *tbUserSessionTrackerDo {
	t.DO = *do.(*gen.DO)
	return t
}
