<template>
  <div class="setting-page">
    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="setting-container">
        <!-- 标签页导航 -->
        <div class="tabs-header">
          <div
            class="tab-item"
            :class="{ active: activeTab === 'general' }"
            @click="activeTab = 'general'"
          >
            <span class="tab-item-text">通用设置</span>
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 'version' }"
            @click="activeTab = 'version'"
          >
            <span class="tab-item-text">版本信息</span>
          </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tabs-content">
          <!-- 通用设置页面 -->
          <div v-if="activeTab === 'general'" class="tab-panel">
            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-placeholder" />
            <!-- 设置内容 -->
            <div v-else class="setting-section">
              <div class="setting-item setting-platformAddress">
                <label class="setting-label">平台地址</label>
                <div style="display: flex; align-items: center;">
                  <base-input
                    v-model="ServerUrl"
                    placeholder="输入您连接的平台服务器地址"
                    class="setting-input"
                    clearable
                    @blur="handleServerUrlBlur"
                  />
                  <span class="spa-label" @click="handleSpaEnabled">
                    <svg
                      class="icon"
                      aria-hidden="true"
                    >
                      <use xlink:href="#icon-spa" />
                    </svg>
                  </span>
                </div>
                <div v-if="SpaEnabled" style="margin-top: 12px; width: 320px;">
                  <div class="spa-code-input-wrapper">
                    <base-input
                      v-model="SpaCode"
                      :type="showSpaCode ? 'text' : 'password'"
                      maxlength="6"
                      placeholder="请输入6位数字访问码"
                      class="setting-input spa-code-input"
                      @blur="handleSpaCodeBlur"
                      @input="handleSpaCodeInput"
                    />
                    <svg
                      class="input-icon-right spa-code-toggle"
                      fill="none"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                      @click="toggleSpaCode"
                    >
                      <path v-if="!showSpaCode" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2" />
                      <circle v-if="!showSpaCode" cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" />
                      <path v-if="showSpaCode" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2" />
                      <line v-if="showSpaCode" stroke="currentColor" stroke-width="2" x1="1" x2="23" y1="1" y2="23" />
                    </svg>
                  </div>
                </div>
              </div>

              <div class="setting-item">
                <label class="setting-label">启动选项</label>
                <div class="checkbox-group">
                  <base-checkbox v-model="IsAutoStartEnabled" class="setting-checkbox">
                    开机自启动
                  </base-checkbox>
                  <base-checkbox v-model="AutoConnectAfterStartup" class="setting-checkbox">
                    启动后自动连接
                  </base-checkbox>
                </div>
              </div>
            </div>
          </div>

          <!-- 版本信息页面 -->
          <div v-if="activeTab === 'version'" class="tab-panel">
            <div v-if="isLoading" class="loading-placeholder" />
            <div v-else>
              <div class="setting-section setting-update">
                <div class="setting-item">
                  <label class="setting-label">更新选项</label>
                  <div class="checkbox-group">
                    <base-checkbox v-model="IsAutoUpdateEnabled" class="setting-checkbox">
                      自动检查更新
                    </base-checkbox>
                  </div>
                </div>

                <div v-if="IsAutoUpdateEnabled" class="setting-item">
                  <label class="setting-label">更新检查频率</label>
                  <base-select v-model="UpdateFrequency" class="setting-select" placeholder="请选择">
                    <base-option label="每30分钟" value="1800" />
                    <base-option label="每小时" value="3600" />
                    <base-option label="每天" value="86400" />
                    <base-option label="每周" value="604800" />
                    <base-option label="每月" value="2592000" />
                  </base-select>
                </div>
              </div>
              <div class="setting-section">
                <div class="setting-item" style="margin-bottom: 8px;">
                  <label class="setting-label">关于安全客户端</label>
                  <div class="version-item">
                    <span class="version-label">当前版本</span>
                    <div class="version-value-group">
                      <span class="version-value" style="margin-left: 68px;">{{ CurrentVersion }}</span>
                      <base-button class="version-update-button" style="display: none;" text type="primary" size="small" @click="checkUpdate">
                        检查更新
                      </base-button>
                    </div>
                  </div>
                  <div class="version-item">
                    <span class="version-label">构建时间</span>
                    <span class="version-value" style="margin-left: 68px;">{{ BuildTimestamp }}</span>
                    <span
                      class="version-value"
                      style="width: 50px; height: 20px;"
                      @click="handleBuildTimeClick"
                    />
                  </div>
                  <div v-if="LastUpdatedTime !== ''" class="version-item">
                    <span class="version-label">上次更新时间</span>
                    <span class="version-value" style="margin-left: 40px;">{{ LastUpdatedTime }}</span>
                  </div>
                </div>
                <div class="copyright">
                  <p>© 2025 Security Systems Inc. 保留所有权利</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { Message, MessageBox, Loading } from '@/components/base'
import agentApi from '@/api/agentApi'
import { updateServerHost } from '@/utils/request'
import { useUserStore } from '@/pinia/modules/user'

// 响应式数据
const activeTab = ref('general')
const SettingConfig = ref({})
const ServerUrl = ref('')
const userStore = useUserStore()
const originalServerUrl = ref('') // 保存原始平台地址，用于取消时还原
const isHandlingServerUrlBlur = ref(false) // 防止重复触发blur处理
const IsAutoStartEnabled = ref(false)
const AutoConnectAfterStartup = ref(true) // 默认选中，表示启动后自动连接
const IsAutoUpdateEnabled = ref(true) // 自动检查更新，默认选中
const UpdateFrequency = ref('daily') // 更新频率，默认每天

// 访问码相关
const SpaEnabled = ref(false)
const SpaCode = ref('')
const originalSpaCode = ref('') // 保存原始安全码
const showSpaCode = ref(false) // 控制访问码显示/隐藏
const isHandlingSpaCodeBlur = ref(false) // 防止重复触发blur处理

// 版本信息
const CurrentVersion = ref('')
const BuildTimestamp = ref('')
const LastUpdatedTime = ref('')

// 构建时间点击相关
const buildTimeClickCount = ref(0)
const buildTimeClickTimer = ref(null)

// 是否正在加载设置（防止初始化时触发保存）
const isLoading = ref(true)

// 初始化设置
onMounted(async() => {
  await loadSettings()
  await nextTick()
  isLoading.value = false
})

// 加载设置
const loadSettings = async() => {
  try {
    logger.log('开始加载客户端设置...')
    const settings = await agentApi.getClientConfig()
    logger.log('加载到的设置:', settings)

    if (settings) {
      ServerUrl.value = settings.ServerUrl || ''
      originalServerUrl.value = settings.ServerUrl || '' // 保存原始值
      IsAutoStartEnabled.value = settings.IsAutoStartEnabled || false
      AutoConnectAfterStartup.value = settings.AutoConnectAfterStartup !== undefined ? settings.AutoConnectAfterStartup : true
      IsAutoUpdateEnabled.value = settings.IsAutoUpdateEnabled !== undefined ? settings.IsAutoUpdateEnabled : true
      // 验证 UpdateFrequency 值是否在有效选项中
      const validFrequencies = ['1800', '3600', '86400', '604800', '2592000']
      const settingFrequency = settings.UpdateFrequency || ''
      UpdateFrequency.value = validFrequencies.includes(settingFrequency) ? settingFrequency : '1800'
      CurrentVersion.value = settings.CurrentVersion || ''
      BuildTimestamp.value = settings.BuildTimestamp || ''
      LastUpdatedTime.value = settings.LastUpdatedTime || ''
      SettingConfig.value = settings
      // 读取访问码
      SpaCode.value = settings.activation_code || ''
      originalSpaCode.value = SpaCode.value
      SpaEnabled.value = !!SpaCode.value
    }
    logger.log('设置加载完成')
  } catch (error) {
    console.error('加载设置失败:', error)
    Message.warning('加载设置失败，使用默认设置')
  }
}

// 保存其他设置（除平台地址外）
const saveOtherSettings = async(skipSpaValidation = false) => {
  if (isLoading.value) {
    return
  }

  // 🎯 优化：可选择跳过访问码验证（用于非访问码相关的设置保存）
  if (!skipSpaValidation && SpaEnabled.value && !/^[0-9]{6}$/.test(SpaCode.value)) {
    Message.error('请输入6位数字访问码')
    return
  }

  try {
    const settings = SettingConfig.value
    settings.IsAutoStartEnabled = IsAutoStartEnabled.value
    settings.AutoConnectAfterStartup = AutoConnectAfterStartup.value
    settings.IsAutoUpdateEnabled = IsAutoUpdateEnabled.value
    settings.UpdateFrequency = UpdateFrequency.value
    // 保存访问码
    settings.activation_code = SpaEnabled.value ? SpaCode.value : ''

    logger.log('保存其他设置:', settings)
    await agentApi.setClientConfig(settings)
    logger.log('其他设置保存成功')
    Message.success('设置已保存')
  } catch (error) {
    console.error('保存其他设置失败:', error)
    Message.error('保存设置失败，请重试')
  }
}

// 处理平台地址失去焦点事件
const handleServerUrlBlur = async() => {
  // 防止重复触发
  if (isHandlingServerUrlBlur.value) {
    return
  }

  // 如果平台地址没有变化，不需要处理
  if (ServerUrl.value === originalServerUrl.value) {
    return
  }

  // 设置处理标志
  isHandlingServerUrlBlur.value = true

  try {
    // 验证服务器地址格式
    const success = await updateServerHost(ServerUrl.value)
    if (!success) {
      throw new Error('服务器地址格式错误')
    }
    await MessageBox.confirm(
      `确定要将平台地址修改为：${ServerUrl.value} 吗？`,
      '确认修改平台地址',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    // 🎯 合并版本：保留连接测试但优化用户体验
    // 用户确认后，立即显示loading
    const loadingInstance = Loading.service({
      fullscreen: true,
      text: '正在连接服务器...'
    })

    try {
      // 解析并标准化服务器地址
      const url = new URL(ServerUrl.value)
      const host = `${url.protocol}//${url.host}`
      ServerUrl.value = host

      // 🎯 SPA模式下跳过连接验证
      if (SpaEnabled.value) {
        console.log('SPA模式已启用，跳过连接验证，直接保存配置')
        loadingInstance.updateText('正在保存配置...')
        await saveServerUrl()
        loadingInstance.close()
        Message.success('服务器地址已保存（SPA模式）')
      } else {
        // 更新loading文本
        loadingInstance.updateText('正在验证服务器连接...')

        try {
          // 创建 AbortController 来控制超时
          const controller = new AbortController()
          const timeoutId = setTimeout(() => {
            controller.abort()
          }, 5000) // 5秒超时

          // 测试服务器连接
          const response = await fetch(`${ServerUrl.value}/auth/login/v1/user/main_idp/list`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            },
            signal: controller.signal
          })

          // 清除超时定时器
          clearTimeout(timeoutId)

          if (response.ok && response.status === 200) {
            // 服务器连接成功
            loadingInstance.updateText('正在保存配置...')
            await saveServerUrl()
            loadingInstance.close()
            Message.success('服务器连接成功！')
          } else {
            throw new Error(`服务器响应错误: ${response.status}`)
          }
        } catch (connectionError) {
          console.warn('服务器连接测试失败:', connectionError)

          // 🎯 优化：连接失败时提供选择
          loadingInstance.close()

          let errorMessage = '连接测试失败'
          if (connectionError.name === 'AbortError') {
            errorMessage = '连接超时，请检查网络或服务器状态'
          } else if (connectionError.name === 'TypeError') {
            errorMessage = '网络错误，请检查服务器地址是否正确'
          }

          // 询问用户是否仍要保存配置
          try {
            await MessageBox.confirm(
              `${errorMessage}，是否仍要保存此服务器地址？\n\n注意：前端将通过本地代理访问，后台会处理实际连接。`,
              '连接测试失败',
              {
                type: 'warning',
                confirmButtonText: '仍要保存',
                cancelButtonText: '取消'
              }
            )

            // 用户选择仍要保存
            console.log('用户选择保存服务器地址（将由后台代理使用）:', ServerUrl.value)
            await saveServerUrl()
            Message.success('服务器地址已保存（将由后台代理处理连接）')
          } catch (confirmError) {
            // 用户取消保存
            throw new Error('已取消保存服务器地址')
          }
        }
      }
    } catch (error) {
      // 确保loading被关闭
      if (loadingInstance) {
        loadingInstance.close()
      }

      // 还原原始值
      ServerUrl.value = originalServerUrl.value

      // 显示错误信息
      Message.error(error.message || '配置服务器地址失败')
    }
  } catch (error) {
    // 用户取消，还原原始值
    logger.log('用户取消修改平台地址，还原原值')
    ServerUrl.value = originalServerUrl.value
    if (error === 'cancel') {
      Message.info('已取消修改')
    } else {
      Message.error(error.message)
    }
  } finally {
    // 清除处理标志
    isHandlingServerUrlBlur.value = false
  }
}

// 保存平台地址
const saveServerUrl = async() => {
  try {
    const settings = SettingConfig.value
    settings.ServerUrl = ServerUrl.value

    logger.log('保存平台地址:', settings)
    await agentApi.setClientConfig(settings)

    // 保存成功，更新原始值
    originalServerUrl.value = ServerUrl.value
    globalUrlHashParams.set('WebUrl', ServerUrl.value)
    logger.log('平台地址保存成功')
    Message.success('平台地址已保存')
  } catch (error) {
    console.error('保存平台地址失败:', error)
    // 保存失败，还原原始值
    ServerUrl.value = originalServerUrl.value
    throw new Error('保存平台地址失败，请重试')
  }
}

// 点击访问码开关
const handleSpaEnabled = async() => {
  if (SpaEnabled.value) {
    try {
      await MessageBox.confirm(
        '确定要关闭访问码吗？这将清空当前的访问码配置。',
        '确认关闭访问码',
        {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      )
      // 关闭访问码
      SpaEnabled.value = !SpaEnabled.value
    } catch (error) {
      // 用户取消，还原开关状态
      logger.log('用户取消')
    }
  } else {
    // 打开访问码
    SpaEnabled.value = !SpaEnabled.value
    showSpaCode.value = true
  }
}

// 处理访问码开关变化
const handleSpaEnabledChange = async(newValue) => {
  if (isLoading.value) {
    return
  }

  // 如果是关闭访问码，需要清理
  if (!newValue && originalSpaCode.value) {
    // 用户确认，清空访问码
    SpaCode.value = ''
    originalSpaCode.value = ''
    await saveOtherSettings()
    Message.success('访问码已关闭')

    // 访问码关闭后，清除用户状态（因为后台token已被清除）
    await userStore.ClearStorage()
    logger.log('访问码已关闭，用户状态已刷新')
  } else if (newValue) {
    // 🎯 优化用户体验：开启访问码时不立即验证，给用户友好提示
    if (!SpaCode.value) {
      // 如果访问码为空，给出友好提示，不立即保存
      Message.info('请在下方输入6位数字访问码，输入完成后会自动保存')
    } else {
      // 如果已有访问码，直接保存
      await saveOtherSettings()
    }
  }
}

// 处理访问码输入事件，限制只能输入数字
const handleSpaCodeInput = (value) => {
  // 只保留数字字符
  const numericValue = value.replace(/[^0-9]/g, '')
  if (numericValue !== value) {
    SpaCode.value = numericValue
  }
}

// 切换访问码显示状态
const toggleSpaCode = () => {
  showSpaCode.value = !showSpaCode.value
}

// 处理访问码失去焦点事件
const handleSpaCodeBlur = async() => {
  // 防止重复触发
  if (isHandlingSpaCodeBlur.value) {
    return
  }

  if (SpaCode.value === originalSpaCode.value) {
    return
  }

  // 设置处理标志
  isHandlingSpaCodeBlur.value = true

  // 🎯 优化用户体验：区分首次输入和修改的情况
  const isFirstTimeInput = !originalSpaCode.value && SpaEnabled.value

  try {
    // 校验访问码格式
    if (!/^[0-9]{6}$/.test(SpaCode.value)) {
      Message.error('请输入6位数字访问码')
      SpaCode.value = originalSpaCode.value
      return
    }

    // 如果是首次输入，直接保存；如果是修改，需要确认
    if (isFirstTimeInput) {
      // 首次输入访问码，直接保存
      SettingConfig.value.activation_code = SpaCode.value
      await agentApi.setClientConfig(SettingConfig.value)
      originalSpaCode.value = SpaCode.value
      Message.success('访问码已保存')

      // 访问码变化后，清除用户状态（因为后台token已被清除）
      await userStore.ClearStorage()
      console.log('访问码已保存，用户状态已刷新')
    } else {
      // 修改已有访问码，需要确认
      await MessageBox.confirm(
        `确定要将访问码修改为：${SpaCode.value} 吗？`,
        '确认修改访问码',
        {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      )

      SettingConfig.value.activation_code = SpaCode.value
      await agentApi.setClientConfig(SettingConfig.value)
      originalSpaCode.value = SpaCode.value
      Message.success('访问码已保存')

      // 访问码变化后，清除用户状态（因为后台token已被清除）
      await userStore.ClearStorage()
      console.log('访问码已修改，用户状态已刷新')
    }
    // 保存后显示*的访问码
    showSpaCode.value = false
  } catch (error) {
    // 用户取消或保存失败，还原原始值
    SpaCode.value = originalSpaCode.value
    if (error === 'cancel') {
      Message.info('已取消修改')
    } else {
      Message.error(error.message || '保存访问码失败')
    }
  } finally {
    // 清除处理标志
    isHandlingSpaCodeBlur.value = false
  }
}

// 监听访问码开关变化
watch(SpaEnabled, handleSpaEnabledChange)

// 处理构建时间点击事件
const handleBuildTimeClick = () => {
  logger.log('构建时间被点击')

  // 增加点击计数
  buildTimeClickCount.value++

  // 如果是第一次点击，启动5秒计时器
  if (buildTimeClickCount.value === 1) {
    buildTimeClickTimer.value = setTimeout(() => {
      // 1秒后重置计数
      buildTimeClickCount.value = 0
      buildTimeClickTimer.value = null
      logger.log('构建时间点击计数已重置')
    }, 1000)
  }

  // 检查是否达到连续点击条件（这里设置为5次点击）
  if (buildTimeClickCount.value >= 5) {
    logger.log('检测到1秒内连续点击构建时间，调用 agentApi.openAsecPage')

    // 清除计时器和重置计数
    if (buildTimeClickTimer.value) {
      clearTimeout(buildTimeClickTimer.value)
      buildTimeClickTimer.value = null
    }
    buildTimeClickCount.value = 0

    try {
      // 调用 agentApi.openAsecPage 方法
      agentApi.openAsecPage('http://127.0.0.1:19998/')
      logger.log('成功调用 agentApi.openAsecPage')
    } catch (error) {
      logger.error('调用 agentApi.openAsecPage 失败:', error)
    }
  }
}

// 检查更新功能
const checkUpdate = async() => {
  try {
    Message.info('正在检查更新...')
    // 这里可以调用更新检查API
    // const updateInfo = await agentApi.checkForUpdates()
    // 暂时显示提示信息
    setTimeout(() => {
      Message.success('当前已是最新版本')
    }, 1000)
  } catch (error) {
    console.error('检查更新失败:', error)
    Message.error('检查更新失败，请稍后重试')
  }
}

// 监听其他设置变化，自动保存（复选框和下拉框通常是单次操作）
watch([IsAutoStartEnabled, AutoConnectAfterStartup, IsAutoUpdateEnabled, UpdateFrequency],
  () => {
    if (!isLoading.value) {
      logger.log('检测到其他设置变化，立即保存...')
      // 🎯 非访问码相关的设置变化，跳过访问码验证
      saveOtherSettings(true)
    }
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.setting-page {
  display: flex;
  height: 100%;
  font-family: PingFang SC, PingFang SC-Medium, "Microsoft YaHei", "微软雅黑";
}

// 左侧导航栏
.sidebar {
  width: 60px;
  background: #667eea;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  .sidebar-menu {
    display: flex;
    flex-direction: column;

    .menu-item {
      width: 48px;
      height: 48px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: rgba(255, 255, 255, 0.7);
      font-size: 10px;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
      }

      &.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }

      .menu-icon {
        width: 16px;
        height: 16px;
        margin-bottom: 4px;

        // 使用简单的几何形状模拟图标
        &::before {
          content: '';
          display: block;
          width: 100%;
          height: 100%;
          background: currentColor;
          border-radius: 2px;
        }
      }
    }
  }
}

// 主内容区域
.main-content {
  flex: 1;
  padding: 16px 32px 32px 32px;
  overflow-y: auto;
}

.setting-container {
  height: calc(100% - 38px);
  background: white;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 auto;
}

// 标签页头部
.tabs-header {
  display: flex;
  border-bottom: 1px solid #e4e7ed;

  .tab-item {
    padding: 16px 24px 16px 0px;
    cursor: pointer;
    color: #686e84;
    font-size: 16px;
    font-family: PingFang SC, PingFang SC-Medium, "Microsoft YaHei", "微软雅黑";
    font-weight: 400; // 默认细体
    line-height: 22px;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;

    &:hover {
      color: #536ce6;
      // background: rgba(64, 158, 255, 0.05);//去除淡蓝色选中效果，因为靠左对齐了，选中效果会只有右边距
    }

    &.active {
      color: #536ce6;
      font-family: PingFang SC, PingFang SC-Medium, "Microsoft YaHei", "微软雅黑";
      font-weight: 600;
      background: white;
      position: relative;
      .tab-item-text {
        border-bottom: 2px solid #536ce6;
        padding-top: 20px;
        padding-bottom: 20px;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: white;
      }
    }
  }
}

// 标签页内容
.tabs-content {
  min-height: 400px;

  .tab-panel {
    padding: 24px 0px 24px 0px;

    .setting-update {
      padding-bottom: 24px;
      margin-bottom: 24px;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

// 加载状态样式
.loading-placeholder {
  padding: 32px 0px;
  min-height: 200px; // 保持一定高度，避免页面跳动
}

// 设置项样式
.setting-section {
  .setting-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .setting-platformAddress {
    padding-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  .setting-label {
    display: block;
    font-size: 14px;
    font-weight: 550;
    color: #303133;
    margin-bottom: 12px;
  }

  .spa-label {
    margin-left: 10px;
    width: 34px;
    height: 34px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .spa-label .icon {
    width: 16px;
    height: 16px;
  }

  .setting-input {
    width: 320px;

    :deep(.el-input__inner) {
      height: 40px;
      border-radius: 6px;

      &:focus {
        border-color: #536ce6;
      }
    }
  }

  .setting-select {
    width: 160px;

    :deep(.el-select__wrapper) {
      height: 40px;
      border-radius: 6px;
    }
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
    width: 200px;

    .setting-checkbox {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      :deep(.el-checkbox__label) {
        font-size: 14px;
        color: #606266;
      }

      :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: #536ce6;
        border-color: #536ce6;
      }
    }
  }
}

// 版本信息样式
.setting-section {
  .version-item {
    display: flex;
    align-items: center;
    padding: 0px 0px 8px 0px;

    &:last-child {
      border-bottom: none;
    }

    .version-label {
      font-size: 14px;
      color: #686e84;
      flex-shrink: 0;
    }

    .version-value {
      font-size: 14px;
      color: #3c404d;
      font-weight: 500;
    }

    .version-value-group {
      display: flex;
      align-items: center;

      .version-value {
        font-size: 14px;
        color: #3c404d;
        font-weight: 500;
        margin-right: 12px;
      }

      .version-update-button {
        width: 64px;
        height: 24px;
        background: #f5f5f7;
        font-size: 12px;
        color: #686e84;
        padding: 2px 7px 5px 7px;
        border-radius: 4px;
        border: none;
        transition: all 0.3s ease;
        cursor: pointer;
        //检查更新鼠标悬停效果增强
        &:hover {
          transform: scale(1.05) translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

// 版权信息
.copyright {
  margin-top: 0px;
  display: none;

  p {
    font-size: 12px;
    color: #909399;
    margin: 0;
  }
}

// 底部操作按钮
.setting-footer {
  padding: 20px 0px;
  border-top: 1px solid #f0f0f0;
  background: #ffffff;
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;

  .base-button {
    padding: 8px 20px;
    border-radius: 6px;
    font-size: 14px;
    min-width: 100px;
    height: 36px;
    margin-left: 12px;

    &:first-child {
      margin-left: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .setting-page {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    flex-direction: row;
    padding: 16px;

    .sidebar-menu {
      flex-direction: row;
      justify-content: center;

      .menu-item {
        margin-bottom: 0;
        margin-right: 16px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .main-content {
    padding: 24px 32px 24px 32px;
  }

  .setting-container {
    margin: 0;
    border-radius: 0;
  }

  .tabs-content .tab-panel {
    padding: 26px 0px;
  }

  .setting-footer {
    padding: 16px 20px;
  }

  .tabs-header .tab-item {
    padding: 12px 16px 16px 0px;
    font-size: 16px;
  }

  .setting-section .setting-select {
    width: 100%;
  }
}

// 访问码输入框样式
.spa-code-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;

  .spa-code-input {
    letter-spacing: 2px;

    &[type="password"] {
      font-family: 'Courier New', monospace;
      letter-spacing: 4px;
    }
  }

  .input-icon-right {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    color: #909399;
    cursor: pointer;
    z-index: 2;
    transition: color 0.3s ease;

    &:hover {
      color: #409eff;
    }
  }

  .spa-code-toggle {
    &:hover {
      color: #409eff;
    }
  }
}
</style>
