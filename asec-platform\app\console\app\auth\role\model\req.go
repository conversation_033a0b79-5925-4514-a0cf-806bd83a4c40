package model

import (
	"asdsec.com/asec/platform/pkg/model"
	"github.com/jackc/pgtype"
)

type CreateAdminRoleReq struct {
	RoleName string       `json:"role_name" binding:"required"`
	Desc     string       `json:"desc"`
	CorpId   string       `json:"corp_id"`
	Status   int          `json:"status" binding:"required"`
	Policy   pgtype.JSONB `json:"policy"`
}
type GetAdminRoleListReq struct {
	model.Pagination
	CorpId string `json:"corpId"`
}
