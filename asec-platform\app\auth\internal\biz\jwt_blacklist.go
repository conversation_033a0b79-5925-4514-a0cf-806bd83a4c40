package biz

import (
	"context"
	"time"
)

// JWTBlacklistRepo JWT黑名单Repository接口
type JWTBlacklistRepo interface {
	// AddToBlacklist 将JWT ID添加到黑名单
	AddToBlacklist(ctx context.Context, jwtId string, expiresAt time.Time) error

	// IsBlacklisted 检查JWT ID是否在黑名单中
	IsBlacklisted(ctx context.Context, jwtId string) (bool, error)

	// RemoveExpiredFromBlacklist 清理过期的黑名单记录
	RemoveExpiredFromBlacklist(ctx context.Context) error
}
