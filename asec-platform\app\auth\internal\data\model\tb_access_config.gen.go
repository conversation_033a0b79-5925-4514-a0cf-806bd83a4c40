// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameTbAccessConfig = "tb_access_config"

// TbAccessConfig mapped from table <tb_access_config>
type TbAccessConfig struct {
	ID              int32          `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt       time.Time      `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt       time.Time      `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	LanAddress      string         `gorm:"column:lan_address" json:"lan_address"`
	InternetAddress string         `gorm:"column:internet_address" json:"internet_address"`
	AliasAddresses  string         `gorm:"column:alias_addresses" json:"alias_addresses"`
}

// TableName TbAccessConfig's table name
func (*TbAccessConfig) TableName() string {
	return TableNameTbAccessConfig
}
