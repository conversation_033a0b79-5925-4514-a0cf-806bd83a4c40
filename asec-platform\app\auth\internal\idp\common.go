package idp

import (
	"crypto/sha1"
	"fmt"

	"asdsec.com/asec/platform/app/auth/internal/dto"
)

// ExternalInfoClient 是所有外部身份源客户端需要实现的通用接口
type ExternalInfoClient interface {
	GetDeptsAndUsers(rootGroupId string) ([]*dto.ExternalDepartment, []*dto.ExternalUser, error)
}

func <PERSON>ey(data interface{}) string {
	h := sha1.New()
	h.Write([]byte(fmt.Sprintf("%v", data)))
	return fmt.Sprintf("%x", h.Sum(nil))
}
