package agent_conf

import (
	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"github.com/golang/protobuf/proto"
	"log"
	"testing"
)

func TestInsertCfgBlob(t *testing.T) {
	global.SqliteClient, _ = global.InitSqlite()
	global.InitTable()
	resp := v1.GetConfigResp{}
	resp.CfgId = "aaa"

	bytes, _ := proto.Marshal(&resp)

	_, err := global.SqliteClient.Exec("insert into tb_agent_config (id, config_type, config_data) values (? ,?,?) on CONFLICT(id)"+
		" do update set config_type=excluded.config_type, config_data=excluded.config_data",
		"aaa", "process_file_mon", bytes)
	if err != nil {
		log.Fatal(err)
	}

	_, err = global.SqliteClient.Exec("insert into tb_agent_config (id, config_type, config_data) values (? ,?,?) on CONFLICT(id)"+
		" do update set config_type=excluded.config_type, config_data=excluded.config_data",
		"aaa", "process_file_mon", bytes)
	if err != nil {
		log.Fatal(err)
	}
}
