package biz

import (
	"context"

	authModel "asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/pkg/ip2region/common"
	model "asdsec.com/asec/platform/pkg/model/strategy_model"
	"github.com/go-kratos/kratos/v2/log"
)

type CommonRepo interface {
	GetFactorTime(ctx context.Context, ids []string) ([]*model.FactorTime, error)
	GetFactorRegion(ctx context.Context, ids []string) ([]*model.UebaStrategyCondition, error)
	GetLastLoginIp(ctx context.Context, userId string) (authModel.TbUserLoginLog, error)
	GetAccessConfigModel(ctx context.Context) (*authModel.TbAccessConfig, error)
	GetIp2Region() common.Ip2region
}

type CommonUsecase struct {
	repo CommonRepo
	log  *log.Helper
}

func NewCommonUsecase(repo CommonRepo, logger log.Logger) *CommonUsecase {
	return &CommonUsecase{
		repo: repo,
		log:  log.<PERSON><PERSON>per(logger),
	}
}
