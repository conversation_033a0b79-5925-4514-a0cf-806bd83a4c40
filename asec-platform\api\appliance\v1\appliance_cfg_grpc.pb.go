// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: appliance/v1/appliance_cfg.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ApplianceCfgClient is the client API for ApplianceCfg service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApplianceCfgClient interface {
	// 获取配置版本信息 todo cl 暂未实现版本比对获取配置
	GetConfigVersion(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*ConfigVersionResp, error)
	// 获取配置
	GetCfg(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*GetConfigResp, error)
}

type applianceCfgClient struct {
	cc grpc.ClientConnInterface
}

func NewApplianceCfgClient(cc grpc.ClientConnInterface) ApplianceCfgClient {
	return &applianceCfgClient{cc}
}

func (c *applianceCfgClient) GetConfigVersion(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*ConfigVersionResp, error) {
	out := new(ConfigVersionResp)
	err := c.cc.Invoke(ctx, "/api.appliance.ApplianceCfg/GetConfigVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applianceCfgClient) GetCfg(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*GetConfigResp, error) {
	out := new(GetConfigResp)
	err := c.cc.Invoke(ctx, "/api.appliance.ApplianceCfg/GetCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApplianceCfgServer is the server API for ApplianceCfg service.
// All implementations must embed UnimplementedApplianceCfgServer
// for forward compatibility
type ApplianceCfgServer interface {
	// 获取配置版本信息 todo cl 暂未实现版本比对获取配置
	GetConfigVersion(context.Context, *ConfigReq) (*ConfigVersionResp, error)
	// 获取配置
	GetCfg(context.Context, *ConfigReq) (*GetConfigResp, error)
	mustEmbedUnimplementedApplianceCfgServer()
}

// UnimplementedApplianceCfgServer must be embedded to have forward compatible implementations.
type UnimplementedApplianceCfgServer struct {
}

func (UnimplementedApplianceCfgServer) GetConfigVersion(context.Context, *ConfigReq) (*ConfigVersionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfigVersion not implemented")
}
func (UnimplementedApplianceCfgServer) GetCfg(context.Context, *ConfigReq) (*GetConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCfg not implemented")
}
func (UnimplementedApplianceCfgServer) mustEmbedUnimplementedApplianceCfgServer() {}

// UnsafeApplianceCfgServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApplianceCfgServer will
// result in compilation errors.
type UnsafeApplianceCfgServer interface {
	mustEmbedUnimplementedApplianceCfgServer()
}

func RegisterApplianceCfgServer(s grpc.ServiceRegistrar, srv ApplianceCfgServer) {
	s.RegisterService(&ApplianceCfg_ServiceDesc, srv)
}

func _ApplianceCfg_GetConfigVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplianceCfgServer).GetConfigVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ApplianceCfg/GetConfigVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplianceCfgServer).GetConfigVersion(ctx, req.(*ConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplianceCfg_GetCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplianceCfgServer).GetCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ApplianceCfg/GetCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplianceCfgServer).GetCfg(ctx, req.(*ConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ApplianceCfg_ServiceDesc is the grpc.ServiceDesc for ApplianceCfg service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApplianceCfg_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.appliance.ApplianceCfg",
	HandlerType: (*ApplianceCfgServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetConfigVersion",
			Handler:    _ApplianceCfg_GetConfigVersion_Handler,
		},
		{
			MethodName: "GetCfg",
			Handler:    _ApplianceCfg_GetCfg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "appliance/v1/appliance_cfg.proto",
}
