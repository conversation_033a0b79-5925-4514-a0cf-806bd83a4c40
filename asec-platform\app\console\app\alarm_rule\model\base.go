package model

import (
	"time"
)

type ChannelTypeDB struct {
	Id          uint64          `gorm:"column:id;primaryKey;type:serial;comment:主键" json:"id"`
	Pid         uint64          `gorm:"column:pid;type:int;comment:父级id" json:"pid"`
	Channel     string          `gorm:"column:channel;type:varchar;comment:事件名称" json:"channel"`
	ChannelName string          `gorm:"column:channel_name;type:varchar;comment:标识id" json:"channel_name"`
	CreateTime  time.Time       `gorm:"column:create_time;type:timestamptz;comment:创建时间" json:"create_time"`
	Children    []ChannelTypeDB `gorm:"-" json:"children"`
	Count       int64           `gorm:"-" json:"count"`
}

func (ChannelTypeDB) TableName() string {
	return "tb_channel_type"
}

type AlertEventDB struct {
	UUID            string `gorm:"column:uuid;primaryKey;type:String;comment:客户端上传唯一ID" json:"uuid"`
	CorpId          string `gorm:"column:corp_id;type:String;comment:组织ID/租户ID" json:"corp_id"`
	Channel         string `gorm:"column:channel;type:String;comment:外发通道名称/上传软件名称" json:"channel"`
	SensitiveRuleID string `gorm:"column:sensitive_rule_id;type:String;comment:命中的数据敏感规则ID" json:"sensitive_rule_id"`
	DataCategory    string `gorm:"column:data_category" json:"data_category"`
	SensitiveLevel  string `gorm:"column:sensitive_level" json:"sensitive_level"`
}

func (AlertEventDB) TableName() string {
	return "tb_ddr_alert"
}

type SensitiveStrategyDB struct {
	Id             string                `gorm:"column:id;primaryKey;type:varchar(64);comment:主键" json:"id"`
	RuleName       string                `gorm:"column:rule_name;type:varchar(255);comment:规则名称" json:"rule_name"`
	SensitiveLevel int16                 `gorm:"column:sensitive_level;type:smallint;comment:敏感等级" json:"sensitive_level"`
	CorpId         string                `gorm:"column:corp_id;type:varchar(64);comment:租户ID " json:"corp_id"`
	Children       []SensitiveStrategyDB `gorm:"-" json:"children"`
	Count          int64                 `gorm:"-" json:"count"`
	// 如果不存在子类则不允许勾选
	Disable bool `json:"disable"`
}

func (SensitiveStrategyDB) TableName() string {
	return "tb_sensitive_strategy"
}

type DataTypeResp struct {
	ID       uint64         `json:"id"`
	RuleName string         `json:"name"`
	Count    int            `json:"count"`
	Children []DataTypeResp `json:"children"`
}
