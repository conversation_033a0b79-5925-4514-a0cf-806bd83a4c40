package model

import (
	"asdsec.com/asec/platform/pkg/model"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
)

type CreateAlertRuleReq struct {
	Id             string         `gorm:"column:id" json:"id"`
	CorpId         string         `gorm:"column:corp_id" db:"corp_id" json:"corp_id"`
	Name           string         `gorm:"column:name" json:"name"`
	Description    string         `gorm:"column:description" json:"description"`
	Enable         int            `gorm:"column:enable" json:"enable"`
	EnableAnalysis int            `gorm:"column:enable_analysis" json:"enable_analysis"`
	SeverityIds    pq.Int64Array  `gorm:"column:severity_ids;type:int" json:"severity_ids"`
	UserIds        pq.StringArray `gorm:"column:user_ids;type:int" json:"user_ids"`
	UserGroupIds   pq.StringArray `gorm:"column:user_group_ids;type:int" json:"user_group_ids"`
	ChannelTypes   pq.StringArray `gorm:"column:channel_types;type:string" json:"channel_types"`
	SensitiveIds   pq.StringArray `gorm:"column:sensitive_ids;type:int" json:"sensitive_ids"`
	SensitiveLevel pq.Int64Array  `gorm:"column:sensitive_level;type:int" json:"sensitive_level"`
	Time           pgtype.JSONB   `gorm:"column:time" json:"time"`

	AlertType        int      `gorm:"column:alert_type;comment:告警类型(1代码/2数据)" json:"alert_type"`
	GitContainOption int      `gorm:"column:git_contain_option;comment:git包含逻辑(1包含/2不包含)" json:"git_contain_option"`
	SvnContainOption int      `gorm:"column:svn_contain_option;comment:svn包含逻辑(1包含/2不包含)" json:"svn_contain_option"`
	GitPath          []string `gorm:"column:git_path;comment:git_path" json:"git_path"`
	SvnPath          []string `gorm:"column:svn_path;comment:svn_path" json:"svn_path"`
}

type GetAlertRuleListReq struct {
	model.Pagination
	BuiltIn int `gorm:"column:built_in" json:"built_in"`
}
