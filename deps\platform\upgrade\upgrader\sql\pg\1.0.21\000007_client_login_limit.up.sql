-- 客户端登录限制功能数据库迁移脚本 - UP

-- 1. 扩展认证策略表，增加客户端限制配置
ALTER TABLE tb_auth_policy 
ADD COLUMN IF NOT EXISTS client_limits JSONB DEFAULT '{"pc_max_clients": 5, "mobile_max_clients": 3, "overflow_strategy": "kick_oldest"}';

-- 添加注释
COMMENT ON COLUMN tb_auth_policy.client_limits IS '客户端数量限制配置JSON: {"pc_max_clients": 数字, "mobile_max_clients": 数字, "overflow_strategy": "kick_oldest|kick_inactive|reject_new"}';

-- 2. 创建用户会话跟踪表（轻量级，配合JWT黑名单使用）
CREATE TABLE IF NOT EXISTS tb_user_session_tracker (
    id VARCHAR(64) PRIMARY KEY,
    corp_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    client_type VARCHAR(50) NOT NULL, -- 存储详细的操作系统类型，如: windows, macos, linux, android, ios, etc.
    client_category VARCHAR(20) NOT NULL CHECK (client_category IN ('pc', 'mobile')), -- 抽象分类：pc或mobile
    jwt_id VARCHAR(64) NOT NULL UNIQUE,  -- 对应access token的jti，用于关联现有黑名单
    refresh_jwt_id VARCHAR(64) NOT NULL UNIQUE,  -- 对应refresh token的jti，用于拉黑refresh token
    device_id VARCHAR(64),
    ip_address VARCHAR(45), -- 登录IP地址（支持IPv4和IPv6）
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'kicked', 'expired', 'logout')),
    kick_reason VARCHAR(255),  -- 踢出原因
    login_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_active_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    logout_time TIMESTAMP,  -- 登出时间（踢出/主动登出）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP

);

-- 3. 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_session_tracker_corp_user_client ON tb_user_session_tracker(corp_id, user_id, client_category);
CREATE INDEX IF NOT EXISTS idx_session_tracker_client_type ON tb_user_session_tracker(client_type);
CREATE INDEX IF NOT EXISTS idx_session_tracker_jwt_id ON tb_user_session_tracker(jwt_id);
CREATE INDEX IF NOT EXISTS idx_session_tracker_refresh_jwt_id ON tb_user_session_tracker(refresh_jwt_id);
CREATE INDEX IF NOT EXISTS idx_session_tracker_ip_address ON tb_user_session_tracker(ip_address);
CREATE INDEX IF NOT EXISTS idx_session_tracker_expires ON tb_user_session_tracker(expires_at);
CREATE INDEX IF NOT EXISTS idx_session_tracker_last_active ON tb_user_session_tracker(last_active_time);
CREATE INDEX IF NOT EXISTS idx_session_tracker_login_time ON tb_user_session_tracker(login_time);
CREATE INDEX IF NOT EXISTS idx_session_tracker_status ON tb_user_session_tracker(status);
CREATE INDEX IF NOT EXISTS idx_session_tracker_active_sessions ON tb_user_session_tracker(corp_id, user_id, client_category, status) WHERE status = 'active';

-- 4. 添加表注释
COMMENT ON TABLE tb_user_session_tracker IS '用户会话跟踪表（轻量级，配合JWT黑名单使用）';
COMMENT ON COLUMN tb_user_session_tracker.id IS '会话跟踪记录唯一ID';
COMMENT ON COLUMN tb_user_session_tracker.corp_id IS '企业ID';
COMMENT ON COLUMN tb_user_session_tracker.user_id IS '用户ID';
COMMENT ON COLUMN tb_user_session_tracker.client_type IS '详细的操作系统类型: windows, macos, linux, android, ios, etc.';
COMMENT ON COLUMN tb_user_session_tracker.client_category IS '抽象客户端分类: pc/mobile';
COMMENT ON COLUMN tb_user_session_tracker.jwt_id IS 'access token的jti字段，用于关联现有黑名单';
COMMENT ON COLUMN tb_user_session_tracker.refresh_jwt_id IS 'refresh token的jti字段，用于拉黑refresh token';
COMMENT ON COLUMN tb_user_session_tracker.device_id IS '设备ID，用于标识唯一设备';
COMMENT ON COLUMN tb_user_session_tracker.ip_address IS '登录IP地址（支持IPv4和IPv6）';
COMMENT ON COLUMN tb_user_session_tracker.status IS '会话状态: active(活跃)/kicked(被踢出)/expired(已过期)/logout(主动登出)';
COMMENT ON COLUMN tb_user_session_tracker.kick_reason IS '踢出原因';
COMMENT ON COLUMN tb_user_session_tracker.login_time IS '登录时间';
COMMENT ON COLUMN tb_user_session_tracker.last_active_time IS '最后活跃时间';
COMMENT ON COLUMN tb_user_session_tracker.expires_at IS 'JWT过期时间';
COMMENT ON COLUMN tb_user_session_tracker.logout_time IS '登出时间（踢出/主动登出）';

-- 5. 创建定期清理过期记录的函数
CREATE OR REPLACE FUNCTION cleanup_expired_session_trackers()
RETURNS void AS $$
BEGIN
    -- 软删除过期的会话记录（标记为expired状态）
    UPDATE tb_user_session_tracker 
    SET status = 'expired',
        logout_time = NOW(),
        updated_at = NOW()
    WHERE expires_at < NOW() 
      AND status = 'active';
    
    -- 记录更新的行数
    RAISE NOTICE 'Marked % expired session tracker records as expired', ROW_COUNT;
    
    -- 可选：删除超过保留期的历史记录（例如30天前的记录）
    -- DELETE FROM tb_user_session_tracker 
    -- WHERE logout_time < NOW() - INTERVAL '30 days'
    --   AND status IN ('kicked', 'expired', 'logout');
END;
$$ LANGUAGE plpgsql;

-- 6. 创建自动更新updated_at字段的触发器函数
CREATE OR REPLACE FUNCTION update_session_tracker_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. 创建触发器
-- 先删除可能存在的触发器，然后重新创建
DROP TRIGGER IF EXISTS trigger_update_session_tracker_updated_at ON tb_user_session_tracker;
CREATE TRIGGER trigger_update_session_tracker_updated_at
    BEFORE UPDATE ON tb_user_session_tracker
    FOR EACH ROW
    EXECUTE FUNCTION update_session_tracker_updated_at();

-- 8. 为现有用户设置默认客户端限制策略（可选）
UPDATE tb_auth_policy 
SET client_limits = '{"pc_max_clients": 5, "mobile_max_clients": 3, "overflow_strategy": "kick_oldest"}'::jsonb
WHERE client_limits IS NULL;

-- 完成迁移
DO $$
BEGIN
    RAISE NOTICE 'Client login limit migration (UP) completed successfully!';
    RAISE NOTICE 'New table: tb_user_session_tracker (with ip_address field)';
    RAISE NOTICE 'New column: tb_auth_policy.client_limits';
    RAISE NOTICE 'New function: cleanup_expired_session_trackers()';
    RAISE NOTICE 'Added ip_address field and index for session tracking';
END $$;
