package service

import (
	"asdsec.com/asec/platform/app/console/app/domain_ssl/constants"
	"asdsec.com/asec/platform/app/console/app/domain_ssl/dto"
	"asdsec.com/asec/platform/app/console/app/domain_ssl/repository"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"sync"
)

var DomainServiceImpl DomainService

var DomainServiceInit sync.Once

type domainService struct {
	db repository.DomainRepository
}

func (d domainService) GetDomainCertificateList(ctx context.Context, pagination model.Pagination) (dto.GetDomainCertificateListRsp, error) {
	return d.db.GetDomainCertificateList(ctx, pagination)
}

func (d domainService) GetCertificateDetail(ctx context.Context, id string) (dto.GetCertificateDetailRsp, error) {
	return d.db.GetCertificateDetail(ctx, id)
}
func (d domainService) AddDomainCertificate(ctx context.Context, req dto.CreateCertificateReq) (string, aerrors.AError) {
	data, aeErr := d.db.QueryDomainCertificateByName(ctx, req.Name)
	if aeErr != nil {
		return "", aeErr
	}
	if data.Id != "" {
		return "", aerrors.New("duplicate name", constants.DuplicateNameError)
	}
	return d.db.AddDomainCertificate(ctx, req)
}

func (d domainService) UpdateDomainCertificate(ctx context.Context, req dto.UpdateCertificateReq) (string, aerrors.AError) {
	data, aeErr := d.db.QueryDomainCertificateByName(ctx, req.Name)
	if aeErr != nil {
		return "", aeErr
	}
	if data.Id != "" && data.Id != req.Id {
		return "", aerrors.New("duplicate name", constants.DuplicateNameError)
	}
	return d.db.UpdateDomainCertificate(ctx, req)
}

func (d domainService) DelDomainCertificate(ctx context.Context, ids []string) aerrors.AError {
	return d.db.DelDomainCertificate(ctx, ids)
}

type DomainService interface {
	GetDomainCertificateList(ctx context.Context, pagination model.Pagination) (dto.GetDomainCertificateListRsp, error)
	GetCertificateDetail(ctx context.Context, id string) (dto.GetCertificateDetailRsp, error)
	AddDomainCertificate(ctx context.Context, req dto.CreateCertificateReq) (string, aerrors.AError)
	UpdateDomainCertificate(ctx context.Context, req dto.UpdateCertificateReq) (string, aerrors.AError)
	DelDomainCertificate(ctx context.Context, ids []string) aerrors.AError
}

func GetDomainService() DomainService {
	DomainServiceInit.Do(func() {
		DomainServiceImpl = domainService{db: repository.NewDomainRepository()}
	})
	return DomainServiceImpl
}
