package common

import "context"

// ContextKey 定义上下文键类型
type ContextKey string

// 定义常用上下文键
const (
	TestIDKey ContextKey = "test_id"
	IsTestKey ContextKey = "is_test"
	IdpIDKey  ContextKey = "idp_id"
)

// GetTestID 从上下文中安全地获取测试ID
func GetTestIDFromCtx(ctx context.Context) (string, bool) {
	if v := ctx.Value(TestIDKey); v != nil {
		if s, ok := v.(string); ok {
			return s, true
		}
	}
	return "", false
}

func GetIdpIDFromCtx(ctx context.Context) (string, bool) {
	if v := ctx.Value(IdpIDKey); v != nil {
		if s, ok := v.(string); ok {
			return s, true
		}
	}
	return "", false
}
