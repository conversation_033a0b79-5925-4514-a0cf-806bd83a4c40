package biz

import (
	"context"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
)

type UserSourceRepo interface {
	CreateUserSource(ctx context.Context, corpId, id, name, sourceType string) error
	UpdateUserSource(ctx context.Context, corpId, id, name string) error
	GetUserSource(ctx context.Context, corpId, id string) (*model.TbUserSource, error)
	GetUserSourceByName(ctx context.Context, corpId, name string) (*model.TbUserSource, error)
	ListUserSource(ctx context.Context, corpId string) ([]*model.TbUserSource, error)
	GetUserSourceByType(ctx context.Context, corpId, sourceType, templateType string) (*model.TbUserSource, error)
}

type UserSourceUsecase struct {
	repo UserSourceRepo
	log  *log.Helper
}

func NewUserSourceUsecase(repo UserSourceRepo, logger log.Logger) *UserSourceUsecase {
	return &UserSourceUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (u UserSourceUsecase) CreateUserSource(ctx context.Context, name, sourceType, corpId string) error {
	_, err := u.repo.GetUserSourceByName(ctx, corpId, name)
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("GetUserSourceByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", name)
	}

	_, err = u.repo.GetUserSourceByType(ctx, corpId, sourceType, "")
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("GetUserSourceByType failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorSourceTypeConflict("sourceType=%v conflict.", sourceType)
	}

	id := uuid.New()
	err = u.repo.CreateUserSource(ctx, corpId, id.String(), name, sourceType)
	if err != nil {
		u.log.Errorf("CreateUserSource failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserSourceUsecase) UpdateUserSource(ctx context.Context, id, name, corpId string) error {
	_, err := u.repo.GetUserSourceByName(ctx, corpId, name)
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("GetUserSourceByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", name)
	}

	if _, err = u.repo.GetUserSource(ctx, corpId, id); err != nil {
		u.log.Errorf("GetUserSource failed. err=%v", err)
		return err
	}

	if err = u.repo.UpdateUserSource(ctx, corpId, id, name); err != nil {
		u.log.Errorf("UpdateUserSource failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserSourceUsecase) GetUserSource(ctx context.Context, corpId, id string) (dto.UserSource, error) {
	userSource, err := u.repo.GetUserSource(ctx, corpId, id)
	if err != nil {
		u.log.Errorf("GetUserSource failed: %v", err)
		return dto.UserSource{}, err
	}
	var result dto.UserSource
	err = copier.Copy(&result, userSource)
	if err != nil {
		u.log.Errorf("copy failed. err=%v", err)
		return dto.UserSource{}, err
	}
	return result, nil
}

func (u UserSourceUsecase) ListUserSource(ctx context.Context, corpId string) ([]dto.UserSource, error) {
	userSourceList, err := u.repo.ListUserSource(ctx, corpId)
	if err != nil {
		u.log.Errorf("ListUserSource failed. err=%v", err)
		return []dto.UserSource{}, err
	}
	var result []dto.UserSource
	err = copier.Copy(&result, &userSourceList)
	if err != nil {
		u.log.Errorf("copy failed. err=%v", err)
		return []dto.UserSource{}, err
	}
	return result, nil
}
