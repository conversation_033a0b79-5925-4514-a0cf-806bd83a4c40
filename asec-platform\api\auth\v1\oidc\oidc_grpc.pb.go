// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: auth/v1/oidc/oidc.proto

package oidc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OIDC_Authorize_FullMethodName = "/api.auth.v1.oidc.OIDC/Authorize"
	OIDC_Token_FullMethodName     = "/api.auth.v1.oidc.OIDC/Token"
	OIDC_UserInfo_FullMethodName  = "/api.auth.v1.oidc.OIDC/UserInfo"
	OIDC_Discovery_FullMethodName = "/api.auth.v1.oidc.OIDC/Discovery"
	OIDC_JWKS_FullMethodName      = "/api.auth.v1.oidc.OIDC/JWKS"
	OIDC_Revoke_FullMethodName    = "/api.auth.v1.oidc.OIDC/Revoke"
	OIDC_Logout_FullMethodName    = "/api.auth.v1.oidc.OIDC/Logout"
)

// OIDCClient is the client API for OIDC service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OIDCClient interface {
	// 授权端点 - OIDC授权码流程的起始点
	Authorize(ctx context.Context, in *AuthorizeRequest, opts ...grpc.CallOption) (*AuthorizeReply, error)
	// 令牌端点 - 用授权码交换访问令牌和ID令牌
	Token(ctx context.Context, in *TokenRequest, opts ...grpc.CallOption) (*TokenReply, error)
	// 用户信息端点 - 使用访问令牌获取用户信息
	UserInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserInfoReply, error)
	// 发现端点 - 提供OIDC提供者的元数据
	Discovery(ctx context.Context, in *DiscoveryRequest, opts ...grpc.CallOption) (*DiscoveryReply, error)
	// JWKS端点 - 提供用于验证JWT签名的公钥集
	JWKS(ctx context.Context, in *JWKSRequest, opts ...grpc.CallOption) (*JWKSReply, error)
	// 令牌撤销端点 - 撤销访问令牌或刷新令牌
	Revoke(ctx context.Context, in *RevokeRequest, opts ...grpc.CallOption) (*RevokeReply, error)
	// 登出端点 - 结束用户会话
	Logout(ctx context.Context, in *LogoutRequest, opts ...grpc.CallOption) (*LogoutReply, error)
}

type oIDCClient struct {
	cc grpc.ClientConnInterface
}

func NewOIDCClient(cc grpc.ClientConnInterface) OIDCClient {
	return &oIDCClient{cc}
}

func (c *oIDCClient) Authorize(ctx context.Context, in *AuthorizeRequest, opts ...grpc.CallOption) (*AuthorizeReply, error) {
	out := new(AuthorizeReply)
	err := c.cc.Invoke(ctx, OIDC_Authorize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oIDCClient) Token(ctx context.Context, in *TokenRequest, opts ...grpc.CallOption) (*TokenReply, error) {
	out := new(TokenReply)
	err := c.cc.Invoke(ctx, OIDC_Token_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oIDCClient) UserInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserInfoReply, error) {
	out := new(UserInfoReply)
	err := c.cc.Invoke(ctx, OIDC_UserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oIDCClient) Discovery(ctx context.Context, in *DiscoveryRequest, opts ...grpc.CallOption) (*DiscoveryReply, error) {
	out := new(DiscoveryReply)
	err := c.cc.Invoke(ctx, OIDC_Discovery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oIDCClient) JWKS(ctx context.Context, in *JWKSRequest, opts ...grpc.CallOption) (*JWKSReply, error) {
	out := new(JWKSReply)
	err := c.cc.Invoke(ctx, OIDC_JWKS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oIDCClient) Revoke(ctx context.Context, in *RevokeRequest, opts ...grpc.CallOption) (*RevokeReply, error) {
	out := new(RevokeReply)
	err := c.cc.Invoke(ctx, OIDC_Revoke_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oIDCClient) Logout(ctx context.Context, in *LogoutRequest, opts ...grpc.CallOption) (*LogoutReply, error) {
	out := new(LogoutReply)
	err := c.cc.Invoke(ctx, OIDC_Logout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OIDCServer is the server API for OIDC service.
// All implementations must embed UnimplementedOIDCServer
// for forward compatibility
type OIDCServer interface {
	// 授权端点 - OIDC授权码流程的起始点
	Authorize(context.Context, *AuthorizeRequest) (*AuthorizeReply, error)
	// 令牌端点 - 用授权码交换访问令牌和ID令牌
	Token(context.Context, *TokenRequest) (*TokenReply, error)
	// 用户信息端点 - 使用访问令牌获取用户信息
	UserInfo(context.Context, *UserInfoRequest) (*UserInfoReply, error)
	// 发现端点 - 提供OIDC提供者的元数据
	Discovery(context.Context, *DiscoveryRequest) (*DiscoveryReply, error)
	// JWKS端点 - 提供用于验证JWT签名的公钥集
	JWKS(context.Context, *JWKSRequest) (*JWKSReply, error)
	// 令牌撤销端点 - 撤销访问令牌或刷新令牌
	Revoke(context.Context, *RevokeRequest) (*RevokeReply, error)
	// 登出端点 - 结束用户会话
	Logout(context.Context, *LogoutRequest) (*LogoutReply, error)
	mustEmbedUnimplementedOIDCServer()
}

// UnimplementedOIDCServer must be embedded to have forward compatible implementations.
type UnimplementedOIDCServer struct {
}

func (UnimplementedOIDCServer) Authorize(context.Context, *AuthorizeRequest) (*AuthorizeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Authorize not implemented")
}
func (UnimplementedOIDCServer) Token(context.Context, *TokenRequest) (*TokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Token not implemented")
}
func (UnimplementedOIDCServer) UserInfo(context.Context, *UserInfoRequest) (*UserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfo not implemented")
}
func (UnimplementedOIDCServer) Discovery(context.Context, *DiscoveryRequest) (*DiscoveryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Discovery not implemented")
}
func (UnimplementedOIDCServer) JWKS(context.Context, *JWKSRequest) (*JWKSReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JWKS not implemented")
}
func (UnimplementedOIDCServer) Revoke(context.Context, *RevokeRequest) (*RevokeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Revoke not implemented")
}
func (UnimplementedOIDCServer) Logout(context.Context, *LogoutRequest) (*LogoutReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedOIDCServer) mustEmbedUnimplementedOIDCServer() {}

// UnsafeOIDCServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OIDCServer will
// result in compilation errors.
type UnsafeOIDCServer interface {
	mustEmbedUnimplementedOIDCServer()
}

func RegisterOIDCServer(s grpc.ServiceRegistrar, srv OIDCServer) {
	s.RegisterService(&OIDC_ServiceDesc, srv)
}

func _OIDC_Authorize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthorizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OIDCServer).Authorize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OIDC_Authorize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OIDCServer).Authorize(ctx, req.(*AuthorizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OIDC_Token_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OIDCServer).Token(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OIDC_Token_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OIDCServer).Token(ctx, req.(*TokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OIDC_UserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OIDCServer).UserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OIDC_UserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OIDCServer).UserInfo(ctx, req.(*UserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OIDC_Discovery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscoveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OIDCServer).Discovery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OIDC_Discovery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OIDCServer).Discovery(ctx, req.(*DiscoveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OIDC_JWKS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JWKSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OIDCServer).JWKS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OIDC_JWKS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OIDCServer).JWKS(ctx, req.(*JWKSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OIDC_Revoke_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OIDCServer).Revoke(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OIDC_Revoke_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OIDCServer).Revoke(ctx, req.(*RevokeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OIDC_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OIDCServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OIDC_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OIDCServer).Logout(ctx, req.(*LogoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OIDC_ServiceDesc is the grpc.ServiceDesc for OIDC service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OIDC_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.auth.v1.oidc.OIDC",
	HandlerType: (*OIDCServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Authorize",
			Handler:    _OIDC_Authorize_Handler,
		},
		{
			MethodName: "Token",
			Handler:    _OIDC_Token_Handler,
		},
		{
			MethodName: "UserInfo",
			Handler:    _OIDC_UserInfo_Handler,
		},
		{
			MethodName: "Discovery",
			Handler:    _OIDC_Discovery_Handler,
		},
		{
			MethodName: "JWKS",
			Handler:    _OIDC_JWKS_Handler,
		},
		{
			MethodName: "Revoke",
			Handler:    _OIDC_Revoke_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _OIDC_Logout_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "auth/v1/oidc/oidc.proto",
}
