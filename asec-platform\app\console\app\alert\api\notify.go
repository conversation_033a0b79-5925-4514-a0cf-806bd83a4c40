package api

import (
	alertComm "asdsec.com/asec/platform/app/console/app/alert/common"
	"asdsec.com/asec/platform/app/console/app/alert/model"
	"asdsec.com/asec/platform/app/console/app/alert/service"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/app/console/common/utils"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/middleware"
	"asdsec.com/asec/platform/app/console/utils/web"
	"github.com/gin-gonic/gin"
	"net/url"
	"strconv"
	"time"
)

type RobotListReq struct {
	RobotName string `form:"robot_name" json:"robot_name" binding:"omitempty,min=1,max=50,regexEnum=COMMON_NAME"`
	RobotPlat string `form:"robot_plat" json:"robot_plat" binding:"omitempty,oneof=wechat dingtalk"`
	Limit     *int   `form:"limit" json:"limit" binding:"required,min=1"`
	Offset    *int   `form:"offset" json:"offset" binding:"required,min=0"`
}

func RobotList(c *gin.Context) {
	var req RobotListReq
	if err := c.ShouldBind(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		global.SysLog.Error(err.Error())
		return
	}
	tenantId, _ := strconv.ParseUint(web.GetCorpId(c), 10, 64)
	res, err := service.GetNotifyService().GetAllRobot(c, tenantId, req.RobotName, req.RobotPlat, *req.Limit, *req.Offset)
	if err != nil {
		common.Fail(c, common.QueryNotifyErr)
		global.SysLog.Error(err.Error())
		return
	}
	common.OkWithData(c, res)
}

type RobotReq struct {
	RobotName   string `json:"robot_name" binding:"required,min=1,max=50,regexEnum=COMMON_NAME"`
	RobotPlat   string `json:"robot_plat" binding:"required,oneof=wechat dingtalk"` //暂时仅支持微信和钉钉
	WebhookAddr string `json:"webhook_addr" binding:"required,min=1,max=150,regexEnum=URL_REG"`
	SecretKey   string `json:"secret_key" binding:"omitempty,min=1,max=150,regexEnum=COMMON_WHITELIST_REG"`
}

func RobotAdd(c *gin.Context) {
	c.Set(middleware.LogCtx, middleware.MarshalLog(c, common.AlertModuleName, common.AlertAddRobot))
	var req RobotReq
	if err := c.ShouldBindJSON(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		global.SysLog.Error(err.Error())
		return
	}
	tenantId, _ := strconv.ParseUint(web.GetCorpId(c), 10, 64)
	robotSetting := model.NotifySetting{
		TenantId:    tenantId,
		RobotName:   req.RobotName,
		PlatName:    req.RobotPlat,
		WebhookAddr: req.WebhookAddr,
		ApiSecret:   req.SecretKey,
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}
	err := service.GetNotifyService().AddRobot(c, tenantId, &robotSetting)
	if err != nil {
		if err.Error() == alertComm.ERRRobotExit {
			//机器名已经存在
			common.Fail(c, common.NotifyExit)
			global.SysLog.Error(err.Error())
			return
		}
		common.Fail(c, common.AddNotifyErr)
		global.SysLog.Error(err.Error())
		return
	}
	common.Ok(c)
}

type RobotEditReq struct {
	RobotId uint64 `form:"id" json:"id" binding:"required,min=1"`
	RobotReq
}

func RobotEdit(c *gin.Context) {
	c.Set(middleware.LogCtx, middleware.MarshalLog(c, common.AlertModuleName, common.AlertEditRobot))
	var req RobotEditReq
	if err := c.ShouldBindJSON(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		global.SysLog.Error(err.Error())
		return
	}
	tenantId, _ := strconv.ParseUint(web.GetCorpId(c), 10, 64)
	_, err := service.GetNotifyService().GetRobotById(c, tenantId, req.RobotId)
	if err != nil {
		if err.Error() == alertComm.ErrRecordNotFound {
			common.Fail(c, common.RobotNotExitErr)
			global.SysLog.Error(err.Error())
			return
		}
		common.Fail(c, common.EditNotifyErr)
		global.SysLog.Error(err.Error())
		return
	}
	robotSetting := model.NotifySetting{
		RobotName:   req.RobotName,
		PlatName:    req.RobotPlat,
		WebhookAddr: req.WebhookAddr,
		ApiSecret:   req.SecretKey,
		UpdateTime:  time.Now(),
	}
	err = service.GetNotifyService().UpdateRobot(c, tenantId, req.RobotId, &robotSetting)
	if err != nil {
		common.Fail(c, common.EditNotifyErr)
		global.SysLog.Error(err.Error())
		return
	}
	common.Ok(c)
}

type RobotDelReq struct {
	RobotId []uint64 `json:"id" binding:"required,min=1"`
}

func RobotDel(c *gin.Context) {
	c.Set(middleware.LogCtx, middleware.MarshalLog(c, common.AlertModuleName, common.AlertDelRobot))
	var req RobotDelReq
	if err := c.ShouldBindJSON(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		global.SysLog.Error(err.Error())
		return
	}
	tenantId, _ := strconv.ParseUint(web.GetCorpId(c), 10, 64)
	err := service.GetNotifyService().DelRobot(c, tenantId, req.RobotId)
	if err != nil {
		common.Fail(c, common.DelNotifyErr)
		global.SysLog.Error(err.Error())
		return
	}
	common.Ok(c)
}

func NotifyTest(c *gin.Context) {
	var req RobotReq
	if err := c.ShouldBindJSON(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		global.SysLog.Error(err.Error())
		return
	}
	webHook := req.WebhookAddr
	//对webhook做进一步的校验
	newUrl, err := url.Parse(webHook)
	if err != nil {
		common.Fail(c, common.IllegalAddrErr)
		return
	}
	newUrlHost := newUrl.Hostname()
	machineIp, err := common.GetLocalIp()
	if err != nil {
		machineIp = []string{alertComm.LocalIp}
	}
	if newUrlHost == alertComm.LocalIp || utils.InArray(newUrlHost, machineIp) {
		common.Fail(c, common.IllegalAddrErr)
		return
	}
	secret := req.SecretKey
	plat := req.RobotPlat
	if plat == "dingtalk" {
		if webHook == "" || secret == "" {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		err := service.GetNotifyService().SendDingMsgTest(c, webHook, secret)
		if err != nil {
			common.Fail(c, common.SendDingTalkErr)
			global.SysLog.Error(err.Error())
			return
		}
	} else if plat == "wechat" {
		err := service.GetNotifyService().SendWxTest(c, webHook)
		if err != nil {
			common.Fail(c, common.SendWxErr)
			global.SysLog.Error(err.Error())
			return
		}
	} else {
		common.Fail(c, common.ParamInvalidError)
		return
	}

}
