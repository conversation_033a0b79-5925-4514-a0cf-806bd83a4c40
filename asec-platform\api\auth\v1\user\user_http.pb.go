// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.1
// source: auth/v1/user/user.proto

package user

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserGetLoginUserInfo = "/api.auth.v1.user.User/GetLoginUserInfo"
const OperationUserLogout = "/api.auth.v1.user.User/Logout"
const OperationUserRedirectVerify = "/api.auth.v1.user.User/RedirectVerify"
const OperationUserUpdatePasswd = "/api.auth.v1.user.User/UpdatePasswd"

type UserHTTPServer interface {
	GetLoginUserInfo(context.Context, *GetLoginUserInfoReq) (*GetLoginUserInfoReply, error)
	Logout(context.Context, *LogoutReq) (*LogoutReply, error)
	RedirectVerify(context.Context, *RedirectVerifyRequest) (*RedirectVerifyReply, error)
	UpdatePasswd(context.Context, *UpdatePasswordReq) (*UpdatePasswordReply, error)
}

func RegisterUserHTTPServer(s *http.Server, srv UserHTTPServer) {
	r := s.Route("/")
	r.GET("/auth/user/v1/login_user", _User_GetLoginUserInfo0_HTTP_Handler(srv))
	r.PUT("/auth/user/v1/password", _User_UpdatePasswd0_HTTP_Handler(srv))
	r.POST("/auth/user/v1/logout", _User_Logout0_HTTP_Handler(srv))
	r.GET("/auth/user/v1/redirect_verify", _User_RedirectVerify0_HTTP_Handler(srv))
}

func _User_GetLoginUserInfo0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLoginUserInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGetLoginUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLoginUserInfo(ctx, req.(*GetLoginUserInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLoginUserInfoReply)
		return ctx.Result(200, reply)
	}
}

func _User_UpdatePasswd0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdatePasswordReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUpdatePasswd)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdatePasswd(ctx, req.(*UpdatePasswordReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdatePasswordReply)
		return ctx.Result(200, reply)
	}
}

func _User_Logout0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LogoutReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserLogout)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Logout(ctx, req.(*LogoutReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LogoutReply)
		return ctx.Result(200, reply)
	}
}

func _User_RedirectVerify0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RedirectVerifyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserRedirectVerify)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RedirectVerify(ctx, req.(*RedirectVerifyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RedirectVerifyReply)
		return ctx.Result(200, reply)
	}
}

type UserHTTPClient interface {
	GetLoginUserInfo(ctx context.Context, req *GetLoginUserInfoReq, opts ...http.CallOption) (rsp *GetLoginUserInfoReply, err error)
	Logout(ctx context.Context, req *LogoutReq, opts ...http.CallOption) (rsp *LogoutReply, err error)
	RedirectVerify(ctx context.Context, req *RedirectVerifyRequest, opts ...http.CallOption) (rsp *RedirectVerifyReply, err error)
	UpdatePasswd(ctx context.Context, req *UpdatePasswordReq, opts ...http.CallOption) (rsp *UpdatePasswordReply, err error)
}

type UserHTTPClientImpl struct {
	cc *http.Client
}

func NewUserHTTPClient(client *http.Client) UserHTTPClient {
	return &UserHTTPClientImpl{client}
}

func (c *UserHTTPClientImpl) GetLoginUserInfo(ctx context.Context, in *GetLoginUserInfoReq, opts ...http.CallOption) (*GetLoginUserInfoReply, error) {
	var out GetLoginUserInfoReply
	pattern := "/auth/user/v1/login_user"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGetLoginUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) Logout(ctx context.Context, in *LogoutReq, opts ...http.CallOption) (*LogoutReply, error) {
	var out LogoutReply
	pattern := "/auth/user/v1/logout"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserLogout))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) RedirectVerify(ctx context.Context, in *RedirectVerifyRequest, opts ...http.CallOption) (*RedirectVerifyReply, error) {
	var out RedirectVerifyReply
	pattern := "/auth/user/v1/redirect_verify"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserRedirectVerify))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UpdatePasswd(ctx context.Context, in *UpdatePasswordReq, opts ...http.CallOption) (*UpdatePasswordReply, error) {
	var out UpdatePasswordReply
	pattern := "/auth/user/v1/password"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUpdatePasswd))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
