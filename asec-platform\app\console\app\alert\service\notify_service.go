package service

import (
	"asdsec.com/asec/platform/app/console/app/alert/model"
	"asdsec.com/asec/platform/app/console/app/alert/plat/dingtalk"
	"asdsec.com/asec/platform/app/console/app/alert/plat/dingtalk/robot"
	"asdsec.com/asec/platform/app/console/app/alert/plat/wechat"
	"asdsec.com/asec/platform/app/console/app/alert/plat/wechat/message"
	"asdsec.com/asec/platform/app/console/app/alert/repository"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"go.uber.org/zap"
	"io/ioutil"
	"sync"
)

var NotifyInstance NotifyService

var NotifyInit sync.Once

type NotifyDB struct {
	db repository.NotifyRepository
}

func (n *NotifyDB) AddRobot(ctx context.Context, tenantId uint64, robot *model.NotifySetting) error {
	return n.db.AddRobot(ctx, tenantId, robot)
}

func (n *NotifyDB) DelRobot(ctx context.Context, tenantId uint64, robotId []uint64) error {
	return n.db.DelRobot(ctx, tenantId, robotId)
}

func (n *NotifyDB) UpdateRobot(ctx context.Context, tenantId, robotId uint64, robot *model.NotifySetting) error {
	return n.db.UpdateRobot(ctx, tenantId, robotId, robot)
}

func (n *NotifyDB) GetRobotById(ctx context.Context, tenantId, robotId uint64) (*model.NotifySetting, error) {
	return n.db.GetRobot(ctx, tenantId, robotId)
}

func (n *NotifyDB) GetAllRobot(ctx context.Context, tenantId uint64, robotName, robotPlat string, limit, offset int) (map[string]interface{}, error) {
	res, err := n.db.QueryAllRobot(ctx, tenantId, robotName, robotPlat, limit, offset)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func GetNotifyService() NotifyService {
	NotifyInit.Do(func() {
		NotifyInstance = &NotifyDB{db: repository.NewNotifyRepository()}
	})
	return NotifyInstance
}

func (n *NotifyDB) GetRobots(ctx context.Context, tenantId uint64) ([]model.NotifySetting, error) {
	return n.db.GetRobots(ctx, tenantId)
}

func (n *NotifyDB) GetRobotByPlat(ctx context.Context, tenantId uint64, plat string) ([]model.NotifySetting, error) {
	return n.db.GetRobotByPlat(ctx, tenantId, plat)
}

func (n *NotifyDB) SendWxTest(ctx context.Context, hook string) error {
	bot := wechat.QyBot{
		WebHook: hook,
	}
	msg := message.Message{
		MsgType: message.TextStr,
		Text: message.Text_{
			Content: "这是告警测试信息",
		},
	}
	send, err := bot.Send(msg)
	if err != nil {
		return err
	}
	global.SysLog.Debug("发送消息成功", zap.Any("message:", send))
	return nil
}

func (n *NotifyDB) SendDingMsgTest(ctx context.Context, webHook, secret string) error {
	//文本类型
	dt := dingtalk.New(webHook, dingtalk.WithSecret(secret))
	textContent := "这是告警测试信息"
	atMobiles := robot.SendWithIsAtAll(false)
	if err := dt.RobotSendText(textContent, atMobiles); err != nil {
		global.SysLog.Error(err.Error())
		return err
	}
	return printResult(dt)
}

func printResult(dt *dingtalk.DingTalk) error {
	response, err := dt.GetResponse()
	if err != nil {
		return err
	}
	reqBody, err := response.Request.GetBody()
	if err != nil {
		return err
	}
	reqData, err := ioutil.ReadAll(reqBody)
	if err != nil {
		return err
	}
	global.SysLog.Debug("发送消息成功", zap.String("message:", string(reqData)))
	return nil
}

type NotifyService interface {
	AddRobot(ctx context.Context, tenantId uint64, robot *model.NotifySetting) error
	GetAllRobot(ctx context.Context, tenantId uint64, robotName, robotPlat string, limit, offset int) (map[string]interface{}, error)
	DelRobot(ctx context.Context, tenantId uint64, robotId []uint64) error
	UpdateRobot(ctx context.Context, tenantId, robotId uint64, robot *model.NotifySetting) error
	GetRobotById(ctx context.Context, tenantId, robotId uint64) (*model.NotifySetting, error)
	SendWxTest(ctx context.Context, hook string) error
	SendDingMsgTest(ctx context.Context, webHook, secret string) error
	GetRobots(ctx context.Context, tenantId uint64) ([]model.NotifySetting, error)
	GetRobotByPlat(ctx context.Context, tenantId uint64, plat string) ([]model.NotifySetting, error)
}
