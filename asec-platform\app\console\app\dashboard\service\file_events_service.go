package service

import (
	"asdsec.com/asec/platform/app/console/app/dashboard/common"
	"asdsec.com/asec/platform/app/console/app/dashboard/repository"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"
)

type FileEventsServiceImpl interface {
	QuerySendTop(ctx context.Context, param common.QueryTimeParam) ([]common.SendTopItem, error)
	QuerySensitiveSendTop(ctx context.Context, param common.QueryTimeParam) ([]common.SensitiveSendTopItem, error)
	QueryDataAccessLogTop(ctx context.Context, param common.QueryTimeParam) ([]common.DataAccessLogTopItem, error)
}

func GetFileEventsService() FileEventsServiceImpl {
	fileEventsServInitPriv.Do(func() {
		fileEventsInstance = &fileEventsServicePriv{repository.NewFileEventsRepository()}
	})
	return fileEventsInstance
}

// private
var fileEventsServInitPriv sync.Once
var fileEventsInstance FileEventsServiceImpl

type fileEventsServicePriv struct {
	db repository.FileEventsRepositoryImpl
}

func (self *fileEventsServicePriv) QuerySendTop(ctx context.Context, param common.QueryTimeParam) ([]common.SendTopItem, error) {
	var result []common.SendTopItem
	var dbParam repository.TimeParam
	var err error
	dbParam.EndTime, err = time.Parse("2006-01-02 15:04:05", param.EndTime)
	if err != nil {
		return nil, err
	}
	dbParam.StartTime, err = time.Parse("2006-01-02 15:04:05", param.StartTime)
	if err != nil {
		return nil, err
	}
	allChannel, err := commonApi.GetAllChannelInfo(ctx)
	if err != nil {
		return nil, err
	}
	channelInfoList, err := self.db.QueryChannelInfo(ctx, dbParam)
	if err != nil {
		return nil, err
	}
	for _, it := range channelInfoList {
		result = append(result, common.SendTopItem{
			Count:   it.Count,
			Size:    it.FileSizeSum,
			Code:    it.Channel,
			BuiltIn: allChannel[it.Channel].BuiltIn,
		})
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].Count > result[j].Count
	})
	timeAggDataList, err := self.db.QueryTimeAggData(ctx, dbParam)
	if err != nil {
		return nil, err
	}
	for _, it := range timeAggDataList {
		index := -1
		for k, v := range result {
			if v.Code == it.Channel {
				index = k
			}
		}
		if index >= 0 {
			result[index].Data = append(result[index].Data, common.TimeAggData{
				Time:  it.Day.String(),
				Count: it.Count,
			})
		}
	}

	for i, v := range result {
		//将时间序列转换为字典加快速度
		timeMap := make(map[string]int, 0)
		for _, v := range v.Data {
			timeMap[v.Time] = v.Count
		}
		result[i].Data = result[i].Data[:0]
		//遍历时间列表
		for d := dbParam.StartTime; d.Before(dbParam.EndTime) || d.Equal(dbParam.EndTime); d = d.AddDate(0, 0, 1) {
			if _, ok := timeMap[d.String()]; ok {
				result[i].Data = append(result[i].Data, common.TimeAggData{
					Time:  d.String(),
					Count: timeMap[d.String()],
				})
			} else {
				result[i].Data = append(result[i].Data, common.TimeAggData{
					Time:  d.String(),
					Count: 0,
				})
			}

		}
	}

	return result, err
}

func (self *fileEventsServicePriv) QuerySensitiveSendTop(ctx context.Context, param common.QueryTimeParam) ([]common.SensitiveSendTopItem, error) {
	var result []common.SensitiveSendTopItem
	var err error
	startT, err := time.Parse("2006-01-02 15:04:05", param.StartTime)
	if err != nil {
		return nil, err
	}
	endT, err := time.Parse("2006-01-02 15:04:05", param.EndTime)
	if err != nil {
		return nil, err
	}
	data, err := self.db.GetSensitiveSendData(ctx, startT, endT)
	if err != nil {
		return nil, err
	}
	userMap := make(map[string][]string)
	dataMap := make(map[string]common.SensitiveSendTopItem)
	dateCountMap := make(map[string]int)
	for _, v := range data {
		//user
		userList := make([]string, 0)
		if users, ok := userMap[v.SensitiveRuleId]; ok {
			userList = append(userList, users...)
		}
		userList = append(userList, v.UserId)
		userMap[v.SensitiveRuleId] = userList
		//data
		dataTmp := common.SensitiveSendTopItem{
			SensitiveRuleId:   v.SensitiveRuleId,
			SensitiveRuleName: v.SensitiveRuleName,
			SensitiveLevel:    v.SensitiveLevel,
			Count:             1,
			Size:              v.FileSize,
		}
		if stg, ok := dataMap[v.SensitiveRuleId]; ok {
			dataTmp.Size = dataTmp.Size + stg.Size
			dataTmp.Count = dataTmp.Count + stg.Count
		}
		dataMap[v.SensitiveRuleId] = dataTmp
		//date
		count := 1
		dateKey := fmt.Sprintf("%s_%s", v.SensitiveRuleId, v.OccurTime.Format("2006-01-02"))
		if dateCount, ok := dateCountMap[dateKey]; ok {
			count = dateCount + count
		}
		dateCountMap[dateKey] = count
	}
	for id, v := range dataMap {
		commonTime := make([]common.TimeAggData, 0)
		for day := startT; day.Before(endT); day = day.AddDate(0, 0, 1) {
			commonTime = append(commonTime, common.TimeAggData{
				Time:  fmt.Sprintf("%s %s", day.Format("2006-01-02"), "00:00:00 +0000 UTC"),
				Count: dateCountMap[fmt.Sprintf("%s_%s", id, day.Format("2006-01-02"))],
			})
		}
		item := common.SensitiveSendTopItem{
			SensitiveRuleId:   dataMap[id].SensitiveRuleId,
			SensitiveRuleName: dataMap[id].SensitiveRuleName,
			SensitiveLevel:    dataMap[id].SensitiveLevel,
			UserName:          utils.SliceUniqueWithMap(userMap[v.SensitiveRuleId]),
			Count:             dataMap[id].Count,
			Size:              dataMap[id].Size,
			Data:              commonTime,
		}
		result = append(result, item)
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].Count > result[j].Count
	})
	return result, err
}

func (self *fileEventsServicePriv) QueryDataAccessLogTop(ctx context.Context, param common.QueryTimeParam) ([]common.DataAccessLogTopItem, error) {
	var result []common.DataAccessLogTopItem
	var err error
	startT, err := time.Parse("2006-01-02 15:04:05", param.StartTime)
	if err != nil {
		return nil, err
	}
	endT, err := time.Parse("2006-01-02 15:04:05", param.EndTime)
	if err != nil {
		return nil, err
	}

	fileEvents, err := self.db.GetSensitiveDownloadData(ctx, startT, endT)
	if err != nil {
		return nil, err
	}
	countMap := make(map[string]int, 0)
	sizeMap := make(map[string]int64, 0)
	userIdMap := make(map[string][]string, 0)
	sensitiveMap := make(map[string][]common.SensitiveData, 0)
	sensitiveBoolMap := make(map[string]bool)
	dateCountMap := make(map[string]int)
	itemIdMap := make(map[string]common.DataAccessLogTopItem, 0)
	for _, v := range fileEvents {
		item := common.DataAccessLogTopItem{
			SourceId:   v.SourceId,
			SourceName: v.SourceName,
			SourceType: v.SourceType,
		}
		if v.SourceId == "" {
			item.SourceId = v.SrcHost
			item.SourceName = v.SrcHost
			if v.SrcHost == "" {
				item.SourceId = GetHostFromBlobURL(v.SrcPath)
				item.SourceName = GetHostFromBlobURL(v.SrcPath)
			}
		}
		itemIdMap[item.SourceId] = item
		// count
		count := 1
		if itemCount, ok := countMap[item.SourceId]; ok {
			count = itemCount + count
		}
		countMap[item.SourceId] = count
		// size
		size := v.FileSize
		if itemSize, ok := sizeMap[item.SourceId]; ok {
			size = itemSize + size
		}
		sizeMap[item.SourceId] = size
		// user
		userIdList := make([]string, 0)
		if v.UserId != "" {
			userIdList = append(userIdList, v.UserId)
		}
		if userIds, ok := userIdMap[item.SourceId]; ok {
			userIdList = append(userIdList, userIds...)
		}
		userIdMap[item.SourceId] = userIdList
		// sensitive info
		sensitiveList := make([]common.SensitiveData, 0)
		sensitiveList = append(sensitiveList, common.SensitiveData{
			SensitiveLevel:    v.SensitiveLevel,
			SensitiveRuleId:   v.SensitiveRuleId,
			SensitiveRuleName: v.SensitiveRuleName,
		})
		if sensitiveBool, _ := sensitiveBoolMap[fmt.Sprintf("%s_%s", item.SourceId, v.SensitiveRuleId)]; !sensitiveBool {
			sensitiveList = append(sensitiveList, sensitiveMap[v.SourceId]...)
			sensitiveBoolMap[fmt.Sprintf("%s_%s", item.SourceId, v.SensitiveRuleId)] = true
		}
		sensitiveMap[item.SourceId] = sensitiveList

		// date count
		date := v.OccurTime.Format("2006-01-02")
		dateCount := 1
		if itemDateCount, ok := dateCountMap[fmt.Sprintf("%s_%s", item.SourceId, date)]; ok {
			dateCount = itemDateCount + dateCount
		}
		dateCountMap[fmt.Sprintf("%s_%s", item.SourceId, date)] = dateCount
	}
	// time
	for id, v := range itemIdMap {
		commonTime := make([]common.TimeAggData, 0)
		for day := startT; day.Before(endT); day = day.AddDate(0, 0, 1) {
			commonTime = append(commonTime, common.TimeAggData{
				Time:  fmt.Sprintf("%s %s", day.Format("2006-01-02"), "00:00:00 +0000 UTC"),
				Count: dateCountMap[fmt.Sprintf("%s_%s", id, day.Format("2006-01-02"))],
			})
		}
		item := common.DataAccessLogTopItem{
			SourceId:      id,
			SourceName:    v.SourceName,
			SourceType:    v.SourceType,
			Count:         countMap[id],
			UserIds:       userIdMap[id],
			Size:          sizeMap[id],
			SensitiveData: sensitiveMap[id],
			Data:          commonTime,
		}
		result = append(result, item)
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].Count > result[j].Count
	})
	return result, err
}

func GetHostFromBlobURL(blobURL string) string {
	parts := strings.SplitN(blobURL, "://", 2)
	if len(parts) < 2 {
		return ""
	}
	return strings.Split(parts[1], ":")[0]
}
