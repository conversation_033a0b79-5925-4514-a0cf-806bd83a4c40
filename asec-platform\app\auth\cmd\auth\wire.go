//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/conf"
	"asdsec.com/asec/platform/app/auth/internal/data"
	"asdsec.com/asec/platform/app/auth/internal/job"
	"asdsec.com/asec/platform/app/auth/internal/server"
	"asdsec.com/asec/platform/app/auth/internal/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, job.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}
