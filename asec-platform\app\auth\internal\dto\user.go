package dto

import (
	"time"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

type CredType string

const (
	CredTypePassword = CredType(AttrValPasswd)
)

type ExpireType string

const (
	ExpireTypeCustom  ExpireType = "custom"
	ExpireTypeForever ExpireType = "forever"
)

type FieldMapType string

const (
	FieldMapTypeSync FieldMapType = "sync"
	FieldMapTypeAuth FieldMapType = "auth"
)

type User struct {
	ID          string
	Name        string
	GroupID     string
	GroupName   string
	CorpID      string
	SourceID    string
	SourceType  string
	SourceName  string
	Phone       string
	Email       string
	Avatar      string
	Enable      bool
	ExpireType  ExpireType
	ExpireEnd   time.Time
	Roles       []RoleInfo
	CreatedAt   time.Time
	DisplayName string
	Identify    string `json:"identifier"`
	AuthType    string `json:"auth_type"`
	ActiveTime  time.Time
	LockStatus  bool
	SecurityCode string
}

type ListUserResult struct {
	Users    []User
	Count    int64
	IdleTime string
}

type UserInfo struct {
	RoleID      string
	ID          string
	Name        string
	DisplayName string
	Path        string
}

type CreateUserParam struct {
	CorpId      string
	Name        string
	GroupId     string
	Phone       string
	Email       string
	Password    string
	Enable      bool
	ExpireType  ExpireType
	ExpireEnd   int64
	DisplayName string
	RoleIds     []string
	Identifier  string
}

type UpdateUserParam struct {
	Id string
	CreateUserParam
}

type CreateUserDaoParam struct {
	CorpId      string
	Id          string
	Name        string
	TrueName    string
	NickName    string
	GroupId     string
	SourceId    string
	Phone       string
	Email       string
	ExpireType  ExpireType
	ExpireEnd   int64
	DisplayName string
	RootGroupID string
	Identifier  string
	Enable      *bool
	AuthType    string
}

type CreatCredParam struct {
	CorpId string
	UserId string

	CredType CredType
	Password string
}

type UpdateCredParam struct {
	CorpId string
	UserId string

	CredType    CredType
	Password    string
	NewPassword string
}

type ExternalInfo struct {
	Depts  []*ExternalDepartment
	Users  []*ExternalUser
	RootId string
}
type NeedChangeExternalInfo struct {
	// dept
	AddDeptList    []*ExternalDepartment
	ExistDeptIds   []string
	ChangeDeptList []*ExternalDepartment
	DeptIdMap      map[string]string
	// user
	AddUserList    []*ExternalUser
	ExistUserIds   []string
	ChangeUserList []*ExternalUser
}

type SyncDeptAndUser struct {
	LocalRootGroupId string
	// ext dept and user
	ExistDeptIds []string
	Depts        []*model.TbExternalDepartment
	ExistUserIds []string
	Users        []*model.TbExternalUser
	// local dept and user
	LocalGroups []*model.TbUserGroup
	LocalUsers  []*model.TbUserEntity
}

type WxInfo struct {
	Depts  []*WxDepartment
	Users  []*WxUser
	RootId int64
}

type FeishuInfo struct {
	Depts  []*FeishuDepartment
	Users  []*FsUser
	RootId *string
}

var FieldOptions = []KV{
	{Key: "name", Value: "用户名"},
	{Key: "display_name", Value: "显示用户名"},
	{Key: "true_name", Value: "真实用户名"},
	{Key: "nick_name", Value: "昵称"},
	{Key: "phone", Value: "手机号码"},
	{Key: "email", Value: "邮箱"},
	{Key: "user_id", Value: "用户标识"},
	{Key: "group_name", Value: "组织名"},
}

var qiyeWxDefaultFieldMapSync = []KV{
	{Key: "userid", Value: "name"},
	{Key: "name", Value: "display_name"},
}

var feiShuDefaultFieldMapSync = []KV{
	{Key: "userid", Value: "name"},
	{Key: "name", Value: "display_name"},
	{Key: "mobile", Value: "phone"},
}

var DdngtalkDefaultFieldMapSync = []KV{
	{Key: "user_id", Value: "name"},
	{Key: "name", Value: "display_name"},
	{Key: "mobile", Value: "phone"},
}

type MKV struct {
	User  []KV
	Group []KV
}

var LdapDefaultFieldMapSync = []KV{
	//User: []KV{
	//{Key: "用户过滤", Value: "(&(uid=*))"},
	//{Key: "组织过滤", Value: "(objectClass=organizationalUnit)"},
	{Key: "uidNumber", Value: "user_id"},
	{Key: "uid", Value: "name"},
	//{Key: "显示名", Value: "displayName"},
	//{Key: "描述", Value: "description"},
	//{Key: "组织名", Value: "ou"},
	//{Key: "状态", Value: "userAccountControl"},
	{Key: "telephoneNumber", Value: "phone"},
	{Key: "mail", Value: "email"},
	//{Key: "ou", Value: "组织名"},
	//{Key: "组织ID", Value: "entryUUID"},
	{Key: "ou", Value: "group_name"},
	//{Key: "组织描述", Value: "description"},
}

var MsadDefaultFieldMapSync = []KV{
	//{Key: "用户过滤", Value: "(&(objectClass=organizationalPerson)(!(objectClass=computer))(sAMAccountName=*))"},
	//{Key: "组织过滤", Value: "(objectClass=organizationalUnit)"},
	//{Key: "外部ID", Value: "objectGUID"},
	{Key: "sAMAccountName", Value: "name"},
	//{Key: "显示名", Value: "displayName"},
	//{Key: "描述", Value: "description"},
	//{Key: "所属组织", Value: "ou"},
	//{Key: "状态", Value: "userAccountControl"},
	{Key: "telephoneNumber", Value: "phone"},
	{Key: "mail", Value: "email"},

	{Key: "objectGUID", Value: "user_id"},
	{Key: "ou", Value: "group_name"},
	//{Key: "组织描述", Value: "description"},
}
var InfogoDefaultFieldMapSync = []KV{
	//User: []KV{
	//{Key: "用户过滤", Value: "(objectCategory=person)"},
	{Key: "外部ID", Value: "ID"},
	{Key: "用户名", Value: "UserName"},
	{Key: "显示名", Value: "TrueNames"},
	//{Key: "描述", Value: "description"},
	{Key: "所属组织", Value: "DepartId"},
	//{Key: "状态", Value: "userAccountControl"},
	{Key: "手机号码", Value: "Tel"},
	{Key: "邮箱", Value: "Email"},
	//},
	//Group: []KV{
	//{Key: "组织过滤", Value: "(ou=*)"},
	{Key: "组织ID", Value: "DepartID"},
	{Key: "组织名", Value: "DepartName"},
	//{Key: "组织描述", Value: "description"},
	//},
}
var ldapDefaultFieldMapAuth = []KV{
	{Key: "userid", Value: "name"},
}

var msadDefaultFieldMapAuth = []KV{
	{Key: "userid", Value: "name"},
}

var dingtalkDefaultFieldMapAuth = []KV{
	{Key: "userid", Value: "name"},
}

var qiyeWxDefaultFieldMapAuth = []KV{
	{Key: "userid", Value: "name"},
}

var feiShuDefaultFieldMapAuth = []KV{
	{Key: "user_id", Value: "name"},
}

var OAuth2FieldMap = map[FieldMapType][]KV{
	// 添加: OAuth2.0字段映射
	FieldMapTypeSync: {
		{Key: "userid", Value: "name"},
		{Key: "name", Value: "display_name"},
	},
	FieldMapTypeAuth: {
		{Key: "userid", Value: "name"},
	},
}

// 派拉字段映射
var PailaFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: {
		{Key: "app_account__account_no", Value: "name"},
		{Key: "app_account__account_name", Value: "display_name"},
		{Key: "idt_user__mobile", Value: "phone"},
		{Key: "idt_user__email", Value: "email"},
	},
	FieldMapTypeAuth: {
		{Key: "app_account__account_no", Value: "name"},
	},
}

// 竹云字段映射
var ZhuyunFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: {
		{Key: "userid", Value: "name"},
		{Key: "name", Value: "display_name"},
		{Key: "mobile", Value: "phone"},
		{Key: "email", Value: "email"},
	},
	FieldMapTypeAuth: {
		{Key: "userid", Value: "name"},
	},
}

// 泛微字段映射
var FanweiFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: {
		{Key: "userid", Value: "name"},
		{Key: "name", Value: "display_name"},
		{Key: "mobile", Value: "phone"},
		{Key: "email", Value: "email"},
	},
	FieldMapTypeAuth: {
		{Key: "userid", Value: "name"},
	},
}

// 浙政钉字段映射
var ZhezhengdingFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: {
		{Key: "userid", Value: "employeeCode"},                    // 用户ID映射到employeeCode
		{Key: "name", Value: "employeeName"},                      // 用户名映射到employeeName
		{Key: "app_account__account_no", Value: "employeeCode"},   // 账号ID映射到employeeCode
		{Key: "app_account__account_name", Value: "employeeName"}, // 账号名称映射到employeeName
		{Key: "idt_user__name", Value: "employeeName"},            // 用户名映射到employeeName
		// 浙政钉可能不提供这些字段，但为保持一致性添加
		{Key: "idt_user__mobile", Value: ""}, // 手机号
		{Key: "idt_user__email", Value: ""},  // 邮箱
	},
}
var QiyeWxFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: qiyeWxDefaultFieldMapSync,
	FieldMapTypeAuth: qiyeWxDefaultFieldMapAuth,
}

var FeiShuFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: feiShuDefaultFieldMapSync,
	FieldMapTypeAuth: feiShuDefaultFieldMapAuth,
}

var DingtalkFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: DdngtalkDefaultFieldMapSync,
	FieldMapTypeAuth: dingtalkDefaultFieldMapAuth,
}

var LdapFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: LdapDefaultFieldMapSync,
	FieldMapTypeAuth: ldapDefaultFieldMapAuth,
}

var MsadFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: MsadDefaultFieldMapSync,
	FieldMapTypeAuth: msadDefaultFieldMapAuth,
}

var InfogoFieldMap = map[FieldMapType][]KV{
	FieldMapTypeSync: InfogoDefaultFieldMapSync,
}
