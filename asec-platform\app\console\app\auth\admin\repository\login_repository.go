package repository

import (
	apRepo "asdsec.com/asec/platform/app/console/app/appliancemgt/repository"
	"asdsec.com/asec/platform/app/console/app/auth/admin/constants"
	model2 "asdsec.com/asec/platform/app/console/app/auth/admin/model"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/special_config"
	"asdsec.com/asec/platform/pkg/ntp"
	"asdsec.com/asec/platform/pkg/utils"
	"asdsec.com/asec/platform/pkg/utils/jwt_util"
	"asdsec.com/asec/platform/pkg/utils/license"
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt/v4"
	"golang.org/x/crypto/pbkdf2"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type LoginRepository interface {
	Login(ctx context.Context, req model2.AdminLoginReq) (model2.Token, error)
	RefreshToken(ctx context.Context, req model2.RefreshTokenReq) (model2.Token, error)
	GetEcdsaKeyByCorpId(ctx context.Context, corpId string, componentNameKey string) (string, error)
	GetAdminByCorpIdAndUserId(ctx context.Context, userId string, corpId string) (model2.Admin, error)
	Logout(ctx context.Context, userId, token string, tokenClaim jwt_util.TokenClaims) error
	GetLicenseInfo(ctx context.Context, corpId string) (license.GetLicenseInfoRsp, error)
	GetLicenseStatus(ctx context.Context, corpId string) (int, int, error)
	RecordLoginFail(ctx context.Context, name, corpId string) (int, error)
	LockUser(ctx context.Context, name string, corpId string, duration time.Duration) error
}

// NewLoginRepository 创建接口实现接口实现
func NewLoginRepository() LoginRepository {
	return &loginRepository{}
}

type loginRepository struct {
}

func (a *loginRepository) LockUser(ctx context.Context, name string, corpId string, duration time.Duration) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	//更新登录失败次数，并且查询返回当前失败次数
	err = db.Model(model2.Admin{}).
		Where("name = ? and corp_id = ?", name, corpId).Update("lock_time", utils.GetFormattedNowTime().Add(duration)).
		Error
	return err
}

func (a *loginRepository) RecordLoginFail(ctx context.Context, name, corpId string) (int, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, err
	}
	var failedTimes int
	//更新登录失败次数，并且查询返回当前失败次数
	err = db.Model(model2.Admin{}).
		Where("name = ? and corp_id = ?", name, corpId).Update("login_failed_times", gorm.Expr("login_failed_times + 1")).
		Select("login_failed_times").Scan(&failedTimes).Error
	return failedTimes, err
}

func (a *loginRepository) GetLicenseInfo(ctx context.Context, corpId string) (license.GetLicenseInfoRsp, error) {
	resp := license.GetLicenseInfoRsp{LicenseStatus: license.ExpireLicenseStatus}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		if err.Error() == constants.EmptyErrorMsg {
			return resp, nil
		}
		global.SysLog.Sugar().Errorf("get license GetDBClient. err=%v", err)
		return resp, err
	}
	licenseInfoStr, err := license.GetLicenseInfoFromPg(ctx, db, corpId)
	if err != nil {
		if err.Error() == constants.EmptyErrorMsg {
			return resp, nil
		}
		global.SysLog.Sugar().Errorf("get license expire time from pg failed. err=%v", err)
		return resp, err
	}
	var remoteTimeStr string
	// 这里重试3次，demo测试时发现udp方式获取云端ntp服务器的时间不稳定，容易超时
	for i := 0; i < license.GetRemoteTimeRetryTimes; i++ {
		remoteTimeStr, err = ntp.GetRemoteTime(global.SysConfig.License.RemoteAddr, global.SysConfig.License.Port)
		if err != nil {
			continue
		}
		break
	}
	// 三次之后错误还是不为nil，则判定为连不上云端，返回网络错误
	if err != nil {
		resp.LicenseStatus = license.CanNotConnectRemoteLicenseStatus
		return resp, err
	}
	global.SysLog.Debug(fmt.Sprintf("get remote time:%s", remoteTimeStr))
	resp, err = license.ParseLicenseInfo(licenseInfoStr, remoteTimeStr)
	if err != nil {
		global.SysLog.Sugar().Errorf("get license status by compare time. expire-time=%s,remote-time=%s, err=%v", licenseInfoStr, remoteTimeStr, err)
		return resp, err
	}

	usedCount, err := apRepo.NewAppRepository().CountAgent(ctx)
	if err != nil {
		return resp, err
	}
	threshold := int64(resp.AgentLimit) * license.LicenseNotEnoughThresholdPercent / 100
	if threshold <= 0 {
		threshold = 1
	}
	//正常状态下，且授权点数即将用完(小于总数10%并且小于100个),登录提示授权不足
	if resp.LicenseStatus == license.CorrectLicenseStatus && (int64(resp.AgentLimit)-usedCount) <= threshold && int64(resp.AgentLimit)-usedCount < 100 {
		resp.LicenseStatus = license.LicenseNotEnoughStatus
	}
	resp.Modules = license.AllLicenseModules()
	resp.UsedAgentCount = uint64(usedCount)
	return resp, nil
}

func (a *loginRepository) GetLicenseStatus(ctx context.Context, corpId string) (int, int, error) {
	licenseInfo, err := a.GetLicenseInfo(ctx, corpId)
	if err != nil {
		global.SysLog.Sugar().Errorf("get license status by compare time, err=%v", err)
		return licenseInfo.LicenseStatus, licenseInfo.Days, err
	}

	return licenseInfo.LicenseStatus, licenseInfo.Days, nil
}

func DecodeRsaMessageByPrivateKey(privateKeyStr string, enCodeStr string) (string, error) {
	if privateKeyStr == "" {
		return "", errors.New(constants.EmptyErrorMsg)
	}
	// 对base64编码的字符串进行解码
	decoded, err := base64.StdEncoding.DecodeString(enCodeStr)
	if err != nil {
		global.SysLog.Sugar().Errorf("base64 decode failed. encodeStr=%s, err=%v", enCodeStr, err)
		return "", err
	}

	block, _ := pem.Decode([]byte(("-----BEGIN RSA PRIVATE KEY-----\n" + privateKeyStr + "\n-----END RSA PRIVATE KEY-----")))
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		global.SysLog.Sugar().Errorf("parse rsa private key failed. private-key=%s, err=%v", privateKeyStr, err)
		return "", err
	}
	//根据私钥解密
	decrypted, err := rsa.DecryptPKCS1v15(rand.Reader, privateKey, decoded)
	if err != nil {
		global.SysLog.Sugar().Errorf("decrypt by private key failed. encodeStr=%s, private-key=%s, err=%v", enCodeStr, privateKeyStr, err)
		return "", err
	}
	return string(decrypted), nil
}

func (a *loginRepository) Logout(ctx context.Context, userId, token string, tokenClaim jwt_util.TokenClaims) error {
	redisDb, err := global.GetRedisClient(ctx)
	if err != nil {
		return err
	}
	duration, err := a.GetCurrentTokenDuration(tokenClaim)
	if err != nil {
		log.Errorf("get current claim failed. err=%v", err)
		return err
	}
	userKey := fmt.Sprintf("%s_%s_%s", constants.LogoutAdmin, constants.ActiveAdmin, token)
	err = redisDb.Set(ctx, userKey, constants.LogoutAdminDefaultValue, duration).Err()
	if err != nil {
		log.Errorf("set logout access token in redis failed. err=%v", err)
		return err
	}
	//set refresh token
	refreshToken, err := redisDb.Get(ctx, token).Result()
	if err != nil {
		log.Warnf("get refresh token failed. err=%v", err)
		return nil
	}
	refreshLogoutKey := fmt.Sprintf("%s_%s_%s", constants.LogoutAdmin, constants.ActiveAdmin, refreshToken)
	err = redisDb.Set(ctx, refreshLogoutKey, constants.LogoutAdminDefaultValue, duration).Err()
	if err != nil {
		log.Warnf("set logout refresh token in redis failed. err=%v", err)
		return nil
	}
	return err
}

func (a *loginRepository) Login(ctx context.Context, req model2.AdminLoginReq) (model2.Token, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model2.Token{}, err
	}
	var adminUser model2.Admin
	err = db.Transaction(func(tx *gorm.DB) error {
		err = db.Model(model2.Admin{}).
			Where("name = ? and corp_id = ? and (expire_time > now() OR expire_type = ?) and status = 1", req.Name, req.CorpId, constants.NeverExpireType).
			Find(&adminUser).Error
		if err != nil {
			log.Errorf("get admin user failed: %w", err)
			return err
		}
		if adminUser.Id == "" {
			return errors.New(constants.AdminLoginPwdInCorrectError)
		}
		//check if user locked
		if adminUser.LockTime.After(time.Now()) {
			return errors.New("用户锁定中，锁定时间至：" + adminUser.LockTime.Format("2006-01-02 15:04:05"))
		}
		var cred model2.AdminCredential
		err = db.Model(model2.AdminCredential{}).
			Where("user_id = ? and corp_id = ?", adminUser.Id, adminUser.CorpId).
			Find(&cred).Error
		if err != nil {
			log.Errorf("get admin credential error: %w", err)
			return errors.New(constants.AdminLoginPwdInCorrectError)
		}
		var credData model2.CredData
		err = json.Unmarshal([]byte(cred.CredentialData), &credData)
		if err != nil {
			log.Errorf("unmarshal failed. err=%v", err)
			return errors.New(constants.AdminLoginPwdInCorrectError)
		}
		if GetHashPassword(req.Password, credData) != cred.SecretData {
			log.Errorf("check password failed, user-name=%s, pwd=%s. err=%v", req.Name, req.Password, err)
			return errors.New(constants.AdminLoginPwdInCorrectError)
		}
		//reset lock times & lock time
		if adminUser.LoginFailedTimes > 0 {
			err = db.Model(model2.Admin{}).
				Where("name = ? and corp_id = ?", req.Name, req.CorpId).
				Update("login_failed_times", 0).
				Update("lock_time", time.Time{}).Error
		}

		return nil
	})
	if err != nil {
		return model2.Token{}, err
	}

	token, err := a.issueToken(ctx, adminUser, constants.AccessTokenTyp, jwt_util.TokenClaims{})
	if err != nil {
		log.Errorf("issue token failed. err=%v", err)
		return model2.Token{}, err
	}
	return token, err
}

func GetHashPassword(password string, credData model2.CredData) string {
	decodedSalt, _ := base64.StdEncoding.DecodeString(credData.UserSalt)
	res := pbkdf2.Key([]byte(password), decodedSalt, credData.Iter, credData.KeyLen, sha256.New)
	return base64.StdEncoding.EncodeToString(res)
}

// GetEcdsaKeyByCorpId 根据租户id和钥匙类型获取value
func (a *loginRepository) GetEcdsaKeyByCorpId(ctx context.Context, corpId string, componentNameKey string) (string, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return "", err
	}
	var value string
	cc := model2.AdminComponentConfig{}.TableName()
	c := model2.AdminComponent{}.TableName()
	err = db.Model(model2.AdminComponent{}).
		Select(fmt.Sprintf("%s.value", cc)).
		Joins(fmt.Sprintf("Left Join %s on %s.component_id = %s.id", cc, cc, c)).
		Where(fmt.Sprintf("%s.corp_id = ? and %s.name = ?", c, cc), corpId, componentNameKey).
		Find(&value).Error
	return value, err
}

// 签发token
func (a *loginRepository) issueToken(ctx context.Context, user model2.Admin, tokenType string, claims jwt_util.TokenClaims) (model2.Token, error) {
	key, err := a.GetEcdsaKeyByCorpId(ctx, user.CorpId, constants.PrivateKey)
	if err != nil {
		log.Errorf("GetEcdsaPrivateKey failed. err=%v", err)
		return model2.Token{}, err
	}
	// 签发token
	issuer := jwt_util.NewJwtIssuer(jwt_util.Ecdsa256)
	privateKey, err := issuer.LoadStringPrivateKey("-----BEGIN EC PRIVATE KEY-----\n" + key + "\n-----END EC PRIVATE KEY-----")
	if err != nil {
		log.Errorf("LoadStringPrivateKey failed. err=%v", err)
		return model2.Token{}, err
	}

	accessTokenDuration := a.GetTokenTime(constants.AccessTokenDuration)
	if accessTokenDuration == 0 {
		log.Errorf("GetAccessToken config failed. err=%v", err)
		return model2.Token{}, err
	}
	refreshTokenDuration := a.GetTokenTime(constants.RefreshTokenDuration)
	if refreshTokenDuration == 0 {
		log.Errorf("GetRefreshToken config failed. err=%v", err)
		return model2.Token{}, err
	}
	// 签发refresh_token
	refreshClaims := jwt_util.TokenClaims{
		Typ:  string(constants.RefreshTokenTyp),
		Name: user.Name,
		RegisteredClaims: jwt.RegisteredClaims{
			Subject: user.Id,
			ExpiresAt: &jwt.NumericDate{
				Time: time.Now().Add(refreshTokenDuration),
			},
		},
	}
	var refreshToken string
	if tokenType == constants.RefreshTokenTyp {
		// 获取当前access token duration
		accessTokenDuration, err = a.GetAccessTokenDuration(accessTokenDuration, claims)
		if err != nil {
			log.Errorf("GetAccessTokenDuration failed. err=%v", err)
			return model2.Token{}, err
		}
		// 获取refresh剩余duration
		refreshTokenDuration, err = a.GetCurrentTokenDuration(claims)
		if err != nil {
			log.Errorf("GetRefreshTokenDuration failed. err=%v", err)
			return model2.Token{}, err
		}
	} else {
		refreshToken, err = issuer.IssueToken(privateKey, refreshClaims)
		if err != nil {
			log.Errorf("IssueToken failed. err=%v", err)
			return model2.Token{}, err
		}
	}
	// 签发access_token
	accessClaims := jwt_util.TokenClaims{
		Typ:  constants.AccessTokenTyp,
		Name: user.Name,
		RegisteredClaims: jwt.RegisteredClaims{
			Subject: user.Id,
			ExpiresAt: &jwt.NumericDate{
				Time: time.Now().Add(accessTokenDuration),
			},
		},
	}
	accessToken, err := issuer.IssueToken(privateKey, accessClaims)
	if err != nil {
		log.Errorf("IssueToken failed. err=%v", err)
		return model2.Token{}, err
	}
	//设置redis缓存，token --> refresh token
	redisDb, err := global.GetRedisClient(ctx)
	if err != nil {
		log.Errorf("get redis client failed. err=%v", err)
		return model2.Token{}, err
	}
	err = redisDb.Set(ctx, accessToken, refreshToken, accessTokenDuration).Err()
	if err != nil {
		log.Errorf("set redis token/refresh failed. err=%v", err)
		return model2.Token{}, err
	}
	return model2.Token{
		AccessToken:     accessToken,
		ExpireIn:        int64(accessTokenDuration.Seconds()),
		RefreshToken:    refreshToken,
		RefreshExpireIn: int64(refreshTokenDuration.Seconds()),
		TokenType:       constants.TokenTypeBear,
	}, nil
}

func (a *loginRepository) RefreshToken(ctx context.Context, req model2.RefreshTokenReq) (model2.Token, error) {
	admin, err := a.GetAdminByCorpIdAndUserId(ctx, req.UserId, req.CorpId)
	if err != nil {
		log.Errorf("get current admin failed. err=%v", err)
		return model2.Token{}, err
	}
	token, err := a.issueToken(ctx, admin, constants.RefreshTokenTyp, req.Claim)
	if err != nil {
		log.Errorf("issueToken failed. err=%v", err)
		return model2.Token{}, err
	}
	token.RefreshToken = req.RefreshToken
	return token, nil
}

func (a *loginRepository) GetAdminByCorpIdAndUserId(ctx context.Context, userId string, corpId string) (model2.Admin, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model2.Admin{}, err
	}
	var res model2.Admin
	err = db.Model(model2.Admin{}).Where("id = ? and corp_id = ?", userId, corpId).Find(&res).Error
	return res, err
}

func (a *loginRepository) GetAccessToken(ctx context.Context, refreshToken string) (string, error) {
	redisDB, err := global.GetRedisClient(ctx)
	if err != nil {
		return "", err
	}
	accessToken, err := redisDB.Get(ctx, refreshToken).Result()
	return accessToken, err
}

func (a *loginRepository) GetTokenTime(key string) time.Duration {
	accessTokenDuration, err := strconv.ParseUint(special_config.GetSpecialConfig(key), 10, 64)
	if err != nil {
		log.Errorf("GetTokenDuration config failed. err=%v", err)
		return 0
	}
	return time.Duration(accessTokenDuration)
}
func (a *loginRepository) GetAccessTokenDuration(accessTokenDuration time.Duration, claim jwt_util.TokenClaims) (time.Duration, error) {
	expireTime := claim.RegisteredClaims.ExpiresAt
	if time.Now().Add(accessTokenDuration).After(expireTime.Time) {
		return expireTime.Time.Sub(time.Now()), nil
	}
	return accessTokenDuration, nil
}

func (a *loginRepository) GetCurrentTokenDuration(claim jwt_util.TokenClaims) (time.Duration, error) {
	expireTime := claim.RegisteredClaims.ExpiresAt
	return expireTime.Time.Sub(time.Now()), nil
}
