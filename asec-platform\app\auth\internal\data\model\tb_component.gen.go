// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameTbComponent = "tb_component"

// TbComponent mapped from table <tb_component>
type TbComponent struct {
	ID         string `gorm:"column:id;primaryKey" json:"id"`
	Name       string `gorm:"column:name;not null" json:"name"`
	ProviderID string `gorm:"column:provider_id;not null" json:"provider_id"`
	CorpID     string `gorm:"column:corp_id" json:"corp_id"`
}

// TableName TbComponent's table name
func (*TbComponent) TableName() string {
	return TableNameTbComponent
}
