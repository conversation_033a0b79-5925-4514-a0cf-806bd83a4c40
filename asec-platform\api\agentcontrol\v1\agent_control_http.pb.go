// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             v5.28.0
// source: agentcontrol/v1/agent_control.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAgentControlGetModuleSwitch = "/api.asdsec.agentcontrol.AgentControl/GetModuleSwitch"

type AgentControlHTTPServer interface {
	GetModuleSwitch(context.Context, *ModuleSwitchReq) (*ModuleSwitchResp, error)
}

func RegisterAgentControlHTTPServer(s *http.Server, srv AgentControlHTTPServer) {
	r := s.Route("/")
	r.POST("appliance/v1/module_switch", _AgentControl_GetModuleSwitch0_HTTP_Handler(srv))
}

func _AgentControl_GetModuleSwitch0_HTTP_Handler(srv AgentControlHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleSwitchReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentControlGetModuleSwitch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetModuleSwitch(ctx, req.(*ModuleSwitchReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleSwitchResp)
		return ctx.Result(200, reply)
	}
}

type AgentControlHTTPClient interface {
	GetModuleSwitch(ctx context.Context, req *ModuleSwitchReq, opts ...http.CallOption) (rsp *ModuleSwitchResp, err error)
}

type AgentControlHTTPClientImpl struct {
	cc *http.Client
}

func NewAgentControlHTTPClient(client *http.Client) AgentControlHTTPClient {
	return &AgentControlHTTPClientImpl{client}
}

func (c *AgentControlHTTPClientImpl) GetModuleSwitch(ctx context.Context, in *ModuleSwitchReq, opts ...http.CallOption) (*ModuleSwitchResp, error) {
	var out ModuleSwitchResp
	pattern := "appliance/v1/module_switch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentControlGetModuleSwitch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
