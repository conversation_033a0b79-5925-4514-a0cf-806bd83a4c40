/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import a from"./header.e90a16ed.js";import e from"./menu.f1058f25.js";import{C as s,h as t,a as i,b as o,j as l,d as r,k as n}from"./index.a794166c.js";import"./logo.b56ac4ae.js";const u={class:"layout-page"},c={class:"layout-wrap"},d={id:"layoutMain",class:"layout-main"},m=Object.assign({name:"Client"},{setup:m=>(s.initIpcClient(),(s,m)=>{const p=t("router-view");return i(),o("div",u,[l(a),r("div",c,[l(e),r("div",d,[(i(),n(p,{key:s.$route.fullPath,style:{height:"100%",display:"flex","flex-direction":"column"}}))])])])})});export{m as default};
