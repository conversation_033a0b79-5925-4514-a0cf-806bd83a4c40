// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: specialconfig/v1/special_config.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSpecialConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Key  string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetSpecialConfigReq) Reset() {
	*x = GetSpecialConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_specialconfig_v1_special_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpecialConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpecialConfigReq) ProtoMessage() {}

func (x *GetSpecialConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_specialconfig_v1_special_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpecialConfigReq.ProtoReflect.Descriptor instead.
func (*GetSpecialConfigReq) Descriptor() ([]byte, []int) {
	return file_specialconfig_v1_special_config_proto_rawDescGZIP(), []int{0}
}

func (x *GetSpecialConfigReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetSpecialConfigReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_specialconfig_v1_special_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_specialconfig_v1_special_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_specialconfig_v1_special_config_proto_rawDescGZIP(), []int{1}
}

func (x *Reply) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_specialconfig_v1_special_config_proto protoreflect.FileDescriptor

var file_specialconfig_v1_special_config_proto_rawDesc = []byte{
	0x0a, 0x25, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x61, 0x6c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x3b, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x1d, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x65, 0x0a, 0x0d, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x54, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x32, 0x5a,
	0x30, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63,
	0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_specialconfig_v1_special_config_proto_rawDescOnce sync.Once
	file_specialconfig_v1_special_config_proto_rawDescData = file_specialconfig_v1_special_config_proto_rawDesc
)

func file_specialconfig_v1_special_config_proto_rawDescGZIP() []byte {
	file_specialconfig_v1_special_config_proto_rawDescOnce.Do(func() {
		file_specialconfig_v1_special_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_specialconfig_v1_special_config_proto_rawDescData)
	})
	return file_specialconfig_v1_special_config_proto_rawDescData
}

var file_specialconfig_v1_special_config_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_specialconfig_v1_special_config_proto_goTypes = []interface{}{
	(*GetSpecialConfigReq)(nil), // 0: api.specialconfig.GetSpecialConfigReq
	(*Reply)(nil),               // 1: api.specialconfig.reply
}
var file_specialconfig_v1_special_config_proto_depIdxs = []int32{
	0, // 0: api.specialconfig.SpecialConfig.GetSpecialConfig:input_type -> api.specialconfig.GetSpecialConfigReq
	1, // 1: api.specialconfig.SpecialConfig.GetSpecialConfig:output_type -> api.specialconfig.reply
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specialconfig_v1_special_config_proto_init() }
func file_specialconfig_v1_special_config_proto_init() {
	if File_specialconfig_v1_special_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_specialconfig_v1_special_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpecialConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_specialconfig_v1_special_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_specialconfig_v1_special_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specialconfig_v1_special_config_proto_goTypes,
		DependencyIndexes: file_specialconfig_v1_special_config_proto_depIdxs,
		MessageInfos:      file_specialconfig_v1_special_config_proto_msgTypes,
	}.Build()
	File_specialconfig_v1_special_config_proto = out.File
	file_specialconfig_v1_special_config_proto_rawDesc = nil
	file_specialconfig_v1_special_config_proto_goTypes = nil
	file_specialconfig_v1_special_config_proto_depIdxs = nil
}
