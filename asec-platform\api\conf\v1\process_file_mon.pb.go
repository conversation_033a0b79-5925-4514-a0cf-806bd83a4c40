// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/process_file_mon.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessFileMonEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 软件名称
	SoftwareName string `protobuf:"bytes,1,opt,name=software_name,json=softwareName,proto3" json:"software_name,omitempty"`
	// 指定的进程镜像名称
	ProcessImageNames []string `protobuf:"bytes,2,rep,name=process_image_names,json=processImageNames,proto3" json:"process_image_names,omitempty"`
	// 指定的文件后缀名
	FileExtensions []string `protobuf:"bytes,3,rep,name=file_extensions,json=fileExtensions,proto3" json:"file_extensions,omitempty"`
}

func (x *ProcessFileMonEntry) Reset() {
	*x = ProcessFileMonEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_process_file_mon_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessFileMonEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessFileMonEntry) ProtoMessage() {}

func (x *ProcessFileMonEntry) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_process_file_mon_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessFileMonEntry.ProtoReflect.Descriptor instead.
func (*ProcessFileMonEntry) Descriptor() ([]byte, []int) {
	return file_conf_v1_process_file_mon_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessFileMonEntry) GetSoftwareName() string {
	if x != nil {
		return x.SoftwareName
	}
	return ""
}

func (x *ProcessFileMonEntry) GetProcessImageNames() []string {
	if x != nil {
		return x.ProcessImageNames
	}
	return nil
}

func (x *ProcessFileMonEntry) GetFileExtensions() []string {
	if x != nil {
		return x.FileExtensions
	}
	return nil
}

type ProcessFileMonConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ListConfig []*ProcessFileMonEntry `protobuf:"bytes,1,rep,name=listConfig,proto3" json:"listConfig,omitempty"`
}

func (x *ProcessFileMonConfig) Reset() {
	*x = ProcessFileMonConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_process_file_mon_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessFileMonConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessFileMonConfig) ProtoMessage() {}

func (x *ProcessFileMonConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_process_file_mon_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessFileMonConfig.ProtoReflect.Descriptor instead.
func (*ProcessFileMonConfig) Descriptor() ([]byte, []int) {
	return file_conf_v1_process_file_mon_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessFileMonConfig) GetListConfig() []*ProcessFileMonEntry {
	if x != nil {
		return x.ListConfig
	}
	return nil
}

var File_conf_v1_process_file_mon_proto protoreflect.FileDescriptor

var file_conf_v1_process_file_mon_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0x93, 0x01, 0x0a, 0x13, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x66, 0x74, 0x77,
	0x61, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x55, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x4d,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0a, 0x6c, 0x69, 0x73, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46,
	0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6c, 0x69, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x29, 0x5a, 0x27, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_process_file_mon_proto_rawDescOnce sync.Once
	file_conf_v1_process_file_mon_proto_rawDescData = file_conf_v1_process_file_mon_proto_rawDesc
)

func file_conf_v1_process_file_mon_proto_rawDescGZIP() []byte {
	file_conf_v1_process_file_mon_proto_rawDescOnce.Do(func() {
		file_conf_v1_process_file_mon_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_process_file_mon_proto_rawDescData)
	})
	return file_conf_v1_process_file_mon_proto_rawDescData
}

var file_conf_v1_process_file_mon_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_conf_v1_process_file_mon_proto_goTypes = []interface{}{
	(*ProcessFileMonEntry)(nil),  // 0: api.conf.ProcessFileMonEntry
	(*ProcessFileMonConfig)(nil), // 1: api.conf.ProcessFileMonConfig
}
var file_conf_v1_process_file_mon_proto_depIdxs = []int32{
	0, // 0: api.conf.ProcessFileMonConfig.listConfig:type_name -> api.conf.ProcessFileMonEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_conf_v1_process_file_mon_proto_init() }
func file_conf_v1_process_file_mon_proto_init() {
	if File_conf_v1_process_file_mon_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_process_file_mon_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessFileMonEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_process_file_mon_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessFileMonConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_process_file_mon_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_process_file_mon_proto_goTypes,
		DependencyIndexes: file_conf_v1_process_file_mon_proto_depIdxs,
		MessageInfos:      file_conf_v1_process_file_mon_proto_msgTypes,
	}.Build()
	File_conf_v1_process_file_mon_proto = out.File
	file_conf_v1_process_file_mon_proto_rawDesc = nil
	file_conf_v1_process_file_mon_proto_goTypes = nil
	file_conf_v1_process_file_mon_proto_depIdxs = nil
}
