package network

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"net"
)

func GetMac() (macAddrs []string) {
	netInterfaces, err := net.Interfaces()
	if err != nil {
		global.Logger.Sugar().Errorf("fail to get net interfaces: %v\n", err)
		return macAddrs
	}

	for _, netInterface := range netInterfaces {
		macAddr := netInterface.HardwareAddr.String()
		if len(macAddr) == 0 {
			continue
		}
		macAddrs = append(macAddrs, macAddr)
	}
	return macAddrs
}

func GetIpsAndMac() (ips []string, macAddrs []string) {
	netInterfaces, err := net.Interfaces()
	if err != nil {
		global.Logger.Sugar().Errorf("fail to get net interfaces: %v\n", err)
		return ips, macAddrs
	}

	for _, inter := range netInterfaces {
		if (inter.Flags & net.FlagUp) != 0 {
			addressList, err := inter.Addrs()
			if err != nil {
				global.Logger.Sugar().Errorf("fail to get net interfaces ipAddress: %v\n", err)
				continue
			}
			for _, it := range addressList {
				ipNet, isVailIpNet := it.(*net.IPNet)
				// 检查ip地址判断是否回环地址
				if isVailIpNet && !ipNet.IP.IsLoopback() {
					macAddr := inter.HardwareAddr.String()
					if ipNet.IP.To4() != nil && len(macAddr) != 0 {
						ips = append(ips, ipNet.IP.String())
						macAddrs = append(macAddrs, macAddr)
					}
				}
			}
		}
	}
	return ips, macAddrs
}
