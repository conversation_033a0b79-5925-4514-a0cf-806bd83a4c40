package repository

import (
	"asdsec.com/asec/platform/app/console/app/access/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strings"
)

type accessLogRepository struct {
}

func (a accessLogRepository) AccessTopN(ctx context.Context, top int, days int) (topN []dto.AccessTopN, err error) {
	sql := `select app_id,
       app_name,
       sum(dayil_count)                                           as total,
       concat('[', arrayStringConcat(groupArray(DATA), ','), ']') as "data"
from (
         select count()                                                                        as dayil_count,
                app_id,
                app_name,
                toDate(occur_time)                                                             as _date,
                concat('{"date":"', toString(_date), '","count":', toString(dayil_count), '}') AS DATA
         from asec.tb_file_events
         where activity = 'access'
           AND app_id > 0
           AND occur_time >= today() - INTERVAL %v DAY
         group by _date, app_id, app_name

         UNION ALL

         select count()                                                                        as dayil_count,
                toInt64(app_id),
                app_name,
                toDate(access_time)                                                            as _date,
                concat('{"date":"', toString(_date), '","count":', toString(dayil_count), '}') AS DATA
         from asec.tb_seven_access_log
         where app_id != ''
           AND access_time >= today() - INTERVAL %v DAY
         group by _date, app_id, app_name) combined_data
group by app_id, app_name
order by total desc;`

	db, err := global.GetCkClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return
	}
	// 无法关联查询，过滤掉已经不存在db中的app记录
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return
	}
	err = db.Raw(fmt.Sprintf(sql, days, days)).Scan(&topN).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if len(topN) <= 0 {
		return topN, err
	}

	var appIds []uint64
	err = pgDb.Model(&model.Application{}).Select("id").Find(&appIds).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	var inDbRecord []dto.AccessTopN
	for _, t := range topN {
		for _, id := range appIds {
			if t.AppID == id {
				inDbRecord = append(inDbRecord, t)
			}
		}
	}
	if len(inDbRecord) <= top {
		return inDbRecord, nil
	}
	// 因为没法保证过滤后还有足够的top数,所以只能内存limit一下
	return inDbRecord[:top], nil
}

var (
	totalCountSql = `select sum(c)
from (SELECT count(*) as c
      FROM asec.tb_file_events
      WHERE occur_time >= toDate(now()) - INTERVAL %d DAY
        AND app_id > 0

      union all

      SELECT count(*) as c
      FROM asec.tb_seven_access_log
      WHERE access_time >= toDate(now()) - INTERVAL %d DAY
        AND app_id != '');`

	todayCountSql = `select sum(c)
from (
         SELECT count(*) as c
         FROM asec.tb_file_events
         WHERE occur_time >= toDate(now())
           AND app_id > 0
         union all
         SELECT count(*) as c
         FROM asec.tb_seven_access_log
         WHERE access_time >= toDate(now())
           AND app_id != '');`
)

func (a accessLogRepository) AccessCount(ctx context.Context, days int) (count dto.AccessCount, err error) {

	db, err := global.GetCkClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return
	}
	var totalCount int64
	var todayCount int64

	err = db.Raw(fmt.Sprintf(totalCountSql, days, days)).Scan(&totalCount).Error
	err = db.Raw(todayCountSql).Scan(&todayCount).Error
	count.TodayCount = todayCount
	count.TotalCount = totalCount
	return
}

func (a accessLogRepository) ListAccessLogs(ctx context.Context, req dto.ListAccessLogReq) (model.Pagination, error) {
	res := req.Pagination
	db, err := global.GetCkClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return res, err
	}
	var logs []model.AccessLog
	db = db.Table("tb_file_events")
	db = db.
		Select("src_ip,src_port,dst_ip,dst_port,protocol,dst_url as destination_url," +
			"access_status as status,deny_reason,user_id as access_user_id,user_name as access_username,app_id,app_name," +
			"strategy_id,strategy_name,occur_time as access_time,agent_id as client_id,agent_name as client_name").
		Where("activity='access'")
	if req.StartTime != "" {
		db = db.Where("occur_time >= ? ", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("occur_time <= ? ", req.EndTime)
	}
	if req.Search != "" {
		search := "%" + strings.ToLower(req.Search) + "%"
		db = db.Where(`
        LOWER(user_name) ILIKE ? OR 
        LOWER(app_name) ILIKE ? OR 
        LOWER(CAST(dst_ip AS TEXT)) ILIKE ? OR 
        LOWER(strategy_name) ILIKE ? OR 
        LOWER(agent_name) ILIKE ? OR 
        LOWER(CAST(dst_port AS TEXT)) ILIKE ?`, search, search, search, search, search, search)
	}
	if req.AppId != "" {
		db = db.Where("app_id = ?", req.AppId)
	}
	res.Sort = "occur_time DESC"
	//过滤掉未关联上应用的访问日志
	db = db.Where("app_id > 0")
	return model.Paginate(&logs, &res, db)
}

type AccessLogRepository interface {
	ListAccessLogs(ctx context.Context, req dto.ListAccessLogReq) (model.Pagination, error)
	AccessCount(ctx context.Context, days int) (dto.AccessCount, error)
	AccessTopN(ctx context.Context, top int, days int) ([]dto.AccessTopN, error)
}

func NewAccessLogRepository() AccessLogRepository {
	return accessLogRepository{}
}
