// 从pg导入的结构不支持数组，修改GroupIds和UserIds对应字段类型
package model

import (
	"time"

	"github.com/jackc/pgtype"

	"github.com/lib/pq"
)

const TableNameTbAuthPolicy = "tb_auth_policy"

// TbAuthPolicy mapped from table <tb_auth_policy>
type TbAuthPolicy struct {
	ID              string         `gorm:"column:id;primaryKey" json:"id"`                // 策略id
	Name            string         `gorm:"column:name;not null" json:"name"`              // 策略名称
	Description     string         `gorm:"column:description" json:"description"`         // 策略描述
	GroupIds        pq.StringArray `gorm:"column:group_ids;type:text[]" json:"group_ids"` // 分组列表
	UserIds         pq.StringArray `gorm:"column:user_ids;type:text[]" json:"user_ids"`   // 用户列表
	CorpID          string         `gorm:"column:corp_id" json:"corp_id"`                 // 租户id
	RootGroupId     string         `gorm:"column:root_group_id" json:"root_group_id"`     // 根目录id
	IsDefault       bool           `gorm:"column:is_default" json:"is_default"`           // 是否为默认策略
	EnableAllUser   bool           `gorm:"column:enable_all_user" json:"enable_all_user"` // 是否为所有用户生效
	Enable          bool           `gorm:"column:enable" json:"enable"`                   // 策略启用禁用状态
	CreatedAt       time.Time      `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt       time.Time      `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	AuthEnhancement pgtype.JSONB   `gorm:"column:auth_enhancement;type:jsonb" json:"auth_enhancement"`
	TimeIds         pq.StringArray `gorm:"column:time_ids;type:text[]" json:"time_ids"`          // 增强认证时间id 单纯的为了 做时间模板的引用
	ClientLimits    pgtype.JSONB   `gorm:"column:client_limits;type:jsonb" json:"client_limits"` // 客户端登录限制
}

// TableName TbAuthPolicy's table name
func (*TbAuthPolicy) TableName() string {
	return TableNameTbAuthPolicy
}
