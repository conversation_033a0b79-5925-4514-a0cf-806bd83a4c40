package repository

import (
	"asdsec.com/asec/platform/app/console/app/auth/role/constants"
	"asdsec.com/asec/platform/app/console/app/auth/role/model"
	global "asdsec.com/asec/platform/app/console/global"
	globalModel "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"strconv"
	"time"
)

type AdminRoleRepository interface {
	CreateAdminRole(ctx context.Context, req model.CreateAdminRoleReq) error
	DeleteAdminRole(ctx context.Context, roleId string, corpId string) error
	GetAdminRoleList(ctx context.Context, req model.GetAdminRoleListReq) (globalModel.Pagination, error)
}

// NewAppRepository 创建接口实现接口实现
func NewAppRepository() AdminRoleRepository {
	return &adminRoleRepository{}
}

type adminRoleRepository struct {
}

func (a adminRoleRepository) CreateAdminRole(ctx context.Context, req model.CreateAdminRoleReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	id, err := snowflake.Sf.GetId()
	role := model.Role{
		Id:       strconv.FormatUint(id, 10),
		RoleName: req.RoleName,
		Desc:     req.Desc,
		Status:   req.Status,
		CorpId:   req.CorpId,
		CreateAt: time.Now(),
		UpdateAt: time.Now(),
		RoleType: constants.RoleTypeCustom,
	}
	//todo 角色权限对应策略 policy
	err = db.Create(&role).Error
	return err
}

func (a adminRoleRepository) DeleteAdminRole(ctx context.Context, roleId string, corpId string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Delete(model.Role{Id: roleId, CorpId: corpId}).Error
	return err
}

func (a adminRoleRepository) GetAdminRoleList(ctx context.Context, req model.GetAdminRoleListReq) (globalModel.Pagination, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return globalModel.Pagination{}, err
	}
	var res = req.Pagination
	if req.Search != "" {
		res.SearchColumns = []string{"role_name"}
	}
	var result []model.Role
	db = db.Model(model.Role{}).Where("corp_id = ?", req.CorpId)
	return globalModel.Paginate(&result, &res, db)
}
