package service

import (
	"context"

	"asdsec.com/asec/platform/pkg/biz"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"

	pb "asdsec.com/asec/platform/api/application/v1"
)

type AppService struct {
	pb.UnimplementedAppServer
	uc *biz.AppUsecase
}

func NewAppService(uc *biz.AppUsecase) *AppService {
	return &AppService{uc: uc}
}

// SeGetApp 根据网关Id 获取app信息接口
func (s *AppService) SeGetApp(ctx context.Context, req *pb.SeGetAppReq) (*pb.GetAppResp, error) {
	resp := pb.GetAppResp{}
	appResp, err := s.uc.GetAppBySe(ctx, req.GetApplianceId())
	if err != nil {
		log.Error("GetAppBySe err:", err)
		return &resp, err
	}
	if appResp == nil {
		return &resp, nil
	}
	appInfoList := make([]*pb.AppInfo, 0, 1)
	err = copier.Copy(&appInfoList, appResp)
	if err != nil {
		log.Error("Copy appInfoList err:", err)
		return &resp, err
	}
	resp.Apps = appInfoList
	return &resp, nil
}
func (s *AppService) WebAppInfo(ctx context.Context, req *pb.WebAppInfoReq) (*pb.WebAppInfoResp, error) {
	resp, err := s.uc.WebAppInfo(ctx, req)
	if err != nil {
		log.Error("get WebAppInfo err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) WebAccessInfo(ctx context.Context, req *pb.WebAccessInfoReq) (*pb.WebAccessInfoResp, error) {
	resp, err := s.uc.WebAccessInfo(ctx, req)
	if err != nil {
		log.Error("get WebAccessInfo err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) SeGetStrategy(ctx context.Context, req *pb.SeGetAppReq) (*pb.SeGetStrategyResp, error) {
	resp, err := s.uc.SeGetStrategy(ctx, req.GetApplianceId())
	if err != nil {
		log.Error("SeGetStrategy err:", err)
		return nil, err
	}
	return resp, nil
}
func (s *AppService) WebGatewayRsApp(ctx context.Context, req *pb.WebGatewayRsAppReq) (*pb.WebGatewayRsAppResp, error) {
	resp, err := s.uc.WebGatewayRsApp(ctx, req)
	if err != nil {
		log.Error("get WebGatewayRsApp err:", err)
		return nil, err
	}
	return resp, nil
}
func (s *AppService) WebGatewayRsCrt(ctx context.Context, req *pb.WebGatewayRsCrtReq) (*pb.WebGatewayRsCrtResp, error) {
	resp, err := s.uc.WebGatewayRsCrt(ctx, req)
	if err != nil {
		log.Error("get WebGatewayRsCrt err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) UciUserInfo(ctx context.Context, req *pb.UciUserInfoReq) (*pb.UciUserInfoResp, error) {
	resp, err := s.uc.UciUserInfo(ctx)
	if err != nil {
		log.Error("SeGetStrategy err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) WebGatewayWatermark(ctx context.Context, req *pb.WebGatewayWatermarkReq) (*pb.WebGatewayWatermarkResp, error) {
	resp, err := s.uc.WebGatewayWatermark(ctx, req)
	if err != nil {
		log.Error("SeGetStrategy err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) WebGatewayHosts(ctx context.Context, req *pb.WebGatewayHostsReq) (*pb.WebGatewayHostsResp, error) {
	resp, err := s.uc.WebGatewayHosts(ctx, req)
	if err != nil {
		log.Error("WebGatewayHosts err:", err)
		return nil, err
	}
	return resp, nil
}

// 新增虚拟IP相关方法
func (s *AppService) WebGatewayVirtualIPPools(ctx context.Context, req *pb.WebGatewayVirtualIPPoolsReq) (*pb.WebGatewayVirtualIPPoolsResp, error) {
	resp, err := s.uc.WebGatewayVirtualIPPools(ctx, req)
	if err != nil {
		log.Error("WebGatewayVirtualIPPools err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) ReportVirtualIPAllocations(ctx context.Context, req *pb.ReportVirtualIPAllocationsReq) (*pb.ReportVirtualIPAllocationsResp, error) {
	resp, err := s.uc.ReportVirtualIPAllocations(ctx, req)
	if err != nil {
		log.Error("ReportVirtualIPAllocations err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) ReportHealthCheckResult(ctx context.Context, req *pb.ReportHealthCheckResultReq) (*pb.ReportHealthCheckResultResp, error) {
	resp, err := s.uc.ReportHealthCheckResult(ctx, req)
	if err != nil {
		log.Error("ReportVirtualIPAllocations err:", err)
		return nil, err
	}
	return resp, nil
}

func (s *AppService) ReportTrafficStats(ctx context.Context, req *pb.TrafficStatsReq) (*pb.TrafficStatsResp, error) {
	resp, err := s.uc.ReportTrafficStats(ctx, req)
	if err != nil {
		log.Error("ReportTrafficStats err:", err)
		return nil, err
	}
	return resp, nil
}

// 网关命令拉取
func (s *AppService) GatewayCommand(ctx context.Context, req *pb.GatewayCommandReq) (*pb.GatewayCommandResp, error) {
	resp, err := s.uc.GatewayCommand(ctx, req)
	if err != nil {
		log.Error("GatewayCommand err:", err)
		return nil, err
	}
	return resp, nil
}

// 网关命令结果报告
func (s *AppService) ReportGatewayCommandResult(ctx context.Context, req *pb.GatewayCommandResultReq) (*pb.GatewayCommandResultResp, error) {
	resp, err := s.uc.ReportGatewayCommandResult(ctx, req)
	if err != nil {
		log.Error("ReportGatewayCommandResult err:", err)
		return nil, err
	}
	return resp, nil
}

// 网关拉取SSO IDP配置
func (s *AppService) WebGatewayRsSSOIDP(ctx context.Context, req *pb.WebGatewayRsSSOIDPReq) (*pb.WebGatewayRsSSOIDPResp, error) {
	resp, err := s.uc.WebGatewayRsSSOIDP(ctx, req)
	if err != nil {
		log.Error("WebGatewayRsSSOIDP err:", err)
		return nil, err
	}
	return resp, nil
}

// 网关获取平台域名
func (s *AppService) WebGatewayPlatformDomain(ctx context.Context, req *pb.WebGatewayPlatformDomainReq) (*pb.WebGatewayPlatformDomainResp, error) {
	resp, err := s.uc.WebGatewayPlatformDomain(ctx, req)
	if err != nil {
		log.Error("WebGatewayPlatformDomain err:", err)
		return nil, err
	}
	return resp, nil
}

// 网关拉取微信验证文件
func (s *AppService) WebGatewayWechatVerify(ctx context.Context, req *pb.WebGatewayWechatVerifyReq) (*pb.WebGatewayWechatVerifyResp, error) {
	resp, err := s.uc.WebGatewayWechatVerify(ctx, req)
	if err != nil {
		log.Error("WebGatewayWechatVerify err:", err)
		return nil, err
	}
	return resp, nil
}
