/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function c(e,o,a,i){var c=o&&o.prototype instanceof d?o:d,s=Object.create(c.prototype);return t(s,"_invoke",function(e,t,o){var a,i,c,d=0,s=o||[],l=!1,f={p:0,n:0,v:n,a:g,f:g.bind(n,4),d:function(e,t){return a=e,i=0,c=n,f.n=t,u}};function g(e,t){for(i=e,c=t,r=0;!l&&d&&!o&&r<s.length;r++){var o,a=s[r],g=f.p,p=a[2];e>3?(o=p===t)&&(c=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=n):a[0]<=g&&((o=e<2&&g<a[1])?(i=0,f.v=t,f.n=a[1]):g<p&&(o=e<3||a[0]>t||t>p)&&(a[4]=e,a[5]=t,f.n=p,i=0))}if(o||e>1)return u;throw l=!0,t}return function(o,s,p){if(d>1)throw TypeError("Generator is already running");for(l&&1===s&&g(s,p),i=s,c=p;(r=i<2?n:c)||!l;){a||(i?i<3?(i>1&&(f.n=-1),g(i,c)):f.n=c:f.v=c);try{if(d=2,a){if(i||(o="next"),r=a[o]){if(!(r=r.call(a,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,i<2&&(i=0)}else 1===i&&(r=a.return)&&r.call(a),i<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=n}else if((r=(l=f.n<0)?c:e.call(t,f))!==u)break}catch(r){a=n,i=1,c=r}finally{d=1}}return{value:r,done:l}}}(e,a,i),!0),s}var u={};function d(){}function s(){}function l(){}r=Object.getPrototypeOf;var f=[][a]?r(r([][a]())):(t(r={},a,(function(){return this})),r),g=l.prototype=d.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,t(e,i,"GeneratorFunction")),e.prototype=Object.create(g),e}return s.prototype=l,t(g,"constructor",l),t(l,"constructor",s),s.displayName="GeneratorFunction",t(l,i,"GeneratorFunction"),t(g),t(g,i,"Generator"),t(g,a,(function(){return this})),t(g,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:c,m:p}})()}function t(e,n,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,r,o){if(n)a?a(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var i=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,a,i){try{var c=e[a](i),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var i=e.apply(t,r);function c(e){n(i,o,a,c,u,"next",e)}function u(e){n(i,o,a,c,u,"throw",e)}c(void 0)}))}}System.register(["./index-legacy.b871e767.js","./secondaryAuth-legacy.03ff7a1a.js"],(function(t,n){"use strict";var o,a,i,c,u,d,s,l,f,g,p,v=document.createElement("style");return v.textContent='@charset "UTF-8";.wechat-class[data-v-0182e4a7]{padding-top:15px;height:350px;overflow:hidden;text-align:center}\n',document.head.appendChild(v),{setters:[function(e){o=e._,a=e.f,i=e.r,c=e.o,u=e.P,d=e.v,s=e.a,l=e.b,f=e.d,g=e.Q},function(e){p=e.u}],execute:function(){var n=Object.assign({name:"Wechat"},{props:{authInfo:{type:Array,default:function(){return[]}},authId:{type:String,default:function(){return""}}},setup:function(t){var n=a(),o=p().handleSecondaryAuthResponse,v=i(0),h=i("https://open.work.weixin.qq.com/wwopen/sso/qrConnect"),w=i(""),y=t,m=function(){logger.log("开始绘制企业微信二维码"),w.value=(new Date).getTime();var e=y.authInfo.wxCorpId,t=y.authInfo.wxAgentId;if(!e||!t)return console.error("企业微信配置缺失:",{appid:e,agentid:t}),void I("企业微信配置缺失");var n={uid:b(),time:w.value,state:y.authId},r=g(),o="".concat(r,"/?").concat(new URLSearchParams(n).toString());logger.log("企业微信认证参数:",{appid:e,agentid:t,callbackUrl:o,time:w.value});var a=document.getElementById("wework_qrcode_login");a&&(a.innerHTML=""),x({id:"wework_qrcode_login",appid:e,agentid:t,redirect_uri:encodeURIComponent(o),state:y.authId,href:"",urlid:"wework"}),void 0!==window.addEventListener?window.addEventListener("message",k,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",k)},b=function(){return"wechat_"+Date.now()},k=function(){var t=r(e().m((function t(n){var r,o,a,i;return e().w((function(e){for(;;)switch(e.n){case 0:if(logger.log("收到企业微信消息:",n),!(n.origin.indexOf("work.weixin.qq.com")>-1)){e.n=9;break}if(logger.log("企业微信消息数据:",n.data),"string"!=typeof n.data||!n.data.includes("code=")){e.n=7;break}if(e.p=1,r=new URL(n.data),o=r.searchParams.get("code"),a=r.searchParams.get("state"),logger.log("解析到的参数:",{code:o,state:a}),!o||!a){e.n=3;break}return logger.log("企业微信认证成功，调用handleAuthSuccess"),e.n=2,_(o,a);case 2:e.n=4;break;case 3:console.error("未能解析到有效的code或state参数"),I("认证参数解析失败");case 4:e.n=6;break;case 5:e.p=5,i=e.v,console.error("解析企业微信URL失败:",i),I("认证数据解析失败");case 6:e.n=8;break;case 7:console.error("解析企业微信URL失败");case 8:e.n=12;break;case 9:if(!n.data||"wechat_auth_success"!==n.data.type){e.n=11;break}return logger.log("收到iframe认证成功消息:",n.data),e.n=10,_(n.data.code,n.data.state);case 10:e.n=12;break;case 11:n.data&&"wechat_auth_error"===n.data.type&&(console.error("收到iframe认证失败消息:",n.data.error),I("认证失败: "+n.data.error));case 12:return e.a(2)}}),t,null,[[1,5]])})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=r(e().m((function t(r,a){var i,c,u;return e().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,logger.log("处理企业微信认证成功:",{code:r,state:a}),i={clientId:"client_portal",grantType:"implicit",redirect_uri:"".concat(g(),"/"),idpId:Array.isArray(a)?a[0]:a,authWeb:{authWebCode:Array.isArray(r)?r[0]:r}},logger.log("调用登录接口，参数:",i),e.n=1,n.LoginIn(i,"qiyewx",y.authId);case 1:if(!(c=e.v)||-1===c.code){e.n=4;break}return e.n=2,o(c);case 2:if(!e.v){e.n=3;break}return logger.log("企业微信登录成功，进入双因子验证"),e.a(2);case 3:logger.log("企业微信登录成功"),e.n=5;break;case 4:console.error("企业微信登录失败"),I("登录失败，请重试"),m();case 5:e.n=7;break;case 6:e.p=6,u=e.v,console.error("企业微信认证处理失败:",u),I("认证处理失败: "+u.message),m();case 7:return e.a(2)}}),t,null,[[0,6]])})));return function(e,n){return t.apply(this,arguments)}}(),x=function(e){logger.log("创建企业微信登录iframe:",e);var t=document.createElement("iframe"),n=(new Date).getTime(),r=h.value+"?appid="+e.appid+"&agentid="+e.agentid+"&redirect_uri="+e.redirect_uri+"&state="+e.state+"&login_type=jssdk&t="+n;r+=e.style?"&style="+e.style:"",r+=e.href?"&href="+e.href:"",t.src=r,t.frameBorder="0",t.allowTransparency="true",t.scrolling="no",t.width="300px",t.height="400px";var o=document.getElementById(e.id);o&&(o.innerHTML="",o.appendChild(t),t.onload=function(){t.contentWindow&&t.contentWindow.postMessage&&t.contentWindow.postMessage("ask_usePostMessage","*")}),logger.log("企业微信iframe创建完成:",r)},I=function(e){var t=document.getElementById("wework_qrcode_login");t&&(t.innerHTML='\n      <div style="text-align: center; padding: 20px; color: #f56c6c;">\n        <div style="margin-bottom: 10px;">企业微信认证失败</div>\n        <div style="font-size: 12px; color: #909399;">'.concat(e,'</div>\n        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">\n          重试\n        </button>\n      </div>\n    '))};return c((function(){logger.log("企业微信认证组件挂载"),m()})),u((function(){logger.log("企业微信认证组件卸载"),void 0!==window.addEventListener?window.removeEventListener("message",k,!1):void 0!==window.attachEvent&&window.detachEvent("onmessage",k)})),d(y,(function(){logger.log("企业微信认证props变化，重新绘制二维码"),v.value++,m()})),function(e,t){return s(),l("div",{key:v.value},t[0]||(t[0]=[f("div",{id:"wework_qrcode_login",class:"wechat-class"},null,-1)]))}}});t("default",o(n,[["__scopeId","data-v-0182e4a7"]]))}}}))}();
