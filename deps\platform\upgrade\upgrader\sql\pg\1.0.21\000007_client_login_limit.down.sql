-- 客户端登录限制功能数据库迁移脚本 - DOWN (回滚)

-- 警告: 此回滚操作将删除所有客户端限制相关的数据和配置
-- 请确保在执行前备份相关数据

DO $$
BEGIN
    RAISE NOTICE 'Starting client login limit migration rollback (DOWN)...';
    RAISE NOTICE 'WARNING: This will remove all client limit configurations and session tracking data!';
END $$;

-- 1. 删除触发器
DROP TRIGGER IF EXISTS trigger_update_session_tracker_updated_at ON tb_user_session_tracker;

-- 2. 删除触发器函数
DROP FUNCTION IF EXISTS update_session_tracker_updated_at();

-- 3. 删除清理函数
DROP FUNCTION IF EXISTS cleanup_expired_session_trackers();

-- 4. 删除会话跟踪表（这将删除所有会话跟踪数据）
DROP TABLE IF EXISTS tb_user_session_tracker CASCADE;

-- 5. 删除认证策略表中的客户端限制列
-- 注意: 这将删除所有已配置的客户端限制策略
ALTER TABLE tb_auth_policy 
DROP COLUMN IF EXISTS client_limits;

-- 6. 清理相关的索引（如果表已删除，索引会自动删除，但保留命令以确保完全清理）
-- 由于表已删除，这些索引也会被自动删除，此处仅作记录
-- DROP INDEX IF EXISTS idx_session_tracker_corp_user_client;
-- DROP INDEX IF EXISTS idx_session_tracker_jwt_id;
-- DROP INDEX IF EXISTS idx_session_tracker_expires;
-- DROP INDEX IF EXISTS idx_session_tracker_last_active;
-- DROP INDEX IF EXISTS idx_session_tracker_login_time;

-- 7. 验证回滚完成
DO $$
DECLARE
    table_exists BOOLEAN;
    column_exists BOOLEAN;
BEGIN
    -- 检查表是否已删除
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'tb_user_session_tracker'
    ) INTO table_exists;
    
    -- 检查列是否已删除
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'tb_auth_policy' 
        AND column_name = 'client_limits'
    ) INTO column_exists;
    
    IF table_exists THEN
        RAISE WARNING 'tb_user_session_tracker table still exists!';
    ELSE
        RAISE NOTICE 'tb_user_session_tracker table successfully removed';
    END IF;
    
    IF column_exists THEN
        RAISE WARNING 'tb_auth_policy.client_limits column still exists!';
    ELSE
        RAISE NOTICE 'tb_auth_policy.client_limits column successfully removed';
    END IF;
    
    IF NOT table_exists AND NOT column_exists THEN
        RAISE NOTICE 'Client login limit migration rollback (DOWN) completed successfully!';
        RAISE NOTICE 'All client limit features have been removed from the database';
    ELSE
        RAISE WARNING 'Rollback may not have completed successfully. Please check manually.';
    END IF;
END $$;
