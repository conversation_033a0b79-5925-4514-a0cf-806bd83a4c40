syntax = "proto3";

package api.auth.v1.admin;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "asdsec.com/asec/platform/api/auth/v1/admin;admin";
option java_multiple_files = true;
option java_package = ".api.auth.v1.admin";

service Admin {
  rpc CreateCorp (CreateCorpRequest) returns (CreateCorpReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/corp",
      body: "*",
    };
  };
  rpc UpdateCorp (UpdateCorpRequest) returns (UpdateCorpReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/corp",
      body: "*",
    };
  };

  rpc UpdateLockStatus (UpdateLockStatusRequest) returns (UpdateLockStatusReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/lock_status",
      body: "*",
    };
  };

  rpc UpdateIdleTime (UpdateIdleTimeRequest) returns (UpdateIdleTimeReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/idle_time",
      body: "*",
    };
  };

  rpc TotpUnbind (TotpUnbindRequest) returns (TotpUnbindTimeReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/totp_unbind",
      body: "*",
    };
  };

  rpc DeleteCorp (DeleteCorpRequest) returns (DeleteCorpReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/corp"
    };
  };
  rpc GetCorp (GetCorpRequest) returns (GetCorpReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/corp"
    };
  };
  rpc ListCorp (ListCorpRequest) returns (ListCorpReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/corp/list"
    };
  };

  rpc CreateUserSource (CreateUserSourceRequest) returns (CreateUserSourceReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/user_source",
      body: "*",
    };
  };
  rpc UpdateUserSource (UpdateUserSourceRequest) returns (UpdateUserSourceReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/user_source",
      body: "*",
    };
  };
  rpc DeleteUserSource (DeleteUserSourceRequest) returns (DeleteUserSourceReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/user_source"
    };
  };
  rpc GetUserSource (GetUserSourceRequest) returns (GetUserSourceReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/user_source"
    };
  };
  rpc ListUserSource (ListUserSourceRequest) returns (ListUserSourceReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/user_source/list"
    };
  };
  rpc ListUserSourceType (ListUserSourceTypeRequest) returns (ListUserSourceTypeReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/user_source/type/list"
    };
  };

  rpc ListIDPType(ListIDPTypeRequest) returns (ListIDPTypeReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/idp_type/list"
    };
  };

  rpc OAuth2Test(OAuth2TestRequest) returns (OAuth2TestReply) {
    option (google.api.http) = {
      post: "/auth/admin/v1/idp/oauth2_test",
      body: "*"
    };
  };

  rpc TestResult(TestResultRequest) returns (TestResultReply) {
    option (google.api.http) = {
      post: "/auth/admin/v1/idp/test_result/{test_id}",
      body: "*"
    };
  };

  rpc CreateIDP (CreateIDPRequest) returns (CreateIDPReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/idp",
      body: "*",
    };
  };

  rpc UpdateIDP (UpdateIDPRequest) returns (UpdateIDPReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/idp",
      body: "*",
    };
  };

  rpc DeleteIDP(DeleteIDPRequest) returns(DeleteIDPReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/idp"
    };
  };

  rpc ListIDP (ListIDPRequest) returns(ListIDPReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/idp/list"
    };
  };

  rpc GetIDPDetail(GetIDPDetailRequest) returns(GetIDPDetailReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/idp/detail"
    };
  }


  rpc ListRootGroup(ListRootGroupRequest) returns (ListRootGroupReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/root_group/list"
    };
  };

  rpc GetRootGroupDetail(GetRootGroupDetailRequest) returns (GetRootGroupDetailReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/root_group/detail"
    };
  };

  rpc CreateRootGroup(CreateRootGroupRequest) returns(CreateRootGroupReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/root_group",
      body: "*"
    };
  };

  rpc GetRootGroupIdpList(GetRootGroupIdpListRequest) returns(GetRootGroupIdpListReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/root_group/idp_list"
    };
  };

  rpc GetFieldMap(GetFieldMapRequest) returns(GetFieldMapReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/field_map"
    };
  };

  rpc GetFieldOptions(GetFieldOptionsRequest)returns(GetFieldOptionsReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/field_option"
    };
  };

  rpc UpdateRootGroup(UpdateRootGroupRequest) returns (UpdateRootGroupReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/root_group",
      body: "*"
    };
  }

  rpc UpdateRootGroupCustom(UpdateRootGroupRequest) returns (UpdateRootGroupReply){
    option (google.api.http) = {
      put: "/auth/openapi/v1/group",
      body: "*"
    };
  }

  rpc SwitchAutoSync (SwitchAutoSyncRequest) returns (SwitchAutoSyncReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/root_group/auto_sync",
      body: "*"
    };
  };

  rpc SyncTrigger(SyncTriggerRequest) returns (SyncTriggerReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/root_group/sync_trigger",
      body: "*"
    };
  };

  rpc ListSyncLog(ListSyncLogRequest) returns(ListSyncLogReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/root_group/sync_log"
    };
  };

  rpc DeleteRootGroup(DeleteRootGroupRequest) returns (DeleteRootGroupReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/root_group"
    };
  }

  rpc DeleteRootGroupCustom(DeleteRootGroupRequest) returns (DeleteRootGroupReply){
    option (google.api.http) = {
      delete: "/auth/openapi/v1/group"
    };
  }

  rpc ListUserGroup (ListUserGroupRequest) returns (ListUserGroupReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/user_group/list"
    };
  };

  rpc CreateUserGroup (CreateUserGroupRequest) returns (CreateUserGroupReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/user_group",
      body: "*",
    };
  };

  rpc CreateUserGroupCustom (CreateUserGroupRequestCustom) returns (CreateUserGroupReply){
    option (google.api.http) = {
      post: "/auth/openapi/v1/group",
      body: "*",
    };
  };

  rpc UpdateUserGroup (UpdateUserGroupRequest) returns (UpdateUserGroupReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/user_group",
      body: "*",
    };
  };

  rpc CreateUser (CreateUserRequest) returns (CreateUserReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/user",
      body: "*",
    };
  }

  rpc CreateUserCustom (CreateUserRequest) returns (CreateUserReply){
    option (google.api.http) = {
      post: "/auth/openapi/v1/user",
      body: "*",
    };
  }
  rpc DeleteUserCustom (CustomDeleteUserRequest) returns (DeleteUserReply){
    option (google.api.http) = {
      delete: "/auth/openapi/v1/user"
    };
  }

  rpc UpdateUserCustom (CustomUpdateUserRequest) returns (UpdateUserReply){
    option (google.api.http) = {
      put: "/auth/openapi/v1/user",
      body: "*",
    };
  }

  rpc ListUser (ListUserRequest) returns (ListUserReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/user/list"
    };
  }

  rpc DeleteUser (DeleteUserRequest) returns (DeleteUserReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/user"
    };
  }

  rpc UpdateUser (UpdateUserRequest) returns (UpdateUserReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/user",
      body: "*",
    };
  }

  rpc CreateRole (CreateRoleRequest) returns (CreateRoleReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/role",
      body: "*"
    };
  }

  rpc ListRole (ListRoleRequest) returns (ListRoleReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/role/list"
    };
  }

  // 闲置账号列表
  rpc IdleAccountList (IdleAccountRequest) returns (IdleAccountReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/idle_account/list"
    };
  }


  rpc UpdateRole (UpdateRoleRequest) returns (UpdateRoleReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/role",
      body: "*"
    };
  }

  rpc DeleteRole (DeleteRoleRequest) returns (DeleteRoleReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/role"
    };
  }

  rpc CreateAuthPolicy (CreateAuthPolicyRequest) returns (CreateAuthPolicyReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/auth_policy",
      body: "*"
    };
  }

  rpc UpdateAuthPolicy (UpdateAuthPolicyRequest) returns (UpdateAuthPolicyReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/auth_policy",
      body: "*"
    };
  }

  rpc ListAuthPolicy (ListAuthPolicyRequest) returns (ListAuthPolicyReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/auth_policy/list"
    };
  }

  rpc DeleteAuthPolicy (DeleteAuthPolicyRequest) returns (DeleteAuthPolicyReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/auth_policy"
    };
  }

  // 账户策略管理接口
  rpc CreateAccountPolicy (CreateAccountPolicyRequest) returns (CreateAccountPolicyReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/account_policy",
      body: "*"
    };
  }

  rpc UpdateAccountPolicy (UpdateAccountPolicyRequest) returns (UpdateAccountPolicyReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/account_policy",
      body: "*"
    };
  }

  rpc DeleteAccountPolicy (DeleteAccountPolicyRequest) returns (DeleteAccountPolicyReply){
    option (google.api.http) = {
      delete: "/auth/admin/v1/account_policy"
    };
  }

  rpc GetAccountPolicy (GetAccountPolicyRequest) returns (GetAccountPolicyReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/account_policy"
    };
  }

  rpc ListAccountPolicies (ListAccountPoliciesRequest) returns (ListAccountPoliciesReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/account_policy/list"
    };
  }

  // 账户锁定管理接口
  rpc UnlockAccount (UnlockAccountRequest) returns (UnlockAccountReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/account_policy/unlock",
      body: "*"
    };
  }

  rpc BatchUnlockAccounts (BatchUnlockAccountsRequest) returns (BatchUnlockAccountsReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/account_policy/batch_unlock",
      body: "*"
    };
  }

  rpc GetAccountLockInfo (GetAccountLockInfoRequest) returns (GetAccountLockInfoReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/account_policy/lock_info"
    };
  }

  rpc ListLockedAccounts (ListLockedAccountsRequest) returns (ListLockedAccountsReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/account_policy/locked_accounts"
    };
  }

  // IP锁定管理接口
  rpc UnlockIP (UnlockIPRequest) returns (UnlockIPReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/ip_policy/unlock",
      body: "*"
    };
  }

  rpc BatchUnlockIPs (BatchUnlockIPsRequest) returns (BatchUnlockIPsReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/ip_policy/batch_unlock",
      body: "*"
    };
  }

  rpc GetIPLockInfo (GetIPLockInfoRequest) returns (GetIPLockInfoReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/ip_policy/lock_info"
    };
  }

  rpc ListLockedIPs (ListLockedIPsRequest) returns (ListLockedIPsReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/ip_policy/locked_ips"
    };
  }

  rpc ValidateWebAuthScript(ValidateWebAuthScriptRequest) returns (ValidateWebAuthScriptReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/idp/validate_script",
      body: "*"
    };
  };

  // 会话管理接口
  rpc ListUserSessions (ListUserSessionsRequest) returns (ListUserSessionsReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/user_sessions"
    };
  };

  rpc KickUserSession (KickUserSessionRequest) returns (KickUserSessionReply){
    option (google.api.http) = {
      post: "/auth/admin/v1/kick_session",
      body: "*"
    };
  };

  rpc GetClientLimits (GetClientLimitsRequest) returns (GetClientLimitsReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/client_limits"
    };
  };

  rpc UpdateClientLimits (UpdateClientLimitsRequest) returns (UpdateClientLimitsReply){
    option (google.api.http) = {
      put: "/auth/admin/v1/client_limits",
      body: "*"
    };
  };

  rpc GetUserCount (GetUserCountRequest) returns (GetUserCountReply){
    option (google.api.http) = {
      get: "/auth/admin/v1/user_count"
    };
  };
}

enum StatusCode {
  SUCCESS = 0;
  FAILED = 1;
}

// 租户
message CreateCorpRequest {
  string name = 1;
}
message CreateCorpReply {
  StatusCode status = 1;
}

message UpdateCorpRequest {
  string id = 1;
  string name = 2;
}

message UpdateIdleTimeRequest{
  string time = 1;
}

message UpdateLockStatusRequest{
  string uid = 1;
  bool status = 2;
}

message TotpUnbindRequest{
  repeated string id = 1;
}

message UpdateLockStatusReply{
  StatusCode status = 1;
}

message TotpUnbindTimeReply{
  StatusCode status = 1;
}


message UpdateIdleTimeReply{
  string time = 1;
}

message UpdateCorpReply {
  StatusCode status = 1;
}

message DeleteCorpRequest {
  string id = 1;
}
message DeleteCorpReply {
  StatusCode status = 1;
}

message GetCorpRequest {
  string id = 1;
}

message Corp {
  string id = 1;
  string name = 2;
}

message GetCorpReply {
  Corp corp = 1;
}

message ListCorpRequest {
  string name = 1;
}
message ListCorpReply {
  repeated Corp corps = 1;
}

// 用户来源
message CreateUserSourceRequest {
  string name = 1;
  string source_type = 2;
}
message CreateUserSourceReply {
  StatusCode status = 1;
}

message UpdateUserSourceRequest {
  string id = 1;
  string name = 2;
}
message UpdateUserSourceReply {
  StatusCode status = 1;
}

message DeleteUserSourceRequest {
  string id = 1;
}
message DeleteUserSourceReply {
  StatusCode status = 1;
}

message UserSource {
  string id = 1;
  string name = 2;
  string source_type = 3;
  bool enable = 4;
  string template_type = 5;
}
message GetUserSourceRequest {
  string id = 1;
}

message GetUserSourceReply {
  UserSource UserSource = 1;
}

message ListUserSourceRequest {
}
message ListUserSourceReply {
  repeated UserSource custom_sources = 1;
  repeated UserSource third_sources = 2;
}

message ListUserSourceTypeRequest{
}

message ListUserSourceTypeReply {
  message UserSourceType{
    string key = 1;
    string val = 2;
  }
  repeated UserSourceType UserSourceTypeList = 1;
}

// IDP
message ListIDPTypeRequest{
}

message OAuth2TestRequest {
  string idp_id = 1;  // 身份提供商ID
}

// OAuth2测试响应
message OAuth2TestReply {
  bool success = 1;
  string message = 2;
  google.protobuf.Struct data = 3;
  string test_id = 4;  // 测试会话ID
}

// 获取测试结果请求
message TestResultRequest {
  string test_id = 1;  // 测试会话ID
  string username = 2;  // 测试用户名
  string password = 3;  // 测试密码
  string type = 4;  // 类型
}

// 测试结果响应
message TestResultReply {
  bool success = 1;
  string message = 2;
  google.protobuf.Struct data = 3;
  string result = 4;  // 原始结果字符串
}

message ListIDPTypeReply{
  message IDPType {
    string type = 1;
    string name = 2;
    string template_type = 3;
    map<string, string> template_attr = 4;
  }
  repeated IDPType main_idp = 1;
  repeated IDPType assist_idp = 2;
}

message CreateIDPRequest {
  message AuthConfig{
    string corp_id = 1;
    string agent_id = 2;
    string secret = 3;
    string app_id = 4;
    string app_secret = 5;
    string sms_type = 6;
    string access_key_id = 7;
    string access_key_secret = 8;
    int32 expiration_time = 9;
    string sign_name = 10;
    string template_code = 11;
    string sdk_app_id = 12;
    string secret_id = 13;
    string secret_key = 14;
    string app_key = 15;
    string channel_no = 16;
    string server_address = 17;
    string administrator_account = 18;
    string administrator_password = 19;
    string search_entry = 20;
    string group = 21;
    string external_id = 22;
    string filtration = 23;
    string global_data = 24;
    string code_data = 25;
    string user_data = 26;
    string logout_open = 27;
    string logout_data = 28;
    string open_type = 29;
    string field_map = 30;
    string auth_data = 31;
    string config_data =32;
    string search_map = 33;
    string token_deviation =34;
    string dd_corp_id = 35;
  }
  string name = 1;
  string template_type = 2;
  string type = 3;
  string description = 4;
  repeated KV field_map = 5;
  AuthConfig auth_config = 6;
  bool enable = 7;
  repeated string bind_root_group_id = 8;
}

message CreateIDPReply {
  StatusCode status = 1;
}

message UpdateIDPRequest{
  message AuthConfig{
    string corp_id = 1;
    string agent_id = 2;
    string secret = 3;
    string app_id = 4;
    string app_secret = 5;
    string sms_type = 6;
    string access_key_id = 7;
    string access_key_secret = 8;
    int32 expiration_time = 9;
    string sign_name = 10;
    string template_code = 11;
    string sdk_app_id = 12;
    string secret_id = 13;
    string secret_key = 14;
    string app_key = 15;
    string channel_no = 16;
    string server_address = 17;
    string administrator_account = 18;
    string administrator_password = 19;
    string search_entry = 20;
    string group = 21;
    string external_id = 22;
    string filtration = 23;
    string global_data = 24;
    string code_data = 25;
    string user_data = 26;
    string logout_open = 27;
    string logout_data = 28;
    string open_type = 29;
    string field_map = 30;
    string auth_data = 31;
    string config_data =32;
    string search_map = 33;
    string token_deviation =34;
    string dd_corp_id = 35;
  }
  string name = 1;
  string template_type = 2;
  string description = 4;
  repeated KV field_map = 5;
  AuthConfig auth_config = 6;
  bool enable = 7;
  string id = 9;
}

message UpdateIDPReply{
  StatusCode status = 1;
}

message GetIDPDetailRequest{
  string id = 1;
}

message GetIDPDetailReply{
  message AuthConfig{
    string corp_id = 1;
    string agent_id = 2;
    string secret = 3;
    string app_id = 4;
    string app_secret = 5;
    string sms_type = 6;
    string access_key_id = 7;
    string access_key_secret = 8;
    int32 expiration_time = 9;
    string sign_name = 10;
    string template_code = 11;
    string app_key = 12;
    string server_address = 13;
    string administrator_account = 14;
    string administrator_password = 15;
    string search_entry = 16;
    string filtration = 17;
    string external_id = 18;
    string group = 19;
    string sdk_app_id = 20;
    string global_data = 21;
    string code_data = 22;
    string user_data = 23;
    string logout_open = 24;
    string logout_data = 25;
    string open_type = 26;
    string config_data = 27;
    string search_map = 28;
    string token_deviation = 29;
    string dd_corp_id = 30;
  }
  message BindGroup{
    string id = 1;
    string name = 2;
  }
  message CasConfig{
    string field_map = 1;
    string auth_data = 2;
    string open_type = 3;
    string search_map = 4;
  }
  string name = 1;
  string template_type = 2;
  string type = 3;
  string description = 4;
  repeated KV field_map = 5;
  AuthConfig auth_config = 6;
  CasConfig cas_config = 9;
  bool enable = 7;
  repeated BindGroup bind_group_list = 8;
}

message CreateUserGroupRequest {
  string name = 1;
  string parent_group_id = 2;
  string source_id = 3;
  string description = 4;
}
message CreateUserGroupRequestCustom {
  string name = 1;
  string parent_group_id = 2;
  string source_id = 3;
  string description = 4;
  string group_id=5;
}


message CreateUserGroupReply{
  StatusCode status = 1;
}


message KV{
  string key = 1;
  string value = 2;
}

message CreateRootGroupRequest{
  message WxConfig{
    string corp_id = 1;
    string agent_id = 2;
    string secret = 3;
  }
  message FeiShuConfig{
    string app_id = 1;
    string app_secret = 2;
  }
  message DingtalkConfig{
    string app_key = 1;
    string app_secret = 2;
    string dd_corp_id = 3;
  }
  message LdapConfig{
    string server_address = 1;
    string administrator_account = 2;
    string administrator_password = 3;
    string search_entry = 4;
    string user_filter = 5;
    string group_filter = 6;
  }
  message InfogoConfig{
    string endpoint = 1;
    string login = 2;
    string pass = 3;
  }
  message OAuth2Config{
    string global_data = 1;
  }
  string name = 1;
  string source_id = 3;
  string description = 4;
  bool auto_sync = 5;
  WxConfig wx_config = 6;
  repeated KV field_map = 7;
  int32 sync_cycle = 8;
  string sync_unit = 9;
  FeiShuConfig feishu_config = 10;
  DingtalkConfig dingtalk_config = 11;
  LdapConfig ldap_config = 12;
  InfogoConfig infogo_config = 13;
  OAuth2Config o_auth2_config = 14;
}

message CreateRootGroupReply{
  StatusCode status = 1;
}

message UpdateRootGroupRequest{
  message WxConfig{
    string corp_id = 1;
    string agent_id = 2;
    string secret = 3;
  }
  message FeishuConfig{
    string app_id = 1;
    string app_secret = 2;
  }
  message DingtalkConfig{
    string app_key = 1;
    string app_secret = 2;
    string dtalk_corp_id = 3;
  }
  message LdapConfig{
    string server_address = 1;
    string administrator_account = 2;
    string administrator_password = 3;
    string search_entry = 4;
    string user_filter = 5;
    string group_filter = 6;
  }
  message InfogoConfig{
    string endpoint = 1;
    string login = 2;
    string pass = 3;
  }
  message OAuth2Config{
    string global_data = 1;
  }
  string group_id = 1;
  string description = 2;
  string name = 3;
  WxConfig wx_config = 4;
  repeated KV field_map = 5;
  bool auto_sync = 6;
  string source_id = 7;
  int32 sync_cycle = 8;
  string sync_unit = 9;
  string parent_group_id = 10;
  FeishuConfig feishu_config = 11;
  DingtalkConfig dd_corp_id = 12;
  LdapConfig ldap_config = 13;
  InfogoConfig infogo_config = 14;
  OAuth2Config o_auth2_config = 15;
}

message UpdateRootGroupReply{
  StatusCode status = 1;
}

message SwitchAutoSyncRequest{
  bool auto_sync = 1;
  string group_id = 2;
}

message SwitchAutoSyncReply{
  StatusCode status = 1;
}

message DeleteRootGroupRequest{
  string group_id = 1;
  string name = 2;
}

message DeleteRootGroupReply{
  StatusCode status = 1;
}

message ListRootGroupRequest{
  uint32 limit = 1;
  uint32 offset = 2;
}

message ListRootGroupReply{
  message RootGroupInfo{
    string name = 1;
    string source_type = 2;
    string source_name = 3;
    string sync_status = 4;
    string sync_time = 5;
    bool auto_sync = 6;
    string id = 7;
    bool is_default = 8;
    string template_type = 9;
  }
  repeated RootGroupInfo group_infos = 1;
  uint32 count = 2;
}

message ListUserGroupRequest{
  uint32 limit = 1;
  uint32 offset = 2;
  string parent_group_id = 3;
}

message ListUserGroupReply{
  message UserGroupTree{
    string id = 1;
    string name = 2;
    string parent_group_id = 3;
    string source_id = 4;
    repeated UserGroupTree children = 5;
    string description = 7;
    bool is_default = 8;
    string source_type = 9;
    string source_name = 10;
    string template_type = 11;
  }
  repeated UserGroupTree user_group_list = 1;
  uint32 count = 2;
}

message CreateUserRequest {
  string name = 1;
  string group_id = 2;
  string phone = 4;
  string email = 5;
  string password = 6;
  bool enable = 7;
  string expire_type = 8;
  int64 expire_end = 9;
  string display_name = 10;
  repeated string role_ids = 11;
  string identifier = 12;
}

message CreateUserReply{
  StatusCode status = 1;
}
message DeleteUserRequest{
  string id = 1;
  string name = 2;
}
message CustomDeleteUserRequest{
  string name = 1;
}

message DeleteUserReply{
  StatusCode status = 1;
}

message ListUserRequest{
  string group_id = 1;
  int32 limit = 2;
  int32 offset = 3;
  string search = 4;
}

message UserEntity {
  string id = 1;
  string name = 2;
  string group_id = 3;
  string source_id = 4;
  string phone = 5;
  string email = 6;
  repeated RoleInfo roles = 7;
  bool enable = 8;
  string expire_type = 9;
  string expire_end = 10;
  string group_name = 11;
  string display_name = 12;
  string source_type = 13;
  string identifier = 14;
  string auth_type =15;
  string active_time = 16;
  string idle_day = 17;
  bool lock_status = 18;
  string security_code = 19;
}

message RoleInfo {
  string id = 1;
  string name = 2;
}

message ListUserReply{
  repeated UserEntity users = 1;
  uint32 count = 2;
}

message CreateRoleRequest {
  string name = 1;
  string description = 2;
  repeated string user_id_list = 3;
}

message CreateRoleReply {
  StatusCode status = 1;
}

message ListRoleRequest{
  uint32 limit = 1;
  uint32 offset = 2;
  string search = 3;
}

message IdleAccountRequest {
  uint32 limit = 1;
  uint32 offset = 2;
  string search = 3;
}

message Role {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated UserInfo users = 4;
}

message UserInfo{
  string id = 1;
  string name = 2;
  string display_name = 3;
  string path = 4;
}

message ListRoleReply{
  repeated Role role = 1;
  uint32 count = 2;
}
message IdleAccountReply{
  repeated UserEntity users = 1;
  uint32 count = 2;
  string time = 3;
}

message ListIDPRequest{
  uint32 limit = 1;
  uint32 offset = 2;
}

message ListIDPReply{
  message BindGroup{
    string id = 1;
    string name = 2;
  }
  message IDP{
    string id = 1;
    string name = 2;
    string type = 3;
    repeated BindGroup bind_group_list = 4;
    string description = 5;
    bool enable = 6;
    string type_name = 7;
    bool is_default = 8;
    string template_type = 9;
  }
  repeated IDP idp_list = 1;
  uint32 count = 2;
}

message CreateAuthPolicyRequest{
  string name = 1;
  repeated string group_ids = 2;
  repeated string user_ids = 3;
  string description = 4;
  repeated string idp_list = 5;
  string root_group_id = 6;
  bool enable_all_user = 7;
  bool enable = 8;
  AuthEnhanceMent auth_enhancement = 9;
  repeated string time_ids = 10;
  ClientLimitsConfig client_limits = 11;          // 客户端限制配置
}

message AuthEnhanceMent {
  repeated Factor factor = 1;
  string idp_id = 2;
}

message Factor {
  string type = 1;
  repeated string ids = 2;
  string operator = 3;
  repeated string region_type = 4;
}

message UpdateAuthPolicyRequest{
  string name = 1;
  repeated string group_ids = 2;
  repeated string user_ids = 3;
  string description = 4;
  repeated string idp_list = 5;
  string root_group_id = 6;
  bool enable_all_user = 7;
  bool enable = 8;
  string policy_id = 9;
  AuthEnhanceMent auth_enhancement = 10;
  repeated string time_ids = 11;
  ClientLimitsConfig client_limits = 12;          // 客户端限制配置
}

message CreateAuthPolicyReply{
  StatusCode status = 1;
}

message UpdateAuthPolicyReply{
  StatusCode status = 1;
}

message ListAuthPolicyRequest{
  string root_group_id = 1;
  uint32 limit = 2;
  uint32 offset = 3;
}

message ListAuthPolicyReply{
  message GroupInfo{
    string id = 1;
    string name = 2;
    string path = 3;
  }
  message UserInfo{
    string id = 1;
    string name = 2;
    string displayName = 3;
    string path = 4;
  }
  message IdpInfo{
    string id = 1;
    string name = 2;
  }
  message IdpInfoMap{
    repeated IdpInfo main_idp_list = 1;
    repeated IdpInfo assist_idp_list = 2;
  }
  message AuthPolicy{
    string id = 1;
    string name = 2;
    repeated GroupInfo group_info_list = 3;
    repeated UserInfo user_info_list = 4;
    string description = 5;
    IdpInfoMap idp_info_map = 6;
    bool enable_all_user = 7;
    bool is_default = 8;
    bool enable = 9;
    AuthEnhanceMent auth_enhancement = 10;
    repeated string time_ids = 11;
    ClientLimitsConfig client_limits = 12;
  }
  repeated AuthPolicy auth_policy_list = 1;
  uint32 count = 2;


}

message UpdateUserGroupRequest{}

message UpdateUserGroupReply{}

message GetRootGroupIdpListRequest{
  string root_group_id = 1;
}

message GetRootGroupIdpListReply{
  message IdpInfo{
    string id = 1;
    string name = 2;
    string type = 3;
  }
  repeated IdpInfo  main_idp_list = 1;
  repeated IdpInfo assist_idp_list = 2;
}

message DeleteAuthPolicyRequest{
  string id = 1;
  string name = 2;
}

message DeleteAuthPolicyReply{
  StatusCode status = 1;
}

message GetUserCountRequest {
}

message GetUserCountReply{
  uint32 total_count = 1;
  uint32 online_count = 2;
  uint32 offline_count = 3;
}
message UpdateRoleRequest{
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string user_id_list = 4;
}
message UpdateRoleReply{
  StatusCode status = 1;
}
message DeleteRoleRequest{
  string id = 1;
  string name = 2;
}
message DeleteRoleReply{
  StatusCode status = 1;
}

message UpdateUserRequest {
  string id = 1;
  string group_id = 2;
  string phone = 3;
  string email = 4;
  string password = 5;
  bool enable = 6;
  string expire_type = 7;
  int64 expire_end = 8;
  string display_name = 9;
  repeated string role_ids = 10;
  string identifier = 11;
}
message CustomUpdateUserRequest {
  string name = 1;
  string group_id = 2;
  string phone = 3;
  string email = 4;
  string password = 5;
  bool enable = 6;
  string expire_type = 7;
  int64 expire_end = 8;
  string display_name = 9;
  repeated string role_ids = 10;
  string identifier = 11;
}
message UpdateUserReply{
  StatusCode status = 1;
}

message SyncTriggerRequest{
  string root_group_id = 1;
}

message SyncTriggerReply{
  StatusCode status = 1;
}

message DeleteIDPRequest{
  string id = 1;
  string name = 2;
}

message DeleteIDPReply{
  StatusCode status = 1;
}

message GetRootGroupDetailRequest{
  string group_id = 1;
}
message GetRootGroupDetailReply{
  message WxConfig{
    string corp_id = 1;
    string agent_id = 2;
    string secret = 3;
    string sync_status = 4;
    string sync_time = 5;
    bool auto_sync = 6;
    uint32 sync_cycle = 7;
    string sync_unit = 8;
    repeated KV field_map = 9;
  }
  message FeiShuConfig{
    string app_id = 1;
    string app_secret = 2;
    string sync_status = 3;
    string sync_time = 4;
    bool auto_sync = 5;
    uint32 sync_cycle = 6;
    string sync_unit = 7;
    repeated KV field_map = 8;
  }
  message DingtalkConfig{
    string app_key = 1;
    string app_secret = 2;
    string sync_status = 3;
    string sync_time = 4;
    bool auto_sync = 5;
    uint32 sync_cycle = 6;
    string sync_unit = 7;
    repeated KV field_map = 8;
    string dd_corp_id = 9;
  }
  message AdConfig{
    string server_address = 1;
    string administrator_account = 2;
    string administrator_password = 3;
    string search_entry = 4;
    string sync_status = 5;
    string sync_time = 6;
    bool auto_sync = 7;
    uint32 sync_cycle = 8;
    string sync_unit = 9;
    repeated KV field_map = 10;
    string user_filter = 11;
    string group_filter = 12;
  }
  message GroupBasicInfo{
    string name = 1;
    string source_type = 2;
    string source_name = 3;
    string id = 4;
    bool is_default = 5;
    string description = 6;
  }
  message InfogoConfig{
    string endpoint = 1;
    string login = 2;
    string pass = 3;
    string sync_status = 4;
    string sync_time = 5;
    bool auto_sync = 6;
    uint32 sync_cycle = 7;
    string sync_unit = 8;
    repeated KV field_map = 9;
  }
  message OAuth2Config{
    string global_data = 1;
    string sync_status = 2;
    string sync_time = 3;
    bool auto_sync = 4;
    uint32 sync_cycle = 5;
    string sync_unit = 6;
    repeated KV field_map = 7;
  }
  GroupBasicInfo group_info = 1;
  WxConfig wx_config = 2;
  FeiShuConfig feishu_config = 3;
  DingtalkConfig dingtalkConfig = 4;
  AdConfig adConfig = 5;
  InfogoConfig infogoConfig = 6;
  OAuth2Config oAuth2Config = 7;
}
message ListSyncLogRequest{
  string root_group_id = 1;
  uint32 limit = 2;
  uint32 offset = 3;
}

message ListSyncLogReply{
  message SyncLog{
    string sync_time = 1;
    string type = 2;
    string sync_status = 3;
    string sync_info = 4;
    string operator = 5;
  }
  repeated SyncLog sync_logs = 1;
  uint32 count = 2;
}

message GetFieldMapRequest{
  string src_type = 1;
  string field_map_type = 2;
}

message GetFieldMapReply{
  repeated KV field_map = 1;
}

message GetFieldOptionsRequest{}

message GetFieldOptionsReply{
  repeated KV options = 1;
}

message ValidateWebAuthScriptRequest {
  string script = 1;      // 脚本内容
  string script_type = 2; // 脚本类型: input/output
}

message ValidateWebAuthScriptReply {
  bool valid = 1;       // 是否有效
  string error = 2;     // 错误信息
}

// 账户策略管理相关消息定义
message CreateAccountPolicyRequest {
  string name = 1;                                    // 策略名称
  string description = 2;                             // 策略描述
  
  // 密码策略相关字段
  int32 password_min_length = 3;                      // 密码最小位数
  int32 password_max_length = 4;                      // 密码最大位数
  int32 password_validity_days = 5;                   // 密码有效期（天）
  int32 password_history_count = 6;                   // 密码历史个数
  bool password_complexity_uppercase = 7;             // 密码复杂度-大写字母
  bool password_complexity_lowercase = 8;             // 密码复杂度-小写字母
  bool password_complexity_numbers = 9;               // 密码复杂度-数字
  bool password_complexity_special_chars = 10;        // 密码复杂度-特殊字符
  bool password_complexity_no_username = 11;          // 密码复杂度-不包含账户名
  
  // 防暴力破解策略 - 密码错误次数限制
  bool password_failure_enabled = 12;                 // 是否启用密码错误次数限制
  int32 password_max_failure_count = 13;              // 连续密码错误最大次数
  int32 password_lockout_duration_sec = 14;           // 锁定时长（秒）
  int32 password_reset_failure_window_sec = 15;       // 密码错误次数重置窗口（秒）
  
  // 防暴力破解策略 - IP错误次数限制
  bool ip_failure_enabled = 16;                       // 是否启用IP错误次数限制
  int32 ip_max_failure_count = 17;                    // 同IP连续错误最大次数
  int32 ip_lockout_duration_sec = 18;                 // IP锁定时长（秒）
  int32 ip_reset_failure_window_sec = 19;             // IP错误次数重置窗口（秒）
  
  // 通用字段
  bool enable = 20;                                    // 是否启用该策略
  bool is_default = 21;                               // 是否为默认策略
  int32 priority = 22;                                // 策略优先级，数值越大优先级越高
}

message CreateAccountPolicyReply {
  StatusCode status = 1;
  string id = 2;                                      // 创建的策略ID
}

message UpdateAccountPolicyRequest {
  string id = 1;                                      // 策略ID
  string name = 2;                                    // 策略名称
  string description = 3;                             // 策略描述
  
  // 密码策略相关字段
  int32 password_min_length = 4;                      // 密码最小位数
  int32 password_max_length = 5;                      // 密码最大位数
  int32 password_validity_days = 6;                   // 密码有效期（天）
  int32 password_history_count = 7;                   // 密码历史个数
  bool password_complexity_uppercase = 8;             // 密码复杂度-大写字母
  bool password_complexity_lowercase = 9;             // 密码复杂度-小写字母
  bool password_complexity_numbers = 10;              // 密码复杂度-数字
  bool password_complexity_special_chars = 11;        // 密码复杂度-特殊字符
  bool password_complexity_no_username = 12;          // 密码复杂度-不包含账户名
  
  // 防暴力破解策略 - 密码错误次数限制
  bool password_failure_enabled = 13;                 // 是否启用密码错误次数限制
  int32 password_max_failure_count = 14;              // 连续密码错误最大次数
  int32 password_lockout_duration_sec = 15;           // 锁定时长（秒）
  int32 password_reset_failure_window_sec = 16;       // 密码错误次数重置窗口（秒）
  
  // 防暴力破解策略 - IP错误次数限制
  bool ip_failure_enabled = 17;                       // 是否启用IP错误次数限制
  int32 ip_max_failure_count = 18;                    // 同IP连续错误最大次数
  int32 ip_lockout_duration_sec = 19;                 // IP锁定时长（秒）
  int32 ip_reset_failure_window_sec = 20;             // IP错误次数重置窗口（秒）
  
  // 通用字段
  bool enable = 21;                                    // 是否启用该策略
  bool is_default = 22;                               // 是否为默认策略
  int32 priority = 23;                                // 策略优先级，数值越大优先级越高
}

message UpdateAccountPolicyReply {
  StatusCode status = 1;
}

message DeleteAccountPolicyRequest {
  string id = 1;                                      // 策略ID
  string name = 2;                                    // 策略名称（用于确认）
}

message DeleteAccountPolicyReply {
  StatusCode status = 1;
}

message GetAccountPolicyRequest {
  string id = 1;                                      // 策略ID
}

message AccountPolicyInfo {
  string id = 1;                                      // 策略ID
  string corp_id = 2;                                 // 租户ID
  string name = 3;                                    // 策略名称
  string description = 4;                             // 策略描述
  
  // 密码策略相关字段
  int32 password_min_length = 5;                      // 密码最小位数
  int32 password_max_length = 6;                      // 密码最大位数
  int32 password_validity_days = 7;                   // 密码有效期（天）
  int32 password_history_count = 8;                   // 密码历史个数
  bool password_complexity_uppercase = 9;             // 密码复杂度-大写字母
  bool password_complexity_lowercase = 10;            // 密码复杂度-小写字母
  bool password_complexity_numbers = 11;              // 密码复杂度-数字
  bool password_complexity_special_chars = 12;        // 密码复杂度-特殊字符
  bool password_complexity_no_username = 13;          // 密码复杂度-不包含账户名
  
  // 防暴力破解策略 - 密码错误次数限制
  bool password_failure_enabled = 14;                 // 是否启用密码错误次数限制
  int32 password_max_failure_count = 15;              // 连续密码错误最大次数
  int32 password_lockout_duration_sec = 16;           // 锁定时长（秒）
  int32 password_reset_failure_window_sec = 17;       // 密码错误次数重置窗口（秒）
  
  // 防暴力破解策略 - IP错误次数限制
  bool ip_failure_enabled = 18;                       // 是否启用IP错误次数限制
  int32 ip_max_failure_count = 19;                    // 同IP连续错误最大次数
  int32 ip_lockout_duration_sec = 20;                 // IP锁定时长（秒）
  int32 ip_reset_failure_window_sec = 21;             // IP错误次数重置窗口（秒）
  
  // 通用字段
  bool enable = 22;                                    // 是否启用该策略
  bool is_default = 23;                               // 是否为默认策略
  int32 priority = 24;                                // 策略优先级，数值越大优先级越高
  
  // 时间戳
  string created_at = 25;                             // 创建时间
  string updated_at = 26;                             // 更新时间
}

message GetAccountPolicyReply {
  AccountPolicyInfo policy = 1;
}

message ListAccountPoliciesRequest {
  uint32 limit = 1;                                   // 分页大小
  uint32 offset = 2;                                  // 分页偏移
  string search = 3;                                  // 搜索关键词
}

message ListAccountPoliciesReply {
  repeated AccountPolicyInfo policies = 1;            // 策略列表
  uint32 count = 2;                                   // 总数量
}

// 账户锁定管理相关消息定义
message UnlockAccountRequest {
  string user_id = 1;                                 // 用户ID
  string reason = 2;                                  // 解锁原因
}

message UnlockAccountReply {
  StatusCode status = 1;
  string message = 2;                                 // 操作结果信息
}

message BatchUnlockAccountsRequest {
  repeated string user_ids = 1;                       // 用户ID数组
  string reason = 2;                                  // 解锁原因
}

message BatchUnlockAccountsReply {
  StatusCode status = 1;
  string message = 2;                                 // 操作结果信息
  int32 success_count = 3;                           // 成功解锁数量
  int32 failed_count = 4;                            // 失败数量
  repeated string failed_user_ids = 5;               // 解锁失败的用户ID列表
}

message GetAccountLockInfoRequest {
  string user_id = 1;                                 // 用户ID
}

message AccountLockInfo {
  bool is_locked = 1;                                 // 是否被锁定
  string locked_until = 2;                           // 锁定到期时间
  string reason = 3;                                  // 锁定原因
  int32 failure_count = 4;                           // 当前失败次数
  string last_failure_time = 5;                      // 最后一次失败时间
  int32 remaining_seconds = 6;                       // 剩余锁定秒数
}

message GetAccountLockInfoReply {
  AccountLockInfo lock_info = 1;
}

// 列出锁定账户相关消息定义
message ListLockedAccountsRequest {
  uint32 limit = 1;                                   // 分页大小
  uint32 offset = 2;                                  // 分页偏移
  string search = 3;                                  // 搜索关键词（用户名、显示名等）
  string lock_type = 4;                               // 锁定类型筛选：password/admin/all
}

message LockedAccountInfo {
  string user_id = 1;                                 // 用户ID
  string username = 2;                                // 用户名
  string group_id = 3;                                // 所属组ID
  string group_name = 4;                              // 所属组名
  AccountLockInfo lock_info = 5;                      // 锁定信息
}

message ListLockedAccountsReply {
  repeated LockedAccountInfo accounts = 1;            // 锁定账户列表
  uint32 count = 2;                                   // 总数量
}

// IP锁定管理相关消息定义
message UnlockIPRequest {
  string ip_address = 1;                              // IP地址
  string reason = 2;                                  // 解锁原因
}

message UnlockIPReply {
  StatusCode status = 1;
  string message = 2;                                 // 操作结果信息
}

message BatchUnlockIPsRequest {
  repeated string ip_addresses = 1;                   // IP地址数组
  string reason = 2;                                  // 解锁原因
}

message BatchUnlockIPsReply {
  StatusCode status = 1;
  string message = 2;                                 // 操作结果信息
  int32 success_count = 3;                           // 成功解锁数量
  int32 failed_count = 4;                            // 失败数量
  repeated string failed_ip_addresses = 5;           // 解锁失败的IP地址列表
}

message GetIPLockInfoRequest {
  string ip_address = 1;                              // IP地址
}

message IPLockInfo {
  bool is_locked = 1;                                 // 是否被锁定
  string locked_until = 2;                           // 锁定到期时间
  string reason = 3;                                  // 锁定原因
  int32 failure_count = 4;                           // 当前失败次数
  string last_failure_time = 5;                      // 最后一次失败时间
  int32 remaining_seconds = 6;                       // 剩余锁定秒数
  string ip_address = 7;                              // IP地址
}

message GetIPLockInfoReply {
  IPLockInfo lock_info = 1;
}

// 列出锁定IP相关消息定义
message ListLockedIPsRequest {
  uint32 limit = 1;                                   // 分页大小
  uint32 offset = 2;                                  // 分页偏移
  string search = 3;                                  // 搜索关键词（IP地址）
  string corp_id = 4;                                 // 企业ID筛选
}

message LockedIPInfo {
  string ip_address = 1;                              // IP地址
  string corp_id = 2;                                 // 所属企业ID
  string corp_name = 3;                               // 所属企业名称
  IPLockInfo lock_info = 4;                          // 锁定信息
  string location = 5;                                // IP地理位置（可选）
  int32 total_attempts = 6;                          // 总尝试次数
}

message ListLockedIPsReply {
  repeated LockedIPInfo ips = 1;                      // 锁定IP列表
  uint32 count = 2;                                   // 总数量
}

// 会话管理相关消息定义
message ListUserSessionsRequest {
  string user_id = 1;                                 // 用户ID（可选，为空则显示所有用户会话）
  string user_name = 2;                               // 用户名（可选，支持模糊搜索）
  string client_category = 3;                         // 客户端分类：pc、mobile、all（默认all）
  uint32 limit = 4;                                   // 分页大小
  uint32 offset = 5;                                  // 分页偏移
}

message UserSessionInfo {
  string session_id = 1;                              // 会话ID
  repeated UserEntity users = 2;                      // 用户信息列表
  string operating_system = 3;                        // 操作系统：windows/macos/linux/android/ios等
  string client_category = 4;                         // 客户端分类：pc/mobile
  string device_info = 5;                             // 设备信息
  string ip_address = 6;                              // IP地址
  string login_time = 7;                              // 登录时间
  string last_activity_time = 8;                      // 最后活动时间
  string token_jti = 9;                               // JWT ID
  string policy_id = 10;                              // 认证策略ID
}

message ListUserSessionsReply {
  repeated UserSessionInfo sessions = 1;              // 会话列表
  uint32 count = 2;                                   // 总数量
}

message KickUserSessionRequest {
  string session_id = 1;                              // 要踢掉的会话ID
  string reason = 3;                                  // 踢掉原因（可选）
}

message KickUserSessionReply {
  StatusCode status = 1;                              // 操作状态
  string message = 2;                                 // 操作结果信息
}

message GetClientLimitsRequest {
  string policy_id = 1;                               // 认证策略ID（可选，为空则返回默认配置）
}

message ClientLimitsConfig {
  int32 pc_max_sessions = 1;                          // PC最大会话数
  int32 mobile_max_sessions = 2;                      // 移动端最大会话数
  string overflow_strategy = 3;                       // 溢出策略：kick_oldest/kick_inactive/reject_new
}

message GetClientLimitsReply {
  ClientLimitsConfig config = 1;                      // 客户端限制配置
}

message UpdateClientLimitsRequest {
  string policy_id = 1;                               // 认证策略ID
  ClientLimitsConfig config = 2;                      // 新的客户端限制配置
}

message UpdateClientLimitsReply {
  StatusCode status = 1;                              // 操作状态
  string message = 2;                                 // 操作结果信息
}