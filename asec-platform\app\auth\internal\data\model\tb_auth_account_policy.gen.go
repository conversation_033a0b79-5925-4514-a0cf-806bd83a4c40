// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbAuthAccountPolicy = "tb_auth_account_policy"

// TbAuthAccountPolicy mapped from table <tb_auth_account_policy>
type TbAuthAccountPolicy struct {
	ID                             string    `gorm:"column:id;primaryKey;default:(gen_random_uuid());comment:策略ID" json:"id"`                                             // 策略ID
	CorpID                         string    `gorm:"column:corp_id;not null;comment:租户ID" json:"corp_id"`                                                                 // 租户ID
	Name                           string    `gorm:"column:name;not null;comment:策略名称" json:"name"`                                                                       // 策略名称
	Description                    string    `gorm:"column:description;comment:策略描述" json:"description"`                                                                  // 策略描述
	PasswordMinLength              int32     `gorm:"column:password_min_length;default:8;comment:密码最小位数" json:"password_min_length"`                                      // 密码最小位数
	PasswordMaxLength              int32     `gorm:"column:password_max_length;default:20;comment:密码最大位数" json:"password_max_length"`                                     // 密码最大位数
	PasswordValidityDays           int32     `gorm:"column:password_validity_days;default:90;comment:密码有效期（天）" json:"password_validity_days"`                             // 密码有效期（天）
	PasswordHistoryCount           int32     `gorm:"column:password_history_count;default:5;comment:密码历史个数" json:"password_history_count"`                                // 密码历史个数
	PasswordComplexityUppercase    bool      `gorm:"column:password_complexity_uppercase;comment:密码复杂度-大写字母" json:"password_complexity_uppercase"`                        // 密码复杂度-大写字母
	PasswordComplexityLowercase    bool      `gorm:"column:password_complexity_lowercase;comment:密码复杂度-小写字母" json:"password_complexity_lowercase"`                        // 密码复杂度-小写字母
	PasswordComplexityNumbers      bool      `gorm:"column:password_complexity_numbers;comment:密码复杂度-数字" json:"password_complexity_numbers"`                              // 密码复杂度-数字
	PasswordComplexitySpecialChars bool      `gorm:"column:password_complexity_special_chars;comment:密码复杂度-特殊字符" json:"password_complexity_special_chars"`                // 密码复杂度-特殊字符
	PasswordComplexityNoUsername   bool      `gorm:"column:password_complexity_no_username;default:true;comment:密码复杂度-不包含账户名" json:"password_complexity_no_username"`     // 密码复杂度-不包含账户名
	PasswordFailureEnabled         bool      `gorm:"column:password_failure_enabled;comment:是否启用密码错误次数限制" json:"password_failure_enabled"`                                // 是否启用密码错误次数限制
	PasswordMaxFailureCount        int32     `gorm:"column:password_max_failure_count;default:5;comment:连续密码错误最大次数" json:"password_max_failure_count"`                    // 连续密码错误最大次数
	PasswordLockoutDurationSec     int32     `gorm:"column:password_lockout_duration_sec;default:300;comment:锁定时长（秒）" json:"password_lockout_duration_sec"`               // 锁定时长（秒）
	PasswordResetFailureWindowSec  int32     `gorm:"column:password_reset_failure_window_sec;default:900;comment:密码错误次数重置窗口（秒）" json:"password_reset_failure_window_sec"` // 密码错误次数重置窗口（秒）
	IPFailureEnabled               bool      `gorm:"column:ip_failure_enabled;comment:是否启用IP错误次数限制" json:"ip_failure_enabled"`                                            // 是否启用IP错误次数限制
	IPMaxFailureCount              int32     `gorm:"column:ip_max_failure_count;default:32;comment:同IP连续错误最大次数" json:"ip_max_failure_count"`                              // 同IP连续错误最大次数
	IPLockoutDurationSec           int32     `gorm:"column:ip_lockout_duration_sec;default:300;comment:IP锁定时长（秒）" json:"ip_lockout_duration_sec"`                         // IP锁定时长（秒）
	IPResetFailureWindowSec        int32     `gorm:"column:ip_reset_failure_window_sec;default:1800;comment:IP错误次数重置窗口（秒）" json:"ip_reset_failure_window_sec"`            // IP错误次数重置窗口（秒）
	Enable                         bool      `gorm:"column:enable;default:true;comment:是否启用该策略" json:"enable"`                                                            // 是否启用该策略
	IsDefault                      bool      `gorm:"column:is_default;comment:是否为默认策略" json:"is_default"`                                                                 // 是否为默认策略
	Priority                       int32     `gorm:"column:priority;comment:策略优先级，数值越大优先级越高" json:"priority"`                                                             // 策略优先级，数值越大优先级越高
	CreatedAt                      time.Time `gorm:"column:created_at;not null;default:now();comment:创建时间" json:"created_at"`                                             // 创建时间
	UpdatedAt                      time.Time `gorm:"column:updated_at;not null;default:now();comment:更新时间" json:"updated_at"`                                             // 更新时间
}

// TableName TbAuthAccountPolicy's table name
func (*TbAuthAccountPolicy) TableName() string {
	return TableNameTbAuthAccountPolicy
}
