package api

import (
	"fmt"

	"asdsec.com/asec/platform/app/console/app/auth/admin/constants"
	"asdsec.com/asec/platform/app/console/app/auth/admin/model"
	"asdsec.com/asec/platform/app/console/app/auth/admin/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	globalModel "asdsec.com/asec/platform/pkg/model"
	"github.com/dlclark/regexp2"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateAdmin godoc
// @Summary 创建管理员
// @Schemes
// @Description 创建管理员
// @Tags        Admin
// @Produce     application/json
// @Success     200
// @Router      /v1/admin [POST]
// @success     200 {object} common.Response{data=model.CreateAdminReq} "create admin"
func CreateAdmin(ctx *gin.Context) {
	req := model.CreateAdminReq{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}

	regex, err := regexp2.Compile(constants.PasswordRegex, 0)
	if err != nil {
		fmt.Println("Error compiling regex:", err)
		return
	}
	if match, err := regex.MatchString(req.Password); !match || err != nil {
		common.Fail(ctx, common.PasswordNeedStrength)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.AdminResourceType,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	req.CorpId = web.GetAdminCorpId(ctx)
	err = service.GetAdminService().CreateAdmin(ctx, req)
	if err != nil {
		errorLog = err.Error()
		common.FailWithMessage(ctx, -1, err.Error())
		return
	}
	common.Ok(ctx)
}

// DeleteAdmin godoc
// @Summary 删除管理员
// @Schemes
// @Description 删除管理员
// @Tags        Admin
// @Produce     application/json
// @Success     200
// @Router      /v1/admin [Delete]
// @success     200 {object} common.Response{} "delete admin"
func DeleteAdmin(ctx *gin.Context) {
	id := ctx.Query("user_id")
	name := ctx.Query("name")
	corpId := web.GetAdminCorpId(ctx)
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.AdminResourceType,
			OperationType:  common.OperateDelete,
			Representation: name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err := service.GetAdminService().DeleteAdmin(ctx, id, corpId)
	if err != nil {
		global.SysLog.Error("admin delete error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// GetAdminList godoc
// @Summary 获取管理员列表
// @Schemes
// @Description 获取管理员列表
// @Tags       	Get Admin List
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/list [GET]
// @success     200 {object} common.Response{} "get admin list"
func GetAdminList(ctx *gin.Context) {
	var req model.GetAdminListReq
	err := ctx.ShouldBindQuery(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	req.CorpId = web.GetAdminCorpId(ctx)
	req.UserId = web.GetCurrentAdminId(ctx)
	data, err := service.GetAdminService().GetAdminList(ctx, req)
	if err != nil {
		global.SysLog.Error("get admin list error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, data)
}

// GetAdminById godoc
// @Summary 获取管理员信息
// @Schemes
// @Description 获取管理员列表
// @Tags       	Get Admin List
// @Produce     application/json
// @Success     200
// @Router      /v1/admin [GET]
// @success     200 {object} common.Response{} "get admin by id"
func GetAdminById(ctx *gin.Context) {
	corpId := web.GetAdminCorpId(ctx)
	userId := ctx.Query("id")
	if userId == "" {
		userId = web.GetCurrentAdminId(ctx)
	}
	data, err := service.GetAdminService().GetAdminById(ctx, userId, corpId)
	if err != nil {
		global.SysLog.Error("get admin error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, data)
}

// UpdateAdmin godoc
// @Summary 修改管理员信息
// @Schemes
// @Description 获取管理员列表
// @Tags       	Get Admin List
// @Produce     application/json
// @Success     200
// @Router      /v1/admin [PUT]
// @success     200 {object} common.Response{} "get admin list"
func UpdateAdmin(ctx *gin.Context) {
	req := model.UpdateAdminReq{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	//更新用户可能存在密码不传的情况
	if req.Password != "" {
		regex, err := regexp2.Compile(constants.PasswordRegex, 0)
		if err != nil {
			fmt.Println("Error compiling regex:", err)
			return
		}
		if match, err := regex.MatchString(req.Password); !match || err != nil {
			common.Fail(ctx, common.PasswordNeedStrength)
			return
		}
	}

	corpId := web.GetAdminCorpId(ctx)
	req.CorpId = corpId
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.AdminResourceType,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetAdminService().UpdateAdmin(ctx, req)
	if err != nil {
		global.SysLog.Error("update admin error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// UpdateAdminPwd godoc
// @Summary 修改管理员密码
// @Schemes
// @Description 修改管理员密码
// @Tags       	Update Admin Pwd
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/password [PUT]
// @success     200 {object} common.Response{data=model.UpdateSelfAdminReq} "update admin pwd self"
func UpdateAdminPwd(ctx *gin.Context) {
	req := model.UpdateSelfAdminReq{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	req.UserId = web.GetCurrentAdminId(ctx)
	req.CorpId = web.GetAdminCorpId(ctx)
	regex, err := regexp2.Compile(constants.PasswordRegex, 0)
	if err != nil {
		fmt.Println("Error compiling regex:", err)
		return
	}
	if match, err := regex.MatchString(req.NewPassword); !match || err != nil {
		common.Fail(ctx, common.PasswordNeedStrength)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.AdminResourceType,
			OperationType:  common.OperateUpdate,
			Representation: fmt.Sprintf("更新 %s 用户密码", web.GetCurrentAdmin(ctx).Username),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetAdminService().UpdateAdminPassword(ctx, req)
	if err != nil {
		global.SysLog.Error("update admin error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// GetLicenseInfo godoc
// @Summary 获取授权信息
// @Schemes
// @Description 获取授权信息
// @Tags       	Get Authorization
// @Produce     application/json
// @Success     200
// @Router      /v1/login/authorization [GET]
// @success     200 {object} common.Response{}
func GetLicenseInfo(ctx *gin.Context) {
	corpId := web.GetAdminCorpId(ctx)
	data, err := service.GetLoginService().GetLicenseInfo(ctx, corpId)
	if err != nil {
		global.SysLog.Error("get authorization error", zap.Error(err))
		common.Fail(ctx, common.AuthorizationError)
		return
	}
	common.OkWithData(ctx, data)
}

func UpdateAdminAuthConfig(ctx *gin.Context) {
	req := model.AdminAuthConfig{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.ApplianceResourceType,
			OperationType:  common.OperateUpdate,
			Representation: "控制台管理",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetAdminService().UpdateAdminAuthConfig(ctx, req)
	if err != nil {
		global.SysLog.Error("UpdateAdminAuthConfig err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}
func GetAdminAuthConfig(ctx *gin.Context) {
	data, err := service.GetAdminService().GetAdminAuthConfig(ctx)
	if err != nil {
		global.SysLog.Error("GetAdminAuthConfig err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, data)
}

// UpdateUserTokenConfig godoc
// @Summary 更新用户token配置
// @Schemes
// @Description 更新用户token配置(access_token_duration和refresh_token_duration)
// @Tags        Admin
// @Accept      application/json
// @Produce     application/json
// @Param       object body model.UserTokenConfig true "用户token配置"
// @Success     200
// @Router      /v1/admin/usertoken/config [PUT]
// @success     200 {object} common.Response{} "update user token config"
func UpdateUserTokenConfig(ctx *gin.Context) {
	req := model.UserTokenConfig{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.SystemResourceType,
			OperationType:  common.OperateUpdate,
			Representation: "用户token配置",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetAdminService().UpdateUserTokenConfig(ctx, req)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("UpdateUserTokenConfig err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// GetUserTokenConfig godoc
// @Summary 获取用户token配置
// @Schemes
// @Description 获取用户token配置(access_token_duration和refresh_token_duration)
// @Tags        Admin
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/usertoken/config [GET]
// @success     200 {object} common.Response{data=model.UserTokenConfig} "get user token config"
func GetUserTokenConfig(ctx *gin.Context) {
	data, err := service.GetAdminService().GetUserTokenConfig(ctx)
	if err != nil {
		global.SysLog.Error("GetUserTokenConfig err", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, data)
}
