package user

import "asdsec.com/asec/platform/app/appliance-sidecar/global"

type UserInfo struct {
	UserName string
	UserId   string
}

func GetUserInfo() UserInfo {
	var userInfo UserInfo
	stmt, err := global.SqliteClient.Prepare("select sub_id,user_name from tb_user_info limit 1")
	if err != nil {
		global.Logger.Sugar().Errorf("SqliteClient Prepare err:%v", err)
		return userInfo
	}
	rows, err := stmt.Query()
	if err != nil {
		global.Logger.Sugar().Errorf("stmt Query userId err:%v", err)
		return userInfo
	}
	defer rows.Close()
	defer stmt.Close()
	if rows.Next() {
		err = rows.Scan(&userInfo.UserId, &userInfo.UserName)
		if err != nil {
			global.Logger.Sugar().Errorf("stmt Scan userId err:%v", err)
			return userInfo
		}
	}
	return userInfo
}
