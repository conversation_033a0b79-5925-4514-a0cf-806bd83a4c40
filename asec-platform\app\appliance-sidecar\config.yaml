zap:
  level: info
  prefix: ""
  format: json
  director: log
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  max-age: 0
  show-line: true
  log-in-console: true
#传输通信相关配置
transport:
  #  使用insecure的模式建立gRPC连接，仅允许开发中使用
  insecure: false
#  TODO TLS证书配置

#部署位置
location:
  Region: default

#日志采集
logbeat:
  enable: true
  interval: 10
  level: warn
  host: cs.asdsec.com
  port: 443

#平台endpoints相关
endpoints:
  service-discovery:
  private-host: cs.asdsec.com:9443
  log-center-host: cs.asdsec.com:9443
  public-host:

#心跳相关
heartbeat:
  enable: true
  interval: 30
  procs-stat-enable: true
  procs-stat-interval: 60

#license检查相关
license-check:
  enable: false
  interval: 300

accessLog:
  enable: true
  interval: 10

webGwConfig:
  enable: true
  timeout: 60

  # 虚拟IP配置
virtualip:
  enable: true
  config-file-path: "./virtualip_config.json"
  sync-interval: 30