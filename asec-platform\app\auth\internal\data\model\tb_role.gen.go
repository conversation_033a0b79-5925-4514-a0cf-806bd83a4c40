// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbRole = "tb_role"

// TbRole mapped from table <tb_role>
type TbRole struct {
	ID          string    `gorm:"column:id;primaryKey" json:"id"`        // 角色id
	Name        string    `gorm:"column:name;not null" json:"name"`      // 角色名称，租户下角色名唯一
	Description string    `gorm:"column:description" json:"description"` // 角色描述
	CorpID      string    `gorm:"column:corp_id" json:"corp_id"`         // 租户id
	IsAllUser   int16     `gorm:"column:is_all_user" json:"is_all_user"`
	CreatedAt   time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbRole's table name
func (*TbRole) TableName() string {
	return TableNameTbRole
}
