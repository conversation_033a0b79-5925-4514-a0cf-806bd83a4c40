package api

import (
	"asdsec.com/asec/platform/app/console/app/alert_event/model"
	"asdsec.com/asec/platform/app/console/app/alert_event/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UpdateDlpScoreConfig godoc
// @Summary 修改DLP评分配置
// @Schemes
// @Description 修改DLP评分配置
// @Tags        alert
// @Produce     application/json
// @Param       req body model.UpdateScoreConfig true "修改DLP评分配置"
// @Success     200
// @Router      /v1/alert/event/setting [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateDlpScoreConfig(c *gin.Context) {
	var req model.UpdateScoreConfig
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.MaxScore < req.MinScore && req.MaxScore != -1 {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.UserRiskConfigType,
			OperationType:  common.OperateUpdate,
			Representation: "Update Dlp Risk Score Config Id:" + req.Id,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetAlertEventService().UpdateScoreConfig(c, req)
	if err != nil {
		global.SysLog.Error("update score config err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}
