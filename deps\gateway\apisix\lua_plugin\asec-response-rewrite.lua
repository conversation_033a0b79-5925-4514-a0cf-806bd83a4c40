--
-- Licensed to the Apache Software Foundation (ASF) under one or more
-- contributor license agreements.  See the NOTICE file distributed with
-- this work for additional information regarding copyright ownership.
-- The ASF licenses this file to You under the Apache License, Version 2.0
-- (the "License"); you may not use this file except in compliance with
-- the License.  You may obtain a copy of the License at
--
--     http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing, software
-- distributed under the License is distributed on an "AS IS" BASIS,
-- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
-- See the License for the specific language governing permissions and
-- limitations under the License.
--
local core        = require("apisix.core")
local expr        = require("resty.expr.v1")
local re_compile  = require("resty.core.regex").re_match_compile
local plugin_name = "asec-response-rewrite"
local ngx         = ngx
local re_match    = ngx.re.match
local re_sub      = ngx.re.sub
local re_gsub     = ngx.re.gsub
local pairs       = pairs
local ipairs      = ipairs
local type        = type
local pcall       = pcall


local lrucache = core.lrucache.new({
    type = "plugin",
})

local schema = {
    type = "object",
    properties = {
        headers = {
            description = "new headers for response",
            anyOf = {
                {
                    type = "object",
                    minProperties = 1,
                    patternProperties = {
                        ["^[^:]+$"] = {
                            oneOf = {
                                {type = "string"},
                                {type = "number"},
                            }
                        }
                    },
                },
                {
                    properties = {
                        add = {
                            type = "array",
                            minItems = 1,
                            items = {
                                type = "string",
                                -- "Set-Cookie: <cookie-name>=<cookie-value>; Max-Age=<number>"
                                pattern = "^[^:]+:[^:]*[^/]$"
                            }
                        },
                        set = {
                            type = "object",
                            minProperties = 1,
                            patternProperties = {
                                ["^[^:]+$"] = {
                                    oneOf = {
                                        {type = "string"},
                                        {type = "number"},
                                    }
                                }
                            },
                        },
                        update = {
                            type = "array",
                            minItems = 1,
                            items = {
                                type = "object",
                                properties = {
                                    header_key = {
                                        type = "string",
                                        description = "the header of update",
                                    },
                                    header_origin = {
                                        type = "string",
                                        description = "the header of origin value",
                                    },
                                    header_replace = {
                                        type = "string",
                                        description = "the header of origin replace",
                                    },
                                },
                                required = {"header_key"},
                            },
                            uniqueItems = true,
                        },
                        remove = {
                            type = "array",
                            minItems = 1,
                            items = {
                                type = "string",
                                -- "Set-Cookie"
                                pattern = "^[^:]+$"
                            }
                        },
                    },
                }
            }
        },
        body = {
            description = "new body for response",
            type = "string",
        },
        body_base64 = {
            description = "whether new body for response need base64 decode before return",
            type = "boolean",
            default = false,
        },
        status_code = {
            description = "new status code for response",
            type = "integer",
            minimum = 200,
            maximum = 598,
        },
        vars = {
            type = "array",
        },
        filters = {
            description = "a group of filters that modify response body" ..
                    "by replacing one specified string by another",
            type = "array",
            minItems = 1,
            items = {
                description = "filter that modifies response body",
                type = "object",
                required = {"regex", "replace"},
                properties = {
                    regex = {
                        description = "match pattern on response body",
                        type = "string",
                        minLength = 1,
                    },
                    scope = {
                        description = "regex substitution range",
                        type = "string",
                        enum = {"once", "global"},
                        default = "once",
                    },
                    replace = {
                        description = "regex substitution content",
                        type = "string",
                    },
                    options = {
                        description = "regex options",
                        type = "string",
                        default = "jo",
                    }
                },
            },
        },
    },
    dependencies = {
        body = {
            ["not"] = {required = {"filters"}}
        },
        filters = {
            ["not"] = {required = {"body"}}
        }
    }
}


local _M = {
    version  = 0.1,
    priority = 898,
    name     = plugin_name,
    schema   = schema,
}

local function vars_matched(conf, ctx)
    if not conf.vars then
        return true
    end

    if not conf.response_expr then
        local response_expr, _ = expr.new(conf.vars)
        conf.response_expr = response_expr
    end

    local match_result = conf.response_expr:eval(ctx.var)

    return match_result
end


local function is_new_headers_conf(headers)
    return
    (headers.add and type(headers.add) == "table") or
            (headers.set and type(headers.set) == "table") or
                (headers.update and type(headers.update) == "table") or
                (headers.remove and type(headers.remove) == "table")
end


local function check_set_headers(headers)
    for field, value in pairs(headers) do
        if type(field) ~= 'string' then
            return false, 'invalid type as header field'
        end

        if type(value) ~= 'string' and type(value) ~= 'number' then
            return false, 'invalid type as header value'
        end

        if #field == 0 then
            return false, 'invalid field length in header'
        end
    end

    return true
end


function _M.check_schema(conf)
    local ok, err = core.schema.check(schema, conf)
    if not ok then
        return false, err
    end

    if conf.headers then
        if not is_new_headers_conf(conf.headers) then
            ok, err = check_set_headers(conf.headers)
            if not ok then
                return false, err
            end
        end
    end

    if conf.body_base64 then
        if not conf.body or #conf.body == 0 then
            return false, 'invalid base64 content'
        end
        local body = ngx.decode_base64(conf.body)
        if not body then
            return  false, 'invalid base64 content'
        end
    end

    if conf.vars then
        local ok, err = expr.new(conf.vars)
        if not ok then
            return false, "failed to validate the 'vars' expression: " .. err
        end
    end

    if conf.filters then
        for _, filter in ipairs(conf.filters) do
            local ok, err = pcall(re_compile, filter.regex, filter.options)
            if not ok then
                return false, "regex \"" .. filter.regex ..
                        "\" validation failed: "  .. err
            end
        end
    end

    return true
end


-- 替换无端口或默认端口的URL，避免端口叠加问题
local function smart_port_aware_replace(body, filter)
    local regex = filter.regex
    local replace = filter.replace
    local options = filter.options or "jo"
    local scope = filter.scope or "once"
    
    -- 检查是否是带转义符的URL（如 https:\/\/）
    local has_https = regex:find("https?:") ~= nil
    local is_escaped_url = has_https and regex:find(":\\/\\/") ~= nil
    
    -- 检查是否是标准URL（如 https://）
    local is_standard_url = has_https and regex:find("://") ~= nil and regex:find(":\\/\\/") == nil
    
    -- 如果既不是转义符URL也不是标准URL，使用原有逻辑
    if not is_escaped_url and not is_standard_url then
        return nil
    end
    
    -- 确定协议默认端口
    local default_port = 80
    if regex:match("^https") then
        default_port = 443
    end

    core.log.debug("Smart port-aware replacement for: " .. regex .. " -> " .. replace)

    -- 创建一个安全的替换函数
    local function safe_replace_iterator(text)
        local result = text
        local pos = 1
        local replacements = 0
        local max_replacements = (scope == "once") and 1 or math.huge
        
        while pos <= #result and replacements < max_replacements do
            local start_pos, end_pos = result:find(regex, pos, true)  -- 精确文本匹配
            if not start_pos then
                break
            end
            
        -- 对于转义符URL，需要检查是否会导致端口重复
        if is_escaped_url then
            -- 检查匹配后面是否有端口号
            local next_char_pos = end_pos + 1
            local should_replace = true
            local replace_end_pos = end_pos
            
            if next_char_pos <= #result and result:sub(next_char_pos, next_char_pos) == ":" then
                -- 后面有冒号，检查是否是端口号
                local port_match_end = result:find("[^0-9]", next_char_pos + 1)
                if not port_match_end then
                    port_match_end = #result + 1
                end
                local port_str = result:sub(next_char_pos + 1, port_match_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    local port_num = tonumber(port_str)
                    -- 确定协议默认端口
                    local default_port = 80
                    if regex:match("^https") then
                        default_port = 443
                    end
                    
                    -- 原文有端口号，检查是否是默认端口
                    if port_num == default_port then
                        -- 是默认端口，可以替换，并且要包含端口部分
                        replace_end_pos = port_match_end - 1
                        should_replace = true
                    elseif replace:match(":%d+") then
                        -- 非默认端口，且替换字符串也有端口，避免重复，不进行替换
                        should_replace = false
                    end
                end
            end
            
            if should_replace then
                result = result:sub(1, start_pos - 1) .. replace .. result:sub(replace_end_pos + 1)
                pos = start_pos + #replace
                replacements = replacements + 1
            else
                pos = end_pos + 1
            end
            else
                -- 标准URL的智能替换逻辑
                local next_char_pos = end_pos + 1
                local should_replace = true
                local replace_end_pos = end_pos
                
                -- 如果匹配后面还有字符，检查是否构成完整URL
                if next_char_pos <= #result then
                    local next_char = result:sub(next_char_pos, next_char_pos)
                    
                    if next_char == ":" then
                        -- 后面有冒号，检查端口号
                        local port_match_end = result:find("[^0-9]", next_char_pos + 1)
                        if not port_match_end then
                            port_match_end = #result + 1
                        end
                        local port_str = result:sub(next_char_pos + 1, port_match_end - 1)
                        
                        if port_str and port_str:match("^%d+$") then
                            local port_num = tonumber(port_str)
                            -- 检查是否是配置中指定的端口
                            local config_port = regex:match(":(%d+)$")
                            if config_port then
                                local config_port_num = tonumber(config_port)
                                if port_num == config_port_num then
                                    -- 端口完全匹配，正常替换
                                    should_replace = true
                                    replace_end_pos = end_pos
                                else
                                    -- 端口不匹配，跳过替换
                                    should_replace = false
                                end
                            else
                                -- 配置没有端口，但文本有端口
                                if port_num == default_port then
                                    -- 默认端口，一起替换掉
                                    replace_end_pos = port_match_end - 1
                                else
                                    -- 非默认端口，跳过替换
                                    should_replace = false
                                end
                            end
                        else
                            -- 冒号后面不是纯数字，跳过替换
                            should_replace = false
                        end
                    elseif next_char:match("[%w%.%-]") then
                        -- 后面是字母、数字、点或连字符，说明是部分匹配，跳过
                        should_replace = false
                    else
                        -- 后面是其他字符（如/、空格、引号等），正常替换
                        should_replace = true
                    end
                else
                    -- 匹配到了字符串结尾，正常替换
                    should_replace = true
                end
                
                if should_replace then
                    -- 执行替换
                    result = result:sub(1, start_pos - 1) .. replace .. result:sub(replace_end_pos + 1)
                    pos = start_pos + #replace
                    replacements = replacements + 1
                    core.log.debug("Replaced standard URL: " .. regex .. " -> " .. replace)
                else
                    -- 跳过这个匹配，继续查找
                    pos = end_pos + 1
                end
            end
        end
        
        return result
    end
    
    local result = safe_replace_iterator(body)
    return result
end


-- 智能头部URL替换函数
local function smart_header_url_replace(header_value, origin_url, replace_url)
    -- 检查是否是转义符URL（如 https:\/\/）
    local has_https = origin_url:find("https?:") ~= nil
    local is_escaped_url = has_https and origin_url:find(":\\/\\/") ~= nil
    -- 检查是否是标准URL（如 https://）
    local is_standard_url = has_https and origin_url:find("://") ~= nil and origin_url:find(":\\/\\/") == nil
    -- 检查是否是URL编码的URL（如 https%3A%2F%2F）
    local is_encoded_url = origin_url:find("https?%%3A%%2F%%2F") ~= nil
    
    -- 如果既不是转义符URL也不是标准URL也不是URL编码URL，返回nil表示使用原有逻辑
    if not is_escaped_url and not is_standard_url and not is_encoded_url then
        return nil
    end
    
    -- 确定协议默认端口
    local default_port = 80
    if origin_url:match("^https") then
        default_port = 443
    end

    core.log.debug("Smart header URL replacement for: " .. origin_url .. " -> " .. replace_url)

    local result = header_value
    local pos = 1
    
    while true do
        local start_pos, end_pos = result:find(origin_url, pos, true)  -- 精确文本匹配
        if not start_pos then
            break
        end
        
        -- 检查是否已经有端口
        local next_char_pos = end_pos + 1
        local should_replace = true
        local replace_end_pos = end_pos
        
        -- 检查前面的字符，确保是完整URL匹配
        if start_pos > 1 then
            local prev_char = result:sub(start_pos - 1, start_pos - 1)
            -- 如果前面是字母、数字、点、连字符，说明不是完整匹配
            if prev_char:match("[%w%.%-]") then
                should_replace = false
                core.log.debug("Skipping: previous char '" .. prev_char .. "' indicates partial match")
            end
        end
        
        -- 对于URL编码的URL，需要特殊处理
        if should_replace and is_encoded_url then
            -- 检查匹配后面是否有编码的端口号（%3A表示冒号:）
            if next_char_pos <= #result and next_char_pos + 2 <= #result and result:sub(next_char_pos, next_char_pos + 2) == "%3A" then
                -- 后面有编码的冒号，检查是否是端口号
                local port_start = next_char_pos + 3
                local port_end = result:find("%%2F", port_start) -- 查找 %2F (/)
                if not port_end then
                    port_end = result:find("[^0-9]", port_start)
                end
                if not port_end then
                    port_end = #result + 1
                end
                local port_str = result:sub(port_start, port_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    local port_num = tonumber(port_str)
                    
                    -- 检查替换URL是否已经包含端口（包括编码和非编码形式）
                    if replace_url:match(":%d+") or replace_url:match("%%3A%d+") then
                        -- 替换URL已经有端口，不应该替换，避免双端口
                        should_replace = false
                        core.log.debug("Skipping encoded URL replacement: replace_url already has port")
                    end
                end
            end
        -- 对于转义符URL，需要检查是否会导致端口重复
        elseif should_replace and is_escaped_url then
            -- 检查匹配后面是否有端口号
            if next_char_pos <= #result and result:sub(next_char_pos, next_char_pos) == ":" then
                -- 后面有冒号，检查是否是端口号
                local port_match_end = result:find("[^0-9]", next_char_pos + 1)
                if not port_match_end then
                    port_match_end = #result + 1
                end
                local port_str = result:sub(next_char_pos + 1, port_match_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    local port_num = tonumber(port_str)
                    
                    -- 原文有端口号，检查是否是默认端口
                    if port_num == default_port then
                        -- 是默认端口，可以替换，并且要包含端口部分
                        replace_end_pos = port_match_end - 1
                        should_replace = true
                    elseif replace_url:match(":%d+") then
                        -- 非默认端口，且替换字符串也有端口，避免重复，不进行替换
                        should_replace = false
                    end
                end
            end
        elseif should_replace and is_standard_url then
            -- 标准URL的智能替换逻辑  
            if next_char_pos <= #result and result:sub(next_char_pos, next_char_pos) == ":" then
                -- 后面跟着冒号，可能是端口，需要谨慎处理
                local port_end = result:find("[^0-9]", next_char_pos + 1)
                if not port_end then
                    port_end = #result + 1
                end
                local port_str = result:sub(next_char_pos + 1, port_end - 1)
                
                if port_str:match("^%d+$") then
                    -- 后面确实是端口号
                    local port_num = tonumber(port_str)
                    
                    if port_num == default_port then
                        -- 是默认端口，一起替换掉
                        replace_end_pos = port_end - 1
                        -- core.log.info("Header: Replacing with default port " .. port_num .. ", removing port from URL")
                    elseif port_num ~= default_port and replace_url:match(":%d+") then
                        -- 非默认端口，且替换字符串也有端口，跳过替换
                        should_replace = false
                        -- core.log.info("Header: Skipping replacement: non-default port " .. port_num)
                    end
                end
            elseif next_char_pos <= #result and next_char_pos + 2 <= #result and result:sub(next_char_pos, next_char_pos + 2) == "%3A" then
                -- 检查URL编码的冒号（%3A），可能后面跟着端口号
                local encoded_port_start = next_char_pos + 3
                local port_end = result:find("[^0-9]", encoded_port_start)
                if not port_end then
                    port_end = #result + 1
                end
                local port_str = result:sub(encoded_port_start, port_end - 1)
                
                if port_str and port_str:match("^%d+$") then
                    -- 后面确实是URL编码的端口号
                    local port_num = tonumber(port_str)
                    
                    if port_num == default_port then
                        -- 是默认端口，一起替换掉（包括%3A部分）
                        replace_end_pos = port_end - 1
                    elseif port_num ~= default_port and replace_url:match(":%d+") then
                        -- 非默认端口，且替换字符串也有端口，跳过替换避免双端口
                        should_replace = false
                    end
                end
            else
                -- 检查后面的字符，确保是完整URL匹配
                if should_replace and next_char_pos <= #result then
                    local next_char = result:sub(next_char_pos, next_char_pos)
                    -- 如果后面是字母、数字、点、连字符，说明不是完整匹配
                    if next_char:match("[%w%.%-]") then
                        should_replace = false
                        core.log.debug("Skipping: next char '" .. next_char .. "' indicates partial match")
                    end
                end
                
                if should_replace then
                    core.log.debug("Replacing URL without port")
                end
            end
        end
        
        if should_replace then
            -- 执行替换
            result = result:sub(1, start_pos - 1) .. replace_url .. result:sub(replace_end_pos + 1)
            pos = start_pos + #replace_url
            core.log.debug("Header: Replaced: " .. origin_url .. " -> " .. replace_url)
        else
            -- 跳过这个匹配，继续查找
            pos = end_pos + 1
        end
        
        if pos > #result then
            break
        end
    end
    
    return result
end


do

    function _M.body_filter(conf, ctx)
        if not ctx.response_rewrite_matched then
            return
        end

        if conf.filters then
            local body = core.response.hold_body_chunk(ctx)
            if not body then
                return
            end

            local err
            for _, filter in ipairs(conf.filters) do
                -- 避免替换带有非默认端口的URL
                local enhanced_body = smart_port_aware_replace(body, filter)
                if enhanced_body then
                    body = enhanced_body
                else
                    -- 回退到原有逻辑
                    if filter.scope == "once" then
                        body, _, err = re_sub(body, filter.regex, filter.replace, filter.options)
                    else
                        body, _, err = re_gsub(body, filter.regex, filter.replace, filter.options)
                    end
                end
                if err ~= nil then
                    core.log.error("regex \"" .. filter.regex .. "\" substitutes failed:" .. err)
                end
            end

            ngx.arg[1] = body
            return
        end

        if conf.body then
            ngx.arg[2] = true
            if conf.body_base64 then
                ngx.arg[1] = ngx.decode_base64(conf.body)
            else
                ngx.arg[1] = conf.body
            end
        end
    end


    local function create_header_operation(hdr_conf)
        local set = {}
        local add = {}
        local update = {}
        if is_new_headers_conf(hdr_conf) then
            if hdr_conf.add then
                for _, value in ipairs(hdr_conf.add) do
                    local m, err = re_match(value, [[^([^:\s]+)\s*:\s*([^:]+)$]], "jo")
                    if not m then
                        return nil, err
                    end
                    core.table.insert_tail(add, m[1], m[2])
                end
            end

            if hdr_conf.set then
                for field, value in pairs(hdr_conf.set) do
                    --reform header from object into array, so can avoid use pairs, which is NYI
                    core.table.insert_tail(set, field, value)
                end
            end

            if hdr_conf.update then
                for _, value in pairs(hdr_conf.update) do
                    --reform header from object into array, so can avoid use pairs, which is NYI
                    core.log.warn("get update header conf of key:",value["header_key"])
                    core.table.insert_tail(update, value["header_key"],value["header_origin"],value["header_replace"])
                end
            end
        else
            for field, value in pairs(hdr_conf) do
                core.table.insert_tail(set, field, value)
            end
        end

        return {
            add = add,
            set = set,
            update = update,
            remove = hdr_conf.remove or {},
        }
    end


    function _M.header_filter(conf, ctx)
        ctx.response_rewrite_matched = vars_matched(conf, ctx)
        if not ctx.response_rewrite_matched then
            return
        end

        if conf.status_code then
            ngx.status = conf.status_code
        end

        -- if filters have no any match, response body won't be modified.
        if conf.filters or conf.body then
            core.response.clear_header_as_body_modified()
        end

        if not conf.headers then
            return
        end

        local hdr_op, err = core.lrucache.plugin_ctx(lrucache, ctx, nil,
                create_header_operation, conf.headers)
        if not hdr_op then
            core.log.error("failed to create header operation: ", err)
            return
        end

        local field_cnt = #hdr_op.add
        for i = 1, field_cnt, 2 do
            local val = core.utils.resolve_var(hdr_op.add[i+1], ctx.var)
            core.response.add_header(hdr_op.add[i], val)
        end

        local field_cnt = #hdr_op.set
        for i = 1, field_cnt, 2 do
            local val = core.utils.resolve_var(hdr_op.set[i+1], ctx.var)
            core.response.set_header(hdr_op.set[i], val)
        end

        local field_cnt = #hdr_op.update
        core.log.debug("get update header count:",field_cnt)
        for i = 1, field_cnt, 3 do
            local replace_val = core.utils.resolve_var(hdr_op.update[i+2], ctx.var)
            local ogi_val = core.utils.resolve_var(hdr_op.update[i+1], ctx.var)
            local header_key = core.utils.resolve_var(hdr_op.update[i], ctx.var)
            local now_val = ngx.header[header_key]
            core.log.warn("get now response header:",now_val)
            if now_val ~= nil then
                local val = now_val
                
                -- 尝试智能URL替换
                local enhanced_val = smart_header_url_replace(now_val, ogi_val, replace_val)
                if enhanced_val then
                    val = enhanced_val
                    -- core.log.debug("Applied smart URL replacement")
                else
                    -- 回退到原有的 re_gsub 逻辑（非URL场景）
                    local temp_val, _, herr = re_gsub(now_val, ogi_val, replace_val, "jo")
                    if herr then
                        core.log.error("re_gsub error:", herr)
                    else
                        val = temp_val
                    end
                end
                
                core.log.warn("get update header:",hdr_op.update[i], " ogi_value:",ogi_val," rep_value:",replace_val," old_value:",now_val," real_value:",val)
                core.response.set_header(hdr_op.update[i], val)
            end
        end

        local field_cnt = #hdr_op.remove
        for i = 1, field_cnt do
            core.response.set_header(hdr_op.remove[i], nil)
        end
    end

end  -- do


return _M
