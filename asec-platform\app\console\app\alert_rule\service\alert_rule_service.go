package service

import (
	"asdsec.com/asec/platform/app/console/app/alert_rule/model"
	repository1 "asdsec.com/asec/platform/app/console/app/alert_rule/repository"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"context"
	"sync"
)

var AlertRuleServiceImpl AlertRuleService

// AlertRuleServiceInit 单例对象
var AlertRuleServiceInit sync.Once

type AlertRuleService interface {
	GetAlertRuleList(ctx context.Context, req model.GetAlertRuleListReq) (modelTable.Pagination, error)
	GetAlertRuleTemplateList(ctx context.Context) ([]model.GetAlertRuleListResp, error)
	Create(ctx context.Context, req *modelTable.AlertRule) error
	CreateTemplate(ctx context.Context, req *model.AlertRuleTemplate) error
	Update(ctx context.Context, req *modelTable.AlertRule) error
	Delete(ctx context.Context, id string) error
}

type alertRuleService struct {
	db repository1.AlertRuleRepository
}

func (a *alertRuleService) GetAlertRuleTemplateList(ctx context.Context) ([]model.GetAlertRuleListResp, error) {
	return a.db.GetAlertRuleTemplateList(ctx)
}

func (a *alertRuleService) CreateTemplate(ctx context.Context, req *model.AlertRuleTemplate) error {
	return a.db.CreateTemplate(ctx, req)
}

func (a *alertRuleService) GetAlertRuleList(ctx context.Context, req model.GetAlertRuleListReq) (modelTable.Pagination, error) {
	return a.db.GetAlertRuleList(ctx, req)
}

func (a *alertRuleService) Create(ctx context.Context, req *modelTable.AlertRule) error {
	return a.db.Create(ctx, req)
}

func (a *alertRuleService) Update(ctx context.Context, req *modelTable.AlertRule) error {
	return a.db.Update(ctx, req)
}

func (a *alertRuleService) Delete(ctx context.Context, id string) error {
	return a.db.Delete(ctx, id)
}

func GetAlertRuleService() AlertRuleService {
	AlertRuleServiceInit.Do(func() {
		AlertRuleServiceImpl = &alertRuleService{db: repository1.NewAppRepository()}
	})
	return AlertRuleServiceImpl
}
