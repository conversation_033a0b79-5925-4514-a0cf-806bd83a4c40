package app_matcher

import (
	pb "asdsec.com/asec/platform/api/application/v1"
	"errors"
	"fmt"
	"net"
	"strings"
)

type UrlMatcherType int
type PortMatcherType int

const (
	IpMatcher             UrlMatcherType = iota // IP 匹配
	IpCidrMatcher                               // IP/Cidr 匹配
	DomainMatcher                               // 域名匹配
	DomainWildcardMatcher                       // 泛域名匹配
)

type Matcher interface {
	GetMatcherType() UrlMatcherType
	// DoMatcher
	// srcUrl srcPort 匹配来源url + 端口,
	// srcType 来源类型 1 ipUrl, 2 domainUrl
	DoMatcher(srcUrl string, srcPort int) error
	GetAppInfo() *pb.AppInfo
}

func GetAppMatcher(appInfo *pb.AppInfo) (Matcher, error) {
	urlType, ipNet := getUrlType(appInfo.Address)
	switch urlType {
	case IpMatcher:
		return NewIpMatcherAdapter(appInfo)
	case IpCidrMatcher:
		return NewIpCidrAdapter(appInfo, ipNet)
	case DomainMatcher:
		return NewDomainMatcherAdapter(appInfo)
	case DomainWildcardMatcher:
		return NewDomainWildcardAdapter(appInfo)
	default:
		return nil, errors.New(fmt.Sprintf("not matcher type,appInfo:%v", appInfo.String()))
	}
}

func getUrlType(url string) (UrlMatcherType, *net.IPNet) {
	ip := net.ParseIP(url)
	if ip != nil {
		// ip 应用
		return IpMatcher, nil
	}
	_, ipNet, err := net.ParseCIDR(url)
	if err == nil {
		// ip cidr 应用
		return IpCidrMatcher, ipNet
	}
	if strings.HasPrefix(url, "*.") {
		// 泛域名
		return DomainWildcardMatcher, nil
	}
	// 域名
	return DomainMatcher, nil
}

// BaseMatcher 装饰器,定义matcher中公共的字段和逻辑
type BaseMatcher struct {
	AppInfo  *pb.AppInfo
	UrlRule  string
	PortRule []PortMatcher
}

func (b BaseMatcher) CommonMatchPort(port int) (res bool, noMatchDetail string) {
	var noMatchDetails []string
	for _, matcher := range b.PortRule {
		res, d := matcher.DoMatcher(port)
		if res {
			return res, ""
		}
		noMatchDetails = append(noMatchDetails, d)
	}
	return false, fmt.Sprintf("port not match:%v,details:%v", res, noMatchDetails)
}

func (b BaseMatcher) NoMatchError(srcUrl string, srcPort int, detail string) error {
	return errors.New(fmt.Sprintf("match error,srcUrl:%v,srcPort:%v,urlRule:%v,detail:%v",
		srcUrl, srcPort, b.UrlRule, detail))
}
