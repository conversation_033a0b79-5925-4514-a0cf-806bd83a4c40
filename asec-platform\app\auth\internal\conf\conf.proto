syntax = "proto3";
package kratos.api;

option go_package = "asdsec.com/asec/platform/app/auth/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  bool primary = 3;
}

message Data {
  message Database {
    string driver = 1;
    string dsn = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    string password = 3;
    int32 db = 4;
    google.protobuf.Duration dial_timeout = 5;
    google.protobuf.Duration read_timeout = 6;
    google.protobuf.Duration write_timeout = 7;
  }
  Database database = 1;
  Redis redis = 2;
  string ip2region_type =3;
  string aiwen_key=4;
}
