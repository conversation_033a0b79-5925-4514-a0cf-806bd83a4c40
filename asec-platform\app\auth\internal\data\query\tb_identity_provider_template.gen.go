// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"gorm.io/plugin/dbresolver"
)

func newTbIdentityProviderTemplate(db *gorm.DB, opts ...gen.DOOption) tbIdentityProviderTemplate {
	_tbIdentityProviderTemplate := tbIdentityProviderTemplate{}

	_tbIdentityProviderTemplate.tbIdentityProviderTemplateDo.UseDB(db, opts...)
	_tbIdentityProviderTemplate.tbIdentityProviderTemplateDo.UseModel(&model.TbIdentityProviderTemplate{})

	tableName := _tbIdentityProviderTemplate.tbIdentityProviderTemplateDo.TableName()
	_tbIdentityProviderTemplate.ALL = field.NewAsterisk(tableName)
	_tbIdentityProviderTemplate.ID = field.NewString(tableName, "id")
	_tbIdentityProviderTemplate.Name = field.NewString(tableName, "name")
	_tbIdentityProviderTemplate.Type = field.NewString(tableName, "type")
	_tbIdentityProviderTemplate.TemplateType = field.NewString(tableName, "template_type")
	_tbIdentityProviderTemplate.CorpID = field.NewString(tableName, "corp_id")
	_tbIdentityProviderTemplate.Avatar = field.NewString(tableName, "avatar")
	_tbIdentityProviderTemplate.Description = field.NewString(tableName, "description")
	_tbIdentityProviderTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_tbIdentityProviderTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbIdentityProviderTemplate.SourceID = field.NewString(tableName, "source_id")

	_tbIdentityProviderTemplate.fillFieldMap()

	return _tbIdentityProviderTemplate
}

type tbIdentityProviderTemplate struct {
	tbIdentityProviderTemplateDo tbIdentityProviderTemplateDo

	ALL          field.Asterisk
	ID           field.String
	Name         field.String
	Type         field.String
	TemplateType field.String
	CorpID       field.String
	Avatar       field.String
	Description  field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time
	SourceID     field.String

	fieldMap map[string]field.Expr
}

func (t tbIdentityProviderTemplate) Table(newTableName string) *tbIdentityProviderTemplate {
	t.tbIdentityProviderTemplateDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbIdentityProviderTemplate) As(alias string) *tbIdentityProviderTemplate {
	t.tbIdentityProviderTemplateDo.DO = *(t.tbIdentityProviderTemplateDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbIdentityProviderTemplate) updateTableName(table string) *tbIdentityProviderTemplate {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.Type = field.NewString(table, "type")
	t.TemplateType = field.NewString(table, "template_type")
	t.CorpID = field.NewString(table, "corp_id")
	t.Avatar = field.NewString(table, "avatar")
	t.Description = field.NewString(table, "description")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.SourceID = field.NewString(table, "source_id")

	t.fillFieldMap()

	return t
}

func (t *tbIdentityProviderTemplate) WithContext(ctx context.Context) *tbIdentityProviderTemplateDo {
	return t.tbIdentityProviderTemplateDo.WithContext(ctx)
}

func (t tbIdentityProviderTemplate) TableName() string {
	return t.tbIdentityProviderTemplateDo.TableName()
}

func (t tbIdentityProviderTemplate) Alias() string { return t.tbIdentityProviderTemplateDo.Alias() }

func (t tbIdentityProviderTemplate) Columns(cols ...field.Expr) gen.Columns {
	return t.tbIdentityProviderTemplateDo.Columns(cols...)
}

func (t *tbIdentityProviderTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbIdentityProviderTemplate) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["type"] = t.Type
	t.fieldMap["template_type"] = t.TemplateType
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["avatar"] = t.Avatar
	t.fieldMap["description"] = t.Description
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["source_id"] = t.SourceID
}

func (t tbIdentityProviderTemplate) clone(db *gorm.DB) tbIdentityProviderTemplate {
	t.tbIdentityProviderTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbIdentityProviderTemplate) replaceDB(db *gorm.DB) tbIdentityProviderTemplate {
	t.tbIdentityProviderTemplateDo.ReplaceDB(db)
	return t
}

type tbIdentityProviderTemplateDo struct{ gen.DO }

func (t tbIdentityProviderTemplateDo) Debug() *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Debug())
}

func (t tbIdentityProviderTemplateDo) WithContext(ctx context.Context) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbIdentityProviderTemplateDo) ReadDB() *tbIdentityProviderTemplateDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbIdentityProviderTemplateDo) WriteDB() *tbIdentityProviderTemplateDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbIdentityProviderTemplateDo) Session(config *gorm.Session) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbIdentityProviderTemplateDo) Clauses(conds ...clause.Expression) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbIdentityProviderTemplateDo) Returning(value interface{}, columns ...string) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbIdentityProviderTemplateDo) Not(conds ...gen.Condition) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbIdentityProviderTemplateDo) Or(conds ...gen.Condition) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbIdentityProviderTemplateDo) Select(conds ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbIdentityProviderTemplateDo) Where(conds ...gen.Condition) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbIdentityProviderTemplateDo) Order(conds ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbIdentityProviderTemplateDo) Distinct(cols ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbIdentityProviderTemplateDo) Omit(cols ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbIdentityProviderTemplateDo) Join(table schema.Tabler, on ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbIdentityProviderTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbIdentityProviderTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbIdentityProviderTemplateDo) Group(cols ...field.Expr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbIdentityProviderTemplateDo) Having(conds ...gen.Condition) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbIdentityProviderTemplateDo) Limit(limit int) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbIdentityProviderTemplateDo) Offset(offset int) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbIdentityProviderTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbIdentityProviderTemplateDo) Unscoped() *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbIdentityProviderTemplateDo) Create(values ...*model.TbIdentityProviderTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbIdentityProviderTemplateDo) CreateInBatches(values []*model.TbIdentityProviderTemplate, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbIdentityProviderTemplateDo) Save(values ...*model.TbIdentityProviderTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbIdentityProviderTemplateDo) First() (*model.TbIdentityProviderTemplate, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderTemplate), nil
	}
}

func (t tbIdentityProviderTemplateDo) Take() (*model.TbIdentityProviderTemplate, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderTemplate), nil
	}
}

func (t tbIdentityProviderTemplateDo) Last() (*model.TbIdentityProviderTemplate, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderTemplate), nil
	}
}

func (t tbIdentityProviderTemplateDo) Find() ([]*model.TbIdentityProviderTemplate, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbIdentityProviderTemplate), err
}

func (t tbIdentityProviderTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbIdentityProviderTemplate, err error) {
	buf := make([]*model.TbIdentityProviderTemplate, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbIdentityProviderTemplateDo) FindInBatches(result *[]*model.TbIdentityProviderTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbIdentityProviderTemplateDo) Attrs(attrs ...field.AssignExpr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbIdentityProviderTemplateDo) Assign(attrs ...field.AssignExpr) *tbIdentityProviderTemplateDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbIdentityProviderTemplateDo) Joins(fields ...field.RelationField) *tbIdentityProviderTemplateDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbIdentityProviderTemplateDo) Preload(fields ...field.RelationField) *tbIdentityProviderTemplateDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbIdentityProviderTemplateDo) FirstOrInit() (*model.TbIdentityProviderTemplate, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderTemplate), nil
	}
}

func (t tbIdentityProviderTemplateDo) FirstOrCreate() (*model.TbIdentityProviderTemplate, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderTemplate), nil
	}
}

func (t tbIdentityProviderTemplateDo) FindByPage(offset int, limit int) (result []*model.TbIdentityProviderTemplate, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbIdentityProviderTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbIdentityProviderTemplateDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbIdentityProviderTemplateDo) Delete(models ...*model.TbIdentityProviderTemplate) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbIdentityProviderTemplateDo) withDO(do gen.Dao) *tbIdentityProviderTemplateDo {
	t.DO = *do.(*gen.DO)
	return t
}
