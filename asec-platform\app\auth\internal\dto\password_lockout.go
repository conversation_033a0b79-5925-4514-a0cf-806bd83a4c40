package dto

import "time"

// AccountPolicyPasswordLockout 账户策略中的密码锁定配置
type AccountPolicyPasswordLockout struct {
	Enabled               bool  `json:"enabled"`                  // 是否启用密码错误次数限制
	MaxFailureCount       int32 `json:"max_failure_count"`        // 连续密码错误最大次数
	LockoutDurationSec    int32 `json:"lockout_duration_sec"`     // 锁定时长（秒）
	ResetFailureWindowSec int32 `json:"reset_failure_window_sec"` // 密码错误次数重置窗口（秒）
}

// PasswordFailureInfo 密码失败信息
type PasswordFailureInfo struct {
	Count           int       `json:"count"`        // 连续失败次数
	LastFailureTime time.Time `json:"last_failure"` // 最后失败时间
}

// AccountLockInfo 账户锁定信息
type AccountLockInfo struct {
	LockedUntil time.Time `json:"locked_until"` // 锁定到期时间
	Reason      string    `json:"reason"`       // 锁定原因
}

// UserLockStatus 用户锁定状态
type UserLockStatus struct {
	IsLocked        bool      `json:"is_locked"`      // 是否被锁定
	FailureCount    int       `json:"failure_count"`  // 密码失败次数
	LastFailureTime time.Time `json:"last_failure"`   // 最后失败时间
	LockedUntil     time.Time `json:"locked_until"`   // 锁定到期时间
	RemainingTime   int64     `json:"remaining_time"` // 剩余锁定时间（秒）
}

// LockedAccountInfo 锁定账户信息
type LockedAccountInfo struct {
	UserID      string    `json:"user_id"`      // 用户ID
	Username    string    `json:"username"`     // 用户名
	GroupID     string    `json:"group_id"`     // 所属组ID
	GroupName   string    `json:"group_name"`   // 所属组名
	LockedAt    time.Time `json:"locked_at"`    // 锁定时间
	LockedUntil time.Time `json:"locked_until"` // 锁定到期时间
	LockReason  string    `json:"lock_reason"`  // 锁定原因
	LockType    string    `json:"lock_type"`    // 锁定类型 (password_policy|manual)
}

// AccountPolicyIPLockout 账户策略中的IP锁定配置
type AccountPolicyIPLockout struct {
	Enabled               bool  `json:"enabled"`                  // 是否启用IP错误次数限制
	MaxFailureCount       int32 `json:"max_failure_count"`        // 同IP连续错误最大次数
	LockoutDurationSec    int32 `json:"lockout_duration_sec"`     // IP锁定时长（秒）
	ResetFailureWindowSec int32 `json:"reset_failure_window_sec"` // IP错误次数重置窗口（秒）
}

// IPFailureInfo IP失败信息
type IPFailureInfo struct {
	Count           int       `json:"count"`        // 连续失败次数
	LastFailureTime time.Time `json:"last_failure"` // 最后失败时间
}

// IPLockInfo IP锁定信息
type IPLockInfo struct {
	LockedUntil        time.Time `json:"locked_until"`        // 锁定到期时间
	Reason             string    `json:"reason"`              // 锁定原因
	IsLocked           bool      `json:"is_locked"`           // 是否被锁定
	FailureCount       int       `json:"failure_count"`       // 当前失败次数
	LastFailureTime    time.Time `json:"last_failure_time"`   // 最后一次失败时间
	RemainingSeconds   int32     `json:"remaining_seconds"`   // 剩余锁定秒数
	IPAddress          string    `json:"ip_address"`          // IP地址
}

// Redis Key 生成函数

// PasswordFailureKey 生成密码失败次数的Redis key
func PasswordFailureKey(userID string) string {
	return "password_failure_count:" + userID
}

// AccountLockKey 生成账户锁定的Redis key
func AccountLockKey(userID string) string {
	return "account_locked:" + userID
}

// IPFailureKey 生成IP失败次数的Redis key
func IPFailureKey(ip string) string {
	return "ip_failure_count:" + ip
}

// IPLockKey 生成IP锁定的Redis key
func IPLockKey(ip string) string {
	return "ip_locked:" + ip
}

// LockedIPInfo 锁定IP信息（用于管理界面显示）
type LockedIPInfo struct {
	IPAddress     string     `json:"ip_address"`     // IP地址
	CorpID        string     `json:"corp_id"`        // 所属企业ID
	CorpName      string     `json:"corp_name"`      // 所属企业名称
	LockInfo      *IPLockInfo `json:"lock_info"`     // 锁定信息
	Location      string     `json:"location"`       // IP地理位置（可选）
	TotalAttempts int        `json:"total_attempts"` // 总尝试次数
}
