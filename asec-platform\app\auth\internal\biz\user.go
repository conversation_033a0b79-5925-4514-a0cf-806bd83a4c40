package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/idp"
	"asdsec.com/asec/platform/app/auth/internal/idp/infogo"
	"asdsec.com/asec/platform/app/auth/internal/idp/paila"
	"asdsec.com/asec/platform/app/auth/internal/idp/qiyewx"
	"asdsec.com/asec/platform/app/auth/internal/idp/zhezhengding"
	"github.com/go-kratos/kratos/v2/errors"
	"gorm.io/gorm"

	"asdsec.com/asec/platform/app/auth/internal/idp/ad"

	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/idp/dingtalk"
	"asdsec.com/asec/platform/app/auth/internal/idp/feishu"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/jinzhu/copier"

	"github.com/google/uuid"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"github.com/go-kratos/kratos/v2/log"
)

type UserRepo interface {
	CreateUser(ctx context.Context, param dto.CreateUserDaoParam) error
	CreateUserCustom(ctx context.Context, param dto.CreateUserDaoParam) error
	BatchInsertUser(ctx context.Context, users []*dto.CreateUserDaoParam) error
	UpdateUser(ctx context.Context, param dto.CreateUserDaoParam, roleIds []string, pwd string, enable bool) error
	TotpUnbind(ctx context.Context, userId []string) error
	UpdateUserRow(ctx context.Context, id string, updateInfo model.TbUserEntity) error
	DeleteUser(ctx context.Context, corpId, id, name string) error
	DeleteUsersOfRoot(ctx context.Context, corpId, rootGroupId string) error
	CreateCredential(ctx context.Context, corpId, id, userId, credType, secretData, credData string) error

	BatchInsertExternalDepts(ctx context.Context, depts []*dto.ExternalDepartment) error
	BatchInsertExternalUsers(ctx context.Context, users []*dto.ExternalUser) error

	DeleteExternalDeptsOfRoot(ctx context.Context, rootGroupId string) error
	DeleteExternalUserOfRoot(ctx context.Context, rootGroupId string) error
	GetIdleTime(ctx context.Context) (*model.TbSpecialConfig, error)
	ListExternalDeps(ctx context.Context, rootGroupId string) ([]*model.TbExternalDepartment, error)
	ListExternalUsers(ctx context.Context, rootGroupId string) ([]*model.TbExternalUser, error)
	GetExternalUser(ctx context.Context, rootGroupId, userId, authType string) (*model.TbExternalUser, error)
	GetExternalUserByLocalUserId(ctx context.Context, localUserId string) (*model.TbExternalUser, error)
	QueryUserByPhoneInSource(ctx context.Context, corpId, phone, sourceId string) (*model.TbUserEntity, error)
	QueryUserByEmailInSource(ctx context.Context, corpId, email, sourceId string) (*model.TbUserEntity, error)
	QueryUserByNameInSource(ctx context.Context, corpId, sourceId, name string) (*model.TbUserEntity, error)
	QueryUserByIdInSource(ctx context.Context, corpId, sourceId, name, id string) (*model.TbUserEntity, error)
	QueryUserByTrueNameInSource(ctx context.Context, corpId, sourceId, trueName string) (*model.TbUserEntity, error)
	QueryUserByFieldMap(ctx context.Context, corpId, sourceId string, fieldMap []dto.KV) ([]*model.TbUserEntity, error)
	QueryUsersRoles(ctx context.Context, corpId string, userIds []string) ([]dto.RoleInfo, error)
	ListUser(ctx context.Context, corpId string) ([]*model.TbUserEntity, error)
	ListUserOfGroups(ctx context.Context, corpId string, groupIds []string, limit, offset int, search string) ([]dto.ListUserRsp, error)
	ListUserOfIdle(ctx context.Context, corpId string, limit, offset int, search string) ([]dto.ListUserRsp, error)
	UpdateIdleTime(ctx context.Context, idleTime string) error
	UpdateLockStatus(ctx context.Context, corpId, userId string, lockStatus bool) error
	CountUserOfIdle(ctx context.Context, corpId string, search string) (int64, error)
	CountUserOfGroups(ctx context.Context, corpId string, groupIds []string, search string) (int64, error)
	GetGroupOfUsers(ctx context.Context, corpId string, ids []string) ([]string, error)
	ListUserBindInfo(ctx context.Context, corpId string, userIds []string) ([]dto.BindUserInfo, error)
	UpdateCredential(ctx context.Context, id, secretData, credData string) error
	QueryCredential(ctx context.Context, corpId, userId, credType string) (*model.TbCredential, error)
	QueryUserWithGroupInfo(ctx context.Context, corpId, userId string) (dto.User, error)
	QueryUserWithGroupAndSource(ctx context.Context, corpId, userId string) (dto.User, error)
	QueryUserEntity(ctx context.Context, corpId, userId string) (*model.TbUserEntity, error)
	BatchInsertExternalInfoTx(data dto.SyncDeptAndUser) error
	QueryUserByName(ctx context.Context, corpId, name string) (*model.TbUserEntity, error)
	GetExternalUserByUserId(ctx context.Context, userId string) (*model.TbExternalUser, error)
	QueryUserByNameAndGroup(ctx context.Context, corpId, name, groupId string) (*model.TbUserEntity, error)

	UpdateExternalUser(ctx context.Context, user model.TbExternalUser) error
	UpdateUserBasicInfo(ctx context.Context, param dto.CreateUserDaoParam) error
}

const batchSize = 500

type UserUsecase struct {
	role       RoleRepo
	repo       UserRepo
	ugRepo     UserGroupRepo
	userSource UserSourceRepo
	log        *log.Helper
}

func NewUserUsecase(repo UserRepo, ugRepo UserGroupRepo, role RoleRepo, userSource UserSourceRepo, logger log.Logger) *UserUsecase {
	return &UserUsecase{
		repo:       repo,
		ugRepo:     ugRepo,
		role:       role,
		userSource: userSource,
		log:        log.NewHelper(logger),
	}
}

func (u UserUsecase) CreateUserCustom(ctx context.Context, param dto.CreateUserParam) error {
	group, err := u.ugRepo.GetUserGroup(ctx, param.CorpId, param.GroupId)
	if err != nil {
		u.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}

	_, err = u.repo.QueryUserByNameInSource(ctx, param.CorpId, group.SourceID, param.Name)
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("QueryUserByNameInSource failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorUserEntityUniqueConflict("user name=%v unique in sourceId=%v conflict", param.Name, group.SourceID)
	}

	var daoParam dto.CreateUserDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}
	daoParam.Enable = &param.Enable
	daoParam.SourceId = group.SourceID
	id := uuid.New().String()
	daoParam.Id = id
	daoParam.RootGroupID = group.RootGroupID
	if err := u.repo.CreateUser(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUser failed. err=%v", err)
		return err
	}
	if len(param.RoleIds) != 0 { // 关联角色 也没有放到一个事务
		if err := u.role.CreateUserRolesMap(ctx, param.CorpId, id, param.RoleIds); err != nil {
			u.log.Errorf("CreateUserRolesMap failed. err=%v", err)
			return err
		}
	}
	return nil
}

func (u UserUsecase) DeleteUserCustom(ctx context.Context, corpId string, userName string) error {
	user, err := u.repo.QueryUserByName(ctx, corpId, userName)
	if err != nil {
		return err
	}
	return u.DeleteUser(ctx, corpId, user.ID, "")
}

func (u UserUsecase) UpdateUserCustom(ctx context.Context, param dto.UpdateUserParam) error {
	user, err := u.repo.QueryUserByName(ctx, param.CorpId, param.Name)
	if err != nil {
		return err
	}
	param.Id = user.ID
	return u.UpdateUser(ctx, param)
}

func (u UserUsecase) TotpUnbind(ctx context.Context, userId []string) error {
	err := u.repo.TotpUnbind(ctx, userId)
	if err != nil {
		return err
	}
	return nil
}

func (u UserUsecase) CreateUser(ctx context.Context, param dto.CreateUserParam) error {
	group, err := u.ugRepo.GetUserGroup(ctx, param.CorpId, param.GroupId)
	if err != nil {
		u.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}

	_, err = u.repo.QueryUserByNameInSource(ctx, param.CorpId, group.SourceID, param.Name)
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("QueryUserByNameInSource failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorUserEntityUniqueConflict("user name=%v unique in sourceId=%v conflict", param.Name, group.SourceID)
	}

	var daoParam dto.CreateUserDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}
	daoParam.Enable = &param.Enable
	daoParam.SourceId = group.SourceID
	id := uuid.New().String()
	daoParam.Id = id
	daoParam.RootGroupID = group.RootGroupID
	daoParam.AuthType = "password"
	if err := u.repo.CreateUser(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUser failed. err=%v", err)
		return err
	}
	if param.Password != "" { // 这里没有把新增用户密码和新增用户放到一个事务中
		credParam := dto.CreatCredParam{
			CorpId:   param.CorpId,
			UserId:   id,
			CredType: dto.CredTypePassword,
			Password: param.Password,
		}
		if err := u.createCredential(ctx, credParam); err != nil {
			u.log.Errorf("createCredential failed. err=%v, param=%+v", err, credParam)
			return err
		}
	}
	if len(param.RoleIds) != 0 { // 关联角色 也没有放到一个事务
		if err := u.role.CreateUserRolesMap(ctx, param.CorpId, id, param.RoleIds); err != nil {
			u.log.Errorf("CreateUserRolesMap failed. err=%v", err)
			return err
		}
	}
	return nil
}

func (u UserUsecase) UpdateUser(ctx context.Context, param dto.UpdateUserParam) error {
	group, err := u.ugRepo.GetUserGroup(ctx, param.CorpId, param.GroupId)
	if err != nil {
		u.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}
	user, err := u.repo.QueryUserEntity(ctx, param.CorpId, param.Id)
	if err != nil {
		u.log.Errorf("QueryUserById failed. err=%v", err)
		return err
	}

	var daoParam dto.CreateUserDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}
	//页面提交的手机号脱敏展示，提交过来会覆盖数据库未脱敏数据，做一下判断，如果包含*，则继续使用原有手机号
	if strings.ContainsAny(param.Phone, "*") {
		daoParam.Phone = user.Phone
	}
	daoParam.SourceId = group.SourceID
	daoParam.Name = user.Name
	if err := u.repo.UpdateUser(ctx, daoParam, param.RoleIds, param.Password, param.Enable); err != nil {
		u.log.Errorf("CreateUser failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserUsecase) UpdateIdleTime(ctx context.Context, idleTime string) error {
	err := u.repo.UpdateIdleTime(ctx, idleTime)
	return err
}

func (u UserUsecase) UpdateLockStatus(ctx context.Context, corpId string, userId string, lockStatus bool) error {
	err := u.repo.UpdateLockStatus(ctx, corpId, userId, lockStatus)
	return err
}

func (u UserUsecase) ListIdleUser(ctx context.Context, corpId string, limit, offset int, search string) (dto.ListUserResult, error) {
	// 获取租户下所有组
	groups, err := u.ugRepo.ListUserGroup(ctx, corpId)
	if err != nil {
		u.log.Errorf("ListUserGroup failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	users, err := u.repo.ListUserOfIdle(ctx, corpId, limit, offset, search)
	if err != nil {
		u.log.Errorf("ListUser failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	treeHandler := common.RootToNodeSimpleTree{}
	err = treeHandler.Build(groups)
	if err != nil {
		u.log.Errorf("New failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	// 整理映射结构
	var uids []string
	for i, user := range users {
		users[i].Phone = common.MaskPhone(user.Phone)
		uids = append(uids, user.ID)
	}
	usersRoles, err := u.repo.QueryUsersRoles(ctx, corpId, uids)
	if err != nil {
		u.log.Errorf("QueryUsersRoles failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	userToRoles := make(map[string][]dto.RoleInfo)
	for _, ur := range usersRoles {
		userToRoles[ur.UserID] = append(userToRoles[ur.UserID], ur)
	}
	groupNodeMap := common.BuildGroupNodeMap(groups)
	var userToGroupPath = make(map[string]string)
	for _, user := range users {
		groupPath := ""
		groupPathIds := treeHandler.GetRootToNodePath(dto.FakeRootUserGroupId, user.GroupID)
		for _, id := range groupPathIds {
			if id == dto.FakeRootUserGroupId {
				continue
			}
			groupPath = groupPath + "/" + groupNodeMap[id].Name
		}
		userToGroupPath[user.ID] = groupPath
	}

	// 整理返回数据
	var result []dto.User
	err = copier.Copy(&result, &users)
	if err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	for id, user := range result {
		result[id].Roles = userToRoles[user.ID]
		result[id].GroupName = userToGroupPath[user.ID]
	}
	count, err := u.repo.CountUserOfIdle(ctx, corpId, search)
	if err != nil {
		return dto.ListUserResult{}, nil
	}
	idleTime, err := u.repo.GetIdleTime(ctx)
	if err != nil {
		return dto.ListUserResult{}, nil
	}
	return dto.ListUserResult{Users: result, Count: count, IdleTime: idleTime.Value}, nil
}

func (u UserUsecase) ListUser(ctx context.Context, corpId, groupId string, limit, offset int, search string) (dto.ListUserResult, error) {
	// 获取租户下所有组
	groups, err := u.ugRepo.ListUserGroup(ctx, corpId)
	if err != nil {
		u.log.Errorf("ListUserGroup failed. err=%v", err)
		return dto.ListUserResult{}, err
	}

	// 获取某组下的所有用户
	treeHandler := common.RootToNodeSimpleTree{}
	err = treeHandler.Build(groups)
	if err != nil {
		u.log.Errorf("New failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	groupIds := treeHandler.GetAllSubNode(groupId)
	searchStr := ""
	if search != "" {
		searchStr = "%" + strings.ToLower(search) + "%"
	}
	users, err := u.repo.ListUserOfGroups(ctx, corpId, groupIds, limit, offset, searchStr)
	if err != nil {
		u.log.Errorf("ListUser failed. err=%v", err)
		return dto.ListUserResult{}, err
	}

	// 整理映射结构
	var uids []string
	for i, user := range users {
		users[i].Phone = common.MaskPhone(user.Phone)
		uids = append(uids, user.ID)
	}
	groupNodeMap := common.BuildGroupNodeMap(groups)
	var userToGroupPath = make(map[string]string)
	for _, user := range users {
		groupPath := ""
		groupPathIds := treeHandler.GetRootToNodePath(dto.FakeRootUserGroupId, user.GroupID)
		for _, id := range groupPathIds {
			if id == dto.FakeRootUserGroupId {
				continue
			}
			groupPath = groupPath + "/" + groupNodeMap[id].Name
		}
		userToGroupPath[user.ID] = groupPath
	}
	usersRoles, err := u.repo.QueryUsersRoles(ctx, corpId, uids)
	if err != nil {
		u.log.Errorf("QueryUsersRoles failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	userToRoles := make(map[string][]dto.RoleInfo)
	for _, ur := range usersRoles {
		userToRoles[ur.UserID] = append(userToRoles[ur.UserID], ur)
	}
	// 整理返回数据
	var result []dto.User
	err = copier.Copy(&result, &users)
	if err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return dto.ListUserResult{}, err
	}
	for id, user := range result {
		result[id].Roles = userToRoles[user.ID]
		result[id].GroupName = userToGroupPath[user.ID]
	}

	count, err := u.repo.CountUserOfGroups(ctx, corpId, groupIds, searchStr)
	if err != nil {
		return dto.ListUserResult{}, nil
	}
	return dto.ListUserResult{Users: result, Count: count}, nil
}

func (u UserUsecase) createCredential(ctx context.Context, param dto.CreatCredParam) error {
	switch param.CredType {
	case dto.CredTypePassword:
		manager := common.NewPbkdf2SaltCredManager()
		credential, err := manager.NewCredential(param.Password)
		if err != nil {
			u.log.Errorf("NewCredential failed. err=%v", err)
			return err
		}

		id := uuid.New()
		if err := u.repo.CreateCredential(ctx, param.CorpId, id.String(), param.UserId, string(param.CredType), credential.SecretDataStr, credential.CredDataStr); err != nil {
			u.log.Errorf("createCredential failed. err=%v", err)
			return err
		}
	default:
		return pb.ErrorCredError("credType=%v not support", param.CredType)
	}
	return nil
}

func (u UserUsecase) UpdateCredential(ctx context.Context, param dto.UpdateCredParam) error {
	switch param.CredType {
	case dto.CredTypePassword:
		// 校验旧密码
		cred, err := u.repo.QueryCredential(ctx, param.CorpId, param.UserId, string(dto.CredTypePassword))
		if err != nil {
			u.log.Errorf("QueryCredential falied. err=%v", err)
			return err
		}
		var credData common.CredData
		err = json.Unmarshal([]byte(cred.CredentialData), &credData)
		if err != nil {
			u.log.Errorf("Unmarshal failed. err=%v", err)
			return err
		}
		manager := common.NewPbkdf2SaltCredManager()
		if !manager.IsPasswordCorrect(param.Password, cred.SecretData, credData) {
			return pb.ErrorPassWordNotCorrect("password not correct")
		}

		// 存储新密码
		newCred, err := manager.NewCredential(param.NewPassword)
		if err != nil {
			u.log.Errorf("NewCredential failed. err=%v", err)
			return err
		}
		if err := u.repo.UpdateCredential(ctx, cred.ID, newCred.SecretDataStr, newCred.CredDataStr); err != nil {
			u.log.Errorf("UpdateCredential failed. err=%v", err)
			return err
		}
	default:
		return pb.ErrorCredError("credType=%v not support", param.CredType)
	}
	return nil
}

func (u UserUsecase) GetLoginUserInfo(ctx context.Context, corpId, userId string) (dto.User, error) {
	user, err := u.repo.QueryUserWithGroupAndSource(ctx, corpId, userId)
	if err != nil {
		u.log.Errorf("QueryUserWithGroupInfo failed. err=%v", err)
		return dto.User{}, err
	}
	userRoles, err := u.repo.QueryUsersRoles(ctx, corpId, []string{user.ID})
	if err != nil {
		u.log.Errorf("QueryUsersRoles failed. err=%v", err)
		return dto.User{}, err
	}
	user.Roles = append(user.Roles, userRoles...)
	return user, nil
}

func (u UserUsecase) DeleteUser(ctx context.Context, corpId, userId, name string) error {
	if err := u.repo.DeleteUser(ctx, corpId, userId, name); err != nil {
		u.log.Errorf("DeleteUser failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserUsecase) SyncUser(ctx context.Context, corpId, rootGroupId string, syncType dto.SyncType) (err error) {
	// 判断是否running
	taskCount, syncErr := u.ugRepo.GetRunningRootGroupSync(ctx, rootGroupId)
	if syncErr != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		u.log.Errorf("GetRootGroupSync failed. err=%v", syncErr)
		return
	}
	if taskCount > 0 {
		u.log.Warnf("RootGroupSyncTask running. root_group_id=%v", rootGroupId)
		return
	}
	// 加一条task进入数据库
	lastSyncTime := time.Now().UTC()
	if syncErr := u.ugRepo.UpdateRootGroupSync(ctx, rootGroupId, lastSyncTime, lastSyncTime, string(dto.SyncRunning)); syncErr != nil {
		u.log.Errorf("UpdateRootGroupSync failed. err=%v", syncErr)
		return
	}
	// 记录日志
	defer func() {
		if err == nil {
			if syncErr := u.syncLog(ctx, corpId, rootGroupId, syncType, dto.SyncSuccess, dto.SyncSuccessInfo); syncErr != nil {
				u.log.Warnf("syncLog failed. err=%v", err)
			}
		} else {
			err := errors.FromError(err)
			if syncErr := u.syncLog(ctx, corpId, rootGroupId, syncType, dto.SyncFailed, fmt.Sprintf("%v。原因：%s", dto.SyncFailedInfo, err.Message)); syncErr != nil {
				u.log.Warnf("syncLog failed. err=%v", err)
			}
		}
	}()
	// 提前计算下一次执行时间,比较时间产生较大的偏移
	sync, syncErr := u.ugRepo.GetRootGroupSync(ctx, rootGroupId)
	if syncErr != nil {
		u.log.Errorf("GetRootGroupSync failed. err=%v", syncErr)
		return
	}
	//lastSyncTime := time.Now().UTC()
	nextSyncTime, syncErr := common.GetNextTime(sync.SyncCycle, dto.SyncUnit(sync.SyncUnit))
	if syncErr != nil {
		u.log.Errorf("GetNextTime failed. err=%v", err)
		return
	}
	// 更新状态
	defer func() {
		if err == nil { // 同步成功
			// 更新状态
			if syncErr := u.ugRepo.UpdateRootGroupSync(ctx, rootGroupId, lastSyncTime, nextSyncTime, string(dto.SyncSuccess)); syncErr != nil {
				u.log.Errorf("UpdateRootGroupSync failed. err=%v", syncErr)
				return
			}
		} else { // 同步失败
			// 更新状态
			if syncErr := u.ugRepo.UpdateRootGroupSync(ctx, rootGroupId, lastSyncTime, nextSyncTime, string(dto.SyncFailed)); syncErr != nil {
				u.log.Errorf("UpdateRootGroupSync failed. err=%v", syncErr)
				return
			}
		}
	}()

	srcType, err := u.ugRepo.GetRootGroupSourceType(ctx, corpId, rootGroupId)
	if err != nil {
		u.log.Errorf("GetRootGroupSourceType failed. err=%v", err)
		return err
	}

	return u.SyncExternalUser(ctx, corpId, rootGroupId, srcType)
}

func (u UserUsecase) syncLog(ctx context.Context, corpId, groupId string, syncType dto.SyncType, status dto.SyncStatus, syncInfo string) error {
	operator, ignoreErr := common.GetUserName(ctx)
	if ignoreErr != nil {
		u.log.Warnf("operator not found. err=%v", ignoreErr)
	}
	if operator == "" {
		operator = dto.SyncOperatorSysDefault
		if syncType == dto.SyncTypeCustom {
			operator = dto.SyncOperatorAdminDefault
		}
	}
	var param = dto.CreateSyncLogParam{
		CorpID:     corpId,
		GroupID:    groupId,
		Type:       syncType,
		SyncStatus: status,
		SyncInfo:   syncInfo,
		Operator:   operator,
	}
	if err := u.ugRepo.CreateSyncLog(ctx, param); err != nil {
		u.log.Errorf("CreateSyncLog failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserUsecase) RootGroupSync(ctx context.Context) {
	rootSyncs, err := u.ugRepo.ListAllRootGroupWithSync(ctx)
	if err != nil {
		u.log.Errorf("ListAllRootGroupWithSync failed. err=%v", err)
		return
	}

	for _, sync := range rootSyncs {
		if !sync.AutoSync {
			continue
		}
		now := time.Now().UTC()
		if sync.NextSyncTime.UTC().Before(now) {
			if err := u.SyncUser(ctx, sync.CorpID, sync.GroupID, dto.SyncTypeAuto); err != nil {
				u.log.Errorf("SyncWxUser failed. err=%v", err)
				continue
			}
		}
	}
}

// SyncExternalUser 同步外部用户
func (u UserUsecase) SyncExternalUser(ctx context.Context, corpId, rootGroupId, sourceType string) error {
	// 获取用户源信息，以便获取模板类型
	userSource, err := u.userSource.GetUserSourceByType(ctx, corpId, sourceType, "")
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("GetUserSourceByType failed. err=%v", err)
		return err
	}
	rootGroup, err := u.ugRepo.GetUserGroup(ctx, corpId, rootGroupId)
	if err != nil {
		u.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}
	// 获取组同步配置
	conf, err := u.ugRepo.GetGroupSyncConfig(ctx, corpId, rootGroupId)
	if err != nil {
		u.log.Errorf("GetGroupSyncConfig failed. err=%v", err)
		return err
	}

	var externalInfo dto.ExternalInfo
	extConf := dto.KVsToIdpAttr(conf)
	switch dto.UserSourceType(sourceType) {
	case dto.UserSourceQiYeWx:
		agentId, err := strconv.ParseInt(extConf.AgentId, 10, 64)
		if err != nil {
			u.log.Errorf("ParseInt failed. err=%v", err)
			return pb.ErrorQiyewxConfigError("agentId=%v config error", extConf.AgentId)
		}
		client := qiyewx.NewWeComClient(extConf.CorpId, extConf.Secret, int(agentId))
		// 获取微信信息
		externalInfo, err = u.getWxInfo(client, extConf.CorpId, extConf.Secret, int(agentId))
		if err != nil {
			u.log.Errorf("getWxInfo failed. err=%v", err)
			return err
		}
	case dto.UserSourceFeiShu:
		client := feishu.NewLarkClient(extConf.AppId, extConf.FeishuConfig.AppSecret)
		// 获取飞书信息
		externalInfo, err = u.getFeishuInfo(client, rootGroupId)
		if err != nil {
			u.log.Errorf("getFsInfo failed. err=%v", err)
			return err
		}
	case dto.UserSourceDingtalk:
		client := dingtalk.NewLarkClient(extConf.DingtalkConfig.AppKey, extConf.DingtalkConfig.AppSecret)
		// 获取飞书信息
		externalInfo, err = u.getDingtalkInfo(client, rootGroupId)
		if err != nil {
			u.log.Errorf("getFsInfo failed. err=%v", err)
			return err
		}
	case dto.UserSourceAD, dto.UserSourceLdap:
		adConf := ad.ActiveDirectory{
			Server:      extConf.IdpConfig.ServerAddress,
			DN:          extConf.IdpConfig.SearchEntry,
			Pwd:         extConf.IdpConfig.AdministratorPassword,
			UserName:    extConf.IdpConfig.AdministratorAccount,
			UserFilter:  extConf.IdpConfig.UserFilter,
			GroupFilter: extConf.IdpConfig.GroupFilter,
			FieldMap:    extConf.FieldMap,
		}
		externalInfo, err = u.getAdInfo(adConf, rootGroupId, sourceType)
		if err != nil {
			u.log.Errorf("getAdInfo failed. err=%v", err)
			return err
		}
	case dto.UserSourceInfogo:
		client := infogo.NewInfogoClient(extConf.Endpoint, extConf.Login, extConf.Pass)
		externalInfo, err = u.getInfogoInfo(client, rootGroupId)
		if err != nil {
			u.log.Errorf("getInfogoInfo failed. err=%v", err)
			return err
		}
	default:
		// 根据模板类型处理其他类型
		templateType := sourceType // 默认使用源类型作为模板类型
		if userSource != nil {
			templateType = userSource.TemplateType // 如果有指定模板类型，则使用
		}

		switch dto.UserSourceType(templateType) {
		case dto.UserSourceOAuth2:
			// 处理所有基于OAuth2模板的源类型
			client, err := u.createClientBySourceType(sourceType, extConf)
			if err != nil {
				u.log.Errorf("创建 %v 客户端失败: %v", sourceType, err)
				return err
			}

			externalInfo, err = u.getExternalInfo(client, rootGroupId, sourceType)
			if err != nil {
				u.log.Errorf("获取 %v 外部信息失败: %v", sourceType, err)
				return err
			}
		default:
			return pb.ErrorParamError("sourceType=%v with templateType=%v not support", sourceType, templateType)
		}
	}
	localExternalDepts, err := u.repo.ListExternalDeps(ctx, rootGroupId)
	if err != nil {
		u.log.Errorf("ListExternalDeps failed. err=%v", err)
		return err
	}
	localExternalUsers, err := u.repo.ListExternalUsers(ctx, rootGroupId)
	if err != nil {
		u.log.Errorf("ListExternalUsers failed. err=%v", err)
		return err
	}

	changeExtInfo := u.getChangeExtInfo(externalInfo, localExternalDepts, localExternalUsers)

	fieldMap := extConf.FieldMap
	u.log.Debugf("OldfieldMap=%+v", fieldMap)
	if len(extConf.FieldMap) == 0 {
		// 使用已有的GetFieldMap方法获取映射关系
		var err error
		// 获取用户源类型
		userSourceType := dto.UserSourceType(sourceType)

		// 优先尝试使用源类型特有的映射
		fieldMap, err = u.GetFieldMap(ctx, userSourceType, dto.FieldMapTypeSync)
		if err != nil {
			u.log.Debugf("未找到源类型 %s 的字段映射，尝试使用模板类型", sourceType)

			// 如果源类型没有特定映射且存在模板类型，则尝试使用模板类型的映射
			if userSource != nil && userSource.TemplateType != "" {
				templateMapping, err := u.GetFieldMap(ctx, dto.UserSourceType(userSource.TemplateType), dto.FieldMapTypeSync)
				if err == nil {
					fieldMap = templateMapping
					u.log.Debugf("使用模板类型 %s 的字段映射", userSource.TemplateType)
				}
			}

			// 如果仍然没有找到适用的映射，使用默认映射
			if len(fieldMap) == 0 {
				fieldMap = dto.FeiShuFieldMap[dto.FieldMapTypeSync]
				u.log.Warnf("无法获取 %s 的字段映射，使用默认映射", sourceType)
			}
		} else {
			u.log.Debugf("使用源类型 %s 的字段映射", sourceType)
		}
	}
	// update
	u.log.Debugf("NewfieldMap=%+v", fieldMap)
	u.log.Infof("syncUpdateDB. rootGroupId=%v, sourceType=%v", rootGroupId, sourceType)

	err = u.syncUpdate(ctx, changeExtInfo, fieldMap, *rootGroup, corpId, sourceType)
	if err != nil {
		u.log.Errorf("syncUpdateDB failed. err=%v", err)
		return err
	}

	return nil
}

func (u UserUsecase) syncUpdate(
	ctx context.Context,
	extInfo dto.NeedChangeExternalInfo,
	filedMap []dto.KV,
	rootGroup model.TbUserGroup,
	corpId string, sourceType string) error {
	// Update Dept
	upsetExt := make([]*model.TbExternalDepartment, 0)
	deptIdMap := extInfo.DeptIdMap
	deptIdMap[rootGroup.ID] = rootGroup.ID
	for _, v := range extInfo.AddDeptList {
		var deptTmp model.TbExternalDepartment
		localGroupId := uuid.New().String()
		err := copier.Copy(&deptTmp, &v)
		if err != nil {
			return err
		}
		deptTmp.Type = sourceType
		deptIdMap[v.ID] = localGroupId
		deptTmp.LocalGroupID = localGroupId
		deptTmp.LocalRootGroupID = rootGroup.RootGroupID
		upsetExt = append(upsetExt, &deptTmp)
	}
	for _, v := range extInfo.ChangeDeptList {
		var deptTmp model.TbExternalDepartment
		err := copier.Copy(&deptTmp, &v)
		if err != nil {
			return err
		}
		deptTmp.Type = sourceType
		upsetExt = append(upsetExt, &deptTmp)
		deptTmp.LocalRootGroupID = rootGroup.RootGroupID
		deptIdMap[v.ID] = v.LocalGroupID
	}
	// 企业微信根部门
	if sourceType == string(dto.UserSourceQiYeWx) {
		deptIdMap["1"] = rootGroup.RootGroupID
	}
	if sourceType == string(dto.UserSourcePaila) {
		// 详细记录部门映射情况
		u.log.Infof("部门ID映射表: %+v", deptIdMap)
		// 将根部门映射到同步目录
		deptIdMap["0"] = rootGroup.ID
	}
	if sourceType == string(dto.UserSourceZhezhengding) {
		// 详细记录部门映射情况
		u.log.Infof("部门ID映射表: %+v", deptIdMap)
		// 将根部门映射到同步目录
		deptIdMap["0"] = rootGroup.ID
	}
	// Update User
	upsetUser := make([]*model.TbExternalUser, 0)
	existLocalUserIds := make([]string, 0)
	for _, v := range extInfo.AddUserList {
		// ext user
		var userTmp model.TbExternalUser
		localUserId := uuid.New().String()
		err := copier.Copy(&userTmp, &v)
		if err != nil {
			return err
		}
		userTmp.LocalUserID = localUserId
		userTmp.LocalRootGroupID = rootGroup.ID
		userTmp.Type = sourceType
		upsetUser = append(upsetUser, &userTmp)
	}
	for _, v := range extInfo.ChangeUserList {
		var userTmp model.TbExternalUser
		err := copier.Copy(&userTmp, &v)
		if err != nil {
			return err
		}
		userTmp.Type = sourceType
		userTmp.LocalRootGroupID = rootGroup.ID
		upsetUser = append(upsetUser, &userTmp)
		existLocalUserIds = append(existLocalUserIds, v.LocalUserID)

	}
	// local group
	localGroupList := structLocalGroup(corpId, sourceType, deptIdMap, rootGroup, upsetExt)
	// local user
	localUserList := u.structLocalUser(corpId, sourceType, deptIdMap, rootGroup, filedMap, upsetUser)
	// upset
	syncData := dto.SyncDeptAndUser{
		LocalRootGroupId: rootGroup.ID,
		ExistDeptIds:     extInfo.ExistDeptIds,
		ExistUserIds:     extInfo.ExistUserIds,
		Depts:            upsetExt,
		Users:            upsetUser,
		LocalGroups:      localGroupList,
		LocalUsers:       localUserList,
	}
	err := u.repo.BatchInsertExternalInfoTx(syncData)
	if err != nil {
		return err
	}
	return nil
}
func (u UserUsecase) structLocalUser(corpId, sourceType string, deptIdMap map[string]string, rootGroup model.TbUserGroup, fieldMap []dto.KV, extUsers []*model.TbExternalUser) []*model.TbUserEntity {
	var userDaoParam []*model.TbUserEntity
	for _, user := range extUsers {
		item := model.TbUserEntity{
			CorpID:      corpId,
			ID:          user.LocalUserID,
			GroupID:     deptIdMap[user.MainDepartment],
			SourceID:    rootGroup.SourceID,
			Identify:    user.Userid,
			ExpireType:  string(dto.ExpireTypeForever),
			RootGroupID: rootGroup.ID,
			Phone:       user.Mobile,
			Name:        user.Name,
			DisplayName: user.DisplayName,
			NickName:    user.NickName,
			TrueName:    user.TrueName,
			AuthType:    sourceType,
			Email:       user.Email,
			Enable:      true,
		}

		userDaoParam = append(userDaoParam, &item)
	}
	return userDaoParam
}
func structLocalGroup(corpId, sourceType string, parentIdMap map[string]string, rootGroup model.TbUserGroup, depts []*model.TbExternalDepartment) []*model.TbUserGroup {
	res := make([]*model.TbUserGroup, 0)
	for _, dept := range depts {
		if dept.Parentid == dto.WxFakeRootDepartmentId && sourceType == string(dto.UserSourceQiYeWx) { // 根目录不插入
			continue
		}
		g := &model.TbUserGroup{
			CorpID:        corpId,
			ID:            dept.LocalGroupID,
			Name:          dept.Name,
			ParentGroupID: parentIdMap[dept.Parentid],
			SourceID:      rootGroup.SourceID,
			RootGroupID:   rootGroup.ID,
		}
		if dept.Parentid == dto.InfogoFakeRootDepartmentId && sourceType == string(dto.UserSourceInfogo) { // 根目录不插入
			g.ParentGroupID = rootGroup.ID
		}
		res = append(res, g)
	}
	return res
}

func (u UserUsecase) getChangeExtInfo(
	extInfo dto.ExternalInfo,
	localDept []*model.TbExternalDepartment,
	localUser []*model.TbExternalUser) dto.NeedChangeExternalInfo {
	// user
	addUser := make([]*dto.ExternalUser, 0)
	changeUser := make([]*dto.ExternalUser, 0)
	existUserIds := make([]string, 0)
	uniqKeyMap := make(map[string]string)
	localUserIdMap := make(map[string]string)
	for _, v := range localUser {
		uniqKeyMap[v.Userid] = v.UniqKey
		localUserIdMap[v.Userid] = v.LocalUserID
	}
	for _, v := range extInfo.Users {
		// 不存在则直接新增
		if _, ok := uniqKeyMap[v.Userid]; !ok {
			addUser = append(addUser, v)
			continue
		}
		if uniqKeyMap[v.Userid] != v.UniqKey {
			var userItem dto.ExternalUser
			copier.Copy(&userItem, &v)
			userItem.LocalUserID = localUserIdMap[v.Userid]
			changeUser = append(changeUser, &userItem)
		}
		existUserIds = append(existUserIds, localUserIdMap[v.Userid])
	}

	// dept
	addDept := make([]*dto.ExternalDepartment, 0)
	changeDept := make([]*dto.ExternalDepartment, 0)
	existDeptIds := make([]string, 0)
	uniqKeyDeptMap := make(map[string]string)
	deptIdMap := make(map[string]string)
	localGroupIdMap := make(map[string]string)
	for _, v := range localDept {
		if v.UniqKey != "" {
			uniqKeyDeptMap[v.ID] = v.UniqKey
		}
		localGroupIdMap[v.ID] = v.LocalGroupID
	}
	for _, v := range extInfo.Depts {
		if uniqKeyDeptMap[v.ID] == "" {
			addDept = append(addDept, v)
			continue
		}
		if uniqKeyDeptMap[v.ID] != v.UniqKey {
			var item dto.ExternalDepartment
			copier.Copy(&item, &v)
			item.LocalGroupID = localGroupIdMap[v.ID]
			changeDept = append(changeDept, &item)
		}
		deptIdMap[v.ID] = localGroupIdMap[v.ID]
		existDeptIds = append(existDeptIds, localGroupIdMap[v.ID])
	}
	// 空数组给一个默认
	if len(existDeptIds) == 0 {
		existDeptIds = append(existDeptIds, "")
	}
	if len(existUserIds) == 0 {
		existUserIds = append(existUserIds, "")
	}
	return dto.NeedChangeExternalInfo{
		AddUserList:    addUser,
		ExistUserIds:   existUserIds,
		ChangeUserList: changeUser,
		AddDeptList:    addDept,
		ExistDeptIds:   existDeptIds,
		ChangeDeptList: changeDept,
		DeptIdMap:      deptIdMap,
	}
}

func (u UserUsecase) getAdInfo(adConf ad.ActiveDirectory, rootGroupId, adtype string) (dto.ExternalInfo, error) {
	depts, users, err := ad.GetAllGroupAndUser(adConf, rootGroupId, adtype)
	if err != nil {
		return dto.ExternalInfo{}, err
	}
	return dto.ExternalInfo{
		Depts:  depts,
		Users:  users,
		RootId: rootGroupId,
	}, nil
}

func (u UserUsecase) getInfogoInfo(client *infogo.InfogoClient, rootGroupId string) (dto.ExternalInfo, error) {
	depts, err := client.GetAllDepts()
	if err != nil {
		u.log.Errorf("GetAllDepts failed. err=%v", err)
		return dto.ExternalInfo{}, err
	}
	users, err := client.GetAllUsers()

	return dto.ExternalInfo{
		Depts:  depts,
		Users:  users,
		RootId: rootGroupId,
	}, nil
}

const MaxRetryCount = 5

func (u UserUsecase) getWxInfo(client *qiyewx.WeComClient, corpId, secret string, agentId int) (dto.ExternalInfo, error) {
	depts, err := client.GetAllDepts()
	if err != nil {
		u.log.Errorf("GetAllDepts failed. err=%v", err)
		return dto.ExternalInfo{}, err
	}
	u.log.Infof("get wecom depts done, total depts count %d", len(depts))
	rootId, err := common.GetRootID(depts)
	if err != nil {
		u.log.Errorf("GetRootID failed. err=%v", err)
		return dto.ExternalInfo{}, err
	}
	u.log.Infof("wecom Group RootID %s, start to GetAllUsers", rootId)

	var allUsers []*dto.ExternalUser
	retryCount := 0
	existingUserIds := make(map[string]struct{})

	for _, dept := range depts {
	retryDeptID:
		if retryCount >= MaxRetryCount {
			u.log.Errorf("GetAllUsers failed reached max times %d . err=%v", MaxRetryCount, err)
			return dto.ExternalInfo{}, err
		}
		// 导入用户
		deptUsers, err := client.GetAllUsers(dept.ID, "0")
		if err != nil {
			u.log.Errorf("GetAllUsers for group ID:%d failed retry times:%d. err=%v", dept.ID, retryCount, err)
			retryCount = retryCount + 1
			time.Sleep(10 * time.Second)
			client = qiyewx.NewWeComClient(corpId, secret, agentId)
			goto retryDeptID
		}
		// 检查重复的用户
		var newUsers []*dto.ExternalUser
		for _, user := range deptUsers {
			if _, exists := existingUserIds[user.Userid]; !exists {
				// 如果用户ID不存在，则添加到allUsers，并标记为已存在
				newUsers = append(newUsers, user)
				existingUserIds[user.Userid] = struct{}{}
			} else {
				// 如果用户ID已存在，则跳过该用户
				u.log.Infof("UserID %s already exists, skipping", user.Userid)
			}
		}

		u.log.Infof("Retry times %d Group ID %s, GetAllUsers got %d users", retryCount, dept.ID, len(deptUsers))
		allUsers = append(allUsers, newUsers...)
		time.Sleep(50 * time.Microsecond)
	}

	return dto.ExternalInfo{
		Depts:  depts,
		Users:  allUsers,
		RootId: rootId,
	}, nil
}

func (u UserUsecase) getFeishuInfo(client *feishu.LarkClient, rootGroupId string) (dto.ExternalInfo, error) {
	depts, users, err := client.GetAllDepts(rootGroupId)
	if err != nil {
		u.log.Errorf("GetAllDepts failed. err=%v", err)
		return dto.ExternalInfo{}, err
	}

	return dto.ExternalInfo{
		Depts:  depts,
		Users:  users,
		RootId: rootGroupId,
	}, nil
}

type Department struct {
	Name string
	Path string
}

func (u UserUsecase) GetFieldMap(ctx context.Context, srcType dto.UserSourceType, fieldMapType dto.FieldMapType) ([]dto.KV, error) {
	switch srcType {
	case dto.UserSourceQiYeWx:
		fieldMap, ok := dto.QiyeWxFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourceFeiShu:
		fieldMap, ok := dto.FeiShuFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourceDingtalk:
		fieldMap, ok := dto.DingtalkFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourceLdap:
		fieldMap, ok := dto.LdapFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourceMsad:
		fieldMap, ok := dto.MsadFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourceInfogo:
		fieldMap, ok := dto.InfogoFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourceOAuth2: // 添加: OAuth2.0用户身份源
		fieldMap, ok := dto.OAuth2FieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourcePaila:
		fieldMap, ok := dto.PailaFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	case dto.UserSourceZhezhengding:
		fieldMap, ok := dto.ZhezhengdingFieldMap[fieldMapType]
		if !ok {
			return []dto.KV{}, pb.ErrorRecordNotFound("fieldMapType=%v not found.", fieldMapType)
		}
		return fieldMap, nil
	default:
		u.log.Errorf("srcType=%v not support", srcType)
		return []dto.KV{}, pb.ErrorSourceTypeNotSupport("srcType=%v not support", srcType)
	}
}

func (u UserUsecase) getDingtalkInfo(client *dingtalk.DingtalkkClient, rootGroupId string) (dto.ExternalInfo, error) {
	depts, err := client.GetAllDepts(rootGroupId)
	if err != nil {
		u.log.Errorf("GetAllDepts failed. err=%v", err)
		return dto.ExternalInfo{}, err
	}
	users, err := client.GetAllUsers(depts, rootGroupId)
	if err != nil {
		u.log.Errorf("GetAllUsers failed. err=%v", err)
		return dto.ExternalInfo{}, err
	}
	return dto.ExternalInfo{
		Depts:  depts,
		Users:  users,
		RootId: rootGroupId,
	}, nil
}

func (u UserUsecase) getExternalInfo(client idp.ExternalInfoClient, rootGroupId string, sourceType string) (dto.ExternalInfo, error) {
	// 获取部门和用户信息
	depts, users, err := client.GetDeptsAndUsers(rootGroupId)
	if err != nil {
		u.log.Errorf("获取%s部门和用户失败: %v", sourceType, err)
		return dto.ExternalInfo{}, err
	}

	u.log.Infof("已获取%s部门,总数: %d", sourceType, len(depts))
	u.log.Infof("已获取%s用户,总数: %d", sourceType, len(users))

	return dto.ExternalInfo{
		Depts:  depts,
		Users:  users,
		RootId: rootGroupId,
	}, nil
}

func (u UserUsecase) createClientBySourceType(sourceType string, extConf dto.IdpAttr) (idp.ExternalInfoClient, error) {
	switch dto.UserSourceType(sourceType) {
	case dto.UserSourcePaila:
		return paila.NewPailaClient(extConf.GlobalData)
	case dto.UserSourceZhezhengding:
		return zhezhengding.NewZhezhengdingClient(extConf.GlobalData)

	case dto.UserSourceOAuth2:
		return nil, fmt.Errorf("不支持的源类型: %s", sourceType)
	default:
		return nil, fmt.Errorf("不支持的源类型: %s", sourceType)
	}
}
