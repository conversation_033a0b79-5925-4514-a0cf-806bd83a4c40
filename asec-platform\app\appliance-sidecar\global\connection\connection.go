package connection

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"net"
	"net/http"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"

	utls "github.com/refraction-networking/utls"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func newUtlsConfig(serverName string) *utls.Config {
	return &utls.Config{
		ServerName:         serverName,
		InsecureSkipVerify: true,
	}
}

// NewGrpcWithUtls 使用uTLS创建gRPC连接，支持SPA扩展
func NewGrpcWithUtls(ctx context.Context, targetAddress string) (*grpc.ClientConn, error) {
	// 在底层统一获取SPA代码
	spaCode := global.GetProcessedSpaCode()

	customDialer := func(ctx context.Context, addr string) (net.Conn, error) {
		rawConn, err := net.Dial("tcp", addr)
		if err != nil {
			return nil, fmt.Errorf("tcp dial to %s failed: %w", addr, err)
		}

		host, _, err := net.SplitHostPort(addr)
		if err != nil {
			host = addr // Fallback
		}

		config := newUtlsConfig(host)
		tlsConn := utls.UClient(rawConn, config, utls.HelloCustom) // Keep Chrome for gRPC
		spec, err := utls.UTLSIdToSpec(utls.HelloChrome_Auto)
		if err != nil {
			tlsConn.Close()
			return nil, fmt.Errorf("failed to create UTLS spec for Chrome: %w", err)
		}
		// 确保gRPC的ALPN支持（无论是否有SPA代码）
		hasALPN := false
		for i, ext := range spec.Extensions {
			if alpn, ok := ext.(*utls.ALPNExtension); ok {
				hasALPN = true
				alpn.AlpnProtocols = []string{"h2"} // Ensure h2 for gRPC
				spec.Extensions[i] = alpn
				break
			}
		}
		if !hasALPN {
			spec.Extensions = append(spec.Extensions, &utls.ALPNExtension{AlpnProtocols: []string{"h2"}})
		}

		// 添加SPA自定义扩展（已经在外层判断过spaCode不为空）
		if spaCode != "" {
			customExtension := &utls.GenericExtension{
				Id:   global.SpaExtensionID,
				Data: []byte(spaCode),
			}
			spec.Extensions = append([]utls.TLSExtension{customExtension}, spec.Extensions...)
		}

		if err := tlsConn.ApplyPreset(&spec); err != nil {
			tlsConn.Close()
			return nil, fmt.Errorf("tlsConn.ApplyPreset for gRPC to %s failed: %w", addr, err)
		}

		if err := tlsConn.Handshake(); err != nil {
			tlsConn.Close()
			return nil, fmt.Errorf("gRPC uTLS handshake to %s failed: %w", addr, err)
		}
		return tlsConn, nil
	}

	conn, err := grpc.DialContext(
		ctx,
		targetAddress,
		grpc.WithContextDialer(customDialer),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)

	if err != nil {
		return nil, fmt.Errorf("gRPC connection to %s failed: %w", targetAddress, err)
	}

	return conn, nil
}

func GetPlatformConnection(ctx context.Context) (*grpc.ClientConn, error) {
	privateHost := global.PrivateHost
	if privateHost == "" {
		privateHost = global.Conf.Endpoints.PrivateHost
	}

	return NewGrpcWithUtls(ctx, privateHost)
}

func GetLogCenterConnection(ctx context.Context) (*grpc.ClientConn, error) {
	endpoint := global.LogServerHost
	if global.LogServerHost == "" {
		endpoint = global.Conf.Endpoints.LogCenterHost
	}
	return NewGrpcWithUtls(ctx, endpoint)
}

// PlatformRequester 平台请求器接口 - 保持简洁
type PlatformRequester interface {
	http.RoundTripper
	Do(ctx context.Context, method, url string, body []byte, headers map[string]string) ([]byte, error)
	Download(ctx context.Context, url string) (io.ReadCloser, *http.Response, error)
}

// customHttpRequester implements PlatformRequester using uTLS/SPA.
type customHttpRequester struct{}

// responseBodyCloser wraps a response body and a connection, closing both.
type responseBodyCloser struct {
	io.ReadCloser
	conn net.Conn
}

func (c *responseBodyCloser) Close() error {
	errBody := c.ReadCloser.Close()
	errConn := c.conn.Close()
	if errBody != nil {
		return errBody
	}
	return errConn
}

// RoundTrip implements the http.RoundTripper interface. This is the core method
// that establishes a uTLS/SPA connection and sends a request, returning a streaming response.
func (r *customHttpRequester) RoundTrip(req *http.Request) (*http.Response, error) {
	parsedURL := req.URL
	host, port, err := net.SplitHostPort(parsedURL.Host)
	if err != nil {
		host = parsedURL.Host
		if parsedURL.Scheme == "https" {
			port = "443"
		} else {
			port = "80"
		}
	}
	serverAddr := net.JoinHostPort(host, port)

	dialConn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("tcp dial failed: %w", err)
	}

	config := newUtlsConfig(host)
	tlsConn := utls.UClient(dialConn, config, utls.HelloCustom)

	// 始终设置基本的Chrome规格，确保ClientHello正确构建
	spec, err := utls.UTLSIdToSpec(utls.HelloChrome_Auto)
	if err != nil {
		tlsConn.Close()
		return nil, fmt.Errorf("failed to create UTLS spec for Chrome: %w", err)
	}

	// 如果有SPA代码，添加自定义扩展
	spaCode := global.GetProcessedSpaCode()
	if spaCode != "" {
		customExtension := &utls.GenericExtension{
			Id:   global.SpaExtensionID,
			Data: []byte(spaCode),
		}
		spec.Extensions = append([]utls.TLSExtension{customExtension}, spec.Extensions...)
	}

	// 应用规格（无论是否有SPA代码）
	if err := tlsConn.ApplyPreset(&spec); err != nil {
		tlsConn.Close()
		return nil, fmt.Errorf("failed to apply uTLS preset: %w", err)
	}

	if err := tlsConn.Handshake(); err != nil {
		tlsConn.Close()
		return nil, fmt.Errorf("uTLS handshake failed: %w", err)
	}

	if err := req.Write(tlsConn); err != nil {
		tlsConn.Close()
		return nil, fmt.Errorf("writing request failed: %w", err)
	}

	resp, err := http.ReadResponse(bufio.NewReader(tlsConn), req)
	if err != nil {
		tlsConn.Close()
		return nil, fmt.Errorf("reading response failed: %w", err)
	}

	resp.Body = &responseBodyCloser{ReadCloser: resp.Body, conn: tlsConn}
	return resp, nil
}

// Do is a convenience wrapper around RoundTrip for non-streaming use cases.
func (r *customHttpRequester) Do(ctx context.Context, method, urlStr string, body []byte, headers map[string]string) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, method, urlStr, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	if headers != nil {
		for k, v := range headers {
			req.Header.Set(k, v)
		}
	}

	resp, err := r.RoundTrip(req)
	if err != nil {
		// Even on error, there might be a response body with details.
		if resp != nil && resp.Body != nil {
			defer resp.Body.Close()
			bodyBytes, _ := io.ReadAll(resp.Body)
			return bodyBytes, fmt.Errorf("request failed: %w, response body: %s", err, string(bodyBytes))
		}
		return nil, err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return bodyBytes, fmt.Errorf("bad status: %s", resp.Status)
	}
	return bodyBytes, nil
}

// Download is a convenience wrapper for streaming downloads.
func (r *customHttpRequester) Download(ctx context.Context, urlStr string) (io.ReadCloser, *http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", urlStr, nil)
	if err != nil {
		return nil, nil, err
	}

	resp, err := r.RoundTrip(req)
	if err != nil {
		return nil, resp, err
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		defer resp.Body.Close()
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		return nil, resp, fmt.Errorf("download failed with status %s: %s", resp.Status, string(bodyBytes))
	}

	return resp.Body, resp, nil
}

// newCustomHttpClient 返回一个新的自定义 requester 实例
func newCustomHttpClient() PlatformRequester {
	return &customHttpRequester{}
}

func GetPlatformRequester(protocol string) PlatformRequester {
	if protocol == "http" || protocol == "https" {
		return newCustomHttpClient()
	}
	// can be extended for other protocols
	return nil
}

func CloseConnection(conn *grpc.ClientConn) {
	if conn != nil {
		_ = conn.Close()
	}
}

// GetPlatformURL 统一平台URL生成函数
func GetPlatformURL(path string) string {
	host := global.PrivateHost
	if host == "" {
		host = global.Conf.Endpoints.PrivateHost
	}
	return fmt.Sprintf("https://%s%s", host, path)
}

// GatewayCommandHooks 网关命令服务钩子接口
type GatewayCommandHooks interface {
	OnPlatformDisconnected() error
	OnApplianceRegistered(applianceID uint64) error
}

var (
	gatewayCommandHooks GatewayCommandHooks
)

// RegisterGatewayCommandHooks 注册网关命令服务钩子
func RegisterGatewayCommandHooks(hooks GatewayCommandHooks) {
	gatewayCommandHooks = hooks
}

// GetGatewayCommandHooks 获取已注册的网关命令服务钩子
func GetGatewayCommandHooks() GatewayCommandHooks {
	return gatewayCommandHooks
}

// notifyApplianceRegistered 通知设备注册完成
func notifyApplianceRegistered(applianceID uint64) {
	// 只使用GatewayCommandHooks系统，避免重复回调
	if gatewayCommandHooks != nil {
		if err := gatewayCommandHooks.OnApplianceRegistered(applianceID); err != nil {
			// 记录错误但不影响正常流程
			fmt.Printf("Warning: Failed to notify appliance registered: %v\n", err)
		}
	}
}

// NotifyApplianceRegistered 导出函数用于通知设备注册完成
func NotifyApplianceRegistered(applianceID uint64) {
	notifyApplianceRegistered(applianceID)
}
