// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameTbUserLoginLog = "tb_user_login_log"

// TbUserLoginLog mapped from table <tb_user_login_log>
type TbUserLoginLog struct {
	ID          string `gorm:"column:id;primaryKey" json:"id"`
	ClientID    string `gorm:"column:client_id" json:"client_id"`
	Error       string `gorm:"column:error" json:"error"`
	IPAddress   string `gorm:"column:ip_address" json:"ip_address"`
	CorpID      string `gorm:"column:corp_id" json:"corp_id"`
	EventTime   int64  `gorm:"column:event_time" json:"event_time"`
	Type        string `gorm:"column:type" json:"type"`
	UserID      string `gorm:"column:user_id" json:"user_id"`
	SourceID    string `gorm:"column:source_id" json:"source_id"`
	Description string `gorm:"column:description" json:"description"`
}

// TableName TbUserLoginLog's table name
func (*TbUserLoginLog) TableName() string {
	return TableNameTbUserLoginLog
}
