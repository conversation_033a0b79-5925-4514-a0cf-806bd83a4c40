version: "3"
services:
  openresty:
    # openresty/openresty:1.21.4.1-0-jammy
    image: registry.cn-guangzhou.aliyuncs.com/asdsec/openresty:1.21.4.1-0-jammy
    container_name: openresty
    ports:
      - "9443:9443"
      - "443:443"
      - "4430:4430"
    volumes:
      - ./nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf
      - /opt/asdsec-compose/openresty/ssl:/usr/local/openresty/nginx/conf/ssl
      - /opt/asdsec-compose/openresty/conf.d:/usr/local/openresty/nginx/conf/conf.d
      - /opt/asdsec-compose/openresty/wechat-verify:/usr/local/openresty/nginx/wechat-verify
      - /opt/front/asec/portal:/opt/front/asec/portal
      - /opt/front/asec/web:/opt/front/asec/web
      - /opt/platform/icon:/opt/platform/icon
    restart: unless-stopped
    network_mode: "host"
    environment:
      TZ: Asia/Shanghai
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "10"