package se

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
)

// 定义与tun-server配置匹配的结构体
type DynamicConfig struct {
	Enabled    bool                `json:"enabled"`
	Pools      []DynamicPoolConfig `json:"pools"`
	Commands   []VirtualIPCommand  `json:"commands,omitempty"`
	ConfigHash string              `json:"config_hash"`
}

type DynamicPoolConfig struct {
	Name               string                   `json:"name"`
	IpRange            string                   `json:"ip_range"`
	IpExpiryDuration   int32                    `json:"ip_expiry_duration"`
	CleanupInterval    int32                    `json:"cleanup_interval"`
	PoolType           string                   `json:"pool_type"`
	AllocationStrategy string                   `json:"allocation_strategy"`
	MaxIpsPerUser      int32                    `json:"max_ips_per_user"`
	DirectoryConfigs   []DirectoryConfigData    `json:"directory_configs,omitempty"`
	DedicatedConfigs   []DynamicDedicatedConfig `json:"dedicated_configs,omitempty"`
	Enabled            bool                     `json:"enabled"`
}

type DynamicDedicatedConfig struct {
	UserId     string   `json:"user_id"`
	UserName   string   `json:"user_name"`
	VirtualIps []string `json:"virtual_ips"`
	Priority   int32    `json:"priority"`
}

type DirectoryConfigData struct {
	DedicatedUsers  []DedicatedUserData `json:"dedicated_users,omitempty"`
	Policy          string              `json:"policy,omitempty"`
	Departments     []DepartmentData    `json:"departments,omitempty"`
	ExpandedUserIds []string            `json:"expanded_user_ids,omitempty"`
	UserIdHashSet   map[string]bool     `json:"-"`
}

type DedicatedUserData struct {
	UserId     string   `json:"user_id"`
	UserName   string   `json:"user_name"`
	VirtualIps []string `json:"virtual_ips"`
	Priority   int32    `json:"priority"`
}

type DepartmentData struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	Path        string `json:"path"`
	Type        string `json:"type"`
	SourceType  string `json:"source_type"`
	DisplayName string `json:"display_name"`
}

type VirtualIPCommand struct {
	ID         string                 `json:"id"`     // 唯一标识
	Type       string                 `json:"type"`   // 命令类型
	Action     string                 `json:"action"` // 具体动作
	UserID     string                 `json:"user_id,omitempty"`
	VirtualIP  string                 `json:"virtual_ip,omitempty"`
	PoolName   string                 `json:"pool_name,omitempty"`
	Params     map[string]interface{} `json:"params,omitempty"` // 通用参数
	Timestamp  int64                  `json:"timestamp"`
	Status     string                 `json:"status"` // pending, executing, completed, failed
	RetryCount int                    `json:"retry_count,omitempty"`
}

// 配置哈希缓存
var (
	configHashCache = make(map[string]string)
	cacheMutex      sync.RWMutex
	cacheExpiry     = make(map[string]time.Time)
	cacheTimeout    = 2 * time.Minute
)

// calculateConfigHash 计算原始配置的哈希值，用于快速比较
func calculateConfigHash(pools []*pb.VirtualIPPoolConfig, globalSettings *pb.VirtualIPGlobalSettings) string {
	// 生成缓存键
	cacheKey := generateCacheKey(pools, globalSettings)

	// 检查缓存
	cacheMutex.RLock()
	if hash, exists := configHashCache[cacheKey]; exists {
		if expiry, expiryExists := cacheExpiry[cacheKey]; expiryExists && time.Now().Before(expiry) {
			cacheMutex.RUnlock()
			return hash
		}
	}
	cacheMutex.RUnlock()

	// 计算新的哈希
	hash := calculateHashInternal(pools, globalSettings)

	// 更新缓存
	cacheMutex.Lock()
	configHashCache[cacheKey] = hash
	cacheExpiry[cacheKey] = time.Now().Add(cacheTimeout)

	// 清理过期缓存
	cleanExpiredCache()
	cacheMutex.Unlock()

	return hash
}

// generateCacheKey 生成缓存键
func generateCacheKey(pools []*pb.VirtualIPPoolConfig, globalSettings *pb.VirtualIPGlobalSettings) string {
	hasher := sha256.New()

	// 全局设置
	if globalSettings != nil {
		hasher.Write([]byte(fmt.Sprintf("g:%v:%d:%s",
			globalSettings.Enabled, globalSettings.GlobalMaxDuration, globalSettings.Description)))
	}

	// 池的基本信息（不包含大数组）
	for i, pool := range pools {
		if pool == nil {
			continue
		}
		hasher.Write([]byte(fmt.Sprintf("p%d:%s:%s:%s:%d:%d",
			i, pool.Name, pool.IpRange, pool.PoolType,
			pool.IpExpiryDuration, pool.CleanupInterval)))

		// 配置数量作为简单标识
		hasher.Write([]byte(fmt.Sprintf("dc:%d", len(pool.DirectoryConfigs))))
	}

	return hex.EncodeToString(hasher.Sum(nil))[:16] // 只取前16位作为缓存键
}

// calculateHashInternal 内部哈希计算
func calculateHashInternal(pools []*pb.VirtualIPPoolConfig, globalSettings *pb.VirtualIPGlobalSettings) string {
	hasher := sha256.New()

	// 包含全局设置
	if globalSettings != nil {
		hasher.Write([]byte(fmt.Sprintf("global_enabled:%v,global_max_duration:%d,description:%s",
			globalSettings.Enabled, globalSettings.GlobalMaxDuration, globalSettings.Description)))
	}

	// 处理每个池
	for i, pool := range pools {
		if pool == nil {
			continue
		}

		// 池的基本信息
		poolInfo := fmt.Sprintf("pool_%d:name=%s,ip_range=%s,type=%s,expiry=%d,cleanup=%d",
			i, pool.Name, pool.IpRange, pool.PoolType, pool.IpExpiryDuration, pool.CleanupInterval)
		hasher.Write([]byte(poolInfo))

		// 分配策略
		if pool.AllocationPolicy != nil {
			policyInfo := fmt.Sprintf(",strategy=%s,max_ips=%d",
				pool.AllocationPolicy.Strategy, pool.AllocationPolicy.MaxIpsPerUser)
			hasher.Write([]byte(policyInfo))
		}

		// 处理目录配置
		for j, dirConfig := range pool.DirectoryConfigs {
			if dirConfig != nil && dirConfig.Config != "" {
				normalizedHash := calculateDirectoryConfigHash(dirConfig.Config)
				configInfo := fmt.Sprintf(",dir_config_%d=%s", j, normalizedHash)
				hasher.Write([]byte(configInfo))
			}
		}
	}

	return hex.EncodeToString(hasher.Sum(nil))
}

// calculateDirectoryConfigHash 为单个目录配置计算哈希
func calculateDirectoryConfigHash(configStr string) string {
	var config map[string]interface{}
	if err := json.Unmarshal([]byte(configStr), &config); err != nil {
		// 解析失败，直接返回原始字符串的哈希
		h := sha256.Sum256([]byte(configStr))
		return hex.EncodeToString(h[:])[:16]
	}

	hasher := sha256.New()

	// 处理 policy
	if policy, exists := config["policy"]; exists {
		hasher.Write([]byte(fmt.Sprintf("policy:%v", policy)))
	}

	// 处理 departments（排序）
	if departments, exists := config["departments"]; exists {
		if deptArray, ok := departments.([]interface{}); ok {
			sort.Slice(deptArray, func(i, j int) bool {
				dept1, ok1 := deptArray[i].(map[string]interface{})
				dept2, ok2 := deptArray[j].(map[string]interface{})
				if !ok1 || !ok2 {
					return false
				}
				id1, _ := dept1["id"].(string)
				id2, _ := dept2["id"].(string)
				return id1 < id2
			})

			// 只写入部门ID，减少数据量
			for _, dept := range deptArray {
				if deptMap, ok := dept.(map[string]interface{}); ok {
					if id, ok := deptMap["id"].(string); ok {
						hasher.Write([]byte(fmt.Sprintf("dept:%s", id)))
					}
				}
			}
		}
	}

	// 处理 expanded_user_ids
	if expandedUserIds, exists := config["expanded_user_ids"]; exists {
		if userIds, ok := expandedUserIds.([]interface{}); ok {
			// 使用集合哈希：先计算每个用户ID的哈希，然后异或所有哈希
			var combinedHash [32]byte
			for _, id := range userIds {
				if strId, ok := id.(string); ok {
					userHash := sha256.Sum256([]byte(strId))
					for k := 0; k < 32; k++ {
						combinedHash[k] ^= userHash[k]
					}
				}
			}
			hasher.Write(combinedHash[:])
			hasher.Write([]byte(fmt.Sprintf("user_count:%d", len(userIds))))
		}
	}

	// 处理 dedicated_users - 修复：包含所有字段
	if dedicatedUsers, exists := config["dedicated_users"]; exists {
		if userArray, ok := dedicatedUsers.([]interface{}); ok {
			sort.Slice(userArray, func(i, j int) bool {
				user1, ok1 := userArray[i].(map[string]interface{})
				user2, ok2 := userArray[j].(map[string]interface{})
				if !ok1 || !ok2 {
					return false
				}
				id1, _ := user1["user_id"].(string)
				id2, _ := user2["user_id"].(string)
				return id1 < id2
			})

			for _, user := range userArray {
				if userMap, ok := user.(map[string]interface{}); ok {
					// 包含 user_id
					if userId, ok := userMap["user_id"].(string); ok {
						hasher.Write([]byte(fmt.Sprintf("deduser_id:%s", userId)))
					}

					// 包含 user_name
					if userName, ok := userMap["user_name"].(string); ok {
						hasher.Write([]byte(fmt.Sprintf("deduser_name:%s", userName)))
					}

					// 包含 virtual_ips
					if virtualIps, ok := userMap["virtual_ips"].([]interface{}); ok {
						sort.Slice(virtualIps, func(i, j int) bool {
							ip1, _ := virtualIps[i].(string)
							ip2, _ := virtualIps[j].(string)
							return ip1 < ip2
						})
						for _, ip := range virtualIps {
							if ipStr, ok := ip.(string); ok {
								hasher.Write([]byte(fmt.Sprintf("deduser_ip:%s", ipStr)))
							}
						}
					}

					// 包含 priority
					if priority, ok := userMap["priority"].(float64); ok {
						hasher.Write([]byte(fmt.Sprintf("deduser_priority:%v", priority)))
					}
				}
			}
		}
	}

	hash := hasher.Sum(nil)
	return hex.EncodeToString(hash)[:16] // 只取前16位
}

// cleanExpiredCache 清理过期缓存
func cleanExpiredCache() {
	now := time.Now()
	for key, expiry := range cacheExpiry {
		if now.After(expiry) {
			delete(configHashCache, key)
			delete(cacheExpiry, key)
		}
	}
}

// 转换平台配置为tun-server的DynamicConfig格式
func convertToDynamicConfig(pools []*pb.VirtualIPPoolConfig, globalSettings *pb.VirtualIPGlobalSettings) DynamicConfig {
	// global.Logger.Sugar().Debugf("开始转换 %d 个虚拟IP池配置", len(pools))

	globalEnabled := globalSettings != nil && globalSettings.Enabled
	// global.Logger.Sugar().Debugf("虚拟IP全局功能开关: %v", globalEnabled)

	if !globalEnabled {
		global.Logger.Sugar().Debug("虚拟IP功能在平台端被禁用，返回空配置")
		return DynamicConfig{
			Enabled:    false,
			Pools:      make([]DynamicPoolConfig, 0),
			ConfigHash: "",
		}
	}
	// 初始化 DynamicConfig
	config := DynamicConfig{
		Enabled:    len(pools) > 0,
		Pools:      make([]DynamicPoolConfig, 0, len(pools)),
		ConfigHash: "",
	}

	for i, pool := range pools {
		if pool == nil {
			global.Logger.Sugar().Warnf("跳过第 %d 个空的池配置", i)
			continue
		}

		global.Logger.Sugar().Debugf("处理池配置 [%d]: 名称=%s, IP范围=%s, 类型=%s",
			i, pool.Name, pool.IpRange, pool.PoolType)

		// 添加调试信息：打印原始池配置中的关键字段
		// global.Logger.Sugar().Debugf("池 %s DirectoryConfigs长度: %d", pool.Name, len(pool.DirectoryConfigs))

		// 设置时间默认值
		ipExpiryDuration := pool.IpExpiryDuration
		if ipExpiryDuration < 0 {
			ipExpiryDuration = 86400 // 默认24小时
		}

		cleanupInterval := pool.CleanupInterval
		if cleanupInterval <= 0 {
			cleanupInterval = 300 // 默认5分钟
		}

		dynPool := DynamicPoolConfig{
			Name:             pool.Name,
			IpRange:          pool.IpRange,
			IpExpiryDuration: ipExpiryDuration,
			CleanupInterval:  cleanupInterval,
			PoolType:         pool.PoolType,
			Enabled:          true,
			DedicatedConfigs: make([]DynamicDedicatedConfig, 0),
		}

		// 安全处理 AllocationPolicy
		if pool.AllocationPolicy != nil {
			dynPool.AllocationStrategy = pool.AllocationPolicy.Strategy
			dynPool.MaxIpsPerUser = pool.AllocationPolicy.MaxIpsPerUser
		} else {
			dynPool.AllocationStrategy = "round_robin"
			dynPool.MaxIpsPerUser = 1
		}

		dedicatedCount := 0

		// 从 DirectoryConfigs 字段解析（修复后的方式）
		if len(pool.DirectoryConfigs) > 0 {
			global.Logger.Sugar().Debugf("池 %s 处理 %d 个DirectoryConfig", pool.Name, len(pool.DirectoryConfigs))

			for j, dirConfig := range pool.DirectoryConfigs {
				if dirConfig == nil || dirConfig.Config == "" {
					global.Logger.Sugar().Warnf("池 %s DirectoryConfig[%d] 为空", pool.Name, j)
					continue
				}

				global.Logger.Sugar().Debugf("池 %s DirectoryConfig[%d]: %s", pool.Name, j, dirConfig.Config)

				var directoryConfig DirectoryConfigData
				if err := json.Unmarshal([]byte(dirConfig.Config), &directoryConfig); err != nil {
					global.Logger.Sugar().Errorf("解析池 %s 的DirectoryConfig[%d]失败: %v", pool.Name, j, err)
					continue
				}

				if pool.PoolType == "shared" {
					// 共享池：保留完整的DirectoryConfig
					if len(directoryConfig.ExpandedUserIds) > 0 {
						directoryConfig.UserIdHashSet = make(map[string]bool)
						for _, userId := range directoryConfig.ExpandedUserIds {
							directoryConfig.UserIdHashSet[userId] = true
						}
						global.Logger.Sugar().Debugf("共享池 %s 构建用户Hash表: %d个用户",
							pool.Name, len(directoryConfig.ExpandedUserIds))
					}

					dynPool.DirectoryConfigs = append(dynPool.DirectoryConfigs, directoryConfig)
					global.Logger.Sugar().Debugf("共享池 %s 保留DirectoryConfig[%d]: policy=%s, departments=%d个",
						pool.Name, j, directoryConfig.Policy, len(directoryConfig.Departments))

				} else if pool.PoolType == "dedicated" {
					// 独享池：提取DedicatedUsers转换为DedicatedConfig
					if len(directoryConfig.DedicatedUsers) > 0 {
						global.Logger.Sugar().Debugf("独享池 %s DirectoryConfig[%d] 找到 %d 个独享用户配置", pool.Name, j, len(directoryConfig.DedicatedUsers))

						for _, user := range directoryConfig.DedicatedUsers {
							dynDedicated := user.toDynamicDedicatedConfig()
							dynPool.DedicatedConfigs = append(dynPool.DedicatedConfigs, dynDedicated)
							dedicatedCount++
							global.Logger.Sugar().Debugf("独享池 %s 从DirectoryConfig添加独享IP: 用户=%s(%s), IP=%v, 优先级=%d",
								pool.Name, user.UserName, user.UserId, user.VirtualIps, user.Priority)
						}
					} else {
						global.Logger.Sugar().Warnf("独享池 %s 的DirectoryConfig[%d]中没有找到dedicated_users字段", pool.Name, j)
					}
				}
			}
		}

		// 如果是独享池但没有配置，记录警告
		if pool.PoolType == "dedicated" && dedicatedCount == 0 {
			global.Logger.Sugar().Warnf("独享池 %s 没有找到任何独享用户配置！DirectoryConfigs长度=%d",
				pool.Name, len(pool.DirectoryConfigs))

			// 打印详细的DirectoryConfigs内容用于调试
			for j, dirConfig := range pool.DirectoryConfigs {
				if dirConfig != nil {
					global.Logger.Sugar().Debugf("DirectoryConfig[%d].Config: %s", j, dirConfig.Config)
				}
			}
		}

		// 添加完整的池配置调试输出
		// if poolBytes, err := json.MarshalIndent(dynPool, "", "  "); err == nil {
		// 	global.Logger.Sugar().Debugf("池 %s 完整配置:\n%s", pool.Name, string(poolBytes))
		// } else {
		// 	global.Logger.Sugar().Debugf("序列化池 %s 配置失败: %v", pool.Name, err)
		// }
		// global.Logger.Sugar().Debugf("池 %s 配置完成: 类型=%s, 独享配置数=%d", pool.Name, pool.PoolType, dedicatedCount)
		config.Pools = append(config.Pools, dynPool)
	}

	global.Logger.Sugar().Debugf("虚拟IP池配置转换完成: 启用=%v, 有效池数=%d", config.Enabled, len(config.Pools))

	// 添加最终配置的调试输出
	if configBytes, err := json.MarshalIndent(config, "", "  "); err == nil {
		global.Logger.Sugar().Debugf("最终转换配置: %s", string(configBytes))
	}

	return config
}
