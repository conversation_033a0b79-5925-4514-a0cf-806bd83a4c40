package common

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"net/url"
	"reflect"
	"time"

	"strings"
	"unicode"

	"asdsec.com/asec/platform/pkg/utils/jwt_util"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"

	"github.com/golang-jwt/jwt/v4"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/dto"
)

type authKey struct{}

// AccessConfigProvider 接口定义，用于获取访问配置
type AccessConfigProvider interface {
	GetAccessConfig(ctx context.Context) (AccessConfig, error)
}

// AccessConfig 访问配置结构体（避免直接依赖data包的model）
type AccessConfig struct {
	LanAddress      string
	InternetAddress string
	AliasAddresses  string
}

// 全局的配置提供者，通过依赖注入设置
var globalAccessConfigProvider AccessConfigProvider

// SetAccessConfigProvider 设置访问配置提供者
func SetAccessConfigProvider(provider AccessConfigProvider) {
	globalAccessConfigProvider = provider
}

func IsSubset(subset []string, parent []string) bool {
	m := make(map[string]struct{})
	for _, p := range parent {
		m[p] = struct{}{}
	}

	for _, s := range subset {
		if _, ok := m[s]; !ok {
			return false
		}
	}
	return true
}

// containsChinese 判断字符串中是否包含中文
func containsChinese(s string) bool {
	for _, r := range s {
		if unicode.Is(unicode.Scripts["Han"], r) {
			return true
		}
	}
	return false
}

// MaskPhone 主要考虑11位手机号以及+86手机号场景。 其它+1 或者+210这未覆盖测试
func MaskPhone(phone string) string {
	if len(phone) >= 11 && !containsChinese(phone) {
		return phone[:len(phone)-8] + "****" + phone[len(phone)-4:]
	}
	return phone
}

func IsInSlice(elem string, parent []string) bool {
	for _, p := range parent {
		if elem == p {
			return true
		}
	}
	return false
}

func GetCorpId(ctx context.Context) (string, error) {
	corpId := ctx.Value(dto.HeaderCorpKey)
	if corpId == nil {
		return "", pb.ErrorRecordNotFound("corpId key=%v not set", dto.HeaderCorpKey)
	}
	corpIdStr, ok := corpId.(string)
	if !ok {
		return "", pb.ErrorTypeError("corpId type=%T error.", corpId)
	}
	return corpIdStr, nil
}

func GetClientHost(ctx context.Context) string {
	if header, ok := transport.FromServerContext(ctx); ok {
		if header.Kind() == transport.KindHTTP {
			if info, ok := header.(*http.Transport); ok {
				// http打印信息
				request := info.Request()
				// 先从X-Forwarded-For中获取源IP
				clientIp := request.Header.Get("X-Forwarded-For")
				if clientIp != "" {
					return clientIp
				}
				//再从remote addr拿
				addr := strings.Split(request.RemoteAddr, ":")
				return addr[0]
			}
		}
	}
	return ""
}

func GetToken(ctx context.Context) (string, error) {
	header, ok := transport.FromServerContext(ctx)
	if !ok {
		return "", pb.ErrorRecordNotFound("header not found")
	}
	auths := strings.SplitN(header.RequestHeader().Get(dto.AuthorizationKey), " ", 2)
	if len(auths) != 2 || !strings.EqualFold(auths[0], dto.TokenTypeBear) {
		return "", pb.ErrorParamError("auth=%v format is wrong", auths)
	}
	return auths[1], nil
}

func GetUserId(ctx context.Context) (string, error) {
	token, ok := GetClaim(ctx)
	if !ok {
		return "", pb.ErrorRecordNotFound("claim not found")
	}
	tokenClaims, ok := token.(*jwt_util.TokenClaims)
	if !ok {
		return "", pb.ErrorRecordNotFound("claim type not support")
	}
	return tokenClaims.Subject, nil
}

func GetUserName(ctx context.Context) (string, error) {
	token, ok := GetClaim(ctx)
	if !ok {
		return "", pb.ErrorRecordNotFound("claim not found")
	}
	tokenClaims, ok := token.(*jwt_util.TokenClaims)
	if !ok {
		return "", pb.ErrorRecordNotFound("claim type not support")
	}
	return tokenClaims.Name, nil
}

func SetClaim(ctx context.Context, info jwt.Claims) context.Context {
	return context.WithValue(ctx, authKey{}, info)
}

func GetClaim(ctx context.Context) (token jwt.Claims, ok bool) {
	token, ok = ctx.Value(authKey{}).(jwt.Claims)
	return
}

func GetLimitOffset(limit, offset uint32) (retLimit uint32, retOffset uint32) {
	retLimit, retOffset = limit, offset
	if limit <= 0 {
		retLimit = dto.DefaultLimit
	}
	if offset < 0 {
		retOffset = dto.DefaultOffset
	}
	return
}

func GetDefaultUniqName(name string) string {
	return fmt.Sprintf("%s_%s", name, "默认策略")
}

func GetNextTime(syncCycle int32, syncUnit dto.SyncUnit) (time.Time, error) {
	if syncCycle <= 0 {
		return time.Time{}, fmt.Errorf("syncCycle=%v should >0", syncCycle)
	}
	switch syncUnit {
	case dto.SyncUnitMin:
		return time.Now().Add(time.Duration(syncCycle) * time.Minute).UTC(), nil
	case dto.SyncUnitHour:
		return time.Now().Add(time.Duration(syncCycle) * time.Hour).UTC(), nil
	case dto.SyncUnitDay:
		return time.Now().Add(time.Duration(syncCycle) * time.Hour * 24).UTC(), nil
	default:
		return time.Time{}, fmt.Errorf("syncUnit=%v not support", syncUnit)
	}
}

func GetIDPTypeName(idpType dto.IDPType) string {
	var name string
	name, ok := dto.MainIDPTypeName[idpType]
	if ok {
		return name
	}
	name, ok = dto.AssistIDPTypeName[idpType]
	if ok {
		return name
	}
	return ""
}

func SnakeToBigCamel(snakeCase string) string {
	words := strings.Split(snakeCase, "_")
	for i := range words {
		words[i] = strings.Title(words[i])
	}
	return strings.Join(words, "")
}

func FieldMapReflect(src interface{}, fieldMap []dto.KV, dstType reflect.Type) interface{} {
	srcValue := reflect.ValueOf(src)
	if srcValue.Kind() != reflect.Struct {
		return nil
	}

	dstValue := reflect.New(dstType).Elem()

	for _, kv := range fieldMap {
		srcField := srcValue.FieldByName(SnakeToBigCamel(kv.Key))
		dstField := dstValue.FieldByName(SnakeToBigCamel(kv.Value))

		if !srcField.IsValid() || !dstField.IsValid() {
			continue
		}

		dstField.Set(srcField)
	}

	return dstValue.Interface()
}

func GetStructFieldValue(s interface{}, key string) (string, error) {
	v := reflect.ValueOf(s)

	if v.Kind() != reflect.Struct {
		return "", pb.ErrorParamError("input should be a struct")
	}

	field := v.FieldByName(SnakeToBigCamel(key))

	if !field.IsValid() {
		return "", pb.ErrorRecordNotFound("Field %s not found", key)
	}

	value := field.Interface()
	if stringValue, ok := value.(string); ok {
		return stringValue, nil
	}

	return fmt.Sprintf("%v", value), nil
}

func Encrypt(key []byte, plaintext []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)
	return hex.EncodeToString(ciphertext), nil
}

func Decrypt(key []byte, ciphertext string) ([]byte, error) {
	ciphertextBytes, err := hex.DecodeString(ciphertext)
	if err != nil {
		return nil, err
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	if len(ciphertextBytes) < aes.BlockSize {
		return nil, fmt.Errorf("ciphertext too short")
	}
	iv := ciphertextBytes[:aes.BlockSize]
	ciphertextBytes = ciphertextBytes[aes.BlockSize:]
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertextBytes, ciphertextBytes)
	return ciphertextBytes, nil
}

// GetClientScheme 获取客户端请求的协议类型（http或https）
func GetClientScheme(ctx context.Context) string {
	if header, ok := transport.FromServerContext(ctx); ok {
		if header.Kind() == transport.KindHTTP {
			if info, ok := header.(*http.Transport); ok {
				request := info.Request()

				// 首先检查X-Forwarded-Proto头（用于反向代理场景）
				scheme := request.Header.Get("X-Forwarded-Proto")
				if scheme != "" {
					return scheme
				}

				// 其次检查请求本身是否使用TLS
				if request.TLS != nil {
					return "https"
				}

				// 最后默认使用http
				return "http"
			}
		}
	}
	// 如果无法确定，默认返回http
	return "http"
}

// GetClientHostPort 获取客户端请求的主机和端口（简单版本）
// 注意：如需要根据访问配置智能匹配地址，请使用GetClientHostPortWithConfig()
func GetClientHostPort(ctx context.Context) string {
	if header, ok := transport.FromServerContext(ctx); ok {
		if header.Kind() == transport.KindHTTP {
			if info, ok := header.(*http.Transport); ok {
				request := info.Request()

				// 首先尝试从Host头获取
				host := request.Host
				if host != "" {
					return host
				}

				// 如果Host头不存在，尝试从X-Forwarded-Host获取
				forwardedHost := request.Header.Get("X-Forwarded-Host")
				if forwardedHost != "" {
					return forwardedHost
				}

				// 最后使用RemoteAddr
				return request.RemoteAddr
			}
		}
	}
	return ""
}

// GetClientHostPortWithConfig 根据访问配置获取客户端主机和端口
func GetClientHostPortWithConfig(ctx context.Context) string {
	// 1. 首先获取请求中的 Host
	host := GetClientHostPort(ctx)
	if host == "" {
		return ""
	}

	// 2. 如果没有配置提供者，直接返回原始 host
	if globalAccessConfigProvider == nil {
		return host
	}

	// 3. 获取访问配置
	accessConfig, err := globalAccessConfigProvider.GetAccessConfig(ctx)
	if err != nil {
		// 如果获取配置失败，返回原始 host
		return host
	}

	// 4. 尝试匹配配置中的地址并获取端口
	matchedAddress := matchAddressWithPort(host, accessConfig)
	if matchedAddress != "" {
		return matchedAddress
	}

	// 5. 如果都不匹配，使用原始的 host
	return host
}

// matchAddressWithPort 匹配地址并返回带端口的完整地址
func matchAddressWithPort(host string, config AccessConfig) string {
	// 提取 host 中的主机名（不包含端口）
	hostOnly := host
	if idx := strings.Index(host, ":"); idx > 0 {
		hostOnly = host[:idx]
	}

	// 准备要检查的地址列表
	addresses := []string{}
	if config.LanAddress != "" {
		addresses = append(addresses, config.LanAddress)
	}
	if config.InternetAddress != "" {
		addresses = append(addresses, config.InternetAddress)
	}
	if config.AliasAddresses != "" {
		// 假设别名地址用逗号分隔
		aliases := strings.Split(config.AliasAddresses, ",")
		for _, alias := range aliases {
			alias = strings.TrimSpace(alias)
			if alias != "" {
				addresses = append(addresses, alias)
			}
		}
	}

	// 遍历配置的地址，找到匹配的主机名并返回完整地址（包含端口）
	for _, addr := range addresses {
		if addr == "" {
			continue
		}

		// 解析配置的地址（处理带协议头的情况）
		parsedURL, err := url.Parse(addr)
		if err != nil {
			// 如果解析失败，尝试添加协议头重新解析
			parsedURL, err = url.Parse("//" + addr)
			if err != nil {
				// 如果还是解析失败，尝试直接字符串匹配
				if strings.Contains(addr, hostOnly) {
					return extractHostPort(addr)
				}
				continue
			}
		}

		configHost := parsedURL.Hostname()
		configPort := parsedURL.Port()

		// 如果主机名匹配
		if strings.EqualFold(hostOnly, configHost) {
			// 返回主机名和端口的组合
			if configPort != "" {
				return configHost + ":" + configPort
			} else {
				// 如果配置中没有端口，保持原始 host 的格式
				return host
			}
		}
	}

	return ""
}

// extractHostPort 从地址字符串中提取主机名和端口
func extractHostPort(addr string) string {
	// 移除协议头
	addr = strings.TrimPrefix(addr, "http://")
	addr = strings.TrimPrefix(addr, "https://")

	// 移除路径部分
	if idx := strings.Index(addr, "/"); idx > 0 {
		addr = addr[:idx]
	}

	return addr
}

// GetClientScheme 获取客户端请求的协议类型（http或https）
func GetHttpRequest(ctx context.Context) (*http.Request, error) {
	if header, ok := transport.FromServerContext(ctx); ok {
		if header.Kind() == transport.KindHTTP {
			if info, ok := header.(*http.Transport); ok {
				return info.Request(), nil
			}
		}
	}
	// 如果无法确定，默认返回http
	return nil, fmt.Errorf("http request not found")
}

// ReplaceEnvVars 替换字符串中的环境变量占位符
func ReplaceEnvVars(ctx context.Context, input string, additionalVars ...map[string]string) string {
	if input == "" {
		return input
	}

	// 准备环境变量
	now := time.Now()
	variables := map[string]string{
		"Env.Time":  fmt.Sprintf("%d", now.Unix()),
		"Env.MTime": fmt.Sprintf("%d", now.UnixNano()/int64(time.Millisecond)),
		"Env.IP":    GetClientHost(ctx),
	}

	// 处理主机名
	host := GetClientHostPort(ctx)
	if host != "" {
		if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
			variables["Env.Host"] = host[:colonIndex]
		} else {
			variables["Env.Host"] = host
		}

		scheme := GetClientScheme(ctx)
		if scheme != "" {
			variables["Env.Scheme"] = scheme
		}
	}

	// 添加额外的变量（包括Global变量）
	for _, extraVars := range additionalVars {
		for k, v := range extraVars {
			// 直接添加完整变量名（不添加前缀）
			variables[k] = v
		}
	}

	// 执行替换
	result := input
	for k, v := range variables {
		placeholder := "{" + k + "}"
		result = strings.Replace(result, placeholder, v, -1)
	}

	return result
}

// maskPhoneNumber 对手机号进行掩码处理
func MaskPhoneNumber(phone string) string {
	if len(phone) <= 7 {
		return phone
	}
	return phone[:3] + "****" + phone[len(phone)-4:]
}

// maskEmailAddress 对邮箱地址进行掩码处理
func MaskEmailAddress(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email
	}
	username := parts[0]
	domain := parts[1]

	if len(username) <= 3 {
		return username + "***@" + domain
	}
	return username[:3] + "***@" + domain
}
