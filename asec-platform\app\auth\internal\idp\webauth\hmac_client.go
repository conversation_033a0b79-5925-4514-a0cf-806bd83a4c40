package webauth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"sort"
	"strings"
)

// HmacClient HMAC认证客户端
type HmacClient struct {
	Domain     string
	AccessKey  string
	SecretKey  string
	ApiName    string
	Parameters map[string]string
	Headers    map[string]string
	ClientIP   string
	ClientMAC  string
}

// NewHmacClient 创建新的HMAC认证客户端
func NewHmacClient() *HmacClient {
	return &HmacClient{
		Parameters: make(map[string]string),
		Headers:    make(map[string]string),
		ClientIP:   "127.0.0.1",
		ClientMAC:  "00:00:00:00:00:00",
	}
}

// SetDomain 设置域名
func (c *HmacClient) SetDomain(domain string) *HmacClient {
	c.Domain = domain
	return c
}

// SetAccessKey 设置访问密钥
func (c *HmacClient) SetAccessKey(accessKey string) *HmacClient {
	c.AccessKey = accessKey
	return c
}

// SetSecretKey 设置密钥
func (c *HmacClient) SetSecretKey(secretKey string) *HmacClient {
	c.SecretKey = secretKey
	return c
}

// SetApiName 设置API名称
func (c *HmacClient) SetApiName(apiName string) *HmacClient {
	c.ApiName = apiName
	return c
}

// SetClientIP 设置客户端IP
func (c *HmacClient) SetClientIP(ip string) *HmacClient {
	c.ClientIP = ip
	return c
}

// SetClientMAC 设置客户端MAC地址
func (c *HmacClient) SetClientMAC(mac string) *HmacClient {
	c.ClientMAC = mac
	return c
}

// AddParameter 添加请求参数
func (c *HmacClient) AddParameter(key, value string) *HmacClient {
	c.Parameters[key] = value
	return c
}

// AddHeader 添加自定义头
func (c *HmacClient) AddHeader(key, value string) *HmacClient {
	c.Headers[key] = value
	return c
}

// GetParameters 获取格式化的参数字符串
func (c *HmacClient) GetParameters() string {
	// 对参数按键名排序
	keys := make([]string, 0, len(c.Parameters))
	for k := range c.Parameters {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构造参数字符串
	var parts []string
	for _, k := range keys {
		parts = append(parts, k+"="+c.Parameters[k])
	}

	return strings.Join(parts, "&")
}

// GetURL 获取完整URL
func (c *HmacClient) GetURL() string {
	if c.Domain == "" {
		return c.ApiName
	}
	// 确保域名和API路径之间有正确的斜杠连接
	if !strings.HasSuffix(c.Domain, "/") && !strings.HasPrefix(c.ApiName, "/") {
		return c.Domain + "/" + c.ApiName
	}
	if strings.HasSuffix(c.Domain, "/") && strings.HasPrefix(c.ApiName, "/") {
		return c.Domain + c.ApiName[1:]
	}
	return c.Domain + c.ApiName
}

// BuildDingTalkSignature 构建钉钉API标准签名
func BuildDingTalkSignature(method, timestamp, nonce, uri string, params map[string][]string, secret string) string {
	// 1. 标准化参数 - 按照key字典序排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 构建参数字符串
	var paramBuilder strings.Builder
	for i, key := range keys {
		if i > 0 {
			paramBuilder.WriteString("&")
		}

		// 获取该key的所有值并排序
		values := params[key]
		sort.Strings(values)

		for j, value := range values {
			if j > 0 {
				paramBuilder.WriteString("&")
			}
			paramBuilder.WriteString(key)
			paramBuilder.WriteString("=")
			paramBuilder.WriteString(value) // 签名时不做URL编码
		}
	}
	paramString := paramBuilder.String()

	// 3. 构建签名字符串
	signString := method + "\n" + timestamp + "\n" + nonce + "\n" + uri + "\n" + paramString

	// 4. 计算签名
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(signString))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}
