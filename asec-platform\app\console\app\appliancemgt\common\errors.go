package common

import "errors"

var (
	ErrParamError              = errors.New("param err")
	ErrRecordNotFound          = errors.New("record not found")
	ErrDurationUnitNotSupport  = errors.New("duration unit not support")
	ErrOssWrong                = errors.New("oss error")
	ErrFileParseWrong          = errors.New("file parse wrong")
	ErrConfigWrong             = errors.New("config err")
	ErrQueueIsNotAvailable     = errors.New("queue is not available")
	ErrSwitchStatusWrong       = errors.New("switch status not support")
	ErrVersionConflict         = errors.New("version conflict")
	ErrGrayModeNotSupport      = errors.New("gray mode not support")
	ErrUpgradeStatusNotSupport = errors.New("upgrade status not support")
	ErrUnSupportFileType       = errors.New("file type not support")
)
