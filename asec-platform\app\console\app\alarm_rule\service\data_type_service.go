package service

import (
	"asdsec.com/asec/platform/app/console/app/alarm_rule/model"
	"asdsec.com/asec/platform/app/console/app/alarm_rule/repository"
	"context"
	"sync"
	"time"
)

type DataTypeServiceImpl interface {
	GetDataType(ctx context.Context) ([]model.SensitiveStrategyDB, error)
	GetDataTypeSummary(ctx context.Context, startT, endT time.Time) ([]model.SensitiveStrategyDB, error)
}

// private
var dataTypeServInitPriv sync.Once
var dataTypeInstance DataTypeServiceImpl

type dataTypeServicePriv struct {
	db repository.DataTypeRepositoryImpl
}

func (d *dataTypeServicePriv) GetDataType(ctx context.Context) ([]model.SensitiveStrategyDB, error) {
	return d.db.GetDataType(ctx)
}

func (d *dataTypeServicePriv) GetDataTypeSummary(ctx context.Context, startT, endT time.Time) ([]model.SensitiveStrategyDB, error) {
	return d.db.GetDataTypeSummary(ctx, startT, endT)
}

func GetDataTypeService() DataTypeServiceImpl {
	dataTypeServInitPriv.Do(func() {
		dataTypeInstance = &dataTypeServicePriv{repository.NewDataTypeRepository()}
	})
	return dataTypeInstance
}
