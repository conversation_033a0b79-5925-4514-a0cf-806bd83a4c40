package logbeat

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"asdsec.com/asec/platform/pkg/utils"
	"go.uber.org/zap"
)

const (
	defaultPort = 3100
	bitPName    = "LogBit.exe"
	bitOldPName = "asec-bit.exe"
)

func StartLogBeat(ctx context.Context) (*os.Process, error) {
	// 拉起时先退出一下，避免sidecar被强杀后进程重复启动
	killBefore()

	// 拿到sidecar当前路径,获取bit二进制运行文件
	pwdDir := getExecDir()

	fluentBitPath := filepath.Join(pwdDir, "LogBit.exe")
	confPath := filepath.Join(pwdDir, "asec-bit.conf")
	logPath := filepath.Join(utils.GetConfigDir(), "log")
	// bit 自身的日志, bit自身不支持 自身日志轮转,仅debug打开吧
	/*bigLogPath := filepath.Join(logPath, "asec-bit.log")
	cmd := exec.Command(fluentBitPath, "-c", confPath, "-l", bigLogPath)*/
	cmd := exec.Command(fluentBitPath, "-c", confPath)

	setCmdEnv(cmd, logPath)

	global.Logger.Sugar().Infof("start with cmd: %s env: %v", cmd.String(), cmd.Env)
	// 启动子进程
	output, err := cmd.Output()
	if err != nil {
		global.Logger.Error("failed to start Logbeat:", zap.Error(err))
		return nil, err
	}
	// Print output to console
	global.Logger.Info(string(output))
	return cmd.Process, nil
}

func killBefore() {
	command := exec.Command("taskkill", "/f", "/t", "/im", bitPName)
	_ = command.Run()
	// 兼容下老版本,后续删除
	command1 := exec.Command("taskkill", "/f", "/t", "/im", bitOldPName)
	_ = command1.Run()
}

func setCmdEnv(cmd *exec.Cmd, logPath string) {
	//采集的日志级别
	logLevel := global.Conf.Logbeat.Level
	if logLevel == "" {
		logLevel = "warn"
	}
	// 优先使用平台地址
	host := global.PrivateIp
	if host == "" {
		host = global.Conf.Logbeat.Host
	}
	port := getLogbeatPort()

	// 获取用户信息
	userInfo := user.GetUserInfo()
	username := "unknown"
	if userInfo.UserName != "" {
		username = userInfo.UserName
	}

	//通过进程级别的环境变量设置fluentbit采集的参数
	cmd.Env = append([]string{}, "LOKI_HOST="+host, fmt.Sprintf("LOKI_PORT=%d", port),
		fmt.Sprintf("APPLIANCE_ID=%d", global.ApplianceID),
		"PATH="+logPath, "APPLIANCE_TYPE="+global.ApplianceType.String(),
		"USERNAME="+username)
}

func getExecDir() string {
	pwd := os.Args[0]
	pwdDir := filepath.Dir(pwd)
	return pwdDir
}

// 获取logbeat端口.优先从命令行获取，否则获取配置文件端口,都没有则返回默认端口
// TODO 使用服务发现接口替换
func getLogbeatPort() int {
	port := global.HttpsPort
	if port > 0 {
		return port
	}
	port = global.Conf.Logbeat.Port
	if port <= 0 {
		port = defaultPort
	}
	return port
}
