package access_log

import (
	"context"
	"io"
	"sync"
	"time"

	pb "asdsec.com/asec/platform/api/accesslog/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/recover_panic"
	"asdsec.com/asec/platform/pkg/utils"
	"go.uber.org/zap"
	"google.golang.org/grpc"
)

func StartSendAccessLog(ctx context.Context, wg *sync.WaitGroup) {
	defer utils.RecoverSideCarPanic(global.Logger, StartSendAccessLog, ctx, wg)
	ctx, cancel := context.WithCancel(ctx)
	defer wg.Done()
	defer cancel()
	for {
		conn, err := connection.GetLogCenterConnection(ctx)
		if err != nil {
			select {
			case <-ctx.Done():
				return
			case <-time.After(time.Second * 5):
				continue
			}
		}
		SendAccessLog(ctx, conn)
	}
}

// SendAccessLog 发送接入日志
func SendAccessLog(ctx context.Context, conn *grpc.ClientConn) {
	subWg := &sync.WaitGroup{}
	defer subWg.Wait()
	for {
		subCtx, cancel := context.WithCancel(ctx)
		subWg.Add(1)
		go handleSend(subCtx, subWg, conn)
		subWg.Wait()

		cancel()
		global.Logger.Warn("transfer has been canceled,wait next try to transfer for 5 seconds")

		select {
		case <-subCtx.Done():
			global.Logger.Sugar().Debugf("<-subCtx.Done")
			return
		case <-ctx.Done():
			global.Logger.Sugar().Debugf("<-ctx.Done()")
			return
		case <-time.After(time.Second * 5):
			global.Logger.Sugar().Debugf("<-time.After(time.Second * 5)")
		}
	}
}

func handleSend(ctx context.Context, wg *sync.WaitGroup, conn *grpc.ClientConn) {
	defer wg.Done()
	defer recover_panic.RecoverAccessLogSend(global.Logger, handleSend, ctx, wg, conn)

	if conn == nil {
		global.Logger.Error("grpc conn is nil when handling access log send")
		return
	}
	interval := global.Conf.AccessLog.Interval
	if interval < 5 {
		interval = 5
	}
	ticker := time.NewTicker(time.Duration(interval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			global.Logger.Sugar().Debugf("<-ctx.Done()")
			return
		case <-ticker.C:
			{
				recs := ReadLogRecords() // TODO:每次读取都会刷新offset，如果发送失败会导致数据丢失，待修复
				if len(recs) != 0 {
					global.Logger.Sugar().Debugf("start send recs=%+v", recs)

					client, err := pb.NewAccessLogServiceClient(conn).AccessLog(context.Background())
					if err != nil {
						if client != nil {
							client.CloseAndRecv()
						}
						global.Logger.Error("get client err:", zap.Error(err))
						return
					}

					err = client.Send(&pb.AccessLogMsgList{AccessLogMsg: recs, ApplianceId: global.ApplianceID})
					if err != nil {
						if client != nil {
							client.CloseAndRecv()
						}
						if err != io.EOF {
							global.Logger.Error("send accessLog err:", zap.Error(err))
						} else {
							global.Logger.Sugar().Warn("get io.EOF error. may be server close stream")
						}
						return
					}
					rev, err := client.CloseAndRecv()
					if err != nil {
						if err != io.EOF {
							global.Logger.Error("send accessLog err:", zap.Error(err))
						} else {
							global.Logger.Sugar().Warn("get io.EOF error. may be server close stream")
						}
						return
					}
					if rev.Status != pb.AccessLogReply_SUCCESS {
						global.Logger.Sugar().Warnf("access log send reply:%v", rev.Status)
					}
					ClearAccessLogsPool(recs)
				}
			}
		}
	}
}
