package app_matcher

import pb "asdsec.com/asec/platform/api/application/v1"

type IpMatcherAdapter struct {
	baseMatcher BaseMatcher
}

func (i IpMatcherAdapter) GetAppInfo() *pb.AppInfo {
	return i.baseMatcher.AppInfo
}

func (i IpMatcherAdapter) GetMatcherType() UrlMatcherType {
	return IpMatcher
}

func (i IpMatcherAdapter) DoMatcher(srcUrl string, srcPort int) error {
	if !(srcUrl == i.baseMatcher.UrlRule) {
		return i.baseMatcher.NoMatchError(srcUrl, srcPort, "")
	}

	matchPort, detail := i.baseMatcher.CommonMatchPort(srcPort)
	if !matchPort {
		return i.baseMatcher.NoMatchError(srcUrl, srcPort, detail)
	}
	return nil
}

func NewIpMatcherAdapter(appInfo *pb.AppInfo) (IpMatcherAdapter, error) {
	ps, err := GetPortMatchers(appInfo.Port)
	if err != nil {
		return IpMatcherAdapter{}, err
	}

	return IpMatcherAdapter{
		baseMatcher: BaseMatcher{
			UrlRule:  appInfo.Address,
			PortRule: ps,
			AppInfo:  appInfo,
		},
	}, nil
}
