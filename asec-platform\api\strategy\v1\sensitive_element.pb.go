// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: strategy/v1/sensitive_element.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetElemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetElemReq) Reset() {
	*x = GetElemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strategy_v1_sensitive_element_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetElemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetElemReq) ProtoMessage() {}

func (x *GetElemReq) ProtoReflect() protoreflect.Message {
	mi := &file_strategy_v1_sensitive_element_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetElemReq.ProtoReflect.Descriptor instead.
func (*GetElemReq) Descriptor() ([]byte, []int) {
	return file_strategy_v1_sensitive_element_proto_rawDescGZIP(), []int{0}
}

func (x *GetElemReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetElemResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info []*SenElemInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
}

func (x *GetElemResp) Reset() {
	*x = GetElemResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strategy_v1_sensitive_element_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetElemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetElemResp) ProtoMessage() {}

func (x *GetElemResp) ProtoReflect() protoreflect.Message {
	mi := &file_strategy_v1_sensitive_element_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetElemResp.ProtoReflect.Descriptor instead.
func (*GetElemResp) Descriptor() ([]byte, []int) {
	return file_strategy_v1_sensitive_element_proto_rawDescGZIP(), []int{1}
}

func (x *GetElemResp) GetInfo() []*SenElemInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type SenElemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Desc     string      `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	RuleList []*RuleList `protobuf:"bytes,3,rep,name=rule_list,json=ruleList,proto3" json:"rule_list,omitempty"`
}

func (x *SenElemInfo) Reset() {
	*x = SenElemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strategy_v1_sensitive_element_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SenElemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SenElemInfo) ProtoMessage() {}

func (x *SenElemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_strategy_v1_sensitive_element_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SenElemInfo.ProtoReflect.Descriptor instead.
func (*SenElemInfo) Descriptor() ([]byte, []int) {
	return file_strategy_v1_sensitive_element_proto_rawDescGZIP(), []int{2}
}

func (x *SenElemInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SenElemInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SenElemInfo) GetRuleList() []*RuleList {
	if x != nil {
		return x.RuleList
	}
	return nil
}

type RuleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  string   `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Value []string `protobuf:"bytes,2,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *RuleList) Reset() {
	*x = RuleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_strategy_v1_sensitive_element_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleList) ProtoMessage() {}

func (x *RuleList) ProtoReflect() protoreflect.Message {
	mi := &file_strategy_v1_sensitive_element_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleList.ProtoReflect.Descriptor instead.
func (*RuleList) Descriptor() ([]byte, []int) {
	return file_strategy_v1_sensitive_element_proto_rawDescGZIP(), []int{3}
}

func (x *RuleList) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RuleList) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_strategy_v1_sensitive_element_proto protoreflect.FileDescriptor

var file_strategy_v1_sensitive_element_proto_rawDesc = []byte{
	0x0a, 0x23, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x22, 0x25, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x0b, 0x47, 0x65,
	0x74, 0x45, 0x6c, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x2e, 0x53, 0x65, 0x6e, 0x45, 0x6c, 0x65, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x66, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x45,
	0x6c, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x33, 0x0a, 0x09, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x34, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x54, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x6e,
	0x45, 0x6c, 0x65, 0x6d, 0x12, 0x46, 0x0a, 0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x6e, 0x45, 0x6c, 0x65, 0x6d, 0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x42, 0x2d, 0x5a, 0x2b,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_strategy_v1_sensitive_element_proto_rawDescOnce sync.Once
	file_strategy_v1_sensitive_element_proto_rawDescData = file_strategy_v1_sensitive_element_proto_rawDesc
)

func file_strategy_v1_sensitive_element_proto_rawDescGZIP() []byte {
	file_strategy_v1_sensitive_element_proto_rawDescOnce.Do(func() {
		file_strategy_v1_sensitive_element_proto_rawDescData = protoimpl.X.CompressGZIP(file_strategy_v1_sensitive_element_proto_rawDescData)
	})
	return file_strategy_v1_sensitive_element_proto_rawDescData
}

var file_strategy_v1_sensitive_element_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_strategy_v1_sensitive_element_proto_goTypes = []interface{}{
	(*GetElemReq)(nil),  // 0: api.strategy.GetElemReq
	(*GetElemResp)(nil), // 1: api.strategy.GetElemResp
	(*SenElemInfo)(nil), // 2: api.strategy.SenElemInfo
	(*RuleList)(nil),    // 3: api.strategy.RuleList
}
var file_strategy_v1_sensitive_element_proto_depIdxs = []int32{
	2, // 0: api.strategy.GetElemResp.info:type_name -> api.strategy.SenElemInfo
	3, // 1: api.strategy.SenElemInfo.rule_list:type_name -> api.strategy.RuleList
	0, // 2: api.strategy.GetSenElem.AgentGetSenElem:input_type -> api.strategy.GetElemReq
	1, // 3: api.strategy.GetSenElem.AgentGetSenElem:output_type -> api.strategy.GetElemResp
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_strategy_v1_sensitive_element_proto_init() }
func file_strategy_v1_sensitive_element_proto_init() {
	if File_strategy_v1_sensitive_element_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_strategy_v1_sensitive_element_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetElemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_strategy_v1_sensitive_element_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetElemResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_strategy_v1_sensitive_element_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SenElemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_strategy_v1_sensitive_element_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_strategy_v1_sensitive_element_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_strategy_v1_sensitive_element_proto_goTypes,
		DependencyIndexes: file_strategy_v1_sensitive_element_proto_depIdxs,
		MessageInfos:      file_strategy_v1_sensitive_element_proto_msgTypes,
	}.Build()
	File_strategy_v1_sensitive_element_proto = out.File
	file_strategy_v1_sensitive_element_proto_rawDesc = nil
	file_strategy_v1_sensitive_element_proto_goTypes = nil
	file_strategy_v1_sensitive_element_proto_depIdxs = nil
}
