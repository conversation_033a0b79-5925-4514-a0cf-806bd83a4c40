package model

import "time"

type GetAdminRsp struct {
	Id               string       `gorm:"column:id" json:"id"`
	Name             string       `gorm:"column:name" json:"name"`
	CorpId           string       `gorm:"column:corp_id" json:"corp_id"`
	Phone            string       `gorm:"phone" json:"phone"`
	Email            string       `gorm:"email" json:"email"`
	Status           int          `gorm:"status" json:"status"`
	RoleId           string       `gorm:"role_id" json:"role_id"`
	RoleName         string       `gorm:"role_name" json:"role_name"`
	RoleType         string       `gorm:"role_type" json:"role_type"`
	CorpName         string       `gorm:"corp_name" json:"corp_name"`
	Desc             string       `gorm:"desc" json:"desc"`
	ExpireType       int          `gorm:"expire_type" json:"expire_type"`
	Permissions      []Permission `gorm:"-" json:"permissions"`
	PermissionsStr   string       `gorm:"permissions_str"  json:"-"`
	LoginFailedTimes int          `gorm:"login_failed_times" json:"login_failed_times"`
	ExpireTime       string       `gorm:"expire_time" json:"expire_time"`
	LockTime         string       `gorm:"lock_time" json:"lock_time"`
	CreateAt         time.Time    `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"-"`
	UpdateAt         time.Time    `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"-"`
}
