// Package service 提供sidecar对其他包的引用
package service

import (
	pb "asdsec.com/asec/platform/api/accesslog/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/access_log"
)

func GetAccessLogPool() *pb.AccessLogMsg {
	return access_log.Pool.Get().(*pb.AccessLogMsg)
}

func PutAccessLogPool(rec *pb.AccessLogMsg) {
	access_log.Pool.Put(rec)
}

// CollectAccessLog 收集接入日志
func CollectAccessLog(msg *pb.AccessLogMsg) {
	access_log.WriteLogRecord(msg)
}
