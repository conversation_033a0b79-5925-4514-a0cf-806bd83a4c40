// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newTbExternalDepartment(db *gorm.DB, opts ...gen.DOOption) tbExternalDepartment {
	_tbExternalDepartment := tbExternalDepartment{}

	_tbExternalDepartment.tbExternalDepartmentDo.UseDB(db, opts...)
	_tbExternalDepartment.tbExternalDepartmentDo.UseModel(&model.TbExternalDepartment{})

	tableName := _tbExternalDepartment.tbExternalDepartmentDo.TableName()
	_tbExternalDepartment.ALL = field.NewAsterisk(tableName)
	_tbExternalDepartment.LocalRootGroupID = field.NewString(tableName, "local_root_group_id")
	_tbExternalDepartment.LocalGroupID = field.NewString(tableName, "local_group_id")
	_tbExternalDepartment.ID = field.NewString(tableName, "id")
	_tbExternalDepartment.Name = field.NewString(tableName, "name")
	_tbExternalDepartment.Parentid = field.NewString(tableName, "parentid")
	_tbExternalDepartment.Order = field.NewInt64(tableName, "order")
	_tbExternalDepartment.CreatedAt = field.NewTime(tableName, "created_at")
	_tbExternalDepartment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbExternalDepartment.NameEn = field.NewString(tableName, "name_en")
	_tbExternalDepartment.Type = field.NewString(tableName, "type")
	_tbExternalDepartment.UniqKey = field.NewString(tableName, "uniq_key")

	_tbExternalDepartment.fillFieldMap()

	return _tbExternalDepartment
}

type tbExternalDepartment struct {
	tbExternalDepartmentDo tbExternalDepartmentDo

	ALL              field.Asterisk
	LocalRootGroupID field.String
	LocalGroupID     field.String
	ID               field.String
	Name             field.String
	Parentid         field.String
	Order            field.Int64
	CreatedAt        field.Time
	UpdatedAt        field.Time
	NameEn           field.String
	Type             field.String
	UniqKey          field.String

	fieldMap map[string]field.Expr
}

func (t tbExternalDepartment) Table(newTableName string) *tbExternalDepartment {
	t.tbExternalDepartmentDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbExternalDepartment) As(alias string) *tbExternalDepartment {
	t.tbExternalDepartmentDo.DO = *(t.tbExternalDepartmentDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbExternalDepartment) updateTableName(table string) *tbExternalDepartment {
	t.ALL = field.NewAsterisk(table)
	t.LocalRootGroupID = field.NewString(table, "local_root_group_id")
	t.LocalGroupID = field.NewString(table, "local_group_id")
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.Parentid = field.NewString(table, "parentid")
	t.Order = field.NewInt64(table, "order")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.NameEn = field.NewString(table, "name_en")
	t.Type = field.NewString(table, "type")
	t.UniqKey = field.NewString(table, "uniq_key")

	t.fillFieldMap()

	return t
}

func (t *tbExternalDepartment) WithContext(ctx context.Context) *tbExternalDepartmentDo {
	return t.tbExternalDepartmentDo.WithContext(ctx)
}

func (t tbExternalDepartment) TableName() string { return t.tbExternalDepartmentDo.TableName() }

func (t tbExternalDepartment) Alias() string { return t.tbExternalDepartmentDo.Alias() }

func (t *tbExternalDepartment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbExternalDepartment) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 11)
	t.fieldMap["local_root_group_id"] = t.LocalRootGroupID
	t.fieldMap["local_group_id"] = t.LocalGroupID
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["parentid"] = t.Parentid
	t.fieldMap["order"] = t.Order
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["name_en"] = t.NameEn
	t.fieldMap["type"] = t.Type
	t.fieldMap["uniq_key"] = t.UniqKey
}

func (t tbExternalDepartment) clone(db *gorm.DB) tbExternalDepartment {
	t.tbExternalDepartmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbExternalDepartment) replaceDB(db *gorm.DB) tbExternalDepartment {
	t.tbExternalDepartmentDo.ReplaceDB(db)
	return t
}

type tbExternalDepartmentDo struct{ gen.DO }

func (t tbExternalDepartmentDo) Debug() *tbExternalDepartmentDo {
	return t.withDO(t.DO.Debug())
}

func (t tbExternalDepartmentDo) WithContext(ctx context.Context) *tbExternalDepartmentDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbExternalDepartmentDo) ReadDB() *tbExternalDepartmentDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbExternalDepartmentDo) WriteDB() *tbExternalDepartmentDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbExternalDepartmentDo) Session(config *gorm.Session) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbExternalDepartmentDo) Clauses(conds ...clause.Expression) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbExternalDepartmentDo) Returning(value interface{}, columns ...string) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbExternalDepartmentDo) Not(conds ...gen.Condition) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbExternalDepartmentDo) Or(conds ...gen.Condition) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbExternalDepartmentDo) Select(conds ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbExternalDepartmentDo) Where(conds ...gen.Condition) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbExternalDepartmentDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbExternalDepartmentDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbExternalDepartmentDo) Order(conds ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbExternalDepartmentDo) Distinct(cols ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbExternalDepartmentDo) Omit(cols ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbExternalDepartmentDo) Join(table schema.Tabler, on ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbExternalDepartmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbExternalDepartmentDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbExternalDepartmentDo) Group(cols ...field.Expr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbExternalDepartmentDo) Having(conds ...gen.Condition) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbExternalDepartmentDo) Limit(limit int) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbExternalDepartmentDo) Offset(offset int) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbExternalDepartmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbExternalDepartmentDo) Unscoped() *tbExternalDepartmentDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbExternalDepartmentDo) Create(values ...*model.TbExternalDepartment) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbExternalDepartmentDo) CreateInBatches(values []*model.TbExternalDepartment, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbExternalDepartmentDo) Save(values ...*model.TbExternalDepartment) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbExternalDepartmentDo) First() (*model.TbExternalDepartment, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalDepartment), nil
	}
}

func (t tbExternalDepartmentDo) Take() (*model.TbExternalDepartment, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalDepartment), nil
	}
}

func (t tbExternalDepartmentDo) Last() (*model.TbExternalDepartment, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalDepartment), nil
	}
}

func (t tbExternalDepartmentDo) Find() ([]*model.TbExternalDepartment, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbExternalDepartment), err
}

func (t tbExternalDepartmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbExternalDepartment, err error) {
	buf := make([]*model.TbExternalDepartment, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbExternalDepartmentDo) FindInBatches(result *[]*model.TbExternalDepartment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbExternalDepartmentDo) Attrs(attrs ...field.AssignExpr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbExternalDepartmentDo) Assign(attrs ...field.AssignExpr) *tbExternalDepartmentDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbExternalDepartmentDo) Joins(fields ...field.RelationField) *tbExternalDepartmentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbExternalDepartmentDo) Preload(fields ...field.RelationField) *tbExternalDepartmentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbExternalDepartmentDo) FirstOrInit() (*model.TbExternalDepartment, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalDepartment), nil
	}
}

func (t tbExternalDepartmentDo) FirstOrCreate() (*model.TbExternalDepartment, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalDepartment), nil
	}
}

func (t tbExternalDepartmentDo) FindByPage(offset int, limit int) (result []*model.TbExternalDepartment, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbExternalDepartmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbExternalDepartmentDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbExternalDepartmentDo) Delete(models ...*model.TbExternalDepartment) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbExternalDepartmentDo) withDO(do gen.Dao) *tbExternalDepartmentDo {
	t.DO = *do.(*gen.DO)
	return t
}
