// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbSpecialConfig = "tb_special_config"

// TbSpecialConfig mapped from table <tb_special_config>
type TbSpecialConfig struct {
	ID        string    `gorm:"column:id;primaryKey" json:"id"`
	Type      string    `gorm:"column:type;not null" json:"type"`
	Value     string    `gorm:"column:value;not null" json:"value"`
	CreatedAt time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	Key       string    `gorm:"column:key" json:"key"`
}

// TableName TbSpecialConfig's table name
func (*TbSpecialConfig) TableName() string {
	return TableNameTbSpecialConfig
}
