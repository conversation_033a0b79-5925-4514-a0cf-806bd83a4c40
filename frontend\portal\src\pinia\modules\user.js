import {
  getUserInfo,
  login,
} from '@/api/user'
import { logout } from '@/api/logout'
import router from '@/router/index'
import { Loading, Message } from '@/components/base'
import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

import { auth_check } from '@/api/auth_wx'
import { post_verify } from '@/api/sms'
import agentApi from '@/api/agentApi'
import Cookies from 'js-cookie'
import { broadcastLogin, broadcastLogout } from '@/utils/loginSync'

export const useUserStore = defineStore('user', () => {
  const loadingInstance = ref(null)

  // 智能提取cookie domain的公共函数
  const getSmartCookieDomain = () => {
    const currentDomain = window.location.hostname
    let cookieDomain = currentDomain

    // 如果是IP地址或localhost，不设置domain
    if (!/^(\d{1,3}\.){3}\d{1,3}$/.test(currentDomain) && currentDomain !== 'localhost') {
      // 智能提取合适的主域名
      const domainParts = currentDomain.split('.')

      // 对于常见的顶级域名组合，需要保留更多部分
      const commonTLDs = ['com.cn', 'net.cn', 'org.cn', 'gov.cn', 'edu.cn', 'co.uk', 'co.jp']
      const lastTwoParts = domainParts.slice(-2).join('.')
      const lastThreeParts = domainParts.slice(-3).join('.')

      if (domainParts.length >= 4 && commonTLDs.includes(lastTwoParts)) {
        // 如果是 xxx.xxx.com.cn 这种格式，保留最后三部分
        cookieDomain = '.' + lastThreeParts
      } else if (domainParts.length > 2) {
        // 普通域名，保留最后两部分
        cookieDomain = '.' + domainParts.slice(-2).join('.')
      }

      // 安全检查：如果提取的域名部分太少，使用当前完整域名
      if (cookieDomain.split('.').length < 3) {
        cookieDomain = currentDomain
      }
    }

    return {
      currentDomain,
      cookieDomain,
      shouldSetDomain: cookieDomain !== currentDomain
    }
  }

  const userInfo = ref({
    id: '',
    name: '',
    groupId: '',
    groupName: '',
    corpId: '',
    sourceId: '',
    phone: '',
    email: '',
    avatar: '',
    roles: [],
    sideMode: 'dark',
    activeColor: '#4D70FF',
    baseColor: '#fff',
  })

  const token = ref(window.localStorage.getItem('token') || '')
  const loginType = ref(window.localStorage.getItem('loginType') || '')
  const tunState = ref(0)
  try {
    token.value = token.value ? JSON.parse(token.value) : ''

    // 如果localStorage中有token但Cookie中没有，设置Cookie
    if (token.value && token.value.accessToken) {
      const existingCookie = Cookies.get('asec_token')
      if (!existingCookie || existingCookie !== token.value.accessToken) {
        // 计算Cookie过期时间
        let cookieExpires = 7 // 默认7天

        if (token.value.refreshExpireIn && typeof token.value.refreshExpireIn === 'number') {
          cookieExpires = Math.max(1, Math.ceil(token.value.refreshExpireIn / 86400))
        } else if (token.value.expireIn && typeof token.value.expireIn === 'number') {
          cookieExpires = Math.max(1, Math.ceil(token.value.expireIn / 86400))
        }

        try {
          // 使用智能domain提取算法
          const { cookieDomain, shouldSetDomain } = getSmartCookieDomain()

          const cookieOptions = {
            expires: cookieExpires,
            secure: window.location.protocol === 'https:',
            sameSite: 'lax',
            path: '/'
          }

          // 只有在需要时才设置domain
          if (shouldSetDomain) {
            cookieOptions.domain = cookieDomain
          }

          Cookies.set('asec_token', token.value.accessToken, cookieOptions)
          logger.log(`初始化时同步token到Cookie，域名: ${cookieDomain}，过期时间: ${cookieExpires}天`)
        } catch (error) {
          logger.error('初始化时设置Cookie失败:', error)
        }
      } else {
        logger.log('Cookie已存在且与localStorage同步')
      }
    }
  } catch (e) {
    logger.log('---清理localStorage中的token---')
    window.localStorage.removeItem('token')
    Cookies.remove('asec_token')
    token.value = ''
  }

  const setTunState = (val) => {
    tunState.value = val
  }

  const setUserInfo = (val) => {
    userInfo.value = val
  }

  const setToken = (val) => {
    logger.log('setToken 被调用，参数:', val)
    token.value = val
    logger.log('token.value 已设置为:', token.value)

    // 同时设置Cookie，确保后端可以从Cookie中读取token
    if (val && val.accessToken) {
      logger.log('开始设置Cookie，accessToken:', val.accessToken)
      // 计算Cookie过期时间：优先使用refreshToken的过期时间，否则默认7天
      let cookieExpires = 7 // 默认7天

      if (val.refreshExpireIn && typeof val.refreshExpireIn === 'number') {
        // 使用refreshToken过期时间，因为它通常比accessToken有效期更长
        cookieExpires = Math.max(1, Math.ceil(val.refreshExpireIn / 86400))
      } else if (val.expireIn && typeof val.expireIn === 'number') {
        // 如果没有refreshToken过期时间，使用accessToken过期时间
        cookieExpires = Math.max(1, Math.ceil(val.expireIn / 86400)) // 至少1天
      }

      try {
        // 使用智能domain提取算法
        const { cookieDomain, shouldSetDomain } = getSmartCookieDomain()

        const cookieOptions = {
          expires: cookieExpires, // 根据token过期时间动态设置
          secure: window.location.protocol === 'https:', // 在HTTPS下使用secure
          sameSite: 'lax', // 允许跨站请求携带Cookie
          path: '/' // 确保在所有路径下都能访问cookie
        }

        // 只有在需要时才设置domain
        if (shouldSetDomain) {
          cookieOptions.domain = cookieDomain
        }

        Cookies.set('asec_token', val.accessToken, cookieOptions)

        // 验证Cookie是否设置成功
        const setCookie = Cookies.get('asec_token')
        if (setCookie === val.accessToken) {
          logger.log(`认证成功时设置asec_token Cookie，域名: ${cookieDomain}，过期时间: ${cookieExpires}天`)
        } else {
          logger.warn('Cookie设置可能失败，请检查浏览器Cookie设置')
        }
      } catch (error) {
        logger.error('设置Cookie时出错:', error)
      }
    } else {
      // 清除Cookie
      try {
        Cookies.remove('asec_token')
        // 验证Cookie是否清除成功
        const removedCookie = Cookies.get('asec_token')
        if (!removedCookie) {
          logger.log('已成功清除asec_token Cookie')
        } else {
          logger.warn('Cookie清除可能失败')
        }
      } catch (error) {
        logger.error('清除Cookie时出错:', error)
      }
    }
  }

  const setLoginType = (val) => {
    logger.log('设置登录方式:', val)
    loginType.value = val
  }

  const ResetUserInfo = (value = {}) => {
    userInfo.value = {
      ...userInfo.value, ...value,
    }
  }
  /* 获取用户信息*/
  const GetUserInfo = async(id) => {
    logger.log('GetUserInfo 开始执行，id:', id)
    try {
      const res = await getUserInfo(id)
      logger.log('getUserInfo API 响应:', res)
      if (res.status === 200) {
        logger.log('设置用户信息:', res.data.userInfo)
        setUserInfo(res.data.userInfo)
      } else {
        logger.log('获取用户信息失败，状态码:', res.status)
      }
      return res
    } catch (error) {
      logger.log('GetUserInfo 执行失败:', error)
      throw error
    }
  }

  /* 登录*/
  const LoginIn = async(loginInfo, auth_type, auth_id) => {
    loadingInstance.value = Loading.service({
      fullscreen: true,
      text: '登录中，请稍候...',
    })
    // 记录是否使用激活码登录（用于后续判断是否需要保存安全码）
    let isActivationCodeLogin = false

    try {
      // 在客户端环境下，尝试获取认证码并添加到登录信息中
      if (agentApi.isClient()) {
        try {
          const clientConfig = await agentApi.getClientConfig()
          if (clientConfig) {
            // 优先级：安全码 > 激活码 > 无认证码
            if (clientConfig.security_code && clientConfig.security_code.trim() !== '') {
              // 安全码优先，使用security_code字段
              loginInfo.security_code = clientConfig.security_code
              logger.log('登录时使用安全码:', clientConfig.security_code)
            } else if (clientConfig.activation_code && clientConfig.activation_code.trim() !== '') {
              // 激活码备用，使用activation_code字段
              loginInfo.activation_code = clientConfig.activation_code
              isActivationCodeLogin = true // 标记为激活码登录
              logger.log('登录时使用激活码:', clientConfig.activation_code)
            } else {
              // 都没有，不传认证码参数
              logger.log('未找到安全码或激活码，不传认证码参数')
            }
          }
        } catch (error) {
          logger.log('获取客户端配置失败，继续登录流程:', error)
        }
      }

      let res = ''
      switch (auth_type) {
        case 'qiyewx':
        case 'qiyewx_oauth':
        case 'feishu':
        case 'dingtalk':
        case 'oauth2':
        case 'cas':
        case 'msad':
        case 'ldap':
          res = await auth_check(loginInfo)
          setLoginType(auth_id)
          break
        case 'accessory':
          res = await post_verify(loginInfo)
          break
        default:
          res = await login(loginInfo)
          setLoginType(auth_id)
          break
      }
      if (res.status === 200) {
        if (res.data.code === -1 || res.data?.data?.status === 1) {
          Message({
            showClose: true,
            message: res.data?.msg,
            type: 'error',
          })
          loadingInstance.value.close()
          return { code: -1 }
        } else {
          if (res.data.data) {
            if (res.data.data.secondary) {
              loadingInstance.value.close()
              return {
                isSecondary: true,
                secondary: res.data.data.secondary,
                uniqKey: res.data.data.uniqKey,
                contactType: res.data.data.contactType,
                qrcode: res.data.data.qrcode,
                CurrentSecret: res.data.data.CurrentSecret,
                hasContactInfo: res.data.data.hasContactInfo,
                secondaryType: res.data.secondaryType,
                userName: res.data.data.userName,
                user_id: res.data.data.userID
              }
            }
            // 检查是否为客户端环境
            if (agentApi.isClient()) {
              const clineData = {
                token: res.data.data.accessToken,
                refreshToken: res.data.data.refreshToken,
                realm: 'default',
              }
              await agentApi.setLoginStatus({
                Token: JSON.stringify(clineData),
                IsActivationCodeLogin: isActivationCodeLogin // 传递激活码登录标记
              })
              // 登录后，要根据“启动后自动连接”开关启动连接
              sessionStorage.setItem('autoConnectChecked', 'login')
            }
            setToken(res.data.data)

            // 广播登录信息到所有连接的页面
            try {
              const loginData = {
                accessToken: res.data.data.accessToken,
                refreshToken: res.data.data.refreshToken,
                userInfo: userInfo.value,
                authId: auth_id
              }
              broadcastLogin(loginData)
              logger.log('登录信息已广播到所有页面')
            } catch (error) {
              logger.log('广播登录信息失败:', error)
            }
          }
          return await handlePostLoginSetup(auth_type)
        }
      } else {
        Message({
          showClose: true,
          message: res.data?.msg || '服务器不通，请检查网络！',
          type: 'error',
        })
        loadingInstance.value.close()
      }
    } catch (e) {
      console.warn('LoginIn: 登录失败:', e)
      if (e.message === 'Network Error') {
        Message.error('服务器不通，请检查网络！')
      } else {
        Message.error('认证失败，请检查终端时间！')
      }
      loadingInstance.value.close()
    }
  }

  /* 登出*/
  const LoginOut = async(fromBroadcast = false) => {
    // 只有不是来自广播通知的退出登录才发送广播，避免消息循环
    if (!fromBroadcast) {
      try {
        broadcastLogout({
          reason: 'user_logout',
          timestamp: Date.now()
        })
        logger.log('退出信息已广播到所有页面')
      } catch (error) {
        logger.log('广播退出信息失败:', error)
      }
    } else {
      logger.log('来自广播通知的退出登录，跳过广播发送')
    }

    const res = await logout() // 调用登出接口
    logger.log('登出res', res)
    if (res.status === 200) {
      if (res.data.code === -1) {
        Message({
          showClose: true,
          message: res.data.msg,
          type: 'error',
        })
      } else {
        // 检查是否有OAuth2登出重定向URL
        if (res.data.redirectUrl) {
          logger.log('检测到OAuth2登出URL，正在重定向:', res.data.redirectUrl)

          // 先清理本地存储
          ClearStorage()

          // 执行重定向到IdP登出页面
          if (agentApi.isClient()) {
            // agentApi.openAsecPage(res.data.redirectUrl)
            const query = agentApi.getClientParams()
            router.push({
              name: 'ClientNewLogin',
              query: query
            })
          } else {
            window.location.href = res.data.redirectUrl
          }
        } else {
          await handlePostLogoutSetup()
        }
      }
    } else {
      console.warn('LoginOut: 登出失败:', res)
      await handlePostLogoutSetup()
      // Message.error('服务器不通，请检查网络！')
    }
  }

  /* 401认证失败登出*/
  const authFailureLoginOut = async() => {
    ClearStorage()
    // 检查是否为客户端环境
    if (agentApi.isClient()) {
      const query = agentApi.getClientParams()
      router.push({
        name: 'ClientNewLogin',
        query: query
      })
    } else {
      router.push({ name: 'Login', replace: true })
      window.location.reload()
    }
  }

  /* 验证token是否有效 */
  const isTokenValid = () => {
    try {
      // 检查token是否存在
      if (!token.value || typeof token.value !== 'object') {
        logger.log('Token不存在或格式错误')
        return false
      }

      // 检查accessToken是否存在
      if (!token.value.accessToken || typeof token.value.accessToken !== 'string') {
        logger.log('AccessToken不存在或格式错误')
        return false
      }

      const accessToken = token.value.accessToken

      // 检查JWT token格式 (header.payload.signature)
      const tokenParts = accessToken.split('.')
      if (tokenParts.length !== 3) {
        logger.log('Token格式错误，不是有效的JWT格式')
        return false
      }

      try {
        // 解析payload部分
        const payload = JSON.parse(atob(tokenParts[1].replace(/-/g, '+').replace(/_/g, '/')))

        // 检查token是否过期
        if (payload.exp) {
          const currentTime = Math.floor(Date.now() / 1000)
          if (payload.exp < currentTime) {
            logger.log('Token已过期', { exp: payload.exp, current: currentTime })
            return false
          }
        }

        // 检查token是否在有效期内 (nbf - not before)
        if (payload.nbf) {
          const currentTime = Math.floor(Date.now() / 1000)
          if (payload.nbf > currentTime) {
            logger.log('Token尚未生效', { nbf: payload.nbf, current: currentTime })
            return false
          }
        }

        logger.log('Token验证通过')
        return true
      } catch (parseError) {
        logger.log('Token payload解析失败:', parseError)
        return false
      }
    } catch (error) {
      logger.log('Token验证过程中发生错误:', error)
      return false
    }
  }

  /**
   * 处理退出登录后的设置和路由跳转
   * 封装了获取用户信息、设置路由、处理重定向等逻辑
   */
  const handlePostLogoutSetup = async() => {
    ClearStorage()
    // 检查是否为客户端环境
    if (agentApi.isClient()) {
      const query = agentApi.getClientParams()
      router.push({
        name: 'ClientNewLogin',
        query: query
      })
    } else {
      router.push({ name: 'Login', replace: true })
    }
  }

  /**
   * 处理登录后的设置和路由跳转
   * 封装了获取用户信息、设置路由、处理重定向等逻辑
   */
  const handlePostLoginSetup = async(auth_type = '') => {
    window.localStorage.setItem('loginType', loginType.value)
    window.localStorage.setItem('refresh_times', 0)
    logger.log('handlePostLoginSetup 开始执行，auth_type:', auth_type)

    if (!loadingInstance.value) {
      logger.log('创建登录加载实例')
      loadingInstance.value = Loading.service({
        fullscreen: true,
        text: '登录中，请稍候...',
      })
    } else {
      logger.log('登录加载实例已存在')
    }

    logger.log('导入路由模块')
    const { useRouterStore } = await import('./router')
    const parse = (await import('url-parse')).default

    logger.log('开始获取用户信息')
    await GetUserInfo()
    logger.log('用户信息获取完成')

    logger.log('开始设置异步路由')
    const routerStore = useRouterStore()
    await routerStore.SetAsyncRouter()
    const asyncRouters = routerStore.asyncRouters
    logger.log('异步路由数量:', asyncRouters.length)

    asyncRouters.forEach(asyncRouter => {
      router.addRoute(asyncRouter)
    })
    logger.log('异步路由添加完成')

    const href = window.location.href.replace(/#/g, '&')
    const url = parse(href, true)
    let data = {}
    let clientType = null
    let clientWp = null
    try {
      const storedParams = localStorage.getItem('client_params')
      if (storedParams) {
        const params = JSON.parse(storedParams)
        clientType = params.type
        clientWp = params.wp
      }
    } catch (e) {
      console.warn('LoginIn: 获取localStorage参数失败:', e)
    }

    // 检查是否有oidc_redirect参数（OIDC登录后的特殊处理）
    if (url.query?.oidc_redirect) {
      const oidcRedirectUrl = decodeURIComponent(url.query.oidc_redirect)

      // 获取当前的token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token')
      if (token) {
        try {
          const parsedToken = typeof token === 'string' ? JSON.parse(token) : token
          const actualToken = parsedToken.accessToken || parsedToken

          // 使用fetch请求，后端会根据Authorization头返回JSON而不是302
          try {
            const response = await fetch(oidcRedirectUrl, {
              method: 'GET',
              headers: {
                'Authorization': 'Bearer ' + actualToken,
                'Content-Type': 'application/json'
              },
              credentials: 'include' // 确保携带Cookie
            })

            if (response.ok) {
              // 200响应，解析JSON获取重定向URL
              try {
                const data = await response.json()

                if (data.RedirectUrl || data.redirectUrl || data.redirect_url) {
                  const redirectUrl = data.RedirectUrl || data.redirectUrl || data.redirect_url
                  window.location.href = redirectUrl
                  loadingInstance.value.close()
                  return { oidcRedirect: true, success: true }
                } else {
                  // 作为最后的fallback，直接跳转到dashboard
                  window.location.href = '/#/dashboard'
                  loadingInstance.value.close()
                  return { oidcRedirect: true, success: true }
                }
              } catch (parseError) {
                // JSON解析失败，很可能是因为权限验证失败返回了HTML错误页面
                // 直接跳转让浏览器处理302重定向到错误页面
                window.location.href = oidcRedirectUrl
                loadingInstance.value.close()
                return { oidcRedirect: true, success: true }
              }
            } else {
              // 检查是否是302重定向
              if (response.status === 302 || response.redirected) {
                window.location.href = oidcRedirectUrl
                loadingInstance.value.close()
                return { oidcRedirect: true, success: true }
              }

              try {
                const text = await response.text()
                // 检查响应内容是否包含错误页面的标识
                if (text.includes('error?code=access_denied') || text.includes('访问被拒绝')) {
                  window.location.href = oidcRedirectUrl
                  loadingInstance.value.close()
                  return { oidcRedirect: true, success: true }
                } else {
                  // fallback: 跳转到dashboard
                  window.location.href = '/#/dashboard'
                  loadingInstance.value.close()
                  return { oidcRedirect: true, success: true }
                }
              } catch (textError) {
                // 无法读取响应内容，fallback到dashboard
                window.location.href = '/#/dashboard'
                loadingInstance.value.close()
                return { oidcRedirect: true, success: true }
              }
            }
          } catch (error) {
            // 检查是否是因为302重定向导致的CORS错误
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
              // 对于302重定向（权限失败），直接跳转让浏览器处理
              window.location.href = oidcRedirectUrl
              loadingInstance.value.close()
              return { oidcRedirect: true, success: true }
            } else {
              // 其他错误，fallback到不带授权头的跳转
              window.location.href = oidcRedirectUrl
              loadingInstance.value.close()
              return { oidcRedirect: true, success: true }
            }
          }
        } catch (e) {
          // token解析失败，直接跳转
          window.location.href = oidcRedirectUrl
          loadingInstance.value.close()
          return { oidcRedirect: true, success: true }
        }
      }

      // 如果没有token，直接跳转
      window.location.href = oidcRedirectUrl
      loadingInstance.value.close()
      return { oidcRedirect: true, success: true }
    }

    if (url.query?.redirect || url.query?.redirect_url) {
      let params = ''
      if (url.query?.redirect) {
        params = url.query?.redirect.indexOf('?') > -1 ? url.query?.redirect.substring(url.query?.redirect.indexOf('?') + 1) : ''
      } else if (url.query?.redirect_url) {
        params = url.query?.redirect_url.indexOf('?') > -1 ? url.query?.redirect_url.substring(url.query?.redirect_url.indexOf('?') + 1) : ''
      }
      params.split('&').forEach(function(item) {
        const pair = item.split('=')
        data[pair[0]] = pair[1]
      })
      if (clientType) data.type = clientType
      if (clientWp) data.wp = clientWp
      loadingInstance.value.close()

      if (auth_type === 'qiyewx_oauth') {
        return true
      } else {
        window.location.href = url.query?.redirect || url.query?.redirect_url
        return true
      }
    } else {
      data = { type: clientType || url.query.type }
      if (clientWp || url.query.wp) {
        data.wp = clientWp || url.query.wp
      }
    }
    if (url.query.wp) {
      data.wp = url.query.wp
    }
    const stateClientType = globalUrlHashParams.get('ClientType')
    if (stateClientType) {
      data.type = stateClientType
    }
    // 检查是否为客户端环境
    logger.log('检查是否为客户端环境')
    if (agentApi.isClient()) {
      logger.log('客户端环境，跳转到 ClientMain')
      const query = agentApi.getClientParams()
      logger.log('客户端参数:', query)
      router.push({
        name: 'ClientMain',
        query: query
      })
    } else {
      logger.log('浏览器环境，跳转到 dashboard，参数:', data)
      await router.push({
        name: 'dashboard',
        query: data
      })
      logger.log('跳转到 dashboard 完成')
    }
    logger.log('关闭加载实例')
    loadingInstance.value.close()
    logger.log('handlePostLoginSetup 执行完成')
    return true
  }

  /* 清理数据 */
  const ClearStorage = async() => {
    sessionStorage.clear()
    window.localStorage.removeItem('userInfo')
    window.localStorage.removeItem('token')

    // 同时清除Cookie
    try {
      Cookies.remove('asec_token')
      const removedCookie = Cookies.get('asec_token')
      if (!removedCookie) {
        logger.log('已清理所有认证数据，包括Cookie')
      } else {
        logger.warn('Cookie清理可能不完整')
      }
    } catch (error) {
      logger.error('清理Cookie时出错:', error)
    }

    token.value = ''
  }

  // const mode = computed(() => userInfo.value.attributes.sideMode[0])
  const mode = 'dark'
  const sideMode = '#273444'
  // const sideMode = computed(() => {
  //   if (userInfo.value.attributes.sideMode[0] === 'dark') {
  //     return '#191a23'
  //   } else if (userInfo.value.attributes.sideMode[0] === 'light') {
  //     return '#fff'
  //   } else {
  //     return userInfo.value.attributes.sideMode[0]
  //   }
  // })
  const baseColor = '#fff'
  // const baseColor = computed(() => {
  //   if (userInfo.value.attributes.sideMode[0] === 'dark') {
  //     return '#fff'
  //   } else if (userInfo.value.attributes.sideMode[0] === 'light') {
  //     return '#191a23'
  //   } else {
  //     return userInfo.value.baseColor
  //   }
  // })
  const activeColor = '#4D70FF'
  // const activeColor = computed(() => {
  //   if (userInfo.value.attributes.sideMode[0] === 'dark' || userInfo.value.attributes.sideMode[0] === 'light') {
  //     return '#4D70FF'
  //   }
  //   return userInfo.attributes.activeColor[0]
  // })

  watch(() => token.value, () => {
    window.localStorage.setItem('token', JSON.stringify(token.value))
  })

  return {
    userInfo,
    token,
    loginType,
    tunState,
    ResetUserInfo,
    GetUserInfo,
    LoginIn,
    LoginOut,
    authFailureLoginOut,
    mode,
    sideMode,
    setToken,
    setTunState,
    baseColor,
    activeColor,
    loadingInstance,
    ClearStorage,
    isTokenValid,
    setLoginType,
    handlePostLoginSetup,
    handlePostLogoutSetup,
  }
})
