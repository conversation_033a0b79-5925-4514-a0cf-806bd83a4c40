syntax = "proto3";

package api.conf;
option go_package = "asdsec.com/asec/platform/api/conf/v1;v1";

// 自定义通道配置
message Channel{
  // 通道名称
  string channel = 1;
  // 通道类型
  string channel_type = 2;
  // 应用系统
  string plat = 3;
  // 进程列表
  repeated string process_list = 4;
  // 文件路径白名单
  repeated string file_path_white_list = 5;
}

message EventFilters{
  // GlobalFilter
  repeated FilterItem global_filter_items = 1;
  // ProcessFilter
  repeated ProcessFilterItem process_filter_items = 2;
  // 开启敏感扫描 1-开启 2-关闭
  uint32 scan_content = 3;
}

enum FieldName{
  // 文件路径
  File_Path = 0;
  // 文件名
  File_Name = 1;
  // 文件扩展名
  File_Extension = 2;
  // 进程名
  Process_Name = 3;
  // 全文件路径
  Full_File_Path = 4;
}

enum ExpressionSymbol{
  // 包含
  Contains = 0;
  // 等于
  Is = 1;
  // 不等于
  Is_Not = 2;
  // 正则表达式
  Regex = 3;
  // 通配符
  Wildcard = 4;
  // 以XXX开始
  Starts_with = 5;
}

message ProcessFilterItem{
  // 属性名称
  FilterItem process_name = 1;
  // 操作类型
  repeated FilterItem filters = 2;

}

message FilterItem{
  // 属性名称
  string field_name = 1;
  // 操作类型
  string expression_symbol = 2;
  // 值
  string expression_value = 3;
  // 匹配方式
  string action = 4;

}
