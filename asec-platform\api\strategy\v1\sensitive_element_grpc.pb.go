// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: strategy/v1/sensitive_element.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	GetSenElem_AgentGetSenElem_FullMethodName = "/api.strategy.GetSenElem/AgentGetSenElem"
)

// GetSenElemClient is the client API for GetSenElem service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GetSenElemClient interface {
	AgentGetSenElem(ctx context.Context, in *GetElemReq, opts ...grpc.CallOption) (*GetElemResp, error)
}

type getSenElemClient struct {
	cc grpc.ClientConnInterface
}

func NewGetSenElemClient(cc grpc.ClientConnInterface) GetSenElemClient {
	return &getSenElemClient{cc}
}

func (c *getSenElemClient) AgentGetSenElem(ctx context.Context, in *GetElemReq, opts ...grpc.CallOption) (*GetElemResp, error) {
	out := new(GetElemResp)
	err := c.cc.Invoke(ctx, GetSenElem_AgentGetSenElem_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GetSenElemServer is the server API for GetSenElem service.
// All implementations must embed UnimplementedGetSenElemServer
// for forward compatibility
type GetSenElemServer interface {
	AgentGetSenElem(context.Context, *GetElemReq) (*GetElemResp, error)
	mustEmbedUnimplementedGetSenElemServer()
}

// UnimplementedGetSenElemServer must be embedded to have forward compatible implementations.
type UnimplementedGetSenElemServer struct {
}

func (UnimplementedGetSenElemServer) AgentGetSenElem(context.Context, *GetElemReq) (*GetElemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AgentGetSenElem not implemented")
}
func (UnimplementedGetSenElemServer) mustEmbedUnimplementedGetSenElemServer() {}

// UnsafeGetSenElemServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GetSenElemServer will
// result in compilation errors.
type UnsafeGetSenElemServer interface {
	mustEmbedUnimplementedGetSenElemServer()
}

func RegisterGetSenElemServer(s grpc.ServiceRegistrar, srv GetSenElemServer) {
	s.RegisterService(&GetSenElem_ServiceDesc, srv)
}

func _GetSenElem_AgentGetSenElem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetElemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GetSenElemServer).AgentGetSenElem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GetSenElem_AgentGetSenElem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GetSenElemServer).AgentGetSenElem(ctx, req.(*GetElemReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GetSenElem_ServiceDesc is the grpc.ServiceDesc for GetSenElem service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GetSenElem_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.strategy.GetSenElem",
	HandlerType: (*GetSenElemServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AgentGetSenElem",
			Handler:    _GetSenElem_AgentGetSenElem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "strategy/v1/sensitive_element.proto",
}
