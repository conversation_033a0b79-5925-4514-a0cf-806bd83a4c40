//go:build !android && !ios

package service

import (
	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/service/app_matcher"
	"asdsec.com/asec/platform/pkg/biz/strategy_engine"
	"sync"
)

var SERouteCache *pb.SERoutes

// DomainAppMatcher 域名app匹配器
var DomainAppMatcher []app_matcher.Matcher

// IpAppMatcher ip app匹配器
var IpAppMatcher []app_matcher.Matcher

var AccessInfo []strategy_engine.AccessModel

var UciInfo sync.Map
