package repository

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/console/app/ddr_source/ddr_const"
	"asdsec.com/asec/platform/app/console/app/ddr_source/dto"
	"asdsec.com/asec/platform/app/console/app/ddr_source/vo"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/ddr_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/proto"
	"gorm.io/gorm"
	"strconv"
	"strings"
)

type ddrSourceRepository struct {
}

func (d ddrSourceRepository) CheckSensitiveQuote(c *gin.Context, req vo.DelSourceReq) ([]string, error) {
	client, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var ruleName []string
	err = client.Model(&ddr_model.SensitiveStrategyDB{}).Select("rule_name").
		Where("source_id in ?", req.Id).Find(&ruleName).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if len(ruleName) == 0 {
		return nil, nil
	}
	return ruleName, errors.New("del source failed when checkSensitiveQuote")
}

func getListQueryTx(client *gorm.DB) *gorm.DB {
	tx := client.Model(&ddr_model.DdrSource{}).Select("tb_ddr_source.id, tb_ddr_source.source_name, tb_ddr_source.source_type,tb_ddr_source.status," +
		" ARRAY_REMOVE(ARRAY_AGG(tb_ddr_source_web.url_addr), NULL)     AS url_addr," +
		"ARRAY_REMOVE(ARRAY_AGG(tb_ddr_source_web.url_port), NULL)      AS url_port," +
		"ARRAY_REMOVE(ARRAY_AGG(tb_ddr_source_web.url_route), NULL)     AS url_route," +
		"ARRAY_REMOVE(ARRAY_AGG(tb_ddr_source_software.process_name), NULL)  AS process_name," +
		"ARRAY_REMOVE(ARRAY_AGG(tb_ddr_source_software.software_name), NULL) AS software_name," +
		"tb_ddr_source_git.git_url,tb_ddr_source_software.include_file_path,tb_ddr_source_software.exclude_file_path").
		Joins("LEFT JOIN tb_ddr_source_web  ON tb_ddr_source.id = tb_ddr_source_web.source_id").
		Joins("LEFT JOIN tb_ddr_source_software  ON tb_ddr_source.id = tb_ddr_source_software.source_id").
		Joins("LEFT JOIN tb_ddr_source_git  ON tb_ddr_source.id = tb_ddr_source_git.source_id").
		Group("tb_ddr_source.id, tb_ddr_source.source_name, tb_ddr_source.source_type, tb_ddr_source_git.git_url," +
			"tb_ddr_source_software.include_file_path,tb_ddr_source_software.exclude_file_path,tb_ddr_source.status").
		Order("tb_ddr_source.create_at desc")
	return tx
}

func (d ddrSourceRepository) DetailSource(c *gin.Context, sourceId string) (dto.SourceListDto, error) {
	client, err := global.GetDBClient(c)
	resp := dto.SourceListDto{}
	if err != nil {
		return resp, err
	}
	tx := getListQueryTx(client)
	tx.Where("tb_ddr_source.id = ?", sourceId)
	err = tx.Find(&resp).Error
	if err != nil && gorm.ErrRecordNotFound != err {
		return resp, err
	}
	return resp, nil
}

func (d ddrSourceRepository) SourceList(c *gin.Context, req vo.SourceListReq) (dto.SourceList, error) {
	client, err := global.GetDBClient(c)
	resp := dto.SourceList{}
	if err != nil {
		return resp, err
	}

	tx := getListQueryTx(client)
	if req.Search != "" {
		searchTerm := fmt.Sprintf("%%%s%%", strings.ToLower(req.Search))
		tx = tx.Where("LOWER(tb_ddr_source.source_name) LIKE ?", searchTerm).
			Or("LOWER(tb_ddr_source_web.url_port) LIKE ?", searchTerm).
			Or("LOWER(tb_ddr_source_web.url_route) LIKE ?", searchTerm).
			Or("LOWER(tb_ddr_source_software.process_name) LIKE ?", searchTerm).
			Or("LOWER(tb_ddr_source_software.software_name) LIKE ?", searchTerm).
			Or("LOWER(tb_ddr_source_git.git_url) LIKE ?", searchTerm)
	}

	var _l []dto.SourceListDto
	pagination := model.Pagination{Limit: req.Limit, Offset: req.Offset}

	paginate, err := model.Paginate(&_l, &pagination, tx)
	if err != nil {
		return resp, err
	}
	resp.SourceListData = _l
	resp.TotalNum = int(paginate.TotalRows)
	resp.PageSize = pagination.TotalPages
	resp.CurrentPage = pagination.Page
	return resp, nil
}

func (d ddrSourceRepository) SourceListQuote(c *gin.Context, search string) ([]vo.SourceListQuote, error) {
	client, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []vo.SourceListQuote
	sql := client.Model(&ddr_model.DdrSource{}).
		Select("id", "source_name", "source_type").
		Order("create_at desc")
	if search != "" {
		sql = sql.Where("source_name like ?", fmt.Sprintf("%%%s%%", search))
	}
	err = sql.Find(&_l).Error
	if err != nil && gorm.ErrRecordNotFound != err {
		return nil, err
	}
	return _l, nil
}

func (d ddrSourceRepository) UpdateSource(c *gin.Context, req vo.UpdateSourceReq) error {
	client, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return client.Transaction(func(tx *gorm.DB) error {
		// 先删除原来的子表关联
		err1 := tx.Where("source_id = ?", req.Id).Delete(&ddr_model.DdrSourceWeb{}).Error
		if err1 != nil {
			return err1
		}
		err1 = tx.Where("source_id = ?", req.Id).Delete(&ddr_model.DdrSourceSoftware{}).Error
		if err1 != nil {
			return err1
		}
		err1 = tx.Where("source_id = ?", req.Id).Delete(&ddr_model.DdrSourceGit{}).Error
		if err1 != nil {
			return err1
		}
		// 再新增子表关联
		err1 = addSubSource(req.CreateSourceReq, req.Id, tx)
		if err1 != nil {
			return err1
		}

		// 修改主表
		ddrSource := ddr_model.DdrSource{
			Id:         req.Id,
			SourceName: req.SourceName,
			SourceType: req.SourceType,
			CorpID:     "0",
			Status:     req.Status,
		}
		err1 = tx.Model(&ddr_model.DdrSource{}).Where("id = ?", req.Id).Updates(&ddrSource).Error
		if err1 != nil {
			return err1
		}

		confReq, err1 := sourceToConfReq(tx, req.CreateSourceReq, req.Id, conf_center.UpdateConf)
		if err1 != nil {
			return err1
		}
		err1 = conf_center.ConfChange(confReq)
		if err1 != nil {
			return err1
		}
		return nil
	})
}

func sourceToConfReq(tx *gorm.DB, req vo.CreateSourceReq, sourceId string, changeType conf_center.ConfChangeType) (conf_center.ConfChangeReq, error) {
	source := v1.DdrSource{
		SourceId:   sourceId,
		SourceName: req.SourceName,
		SourceType: req.SourceType,
		Status:     uint32(req.Status),
	}

	switch req.SourceType {
	case ddr_const.SourceGit:
		source.VcsUrl = req.GitUrl
	case ddr_const.SourceSoftware:
		var sourceSoftware []*v1.SourceSoftware
		for _, info := range req.SoftwareInfos {
			software := &v1.SourceSoftware{
				SoftwareName:    info.SoftwareName,
				ProcessName:     info.ProcessName,
				IncludeFilePath: req.IncludeFilePath,
				ExcludeFilePath: req.ExcludeFilePath,
			}
			sourceSoftware = append(sourceSoftware, software)
		}
		source.SourceSoftware = sourceSoftware
	case ddr_const.SourceWeb:
		var sourceWebs []*v1.SourceWeb
		for _, info := range req.WebInfos {
			web := &v1.SourceWeb{
				UrlAddr:  info.UrlAddr,
				UrlPort:  info.UrlPort,
				UrlRoute: info.UrlRoute,
			}
			sourceWebs = append(sourceWebs, web)
		}
		source.SourceWeb = sourceWebs
	}

	marshal, err := proto.Marshal(&source)
	if err != nil {
		return conf_center.ConfChangeReq{}, err
	}

	changeReq := conf_center.ConfChangeReq{
		ConfBizId:       sourceId,
		ConfType:        "source",
		ConfData:        marshal,
		ConfGranularity: 1,
		Tx:              tx,
		RedisCli:        global.SysRedisClient,
		ChangeType:      changeType,
	}
	return changeReq, nil
}

func (d ddrSourceRepository) DelSource(c *gin.Context, req vo.DelSourceReq) error {
	client, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return client.Transaction(func(tx *gorm.DB) error {
		err1 := tx.Where("id in ?", req.Id).Delete(&ddr_model.DdrSource{}).Error
		if err1 != nil {
			return err1
		}
		err1 = tx.Where("source_id in ?", req.Id).Delete(&ddr_model.DdrSourceWeb{}).Error
		if err1 != nil {
			return err1
		}
		err1 = tx.Where("source_id in ?", req.Id).Delete(&ddr_model.DdrSourceSoftware{}).Error
		if err1 != nil {
			return err1
		}
		err1 = tx.Where("source_id in ?", req.Id).Delete(&ddr_model.DdrSourceGit{}).Error
		if err1 != nil {
			return err1
		}

		changeReq := conf_center.ConfChangeReq{
			ConfBizId:  req.Id[0],
			ConfType:   "source",
			Tx:         tx,
			RedisCli:   global.SysRedisClient,
			ChangeType: conf_center.DelConf,
		}
		err1 = conf_center.ConfChange(changeReq)
		if err1 != nil {
			return err1
		}
		return nil
	})
}

func (d ddrSourceRepository) CreateSource(c *gin.Context, req vo.CreateSourceReq) error {
	client, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	// 转化主表模型
	sourceModel, err := getSourceModel(req)
	if err != nil {
		return err
	}

	err = client.Transaction(func(tx *gorm.DB) error {
		err1 := tx.Model(&ddr_model.DdrSource{}).Create(&sourceModel).Error
		if err1 != nil {
			return err1
		}
		err1 = addSubSource(req, sourceModel.Id, tx)
		if err1 != nil {
			return err1
		}

		confReq, err1 := sourceToConfReq(tx, req, sourceModel.Id, conf_center.AddConf)
		if err1 != nil {
			return err1
		}
		err1 = conf_center.ConfChange(confReq)
		if err1 != nil {
			return err1
		}
		return nil
	})
	if err != nil {
		global.SysLog.Sugar().Errorf("insert Source err:%v", err)
	}
	return err
}

// addSubSource 按不同类型插入来源父-子表
func addSubSource(req vo.CreateSourceReq, sourceId string, tx *gorm.DB) error {
	switch req.SourceType {
	case ddr_const.SourceWeb:
		if len(req.WebInfos) == 0 {
			return errors.New("no web info to be added")
		}
		webModels, err1 := webInfosToModel(req.WebInfos, sourceId)
		if err1 != nil {
			return err1
		}
		// 分批插入
		err1 = tx.Model(&ddr_model.DdrSourceWeb{}).CreateInBatches(&webModels, 5).Error
		if err1 != nil {
			return err1
		}
		return nil
	case ddr_const.SourceGit:
		if req.GitUrl == "" {
			return errors.New("no git info to be added")
		}
		gitModel, err1 := gitInfoToModel(req.GitUrl, sourceId)
		if err1 != nil {
			return err1
		}

		err1 = tx.Model(&ddr_model.DdrSourceGit{}).Create(&gitModel).Error
		if err1 != nil {
			return err1
		}
		return nil
	case ddr_const.SourceSoftware:
		if len(req.SoftwareInfos) == 0 {
			return errors.New("no software info to be added")
		}
		softwareModel, err1 := getSoftwareModel(req.SoftwareInfos, sourceId, req.IncludeFilePath, req.ExcludeFilePath)
		if err1 != nil {
			return err1
		}
		err1 = tx.Model(&ddr_model.DdrSourceSoftware{}).CreateInBatches(&softwareModel, 5).Error
		if err1 != nil {
			return err1
		}
		return nil
	default:
		return fmt.Errorf("not supported source type:%v", req.SourceType)
	}

}

func getSoftwareModel(softwareInfos []vo.SoftwareInfo, sourceId string, includeFilePath []string, excludeFilePath []string) ([]ddr_model.DdrSourceSoftware, error) {
	if len(includeFilePath) == 0 {
		includeFilePath = make([]string, 0)
	}
	if len(excludeFilePath) == 0 {
		excludeFilePath = make([]string, 0)
	}
	var s []ddr_model.DdrSourceSoftware
	for _, info := range softwareInfos {
		id, err := snowflake.Sf.GetId()
		if err != nil {
			return nil, err
		}
		software := ddr_model.DdrSourceSoftware{
			Id:              strconv.FormatUint(id, 10),
			SourceId:        sourceId,
			SoftwareName:    info.SoftwareName,
			ProcessName:     info.ProcessName,
			IncludeFilePath: includeFilePath,
			ExcludeFilePath: excludeFilePath,
		}
		s = append(s, software)
	}
	return s, nil
}

func gitInfoToModel(url string, sourceId string) (ddr_model.DdrSourceGit, error) {
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return ddr_model.DdrSourceGit{}, err
	}
	return ddr_model.DdrSourceGit{
		Id:       strconv.FormatUint(id, 10),
		GitUrl:   url,
		SourceId: sourceId,
	}, nil
}

func getSourceModel(req vo.CreateSourceReq) (ddr_model.DdrSource, error) {
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return ddr_model.DdrSource{}, err
	}
	return ddr_model.DdrSource{
		Id:         strconv.FormatUint(id, 10),
		SourceName: req.SourceName,
		SourceType: req.SourceType,
		CorpID:     "0",
		Status:     req.Status,
	}, nil
}

func webInfosToModel(infos []vo.WebInfo, sourceId string) ([]ddr_model.DdrSourceWeb, error) {
	var m []ddr_model.DdrSourceWeb
	for _, info := range infos {
		id, err := snowflake.Sf.GetId()
		if err != nil {
			return nil, err
		}
		web := ddr_model.DdrSourceWeb{
			Id:       strconv.FormatUint(id, 10),
			SourceId: sourceId,
			UrlAddr:  info.UrlAddr,
			UrlPort:  info.UrlPort,
			UrlRoute: info.UrlRoute,
		}
		m = append(m, web)
	}
	return m, nil
}

type DdrSourceRepository interface {
	CreateSource(c *gin.Context, req vo.CreateSourceReq) error
	DelSource(c *gin.Context, req vo.DelSourceReq) error
	UpdateSource(c *gin.Context, req vo.UpdateSourceReq) error
	SourceListQuote(c *gin.Context, search string) ([]vo.SourceListQuote, error)
	SourceList(c *gin.Context, req vo.SourceListReq) (dto.SourceList, error)
	DetailSource(c *gin.Context, sourceId string) (dto.SourceListDto, error)
	CheckSensitiveQuote(c *gin.Context, req vo.DelSourceReq) ([]string, error)
}

func NewDdrSourceRepository() DdrSourceRepository {
	return ddrSourceRepository{}
}
