/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import a from"./verifyCode.48f12145.js";import{_ as e,r as t,c as s,h as l,a as n,b as u,d as i,F as c,A as o,k as r,w as d,j as h,t as v,i as y,l as f}from"./index.a794166c.js";const m={class:"secondary-auth-overlay"},p={class:"secondary-auth-container"},b={key:0,class:"auth-selector"},g={class:"auth-methods"},k={class:"auth-method-content"},_={class:"icon","aria-hidden":"true"},I=["xlink:href"],j={class:"auth-method-name"},x={class:"selector-footer"},C=e(Object.assign({name:"SecondaryAuth"},{props:{authMethods:{type:Array,default:()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:!0},{type:"email",name:"邮箱验证",icon:"email",available:!0}]},authInfo:{type:Object,required:!0},authId:{type:String,required:!0},userName:{type:String,default:""},lastId:{type:String,default:""}},emits:["verification-success","cancel"],setup(e,{emit:C}){const S=e,q=t(!0),w=t(null),A=s((()=>S.authMethods.filter((a=>a.available)))),M=a=>{w.value=a,q.value=!1};logger.log("双因子认证方式个数:"+A.value.length),1===A.value.length&&M(A.value[0]);const N=C,O=()=>{N("cancel")},B=a=>{"client"===route.query.type&&(a.clientParams={type:"client",wp:route.query.wp||"50001"}),N("verification-success",a)};return(t,s)=>{const C=l("base-avatar"),S=l("base-card"),N=l("base-button");return n(),u("div",m,[i("div",p,[q.value?(n(),u("div",b,[s[3]||(s[3]=i("h2",{class:"title"},"请选择二次认证方式",-1)),i("div",g,[(n(!0),u(c,null,o(A.value,(a=>(n(),r(S,{key:a.type,class:"auth-method-card",onClick:e=>M(a)},{default:d((()=>[i("div",k,[h(C,null,{default:d((()=>[(n(),u("svg",_,[i("use",{"xlink:href":"#icon-auth-"+a.icon},null,8,I)]))])),_:2},1024),i("div",j,v(a.name),1)])])),_:2},1032,["onClick"])))),128))]),i("div",x,[h(N,{type:"info",onClick:s[0]||(s[0]=()=>O())},{default:d((()=>s[2]||(s[2]=[y("取消")]))),_:1,__:[2]})])])):f("",!0),!q.value&&w.value?(n(),r(a,{key:1,"auth-info":e.authInfo,"auth-id":e.authId,"user-name":e.userName,"last-id":e.lastId,"secondary-type":w.value.type,onVerificationSuccess:B,onBack:s[1]||(s[1]=a=>q.value=!0),onCancel:O},null,8,["auth-info","auth-id","user-name","last-id","secondary-type"])):f("",!0)])])}}}),[["__scopeId","data-v-fdda650f"]]);export{C as default};
