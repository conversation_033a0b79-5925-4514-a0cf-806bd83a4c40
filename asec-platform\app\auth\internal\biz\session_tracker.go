package biz

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/dto"
)

// SessionTrackerRepo 会话跟踪Repository接口
type SessionTrackerRepo interface {
	// CreateSessionTracker 创建会话跟踪记录
	CreateSessionTracker(ctx context.Context, param dto.CreateSessionTrackerParam) error

	// GetUserActiveJWTIds 获取用户指定客户端分类的所有活跃JWT ID
	GetUserActiveJWTIds(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) ([]string, error)

	// UpdateLastActiveTime 更新最后活跃时间
	UpdateLastActiveTime(ctx context.Context, jwtId string) error

	// DeleteSessionTracker 删除会话跟踪记录
	DeleteSessionTracker(ctx context.Context, jwtId string) error

	// DeleteExpiredTrackers 删除过期的跟踪记录
	DeleteExpiredTrackers(ctx context.Context) error

	// GetActiveSessionCount 获取活跃会话数量
	GetActiveSessionCount(ctx context.Context, corpId, userId string, clientType dto.ClientType) (int, error)

	// GetActiveSessionCountByCategory 按客户端分类获取活跃会话数量
	GetActiveSessionCountByCategory(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) (int, error)

	// GetOldestJWTId 获取最旧的JWT ID
	GetOldestJWTId(ctx context.Context, corpId, userId string, clientType dto.ClientType) (string, error)

	// GetOldestJWTIdByCategory 按客户端分类获取最旧的JWT ID
	GetOldestJWTIdByCategory(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) (string, error)

	// GetInactiveJWTId 获取最不活跃的JWT ID
	GetInactiveJWTId(ctx context.Context, corpId, userId string, clientType dto.ClientType) (string, error)

	// GetInactiveJWTIdByCategory 按客户端分类获取最不活跃的JWT ID
	GetInactiveJWTIdByCategory(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) (string, error)

	// GetSessionByJWTId 根据JWT ID获取会话详情
	GetSessionByJWTId(ctx context.Context, jwtId string) (*dto.UserSessionInfo, error)

	// KickSession 踢出会话（软删除）
	KickSession(ctx context.Context, jwtId, reason string) error

	// LogoutSession 用户主动登出（更新状态为logout）
	LogoutSession(ctx context.Context, jwtId, reason string) error

	// GetAllActiveJWTIdsByCorpId 获取企业下所有活跃的JWT ID（支持按用户名和客户端类型过滤）
	GetAllActiveJWTIdsByCorpId(ctx context.Context, corpId, userName, clientType string, limit, offset int) ([]string, int, error)

	// GetAllActiveSessionsByCorpId 获取企业下所有活跃会话（支持按用户名和客户端类型过滤）
	GetAllActiveSessionsByCorpId(ctx context.Context, corpId, userName, clientType string, limit, offset int) ([]*dto.UserSessionInfo, int, error)

	// GetSessionByRefreshJWTId 根据刷新令牌JWT ID获取会话详情
	GetSessionByRefreshJWTId(ctx context.Context, refreshJwtId string) (*dto.UserSessionInfo, error)

	// GetSessionBySessionId 根据会话ID获取会话详情
	GetSessionBySessionId(ctx context.Context, sessionId string) (*dto.UserSessionInfo, error)

	// UpdateSessionAccessToken 更新会话的access token JTI
	UpdateSessionAccessToken(ctx context.Context, sessionID, newAccessTokenJTI string) error
}

// SessionTrackerUsecase 会话跟踪业务逻辑
type SessionTrackerUsecase struct {
	trackerRepo   SessionTrackerRepo
	blacklistRepo JWTBlacklistRepo
	policyRepo    AuthPolicyRepo
	userRepo      UserRepo
	userGroupRepo UserGroupRepo
	log           *log.Helper
}

// NewSessionTrackerUsecase 创建会话跟踪业务逻辑
func NewSessionTrackerUsecase(
	trackerRepo SessionTrackerRepo,
	blacklistRepo JWTBlacklistRepo,
	policyRepo AuthPolicyRepo,
	userRepo UserRepo,
	userGroupRepo UserGroupRepo,
	logger log.Logger,
) *SessionTrackerUsecase {
	return &SessionTrackerUsecase{
		trackerRepo:   trackerRepo,
		blacklistRepo: blacklistRepo,
		policyRepo:    policyRepo,
		userRepo:      userRepo,
		userGroupRepo: userGroupRepo,
		log:           log.NewHelper(logger),
	}
}

// CheckClientLimit 检查客户端数量限制
func (s *SessionTrackerUsecase) CheckClientLimit(ctx context.Context, param dto.ClientLimitCheckParam) error {
	// 1. 获取用户的认证策略
	policy, err := s.getUserAuthPolicy(ctx, param.CorpID, param.UserID)
	if err != nil {
		return err
	}

	// 2. 获取客户端限制配置
	limits := policy.ClientLimits
	if limits == nil {
		defaultLimits := dto.DefaultClientLimits()
		limits = &defaultLimits
	}

	// 3. 检查是否禁止该类型客户端
	clientCategory := param.ClientType.GetClientCategory()
	maxClients := limits.GetMaxClientsForType(clientCategory)
	if maxClients == -1 {
		return pb.ErrorClientTypeForbidden("client type %s is forbidden", param.ClientType)
	}

	// 4. 如果不限制数量，直接通过
	if maxClients == 0 {
		return nil
	}

	// 5. 获取当前活跃会话数量（按客户端分类统计）
	activeCount, err := s.trackerRepo.GetActiveSessionCountByCategory(ctx, param.CorpID, param.UserID, clientCategory)
	if err != nil {
		return err
	}

	// 6. 如果未超限，直接通过
	if activeCount < maxClients {
		return nil
	}

	// 7. 根据策略处理超限情况
	return s.handleClientOverflow(ctx, param.CorpID, param.UserID, param.ClientType, limits.OverflowStrategy)
}

// CreateSessionTracker 创建会话跟踪记录
func (s *SessionTrackerUsecase) CreateSessionTracker(ctx context.Context, param dto.CreateSessionTrackerParam) error {
	return s.trackerRepo.CreateSessionTracker(ctx, param)
}

// KickSession 踢出指定会话
func (s *SessionTrackerUsecase) KickSession(ctx context.Context, jwtId string) error {
	// 1. 将JWT加入黑名单
	err := s.blacklistRepo.AddToBlacklist(ctx, jwtId, time.Now().Add(24*time.Hour))
	if err != nil {
		return err
	}

	// 2. 删除会话跟踪记录
	err = s.trackerRepo.DeleteSessionTracker(ctx, jwtId)
	if err != nil {
		s.log.Errorf("Failed to delete session tracker: %v", err)
		// 不返回错误，因为黑名单已经生效
	}

	return nil
}

// UpdateSessionActivity 更新会话活跃度
func (s *SessionTrackerUsecase) UpdateSessionActivity(ctx context.Context, jwtId string) error {
	return s.trackerRepo.UpdateLastActiveTime(ctx, jwtId)
}

// CleanExpiredSessions 清理过期会话
func (s *SessionTrackerUsecase) CleanExpiredSessions(ctx context.Context) error {
	// 清理过期的会话跟踪记录
	err := s.trackerRepo.DeleteExpiredTrackers(ctx)
	if err != nil {
		s.log.Errorf("Failed to delete expired trackers: %v", err)
	}

	// 清理过期的黑名单记录
	err = s.blacklistRepo.RemoveExpiredFromBlacklist(ctx)
	if err != nil {
		s.log.Errorf("Failed to remove expired blacklist: %v", err)
	}

	return nil
}

// handleClientOverflow 处理客户端超限
func (s *SessionTrackerUsecase) handleClientOverflow(ctx context.Context, corpId, userId string, clientType dto.ClientType, strategy dto.OverflowStrategy) error {
	switch strategy {
	case dto.OverflowStrategyKickOldest:
		return s.kickOldestSession(ctx, corpId, userId, clientType)
	case dto.OverflowStrategyKickInactive:
		return s.kickInactiveSession(ctx, corpId, userId, clientType)
	case dto.OverflowStrategyRejectNew:
		return pb.ErrorClientLimitExceeded("client limit exceeded")
	default:
		return s.kickOldestSession(ctx, corpId, userId, clientType)
	}
}

// kickOldestSession 踢出最旧会话
func (s *SessionTrackerUsecase) kickOldestSession(ctx context.Context, corpId, userId string, clientType dto.ClientType) error {
	// 按客户端分类获取最旧的JWT ID
	clientCategory := clientType.GetClientCategory()
	jwtId, err := s.trackerRepo.GetOldestJWTIdByCategory(ctx, corpId, userId, clientCategory)
	if err != nil {
		return err
	}

	if jwtId == "" {
		return nil // 没有找到会话
	}

	// 踢出会话
	return s.KickSession(ctx, jwtId)
}

// kickInactiveSession 踢出最不活跃会话
func (s *SessionTrackerUsecase) kickInactiveSession(ctx context.Context, corpId, userId string, clientType dto.ClientType) error {
	// 按客户端分类获取最不活跃的JWT ID
	clientCategory := clientType.GetClientCategory()
	jwtId, err := s.trackerRepo.GetInactiveJWTIdByCategory(ctx, corpId, userId, clientCategory)
	if err != nil {
		return err
	}

	if jwtId == "" {
		return nil // 没有找到会话
	}

	// 踢出会话
	return s.KickSession(ctx, jwtId)
}

// getUserAuthPolicy 获取用户的认证策略
func (s *SessionTrackerUsecase) getUserAuthPolicy(ctx context.Context, corpId, userId string) (*dto.AuthPolicyInfo, error) {
	// 1. 获取用户信息
	user, err := s.userRepo.QueryUserEntity(ctx, corpId, userId)
	if err != nil {
		s.log.Errorf("QueryUserEntity failed. err=%v, corpId=%v, userId=%v", err, corpId, userId)
		// 如果用户不存在，返回默认限制策略
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	// 2. 尝试获取用户根组的默认认证策略
	defaultPolicy, err := s.policyRepo.GetDefaultPolicyInRootGroup(ctx, corpId, user.GroupID)
	if err != nil {
		s.log.Errorf("GetDefaultPolicyInRootGroup failed. err=%v, corpId=%v, groupId=%v", err, corpId, user.GroupID)
		// 如果获取策略失败，返回默认限制策略
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	// 3. 如果没有找到默认策略，返回默认限制策略
	if defaultPolicy.ID == "" {
		s.log.Warnf("No default policy found for user. corpId=%v, userId=%v, groupId=%v", corpId, userId, user.GroupID)
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	// 4. 使用 GetClientLimits 方法获取策略中的客户端限制配置
	clientLimits, err := s.policyRepo.GetClientLimits(ctx, corpId, defaultPolicy.ID)
	if err != nil {
		s.log.Errorf("GetClientLimits failed. err=%v, corpId=%v, policyId=%v", err, corpId, defaultPolicy.ID)
		// 如果获取客户端限制失败，返回默认限制策略
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	// 5. 构造并返回认证策略信息
	return &dto.AuthPolicyInfo{
		ID:           defaultPolicy.ID,
		Name:         defaultPolicy.Name,
		Description:  defaultPolicy.Description,
		ClientLimits: clientLimits,
	}, nil
}

// GenerateSessionID 生成会话ID
func (s *SessionTrackerUsecase) GenerateSessionID() string {
	return uuid.New().String()
}

// GenerateJWTID 生成JWT ID
func (s *SessionTrackerUsecase) GenerateJWTID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
