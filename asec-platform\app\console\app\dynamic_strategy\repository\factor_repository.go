package repository

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/dto"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	modelAccess "asdsec.com/asec/platform/pkg/model"
	pageModel "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/auth_model"
	model "asdsec.com/asec/platform/pkg/model/strategy_model"
	"asdsec.com/asec/platform/pkg/utils"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strings"
)

type factorRepository struct {
}

func (f factorRepository) StrategyRuleChange(db *gorm.DB, needUpdates []modelAccess.AccessStrategy) error {
	return db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"dynamic_rule", "rego_file"}),
	}).Create(&needUpdates).Error
}

func (f factorRepository) GetStrategyChange(c *gin.Context, id string) ([]dto.StrategyChange, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []dto.StrategyChange
	err = db.Model(&modelAccess.AccessStrategy{}).Select("id,dynamic_rule").
		Where("dynamic_rule @> jsonb_build_object('rule',json_build_array"+
			"(json_build_object('condition',json_build_array(json_build_object('id', CAST(? AS text) )))))", id).
		Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (f factorRepository) CheckDelTimeQuote(c *gin.Context, ids []string) ([]string, []string, error) {
	if len(ids) < 1 {
		return nil, nil, nil
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, nil, err
	}
	var names []string
	err = db.Model(&modelAccess.AccessStrategy{}).Select("strategy_name").
		Where("time_id in ?", ids).Find(&names).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil, err
	}
	var authPolicy []string
	err = db.Model(&auth_model.TbAuthPolicy{}).Select("name").
		Where("? && tb_auth_policy.time_ids", pq.Array(ids)).Find(&authPolicy).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil, err
	}
	return names, authPolicy, err
}

func (f factorRepository) CheckDelQuote(c *gin.Context, ids []string) ([]string, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var names []string
	tx := db.Model(&modelAccess.AccessStrategy{}).Select("strategy_name")
	for _, id := range ids {
		tx.Or("dynamic_rule @> jsonb_build_object('rule', "+
			"json_build_array(json_build_object('condition',json_build_array(json_build_object('id', CAST(? AS text) )))))", id)
	}
	err = tx.Find(&names).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return names, err
}

func (f factorRepository) FactorNetLocation(c *gin.Context) ([]vo.FactorNetLocationResp, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []vo.FactorNetLocationResp
	err = db.Model(&model.FactorNetLocation{}).Select("id,net_location_name,public_ip,private_ip,dns,wifi_ssd").Order("created_at desc").Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (f factorRepository) FactorProcess(c *gin.Context) ([]vo.FactorProcessResp, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []vo.FactorProcessResp
	err = db.Model(&model.FactorProcess{}).Select("id,software_name,process_name").Order("created_at desc").Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (f factorRepository) FactorIp(c *gin.Context) ([]vo.FactorIpResp, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []vo.FactorIpResp
	err = db.Model(&model.FactorIp{}).Select("id,ip_name,ip_content").Order("created_at desc").Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (f factorRepository) CreateFactorProcess(c *gin.Context, factorProcess model.FactorProcess) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return db.Model(&model.FactorProcess{}).Create(&factorProcess).Error
}

func (f factorRepository) FactorProcessList(c *gin.Context, req vo.ListReq) (vo.FactorProcessListResp, error) {
	resp := vo.FactorProcessListResp{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return resp, err
	}
	tx := db.Table("tb_factor_process t1").Select("distinct t1.* ,CASE WHEN t2.ID IS NULL THEN 2 ELSE 1 END AS referenced").
		Joins("LEFT JOIN tb_access_strategy t2 ON t2.dynamic_rule @> " +
			"jsonb_build_object ( 'rule', json_build_array ( json_build_object ( 'factor_attr', 'client.process', 'condition', json_build_array ( json_build_object ( 'id', t1.ID ) ) ) ) )")
	if req.Search != "" {
		s := fmt.Sprintf("%%%v%%", strings.ToLower(req.Search))
		tx = tx.Or("LOWER(t1.software_name) LIKE ?", s).
			Or("LOWER(array_to_string(t1.process_name, ' ')) LIKE ?", s)
	}
	tx = tx.Order("created_at desc")
	var _l []vo.FactorProcessListData
	pagination := pageModel.Pagination{Limit: req.Limit, Offset: req.Offset}
	paginate, err := pageModel.Paginate(&_l, &pagination, tx)
	if err != nil {
		return resp, err
	}
	resp.TotalNum = int(paginate.TotalRows)
	resp.FactorProcessListData = _l
	return resp, nil
}

func (f factorRepository) UpdateFactorProcess(db *gorm.DB, factorProcess model.FactorProcess) error {
	return db.Model(&model.FactorProcess{}).Where("id = ?", factorProcess.Id).Updates(&factorProcess).Error
}

func (f factorRepository) DelFactorProcess(c *gin.Context, req vo.DelReq) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return db.Where("id in ?", req.Ids).Delete(&model.FactorProcess{}).Error
}

func (f factorRepository) NetLocationList(c *gin.Context, req vo.ListReq) (vo.NetLocationListResp, error) {
	resp := vo.NetLocationListResp{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return resp, err
	}
	tx := db.Table("tb_factor_net_location t1").Select("distinct t1.* ,CASE WHEN t2.ID IS NULL THEN 2 ELSE 1 END AS referenced").
		Joins("LEFT JOIN tb_access_strategy t2 ON t2.dynamic_rule @> " +
			"jsonb_build_object ( 'rule', json_build_array ( json_build_object ( 'factor_attr', 'client.net_location', 'condition', json_build_array ( json_build_object ( 'id', t1.ID ) ) ) ) )")
	if req.Search != "" {
		s := fmt.Sprintf("%%%v%%", strings.ToLower(req.Search))
		tx = tx.Or("LOWER(t1.net_location_name) LIKE ?", s).
			Or("LOWER(array_to_string(t1.public_ip, ' ')) LIKE ?", s).
			Or("LOWER(array_to_string(t1.private_ip, ' ')) LIKE ?", s).
			Or("LOWER(array_to_string(t1.dns, ' ')) LIKE ?", s).
			Or("LOWER(array_to_string(t1.wifi_ssd, ' ')) LIKE ?", s)
	}
	var _l []vo.NetLocationListData
	pagination := pageModel.Pagination{Limit: req.Limit, Offset: req.Offset}
	pagination.Sort = "updated_at desc"
	paginate, err := pageModel.Paginate(&_l, &pagination, tx)
	if err != nil {
		return resp, err
	}
	resp.TotalNum = int(paginate.TotalRows)
	resp.NetLocationListData = _l
	return resp, nil
}

func (f factorRepository) DelNetLocation(c *gin.Context, req vo.DelReq) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return db.Where("id in ?", req.Ids).Delete(&model.FactorNetLocation{}).Error
}

func (f factorRepository) UpdateNetLocation(db *gorm.DB, netLocation model.FactorNetLocation) error {
	netLocation.UpdatedAt = utils.GetFormattedNowTime()
	return db.Model(&model.FactorNetLocation{}).Where("id = ?", netLocation.Id).Updates(&netLocation).Error
}

func (f factorRepository) CreateNetLocation(c *gin.Context, netLocation model.FactorNetLocation) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	netLocation.UpdatedAt = utils.GetFormattedNowTime()
	netLocation.CreatedAt = utils.GetFormattedNowTime()
	return db.Model(&model.FactorNetLocation{}).Create(&netLocation).Error

}

func (f factorRepository) FactorIpList(c *gin.Context, req vo.ListReq) (vo.FactorIpListResp, error) {
	resp := vo.FactorIpListResp{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return resp, err
	}

	tx := db.Table("tb_factor_ip t1").Select("distinct t1.* ,CASE WHEN t2.ID IS NULL THEN 2 ELSE 1 END AS referenced").
		Joins("LEFT JOIN tb_access_strategy t2 ON t2.dynamic_rule @> " +
			"jsonb_build_object ( 'rule', json_build_array ( json_build_object ( 'factor_attr', 'clientless.ip', 'condition', json_build_array ( json_build_object ( 'id', t1.ID ) ) ) ) )")
	if req.Search != "" {
		searchStr := fmt.Sprintf("%%%v%%", strings.ToLower(req.Search))
		tx = tx.Or("LOWER(t1.ip_name) LIKE ?", searchStr).
			Or("LOWER(array_to_string(t1.ip_content, ' ')) LIKE ?", searchStr)
	}
	tx = tx.Order("created_at desc")
	var _l []vo.FactorIpListData
	pagination := pageModel.Pagination{Limit: req.Limit, Offset: req.Offset}
	paginate, err := pageModel.Paginate(&_l, &pagination, tx)
	if err != nil {
		return resp, err
	}
	resp.TotalNum = int(paginate.TotalRows)
	resp.FactorIpListData = _l
	return resp, nil
}

func (f factorRepository) UpdateFactorIp(db *gorm.DB, factorIp model.FactorIp) error {
	return db.Model(&model.FactorIp{}).Where("id=?", factorIp.ID).Updates(&factorIp).Error
}

func (f factorRepository) DelFactorIp(c *gin.Context, req vo.DelReq) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return db.Where("id in ?", req.Ids).Delete(&model.FactorIp{}).Error
}

func (f factorRepository) CreateFactorIp(c *gin.Context, factorIp model.FactorIp) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return db.Model(&model.FactorIp{}).Create(&factorIp).Error
}

func (f factorRepository) DelFactorTime(c *gin.Context, req vo.DelReq) aerrors.AError {
	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = db.Where("id in ?", req.Ids).Delete(&model.FactorTime{}).Error
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (f factorRepository) FactorTimeListPage(c *gin.Context, req vo.FactorTimeListPageReq) (vo.FactorTimeListPageResp, error) {
	resp := vo.FactorTimeListPageResp{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return resp, err
	}
	var list []vo.SourceListData
	pagination := pageModel.Pagination{Limit: req.Limit, Offset: req.Offset}
	tx := db.Table("tb_factor_time t1").
		Select("distinct t1.*,CASE WHEN t2.ID IS NULL AND t3.id IS NULL THEN 2 ELSE 1 END AS referenced").
		Joins("LEFT JOIN tb_access_strategy t2 on t1.id = t2.time_id").
		Joins("LEFT JOIN tb_auth_policy t3 on t1.id = any(t3.time_ids)")
	if req.Search != "" {
		searchStr := fmt.Sprintf("%%%v%%", strings.ToLower(req.Search))
		tx = tx.Where("LOWER(gap_name) LIKE ?", searchStr)
	}
	tx = tx.Order("created_at desc")
	paginate, err := pageModel.Paginate(&list, &pagination, tx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return resp, err
	}
	resp.SourceListData = list
	resp.TotalNum = int(paginate.TotalRows)
	return resp, nil
}

func (f factorRepository) UpdateFactorTime(c *gin.Context, factorTime model.FactorTime) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	err = db.Omit("created_at").Save(&factorTime).Error
	if err != nil {
		return err
	}
	return nil
}

func (f factorRepository) FactorTimeList(c *gin.Context) ([]vo.FactorTimeListResp, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var resp []vo.FactorTimeListResp
	err = db.Model(&model.FactorTime{}).Select("id,gap_name").Find(&resp).Order("created_at desc").Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return resp, nil
}

func (f factorRepository) AddFactorTime(c *gin.Context, factorTime model.FactorTime) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return db.Model(&model.FactorTime{}).Create(&factorTime).Error
}

func (f factorRepository) FactorList(c *gin.Context) ([]vo.FactorListResp, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var list []vo.FactorListResp
	err = db.Model(&model.DynamicFactor{}).Select("*").Order("id").Find(&list).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return list, nil
}

func NewFactorRepository() FactorRepository {
	return factorRepository{}
}

type FactorRepository interface {
	FactorList(c *gin.Context) ([]vo.FactorListResp, error)
	AddFactorTime(c *gin.Context, factorTime model.FactorTime) error
	FactorTimeList(c *gin.Context) ([]vo.FactorTimeListResp, error)
	FactorTimeListPage(c *gin.Context, req vo.FactorTimeListPageReq) (vo.FactorTimeListPageResp, error)
	UpdateFactorTime(c *gin.Context, factorTime model.FactorTime) error
	DelFactorTime(c *gin.Context, req vo.DelReq) aerrors.AError
	CreateFactorIp(c *gin.Context, factorIp model.FactorIp) error
	DelFactorIp(c *gin.Context, req vo.DelReq) error
	UpdateFactorIp(c *gorm.DB, factorIp model.FactorIp) error
	FactorIpList(c *gin.Context, req vo.ListReq) (vo.FactorIpListResp, error)
	CreateNetLocation(c *gin.Context, netLocation model.FactorNetLocation) error
	NetLocationList(c *gin.Context, req vo.ListReq) (vo.NetLocationListResp, error)
	DelNetLocation(c *gin.Context, req vo.DelReq) error
	UpdateNetLocation(c *gorm.DB, netLocation model.FactorNetLocation) error
	CreateFactorProcess(c *gin.Context, factorProcess model.FactorProcess) error
	FactorProcessList(c *gin.Context, req vo.ListReq) (vo.FactorProcessListResp, error)
	UpdateFactorProcess(c *gorm.DB, factorProcess model.FactorProcess) error
	DelFactorProcess(c *gin.Context, req vo.DelReq) error
	FactorIp(c *gin.Context) ([]vo.FactorIpResp, error)
	FactorProcess(c *gin.Context) ([]vo.FactorProcessResp, error)
	FactorNetLocation(c *gin.Context) ([]vo.FactorNetLocationResp, error)
	CheckDelQuote(c *gin.Context, ids []string) ([]string, error)
	CheckDelTimeQuote(c *gin.Context, ids []string) ([]string, []string, error)
	GetStrategyChange(c *gin.Context, id string) ([]dto.StrategyChange, error)
	StrategyRuleChange(c *gorm.DB, needUpdates []modelAccess.AccessStrategy) error
}
