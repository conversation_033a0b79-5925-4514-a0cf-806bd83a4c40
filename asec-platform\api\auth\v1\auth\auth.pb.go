// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: auth/v1/auth/auth.proto

package auth

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StatusCode int32

const (
	StatusCode_SUCCESS StatusCode = 0
	StatusCode_FAILED  StatusCode = 1
)

// Enum value maps for StatusCode.
var (
	StatusCode_name = map[int32]string{
		0: "SUCCESS",
		1: "FAILED",
	}
	StatusCode_value = map[string]int32{
		"SUCCESS": 0,
		"FAILED":  1,
	}
)

func (x StatusCode) Enum() *StatusCode {
	p := new(StatusCode)
	*p = x
	return p
}

func (x StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_auth_v1_auth_auth_proto_enumTypes[0].Descriptor()
}

func (StatusCode) Type() protoreflect.EnumType {
	return &file_auth_v1_auth_auth_proto_enumTypes[0]
}

func (x StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusCode.Descriptor instead.
func (StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{0}
}

type AdmsLoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerAddr string `protobuf:"bytes,1,opt,name=server_addr,json=serverAddr,proto3" json:"server_addr,omitempty"`
	Entry      string `protobuf:"bytes,2,opt,name=entry,proto3" json:"entry,omitempty"`
	UserName   string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
}

func (x *AdmsLoginRequest) Reset() {
	*x = AdmsLoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdmsLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdmsLoginRequest) ProtoMessage() {}

func (x *AdmsLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdmsLoginRequest.ProtoReflect.Descriptor instead.
func (*AdmsLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{0}
}

func (x *AdmsLoginRequest) GetServerAddr() string {
	if x != nil {
		return x.ServerAddr
	}
	return ""
}

func (x *AdmsLoginRequest) GetEntry() string {
	if x != nil {
		return x.Entry
	}
	return ""
}

func (x *AdmsLoginRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type TokenInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token        string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken string `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	Realm        string `protobuf:"bytes,3,opt,name=realm,proto3" json:"realm,omitempty"`
}

func (x *TokenInfo) Reset() {
	*x = TokenInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenInfo) ProtoMessage() {}

func (x *TokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenInfo.ProtoReflect.Descriptor instead.
func (*TokenInfo) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{1}
}

func (x *TokenInfo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TokenInfo) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *TokenInfo) GetRealm() string {
	if x != nil {
		return x.Realm
	}
	return ""
}

type AdmsLoginReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TokenInfo *TokenInfo `protobuf:"bytes,1,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	AdmsCheck bool       `protobuf:"varint,2,opt,name=adms_check,json=admsCheck,proto3" json:"adms_check,omitempty"`
}

func (x *AdmsLoginReply) Reset() {
	*x = AdmsLoginReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdmsLoginReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdmsLoginReply) ProtoMessage() {}

func (x *AdmsLoginReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdmsLoginReply.ProtoReflect.Descriptor instead.
func (*AdmsLoginReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{2}
}

func (x *AdmsLoginReply) GetTokenInfo() *TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *AdmsLoginReply) GetAdmsCheck() bool {
	if x != nil {
		return x.AdmsCheck
	}
	return false
}

type TokenVerifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedirectUrl string `protobuf:"bytes,1,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
	HostUrl     string `protobuf:"bytes,2,opt,name=host_url,json=hostUrl,proto3" json:"host_url,omitempty"`
}

func (x *TokenVerifyRequest) Reset() {
	*x = TokenVerifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenVerifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenVerifyRequest) ProtoMessage() {}

func (x *TokenVerifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenVerifyRequest.ProtoReflect.Descriptor instead.
func (*TokenVerifyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{3}
}

func (x *TokenVerifyRequest) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

func (x *TokenVerifyRequest) GetHostUrl() string {
	if x != nil {
		return x.HostUrl
	}
	return ""
}

type TokenVerifyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName string   `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	SmsIdp   []string `protobuf:"bytes,3,rep,name=sms_idp,json=smsIdp,proto3" json:"sms_idp,omitempty"`
}

func (x *TokenVerifyReply) Reset() {
	*x = TokenVerifyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenVerifyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenVerifyReply) ProtoMessage() {}

func (x *TokenVerifyReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenVerifyReply.ProtoReflect.Descriptor instead.
func (*TokenVerifyReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{4}
}

func (x *TokenVerifyReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TokenVerifyReply) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *TokenVerifyReply) GetSmsIdp() []string {
	if x != nil {
		return x.SmsIdp
	}
	return nil
}

type SendSmsKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IdpId  string `protobuf:"bytes,2,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`
}

func (x *SendSmsKeyRequest) Reset() {
	*x = SendSmsKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsKeyRequest) ProtoMessage() {}

func (x *SendSmsKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsKeyRequest.ProtoReflect.Descriptor instead.
func (*SendSmsKeyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{5}
}

func (x *SendSmsKeyRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SendSmsKeyRequest) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

type SendSmsKeyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *SmsInfo `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SendSmsKeyReply) Reset() {
	*x = SendSmsKeyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsKeyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsKeyReply) ProtoMessage() {}

func (x *SendSmsKeyReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsKeyReply.ProtoReflect.Descriptor instead.
func (*SendSmsKeyReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{6}
}

func (x *SendSmsKeyReply) GetData() *SmsInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type SmsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NotPhone bool   `protobuf:"varint,1,opt,name=not_phone,json=notPhone,proto3" json:"not_phone,omitempty"`
	UniqKey  string `protobuf:"bytes,2,opt,name=uniq_key,json=uniqKey,proto3" json:"uniq_key,omitempty"`
	UserName string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
}

func (x *SmsInfo) Reset() {
	*x = SmsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsInfo) ProtoMessage() {}

func (x *SmsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsInfo.ProtoReflect.Descriptor instead.
func (*SmsInfo) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{7}
}

func (x *SmsInfo) GetNotPhone() bool {
	if x != nil {
		return x.NotPhone
	}
	return false
}

func (x *SmsInfo) GetUniqKey() string {
	if x != nil {
		return x.UniqKey
	}
	return ""
}

func (x *SmsInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type LoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName       string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Password       string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	IdpId          string `protobuf:"bytes,3,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`
	RedirectUri    string `protobuf:"bytes,4,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`
	ClientId       string `protobuf:"bytes,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`                   // 客户端应用id
	GrantType      string `protobuf:"bytes,6,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`                // 授权类型
	Scope          string `protobuf:"bytes,7,opt,name=scope,proto3" json:"scope,omitempty"`                                         // 访问范围
	ActivationCode string `protobuf:"bytes,8,opt,name=activation_code,json=activationCode,proto3" json:"activation_code,omitempty"` // 激活码
	Encryption     string `protobuf:"bytes,9,opt,name=encryption,proto3" json:"encryption,omitempty"`
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{8}
}

func (x *LoginRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *LoginRequest) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

func (x *LoginRequest) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *LoginRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *LoginRequest) GetGrantType() string {
	if x != nil {
		return x.GrantType
	}
	return ""
}

func (x *LoginRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *LoginRequest) GetActivationCode() string {
	if x != nil {
		return x.ActivationCode
	}
	return ""
}

func (x *LoginRequest) GetEncryption() string {
	if x != nil {
		return x.Encryption
	}
	return ""
}

type LoginReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *structpb.Struct `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 根据不同的grant_type返回不同的结构
}

func (x *LoginReply) Reset() {
	*x = LoginReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReply) ProtoMessage() {}

func (x *LoginReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReply.ProtoReflect.Descriptor instead.
func (*LoginReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{9}
}

func (x *LoginReply) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	GrantType string `protobuf:"bytes,2,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	ClientId  string `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Scope     string `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`
}

func (x *GetTokenRequest) Reset() {
	*x = GetTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenRequest) ProtoMessage() {}

func (x *GetTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenRequest.ProtoReflect.Descriptor instead.
func (*GetTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{10}
}

func (x *GetTokenRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GetTokenRequest) GetGrantType() string {
	if x != nil {
		return x.GrantType
	}
	return ""
}

func (x *GetTokenRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GetTokenRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type GetTokenReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken     string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	ExpireIn        int64  `protobuf:"varint,2,opt,name=expire_in,json=expireIn,proto3" json:"expire_in,omitempty"`
	RefreshToken    string `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	RefreshExpireIn int64  `protobuf:"varint,4,opt,name=refresh_expire_in,json=refreshExpireIn,proto3" json:"refresh_expire_in,omitempty"`
	TokenType       string `protobuf:"bytes,5,opt,name=token_type,json=tokenType,proto3" json:"token_type,omitempty"`
}

func (x *GetTokenReply) Reset() {
	*x = GetTokenReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenReply) ProtoMessage() {}

func (x *GetTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenReply.ProtoReflect.Descriptor instead.
func (*GetTokenReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{11}
}

func (x *GetTokenReply) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GetTokenReply) GetExpireIn() int64 {
	if x != nil {
		return x.ExpireIn
	}
	return 0
}

func (x *GetTokenReply) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *GetTokenReply) GetRefreshExpireIn() int64 {
	if x != nil {
		return x.RefreshExpireIn
	}
	return 0
}

func (x *GetTokenReply) GetTokenType() string {
	if x != nil {
		return x.TokenType
	}
	return ""
}

type ListMainIDPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListMainIDPRequest) Reset() {
	*x = ListMainIDPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMainIDPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainIDPRequest) ProtoMessage() {}

func (x *ListMainIDPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainIDPRequest.ProtoReflect.Descriptor instead.
func (*ListMainIDPRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{12}
}

type ListMainIDPReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IdpList []*ListMainIDPReply_IDP `protobuf:"bytes,1,rep,name=idp_list,json=idpList,proto3" json:"idp_list,omitempty"`
}

func (x *ListMainIDPReply) Reset() {
	*x = ListMainIDPReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMainIDPReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainIDPReply) ProtoMessage() {}

func (x *ListMainIDPReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainIDPReply.ProtoReflect.Descriptor instead.
func (*ListMainIDPReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{13}
}

func (x *ListMainIDPReply) GetIdpList() []*ListMainIDPReply_IDP {
	if x != nil {
		return x.IdpList
	}
	return nil
}

type RefreshTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{14}
}

type ThirdLoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedirectUri string                          `protobuf:"bytes,1,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"` // 重定向uri
	ClientId    string                          `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`          // 客户端应用id
	GrantType   string                          `protobuf:"bytes,3,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`       // 授权类型
	Scope       string                          `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`                                // 访问范围
	IdpId       string                          `protobuf:"bytes,5,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`                   // 认证源配置id
	AuthWeb     *ThirdLoginRequest_AuthWebLogin `protobuf:"bytes,6,opt,name=auth_web,json=authWeb,proto3" json:"auth_web,omitempty"`             //第三方网页登录的登录参数
	AdUsername  string                          `protobuf:"bytes,7,opt,name=ad_username,json=adUsername,proto3" json:"ad_username,omitempty"`
	AdPwd       string                          `protobuf:"bytes,8,opt,name=ad_pwd,json=adPwd,proto3" json:"ad_pwd,omitempty"`
	Encryption  string                          `protobuf:"bytes,9,opt,name=encryption,proto3" json:"encryption,omitempty"`
}

func (x *ThirdLoginRequest) Reset() {
	*x = ThirdLoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdLoginRequest) ProtoMessage() {}

func (x *ThirdLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdLoginRequest.ProtoReflect.Descriptor instead.
func (*ThirdLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{15}
}

func (x *ThirdLoginRequest) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *ThirdLoginRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ThirdLoginRequest) GetGrantType() string {
	if x != nil {
		return x.GrantType
	}
	return ""
}

func (x *ThirdLoginRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *ThirdLoginRequest) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

func (x *ThirdLoginRequest) GetAuthWeb() *ThirdLoginRequest_AuthWebLogin {
	if x != nil {
		return x.AuthWeb
	}
	return nil
}

func (x *ThirdLoginRequest) GetAdUsername() string {
	if x != nil {
		return x.AdUsername
	}
	return ""
}

func (x *ThirdLoginRequest) GetAdPwd() string {
	if x != nil {
		return x.AdPwd
	}
	return ""
}

func (x *ThirdLoginRequest) GetEncryption() string {
	if x != nil {
		return x.Encryption
	}
	return ""
}

type CacheRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string           `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Data *structpb.Struct `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CacheRequest) Reset() {
	*x = CacheRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheRequest) ProtoMessage() {}

func (x *CacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheRequest.ProtoReflect.Descriptor instead.
func (*CacheRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{16}
}

func (x *CacheRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CacheRequest) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type CacheReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqKey string `protobuf:"bytes,1,opt,name=uniq_key,json=uniqKey,proto3" json:"uniq_key,omitempty"`
}

func (x *CacheReply) Reset() {
	*x = CacheReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CacheReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CacheReply) ProtoMessage() {}

func (x *CacheReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CacheReply.ProtoReflect.Descriptor instead.
func (*CacheReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{17}
}

func (x *CacheReply) GetUniqKey() string {
	if x != nil {
		return x.UniqKey
	}
	return ""
}

type SendSmsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqKey string `protobuf:"bytes,1,opt,name=uniq_key,json=uniqKey,proto3" json:"uniq_key,omitempty"`
	IdpId   string `protobuf:"bytes,2,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`
}

func (x *SendSmsRequest) Reset() {
	*x = SendSmsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsRequest) ProtoMessage() {}

func (x *SendSmsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsRequest.ProtoReflect.Descriptor instead.
func (*SendSmsRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{18}
}

func (x *SendSmsRequest) GetUniqKey() string {
	if x != nil {
		return x.UniqKey
	}
	return ""
}

func (x *SendSmsRequest) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

type SendSmsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status StatusCode `protobuf:"varint,1,opt,name=status,proto3,enum=api.auth.v1.auth.StatusCode" json:"status,omitempty"`
}

func (x *SendSmsReply) Reset() {
	*x = SendSmsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsReply) ProtoMessage() {}

func (x *SendSmsReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsReply.ProtoReflect.Descriptor instead.
func (*SendSmsReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{19}
}

func (x *SendSmsReply) GetStatus() StatusCode {
	if x != nil {
		return x.Status
	}
	return StatusCode_SUCCESS
}

type SmsVerifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqKey     string `protobuf:"bytes,1,opt,name=uniq_key,json=uniqKey,proto3" json:"uniq_key,omitempty"`
	AuthCode    string `protobuf:"bytes,2,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
	UserName    string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	IdpId       string `protobuf:"bytes,4,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`
	RedirectUri string `protobuf:"bytes,5,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`
	ClientId    string `protobuf:"bytes,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`       // 客户端应用id
	GrantType   string `protobuf:"bytes,7,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`    // 授权类型
	Scope       string `protobuf:"bytes,8,opt,name=scope,proto3" json:"scope,omitempty"`                             // 访问范围
	VerifyType  string `protobuf:"bytes,9,opt,name=verify_type,json=verifyType,proto3" json:"verify_type,omitempty"` // 可选值: "sms"、"email"如果为空则根据idp_id判断
	TotpKey     string `protobuf:"bytes,10,opt,name=totp_key,json=totpKey,proto3" json:"totp_key,omitempty"`
}

func (x *SmsVerifyRequest) Reset() {
	*x = SmsVerifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsVerifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsVerifyRequest) ProtoMessage() {}

func (x *SmsVerifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsVerifyRequest.ProtoReflect.Descriptor instead.
func (*SmsVerifyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{20}
}

func (x *SmsVerifyRequest) GetUniqKey() string {
	if x != nil {
		return x.UniqKey
	}
	return ""
}

func (x *SmsVerifyRequest) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

func (x *SmsVerifyRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *SmsVerifyRequest) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

func (x *SmsVerifyRequest) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *SmsVerifyRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *SmsVerifyRequest) GetGrantType() string {
	if x != nil {
		return x.GrantType
	}
	return ""
}

func (x *SmsVerifyRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *SmsVerifyRequest) GetVerifyType() string {
	if x != nil {
		return x.VerifyType
	}
	return ""
}

func (x *SmsVerifyRequest) GetTotpKey() string {
	if x != nil {
		return x.TotpKey
	}
	return ""
}

type UserBindRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 第三方用户ID
}

func (x *UserBindRequest) Reset() {
	*x = UserBindRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBindRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBindRequest) ProtoMessage() {}

func (x *UserBindRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBindRequest.ProtoReflect.Descriptor instead.
func (*UserBindRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{21}
}

func (x *UserBindRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UserBindReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success       bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	LocalUserId   string `protobuf:"bytes,2,opt,name=local_user_id,json=localUserId,proto3" json:"local_user_id,omitempty"`       // 平台用户ID
	LocalUserName string `protobuf:"bytes,3,opt,name=local_user_name,json=localUserName,proto3" json:"local_user_name,omitempty"` // 平台用户名
	Message       string `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *UserBindReply) Reset() {
	*x = UserBindReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBindReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBindReply) ProtoMessage() {}

func (x *UserBindReply) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBindReply.ProtoReflect.Descriptor instead.
func (*UserBindReply) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{22}
}

func (x *UserBindReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UserBindReply) GetLocalUserId() string {
	if x != nil {
		return x.LocalUserId
	}
	return ""
}

func (x *UserBindReply) GetLocalUserName() string {
	if x != nil {
		return x.LocalUserName
	}
	return ""
}

func (x *UserBindReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type OAuth2CallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`                                  // OAuth2授权码
	IdpId       string `protobuf:"bytes,2,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`                   // 身份提供者ID
	CorpId      string `protobuf:"bytes,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`                // 企业ID
	RedirectUri string `protobuf:"bytes,4,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"` // 重定向URI
	ClientId    string `protobuf:"bytes,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`          // 客户端ID
	State       string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`                                // 状态值
	Ticket      string `protobuf:"bytes,7,opt,name=ticket,proto3" json:"ticket,omitempty"`                              // Cas授权码
	Redirect    string `protobuf:"bytes,8,opt,name=redirect,proto3" json:"redirect,omitempty"`                          // 回跳地址
}

func (x *OAuth2CallbackRequest) Reset() {
	*x = OAuth2CallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OAuth2CallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuth2CallbackRequest) ProtoMessage() {}

func (x *OAuth2CallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuth2CallbackRequest.ProtoReflect.Descriptor instead.
func (*OAuth2CallbackRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{23}
}

func (x *OAuth2CallbackRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *OAuth2CallbackRequest) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

func (x *OAuth2CallbackRequest) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *OAuth2CallbackRequest) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *OAuth2CallbackRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *OAuth2CallbackRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *OAuth2CallbackRequest) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (x *OAuth2CallbackRequest) GetRedirect() string {
	if x != nil {
		return x.Redirect
	}
	return ""
}

// 认证回调请求（通用）
type AuthCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthCode     string            `protobuf:"bytes,1,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`                                                                                                   // 认证授权码
	AuthType     string            `protobuf:"bytes,2,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`                                                                                                   // 认证类型 (oauth2/micro_app/cas/saml等)
	AuthProvider string            `protobuf:"bytes,3,opt,name=auth_provider,json=authProvider,proto3" json:"auth_provider,omitempty"`                                                                                       // 认证提供商 (dingtalk/wechat_work/feishu/google/github等)
	CorpId       string            `protobuf:"bytes,4,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`                                                                                                         // 企业ID
	IdpId        string            `protobuf:"bytes,5,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`                                                                                                            // 身份提供者ID
	AppId        string            `protobuf:"bytes,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                                                                                                            // 应用ID
	RedirectUri  string            `protobuf:"bytes,7,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`                                                                                          // 重定向URI
	State        string            `protobuf:"bytes,8,opt,name=state,proto3" json:"state,omitempty"`                                                                                                                         // 状态值
	ClientId     string            `protobuf:"bytes,9,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`                                                                                                   // 客户端ID
	Timestamp    int64             `protobuf:"varint,10,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                                                                               // 时间戳
	ExtraParams  map[string]string `protobuf:"bytes,11,rep,name=extra_params,json=extraParams,proto3" json:"extra_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展参数
}

func (x *AuthCallbackRequest) Reset() {
	*x = AuthCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthCallbackRequest) ProtoMessage() {}

func (x *AuthCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthCallbackRequest.ProtoReflect.Descriptor instead.
func (*AuthCallbackRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{24}
}

func (x *AuthCallbackRequest) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

func (x *AuthCallbackRequest) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *AuthCallbackRequest) GetAuthProvider() string {
	if x != nil {
		return x.AuthProvider
	}
	return ""
}

func (x *AuthCallbackRequest) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *AuthCallbackRequest) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

func (x *AuthCallbackRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AuthCallbackRequest) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *AuthCallbackRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *AuthCallbackRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *AuthCallbackRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *AuthCallbackRequest) GetExtraParams() map[string]string {
	if x != nil {
		return x.ExtraParams
	}
	return nil
}

type ListMainIDPReply_IDP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string           `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Avatar       string           `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Type         string           `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Attrs        *structpb.Struct `protobuf:"bytes,5,opt,name=attrs,proto3" json:"attrs,omitempty"`
	TemplateType string           `protobuf:"bytes,6,opt,name=template_type,json=templateType,proto3" json:"template_type,omitempty"`
}

func (x *ListMainIDPReply_IDP) Reset() {
	*x = ListMainIDPReply_IDP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMainIDPReply_IDP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMainIDPReply_IDP) ProtoMessage() {}

func (x *ListMainIDPReply_IDP) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMainIDPReply_IDP.ProtoReflect.Descriptor instead.
func (*ListMainIDPReply_IDP) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ListMainIDPReply_IDP) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ListMainIDPReply_IDP) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListMainIDPReply_IDP) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ListMainIDPReply_IDP) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ListMainIDPReply_IDP) GetAttrs() *structpb.Struct {
	if x != nil {
		return x.Attrs
	}
	return nil
}

func (x *ListMainIDPReply_IDP) GetTemplateType() string {
	if x != nil {
		return x.TemplateType
	}
	return ""
}

type ThirdLoginRequest_AuthWebLogin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthWebCode  string `protobuf:"bytes,1,opt,name=auth_web_code,json=authWebCode,proto3" json:"auth_web_code,omitempty"`
	AuthWebToken string `protobuf:"bytes,2,opt,name=auth_web_token,json=authWebToken,proto3" json:"auth_web_token,omitempty"`
}

func (x *ThirdLoginRequest_AuthWebLogin) Reset() {
	*x = ThirdLoginRequest_AuthWebLogin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_auth_v1_auth_auth_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdLoginRequest_AuthWebLogin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdLoginRequest_AuthWebLogin) ProtoMessage() {}

func (x *ThirdLoginRequest_AuthWebLogin) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_auth_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdLoginRequest_AuthWebLogin.ProtoReflect.Descriptor instead.
func (*ThirdLoginRequest_AuthWebLogin) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_auth_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ThirdLoginRequest_AuthWebLogin) GetAuthWebCode() string {
	if x != nil {
		return x.AuthWebCode
	}
	return ""
}

func (x *ThirdLoginRequest_AuthWebLogin) GetAuthWebToken() string {
	if x != nil {
		return x.AuthWebToken
	}
	return ""
}

var File_auth_v1_auth_auth_proto protoreflect.FileDescriptor

var file_auth_v1_auth_auth_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x66, 0x0a, 0x10, 0x41, 0x64, 0x6d, 0x73, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x5c, 0x0a, 0x09, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x6c, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x61, 0x6c, 0x6d, 0x22, 0x6b, 0x0a,
	0x0e, 0x41, 0x64, 0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x3a, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x64, 0x6d, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x61, 0x64, 0x6d, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x22, 0x52, 0x0a, 0x12, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x61,
	0x0a, 0x10, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x6d, 0x73, 0x5f,
	0x69, 0x64, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6d, 0x73, 0x49, 0x64,
	0x70, 0x22, 0x43, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x4b, 0x65, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x64, 0x70, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d,
	0x73, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x6d, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5e, 0x0a, 0x07, 0x53, 0x6d, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6e, 0x6f, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x75, 0x6e, 0x69, 0x71, 0x4b, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9c, 0x02, 0x0a, 0x0c, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x69, 0x64, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x69, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x0a, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x77, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x49, 0x6e, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x72,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x49, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x14, 0x0a,
	0x12, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x49, 0x44, 0x50, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x81, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x69, 0x6e,
	0x49, 0x44, 0x50, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x41, 0x0a, 0x08, 0x69, 0x64, 0x70, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x49, 0x44, 0x50, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x49,
	0x44, 0x50, 0x52, 0x07, 0x69, 0x64, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xa9, 0x01, 0x0a, 0x03,
	0x49, 0x44, 0x50, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x61, 0x74, 0x74,
	0x72, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x9e,
	0x03, 0x0a, 0x11, 0x54, 0x68, 0x69, 0x72, 0x64, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x5f, 0x75, 0x72, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x64, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x64, 0x70, 0x49, 0x64,
	0x12, 0x4b, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x77, 0x65, 0x62, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x57, 0x65, 0x62, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x57, 0x65, 0x62, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x61, 0x64, 0x5f, 0x70, 0x77, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x64, 0x50, 0x77, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x58, 0x0a, 0x0c, 0x41, 0x75, 0x74, 0x68, 0x57, 0x65, 0x62,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x77, 0x65,
	0x62, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75,
	0x74, 0x68, 0x57, 0x65, 0x62, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x77, 0x65, 0x62, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x57, 0x65, 0x62, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0x4f, 0x0a, 0x0c, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x27, 0x0a, 0x0a, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x19,
	0x0a, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x75, 0x6e, 0x69, 0x71, 0x4b, 0x65, 0x79, 0x22, 0x42, 0x0a, 0x0e, 0x53, 0x65, 0x6e,
	0x64, 0x53, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x75,
	0x6e, 0x69, 0x71, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75,
	0x6e, 0x69, 0x71, 0x4b, 0x65, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x64, 0x70, 0x49, 0x64, 0x22, 0x44, 0x0a,
	0x0c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xaf, 0x02, 0x0a, 0x10, 0x53, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x71,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x6e, 0x69, 0x71,
	0x4b, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x64, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x5f, 0x75, 0x72, 0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f,
	0x74, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x6f,
	0x74, 0x70, 0x4b, 0x65, 0x79, 0x22, 0x2a, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x8f, 0x01, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x22, 0x0a,
	0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0xe5, 0x01, 0x0a, 0x15, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x32, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x64, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72,
	0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x55, 0x72, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x22, 0xca, 0x03, 0x0a, 0x13,
	0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x64, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x64, 0x70,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x69, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x59, 0x0a,
	0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x25, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x32,
	0xb3, 0x0d, 0x0a, 0x04, 0x41, 0x75, 0x74, 0x68, 0x12, 0x65, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x71, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x82, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x49,
	0x44, 0x50, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x49, 0x44,
	0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x61, 0x69, 0x6e, 0x49, 0x44, 0x50, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x29, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x69,
	0x64, 0x70, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x7a, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x75, 0x0a, 0x0a, 0x54,
	0x68, 0x69, 0x72, 0x64, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x68, 0x69,
	0x72, 0x64, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x24, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x74, 0x68, 0x69,
	0x72, 0x64, 0x12, 0x66, 0x0a, 0x05, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x12, 0x6f, 0x0a, 0x07, 0x53, 0x65,
	0x6e, 0x64, 0x53, 0x6d, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53,
	0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a,
	0x01, 0x2a, 0x22, 0x17, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x6d, 0x73, 0x12, 0x73, 0x0a, 0x09, 0x53,
	0x6d, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x6d, 0x73, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6d, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x12, 0x7c, 0x0a, 0x0b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x12, 0x1b, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x7a,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x4b, 0x65, 0x79, 0x12,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x4b,
	0x65, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a,
	0x01, 0x2a, 0x22, 0x16, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x6d, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x12, 0x71, 0x0a, 0x09, 0x41, 0x64,
	0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x41, 0x64, 0x6d, 0x73, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x41,
	0x64, 0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x6d, 0x73, 0x12, 0x72, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x62, 0x69, 0x6e,
	0x64, 0x12, 0xa8, 0x01, 0x0a, 0x0e, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x32, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x32, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4f, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x49, 0x5a, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x2f, 0x7b, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x20, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x2f, 0x7b, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x77, 0x0a, 0x0c,
	0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x42, 0x45, 0x0a, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x50, 0x01, 0x5a, 0x2e, 0x61, 0x73,
	0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x3b, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_auth_v1_auth_auth_proto_rawDescOnce sync.Once
	file_auth_v1_auth_auth_proto_rawDescData = file_auth_v1_auth_auth_proto_rawDesc
)

func file_auth_v1_auth_auth_proto_rawDescGZIP() []byte {
	file_auth_v1_auth_auth_proto_rawDescOnce.Do(func() {
		file_auth_v1_auth_auth_proto_rawDescData = protoimpl.X.CompressGZIP(file_auth_v1_auth_auth_proto_rawDescData)
	})
	return file_auth_v1_auth_auth_proto_rawDescData
}

var file_auth_v1_auth_auth_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_auth_v1_auth_auth_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_auth_v1_auth_auth_proto_goTypes = []interface{}{
	(StatusCode)(0),                        // 0: api.auth.v1.auth.StatusCode
	(*AdmsLoginRequest)(nil),               // 1: api.auth.v1.auth.AdmsLoginRequest
	(*TokenInfo)(nil),                      // 2: api.auth.v1.auth.TokenInfo
	(*AdmsLoginReply)(nil),                 // 3: api.auth.v1.auth.AdmsLoginReply
	(*TokenVerifyRequest)(nil),             // 4: api.auth.v1.auth.TokenVerifyRequest
	(*TokenVerifyReply)(nil),               // 5: api.auth.v1.auth.TokenVerifyReply
	(*SendSmsKeyRequest)(nil),              // 6: api.auth.v1.auth.SendSmsKeyRequest
	(*SendSmsKeyReply)(nil),                // 7: api.auth.v1.auth.SendSmsKeyReply
	(*SmsInfo)(nil),                        // 8: api.auth.v1.auth.SmsInfo
	(*LoginRequest)(nil),                   // 9: api.auth.v1.auth.LoginRequest
	(*LoginReply)(nil),                     // 10: api.auth.v1.auth.LoginReply
	(*GetTokenRequest)(nil),                // 11: api.auth.v1.auth.GetTokenRequest
	(*GetTokenReply)(nil),                  // 12: api.auth.v1.auth.GetTokenReply
	(*ListMainIDPRequest)(nil),             // 13: api.auth.v1.auth.ListMainIDPRequest
	(*ListMainIDPReply)(nil),               // 14: api.auth.v1.auth.ListMainIDPReply
	(*RefreshTokenRequest)(nil),            // 15: api.auth.v1.auth.RefreshTokenRequest
	(*ThirdLoginRequest)(nil),              // 16: api.auth.v1.auth.ThirdLoginRequest
	(*CacheRequest)(nil),                   // 17: api.auth.v1.auth.CacheRequest
	(*CacheReply)(nil),                     // 18: api.auth.v1.auth.CacheReply
	(*SendSmsRequest)(nil),                 // 19: api.auth.v1.auth.SendSmsRequest
	(*SendSmsReply)(nil),                   // 20: api.auth.v1.auth.SendSmsReply
	(*SmsVerifyRequest)(nil),               // 21: api.auth.v1.auth.SmsVerifyRequest
	(*UserBindRequest)(nil),                // 22: api.auth.v1.auth.UserBindRequest
	(*UserBindReply)(nil),                  // 23: api.auth.v1.auth.UserBindReply
	(*OAuth2CallbackRequest)(nil),          // 24: api.auth.v1.auth.OAuth2CallbackRequest
	(*AuthCallbackRequest)(nil),            // 25: api.auth.v1.auth.AuthCallbackRequest
	(*ListMainIDPReply_IDP)(nil),           // 26: api.auth.v1.auth.ListMainIDPReply.IDP
	(*ThirdLoginRequest_AuthWebLogin)(nil), // 27: api.auth.v1.auth.ThirdLoginRequest.AuthWebLogin
	nil,                                    // 28: api.auth.v1.auth.AuthCallbackRequest.ExtraParamsEntry
	(*structpb.Struct)(nil),                // 29: google.protobuf.Struct
}
var file_auth_v1_auth_auth_proto_depIdxs = []int32{
	2,  // 0: api.auth.v1.auth.AdmsLoginReply.token_info:type_name -> api.auth.v1.auth.TokenInfo
	8,  // 1: api.auth.v1.auth.SendSmsKeyReply.data:type_name -> api.auth.v1.auth.SmsInfo
	29, // 2: api.auth.v1.auth.LoginReply.data:type_name -> google.protobuf.Struct
	26, // 3: api.auth.v1.auth.ListMainIDPReply.idp_list:type_name -> api.auth.v1.auth.ListMainIDPReply.IDP
	27, // 4: api.auth.v1.auth.ThirdLoginRequest.auth_web:type_name -> api.auth.v1.auth.ThirdLoginRequest.AuthWebLogin
	29, // 5: api.auth.v1.auth.CacheRequest.data:type_name -> google.protobuf.Struct
	0,  // 6: api.auth.v1.auth.SendSmsReply.status:type_name -> api.auth.v1.auth.StatusCode
	28, // 7: api.auth.v1.auth.AuthCallbackRequest.extra_params:type_name -> api.auth.v1.auth.AuthCallbackRequest.ExtraParamsEntry
	29, // 8: api.auth.v1.auth.ListMainIDPReply.IDP.attrs:type_name -> google.protobuf.Struct
	9,  // 9: api.auth.v1.auth.Auth.Login:input_type -> api.auth.v1.auth.LoginRequest
	11, // 10: api.auth.v1.auth.Auth.GetToken:input_type -> api.auth.v1.auth.GetTokenRequest
	13, // 11: api.auth.v1.auth.Auth.ListMainIDP:input_type -> api.auth.v1.auth.ListMainIDPRequest
	15, // 12: api.auth.v1.auth.Auth.RefreshToken:input_type -> api.auth.v1.auth.RefreshTokenRequest
	16, // 13: api.auth.v1.auth.Auth.ThirdLogin:input_type -> api.auth.v1.auth.ThirdLoginRequest
	17, // 14: api.auth.v1.auth.Auth.Cache:input_type -> api.auth.v1.auth.CacheRequest
	19, // 15: api.auth.v1.auth.Auth.SendSms:input_type -> api.auth.v1.auth.SendSmsRequest
	21, // 16: api.auth.v1.auth.Auth.SmsVerify:input_type -> api.auth.v1.auth.SmsVerifyRequest
	4,  // 17: api.auth.v1.auth.Auth.TokenVerify:input_type -> api.auth.v1.auth.TokenVerifyRequest
	6,  // 18: api.auth.v1.auth.Auth.GetSendSmsKey:input_type -> api.auth.v1.auth.SendSmsKeyRequest
	1,  // 19: api.auth.v1.auth.Auth.AdmsLogin:input_type -> api.auth.v1.auth.AdmsLoginRequest
	22, // 20: api.auth.v1.auth.Auth.UserBind:input_type -> api.auth.v1.auth.UserBindRequest
	24, // 21: api.auth.v1.auth.Auth.OAuth2Callback:input_type -> api.auth.v1.auth.OAuth2CallbackRequest
	25, // 22: api.auth.v1.auth.Auth.AuthCallback:input_type -> api.auth.v1.auth.AuthCallbackRequest
	10, // 23: api.auth.v1.auth.Auth.Login:output_type -> api.auth.v1.auth.LoginReply
	12, // 24: api.auth.v1.auth.Auth.GetToken:output_type -> api.auth.v1.auth.GetTokenReply
	14, // 25: api.auth.v1.auth.Auth.ListMainIDP:output_type -> api.auth.v1.auth.ListMainIDPReply
	12, // 26: api.auth.v1.auth.Auth.RefreshToken:output_type -> api.auth.v1.auth.GetTokenReply
	10, // 27: api.auth.v1.auth.Auth.ThirdLogin:output_type -> api.auth.v1.auth.LoginReply
	18, // 28: api.auth.v1.auth.Auth.Cache:output_type -> api.auth.v1.auth.CacheReply
	20, // 29: api.auth.v1.auth.Auth.SendSms:output_type -> api.auth.v1.auth.SendSmsReply
	10, // 30: api.auth.v1.auth.Auth.SmsVerify:output_type -> api.auth.v1.auth.LoginReply
	5,  // 31: api.auth.v1.auth.Auth.TokenVerify:output_type -> api.auth.v1.auth.TokenVerifyReply
	7,  // 32: api.auth.v1.auth.Auth.GetSendSmsKey:output_type -> api.auth.v1.auth.SendSmsKeyReply
	3,  // 33: api.auth.v1.auth.Auth.AdmsLogin:output_type -> api.auth.v1.auth.AdmsLoginReply
	23, // 34: api.auth.v1.auth.Auth.UserBind:output_type -> api.auth.v1.auth.UserBindReply
	10, // 35: api.auth.v1.auth.Auth.OAuth2Callback:output_type -> api.auth.v1.auth.LoginReply
	10, // 36: api.auth.v1.auth.Auth.AuthCallback:output_type -> api.auth.v1.auth.LoginReply
	23, // [23:37] is the sub-list for method output_type
	9,  // [9:23] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_auth_v1_auth_auth_proto_init() }
func file_auth_v1_auth_auth_proto_init() {
	if File_auth_v1_auth_auth_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_auth_v1_auth_auth_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdmsLoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdmsLoginReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenVerifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenVerifyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsKeyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMainIDPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMainIDPReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdLoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CacheRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CacheReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsVerifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserBindRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserBindReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OAuth2CallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMainIDPReply_IDP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_auth_v1_auth_auth_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdLoginRequest_AuthWebLogin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_auth_v1_auth_auth_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_v1_auth_auth_proto_goTypes,
		DependencyIndexes: file_auth_v1_auth_auth_proto_depIdxs,
		EnumInfos:         file_auth_v1_auth_auth_proto_enumTypes,
		MessageInfos:      file_auth_v1_auth_auth_proto_msgTypes,
	}.Build()
	File_auth_v1_auth_auth_proto = out.File
	file_auth_v1_auth_auth_proto_rawDesc = nil
	file_auth_v1_auth_auth_proto_goTypes = nil
	file_auth_v1_auth_auth_proto_depIdxs = nil
}
