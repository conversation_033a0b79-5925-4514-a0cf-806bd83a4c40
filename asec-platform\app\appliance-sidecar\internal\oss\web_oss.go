package oss

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/agent_conf"
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go/service/s3"
	"google.golang.org/grpc"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"
)

const (
	webDownloadRetainDir = "/usr/local/download"
	WebOssDuration       = 30
)

func UploadWebFile(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:              ctx,
		Wg:               wg,
		DoStreamSendFunc: doUploadWebFile,
		RunType:          common.StreamSend,
		GetConnFun:       connection.GetLogCenterConnection,
		WaitSecond:       WebOssDuration,
		RandomOffset:     2,
	}
	common.Send(param)
}

func doUploadWebFile(conn *grpc.ClientConn, ctx context.Context, wg *sync.WaitGroup) error {
	var files []fs.DirEntry
	var op string
	_, err := os.Stat(webDownloadRetainDir)
	if err != nil {
		global.Logger.Sugar().Errorln("get web download dir stat err:%v", err)
		return err
	}
	op, err = filepath.Abs(webDownloadRetainDir)
	if nil != err {
		global.Logger.Sugar().Infoln("web download dir stat err:%v", err)
		return err
	}
	files, err = os.ReadDir(op)
	if nil != err {
		global.Logger.Sugar().Infoln("web download dir stat err:%v", err)
		return err
	}
	if len(files) == 0 {
		return nil
	}
	// 从配置中心获得oss配置
	ossConfig, err := agent_conf.GetOssConf(conn)
	if err != nil {
		global.Logger.Sugar().Errorf("get oss conf error : %v", err)
		return err
	}
	var s3Service *s3.S3
	if ossConfig.OssType == NoneOssType || ossConfig.Enable == 0 {
		s3Service = nil
	} else {
		awsConfig := StructAwsConfig(ossConfig)
		s3Service, err = GetOssS3Service(awsConfig, ossConfig.Key, ossConfig.Random)
		if err != nil {
			global.Logger.Sugar().Errorf("get oss service error : %v", err)
			return err
		}
	}

	// 获取Oss文件路径
	ossFilePathPrefix := EvidenceBucketPrefix
	if ossConfig.OssType == TemporaryOssType {
		ossFilePathPrefix = fmt.Sprintf("%s_%s/%s", TemporaryBucketPrefix, ossConfig.CorpId, EvidenceBucketPrefix)
	}

	for _, f := range files {
		if f.IsDir() {
			global.Logger.Sugar().Warnln("get directory of web download dir,name = %v", f.Name())
			continue
		} else {
			realFilePath := filepath.Join(webDownloadRetainDir, f.Name())
			if s3Service != nil {
				fileInfo := strings.Split(f.Name(), "_")
				if len(fileInfo) < 3 {
					global.Logger.Sugar().Warnln("get not support filename of web download dir,name = %v", f.Name())
					continue
				}
				eventTime := fileInfo[0]
				uuid := fileInfo[1]
				fileName := strings.Join(fileInfo[2:], "")
				// upload to oss
				err = uploadFile(eventTime, uuid, fileName, realFilePath, ossFilePathPrefix, s3Service, ossConfig)
				if err != nil {
					global.Logger.Sugar().Errorf("upload web file err, file-name:%s, err:%v", fileName, err)
					continue
				}
			}
			// del file
			err = os.Remove(realFilePath)
			if err != nil {
				global.Logger.Sugar().Errorf("clean web file err, file:%s, err:%v", realFilePath, err)
				continue
			}
		}
	}

	return err
}

func uploadFile(
	eventTime string,
	uuid string,
	fileName string,
	filepath string,
	ossFilePathPrefix string,
	s3Service *s3.S3,
	ossConfig agent_conf.OssConfig) error {
	// 初始化文件上传配置
	uploadConfig := UploadConfig{
		Bucket:         ossConfig.Bucket,
		OriginFilePath: filepath, //当前需要上传的文件绝对路径
	}
	// 设置上传文件路径
	uploadConfig.OssFilePath = fmt.Sprintf("%s/%s/%s/%s/%s", ossFilePathPrefix, FileBucketPath, eventTime, uuid, fileName)
	// 文件上传content-type
	uploadConfig.ContentType = FileContentType
	// 这里web网关下载的限制大小为50MB，所以size固定传1，不会出发分段上传
	return UploadFileToOss(s3Service, uploadConfig, 1)
}
