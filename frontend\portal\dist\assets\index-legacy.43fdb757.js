/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(a){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(a)}function a(e,a){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=c(e))||a&&e&&"number"==typeof e.length){t&&(e=t);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,i=!0,p=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return i=e.done,e},e:function(e){p=!0,r=e},f:function(){try{i||null==t.return||t.return()}finally{if(p)throw r}}}}function t(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);a&&(n=n.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,n)}return t}function n(e){for(var a=1;a<arguments.length;a++){var n=null!=arguments[a]?arguments[a]:{};a%2?t(Object(n),!0).forEach((function(a){o(e,a,n[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(n,a))}))}return e}function o(a,t,n){return(t=function(a){var t=function(a,t){if("object"!=e(a)||!a)return a;var n=a[Symbol.toPrimitive];if(void 0!==n){var o=n.call(a,t||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(a)}(a,"string");return"symbol"==e(t)?t:t+""}(t))in a?Object.defineProperty(a,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[t]=n,a}function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,a,t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",o=t.toStringTag||"@@toStringTag";function p(t,n,o,r){var p=n&&n.prototype instanceof s?n:s,l=Object.create(p.prototype);return i(l,"_invoke",function(t,n,o){var r,i,p,s=0,l=o||[],d=!1,u={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(a,t){return r=a,i=0,p=e,u.n=t,c}};function f(t,n){for(i=t,p=n,a=0;!d&&s&&!o&&a<l.length;a++){var o,r=l[a],f=u.p,g=r[2];t>3?(o=g===n)&&(p=r[(i=r[4])?5:(i=3,3)],r[4]=r[5]=e):r[0]<=f&&((o=t<2&&f<r[1])?(i=0,u.v=n,u.n=r[1]):f<g&&(o=t<3||r[0]>n||n>g)&&(r[4]=t,r[5]=n,u.n=g,i=0))}if(o||t>1)return c;throw d=!0,n}return function(o,l,g){if(s>1)throw TypeError("Generator is already running");for(d&&1===l&&f(l,g),i=l,p=g;(a=i<2?e:p)||!d;){r||(i?i<3?(i>1&&(u.n=-1),f(i,p)):u.n=p:u.v=p);try{if(s=2,r){if(i||(o="next"),a=r[o]){if(!(a=a.call(r,p)))throw TypeError("iterator result is not an object");if(!a.done)return a;p=a.value,i<2&&(i=0)}else 1===i&&(a=r.return)&&a.call(r),i<2&&(p=TypeError("The iterator does not provide a '"+o+"' method"),i=1);r=e}else if((a=(d=u.n<0)?p:t.call(n,u))!==c)break}catch(a){r=e,i=1,p=a}finally{s=1}}return{value:a,done:d}}}(t,o,r),!0),l}var c={};function s(){}function l(){}function d(){}a=Object.getPrototypeOf;var u=[][n]?a(a([][n]())):(i(a={},n,(function(){return this})),a),f=d.prototype=s.prototype=Object.create(u);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,i(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return l.prototype=d,i(f,"constructor",d),i(d,"constructor",l),l.displayName="GeneratorFunction",i(d,o,"GeneratorFunction"),i(f),i(f,o,"Generator"),i(f,n,(function(){return this})),i(f,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:p,m:g}})()}function i(e,a,t,n){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}i=function(e,a,t,n){if(a)o?o(e,a,{value:t,enumerable:!n,configurable:!n,writable:!n}):e[a]=t;else{var r=function(a,t){i(e,a,(function(e){return this._invoke(a,t,e)}))};r("next",0),r("throw",1),r("return",2)}},i(e,a,t,n)}function p(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,o,r,i,p=[],c=!0,s=!1;try{if(r=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=r.call(t)).done)&&(p.push(n.value),p.length!==a);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(s)throw o}}return p}}(e,a)||c(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,a){if(e){if("string"==typeof e)return s(e,a);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?s(e,a):void 0}}function s(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,n=Array(a);t<a;t++)n[t]=e[t];return n}function l(e,a,t,n,o,r,i){try{var p=e[r](i),c=p.value}catch(e){return void t(e)}p.done?a(c):Promise.resolve(c).then(n,o)}function d(e){return function(){var a=this,t=arguments;return new Promise((function(n,o){var r=e.apply(a,t);function i(e){l(r,n,o,i,p,"next",e)}function p(e){l(r,n,o,i,p,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.b871e767.js"],(function(e,t){"use strict";var o,i,c,s,l,u,f,g,m,b,v,h,x,y,w,A,k,_,z,C,E,j,F,I,P,S,O,M,U,L,D,R,N=document.createElement("style");return N.textContent='@charset "UTF-8";.form-fill-dialog[data-v-99b414e6]{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1000;display:flex;align-items:center;justify-content:center}.form-fill-dialog .dialog-mask[data-v-99b414e6]{position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);z-index:1001}.form-fill-dialog .dialog-wrapper[data-v-99b414e6]{position:relative;z-index:1002}.form-fill-dialog .dialog-wrapper .dialog-container[data-v-99b414e6]{width:500px;background:#ffffff;border-radius:8px;box-shadow:0 4px 20px rgba(0,0,0,.15);overflow:hidden;animation:dialogSlideIn-99b414e6 .3s ease-out}.form-fill-dialog .dialog-header[data-v-99b414e6]{display:flex;align-items:center;justify-content:space-between;padding:20px 24px;border-bottom:1px solid #e9ecef}.form-fill-dialog .dialog-header .dialog-title[data-v-99b414e6]{font-size:16px;font-weight:600;color:#212529}.form-fill-dialog .dialog-header .dialog-close[data-v-99b414e6]{cursor:pointer;color:#6c757d;font-size:16px;transition:color .3s}.form-fill-dialog .dialog-header .dialog-close[data-v-99b414e6]:hover{color:#212529}.form-fill-dialog .dialog-body[data-v-99b414e6]{padding:24px}.form-fill-dialog .dialog-footer[data-v-99b414e6]{display:flex;justify-content:flex-end;gap:12px;padding:16px 24px;border-top:1px solid #e9ecef;background:#f8f9fa}.form-fill-dialog .form-content .form-header[data-v-99b414e6]{margin-bottom:24px}.form-fill-dialog .form-content .form-header .app-info[data-v-99b414e6]{display:flex;align-items:center;gap:12px;padding:16px;background:#f8f9fa;border-radius:8px}.form-fill-dialog .form-content .form-header .app-info .app-icon[data-v-99b414e6]{width:48px;height:48px;border-radius:8px;display:flex;align-items:center;justify-content:center}.form-fill-dialog .form-content .form-header .app-info .app-icon[data-v-99b414e6] .base-avatar{color:#fff;font-size:16px;font-weight:500;border-radius:8px}.form-fill-dialog .form-content .form-header .app-info .app-icon[data-v-99b414e6] .base-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.form-fill-dialog .form-content .form-header .app-info .app-details[data-v-99b414e6]{flex:1}.form-fill-dialog .form-content .form-header .app-info .app-details .app-name[data-v-99b414e6]{font-size:16px;font-weight:600;color:#212529;margin-bottom:4px}.form-fill-dialog .form-content .form-header .app-info .app-details .app-desc[data-v-99b414e6]{font-size:14px;color:#6c757d;line-height:1.4}.form-fill-dialog .form-content .form-tip[data-v-99b414e6]{display:flex;align-items:flex-start;gap:8px;padding:12px;background:#e7f4ff;border-radius:6px;margin-top:16px}.form-fill-dialog .form-content .form-tip[data-v-99b414e6] .base-icon{color:#536ce6;margin-top:2px;flex-shrink:0}.form-fill-dialog .form-content .form-tip span[data-v-99b414e6]{font-size:14px;color:#536ce6;line-height:1.4}@keyframes dialogSlideIn-99b414e6{0%{opacity:0;transform:translateY(-50px) scale(.9)}to{opacity:1;transform:translateY(0) scale(1)}}[data-v-99b414e6] .base-input .base-input__inner[type=password]{-webkit-text-security:disc;font-family:Courier New,monospace;letter-spacing:2px}[data-v-99b414e6] .base-input .base-input__inner[type=password]::selection{background:transparent}[data-v-99b414e6] .base-input .base-input__inner[type=password]::-webkit-textfield-decoration-container{display:none}.app-page-root[data-v-ebc7c85c]{flex:1;min-height:0;display:flex;flex-direction:column;overflow:hidden;width:100%;max-width:100%}.person[data-v-ebc7c85c]{flex:1;min-height:0;display:flex;flex-direction:column;background:#FFFFFF;border-radius:4px;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;width:100%;max-width:100%}.person[data-v-ebc7c85c] .base-header--shadow{box-shadow:none}.person .app-header[data-v-ebc7c85c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;background:#ffffff;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box}.person .app-header[data-v-ebc7c85c] .base-input{padding:5px 12px 5px 30px}.person .app-header .header-left .page-title[data-v-ebc7c85c]{margin:0;font-size:16px;font-weight:600;color:#1f2329;line-height:28px}.person .app-header .header-right .search-controls[data-v-ebc7c85c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.person .app-header .header-right .search-controls .search-icon[data-v-ebc7c85c]{width:14px;height:14px;position:relative;margin-right:-24px;z-index:1000;color:#b3b6c1!important}.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c]{width:200px;height:28px;margin-right:10px}.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c] .base-input__wrapper{border-radius:6px;background-color:#f7f8fa;border:1px solid transparent;transition:all .2s;box-sizing:border-box}.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c] .base-input__wrapper:hover{background-color:#fff;border-color:#d0d7de}.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c] .base-input__wrapper.is-focus{background-color:#fff;border-color:#536ce6;box-shadow:0 0 0 2px rgba(64,158,255,.1)}.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c] .base-input__inner{height:36px;line-height:36px;font-size:14px;color:#1f2329}.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c] .base-input__inner::placeholder{color:#8a919f}.person .app-header .header-right .search-controls .refresh-btn[data-v-ebc7c85c]{width:28px;height:28px;padding:0;margin-right:10px;border-radius:4px;background:#f5f5f7;color:#686e84;display:flex;align-items:center;justify-content:center}.person .app-header .header-right .search-controls .refresh-btn .refresh-btn-icon[data-v-ebc7c85c]{width:14px;height:14px}.person .app-header .header-right .search-controls .refresh-btn[data-v-ebc7c85c]:hover{background:#536ce6;border-color:#d0d7de;color:#fff}.person .app-header .header-right .search-controls .view-select[data-v-ebc7c85c]{width:70px;height:20px}.person .app-header .header-right .search-controls .view-select[data-v-ebc7c85c] .base-select__input{padding:0;border:none}.person .app-header .header-right .search-controls .view-select[data-v-ebc7c85c] .base-select__dropdown{width:88px;padding:7px 7px 3px}.person .app-header .header-right .search-controls .view-select[data-v-ebc7c85c] .base-option{padding:4px 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;background:#f5f5f7;border-radius:4px;margin-bottom:4px}.person .app-header .header-right .search-controls .view-select[data-v-ebc7c85c] .base-option.is-selected{color:#fff;background:#536ce6;border-radius:4px}.person .app-header .header-right .search-controls .view-select[data-v-ebc7c85c] .base-select__wrapper{border-radius:6px;background-color:#f7f8fa;border:1px solid transparent;height:36px}.person .app-header .header-right .search-controls .view-select[data-v-ebc7c85c] .base-select__wrapper:hover{background-color:#fff;border-color:#d0d7de}.person .app-header .search-input[data-v-ebc7c85c]{width:200px;height:28px}.person .app-header .search-input[data-v-ebc7c85c] .el-input__wrapper{border-radius:4px;background-color:#f5f7fa}.person .app-header .search-input[data-v-ebc7c85c] .el-input__wrapper.is-focus{background-color:#fff}.person .app-header .search-input[data-v-ebc7c85c] .el-input__inner{height:32px;line-height:32px;font-size:14px}.person .app-header .search-input[data-v-ebc7c85c] .el-input__inner::placeholder{color:#909399}.person .flex-container[data-v-ebc7c85c]{flex:1;min-height:0;display:flex;height:100%;width:100%;max-width:100%}.person .flex-container.flex-row[data-v-ebc7c85c]{flex-direction:row}.person .category-aside[data-v-ebc7c85c]{flex:0 0 104px;width:104px;height:100%;border-bottom:none;overflow:hidden;min-width:104px}.person .category-aside[data-v-ebc7c85c] .base-menu--vertical{width:100%;height:100%}.person .category-aside .category-menu[data-v-ebc7c85c]{border-right:none;background:transparent;padding:0 8px 8px;height:100%;box-sizing:border-box;overflow-y:auto;overflow-x:hidden}.person .category-aside .category-menu[data-v-ebc7c85c]::-webkit-scrollbar{width:6px}.person .category-aside .category-menu .category-menu-item[data-v-ebc7c85c]{width:88px;height:28px;flex:0 0 auto}.person .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item__content{padding:0;height:28px;line-height:28px;justify-content:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%;box-sizing:border-box;text-align:center}.person .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item{margin:8px 0 8px 8px;font-size:14px;color:#4e5969;border-radius:6px;transition:all .2s ease;cursor:pointer;width:80px;min-height:28px;display:flex;align-items:center;justify-content:center}.person .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item:not(.base-menu-item--active){background-color:transparent;color:#4e5969}.person .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item:hover:not(.base-menu-item--active){background-color:#f5f5f7}.person .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item:hover:not(.base-menu-item--active) .base-menu-item__content{color:#686e84}.person .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item.base-menu-item--active{background-color:#536ce6;color:#fff;font-weight:500}.person .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item:active{background-color:#3370ff}.person .app-main[data-v-ebc7c85c]{flex:1;min-height:0;min-width:0;width:auto;max-width:calc(100% - 104px);height:100%;padding:10px 0 24px 8px;overflow-y:auto;overflow-x:hidden;background:#ffffff;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box}.person .app-main .category-section[data-v-ebc7c85c]{margin-bottom:16px;margin-top:0}.person .app-main .category-section[data-v-ebc7c85c]:last-child{margin-bottom:0}.person .app-main .category-section .apps-grid[data-v-ebc7c85c]{display:-ms-grid;display:grid;gap:8px;padding:0 16px 0 0;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box}.person .app-main .category-section .apps-grid.view-standard[data-v-ebc7c85c]{grid-template-columns:repeat(auto-fit,minmax(210px,1fr));gap:8px;justify-content:start}.person .app-main .category-section .apps-grid.view-standard.apps-grid-limited[data-v-ebc7c85c]{grid-template-columns:repeat(auto-fit,minmax(210px,228px))}.person .app-main .category-section .apps-grid.view-standard .app-item[data-v-ebc7c85c]{width:100%;height:64px;background:#f7f7fa;border:1px solid #f2f2f5;border-radius:4px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon[data-v-ebc7c85c]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:-4px;right:20px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--yishoucang[data-v-ebc7c85c]{color:#ffbf00!important}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c],.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c]:hover{color:#b3b6c1!important}.person .app-main .category-section .apps-grid.view-standard .app-item .app-form-fill-icon[data-v-ebc7c85c]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:26px;right:20px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-form-fill-icon[data-v-ebc7c85c]:hover{color:#007bff!important}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content[data-v-ebc7c85c]{display:flex;flex-direction:row;text-align:left;height:40px;margin:12px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-icon[data-v-ebc7c85c]{margin-bottom:0;margin-right:12px;margin-top:6px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-icon[data-v-ebc7c85c] .avatar-text{font-size:14px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details[data-v-ebc7c85c]{display:flex;flex-direction:column;justify-content:center}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-name[data-v-ebc7c85c]{font-size:14px;line-height:20px;color:#282a33;height:20px;display:flex}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-name .app-name-text[data-v-ebc7c85c]{min-width:56px;height:20px;max-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:14px;font-family:PingFang SC,PingFang SC-Medium,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:500;text-align:left;color:#282a33;line-height:20px;margin-top:-2px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-desc[data-v-ebc7c85c]{margin-top:2px;font-size:12px;color:#8a919f;line-height:16px;min-height:16px;text-align:left;cursor:pointer;user-select:none}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-desc .app-desc-text[data-v-ebc7c85c]{display:inline-block;width:100%;overflow:hidden;font-size:12px;text-overflow:ellipsis;white-space:nowrap;max-width:150px;cursor:pointer;user-select:none}.person .app-main .category-section .apps-grid.view-standard .app-item:hover:not(.disabled) .app-collect-icon[data-v-ebc7c85c]{display:block}.person .app-main .category-section .apps-grid.view-standard .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-ebc7c85c]{display:block}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--yishoucang[data-v-ebc7c85c]{display:block!important}.person .app-main .category-section .apps-grid.view-compact[data-v-ebc7c85c]{grid-template-columns:repeat(auto-fit,minmax(110px,1fr));gap:8px}.person .app-main .category-section .apps-grid.view-compact.apps-grid-limited[data-v-ebc7c85c]{grid-template-columns:repeat(auto-fit,minmax(110px,120px))}.person .app-main .category-section .apps-grid.view-compact .app-item[data-v-ebc7c85c]{width:100%;height:64px;background:#f7f7fa;border:1px solid #f2f2f5;border-radius:4px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon[data-v-ebc7c85c]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:8px;right:8px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-ebc7c85c]{color:#ffbf00!important}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c],.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c]:hover{color:#b3b6c1!important}.person .app-main .category-section .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-ebc7c85c]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:38px;right:8px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-ebc7c85c]:hover{color:#007bff!important}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content[data-v-ebc7c85c]{display:flex;flex-direction:column;align-items:center}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-icon[data-v-ebc7c85c]{margin:10px 0 0;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-icon[data-v-ebc7c85c] .avatar-text{font-size:14px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-details[data-v-ebc7c85c]{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;text-align:left}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-details .app-name[data-v-ebc7c85c]{font-size:14px;line-height:20px;font-weight:500;max-width:98px}.person .app-main .category-section .apps-grid.view-compact .app-item:hover:not(.disabled) .app-collect-icon[data-v-ebc7c85c]{display:block}.person .app-main .category-section .apps-grid.view-compact .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-ebc7c85c]{display:block}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-ebc7c85c]{display:block!important}.person .app-main .category-section .apps-grid .app-item[data-v-ebc7c85c]{background:#f7f7fa;border:1px solid #f2f2f5;border-radius:8px;position:relative;cursor:pointer;transition:all .2s ease;overflow:hidden}.person .app-main .category-section .apps-grid .app-item .app-collect-icon[data-v-ebc7c85c]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;transition:color .2s ease}.person .app-main .category-section .apps-grid .app-item .app-collect-icon.base-icon--yishoucang[data-v-ebc7c85c]{color:#ffbf00!important}.person .app-main .category-section .apps-grid .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c],.person .app-main .category-section .apps-grid .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c]:hover{color:#b3b6c1!important}.person .app-main .category-section .apps-grid .app-item[data-v-ebc7c85c]:hover:not(.disabled){border-color:#536ce6;box-shadow:0 4px 12px rgba(64,158,255,.15);transform:translateY(-2px)}.person .app-main .category-section .apps-grid .app-item:hover:not(.disabled) .app-collect-icon[data-v-ebc7c85c]{display:block}.person .app-main .category-section .apps-grid .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-ebc7c85c]{display:block}.person .app-main .category-section .apps-grid .app-item .app-content[data-v-ebc7c85c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%;height:100%;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;position:relative}.person .app-main .category-section .apps-grid .app-item .app-content .app-icon[data-v-ebc7c85c] .base-avatar{color:#fff;font-size:16px;font-weight:500;border-radius:8px}.person .app-main .category-section .apps-grid .app-item .app-content .app-icon[data-v-ebc7c85c] .base-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.person .app-main .category-section .apps-grid .app-item .app-content .app-details .app-name[data-v-ebc7c85c]{color:#1f2329;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.person .app-main .category-section .apps-grid .app-item .status-badge[data-v-ebc7c85c]{position:absolute;top:8px;padding:2px 6px;border-radius:4px;font-size:10px;color:#fff;font-weight:500;z-index:10}.person .app-main .category-section .apps-grid .app-item .status-badge-compact[data-v-ebc7c85c]{top:0px;left:0px}.person .app-main .category-section .apps-grid .app-item .status-badge-compact .status-maint[data-v-ebc7c85c]{width:45px;height:45px}.person .app-main .category-section .apps-grid .app-item .status-badge-inline[data-v-ebc7c85c]{height:18px;line-height:18px;top:-8px;margin-left:10px;border-radius:2px;font-size:12px;background-color:#ededf1;color:#686e84}.person .app-main .category-section .apps-grid .app-item .status-badge-inline .status-maint[data-v-ebc7c85c]{width:44px;height:18px}.person .app-main .category-section .apps-grid .app-item.disabled[data-v-ebc7c85c]{cursor:not-allowed;opacity:.6}.person .app-main .category-section .apps-grid .app-item.disabled[data-v-ebc7c85c]:hover{border-color:#e5e6eb;box-shadow:none;transform:none}.person .app-main .category-section .apps-grid .app-item.disabled .app-content .app-icon[data-v-ebc7c85c] .base-avatar{filter:grayscale(100%)}.person .app-main .category-section .apps-grid .app-item.disabled .app-content .app-details .app-name[data-v-ebc7c85c]{color:#b3b6c1}.app-header[data-v-ebc7c85c] .el-row{display:flex}.app-header .el-recent-access[data-v-ebc7c85c]{display:flex;flex-shrink:0;width:80px;height:20px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:Medium;text-align:left;color:#282a33;line-height:20px;justify-content:center}.app-header .el-recent-access .el-recent-text[data-v-ebc7c85c]{width:56px}.app-header .el-recent-data[data-v-ebc7c85c]{padding-left:16px;display:flex;justify-content:space-between}.app-header .el-recent-item-container[data-v-ebc7c85c]{overflow:hidden;display:flex}.app-header .el-recent-item[data-v-ebc7c85c]{margin-top:2px;margin-right:6px;height:25px;padding:4px 0 4px 6px;background:#f5f6fe;border-radius:4px;white-space:nowrap;font-size:12px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:Regular;color:#536ce6;cursor:pointer;display:flex}.app-header .recent-app-name[data-v-ebc7c85c]{max-width:10vh;overflow:hidden;text-overflow:ellipsis}.app-header .el-recent-icon[data-v-ebc7c85c]{opacity:.6;width:8px;height:8px;margin:8px 6px 8px 5px}.app-header .el-recent-clear[data-v-ebc7c85c]{opacity:.6;margin-top:6px;position:absolute;width:14px;height:14px;right:20px;cursor:pointer}.app-header .el-recent-empty[data-v-ebc7c85c]{color:#b3b6c1;font-size:14px;height:20px}.base-icon--yishoucang[data-v-ebc7c85c]{color:#ffbf00!important}@media screen and (min-width: 900px) and (max-width: 900px){.recent-app-name[data-v-ebc7c85c]{max-width:52px!important}}@media screen and (max-width: 1200px){.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c]{width:200px;height:28px}.person .app-main[data-v-ebc7c85c]{padding:10px 0 16px 8px;flex:1;min-height:0}.person .app-main .apps-grid.view-compact[data-v-ebc7c85c]{grid-template-columns:repeat(auto-fit,minmax(110px,1fr));gap:8px}.person .app-main .apps-grid.view-compact.apps-grid-limited[data-v-ebc7c85c]{grid-template-columns:repeat(auto-fit,minmax(110px,120px))}.person .app-main .apps-grid.view-compact .app-item[data-v-ebc7c85c]{height:64px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-ebc7c85c]{margin-bottom:8px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-ebc7c85c] .base-avatar{width:24px;height:24px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-details .app-name[data-v-ebc7c85c]{font-size:12px;line-height:16px;max-width:98px}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon[data-v-ebc7c85c]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:8px;right:8px}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-ebc7c85c]{color:#ffbf00!important}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c],.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-ebc7c85c]:hover{color:#b3b6c1!important}.person .app-main .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-ebc7c85c]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:8px;right:32px}.person .app-main .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-ebc7c85c]:hover{color:#007bff!important}.person .app-main .apps-grid.view-compact .app-item:hover:not(.disabled) .app-collect-icon[data-v-ebc7c85c]{display:block}.person .app-main .apps-grid.view-compact .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-ebc7c85c]{display:block}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-ebc7c85c]{display:block!important}}@media screen and (max-width: 768px){.person .app-header[data-v-ebc7c85c]{flex-direction:column;align-items:stretch}.person .app-header .header-left[data-v-ebc7c85c]{margin-bottom:16px;display:none}.person .app-header .header-left .page-title[data-v-ebc7c85c]{font-size:18px}.person .app-header .header-right .search-controls[data-v-ebc7c85c]{justify-content:space-between}.person .app-header .header-right .search-controls .search-input[data-v-ebc7c85c]{flex:1;max-width:none}.person .base-container[data-v-ebc7c85c]{flex-direction:column}.person .base-container .category-aside[data-v-ebc7c85c]{width:100%!important;display:none}.person .base-container .category-aside .category-menu[data-v-ebc7c85c]{display:flex;overflow-x:auto;padding:12px 16px;scrollbar-width:none;-ms-overflow-style:none}.person .base-container .category-aside .category-menu[data-v-ebc7c85c]::-webkit-scrollbar{display:none}.person .base-container .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item{flex-shrink:0;margin:0 4px;white-space:nowrap;min-width:60px;max-width:120px}.person .base-container .category-aside .category-menu[data-v-ebc7c85c] .base-menu-item .category-menu-text{max-width:100px}.person .app-main[data-v-ebc7c85c]{padding:10px 0 16px 8px}.person .app-main .apps-grid.view-standard .app-item[data-v-ebc7c85c]{width:100%;height:64px}.person .app-main .apps-grid.view-standard .app-item .app-content[data-v-ebc7c85c]{padding:8px 4px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-details .app-name[data-v-ebc7c85c]{font-size:11px;line-height:14px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-details .app-desc[data-v-ebc7c85c]{display:none}.person .app-main .apps-grid.view-compact .app-item[data-v-ebc7c85c]{height:56px}.person .app-main .apps-grid.view-compact .app-item .app-content[data-v-ebc7c85c]{padding:8px 12px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-ebc7c85c]{margin-right:8px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-ebc7c85c] .base-avatar{width:28px!important;height:28px!important}}.tooltip-content[data-v-ebc7c85c]{width:200px;text-align:center}.web-link[data-v-ebc7c85c]{color:#536ce6;text-decoration:none;word-break:break-all}.el-select__popper[data-v-ebc7c85c]{background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1);margin-left:-10px!important;right:15px;top:110px;max-width:88px}.el-select__popper .el-select-dropdown__item[data-v-ebc7c85c]{width:72px;height:28px;border-radius:4px;margin-left:7px;margin-bottom:4px;padding:0 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;line-height:20px;display:flex;align-items:center;background:#f5f5f7!important}.el-select__popper .el-select-dropdown__item.selected[data-v-ebc7c85c]{color:#fff;background:#536ce6!important}.text-center[data-v-ebc7c85c]{text-align:center}.web-link[data-v-ebc7c85c]{color:#536ce6;text-decoration:none}.web-link[data-v-ebc7c85c]:hover{text-decoration:underline}.connected-content[data-v-ebc7c85c]{flex:1;min-height:0;display:flex;flex-direction:column}.disconnected-content[data-v-ebc7c85c]{display:flex;align-items:center;justify-content:center;background-color:#fff}.no-connection-wrapper[data-v-ebc7c85c]{text-align:center;padding:40px}.no-connection-wrapper .no-connection-image[data-v-ebc7c85c]{margin-bottom:24px}.no-connection-wrapper .no-connection-image img[data-v-ebc7c85c]{width:222px;height:120px;opacity:.8}.no-connection-wrapper .no-connection-text h3[data-v-ebc7c85c]{font-size:18px;font-weight:500;color:#333;margin:0 0 8px}.no-connection-wrapper .no-connection-text p[data-v-ebc7c85c]{font-size:14px;color:#666;margin:0}.no-apps-wrapper[data-v-ebc7c85c]{height:100%;display:flex;align-items:center;justify-content:center;padding:40px}.no-apps-content[data-v-ebc7c85c]{text-align:center}.no-apps-content .no-apps-image[data-v-ebc7c85c]{margin-bottom:16px}.no-apps-content .no-apps-image img[data-v-ebc7c85c]{width:222px;height:120px;opacity:.8}.no-apps-content .no-apps-text[data-v-ebc7c85c]{font-size:14px;color:#999;font-weight:400}.el-message{white-space:pre-line!important;line-height:1.5!important;padding:12px 20px!important}\n',document.head.appendChild(N),{setters:[function(e){o=e.D,i=e.r,c=e.N,s=e.c,l=e.v,u=e.h,f=e.a,g=e.b,m=e.d,b=e.t,v=e.j,h=e.w,x=e.n,y=e.O,w=e.l,A=e._,k=e.M,_=e.i,z=e.P,C=e.o,E=e.f,j=e.k,F=e.y,I=e.F,P=e.A,S=e.e,O=e.C,M=e.Q,U=e.R,L=e.K,D=e.S,R=e.T}],execute:function(){var N=""+new URL("fault_compact.3c013a62.png",t.meta.url).href,W=""+new URL("maintenance_compact.52fb32ae.png",t.meta.url).href,V=function(e){return o({url:"/console/v1/form-fill/account",method:"get",params:{app_id:e}})},q={key:0,class:"form-fill-dialog"},B={class:"dialog-wrapper"},Q={class:"dialog-container"},X={class:"dialog-header"},G={class:"dialog-title"},Y={class:"dialog-body"},T={class:"form-content"},H={class:"form-header"},K={class:"app-info"},J={class:"app-icon"},Z={class:"app-details"},$={class:"app-name"},ee={class:"app-desc"},ae={class:"form-tip"},te={class:"dialog-footer"},ne=Object.assign({name:"FormFillDialog"},{props:{visible:{type:Boolean,default:!1},appInfo:{type:Object,required:!0,default:function(){return{}}}},emits:["update:visible","success"],setup:function(e,a){var t=a.emit,n=e,A=t,z=i(!1),C=i(),E=i(!1),j=i(!1),F=c({username:"",password:""}),I={username:[{required:!0,message:"请输入账户名称",trigger:"blur"},{min:1,max:100,message:"账户名称长度应在1-100字符之间",trigger:"blur"}],password:[{required:!0,message:"请输入账户密码",trigger:"blur"},{min:1,max:100,message:"账户密码长度应在1-100字符之间",trigger:"blur"}]},P=s((function(){return j.value?"编辑表单代填账户":"创建表单代填账户"}));l((function(){return n.visible}),(function(e){z.value=e,e&&S()}),{immediate:!0}),l(z,(function(e){e||(A("update:visible",!1),U())}));var S=function(){var e=d(r().m((function e(){var a,t,o,i,c,s,l,d;return r().w((function(e){for(;;)switch(e.n){case 0:if(n.appInfo.id){e.n=1;break}return e.a(2);case 1:return E.value=!0,e.p=2,a=String(n.appInfo.id),e.n=3,V(a);case 3:(t=e.v).data&&"0"===t.data.errcode&&t.data.credentials?(o=atob(t.data.credentials),i=o.split(":"),c=p(i,2),s=c[0],l=c[1],F.username=s,F.password=l,j.value=!0):(j.value=!1,F.username="",F.password=""),e.n=5;break;case 4:e.p=4,d=e.v,console.error("加载表单代填账户失败:",d),k.error("加载账户信息失败");case 5:return e.p=5,E.value=!1,e.f(5);case 6:return e.a(2)}}),e,null,[[2,4,5,6]])})));return function(){return e.apply(this,arguments)}}(),O=function(){var e=d(r().m((function e(){var a,t,i,p,c,s;return r().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,null===(a=C.value)||void 0===a?void 0:a.validate();case 1:if(e.v){e.n=2;break}return e.a(2);case 2:return E.value=!0,e.p=3,t=btoa("".concat(F.username,":").concat(F.password)),i={app_id:String(n.appInfo.id),credentials:t},e.n=4,o({url:"/console/v1/form-fill/account",method:"put",data:i});case 4:200===(p=e.v).status||"0"===p.data.errcode?(k.success(j.value?"账户信息已更新":"账户信息已创建"),A("success"),M()):k.error((null===(c=p.data)||void 0===c?void 0:c.message)||"操作失败"),e.n=6;break;case 5:e.p=5,s=e.v,console.error("提交表单失败:",s),k.error("操作失败，请重试");case 6:return e.p=6,E.value=!1,e.f(6);case 7:return e.a(2)}}),e,null,[[3,5,6,7]])})));return function(){return e.apply(this,arguments)}}(),M=function(){z.value=!1},U=function(){var e;F.username="",F.password="",j.value=!1,null===(e=C.value)||void 0===e||e.resetFields()},L=function(e){for(var a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],t=0,n=0;n<e.length;n++)t+=e.charCodeAt(n);return a[t%a.length]};return function(a,t){var n=u("base-icon"),o=u("base-avatar"),r=u("base-input"),i=u("base-form-item"),p=u("base-form"),c=u("base-button");return z.value?(f(),g("div",q,[m("div",{class:"dialog-mask",onClick:M}),m("div",B,[m("div",Q,[m("div",X,[m("span",G,b(P.value),1),v(n,{class:"dialog-close",name:"close",onClick:M})]),m("div",Y,[m("div",T,[m("div",H,[m("div",K,[m("div",J,[v(o,{shape:"square",size:36,src:e.appInfo.iconError?"":e.appInfo.icon,style:x(!e.appInfo.icon||e.appInfo.iconError?"background-color: ".concat(L(e.appInfo.app_name)," !important"):"background-color: #f7f7fa !important"),onError:t[0]||(t[0]=function(){e.appInfo.iconError=!0})},{default:h((function(){return[_(b(!e.appInfo.icon||e.appInfo.iconError?e.appInfo.app_name.slice(0,1):""),1)]})),_:1},8,["src","style"])]),m("div",Z,[m("div",$,b(e.appInfo.app_name),1),m("div",ee,b(e.appInfo.app_desc||"设置表单代填账户信息"),1)])])]),v(p,{ref_key:"formRef",ref:C,model:F,rules:I,"label-width":"80px",onSubmit:y(O,["prevent"])},{default:h((function(){return[v(i,{label:"账户名称",prop:"username"},{default:h((function(){return[v(r,{modelValue:F.username,"onUpdate:modelValue":t[1]||(t[1]=function(e){return F.username=e}),placeholder:"请输入账户名称",maxlength:100,clearable:""},null,8,["modelValue"])]})),_:1}),v(i,{label:"账户密码",prop:"password"},{default:h((function(){return[v(r,{modelValue:F.password,"onUpdate:modelValue":t[2]||(t[2]=function(e){return F.password=e}),type:"password",placeholder:"请输入账户密码",maxlength:100,clearable:"","show-password":""},null,8,["modelValue"])]})),_:1}),m("div",ae,[v(n,{name:"info",color:"#536ce6"}),t[3]||(t[3]=m("span",null,"此账户信息将用于应用的自动登录，请确保账户信息准确无误。",-1))])]})),_:1},8,["model"])])]),m("div",te,[v(c,{onClick:M},{default:h((function(){return t[4]||(t[4]=[_("取消")])})),_:1,__:[4]}),v(c,{type:"primary",loading:E.value,onClick:O},{default:h((function(){return[_(b(j.value?"保存":"创建"),1)]})),_:1},8,["loading"])])])])])):w("",!0)}}}),oe=A(ne,[["__scopeId","data-v-99b414e6"]]),re=""+new URL("no_power.31f14e62.png",t.meta.url).href,ie=""+new URL("no_result.d0219d8d.png",t.meta.url).href,pe={class:"app-page-root"},ce={class:"person"},se={class:"header-right"},le={class:"search-controls"},de={class:"el-row"},ue={class:"el-recent-data"},fe={class:"el-recent-item-container"},ge=["onClick"],me={class:"recent-app-name"},be={key:0,class:"el-recent-empty"},ve={key:1,class:"connected-content"},he={class:"category-menu-text"},xe={key:0,class:"loading-wrapper"},ye={key:1},we=["onClick"],Ae={key:0,class:"status-badge status-badge-compact"},ke={key:1,class:"status-badge status-badge-compact"},_e={class:"app-content"},ze={class:"app-icon"},Ce={class:"tooltip-content"},Ee={key:0},je={key:1},Fe={key:0},Ie={key:2},Pe={class:"app-details"},Se=["title"],Oe={class:"app-name-text"},Me={key:0,class:"status-badge-inline"},Ue={key:1,class:"status-badge-inline"},Le={key:0,class:"app-desc"},De={class:"app-desc-text"},Re={key:2,class:"no-apps-wrapper"},Ne={class:"no-apps-content"},We={class:"no-apps-image"},Ve=["src"],qe={class:"no-apps-text"},Be={key:2,class:"disconnected-content"},Qe={class:"no-connection-wrapper"},Xe={class:"no-connection-image"},Ge=["src"],Ye={name:"AppPage",props:{isConnected:{type:Boolean,default:!1}}},Te=Object.assign(Ye,{props:{isConnected:{type:Boolean,default:!1}},setup:function(e){var t=e,p=s((function(){return!O.isClient()||(!(!ga.token||!ga.token.accessToken)||t.isConnected)})),A=s((function(){return!(!H.value&&K.value)||!(!X.value||0===X.value.length)&&X.value.some((function(e){return e.apps&&e.apps.length>0}))})),q=i(""),B=i(null),Q=i([]),X=i([]),G=i("0"),Y=i("standard"),T=c([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),H=i(!1),K=i(!1),J=i([]),Z=i([]),$=i(!1),ee=i(null),ae=function(e){var a,t,n=(null===(a=ga.userInfo)||void 0===a?void 0:a.id)||(null===(t=ga.userInfo)||void 0===t?void 0:t.name)||"anonymous";return"".concat(e,"_").concat(n)},te="app_favorites",ne="app_recent",Ye=function(e){k({message:e,type:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",duration:arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3})},Te=function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];try{var t=ae(e),n=localStorage.getItem(t);return n?JSON.parse(n):a}catch(o){return console.error("加载".concat(e,"失败:"),o),a}},He=function(e,a){try{var t=ae(e);localStorage.setItem(t,JSON.stringify(a))}catch(n){console.error("保存".concat(e,"失败:"),n)}},Ke=function(e){return J.value.some((function(a){return a.id===e.id}))},Je=function(e){var a=Z.value.findIndex((function(a){return a.id===e.id}));a>-1&&Z.value.splice(a,1),Z.value.unshift({id:e.id,app_name:e.app_name,app_desc:e.app_desc,icon:e.icon,WebUrl:e.WebUrl,maint:e.maint,app_type:e.app_type,open_config:e.open_config,health_status:e.health_status,form_fill_enabled:e.form_fill_enabled,accessTime:Date.now()}),Z.value.length>8&&(Z.value=Z.value.slice(0,8)),He(ne,Z.value)},Ze=function(){Z.value=[],He(ne,[]),Ye("已清空最近访问","info")},$e=function(e){1===e.form_fill_enabled?(ee.value=e,$.value=!0):Ye("该应用未启用表单代填功能","warning")},ea=function(){console.log("表单代填设置成功")},aa=function(e){return e?/^(https?:)?\/\//.test(e)||e.startsWith("data:")||e.startsWith("blob:")||e.endsWith(".svg")?e:M()+e:""},ta=function(){var e=d(r().m((function e(a){var t,n;return r().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,R(a);case 1:if(t=e.v,logger.log("应用启动响应:",t),!(t&&t.startsWith&&t.startsWith("Ok"))){e.n=2;break}return e.a(2,Promise.resolve());case 2:if(!(t&&t.startsWith&&t.startsWith("Failed"))){e.n=3;break}return Ye(t,"error"),e.a(2,Promise.reject(new Error(t)));case 3:return e.a(2,Promise.resolve());case 4:e.n=6;break;case 5:return e.p=5,n=e.v,logger.log("应用启动失败:",n),e.a(2,Promise.reject(n));case 6:return e.a(2)}}),e,null,[[0,5]])})));return function(a){return e.apply(this,arguments)}}(),na=function(){var e=d(r().m((function e(a){var t,n,o;return r().w((function(e){for(;;)switch(e.n){case 0:if((a.WebUrl||!1!==a.open_config.enabled)&&!a.maint){e.n=1;break}return e.a(2);case 1:if(!0!==a.open_config.enabled){e.n=2;break}if("browser"!==a.open_config.open_type&&(a.WebUrl=""),a.form_fill_enabled=0,O.isClient()){e.n=2;break}return Ye("请在客户端访问","warning"),e.a(2);case 2:if(1!==a.form_fill_enabled){e.n=7;break}return e.p=3,e.n=4,V(String(a.id));case 4:if((t=e.v).data&&"0"===t.data.errcode&&t.data.credentials){e.n=5;break}return Ye("请先设置表单代填账户信息","warning"),$e(a),e.a(2);case 5:e.n=7;break;case 6:return e.p=6,e.v,Ye("请先设置表单代填账户信息","warning"),$e(a),e.a(2);case 7:if(Je(a),!a.WebUrl.toLowerCase().startsWith("cs:")){e.n=12;break}return n=a.WebUrl.substring(3),e.p=8,Ye("正在启动爱尔企业浏览器...","info"),e.n=9,ta(n);case 9:Ye("启动成功","success"),e.n=11;break;case 10:e.p=10,e.v,Ye("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3);case 11:e.n=16;break;case 12:if(!O.isClient()){e.n=15;break}return e.n=13,oa(a);case 13:return o=e.v,e.n=14,O.openResource(o);case 14:e.n=16;break;case 15:window.open(a.WebUrl,"_blank");case 16:return e.a(2)}}),e,null,[[8,10],[3,6]])})));return function(a){return e.apply(this,arguments)}}(),oa=function(){var e=d(r().m((function e(a){var t,n,o,i,p,c,s,l,d;return r().w((function(e){for(;;)switch(e.n){case 0:if(t={Type:"URL",Data:{URL:a.WebUrl,OpenExplorer:[]}},!a.open_config.enabled){e.n=15;break}if("browser"!==a.open_config.open_type){e.n=1;break}for(n=[],o=0;o<a.open_config.browser_configs.length;o++)n.push({ExplorerName:a.open_config.browser_configs[o].type,ExplorerParam:a.open_config.browser_configs[o].params});t.Data.OpenExplorer=n,e.n=15;break;case 1:if(t.Type="APP","client"!==a.open_config.open_type){e.n=8;break}i={Windows:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""},MacOS:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""},Linux:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""}},p=0;case 2:if(!(p<a.open_config.program_configs.length)){e.n=7;break}l=a.open_config.program_configs[p].os,e.n="windows"===l?3:"macos"===l?4:"linux"===l?5:6;break;case 3:return i.Windows.AppName=a.open_config.program_configs[p].name,i.Windows.AppPath=a.open_config.program_configs[p].path,i.Windows.AppParam=a.open_config.program_configs[p].params,i.Windows.NotFoundMsg=a.open_config.program_configs[p].notFoundMessage,e.a(3,6);case 4:return i.MacOS.AppName=a.open_config.program_configs[p].bundleId,i.MacOS.AppParam=a.open_config.program_configs[p].params,i.MacOS.NotFoundMsg=a.open_config.program_configs[p].notFoundMessage,e.a(3,6);case 5:return i.Linux.AppName=a.open_config.program_configs[p].name,i.Linux.AppPath=a.open_config.program_configs[p].path,i.Linux.AppParam=a.open_config.program_configs[p].params,i.Linux.NotFoundMsg=a.open_config.program_configs[p].notFoundMessage,e.a(3,6);case 6:p++,e.n=2;break;case 7:t.Data=i,e.n=15;break;case 8:if("system"!==a.open_config.open_type){e.n=15;break}c={Windows:{AppName:"",AppPath:""},MacOS:{AppName:"",AppPath:""},Linux:{AppName:"",AppPath:""}},s=0;case 9:if(!(s<a.open_config.system_app_configs.length)){e.n=14;break}d=a.open_config.system_app_configs[s].os,e.n="windows"===d?10:"macos"===d?11:"linux"===d?12:13;break;case 10:return c.Windows.AppName=a.open_config.system_app_configs[s].type,c.Windows.AppPath=a.open_config.system_app_configs[s].type,e.a(3,13);case 11:return c.MacOS.AppName=a.open_config.system_app_configs[s].type,c.MacOS.AppPath=a.open_config.system_app_configs[s].type,e.a(3,13);case 12:return c.Linux.AppName=a.open_config.system_app_configs[s].type,c.Linux.AppPath=a.open_config.system_app_configs[s].type,e.a(3,13);case 13:s++,e.n=9;break;case 14:t.Data=c;case 15:return e.a(2,t)}}),e)})));return function(a){return e.apply(this,arguments)}}(),ra=function(){var e=d(r().m((function e(){return r().w((function(e){for(;;)switch(e.n){case 0:if(!O.isClient()){e.n=2;break}return e.n=1,U(ga);case 1:logger.log("客户端模式：发送隧道状态刷新事件"),L.emit("refreshTunnelStatus",{timestamp:Date.now(),source:"app-refresh-btn"});case 2:return e.n=3,ua();case 3:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();z((function(){}));var ia=function(e){for(var a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],t=0,n=0;n<e.length;n++)t+=e.charCodeAt(n);return a[t%a.length]},pa=function(){var e=d(r().m((function e(a){return r().w((function(e){for(;;)switch(e.n){case 0:return a.iconError=!0,e.n=1,D();case 1:return e.a(2)}}),e)})));return function(a){return e.apply(this,arguments)}}(),ca=s((function(){return-1===B.value})),sa=function(){if(-1===B.value)J.value.length>0?X.value=[{id:-1,name:"我的收藏",apps:J.value}]:X.value=[{id:-1,name:"我的收藏",apps:[]}];else if(null===B.value||0===B.value){var e=[],a=new Set;Q.value.forEach((function(t){t.apps&&t.apps.length>0&&t.apps.forEach((function(t){a.has(t.id)||(a.add(t.id),e.push(t))}))})),X.value=[{id:0,name:"全部应用",apps:e}]}else{var t=Q.value.filter((function(e){return e.id===B.value}));if(0===t.length)return console.warn("当前选中的分类不存在，回退到全部分类"),B.value=null,G.value="0",void sa();X.value=t}},la=function(e){-1===e?(B.value=-1,G.value="-1"):null===e||0===e?(B.value=null,G.value="0"):(B.value=parseInt(e),G.value=e.toString()),sa()},da=function(){if(q.value){var e=q.value.toLowerCase().trim();if(-1===B.value){var a=J.value.filter((function(a){return a.app_name.toLowerCase().includes(e)}));X.value=[{id:-1,name:"我的收藏",apps:a}]}else if(null===B.value||0===B.value){var t=[],o=new Set;Q.value.forEach((function(a){a.apps&&a.apps.length>0&&a.apps.filter((function(a){return a.app_name.toLowerCase().includes(e)})).forEach((function(e){o.has(e.id)||(o.add(e.id),t.push(e))}))})),X.value=[{id:0,name:"全部应用",apps:t}]}else{var r=Q.value.filter((function(e){return e.id===B.value}));X.value=r.map((function(a){return n(n({},a),{},{apps:a.apps.filter((function(a){return a.app_name.toLowerCase().includes(e)}))})})).filter((function(e){return e.apps.length>0}))}}else sa()},ua=function(){var e=d(r().m((function e(){var a,t,n,i;return r().w((function(e){for(;;)switch(e.n){case 0:return H.value=!0,e.p=1,e.n=2,o({url:"/console/v1/application/getuserapp",method:"get"});case 2:a=e.v,t=a.data,logger.log("API返回数据:",t),0===t.code&&t.data?(n=t.data.map((function(e,a){return{id:a+1,name:e.category,apps:e.apps.map((function(e){return{id:e.id,app_name:e.portal_show_name?e.portal_show_name:e.app_name,app_desc:e.portal_desc?e.portal_desc:"web"===e.app_type?"Web应用":"tun"===e.app_type?"隧道应用":"导航应用",icon:e.icon,maint:2===e.app_status,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl,form_fill_enabled:e.form_fill_enabled,open_config:e.open_config,health_status:e.health_status}}))}})),logger.log("格式化后的数据:",n),Q.value=n,null!==B.value&&-1!==B.value&&0!==B.value&&(n.some((function(e){return e.id===B.value}))||(console.warn("当前选中的分类不存在于新数据中，重置为全部分类"),B.value=null,G.value="0")),sa()):0===t.code&&null===t.data&&(Q.value=[],sa()),e.n=4;break;case 3:e.p=3,i=e.v,console.error("API调用出错:",i);case 4:return e.p=4,H.value=!1,K.value=!0,e.f(4);case 5:return e.a(2)}}),e,null,[[1,3,4,5]])})));return function(){return e.apply(this,arguments)}}(),fa=function(){var e,t,n,o;if(null!==(e=ga.userInfo)&&void 0!==e&&e.id||null!==(t=ga.userInfo)&&void 0!==t&&t.name){J.value=Te(te,[]),Z.value=Te(ne,[]);for(var r=0;r<Z.value.length;r++){var i,p=[],c=a(X.value);try{for(c.s();!(i=c.n()).done;){var s=i.value;if(0===s.id&&"全部应用"===s.name){var l,d=a(s.apps);try{for(d.s();!(l=d.n()).done;){var u=l.value;p.push(u.id),u.id===Z.value[r].id&&(Z.value[r]=u)}}catch(w){d.e(w)}finally{d.f()}}}}catch(w){c.e(w)}finally{c.f()}p.includes(Z.value[r].id)||Z.value.splice(r,1)}for(var f=0;f<J.value.length;f++){var g,m=[],b=a(X.value);try{for(b.s();!(g=b.n()).done;){var v=g.value;if(0===v.id&&"全部应用"===v.name){var h,x=a(v.apps);try{for(x.s();!(h=x.n()).done;){var y=h.value;m.push(y.id),y.id===J.value[f].id&&(J.value[f]=y)}}catch(w){x.e(w)}finally{x.f()}}}}catch(w){b.e(w)}finally{b.f()}m.includes(J.value[f].id)||J.value.splice(f,1)}logger.log("加载用户收藏应用:",J.value.length,"个"),logger.log("加载用户最近访问:",Z.value.length,"个"),logger.log("当前用户ID:",(null===(n=ga.userInfo)||void 0===n?void 0:n.id)||(null===(o=ga.userInfo)||void 0===o?void 0:o.name))}else console.warn("用户信息未加载，跳过存储数据初始化")};C(d(r().m((function e(){var a;return r().w((function(e){for(;;)switch(e.n){case 0:return(a=localStorage.getItem("appViewType"))&&["standard","compact"].includes(a)&&(Y.value=a),B.value=null,G.value="0",e.n=1,ua();case 1:fa();case 2:return e.a(2)}}),e)}))));var ga=E();l((function(){return Y.value}),(function(e){localStorage.setItem("appViewType",e)})),l((function(){var e;return null===(e=ga.userInfo)||void 0===e?void 0:e.id}),(function(e,a){e&&e!==a&&(logger.log("用户切换，重新加载存储数据:",{oldUserId:a,newUserId:e}),fa(),sa())}),{immediate:!1}),l((function(){return ga.token}),(function(e,a){a&&!e&&(logger.log("用户注销，清理存储数据"),J.value=[],Z.value=[],sa())}),{immediate:!1});var ma=function(e){var a=e.apps?e.apps.length:0;if("compact"===Y.value){if(a<=5)return"apps-grid-limited"}else if(a<=2)return"apps-grid-limited";return""};return function(e,a){var t=u("base-icon"),n=u("base-input"),o=u("base-button"),r=u("base-option"),i=u("base-select"),c=u("base-header"),s=u("base-menu-item"),l=u("base-tooltip"),d=u("base-menu"),k=u("base-aside"),z=u("base-avatar"),C=u("base-main"),E=u("base-container");return f(),g("div",pe,[m("div",ce,[v(c,{class:"app-header",height:"44px",padding:"0 16px"},{default:h((function(){return[a[6]||(a[6]=m("div",{class:"header-left"},[m("h1",{class:"page-title"},"应用门户")],-1)),m("div",se,[m("div",le,[v(t,{class:"search-icon",name:"search"}),v(n,{modelValue:q.value,"onUpdate:modelValue":a[0]||(a[0]=function(e){return q.value=e}),class:"search-input",clearable:"",placeholder:"搜索应用","prefix-icon":"Search",onInput:da},null,8,["modelValue"]),v(o,{class:"refresh-btn",icon:"Refresh",size:"small",onClick:ra},{default:h((function(){return a[5]||(a[5]=[m("svg",{"aria-hidden":"true",class:"icon refresh-btn-icon"},[m("use",{"xlink:href":"#icon-search"})],-1)])})),_:1,__:[5]}),v(i,{modelValue:Y.value,"onUpdate:modelValue":a[1]||(a[1]=function(e){return Y.value=e}),class:"view-select",size:"small"},{default:h((function(){return[(f(!0),g(I,null,P(T,(function(e){return f(),j(r,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])])]})),_:1,__:[6]}),p.value?(f(),j(c,{key:0,class:"app-header",height:"36px",padding:"12px 16px 8px 16px"},{default:h((function(){return[m("div",de,[a[8]||(a[8]=m("span",{class:"el-recent-access"},[m("span",{class:"el-recent-text"},"最近访问")],-1)),m("span",ue,[m("span",fe,[(f(!0),g(I,null,P(Z.value,(function(e,a){return f(),g("span",{key:e.id,class:"el-recent-item",onClick:function(a){return na(e)}},[m("span",me,b(e.app_name),1),v(t,{class:"el-recent-icon",name:"close",size:"8px",onClick:y((function(e){return function(e){var a=Z.value.splice(e,1)[0];a&&(Ye("已从最近访问中移除 ".concat(a.app_name),"info"),He(ne,Z.value))}(a)}),["stop"])},null,8,["onClick"])],8,ge)})),128))]),0===Z.value.length?(f(),g("span",be," 暂无最近访问记录 ")):w("",!0),Z.value.length>0?(f(),g("svg",{key:1,"aria-hidden":"true",class:"icon el-recent-clear",title:"清空最近访问",onClick:Ze},a[7]||(a[7]=[m("use",{"xlink:href":"#icon-qingkong"},null,-1)]))):w("",!0)])])]})),_:1})):w("",!0),p.value?(f(),g("div",ve,[v(E,{class:"flex-container flex-row"},{default:h((function(){return[v(k,{class:"category-aside",width:"104px"},{default:h((function(){return[v(d,{"default-active":G.value,"background-color":"#ffffff",class:"category-menu",mode:"vertical",onSelect:la},{default:h((function(){return[v(s,{class:"category-menu-item",index:"-1",onClick:a[2]||(a[2]=function(e){return la(-1)})},{default:h((function(){return a[9]||(a[9]=[_(" 收藏 ")])})),_:1,__:[9]}),v(s,{class:"category-menu-item",index:"0",onClick:a[3]||(a[3]=function(e){return la(null)})},{default:h((function(){return a[10]||(a[10]=[_(" 全部 ")])})),_:1,__:[10]}),(f(!0),g(I,null,P(Q.value,(function(e){return f(),j(s,{key:e.id,index:e.id.toString()},{default:h((function(){return[v(l,{content:e.name,disabled:e.name.length<=5,effect:"light",placement:"right"},{default:h((function(){return[m("span",he,b((a=e.name,a.length<=5?a:a.substring(0,4)+"...")),1)];var a})),_:2},1032,["content","disabled"])]})),_:2},1032,["index"])})),128))]})),_:1},8,["default-active"])]})),_:1}),v(C,{class:"app-main custom-scrollbar"},{default:h((function(){return[H.value?(f(),g("div",xe)):A.value?(f(),g("div",ye,[(f(!0),g(I,null,P(X.value,(function(e){return f(),g("div",{key:e.id,class:"category-section"},[m("div",{class:S([["view-".concat(Y.value),ma(e),{"favorite-category":ca.value}],"apps-grid"])},[(f(!0),g(I,null,P(e.apps,(function(e){return f(),g("div",{key:e.id,class:S([{disabled:!e.WebUrl&&!1===e.open_config.enabled||e.maint},"app-item transition transform-hover"]),onClick:function(a){return na(e)}},[2===e.health_status&&"compact"===Y.value?(f(),g("div",Ae,a[11]||(a[11]=[m("img",{alt:"故障",class:"status-maint",src:N},null,-1)]))):e.maint&&"compact"===Y.value?(f(),g("div",ke,a[12]||(a[12]=[m("img",{alt:"维护中",class:"status-maint",src:W},null,-1)]))):w("",!0),m("div",_e,[m("div",ze,["portal"!==e.app_type||e.WebUrl&&"browser"===e.open_config.open_type?(f(),j(l,{key:0,effect:"light",placement:"bottom"},{content:h((function(){return[m("div",Ce,[e.WebUrl?(f(),g("span",Ee,b(e.WebUrl),1)):"tun"===e.app_type?(f(),g("div",je,[(f(!0),g(I,null,P(e.app_sites,(function(e){return f(),g("div",{key:e.id},[_(b(e.protocol+":"+e.address)+" ",1),e.port?(f(),g("span",Fe,":"+b(e.port),1)):w("",!0)])})),128))])):(f(),g("span",Ie,"暂无访问地址"))])]})),default:h((function(){return[v(z,{size:"compact"===Y.value?20:28,src:e.iconError?"":aa(e.icon),style:x(!e.icon||e.iconError?"background-color: ".concat(ia(e.app_name)," !important"):"background-color: #f7f7fa !important"),shape:"square",onError:function(){return pa(e)}},{default:h((function(){return[_(b(!e.icon||e.iconError?e.app_name.slice(0,1):""),1)]})),_:2},1032,["size","src","style","onError"])]})),_:2},1024)):(f(),j(z,{key:1,size:"compact"===Y.value?20:28,src:e.iconError?"":aa(e.icon),style:x(!e.icon||e.iconError?"background-color: ".concat(ia(e.app_name)," !important"):"background-color: #f7f7fa !important"),shape:"square",onError:function(){return pa(e)}},{default:h((function(){return[_(b(!e.icon||e.iconError?e.app_name.slice(0,1):""),1)]})),_:2},1032,["size","src","style","onError"]))]),m("div",Pe,[m("div",{title:e.app_name,class:"app-name"},[m("span",Oe,b(e.app_name),1),2===e.health_status&&"standard"===Y.value?(f(),g("span",Me,a[13]||(a[13]=[m("img",{alt:"故障",class:"status-maint",src:"data:image/png;base64,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"},null,-1)]))):e.maint&&"standard"===Y.value?(f(),g("span",Ue,a[14]||(a[14]=[m("img",{alt:"维护中",class:"status-maint",src:"data:image/png;base64,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"},null,-1)]))):w("",!0)],8,Se),"standard"===Y.value?(f(),g("div",Le,[v(l,{content:e.app_desc||"应用程序",disabled:!(e.app_desc&&e.app_desc.length>8),effect:"light",placement:"bottom"},{default:h((function(){return[m("span",De,b(e.app_desc||"应用程序"),1)]})),_:2},1032,["content","disabled"])])):w("",!0)]),v(t,{name:Ke(e)?"yishoucang":"shoucang",title:Ke(e)?"取消收藏":"收藏",class:"app-collect-icon",onClick:y((function(a){return t=e,(n=J.value.findIndex((function(e){return e.id===t.id})))>-1?(J.value.splice(n,1),Ye("已取消收藏 ".concat(t.app_name),"info")):(J.value.push({id:t.id,app_name:t.app_name,app_desc:t.app_desc,icon:t.icon,WebUrl:t.WebUrl,maint:t.maint,app_type:t.app_type,open_config:t.open_config,health_status:t.health_status,form_fill_enabled:t.form_fill_enabled,favoriteTime:Date.now()}),Ye("已收藏 ".concat(t.app_name),"success")),He(te,J.value),void sa();var t,n}),["stop"])},null,8,["name","title","onClick"]),1===e.form_fill_enabled||ca.value&&0!==e.form_fill_enabled?(f(),j(t,{key:0,class:"app-form-fill-icon",name:"bianji",title:"编辑表单代填",onClick:y((function(a){return $e(e)}),["stop"])},null,8,["onClick"])):w("",!0)])],10,we)})),128))],2)])})),128))])):(f(),g("div",Re,[m("div",Ne,[m("div",We,[m("img",{src:F(ie),alt:"暂无搜索结果"},null,8,Ve)]),m("div",qe,b(q.value?"暂无搜索结果":"暂无应用数据，请添加应用"),1)])]))]})),_:1})]})),_:1})])):(f(),g("div",Be,[m("div",Qe,[m("div",Xe,[m("img",{src:F(re),alt:"请先建立安全连接"},null,8,Ge)]),a[15]||(a[15]=m("div",{class:"no-connection-text"},[m("h3",{style:{"font-size":"16px"}},"请先建立安全连接"),m("p",{style:{"font-size":"12px"}},"成功连接后可查看授权的应用")],-1))])]))]),v(oe,{"app-info":ee.value,visible:$.value,onSuccess:ea,"onUpdate:visible":a[4]||(a[4]=function(e){return $.value=e})},null,8,["app-info","visible"])])}}});e("default",A(Te,[["__scopeId","data-v-ebc7c85c"]]))}}}))}();
