[SERVICE]
    # Flush
    # =====
    # set an interval of seconds before to flush records to a destination
    flush        5
    daemon       yes
    log_level    warn
    parsers_file asec-parsers.conf
    #plugins_file plugins.conf
    storage.metrics on
[INPUT]
    Name         tail
    Path ${PATH}\*.log
    Parser AsecLogParse
    Tag clientlog
[FILTER]
    Name grep
    Match clientlog
    Regex level (warn|warning|error|criti|panic|critical) 
[OUTPUT]
    name   loki
    host ${LOKI_HOST}
    port ${LOKI_PORT}
    tls on
    tls.verify  off
    match  clientlog
    labels job=client-log, mystream=$sub['stream'], appliance_id=${APPLIANCE_ID},user_name=${USERNAME},appliance_type=${APPLIANCE_TYPE}