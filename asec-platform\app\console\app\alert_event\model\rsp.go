package model

import "asdsec.com/asec/platform/app/console/common/utils"

type GetAlertEventRsp struct {
	SeverityId int    `gorm:"column:severity_id" json:"severity_id"`
	Severity   string `gorm:"column:severity" json:"severity"`
	EventCount int    `gorm:"column:event_count" json:"event_count"`
	MinScore   int    `gorm:"column:min_score" json:"min_score"`
	MaxScore   int    `gorm:"column:max_score" json:"max_score"`
}

type GetAlertEventDetailResp struct {
	UserName          string          `gorm:"column:user_name" json:"user_name"`
	AgentName         string          `gorm:"column:agent_name" json:"agent_name"`
	PolicyName        string          `gorm:"column:policy_name" json:"policy_name"`
	SeverityId        int             `gorm:"column:severity_id" json:"severity_id"`
	Severity          string          `gorm:"column:severity" json:"severity"`
	AlertType         string          `gorm:"column:alert_type" json:"alert_type"`
	OccurTime         utils.FrontTime `gorm:"column:occur_time" json:"occur_time"`
	FileName          string          `gorm:"column:file_name" json:"file_name"`
	FileType          string          `gorm:"column:file_type" json:"file_type"`
	FileSize          int64           `gorm:"column:file_size" json:"file_size"`
	DataCategory      string          `gorm:"column:data_category" json:"data_category"`
	SensitiveLevel    string          `gorm:"column:sensitive_level" json:"sensitive_level"`
	SensitiveRuleName string          `gorm:"column:sensitive_rule_name" json:"sensitive_rule_name"`
	Md5               string          `gorm:"column:md5" json:"md5"`
	Sha256            string          `gorm:"column:sha256" json:"sha256"`
	Channel           string          `gorm:"column:channel" json:"channel"`
	ChannelType       string          `gorm:"column:channel_type" json:"channel_type"`
	UserTags          []string        `gorm:"column:user_tags;type:string" json:"user_tags"`
	ExtensionName     string          `gorm:"column:extension_name" json:"extension_name"`
	RealExtensionName string          `gorm:"column:real_extension_name" json:"real_extension_name"`
	Score             int             `gorm:"column:score" json:"score"`
	ScoreEntity
	FilePath         string `gorm:"column:file_path" json:"file_path"`
	OriginalFileName string `gorm:"column:original_file_name" json:"original_file_name"`
	OriginalFilePath string `gorm:"column:original_file_path" json:"original_file_path"`
	Owner            string `gorm:"column:owner" json:"owner"`
	NameMatchInfo    string `gorm:"column:name_match_info" json:"name_match_info"`
	ContentMatchInfo string `gorm:"column:content_match_info" json:"content_match_info"`
	FileCategoryId   int    `gorm:"column:file_category_id" json:"file_category_id"`
	SrcPath          string `gorm:"-" json:"src_path"`
	SourceType       string `gorm:"-" json:"source_type"`
	SourceName       string `gorm:"-" json:"source_name"`
	DstPath          string `gorm:"-" json:"dst_path"`
	Activity         string `gorm:"column:activity" json:"activity"`
	FileEventId      string `gorm:"column:file_event_id" json:"file_event_id"`
	FileCategory     string `gorm:"-" json:"file_category"`
	PlatType         string `gorm:"column:plat_type" json:"plat_type"`
	DisposeAction    int    `gorm:"column:dispose_action" json:"dispose_action"`
}

type GetAlertEventListResp struct {
	UuId              string          `gorm:"column:uuid" json:"uuid"`
	SeverityId        int             `gorm:"column:severity_id" json:"severity_id"`
	Severity          string          `gorm:"column:severity" json:"severity" excelColumn:"A" excelDesc:"Alert.AlertData.Severity"`
	AlertType         string          `gorm:"column:alert_type" json:"alert_type"`
	PolicyName        string          `gorm:"column:policy_name" json:"policy_name" excelColumn:"B" excelDesc:"Alert.AlertData.PolicyName" excelWidth:"30"`
	OccurTime         utils.FrontTime `gorm:"column:occur_time;type:timestamptz" json:"occur_time" excelColumn:"C" excelDesc:"Alert.AlertData.OccurTime" excelWidth:"25"`
	AlertSummary      string          `gorm:"column:alert_summary" json:"alert_summary" excelColumn:"D" excelDesc:"Alert.AlertData.EventSummary" excelWidth:"80"`
	UserName          string          `gorm:"column:user_name" json:"user_name"`
	FileName          string          `gorm:"column:file_name" json:"file_name"`
	FileType          string          `gorm:"column:file_type" json:"file_type"`
	FileSize          int64           `gorm:"column:file_size" json:"file_size"`
	RealExtensionName string          `gorm:"column:real_extension_name" json:"real_extension_name"`
	ExtensionName     string          `gorm:"column:extension_name" json:"extension_name"`
	Channel           string          `gorm:"column:channel" json:"channel"`
	Activity          string          `gorm:"column:activity" json:"activity"`
	ChannelType       string          `gorm:"column:channel_type" json:"channel_type"`
	SensitiveRuleName string          `gorm:"column:sensitive_rule_name" json:"sensitive_rule_name"`
	Score             int             `gorm:"column:score" json:"score"`
	SensitiveLevel    string          `gorm:"column:sensitive_level" json:"sensitive_level"`
	DisposeAction     int             `gorm:"column:dispose_action" json:"dispose_action"`
	ScoreEntity
}

type ScoreEntity struct {
	TagScore             int    `gorm:"column:tag_score" json:"tag_score"`
	TagName              string `gorm:"column:tag_name" json:"tag_name"`
	ChannelScore         int    `gorm:"column:channel_score" json:"channel_score"`
	SensitiveDataScore   int    `gorm:"column:sensitive_data_score" json:"sensitive_data_score"`
	HideSuffixScore      int    `gorm:"column:hide_suffix_score" json:"hide_suffix_score"`
	FileCompressionScore int    `gorm:"column:file_compression_score" json:"file_compression_score"`
	RenameScore          int    `gorm:"column:rename_score" json:"rename_score"`
	CopyScore            int    `gorm:"column:copy_score" json:"copy_score"`
}
