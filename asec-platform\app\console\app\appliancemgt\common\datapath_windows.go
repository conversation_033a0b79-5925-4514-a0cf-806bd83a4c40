package common

import "path/filepath"

// 同样的配置在datapath_linux.go文件也有一份，分别在不同的环境上使用
// 名称相同实际不需要存两份，导致如果要改其中一个两个都得改，不然在windows本地和linux服务器上行为不一致

var BaseDir = "F:\\"
var AgentDir = filepath.Join(BaseDir, "agents")
var GatewayDir = "D:\\data\\asec\\gateway" 
var AgentWinFileName = "ASec_Client_Setup_Win32.exe"
var AgentWinFileDownloadTemp = "ASec_Client_Setup_%s[%s].exe"
var AgentDarwinFileName = "ASec_Client_Installer.pkg"
var AgentDarwinFileDownloadTemp = "ASec_Client_Installer_%s[%s].pkg"
var AgentAndroidFileName = "ASec_Client.apk"
var AgentAndroidFileDownloadTemp = "ASec_Client_%s[%s].apk"
var AuthDir = "F:\\data\\auth"
var FreeOptFileName = "FreeOTP.apk"
var GoogleAuthFileName = "Google_Authenticator.apk"
var ConfigPath = "D:\\data\\asec\\config.ini"

