// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbRole(db *gorm.DB, opts ...gen.DOOption) tbRole {
	_tbRole := tbRole{}

	_tbRole.tbRoleDo.UseDB(db, opts...)
	_tbRole.tbRoleDo.UseModel(&model.TbRole{})

	tableName := _tbRole.tbRoleDo.TableName()
	_tbRole.ALL = field.NewAsterisk(tableName)
	_tbRole.ID = field.NewString(tableName, "id")
	_tbRole.Name = field.NewString(tableName, "name")
	_tbRole.Description = field.NewString(tableName, "description")
	_tbRole.CorpID = field.NewString(tableName, "corp_id")
	_tbRole.CreatedAt = field.NewTime(tableName, "created_at")
	_tbRole.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbRole.IsAllUser = field.NewInt16(tableName, "is_all_user")

	_tbRole.fillFieldMap()

	return _tbRole
}

type tbRole struct {
	tbRoleDo tbRoleDo

	ALL         field.Asterisk
	ID          field.String // 角色id
	Name        field.String // 角色名称，租户下角色名唯一
	Description field.String // 角色描述
	CorpID      field.String // 租户id
	CreatedAt   field.Time
	UpdatedAt   field.Time
	IsAllUser   field.Int16

	fieldMap map[string]field.Expr
}

func (t tbRole) Table(newTableName string) *tbRole {
	t.tbRoleDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbRole) As(alias string) *tbRole {
	t.tbRoleDo.DO = *(t.tbRoleDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbRole) updateTableName(table string) *tbRole {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.Description = field.NewString(table, "description")
	t.CorpID = field.NewString(table, "corp_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.IsAllUser = field.NewInt16(table, "is_all_user")

	t.fillFieldMap()

	return t
}

func (t *tbRole) WithContext(ctx context.Context) *tbRoleDo { return t.tbRoleDo.WithContext(ctx) }

func (t tbRole) TableName() string { return t.tbRoleDo.TableName() }

func (t tbRole) Alias() string { return t.tbRoleDo.Alias() }

func (t *tbRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbRole) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 7)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["description"] = t.Description
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["is_all_user"] = t.IsAllUser
}

func (t tbRole) clone(db *gorm.DB) tbRole {
	t.tbRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbRole) replaceDB(db *gorm.DB) tbRole {
	t.tbRoleDo.ReplaceDB(db)
	return t
}

type tbRoleDo struct{ gen.DO }

func (t tbRoleDo) Debug() *tbRoleDo {
	return t.withDO(t.DO.Debug())
}

func (t tbRoleDo) WithContext(ctx context.Context) *tbRoleDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbRoleDo) ReadDB() *tbRoleDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbRoleDo) WriteDB() *tbRoleDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbRoleDo) Session(config *gorm.Session) *tbRoleDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbRoleDo) Clauses(conds ...clause.Expression) *tbRoleDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbRoleDo) Returning(value interface{}, columns ...string) *tbRoleDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbRoleDo) Not(conds ...gen.Condition) *tbRoleDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbRoleDo) Or(conds ...gen.Condition) *tbRoleDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbRoleDo) Select(conds ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbRoleDo) Where(conds ...gen.Condition) *tbRoleDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbRoleDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbRoleDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbRoleDo) Order(conds ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbRoleDo) Distinct(cols ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbRoleDo) Omit(cols ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbRoleDo) Join(table schema.Tabler, on ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbRoleDo) Group(cols ...field.Expr) *tbRoleDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbRoleDo) Having(conds ...gen.Condition) *tbRoleDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbRoleDo) Limit(limit int) *tbRoleDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbRoleDo) Offset(offset int) *tbRoleDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbRoleDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbRoleDo) Unscoped() *tbRoleDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbRoleDo) Create(values ...*model.TbRole) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbRoleDo) CreateInBatches(values []*model.TbRole, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbRoleDo) Save(values ...*model.TbRole) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbRoleDo) First() (*model.TbRole, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbRole), nil
	}
}

func (t tbRoleDo) Take() (*model.TbRole, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbRole), nil
	}
}

func (t tbRoleDo) Last() (*model.TbRole, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbRole), nil
	}
}

func (t tbRoleDo) Find() ([]*model.TbRole, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbRole), err
}

func (t tbRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbRole, err error) {
	buf := make([]*model.TbRole, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbRoleDo) FindInBatches(result *[]*model.TbRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbRoleDo) Attrs(attrs ...field.AssignExpr) *tbRoleDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbRoleDo) Assign(attrs ...field.AssignExpr) *tbRoleDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbRoleDo) Joins(fields ...field.RelationField) *tbRoleDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbRoleDo) Preload(fields ...field.RelationField) *tbRoleDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbRoleDo) FirstOrInit() (*model.TbRole, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbRole), nil
	}
}

func (t tbRoleDo) FirstOrCreate() (*model.TbRole, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbRole), nil
	}
}

func (t tbRoleDo) FindByPage(offset int, limit int) (result []*model.TbRole, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbRoleDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbRoleDo) Delete(models ...*model.TbRole) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbRoleDo) withDO(do gen.Dao) *tbRoleDo {
	t.DO = *do.(*gen.DO)
	return t
}
