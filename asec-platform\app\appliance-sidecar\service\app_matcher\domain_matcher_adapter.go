package app_matcher

import (
	pb "asdsec.com/asec/platform/api/application/v1"
	"strings"
)

type DomainMatcherAdapter struct {
	baseMatcher BaseMatcher
}

func (d DomainMatcherAdapter) GetAppInfo() *pb.AppInfo {
	return d.baseMatcher.AppInfo
}

func (d DomainMatcherAdapter) GetMatcherType() UrlMatcherType {
	return DomainMatcher
}

func (d DomainMatcherAdapter) DoMatcher(srcUrl string, srcPort int) error {
	if !(srcUrl == d.baseMatcher.UrlRule) {
		return d.baseMatcher.NoMatchError(srcUrl, srcPort, "")
	}

	matchPort, detail := d.baseMatcher.CommonMatchPort(srcPort)
	if !matchPort {
		return d.baseMatcher.NoMatchError(srcUrl, srcPort, detail)
	}
	return nil
}

func NewDomainMatcherAdapter(appInfo *pb.AppInfo) (DomainMatcherAdapter, error) {
	urlRule := strings.ToLower(appInfo.Address)
	ps, err := GetPortMatchers(appInfo.Port)
	if err != nil {
		return DomainMatcherAdapter{}, err
	}

	return DomainMatcherAdapter{
		baseMatcher: BaseMatcher{
			UrlRule:  urlRule,
			PortRule: ps,
			AppInfo:  appInfo,
		},
	}, nil
}
