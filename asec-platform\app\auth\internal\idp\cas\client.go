package cas

import (
	"github.com/go-kratos/kratos/v2/log"
)

// CasClient 提供Cas认证和用户同步的客户端
type CasClient struct {
	AuthData string
	FieldMap   string
	OpenType   string
	logger     *log.Helper
}

// NewCasClient 创建一个新的Cas客户端
func NewCasClient(authData, fieldMap, openType string) *CasClient {
	logger := log.With(log.GetLogger(), "module", "Cas_client")
	return &CasClient{
		AuthData: authData,
		FieldMap:   fieldMap,
		OpenType:   openType,
		logger:     log.NewHelper(logger),
	}
}