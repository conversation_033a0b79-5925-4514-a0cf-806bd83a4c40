package model

type AlertEvent struct {
	UuId              string `gorm:"column:uuid" json:"uuid"`
	CorpId            string `gorm:"column:corp_id" db:"corp_id" json:"corp_id"`
	UserName          string `gorm:"column:user_name" json:"user_name"`
	AlertType         string `gorm:"column:alert_type" json:"alert_type"`
	SeverityId        int    `gorm:"column:severity_id" json:"severity_id"`
	Md5               string `gorm:"column:md5" json:"md5"`
	Sha256            string `gorm:"column:sha256" json:"sha256"`
	Channel           string `gorm:"column:channel" json:"channel"`
	ChannelType       string `gorm:"column:channel_type" json:"channel_type"`
	AgentName         string `gorm:"column:agent_name" json:"agent_name"`
	PolicyName        string `gorm:"column:policy_name" json:"policy_name"`
	OccurTime         int64  `gorm:"column:occur_time" json:"occur_time"`
	AlertSummary      string `gorm:"column:alert_summary" json:"alert_summary"`
	FileName          string `gorm:"column:file_name" json:"file_name"`
	FileType          string `gorm:"column:file_type" json:"file_type"`
	FileSize          int64  `gorm:"column:file_size" json:"file_size"`
	RealExtensionName string `gorm:"column:real_extension_name" json:"real_extension_name"`
	ExtensionName     string `gorm:"column:extension_name" json:"extension_name"`
	DataCategory      string `gorm:"column:data_category" json:"data_category"`
}
