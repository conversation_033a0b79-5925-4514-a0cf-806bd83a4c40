syntax = "proto3";

package api.auth.v1.user;

import "google/api/annotations.proto";

option go_package = "asdsec.com/asec/platform/api/auth/v1/user;user";
option java_multiple_files = true;
option java_package = ".api.auth.v1.user";

service User {
	rpc GetLoginUserInfo(GetLoginUserInfoReq) returns(GetLoginUserInfoReply){
		option (google.api.http) = {
			get: "/auth/user/v1/login_user"
		};
	}
	rpc UpdatePasswd(UpdatePasswordReq) returns(UpdatePasswordReply){
		option (google.api.http) = {
			put: "/auth/user/v1/password",
			body: "*"
		};
	}
	rpc Logout(LogoutReq) returns(LogoutReply){
		option (google.api.http) = {
			post: "/auth/user/v1/logout",
			body: "*"
		};
	}
	rpc RedirectVerify (RedirectVerifyRequest) returns(RedirectVerifyReply){
		option (google.api.http) = {
			get: "/auth/user/v1/redirect_verify",
		};
	};
}

enum StatusCode {
	SUCCESS = 0;
	FAILED = 1;
}

message RedirectVerifyRequest{
	string redirect_url = 1;
}
message RedirectVerifyReply{
	string url = 1;
}

message GetLoginUserInfoReq{
}

message RoleInfo {
	string id = 1;
	string name = 2;
}

message GetLoginUserInfoReply{
	message LoginUserInfo {
		string id = 1;
		string name = 2;
		string group_id = 3;
		string group_name = 4;
		string source_id = 6;
		string phone = 7;
		string email = 8;
		string avatar = 9;
		repeated RoleInfo roles = 10;
		string source_type = 11;
		string source_name = 12;
		string expire_type = 13;
		string expire_end = 14;
		string display_name = 15;
		string auth_type =16;
	}
	LoginUserInfo user_info = 1;
}

message UpdatePasswordReq{
	string password = 2;
	string new_password = 3;
}

message UpdatePasswordReply{
		StatusCode status = 1;
}

message LogoutReq{
}

message LogoutReply{
	StatusCode status = 1;
	string redirect_url = 2; 
}