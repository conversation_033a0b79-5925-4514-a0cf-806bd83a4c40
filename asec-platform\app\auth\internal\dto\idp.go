package dto

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
)

// AssistIDPNumLimit 辅助认证服务器的限制
var AssistIDPNumLimit = map[IDPType]int64{
	IDPTypeTotp:       1,
	IDPTypeVerifyCode: 2,
}

// IDPType 认证服务器类型
type IDPType string

// 验证码认证通道类型
type VerifyCodeChannelType string

const (
	// 短信通道
	ChannelSMS VerifyCodeChannelType = "sms"
	// 邮件通道
	ChannelEmail VerifyCodeChannelType = "email"
)

const (
	IDPTypeQiYeWx     = IDPType(UserSourceQiYeWx)
	IDPTypeZZDMobile  = IDPType(UserSourceZZDMobile)
	IDPTypeZZDScan    = IDPType(UserSourceZZDScan)
	IDPTypeFeiShu     = IDPType(UserSourceFeiShu)
	IDPTypeDingtalk   = IDPType(UserSourceDingtalk)
	IDPTypeLdap       = IDPType(UserSourceLdap)
	IDPTypeMsad       = IDPType(UserSourceMsad)
	IDPTypeLocal      = IDPType(UserSourceLocal)
	IDPTypeSMS        = IDPType(UserSourceSMS)
	IDPTypeInfogo     = IDPType(UserSourceInfogo)
	IDPTypeOAuth2     = IDPType(UserSourceOAuth2)
	IDPTypeCas        = IDPType(UserSourceCas)
	IDPTypeWeb        = IDPType(UserSourceWebOAuth)
	IDPTypeEmail      = IDPType(UserSourceEmail)
	IDPTypeVerifyCode = IDPType(UserSourceVerifyCode)
	IDPTypeTotp       = IDPType("totp")
)

var MainIDPTypeName = map[IDPType]string{
	IDPTypeQiYeWx:    "企业微信",
	IDPTypeFeiShu:    "飞书",
	IDPTypeLocal:     "本地用户",
	IDPTypeDingtalk:  "钉钉",
	IDPTypeLdap:      "Open LDAP",
	IDPTypeMsad:      "MS ActiveDirectory",
	IDPTypeOAuth2:    "OAuth2.0票据",
	IDPTypeCas:       "CAS票据",
	IDPTypeWeb:       "WEB服务器",
	IDPTypeEmail:     "邮箱认证",
	IDPTypeZZDMobile: "浙政钉拉起",
	IDPTypeZZDScan:   "专有钉钉",
}

var AssistIDPTypeName = map[IDPType]string{
	//IDPTypeTotp: "TOTP", // todo 后续再支持
	IDPTypeVerifyCode: "验证码",
	IDPTypeTotp:       "令牌口令(TOTP)",
}

var AssistIDPType = []string{
	"verify_code", // 验证码认证
	"totp",
}

// AssistIDPSourceID 辅助认证服务器的来源id为"0"，表示不受用户来源的限制
const AssistIDPSourceID = "0"

type VerifyCodeConfig struct {
	// 通道类型：短信或邮件
	ChannelType VerifyCodeChannelType `json:"channel_type"`

	// 短信配置（与现有SmsConfig兼容）
	SmsConfig

	// 邮件配置
	EmailConfig EmailConfig `json:"email_config"`
}

type EmailConfig struct {
	ConfigData string `json:"configData"`
	SearchMap  string `json:"search_map"`
}

// IDPLocalAttr 本地认证服务器的属性
var IDPLocalAttr = []KV{
	{Key: AttrKeyAuthType, Value: AttrValPasswd}, // 支持账号密码登陆
}

const (
	AttrKeyAuthType = "auth_type"
	AttrValPasswd   = "password"

	AttrKeyWxCorpId      = "wx_corp_id"
	AttrKeyWxAgentId     = "wx_agent_id"
	AttrKeyWxAgentSecret = "wx_agent_secret"
	AttrKeyWxFieldMap    = "wx_field_map"

	AttrKeyFeiShuAppId    = "fs_app_id"
	AttrKeyFeiShuSecret   = "fs_app_secret"
	AttrKeyFeiShuFieldMap = "fs_field_map"

	AttrKeyDingtalkAppKey    = "dingtalk_app_key"
	AttrKeyDingtalkAppSecret = "dingtalk_app_secret"
	AttrKeyDingtalkCorpId    = "dingtalk_dd_corp_id"
	AttrKeyDingtalkFieldMap  = "dingtalk_field_map"
	AttrKeyAdFieldMap        = "ad_field_map"

	AttrKeyInfogoEndpoint = "infogo_api_endpoint"
	AttrKeyInfogoUsername = "infogo_api_user"
	AttrKeyInfogoPass     = "infogo_api_pass"
	AttrKeyInfogoFieldMap = "infogo_field_map"

	AttrKeyAdServerAddress         = "ad_server_address"
	AttrKeyAdAdministratorAccount  = "ad_administrator_account"
	AttrKeyAdAdministratorPassword = "ad_administrator_password"
	AttrKeyAdSearchEntry           = "ad_search_entry"
	AttrKeyAdUserFilter            = "ad_user_filter"
	AttrKeyAdGroupFilter           = "ad_group_filter"
	AttrKeyAdExternalId            = "ad_external_id"
	AttrKeyAdFiltration            = "ad_filtration"
	AttrKeyAdGroup                 = "ad_group"

	AttrKeySmsType            = "sms_type"
	AttrKeySmsAccessKeyId     = "sms_AccessKey_id"
	AttrKeySmsAccessKeySecret = "sms_AccessKey_Secret"
	AttrKeySmsSdkAppId        = "sms_sdk_app_id"
	AttrKeySmsSecretId        = "sms_secret_id"
	AttrKeySmsSecretKey       = "sms_secret_key"
	AttrKeySmsAppKey          = "sms_app_key"
	AttrKeySmsAppSecret       = "sms_app_secret"
	AttrKeySmsChannelNo       = "sms_channel_no"
	AttrKeySmsExpirationTime  = "sms_expiration_time"
	AttrKeySmsSignName        = "sms_sign_name"
	AttrKeySmsTemplateCode    = "sms_template_code"

	AttrKeyOAuth2GlobalData = "oauth2_global_data"
	AttrKeyOAuth2SearchMap  = "oauth2_search_map"
	AttrKeyOAuth2CodeData   = "oauth2_code_data"
	AttrKeyOAuth2UserData   = "oauth2_user_data"
	AttrKeyOAuth2LogoutOpen = "oauth2_logout_open"
	AttrKeyOAuth2LogoutData = "oauth2_logout_data"
	AttrKeyOAuth2OpenType   = "oauth2_open_type"
	AttrKeyOAuth2FieldMap   = "oauth2_field_map"
	AttrKeyPailaFieldMap    = "pailaFieldMap"
	AttrKeyCasAuthData      = "cas_auth_data"
	AttrKeyCasFieldMap      = "cas_field_map"
	AttrKeyCasOpenType      = "cas_open_type"
	AttrKeyCasSearchMap     = "cas_search_map"
	AttrKeyWebUserData      = "web_auth_user_data"
	AttrKeyWebSearchMap     = "web_auth_search_map"
	AttrKeyTop              = "token_deviation"

	AttrKeyEmailConfigData = "email_config_data"
	AttrKeyEmailSearchMap  = "email_search_map"
)

const (
	WxWebAccessTokenPrefix     = "wx_web_access_token_"
	FsWebAccessTokenPrefix     = "fs_web_access_token_"
	WxFakeRootDepartmentId     = "0"
	InfogoFakeRootDepartmentId = "-1"
	FsFakeRootDepartmentId     = "0"
)

type IDPTypeObject struct {
	Type         IDPType
	TemplateType string
	Name         string
	TemplateAttr map[string]string
}
type IDPTypeResp struct {
	MainIdp   []IDPTypeObject
	AssistIdp []IDPTypeObject
}

type IDPBasic struct {
	ID           string
	SourceID     string
	Name         string
	Type         string
	TypeName     string
	TemplateType string
	CorpID       string
	Avatar       string
	Description  string
	Enable       bool
	IsDefault    bool
}

type IDPInfo struct {
	IDPBasic
	Attrs         map[string]string
	BindGroupList []BindGroupInfo
}

type ListIDPResp struct {
	IdpInfos []IDPInfo
	Count    uint32
}

type IDPWithGroup struct {
	IDPBasic
	GroupID   string
	GroupName string
}

type IDPWithAttr struct {
	IDPBasic
	Key   string
	Value string
}

type IDPWithGroups []IDPWithGroup

func (g IDPWithGroups) ToIDPInfo() ([]IDPInfo, error) {
	idpToGroup := make(map[string][]BindGroupInfo)
	for _, e := range g {
		idpToGroup[e.ID] = append(idpToGroup[e.ID], BindGroupInfo{
			ID:   e.GroupID,
			Name: e.GroupName,
		})
	}

	var result []IDPInfo
	recorded := make(map[string]struct{})
	for _, e := range g {
		if _, ok := recorded[e.ID]; ok {
			continue
		}
		var basicInfo IDPBasic
		if err := copier.Copy(&basicInfo, &e); err != nil {
			return []IDPInfo{}, err
		}
		result = append(result, IDPInfo{
			IDPBasic:      basicInfo,
			BindGroupList: idpToGroup[e.ID],
		})
		recorded[e.ID] = struct{}{}
	}
	return result, nil
}

type InfogoConfig struct {
	Endpoint string `json:"endpoint,omitempty"`
	Login    string `json:"login,omitempty"`
	Pass     string `json:"pass,omitempty"`
}

type WebConfig struct {
	UserData  string `json:"user_data"`
	SearchMap string `json:"search_map"`
}

type TotpConfig struct {
	TokenDeviation string `json:"token_deviation"`
}

type CasConfig struct {
	AuthData  string `json:"auth_data"`
	FieldMap  string `json:"field_map"`
	OpenType  string `json:"open_type"`
	SearchMap string `json:"search_map"`
}

type OAuth2Config struct {
	GlobalData string `json:"global_data"`
	CodeData   string `json:"code_data"`
	UserData   string `json:"user_data"`
	SearchMap  string `json:"search_map"`
	LogoutOpen string `json:"logout_open"`
	LogoutData string `json:"logout_data"`
	OpenType   string `json:"open_type"`
}

type SsoState struct {
	State       string `json:"state"`
	CallBackUrl string `json:"callback_url"`
	IdpId       string `json:"idp_id"`
	IsTest      string `json:"is_test"`
	TestId      string `json:"test_id"`
	Time        string `json:"time"`
	Redirect    string `json:"redirect"`
}

type WxConfig struct {
	CorpId  string `json:"corp_id"`
	AgentId string `json:"agent_id"`
	Secret  string `json:"secret"`
}

type LdapConfig struct {
	ServerAddress         string `json:"server_address"`
	AdministratorAccount  string `json:"administrator_account"`
	AdministratorPassword string `json:"administrator_password"`
	SearchEntry           string `json:"search_entry"`
	UserFilter            string `json:"user_filter"`
	GroupFilter           string `json:"group_filter"`
}

type FeishuConfig struct {
	AppId     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}

type IdpConfig struct {
	AdministratorAccount  string `json:"administrator_account"`
	AdministratorPassword string `json:"administrator_password"`
	ExternalId            string `json:"external_id"`
	Filtration            string `json:"filtration"`
	Group                 string `json:"group"`
	SearchEntry           string `json:"search_entry"`
	ServerAddress         string `json:"server_address"`
	UserFilter            string `json:"user_filter"`
	GroupFilter           string `json:"group_filter"`
}

type DingtalkConfig struct {
	AppKey         string `json:"app_key"`
	AppSecret      string `json:"app_secret"`
	DingtalkCorpId string `json:"dd_corp_id"`
}

type SmsConfig struct {
	SmsType         string `json:"sms_type"`
	AccessKeyId     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	SdkAppId        string `json:"sdk_app_id"`
	SecretId        string `json:"secret_id"`
	SecretKey       string `json:"secret_key"`
	AppKey          string `json:"app_key"`
	AppSecret       string `json:"app_secret"`
	ChannelNo       string `json:"channel_no"`
	ExpirationTime  int32  `json:"expiration_time"`
	SignName        string `json:"sign_name"`
	TemplateCode    string `json:"template_code"`
}

type WxKvConfig struct {
	KV
	SyncId string `gorm:"sync_id"`
}

type FeiShuKvConfig struct {
	KV
	SyncId string `gorm:"sync_id"`
}

func (ldap LdapConfig) ToKVs() []KV {
	var result []KV
	if ldap.ServerAddress != "" {
		result = append(result, KV{
			Key:   AttrKeyAdServerAddress,
			Value: ldap.ServerAddress,
		})
	}
	if ldap.AdministratorAccount != "" {
		result = append(result, KV{
			Key:   AttrKeyAdAdministratorAccount,
			Value: ldap.AdministratorAccount,
		})
	}
	if ldap.AdministratorPassword != "" {
		result = append(result, KV{
			Key:   AttrKeyAdAdministratorPassword,
			Value: ldap.AdministratorPassword,
		})
	}
	if ldap.SearchEntry != "" {
		result = append(result, KV{
			Key:   AttrKeyAdSearchEntry,
			Value: ldap.SearchEntry,
		})
	}
	if ldap.UserFilter != "" {
		result = append(result, KV{
			Key:   AttrKeyAdUserFilter,
			Value: ldap.UserFilter,
		})
	}
	if ldap.GroupFilter != "" {
		result = append(result, KV{
			Key:   AttrKeyAdGroupFilter,
			Value: ldap.GroupFilter,
		})
	}
	return result
}

func (w WxConfig) ToKVs() []KV {
	var result []KV
	if w.CorpId != "" {
		result = append(result, KV{
			Key:   AttrKeyWxCorpId,
			Value: w.CorpId,
		})
	}
	if w.AgentId != "" {
		result = append(result, KV{
			Key:   AttrKeyWxAgentId,
			Value: w.AgentId,
		})
	}
	if w.Secret != "" {
		result = append(result, KV{
			Key:   AttrKeyWxAgentSecret,
			Value: w.Secret,
		})
	}
	return result
}

func (fs FeishuConfig) ToKVs() []KV {
	var result []KV
	if fs.AppId != "" {
		result = append(result, KV{
			Key:   AttrKeyFeiShuAppId,
			Value: fs.AppId,
		})
	}
	if fs.AppSecret != "" {
		result = append(result, KV{
			Key:   AttrKeyFeiShuSecret,
			Value: fs.AppSecret,
		})
	}
	return result
}

func (i IdpConfig) ToKVs() []KV {
	var result []KV
	if i.ServerAddress != "" {
		result = append(result, KV{
			Key:   AttrKeyAdServerAddress,
			Value: i.ServerAddress,
		})
	}
	if i.AdministratorPassword != "" {
		result = append(result, KV{
			Key:   AttrKeyAdAdministratorPassword,
			Value: i.AdministratorPassword,
		})
	}
	if i.AdministratorAccount != "" {
		result = append(result, KV{
			Key:   AttrKeyAdAdministratorAccount,
			Value: i.AdministratorAccount,
		})
	}
	if i.SearchEntry != "" {
		result = append(result, KV{
			Key:   AttrKeyAdSearchEntry,
			Value: i.SearchEntry,
		})
	}
	if i.ExternalId != "" {
		result = append(result, KV{
			Key:   AttrKeyAdExternalId,
			Value: i.ExternalId,
		})
	}
	if i.Filtration != "" {
		result = append(result, KV{
			Key:   AttrKeyAdFiltration,
			Value: i.Filtration,
		})
	}
	if i.Group != "" {
		result = append(result, KV{
			Key:   AttrKeyAdGroup,
			Value: i.Group,
		})
	}
	return result
}
func (c InfogoConfig) ToKVs() []KV {
	var result []KV
	if c.Endpoint != "" {
		result = append(result, KV{
			Key:   AttrKeyInfogoEndpoint,
			Value: c.Endpoint,
		})
	}
	if c.Login != "" {
		result = append(result, KV{
			Key:   AttrKeyInfogoUsername,
			Value: c.Login,
		})
	}
	if c.Pass != "" {
		result = append(result, KV{
			Key:   AttrKeyInfogoPass,
			Value: c.Pass,
		})
	}
	return result
}

func (totp TotpConfig) ToKVs() []KV {
	var result []KV
	if totp.TokenDeviation != "" {
		result = append(result, KV{
			Key:   AttrKeyTop,
			Value: totp.TokenDeviation,
		})
	}
	return result
}

func (web WebConfig) ToKVs() []KV {
	var result []KV
	if web.UserData != "" {
		result = append(result, KV{
			Key:   AttrKeyWebUserData,
			Value: web.UserData,
		})
	}
	if web.SearchMap != "" {
		result = append(result, KV{
			Key:   AttrKeyWebSearchMap,
			Value: web.SearchMap,
		})
	}
	return result
}

func (cas CasConfig) ToKVs() []KV {
	var result []KV
	if cas.AuthData != "" {
		result = append(result, KV{
			Key:   AttrKeyCasAuthData,
			Value: cas.AuthData,
		})
	}

	if cas.FieldMap != "" {
		result = append(result, KV{
			Key:   AttrKeyCasFieldMap,
			Value: cas.FieldMap,
		})
	}

	if cas.OpenType != "" {
		result = append(result, KV{
			Key:   AttrKeyCasOpenType,
			Value: cas.OpenType,
		})
	}
	if cas.SearchMap != "" {
		result = append(result, KV{
			Key:   AttrKeyCasSearchMap,
			Value: cas.SearchMap,
		})
	}
	return result
}

func (oa OAuth2Config) ToKVs() []KV {
	var result []KV
	if oa.GlobalData != "" {
		result = append(result, KV{
			Key:   AttrKeyOAuth2GlobalData,
			Value: oa.GlobalData,
		})
	}
	if oa.SearchMap != "" {
		result = append(result, KV{
			Key:   AttrKeyOAuth2SearchMap,
			Value: oa.SearchMap,
		})
	}
	if oa.CodeData != "" {
		result = append(result, KV{
			Key:   AttrKeyOAuth2CodeData,
			Value: oa.CodeData,
		})
	}
	if oa.UserData != "" {
		result = append(result, KV{
			Key:   AttrKeyOAuth2UserData,
			Value: oa.UserData,
		})
	}
	if oa.LogoutOpen != "" {
		result = append(result, KV{
			Key:   AttrKeyOAuth2LogoutOpen,
			Value: oa.LogoutOpen,
		})
	}
	if oa.OpenType != "" {
		result = append(result, KV{
			Key:   AttrKeyOAuth2OpenType,
			Value: oa.OpenType,
		})
	}
	if oa.LogoutData != "" {
		result = append(result, KV{
			Key:   AttrKeyOAuth2LogoutData,
			Value: oa.LogoutData,
		})
	}
	return result
}

func (fs DingtalkConfig) ToKVs() []KV {
	var result []KV
	if fs.AppKey != "" {
		result = append(result, KV{
			Key:   AttrKeyDingtalkAppKey,
			Value: fs.AppKey,
		})
	}
	if fs.AppSecret != "" {
		result = append(result, KV{
			Key:   AttrKeyDingtalkAppSecret,
			Value: fs.AppSecret,
		})
	}
	if fs.DingtalkCorpId != "" {
		result = append(result, KV{
			Key:   AttrKeyDingtalkCorpId,
			Value: fs.DingtalkCorpId,
		})
	}
	return result
}

func (email EmailConfig) ToKVs() []KV {
	var result []KV
	if email.ConfigData != "" {
		result = append(result, KV{
			Key:   AttrKeyEmailConfigData,
			Value: email.ConfigData,
		})
	}
	if email.SearchMap != "" {
		result = append(result, KV{
			Key:   AttrKeyEmailSearchMap,
			Value: email.SearchMap,
		})
	}
	return result
}

func (sms SmsConfig) ToKVs() []KV {
	var result []KV
	if sms.SmsType != "" {
		result = append(result, KV{
			Key:   AttrKeySmsType,
			Value: sms.SmsType,
		})
	}
	if sms.ExpirationTime != 0 {
		str := strconv.Itoa(int(sms.ExpirationTime))
		result = append(result, KV{
			Key:   AttrKeySmsExpirationTime,
			Value: str,
		})
	}
	if sms.SignName != "" {
		result = append(result, KV{
			Key:   AttrKeySmsSignName,
			Value: sms.SignName,
		})
	}
	if sms.TemplateCode != "" {
		result = append(result, KV{
			Key:   AttrKeySmsTemplateCode,
			Value: sms.TemplateCode,
		})
	}
	switch sms.SmsType {
	case "Aliyun":
		if sms.AccessKeyId != "" {
			result = append(result, KV{
				Key:   AttrKeySmsAccessKeyId,
				Value: sms.AccessKeyId,
			})
		}
		if sms.AccessKeySecret != "" {
			result = append(result, KV{
				Key:   AttrKeySmsAccessKeySecret,
				Value: sms.AccessKeySecret,
			})
		}
	case "Volc":
		if sms.AccessKeyId != "" {
			result = append(result, KV{
				Key:   AttrKeySmsAccessKeyId,
				Value: sms.AccessKeyId,
			})
		}
		if sms.AccessKeySecret != "" {
			result = append(result, KV{
				Key:   AttrKeySmsAccessKeySecret,
				Value: sms.AccessKeySecret,
			})
		}
		if sms.SdkAppId != "" {
			result = append(result, KV{
				Key:   AttrKeySmsSdkAppId,
				Value: sms.SdkAppId,
			})
		}
	case "TencentCloud":
		if sms.SdkAppId != "" {
			result = append(result, KV{
				Key:   AttrKeySmsSdkAppId,
				Value: sms.SdkAppId,
			})
		}
		if sms.SecretId != "" {
			result = append(result, KV{
				Key:   AttrKeySmsSecretId,
				Value: sms.SecretId,
			})
		}
		if sms.SecretKey != "" {
			result = append(result, KV{
				Key:   AttrKeySmsSecretKey,
				Value: sms.SecretKey,
			})
		}
	case "HuaweiCloud":
		if sms.AppKey != "" {
			result = append(result, KV{
				Key:   AttrKeySmsAppKey,
				Value: sms.AppKey,
			})
		}
		if sms.AppSecret != "" {
			result = append(result, KV{
				Key:   AttrKeySmsAppSecret,
				Value: sms.AppSecret,
			})
		}
		if sms.ChannelNo != "" {
			result = append(result, KV{
				Key:   AttrKeySmsChannelNo,
				Value: sms.ChannelNo,
			})
		}
	}
	return result
}

type IdpAttr struct {
	WxConfig
	FeishuConfig
	DingtalkConfig
	SmsConfig
	IdpConfig
	InfogoConfig
	OAuth2Config
	ConfigData     string `json:"configData"`
	AuthData       string `json:"auth_data"`
	TokenDeviation string `json:"token_deviation"`
	FieldMap       []KV
}

type WxIdpAttr struct {
	WxConfig
	FieldMap []KV
}

type FsIdpAttr struct {
	FeishuConfig
	FieldMap []KV
}

type DingtalkIdpAttr struct {
	DingtalkConfig
	FieldMap []KV
}

type SmsIdpAttr struct {
	SmsConfig
	FieldMap []KV
}

type EmailIdpAttr struct {
	EmailConfig
	FieldMap []KV
}

func SnakeToSmallCamelCase(str string) string {
	// 将字符串按下划线分割成多个单词
	words := strings.Split(str, "_")

	// 遍历单词，将首字母大写（除第一个单词外）
	for i := range words {
		if i == 0 {
			continue
		}
		words[i] = strings.Title(words[i])
	}

	// 将单词拼接起来，并将首字母小写
	return strings.Join(words, "")
}

type IDPWithAttrs []IDPWithAttr

func (a IDPWithAttrs) ToIDPInfo(removeSens bool) []IDPInfo {
	idpToAttr := make(map[string][]KV)
	for _, e := range a {
		if removeSens && a.SensitiveRemove(e) {
			continue
		}
		idpToAttr[e.ID] = append(idpToAttr[e.ID], KV{
			Key:   e.Key,
			Value: e.Value,
		})
	}
	var result []IDPInfo
	recorded := make(map[string]struct{})
	for _, e := range a {
		if _, ok := recorded[e.ID]; ok {
			continue
		}

		attrsMap := make(map[string]string)
		for _, attr := range idpToAttr[e.ID] {
			attrsMap[SnakeToSmallCamelCase(attr.Key)] = attr.Value
		}
		result = append(result, IDPInfo{
			IDPBasic: IDPBasic{
				ID:           e.ID,
				Name:         e.Name,
				Type:         e.Type,
				CorpID:       e.CorpID,
				Avatar:       e.Avatar,
				TemplateType: e.TemplateType,
			},
			Attrs: attrsMap,
		})
		recorded[e.ID] = struct{}{}
	}
	return result
}

func (a IDPWithAttrs) SensitiveRemove(attr IDPWithAttr) bool {
	Blacklist := []string{
		AttrKeyWxAgentSecret,
		AttrKeyFeiShuSecret,
		AttrKeyDingtalkAppSecret,
		AttrKeyInfogoPass,
		AttrKeySmsAccessKeySecret,
		AttrKeySmsAppSecret,
		AttrKeySmsSecretKey,
		AttrKeySmsSecretId,
		AttrKeyOAuth2GlobalData,
		AttrKeyOAuth2CodeData,
		AttrKeyOAuth2UserData,
		AttrKeyOAuth2LogoutData,
		AttrKeyOAuth2FieldMap,
		AttrKeyPailaFieldMap,
		AttrKeyCasAuthData,
		AttrKeyCasFieldMap,
		AttrKeyWebUserData,
	}
	// attr.Key 是否在 Blacklist 中
	for _, v := range Blacklist {
		if v == attr.Key {
			return true
		}
	}
	return false
}

func KVsIdpAttrCas(kvs []KV) CasConfig {
	var result CasConfig
	for _, kv := range kvs {
		if kv.Key == AttrKeyCasAuthData {
			result.AuthData = kv.Value
		}
		if kv.Key == AttrKeyCasOpenType {
			result.OpenType = kv.Value
		}
		if kv.Key == AttrKeyCasFieldMap {
			result.FieldMap = kv.Value
		}
		if kv.Key == AttrKeyCasSearchMap {
			result.SearchMap = kv.Value
		}
	}
	return result
}

func KVsToIdpAttr(kvs []KV) IdpAttr {
	var result IdpAttr
	for _, kv := range kvs {
		if kv.Key == AttrKeyWxCorpId {
			result.CorpId = kv.Value
		}
		if kv.Key == AttrKeyWxAgentId {
			result.AgentId = kv.Value
		}
		if kv.Key == AttrKeyWxAgentSecret {
			result.Secret = kv.Value
		}
		if kv.Key == AttrKeyFeiShuAppId {
			result.AppId = kv.Value
		}
		if kv.Key == AttrKeyFeiShuSecret {
			result.FeishuConfig.AppSecret = kv.Value
		}
		if kv.Key == AttrKeyDingtalkCorpId {
			result.DingtalkConfig.DingtalkCorpId = kv.Value
		}
		if kv.Key == AttrKeyDingtalkAppKey {
			result.DingtalkConfig.AppKey = kv.Value
		}
		if kv.Key == AttrKeyDingtalkAppSecret {
			result.DingtalkConfig.AppSecret = kv.Value
		}
		if kv.Key == AttrKeySmsType {
			result.SmsType = kv.Value
		}
		if kv.Key == AttrKeySmsAccessKeyId {
			result.AccessKeyId = kv.Value
		}
		if kv.Key == AttrKeySmsSdkAppId {
			result.SdkAppId = kv.Value
		}
		if kv.Key == AttrKeySmsAccessKeySecret {
			result.AccessKeySecret = kv.Value
		}
		if kv.Key == AttrKeySmsTemplateCode {
			result.TemplateCode = kv.Value
		}
		if kv.Key == AttrKeySmsSignName {
			result.SignName = kv.Value
		}
		if kv.Key == AttrKeySmsExpirationTime {
			i, err := strconv.Atoi(kv.Value)
			if err != nil {
				i = 0
			}
			result.ExpirationTime = int32(i)
		}
		if kv.Key == AttrKeyAdServerAddress {
			result.ServerAddress = kv.Value
		}
		if kv.Key == AttrKeyAdAdministratorAccount {
			result.AdministratorAccount = kv.Value
		}
		if kv.Key == AttrKeyAdAdministratorPassword {
			result.AdministratorPassword = kv.Value
		}
		if kv.Key == AttrKeyAdSearchEntry {
			result.SearchEntry = kv.Value
		}
		if kv.Key == AttrKeyAdUserFilter {
			result.UserFilter = kv.Value
		}
		if kv.Key == AttrKeyAdGroupFilter {
			result.GroupFilter = kv.Value
		}
		if kv.Key == AttrKeyAdExternalId {
			result.ExternalId = kv.Value
		}
		if kv.Key == AttrKeyAdFiltration {
			result.Filtration = kv.Value
		}
		if kv.Key == AttrKeyAdGroup {
			result.Group = kv.Value
		}
		if kv.Key == AttrKeyWxFieldMap || kv.Key == AttrKeyFeiShuFieldMap || kv.Key == AttrKeyDingtalkFieldMap || kv.Key == AttrKeyAdFieldMap {
			var fieldMap []KV
			_ = json.Unmarshal([]byte(kv.Value), &fieldMap)
			result.FieldMap = fieldMap
		}
		if kv.Key == AttrKeyPailaFieldMap {
			var fieldMap []KV
			_ = json.Unmarshal([]byte(kv.Value), &fieldMap)
			result.FieldMap = fieldMap
		}
		if kv.Key == AttrKeyInfogoEndpoint {
			result.Endpoint = kv.Value
		}
		if kv.Key == AttrKeyInfogoUsername {
			result.Login = kv.Value
		}
		if kv.Key == AttrKeyInfogoPass {
			result.Pass = kv.Value
		}
		if kv.Key == AttrKeyOAuth2GlobalData {
			result.GlobalData = kv.Value
		}
		if kv.Key == AttrKeyOAuth2SearchMap || kv.Key == AttrKeyWebSearchMap || kv.Key == AttrKeyCasSearchMap || kv.Key == AttrKeyEmailSearchMap {
			result.SearchMap = kv.Value
		}
		if kv.Key == AttrKeyOAuth2CodeData {
			result.CodeData = kv.Value
		}
		if kv.Key == AttrKeyOAuth2UserData || kv.Key == AttrKeyWebUserData {
			result.UserData = kv.Value
		}
		if kv.Key == AttrKeyOAuth2LogoutOpen {
			result.LogoutOpen = kv.Value
		}
		if kv.Key == AttrKeyOAuth2LogoutData {
			result.LogoutData = kv.Value
		}
		if kv.Key == AttrKeyOAuth2OpenType {
			result.OpenType = kv.Value
		}
		if kv.Key == AttrKeyCasAuthData {
			result.AuthData = kv.Value
		}
		if kv.Key == AttrKeyCasFieldMap {
			var fieldMap []KV
			_ = json.Unmarshal([]byte(kv.Value), &fieldMap)
			result.FieldMap = fieldMap
		}
		if kv.Key == AttrKeyWebUserData {
			result.UserData = kv.Value
		}
		if kv.Key == AttrKeyEmailConfigData {
			result.ConfigData = kv.Value
		}
		if kv.Key == AttrKeyTop {
			result.TokenDeviation = kv.Value
		}
	}
	return result
}

func KVsToWxIdpAttr(kvs []KV) WxIdpAttr {
	var result WxIdpAttr
	for _, kv := range kvs {
		if kv.Key == AttrKeyWxCorpId {
			result.CorpId = kv.Value
		}
		if kv.Key == AttrKeyWxAgentId {
			result.AgentId = kv.Value
		}
		if kv.Key == AttrKeyWxAgentSecret {
			result.Secret = kv.Value
		}
		if kv.Key == AttrKeyWxFieldMap {
			var fieldMap []KV
			_ = json.Unmarshal([]byte(kv.Value), &fieldMap)
			result.FieldMap = fieldMap
		}
		if kv.Key == AttrKeyFeiShuFieldMap {
			var fieldMap []KV
			_ = json.Unmarshal([]byte(kv.Value), &fieldMap)
			result.FieldMap = fieldMap
		}
	}
	return result
}

func KVsToFsIdpAttr(kvs []KV) FsIdpAttr {
	var result FsIdpAttr
	for _, kv := range kvs {
		if kv.Key == AttrKeyFeiShuAppId {
			result.AppId = kv.Value
		}
		if kv.Key == AttrKeyFeiShuSecret {
			result.AppSecret = kv.Value
		}
		if kv.Key == AttrKeyFeiShuFieldMap {
			var fieldMap []KV
			_ = json.Unmarshal([]byte(kv.Value), &fieldMap)
			result.FieldMap = fieldMap
		}
	}
	return result
}

func KVsToDingtalkIdpAttr(kvs []KV) DingtalkIdpAttr {
	var result DingtalkIdpAttr
	for _, kv := range kvs {
		if kv.Key == AttrKeyDingtalkAppKey {
			result.AppKey = kv.Value
		}
		if kv.Key == AttrKeyDingtalkAppSecret {
			result.AppSecret = kv.Value
		}
		if kv.Key == AttrKeyDingtalkCorpId {
			result.DingtalkConfig.DingtalkCorpId = kv.Value
		}
		if kv.Key == AttrKeyDingtalkFieldMap {
			var fieldMap []KV
			_ = json.Unmarshal([]byte(kv.Value), &fieldMap)
			result.FieldMap = fieldMap
		}
	}
	return result
}

func KVsToSmsIdpAttr(kvs []KV) SmsIdpAttr {
	var result SmsIdpAttr
	for _, kv := range kvs {
		if kv.Key == AttrKeySmsType {
			result.SmsType = kv.Value
		}
		if kv.Key == AttrKeySmsAccessKeyId {
			result.AccessKeyId = kv.Value
		}
		if kv.Key == AttrKeySmsAccessKeySecret {
			result.AccessKeySecret = kv.Value
		}
		if kv.Key == AttrKeySmsSdkAppId {
			result.SdkAppId = kv.Value
		}
		if kv.Key == AttrKeySmsTemplateCode {
			result.TemplateCode = kv.Value
		}
		if kv.Key == AttrKeySmsSignName {
			result.SignName = kv.Value
		}
		if kv.Key == AttrKeySmsExpirationTime {
			i, err := strconv.Atoi(kv.Value)
			if err != nil {
				i = 0
			}
			result.ExpirationTime = int32(i)
		}
	}
	return result
}

type AuthConfig struct {
	AppId                 string
	CorpId                string
	DdCorpId              string
	AgentId               string
	AppSecret             string
	Secret                string
	AuthType              string
	SmsType               string
	AccessKeyId           string
	AccessKeySecret       string
	SdkAppId              string
	SecretId              string
	SecretKey             string
	AppKey                string
	ChannelNo             string
	ExpirationTime        int32
	SignName              string
	TemplateCode          string
	ServerAddress         string
	AdministratorAccount  string
	AdministratorPassword string
	ExternalId            string
	Filtration            string
	Group                 string
	SearchEntry           string
	Endpoint              string
	Username              string
	Password              string
	GlobalData            string
	CodeData              string
	UserData              string
	LogoutOpen            string
	LogoutData            string
	OpenType              string
	FieldMap              string
	AuthData              string
	ConfigData            string
	SearchMap             string
	TokenDeviation        string
}

type CreateIDPParam struct {
	CorpId          string
	Name            string
	Type            string
	TemplateType    string
	Description     string
	FieldMap        []KV
	AuthConfig      AuthConfig
	Enable          bool
	BindRootGroupId []string
}

type UpdateIDPParam struct {
	CorpId       string
	Id           string
	Name         string
	Description  string
	TemplateType string
	FieldMap     []KV
	AuthConfig   AuthConfig
	Enable       *bool
}

type CreateIDPDaoParam struct {
	CorpId       string
	Id           string
	Name         string
	SourceId     string
	Type         string
	TemplateType string
	Enable       bool
	Description  string
	Attr         []KV
}

type UpdateIDPDaoParam struct {
	Id           string
	Name         string
	Enable       bool
	Description  string
	TemplateType string
	AuthConfig   AuthConfig
	Attr         []KV
}

type GetRootGroupListResp struct {
	MainIdpList   []IDPBasic
	AssistIdpList []IDPBasic
}

type ExternalDepartment struct {
	LocalRootGroupID string `json:"local_root_group_id"`
	ID               string `json:"id"`
	Name             string `json:"name"`
	NameEn           string `json:"name_en"`
	Parentid         string `json:"parentid"`
	Path             string `json:"path"`
	Order            int64  `json:"order"`
	LocalGroupID     string
	Type             string
	UniqKey          string `json:"-"`
}

type WxDepartment struct {
	WxCorpID         string `json:"wx_corp_id"`
	LocalRootGroupID string `json:"local_root_group_id"`
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	NameEn           string `json:"name_en"`
	Parentid         int64  `json:"parentid"`
	Path             string `json:"path"`
	Order            int64  `json:"order"`
	LocalGroupID     string
}

type FeishuDepartment struct {
	LocalRootGroupID string  `json:"local_root_group_id"`
	ID               *string `json:"department_id,omitempty"`
	Name             *string `json:"name,omitempty"`
	Parentid         *string `json:"parent_department_id,omitempty"`
	Path             string  `json:"path"`
	Order            int64   `json:"order"`
	LocalGroupID     string
}

type ExternalUser struct {
	LocalRootGroupID string       `json:"local_root_group_id"`
	MainDepartment   string       `json:"department_id,omitempty"`
	Userid           string       `json:"user_id,omitempty"`
	Name             string       `json:"name,omitempty"`
	NickName         string       `json:"nick_name,omitempty"`
	DisplayName      string       `json:"display_name,omitempty"`
	TrueName         string       `json:"true_name,omitempty"`
	Email            string       `json:"email,omitempty"`
	Mobile           string       `json:"mobile,omitempty"`
	LocalUserID      string       `json:"local_user_id"`
	Status           *UserStatus  `json:"status,omitempty"`
	Orders           []*UserOrder `json:"orders,omitempty"` // 用户排序信息。;;用于标记通讯录下组织架构的人员顺序，人员可能存在多个部门中，且有不同的排序。
	UniqKey          string       `json:"-"`
	Type             string       `json:"auth_type"`
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

type WxUser struct {
	WxCorpID         string `json:"wx_corp_id"`
	LocalRootGroupID string `json:"local_root_group_id"`
	MainDepartment   int64  `json:"main_department"`
	Userid           string `json:"userid"`
	Name             string `json:"name"`
	LocalUserID      string `json:"local_user_id"`
	Phone            string
	Email            string
}

type UserStatus struct {
	IsFrozen    *bool `json:"is_frozen,omitempty"`    // 是否暂停
	IsResigned  *bool `json:"is_resigned,omitempty"`  // 是否离职
	IsActivated *bool `json:"is_activated,omitempty"` // 是否激活
	IsExited    *bool `json:"is_exited,omitempty"`    // 是否主动退出，主动退出一段时间后用户会自动转为已离职
	IsUnjoin    *bool `json:"is_unjoin,omitempty"`    // 是否未加入，需要用户自主确认才能加入团队
}

type UserOrder struct {
	DepartmentId    *string `json:"department_id,omitempty"`    // 排序信息对应的部门ID， ID值与查询参数中的department_id_type 对应。;;表示用户所在的、且需要排序的部门。;;不同 ID 的说明参见及获取方式参见 [部门ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview)
	UserOrder       *int    `json:"user_order,omitempty"`       // 用户在其直属部门内的排序，数值越大，排序越靠前
	DepartmentOrder *int    `json:"department_order,omitempty"` // 用户所属的多个部门间的排序，数值越大，排序越靠前
	IsPrimaryDept   *bool   `json:"is_primary_dept,omitempty"`  // 是否为用户主部门
}

type FsUser struct {
	LocalRootGroupID string       `json:"local_root_group_id"`
	MainDepartment   string       `json:"department_id,omitempty"`
	UserId           string       `json:"user_id,omitempty"`
	Name             string       `json:"name,omitempty"`
	EnName           string       `json:"en_name,omitempty"`
	Nickname         string       `json:"nick_name,omitempty"`
	Email            string       `json:"email,omitempty"`
	Mobile           string       `json:"mobile,omitempty"`
	LocalUserID      string       `json:"local_user_id"`
	Status           *UserStatus  `json:"status,omitempty"`
	Orders           []*UserOrder `json:"orders,omitempty"` // 用户排序信息。;;用于标记通讯录下组织架构的人员顺序，人员可能存在多个部门中，且有不同的排序。
}

type WxDepartmentTree struct {
	ExternalDepartment
	Children []*WxDepartmentTree
}

type FeishuDepartmentTree struct {
	FeishuDepartment
	Children []*FeishuDepartmentTree
}

type IDPDetail struct {
	IDPBasic
	AuthConfig    AuthConfig
	CasConfig     CasConfig
	FieldMap      []KV
	BindGroupList []BindGroupInfo
}

// 返回认证服务器的逻辑类型
func GetIdpType(sourceType, templateType string) string {
	if templateType != "" {
		return templateType
	}
	return sourceType
}
