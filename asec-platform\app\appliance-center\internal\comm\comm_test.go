package comm

import "testing"

func TestFormatMacAddr(t *testing.T) {
	type args struct {
		mac string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{mac: "d8:bb:c1:a3:b4:a0"},
			want: "d8-bb-c1-a3-b4-a0",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FormatMacAddr(tt.args.mac); got != tt.want {
				t.<PERSON>("FormatMacAddr() = %v, want %v", got, tt.want)
			}
		})
	}
}
