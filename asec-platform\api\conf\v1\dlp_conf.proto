syntax = "proto3";

import public "google/protobuf/timestamp.proto";
package api.conf;
option go_package = "asdsec.com/asec/platform/api/conf/v1;v1";

// DdrSource 数据来源配置
// 将所有来源类型的数据返回在了一起,通过不同的类型取字段即可
message DdrSource{
  // 来源id
  string source_id = 1;
  // 来源名称
  string source_name = 2;
  // 来源类型枚举(web/vcs/software)
  string source_type = 3;
  // 来源优先级(扩展字段,当前默认0)
  uint32 source_priority = 4;

  // 来源web信息
  repeated SourceWeb source_web = 6;
  // 来源软件信息
  repeated SourceSoftware source_software = 7;

  // vcs地址  对应vcs来源类型
  string vcs_url = 8;

  // 状态 1-启用 2-禁用
  uint32 status = 9;
}

message SourceWeb{
  // web地址 对应web来源类型
  string url_addr = 5;
  // web端口 对应web来源类型
  string url_port = 6;
  // web路径 对应web来源类型
  string url_route = 7;
}

message SourceSoftware{
  // 软件名称  对应software来源类型
  string software_name = 8;
  // 进程名称(以逗号分割多个)  对应software来源类型
  string process_name = 9;
  // 包含目录
  repeated string include_file_path = 10;
  // 排除目录
  repeated string exclude_file_path = 11;
}

// SensitiveStrategy 敏感数据策略
message SensitiveStrategy{
  // 规则Id
  string rule_id = 1;
  // 规则名称
  string rule_name = 2;
  // 规则优先级(扩展字段,当前默认0)
  uint32 rule_priority = 3;
  // 规则等级(1,2,3,4)
  uint32 sensitive_level = 4;
  // 检查加密文件(1是;2否)
  uint32 check_file_encrypted = 5;
  // 检查文件隐藏(1是;2否)
  uint32 check_file_suffix = 6;
  // 最小文件大小
  int32 min_file_size = 7;
  // 最大文件大小
  int32 max_file_size = 8;
  // 最小文件大小 单位(kb/mb)
  string min_file_size_unit = 9;
  // 最大文件大小 单位(kb/mb)
  string max_file_size_unit = 10;
  // 文件类型(数组,存储文件类型的code) [0]标识所有文件
  repeated uint32 file_type_code = 11;
  // 规则匹配符(and;or)
  string rule_operator = 12;
  // 文件名称匹配符(and;or)
  string filename_operator = 13;
  // 文件名匹配规则列表
  repeated SensitiveElementRule filename_rule = 14;
  // 文件内容匹配符(and;or)
  string content_operator = 15;
  // 文件内容匹配规则列表
  repeated SensitiveElementRule content_rule = 16;
  // 数据规则类型id
  string category_id = 17;
  // 数据规则内容
  string category = 18;
  // 来源id
  repeated string source_id = 19;
  // 识别方式
  repeated uint32 identify_way = 20;
  // 是否开启 (1启用;2禁用)
  uint32 enable = 21;
}

// SensitiveElementRule 文件名/文件内容敏感规则
message SensitiveElementRule{
  // 计数count,operator为count时生效
  int32 count = 1;
  // 判断操作(满足部分count / 满足任一or / 满足所有 and)
  string operator = 2;
  // 敏感元素code
  repeated uint32 sensitive_element_codes = 3;
}

// DlpRule DLP告警规则
message DlpRule{
  // 规则ID
  string rule_id = 1;
  // 规则名称
  string rule_name = 2;
  // 启用行为分析(1启用;2禁用)
  uint32 enable_analysis = 3;
  // 外发通道(数组) [“0”]标识所有分组
  repeated string channel_types = 4;
  // 敏感数据等级 [0]标识所有等级
  repeated string sensitive_ids = 5;
  // 敏感数据类型(数组) [0]标识所有数据
  repeated int64 sensitive_level = 6;
  // 告警生效时间
  TimeSequence time = 7;
  // 告警类型(1代码/2数据)
  uint32 alert_type = 8;
  // git包含逻辑(1包含/2不包含)
  uint32 git_contain_option = 9;
  // git_path
  repeated string git_path = 10;
  // svn包含逻辑(1包含/2不包含)
  uint32 svn_contain_option = 11;
  // svn_path
  repeated string svn_path = 12;
  // 处置动作 1审批/2阻断/3放通
  uint32 dispose_action = 13;
  // 规则优先级(扩展字段,当前默认0)
  uint32 rule_priority = 14;
  // 是否开启 (1启用;2禁用)
  uint32 enable = 15;
  // 敏感数据分类["0"]标识所有数据
  repeated string sensitive_category = 16;
  // 引用的消息通知模板ID
  string notification_id = 17;
  bool enable_notification = 18;
}

// 时间序列
message TimeSequence{
  // 是否全选生效 (1全选;2不全选)
  uint32 all = 1;
  // 数组 选中为"1" 不选中为"0"
  repeated string rule = 2;
}