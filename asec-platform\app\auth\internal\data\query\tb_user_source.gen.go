// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserSource(db *gorm.DB, opts ...gen.DOOption) tbUserSource {
	_tbUserSource := tbUserSource{}

	_tbUserSource.tbUserSourceDo.UseDB(db, opts...)
	_tbUserSource.tbUserSourceDo.UseModel(&model.TbUserSource{})

	tableName := _tbUserSource.tbUserSourceDo.TableName()
	_tbUserSource.ALL = field.NewAsterisk(tableName)
	_tbUserSource.ID = field.NewString(tableName, "id")
	_tbUserSource.Name = field.NewString(tableName, "name")
	_tbUserSource.SourceType = field.NewString(tableName, "source_type")
	_tbUserSource.CorpID = field.NewString(tableName, "corp_id")
	_tbUserSource.CreatedAt = field.NewTime(tableName, "created_at")
	_tbUserSource.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbUserSource.TemplateType = field.NewString(tableName, "template_type")

	_tbUserSource.fillFieldMap()

	return _tbUserSource
}

type tbUserSource struct {
	tbUserSourceDo tbUserSourceDo

	ALL          field.Asterisk
	ID           field.String // 用户来源id
	Name         field.String // 用户来源名称，租户下唯一
	SourceType   field.String // 来源类型，租户下唯一
	CorpID       field.String // 租户id外键，维护租户-用户来源的1对多关系
	CreatedAt    field.Time
	UpdatedAt    field.Time
	TemplateType field.String

	fieldMap map[string]field.Expr
}

func (t tbUserSource) Table(newTableName string) *tbUserSource {
	t.tbUserSourceDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserSource) As(alias string) *tbUserSource {
	t.tbUserSourceDo.DO = *(t.tbUserSourceDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserSource) updateTableName(table string) *tbUserSource {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.SourceType = field.NewString(table, "source_type")
	t.CorpID = field.NewString(table, "corp_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.TemplateType = field.NewString(table, "template_type")

	t.fillFieldMap()

	return t
}

func (t *tbUserSource) WithContext(ctx context.Context) *tbUserSourceDo {
	return t.tbUserSourceDo.WithContext(ctx)
}

func (t tbUserSource) TableName() string { return t.tbUserSourceDo.TableName() }

func (t tbUserSource) Alias() string { return t.tbUserSourceDo.Alias() }

func (t tbUserSource) Columns(cols ...field.Expr) gen.Columns {
	return t.tbUserSourceDo.Columns(cols...)
}

func (t *tbUserSource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserSource) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 7)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["source_type"] = t.SourceType
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["template_type"] = t.TemplateType
}

func (t tbUserSource) clone(db *gorm.DB) tbUserSource {
	t.tbUserSourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserSource) replaceDB(db *gorm.DB) tbUserSource {
	t.tbUserSourceDo.ReplaceDB(db)
	return t
}

type tbUserSourceDo struct{ gen.DO }

func (t tbUserSourceDo) Debug() *tbUserSourceDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserSourceDo) WithContext(ctx context.Context) *tbUserSourceDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserSourceDo) ReadDB() *tbUserSourceDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserSourceDo) WriteDB() *tbUserSourceDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserSourceDo) Session(config *gorm.Session) *tbUserSourceDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserSourceDo) Clauses(conds ...clause.Expression) *tbUserSourceDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserSourceDo) Returning(value interface{}, columns ...string) *tbUserSourceDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserSourceDo) Not(conds ...gen.Condition) *tbUserSourceDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserSourceDo) Or(conds ...gen.Condition) *tbUserSourceDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserSourceDo) Select(conds ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserSourceDo) Where(conds ...gen.Condition) *tbUserSourceDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserSourceDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserSourceDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserSourceDo) Order(conds ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserSourceDo) Distinct(cols ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserSourceDo) Omit(cols ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserSourceDo) Join(table schema.Tabler, on ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserSourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserSourceDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserSourceDo) Group(cols ...field.Expr) *tbUserSourceDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserSourceDo) Having(conds ...gen.Condition) *tbUserSourceDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserSourceDo) Limit(limit int) *tbUserSourceDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserSourceDo) Offset(offset int) *tbUserSourceDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserSourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserSourceDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserSourceDo) Unscoped() *tbUserSourceDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserSourceDo) Create(values ...*model.TbUserSource) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserSourceDo) CreateInBatches(values []*model.TbUserSource, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserSourceDo) Save(values ...*model.TbUserSource) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserSourceDo) First() (*model.TbUserSource, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSource), nil
	}
}

func (t tbUserSourceDo) Take() (*model.TbUserSource, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSource), nil
	}
}

func (t tbUserSourceDo) Last() (*model.TbUserSource, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSource), nil
	}
}

func (t tbUserSourceDo) Find() ([]*model.TbUserSource, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserSource), err
}

func (t tbUserSourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserSource, err error) {
	buf := make([]*model.TbUserSource, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserSourceDo) FindInBatches(result *[]*model.TbUserSource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserSourceDo) Attrs(attrs ...field.AssignExpr) *tbUserSourceDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserSourceDo) Assign(attrs ...field.AssignExpr) *tbUserSourceDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserSourceDo) Joins(fields ...field.RelationField) *tbUserSourceDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserSourceDo) Preload(fields ...field.RelationField) *tbUserSourceDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserSourceDo) FirstOrInit() (*model.TbUserSource, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSource), nil
	}
}

func (t tbUserSourceDo) FirstOrCreate() (*model.TbUserSource, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserSource), nil
	}
}

func (t tbUserSourceDo) FindByPage(offset int, limit int) (result []*model.TbUserSource, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserSourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserSourceDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserSourceDo) Delete(models ...*model.TbUserSource) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserSourceDo) withDO(do gen.Dao) *tbUserSourceDo {
	t.DO = *do.(*gen.DO)
	return t
}
