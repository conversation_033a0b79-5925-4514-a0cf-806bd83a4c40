-- 回滚：删除应用表的证书ID字段

-- 删除外键约束（如果存在）
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'tb_application' 
        AND constraint_name = 'fk_tb_application_certificate_id'
    ) THEN
        ALTER TABLE public.tb_application 
        DROP CONSTRAINT fk_tb_application_certificate_id;
    END IF;
END $$;

-- 删除证书ID字段
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'tb_application' 
        AND column_name = 'certificate_id'
    ) THEN
        ALTER TABLE public.tb_application DROP COLUMN certificate_id;
    END IF;
END $$;
