import { useUserStore } from '@/pinia/modules/user'
import { useRouterStore } from '@/pinia/modules/router'
import getPageTitle from '@/utils/page'
import router from '@/router'
import agentApi from '@/api/agentApi'
import { refreshToken } from '@/api/token'
import { connectForToken } from '@/utils/websocket'

let asyncRouterFlag = 0

const whiteList = ['Login', 'Init', 'ClientLogin', 'Status', 'downloadWin', 'WxOAuthCallback', 'OAuth2Result', 'OAuth2Premises', 'Error']

const getRouter = async(userStore) => {
  logger.log('----getRouter---')
  const routerStore = useRouterStore()
  await routerStore.SetAsyncRouter()
  await userStore.GetUserInfo()
  //   start(userStore.LoginOut, userStore.setToken)
  const asyncRouters = routerStore.asyncRouters
  asyncRouters.forEach(asyncRouter => {
    router.addRoute(asyncRouter)
  })
}

async function handleKeepAlive(to) {
  if (to.matched.some(item => item.meta.keepAlive)) {
    if (to.matched && to.matched.length > 2) {
      for (let i = 1; i < to.matched.length; i++) {
        const element = to.matched[i - 1]
        if (element.name === 'layout') {
          to.matched.splice(i, 1)
          await handleKeepAlive(to)
        }
        // 如果没有按需加载完成则等待加载
        if (typeof element.components.default === 'function') {
          await element.components.default()
          await handleKeepAlive(to)
        }
      }
    }
  }
}

/**
 * 跳转到工作台的封装函数
 * @param {Object} userStore - 用户状态管理
 * @param {Object} to - 路由目标对象
 * @returns {boolean|null} true表示跳转成功，null表示需要清除token
 */
const redirectToWorkbench = async(userStore, to, isRoute) => {
  try {
    const routerStore = useRouterStore()
    await routerStore.SetAsyncRouter()
    await userStore.GetUserInfo()
    const asyncRouters = routerStore.asyncRouters
    asyncRouters.forEach(asyncRouter => {
      router.addRoute(asyncRouter)
    })

    if (userStore.userInfo) {
      const redirectPath = to.query.redirect || to.query.redirect_url
      if (redirectPath) {
        // 判断是否为外部URL（跨域跳转）
        if (redirectPath.startsWith('http')) {
          if (isRoute) {
            // 在路由守卫中，延迟执行外部跳转
            setTimeout(() => {
              window.location.href = redirectPath
            }, 100)
            return false // 阻止当前路由继续
          } else {
            window.location.href = redirectPath
          }
        } else {
          // 内部路径跳转，统一返回路由对象
          return { path: redirectPath, replace: true }
        }
      } else {
        if (isRoute) {
          return { name: 'dashboard', replace: true }
        }
        // 在路由守卫中，也返回路由对象而不是直接跳转
        return { name: 'dashboard', replace: true }
      }
      return true // 表示跳转成功
    } else {
      await userStore.ClearStorage()
      return null // 表示需要清除token
    }
  } catch (error) {
    logger.log('跳转工作台失败:', error)
    await userStore.ClearStorage()
    return null // 表示需要清除token
  }
}

/**
 * 处理客户端登录状态检查和重定向
 * @param {Object} userStore - 用户状态管理对象
 * @param {Object} to - 目标路由对象
 * @returns {boolean|Object} - 返回 true 表示已登录继续访问，返回路由对象表示需要重定向
 */
const handleClientLoginCheck = (userStore, to, isRoute) => {
  if (userStore.token) {
    logger.log('客户端已登录直接返回:', to.path)
    return true
  }

  logger.log('客户端未登录重定向到登录页:', to.path)
  const query = agentApi.getClientParams()
  query.redirect = to.href
  if (isRoute) {
    return {
      name: 'ClientNewLogin',
      query: query
    }
  }
  router.push({
    name: 'ClientNewLogin',
    query: query
  })
}

const scoketToken = async(userStore) => {
  logger.log('socket连接开始')
  try {
    const tokenInfo = await connectForToken()
    if (tokenInfo) {
      await userStore.setToken(tokenInfo)
      logger.log('Token设置成功')
    } else {
      logger.log('未获取到Token')
    }
  } catch (error) {
    logger.log('获取Token失败:', error)
  }
}

// 客户端获取token
export const clientToken = async(userStore) => {
  logger.log('clientToken 开始')
  try {
    // 获取登录状态
    const loginData = await agentApi.getChannelStatus()
    logger.log('客户端查询登录结果:', loginData)
    if (!loginData || !loginData.Token) {
      logger.log('客户端未获取到token')
      await userStore.setToken('')
      return
    }
    const tokenInfo = JSON.parse(loginData.Token)
    if (!tokenInfo.token) {
      logger.log('客户端token为空')
      await userStore.setToken('')
      return
    }
    userStore.setTunState(loginData.TunState)
    const info = {
      accessToken: tokenInfo.token,
      refreshToken: tokenInfo.refreshToken,
      tokenType: 'Bearer'
    }
    await userStore.setToken(info)

    // 验证token是否有效
    if (!userStore.isTokenValid()) {
      logger.log('客户端token无效，尝试刷新token')
      try {
        const reftoken = await refreshToken()
        logger.log('刷新token结果:', reftoken)

        if (reftoken && reftoken.status === 200 && reftoken.data && reftoken.data.code !== -1) {
          await userStore.setToken(reftoken.data)
          const clineData = {
            token: reftoken.data.accessToken,
            refreshToken: reftoken.data.refreshToken,
            realm: 'default',
          }
          await agentApi.setLoginStatus({
            Token: JSON.stringify(clineData),
            IsActivationCodeLogin: false
          })
          logger.log('刷新后的token验证通过')
        } else {
          logger.log('刷新token失败，清空token')
          await userStore.setToken('')
          return
        }
      } catch (refreshError) {
        logger.log('刷新token过程中发生错误:', refreshError)
        await userStore.setToken('')
        return
      }
    } else {
      logger.log('客户端token验证通过')
    }

    return loginData
  } catch (error) {
    await userStore.setToken('')
    logger.log('clientToken 执行失败:', error)
    throw error
  }
}

router.beforeEach(async(to, from) => {
  // 如果是客户端则直接返回
  if (agentApi.isClient()) {
    return routerClientBefore(to)
  }
  const userStore = useUserStore()
  to.meta.matched = [...to.matched]
  await handleKeepAlive(to)
  let token = userStore.token
  const refresh_times = window.localStorage.getItem('refresh_times') || 0

  // 特殊处理：如果用户访问登录页面但已经有有效token，直接重定向
  if (to.name === 'Login') {
    if (token) {
      logger.log('Login页面获取到token，跳转到工作台')
      const redirectResult = await redirectToWorkbench(userStore, to, true)
      if (redirectResult === null) {
        // 需要清除token
        token = null
      } else if (redirectResult === true) {
        // 跳转成功，阻止路由继续处理
        return false
      } else if (redirectResult !== undefined) {
        // 调用有明确的跳转结果
        return redirectResult
      }
    }
    logger.log('Login页面异步调用scoketToken：', refresh_times)
    if (Number(refresh_times) < 5) {
      // 使用 await 等待异步操作完成
      try {
        await scoketToken(userStore, false)
        token = userStore.token

        // 如果获取到token，直接跳转到工作台
        if (token && token !== '""') {
          logger.log('Login页面从客户端获取到token，跳转到工作台')
          const redirectResult = await redirectToWorkbench(userStore, to, true)
          if (redirectResult === null) {
            // 需要清除token，继续显示登录页
            token = null
          } else if (redirectResult !== undefined) {
            // 有明确的跳转结果，返回跳转指令
            return redirectResult
          }
        }
      } catch (error) {
        logger.log('异步获取token失败:', error)
      }
    }
  }

  // 在白名单中的判断情况
  document.title = getPageTitle(to.meta.title, to)

  if (to.name === 'WxOAuthCallback' || to.name === 'verify') {
    document.title = ''
  } else {
    document.title = getPageTitle(to.meta.title, to)
  }
  // logger.log('to.name')
  logger.log('路由参数：', { whiteList: whiteList, to: to, from: from })
  // logger.log(to.name)
  if ((!token || token === '""') && Number(refresh_times) < 5 && to.name !== 'Login') {
    await scoketToken(userStore)
    token = userStore.token
  }
  //    if (whiteList.includes(to.name)) {
  //     if (token && to.name != "downloadWin") {
  if (whiteList.includes(to.name)) {
    if (token && !['downloadWin', 'Login', 'WxOAuthCallback', 'OAuth2Callback', 'Error'].includes(to.name)) {
      if (!asyncRouterFlag && whiteList.indexOf(from.name) < 0) {
        asyncRouterFlag++
        await getRouter(userStore)
        logger.log('getRouter')
      }
      // token 可以解析但是却是不存在的用户 id 或角色 id 会导致无限调用
      if (userStore.userInfo) {
        logger.log('dashboard')
        return { name: 'dashboard' }
      } else {
        // 强制退出账号
        await userStore.ClearStorage()
        logger.log('强制退出账号')
        return {
          name: 'Login',
          query: {
            redirect: window.location.href, // 保存完整的URL（包含query参数和fragment）
          },
        }
      }
    } else {
      logger.log('直接返回')
      return true
    }
  } else {
    // 不在白名单中并且已经登录的时候
    // logger.log('permission token')
    logger.log('不在白名单中:', token)
    if (token) {
      // logger.log('permission token')
      // logger.log(token)
      // 添加flag防止多次获取动态路由和栈溢出
      // logger.log(asyncRouterFlag)
      // logger.log(whiteList)
      // logger.log(from.name)
      if (!asyncRouterFlag && whiteList.indexOf(from.name) < 0) {
        asyncRouterFlag++
        await getRouter(userStore)
        // logger.log('userStore.token')
        logger.log('初始化动态路由:', userStore.token)
        // logger.log('to')
        // logger.log(to)
        if (userStore.token) {
          logger.log('返回to')
          return { ...to, replace: false }
        } else {
          logger.log('返回login')
          return {
            name: 'Login',
            query: { redirect: window.location.href }, // 保存完整的URL
          }
        }
      } else {
        if (to.matched.length) {
          logger.log('返回refresh')
          return true
        } else {
          // logger.log(404)
          console.log('404:', to.matched)
          return { name: 'Error', query: { code: '404', message: '页面未找到' }}
        }
      }
    } else {
      logger.log('不在白名单中并且未登录的时候')
      return {
        name: 'Login',
        query: {
          redirect: document.location.hash,
        },
      }
    }
  }
})

router.afterEach(() => {
  // 路由加载完成
  document.title = getPageTitle()
})

router.onError(() => {
  // 路由发生错误后的处理
  console.error('路由发生错误')
})

// 客户端路由处理
const routerClientBefore = async(to) => {
  const whiteListPath = ['/client', '/client/login', '/client/setting', '/status',
    '/login', '/oauth2_premises', '/error']
  // 白名单路径，不校验权限
  const isWhiteListPath = whiteListPath.includes(to.path)
  if (isWhiteListPath) {
    logger.log('客户端直接返回:', to.path)
    return true
  }
  // 判断不是/client/开头的路径，直接调用agentApi.openAsecPage打开
  if (!to.path.startsWith('/client') && window.self === window.top) {
    logger.log('非客户端路径，使用浏览器打开:', window.location.href)
    agentApi.openAsecPage(window.location.href)
    return false // 阻止路由继续
  }

  // 客户端获取token 判断登录状态
  const userStore = useUserStore()
  await clientToken(userStore)
  return handleClientLoginCheck(userStore, to, true)
}

if (agentApi.isClient()) {
  // 监听客户端主动调用的认证事件
  if (typeof window !== 'undefined') {
    window.addEventListener('clientAuthTokenReceived', async(event) => {
      logger.log('收到客户端认证事件:', event.detail)

      // 检查是否为客户端环境
      if (!agentApi.isClient()) {
        logger.log('非客户端环境，忽略认证事件')
        return
      }

      try {
        const userStore = useUserStore()
        logger.log('客户端未登录，开始执行 clientToken 获取登录状态')

        // 调用 clientToken 方法获取登录状态
        await clientToken(userStore)

        // 如果获取到 token，跳转到主页
        if (userStore.token) {
          logger.log('客户端登录状态同步成功，跳转到主页')
          const query = agentApi.getClientParams()
          router.push({
            name: 'ClientMain',
            query: query
          })
        } else {
          logger.log('客户端未获取到有效登录状态')
        }
      } catch (error) {
        logger.log('处理客户端认证事件失败:', error)
      }
    })

    logger.log('已注册客户端认证事件监听器')
  }
}
