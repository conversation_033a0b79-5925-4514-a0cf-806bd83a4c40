package dto

import (
	model "asdsec.com/asec/platform/pkg/model/strategy_model"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"time"
)

type StrategyListDto struct {
	Id                  string         `gorm:"column:id;NOT NULL" json:"id,omitempty"`
	StrategyName        string         `gorm:"column:strategy_name;comment:'策略名称'" json:"strategy_name,omitempty"`
	UserIds             pq.StringArray `gorm:"column:user_ids;comment:'用户id';type:string" json:"user_ids,omitempty"`
	UserGroupIds        pq.StringArray `gorm:"column:user_group_ids;comment:'用户分组id';type:string" json:"user_group_ids,omitempty"`
	RoleIds             pq.StringArray `gorm:"column:role_ids;comment:'角色id';type:string" json:"user_role_ids,omitempty"`
	EnableAllUser       int            `gorm:"column:enable_all_user;comment:'全部用户'" json:"enable_all_user,omitempty"`
	EnableAllApp        int            `gorm:"column:enable_all_app;comment:'全部应用'" json:"enable_all_app,omitempty"`
	Action              string         `gorm:"column:action;comment:'执行动作类型'" json:"action,omitempty"`
	ActionConfig        pgtype.JSONB   `gorm:"column:action_config;comment:'动作所属配置文件';default:'{}'::jsonb" json:"action_config,omitempty"`
	StrategyGroupId     string         `gorm:"column:strategy_group_id;comment:'策略分组id'" json:"strategy_group_id,omitempty"`
	StrategyStatus      int            `gorm:"column:strategy_status;comment:'是否启用'" json:"enable,omitempty"`
	Priority            int            `gorm:"column:priority;comment:'优先级'" json:"priority,omitempty"`
	DynamicRule         pgtype.JSONB   `gorm:"column:dynamic_rule;comment:'动态策略规则';default:'{}'::jsonb" json:"dynamic_rule,omitempty"`
	TimeId              string         `gorm:"column:time_id;comment:'时间选择模板id'" json:"time_id,omitempty"`
	CreatedAt           time.Time      `gorm:"column:created_at" json:"created_at,omitempty"`
	UpdatedAt           time.Time      `gorm:"column:updated_at" json:"updated_at,omitempty"`
	AppIds              pq.StringArray `gorm:"column:app_ids;comment:'应用id';type:bigint" json:"app_ids,omitempty"`
	AppGroupIds         pq.StringArray `gorm:"column:app_group_ids;comment:'应用标签id';type:bigint" json:"app_group_ids,omitempty"`
	StrategyDetail      string         `gorm:"column:strategy_detail;comment:'策略描述'" json:"strategy_desc,omitempty"`
	ExcludeUserIds      pq.StringArray `gorm:"column:exclude_user_ids;comment:'排除用户';type:string" json:"exclude_user_ids,omitempty"`
	ExcludeUserGroupIds pq.StringArray `gorm:"column:exclude_user_group_ids;type:string" json:"exclude_user_group_ids,omitempty"`
	ExcludeUserRoleIds  pq.StringArray `gorm:"column:exclude_user_role_ids;type:string" json:"exclude_user_role_ids,omitempty"`
	StartTime           string         `gorm:"column:start_time;comment:'开始时间'" json:"start_time,omitempty"`
	EndTime             string         `gorm:"column:end_time;comment:'结束时间'" json:"end_time,omitempty"`
	MatchCount          int            `gorm:"column:match_count" json:"match_count"`
	MaxCountData        *time.Time     `gorm:"column:max_count_data" json:"max_count_data"`
	GroupName           string         `gorm:"column:group_name" json:"group_name,omitempty"`

	AppNames              pq.StringArray `gorm:"column:app_names;type:string" json:"app_names"`
	AppTagNames           pq.StringArray `gorm:"column:app_tag_names;type:string" json:"app_tag_names"`
	ExcludeUserNames      pq.StringArray `gorm:"column:exclude_user_names;type:string" json:"exclude_user_names"`
	ExcludeUserGroupNames pq.StringArray `gorm:"column:exclude_user_group_names;type:string" json:"exclude_user_group_names"`
	ExcludeUserRoleNames  pq.StringArray `gorm:"column:exclude_user_role_names;type:string" json:"exclude_user_role_names"`
	UserNames             pq.StringArray `gorm:"column:user_names;type:string" json:"user_names"`
	UserGroupNames        pq.StringArray `gorm:"column:user_group_names;type:string" json:"user_group_names"`
	UserRoleNames         pq.StringArray `gorm:"column:user_role_names;type:string" json:"user_role_names"`

	App pgtype.JSONB `gorm:"column:app;comment:'动态策略规则';default:'{}'::jsonb" json:"app,omitempty"`

	model.FactorTime
	UserRiskRule pgtype.JSONB `gorm:"column:user_risk_rule;comment:'用户评分约束';default:'{}'::jsonb" json:"-"`
}

type UserRiskRule struct {
	Type      string `json:"type"`
	Operator  string `json:"operator"`
	Score     int    `json:"score"`
	RiskLevel int    `json:"risk_level"`
}

type DelStrategyDto struct {
	Ids []int64 `json:"ids" binding:"required"`
}

type EnableStrategyDto struct {
	DelStrategyDto
	// 1启用/2停止
	Enable int `gorm:"column:enable;comment:'是否启用'" json:"enable"  binding:"required,oneof=1 2"`
}

type StrategyPriority struct {
	Id       uint64  `gorm:"id"`
	Priority float32 `gorm:"priority"`
}

type ReGroupStrategy struct {
	Id        uint64 `gorm:"id"`
	Priority  int    `gorm:"priority"`
	RowNumber int    `gorm:"row_number"`
}

type StrategyChange struct {
	Id          uint64       `gorm:"column:id;NOT NULL" json:"id"`
	DynamicRule pgtype.JSONB `gorm:"column:dynamic_rule;comment:'动态策略规则';default:'{}'::jsonb" json:"dynamic_rule"`
}

type MatchCountDto struct {
	Id           string     `gorm:"id"`
	Count        int        `gorm:"count"`
	MaxCountData *time.Time `gorm:"column:max_count_data" json:"max_count_data"`
}
