package common

import (
	"fmt"
	"gorm.io/gorm"
	"time"
)

func GetFeildCountByDB(db *gorm.DB, table interface{}, feildName string, value interface{}, startT, endT time.Time) error {
	select_str := fmt.Sprintf("%s,count(%s) as count", feildName, feildName)
	err := db.Model(&table).Select(select_str).Group(feildName).
		Where("occur_time >= ? AND occur_time <= ?", startT.Unix(), endT.Unix()).Find(value).Error
	return err
}
