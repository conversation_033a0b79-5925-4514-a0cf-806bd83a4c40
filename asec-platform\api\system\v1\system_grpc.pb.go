// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: system/v1/system.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AgentGetES256Key_QueryES256Key_FullMethodName = "/api.system.AgentGetES256Key/QueryES256Key"
)

// AgentGetES256KeyClient is the client API for AgentGetES256Key service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgentGetES256KeyClient interface {
	QueryES256Key(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ES256KeyMsg, error)
}

type agentGetES256KeyClient struct {
	cc grpc.ClientConnInterface
}

func NewAgentGetES256KeyClient(cc grpc.ClientConnInterface) AgentGetES256KeyClient {
	return &agentGetES256KeyClient{cc}
}

func (c *agentGetES256KeyClient) QueryES256Key(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ES256KeyMsg, error) {
	out := new(ES256KeyMsg)
	err := c.cc.Invoke(ctx, AgentGetES256Key_QueryES256Key_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgentGetES256KeyServer is the server API for AgentGetES256Key service.
// All implementations must embed UnimplementedAgentGetES256KeyServer
// for forward compatibility
type AgentGetES256KeyServer interface {
	QueryES256Key(context.Context, *emptypb.Empty) (*ES256KeyMsg, error)
	mustEmbedUnimplementedAgentGetES256KeyServer()
}

// UnimplementedAgentGetES256KeyServer must be embedded to have forward compatible implementations.
type UnimplementedAgentGetES256KeyServer struct {
}

func (UnimplementedAgentGetES256KeyServer) QueryES256Key(context.Context, *emptypb.Empty) (*ES256KeyMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryES256Key not implemented")
}
func (UnimplementedAgentGetES256KeyServer) mustEmbedUnimplementedAgentGetES256KeyServer() {}

// UnsafeAgentGetES256KeyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgentGetES256KeyServer will
// result in compilation errors.
type UnsafeAgentGetES256KeyServer interface {
	mustEmbedUnimplementedAgentGetES256KeyServer()
}

func RegisterAgentGetES256KeyServer(s grpc.ServiceRegistrar, srv AgentGetES256KeyServer) {
	s.RegisterService(&AgentGetES256Key_ServiceDesc, srv)
}

func _AgentGetES256Key_QueryES256Key_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentGetES256KeyServer).QueryES256Key(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentGetES256Key_QueryES256Key_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentGetES256KeyServer).QueryES256Key(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// AgentGetES256Key_ServiceDesc is the grpc.ServiceDesc for AgentGetES256Key service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgentGetES256Key_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.system.AgentGetES256Key",
	HandlerType: (*AgentGetES256KeyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryES256Key",
			Handler:    _AgentGetES256Key_QueryES256Key_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "system/v1/system.proto",
}
