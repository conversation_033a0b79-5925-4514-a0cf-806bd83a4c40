/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import{_ as e,a as r,b as o,d as s,t,l as a,e as n}from"./index.a794166c.js";const i=""+new URL("400.162e6142.png",import.meta.url).href,l=""+new URL("401.0a3a9fbb.png",import.meta.url).href,c=""+new URL("403.7a85b4fb.png",import.meta.url).href,d=""+new URL("404.79989b51.png",import.meta.url).href,g={key:0,class:"error-content"},h={class:"error-icon-container"},m=["src"],u={key:1,class:"error-icon error-svg",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},p={class:"error-info-display"},f={class:"main-error-message"},w={key:0,class:"error-reason-display"},y={class:"reason-detail"},v={class:"error-actions"},b={key:1,class:"loading-container"};const R=e({name:"ErrorPage",data:()=>({errorCode:"",errorMessage:"",errorReason:"",state:"",requestUrl:"",isLoading:!0}),computed:{errorIconPath(){return{400:i,401:l,403:c,404:d}[this.errorCode]||null}},mounted(){this.parseUrlParams(),this.$nextTick((()=>{this.calculateTextAlignment()}))},methods:{parseUrlParams(){var e,r;const o=window.location.hash;let s;if(o.includes("?")){const e=o.split("?")[1];s=new URLSearchParams(e)}else s=new URLSearchParams(window.location.search);if(console.log("当前URL:",window.location.href),console.log("Hash:",o),console.log("当前路由名称:",null==(e=this.$route)?void 0:e.name),console.log("解析到的参数:",Object.fromEntries(s)),this.errorCode=s.get("code")||s.get("error")||s.get("status")||"",this.errorMessage=s.get("message")||s.get("error_description")||s.get("msg")||s.get("description")||"",this.errorReason=s.get("errorreason")||s.get("reason")||"",this.state=s.get("state")||"",this.requestUrl=s.get("url")||"","Error"!==(null==(r=this.$route)?void 0:r.name)||this.errorCode||s.get("error")||s.get("code")||(this.errorCode="404",this.errorMessage||(this.errorMessage="您访问的页面不存在")),console.log("解析结果:",{errorCode:this.errorCode,errorMessage:this.errorMessage,errorReason:this.errorReason,state:this.state,requestUrl:this.requestUrl}),!this.errorMessage){const e={400:"很抱歉，您的请求存在错误，无法处理",401:"很抱歉，您需要登录后才能访问此页面",403:"很抱歉，您无权限访问此页面",404:"很抱歉，您访问的页面不存在",500:"很抱歉，服务器遇到了内部错误",network_error:"网络连接失败，请检查您的网络设置",timeout:"请求超时，请稍后重试",permission_denied:"您没有执行此操作的权限",invalid_request:"请求参数无效或缺失",unauthorized_client:"客户端未经授权",access_denied:"用户拒绝了授权请求"};this.errorCode&&e[this.errorCode]?this.errorMessage=e[this.errorCode]:this.errorMessage="页面访问出现错误，请联系管理员或稍后重试"}if(!this.errorReason&&this.errorCode){const e={403:"您未经授权或未登录，无法访问所请求的页面。",401:"您的身份验证已过期，需要重新登录。",404:"请求的资源不存在或已被移动。"};this.errorReason=e[this.errorCode]||""}this.decodeBase64Params(),this.isLoading=!1,this.$nextTick((()=>{this.calculateTextAlignment()}))},decodeBase64Params(){try{this.errorMessage&&(console.log("原始message:",this.errorMessage),this.errorMessage=this.decodeUTF8Base64(this.errorMessage),console.log("解码后message:",this.errorMessage))}catch(e){console.warn("Message base64解码失败，使用原值",e)}try{this.errorReason&&(console.log("原始reason:",this.errorReason),this.errorReason=this.decodeUTF8Base64(this.errorReason),console.log("解码后reason:",this.errorReason))}catch(e){console.warn("Reason base64解码失败，使用原值",e)}},decodeUTF8Base64(e){if(!e)return e;console.log("尝试URL-safe base64解码:",e);let r=e.replace(/-/g,"+").replace(/_/g,"/");for(;r.length%4;)r+="=";console.log("转换为标准base64:",r);try{const e=Uint8Array.from(atob(r),(e=>e.charCodeAt(0))),o=new TextDecoder("utf-8").decode(e);return console.log("URL-safe base64解码结果:",o),o}catch(o){throw console.warn("URL-safe base64解码失败:",o),o}},onImageLoad(){console.log("错误图标加载完成")},onImageError(){console.warn("错误图标加载失败，使用默认图标")},calculateTextAlignment(){const e=this.$el.querySelector(".main-error-message"),r=this.$el.querySelector(".error-reason-display");if(e&&r){const o=document.createElement("div");o.style.cssText=`\n          position: absolute;\n          visibility: hidden;\n          white-space: nowrap;\n          font-size: 18px;\n          font-family: ${window.getComputedStyle(e).fontFamily};\n          line-height: 1.5;\n        `,o.textContent=e.textContent,document.body.appendChild(o);const s=o.offsetWidth,t=e.offsetWidth,a=Math.max(0,(t-s)/2);r.style.setProperty("--text-align-offset",`${a}px`),r.classList.add("aligned"),document.body.removeChild(o)}},goBack(){window.history.length>1?this.$router.go(-1):this.goHome()},goHome(){this.$router.push("/login")}}},[["render",function(e,i,l,c,d,R){return r(),o("div",{class:n(["error-page",{loading:d.isLoading}])},[d.isLoading?(r(),o("div",b,i[7]||(i[7]=[s("div",{class:"loading-spinner"},null,-1),s("p",{class:"loading-text"},"页面加载中...",-1)]))):(r(),o("div",g,[s("div",h,[R.errorIconPath?(r(),o("img",{key:0,src:R.errorIconPath,alt:"错误图标",class:"error-icon",loading:"lazy",onLoad:i[0]||(i[0]=(...e)=>R.onImageLoad&&R.onImageLoad(...e)),onError:i[1]||(i[1]=(...e)=>R.onImageError&&R.onImageError(...e))},null,40,m)):(r(),o("svg",u,i[4]||(i[4]=[s("path",{d:"M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0zM297.6 297.6c-12.8 12.8-12.8 33.472 0 46.272L465.664 512 297.6 680.064a32.768 32.768 0 0 0 46.272 46.336L512 558.272l168.064 168.128a32.704 32.704 0 1 0 46.336-46.336L558.336 512.064l168.128-168.128a32.768 32.768 0 0 0-46.336-46.272L512.064 465.728 343.872 297.6c-12.8-12.8-33.472-12.8-46.272 0z",fill:"#FF4D4D"},null,-1)])))]),s("div",p,[s("div",f,t(d.errorMessage)+"，错误代码："+t(d.errorCode||"未知"),1),d.errorReason?(r(),o("div",w,[i[5]||(i[5]=s("div",{class:"reason-title"},"访问阻断：",-1)),i[6]||(i[6]=s("div",{class:"reason-text"},"您可能遇到了以下情况之一：",-1)),s("div",y,t(d.errorReason),1)])):a("",!0)]),s("div",v,[s("button",{class:"btn btn-secondary",onClick:i[2]||(i[2]=(...e)=>R.goBack&&R.goBack(...e))},"返回上页"),s("button",{class:"btn btn-primary",onClick:i[3]||(i[3]=(...e)=>R.goHome&&R.goHome(...e))},"回到首页")])]))],2)}],["__scopeId","data-v-0976c458"]]);export{R as default};
