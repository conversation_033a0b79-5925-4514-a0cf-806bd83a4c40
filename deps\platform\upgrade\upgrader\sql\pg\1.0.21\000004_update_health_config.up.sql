UPDATE tb_application set health_config = '{"config": {"path": "/index.html", "timeout": 3, "protocol": "https", "fail_nums": 3, "health_code": [200], "success_num": 3, "interval_time": 15, "health_intervals": 5, "un_health_intervals": 5}, "enable": "0"}';

update tb_application set open_config = '{"enabled": false, "open_type": "browser", "supported_os": ["windows", "macos", "linux"], "per_os_config": false, "browser_configs": [{"type": "Default", "params": ""}], "program_configs": [{"os": "windows", "name": "", "path": "", "params": "", "bundleId": "", "showMoreConfig": false, "notFoundMessage": ""}], "not_found_message": "", "show_not_found_tip": false, "system_app_configs": [{"os": "windows", "type": "remote_desktop"}]}';

delete from tb_opt_resource where id IN('57','58');
INSERT INTO "public"."tb_opt_resource" ("id", "name", "resource_type", "opt_type") VALUES ('57', '下载', 'DOWNLOAD', 1);
INSERT INTO "public"."tb_opt_resource" ("id", "name", "resource_type", "opt_type") VALUES ('58', '导入', 'IMPORT', 1);




-- 添加健康检查配置字段
DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'tb_app_health_log'
              AND column_name = 'health_config'
        ) THEN
            ALTER TABLE public.tb_app_health_log ADD COLUMN health_config jsonb NULL;
        END IF;
    END $$;
COMMENT ON COLUMN public.tb_app_health_log.health_config IS 'JSON格式存储健康检查配置';