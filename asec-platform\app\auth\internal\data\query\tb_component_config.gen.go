// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbComponentConfig(db *gorm.DB, opts ...gen.DOOption) tbComponentConfig {
	_tbComponentConfig := tbComponentConfig{}

	_tbComponentConfig.tbComponentConfigDo.UseDB(db, opts...)
	_tbComponentConfig.tbComponentConfigDo.UseModel(&model.TbComponentConfig{})

	tableName := _tbComponentConfig.tbComponentConfigDo.TableName()
	_tbComponentConfig.ALL = field.NewAsterisk(tableName)
	_tbComponentConfig.ID = field.NewString(tableName, "id")
	_tbComponentConfig.ComponentID = field.NewString(tableName, "component_id")
	_tbComponentConfig.Name = field.NewString(tableName, "name")
	_tbComponentConfig.Value = field.NewString(tableName, "value")

	_tbComponentConfig.fillFieldMap()

	return _tbComponentConfig
}

type tbComponentConfig struct {
	tbComponentConfigDo tbComponentConfigDo

	ALL         field.Asterisk
	ID          field.String
	ComponentID field.String
	Name        field.String
	Value       field.String

	fieldMap map[string]field.Expr
}

func (t tbComponentConfig) Table(newTableName string) *tbComponentConfig {
	t.tbComponentConfigDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbComponentConfig) As(alias string) *tbComponentConfig {
	t.tbComponentConfigDo.DO = *(t.tbComponentConfigDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbComponentConfig) updateTableName(table string) *tbComponentConfig {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.ComponentID = field.NewString(table, "component_id")
	t.Name = field.NewString(table, "name")
	t.Value = field.NewString(table, "value")

	t.fillFieldMap()

	return t
}

func (t *tbComponentConfig) WithContext(ctx context.Context) *tbComponentConfigDo {
	return t.tbComponentConfigDo.WithContext(ctx)
}

func (t tbComponentConfig) TableName() string { return t.tbComponentConfigDo.TableName() }

func (t tbComponentConfig) Alias() string { return t.tbComponentConfigDo.Alias() }

func (t *tbComponentConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbComponentConfig) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 4)
	t.fieldMap["id"] = t.ID
	t.fieldMap["component_id"] = t.ComponentID
	t.fieldMap["name"] = t.Name
	t.fieldMap["value"] = t.Value
}

func (t tbComponentConfig) clone(db *gorm.DB) tbComponentConfig {
	t.tbComponentConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbComponentConfig) replaceDB(db *gorm.DB) tbComponentConfig {
	t.tbComponentConfigDo.ReplaceDB(db)
	return t
}

type tbComponentConfigDo struct{ gen.DO }

func (t tbComponentConfigDo) Debug() *tbComponentConfigDo {
	return t.withDO(t.DO.Debug())
}

func (t tbComponentConfigDo) WithContext(ctx context.Context) *tbComponentConfigDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbComponentConfigDo) ReadDB() *tbComponentConfigDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbComponentConfigDo) WriteDB() *tbComponentConfigDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbComponentConfigDo) Session(config *gorm.Session) *tbComponentConfigDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbComponentConfigDo) Clauses(conds ...clause.Expression) *tbComponentConfigDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbComponentConfigDo) Returning(value interface{}, columns ...string) *tbComponentConfigDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbComponentConfigDo) Not(conds ...gen.Condition) *tbComponentConfigDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbComponentConfigDo) Or(conds ...gen.Condition) *tbComponentConfigDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbComponentConfigDo) Select(conds ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbComponentConfigDo) Where(conds ...gen.Condition) *tbComponentConfigDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbComponentConfigDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbComponentConfigDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbComponentConfigDo) Order(conds ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbComponentConfigDo) Distinct(cols ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbComponentConfigDo) Omit(cols ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbComponentConfigDo) Join(table schema.Tabler, on ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbComponentConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbComponentConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbComponentConfigDo) Group(cols ...field.Expr) *tbComponentConfigDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbComponentConfigDo) Having(conds ...gen.Condition) *tbComponentConfigDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbComponentConfigDo) Limit(limit int) *tbComponentConfigDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbComponentConfigDo) Offset(offset int) *tbComponentConfigDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbComponentConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbComponentConfigDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbComponentConfigDo) Unscoped() *tbComponentConfigDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbComponentConfigDo) Create(values ...*model.TbComponentConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbComponentConfigDo) CreateInBatches(values []*model.TbComponentConfig, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbComponentConfigDo) Save(values ...*model.TbComponentConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbComponentConfigDo) First() (*model.TbComponentConfig, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponentConfig), nil
	}
}

func (t tbComponentConfigDo) Take() (*model.TbComponentConfig, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponentConfig), nil
	}
}

func (t tbComponentConfigDo) Last() (*model.TbComponentConfig, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponentConfig), nil
	}
}

func (t tbComponentConfigDo) Find() ([]*model.TbComponentConfig, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbComponentConfig), err
}

func (t tbComponentConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbComponentConfig, err error) {
	buf := make([]*model.TbComponentConfig, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbComponentConfigDo) FindInBatches(result *[]*model.TbComponentConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbComponentConfigDo) Attrs(attrs ...field.AssignExpr) *tbComponentConfigDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbComponentConfigDo) Assign(attrs ...field.AssignExpr) *tbComponentConfigDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbComponentConfigDo) Joins(fields ...field.RelationField) *tbComponentConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbComponentConfigDo) Preload(fields ...field.RelationField) *tbComponentConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbComponentConfigDo) FirstOrInit() (*model.TbComponentConfig, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponentConfig), nil
	}
}

func (t tbComponentConfigDo) FirstOrCreate() (*model.TbComponentConfig, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbComponentConfig), nil
	}
}

func (t tbComponentConfigDo) FindByPage(offset int, limit int) (result []*model.TbComponentConfig, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbComponentConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbComponentConfigDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbComponentConfigDo) Delete(models ...*model.TbComponentConfig) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbComponentConfigDo) withDO(do gen.Dao) *tbComponentConfigDo {
	t.DO = *do.(*gen.DO)
	return t
}
