package service

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/consts"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/dto"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/repository"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/strategy_model"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"sort"
	"strconv"
	"sync"
)

var strategyGroupServiceImpl StrategyGroupService

var StrategyGroupServiceInit sync.Once

type strategyGroupService struct {
	db         repository.StrategyGroupRepository
	strategyDb repository.StrategyRepository
}

func (s strategyGroupService) GroupMove(c *gin.Context, moveReq vo.GroupMoveReq) aerrors.AError {
	// 不允许操作默认分组
	if moveReq.GroupId == "1" {
		return aerrors.NewWithError(errors.New("DefaultDynamicGroupErr"), consts.DefaultDynamicGroupErr)
	}
	// 如果是移动到xx之后, 判断是否在默认分组之后
	if moveReq.Where == "after" && moveReq.DestPriority > 0 {
		byPriority, err := s.db.GetByPriority(c, moveReq.DestPriority)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		if byPriority.ID == "1" {
			return aerrors.NewWithError(errors.New("move after default group"), consts.MoveAfterDefaultGroupErr)
		}
	}
	// 如果是置底 则改为在默认分组之前
	if moveReq.DestPriority == -1 {
		defaultGroup, err := s.db.GetGroupById(c, "1")
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		moveReq.DestPriority = defaultGroup.Priority
		moveReq.Where = consts.Before
	}

	err2, groups := s.GetChangeGroupV2(c, moveReq)
	if err2 != nil {
		return aerrors.NewWithError(err2, common.OperateError)
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err2 = s.db.UpdateGroupsPriority(db, groups)
	if err2 != nil {
		return aerrors.NewWithError(err2, common.OperateError)
	}
	// 策略随 策略组优先级变动而变动
	reGroupStrategies, err := s.strategyDb.ReGroupByRowNumber(c)
	if err != nil {
		return aerrors.NewWithError(err2, common.OperateError)
	}
	// 计算需要更改优先级的策略
	var changeList []model.AccessStrategy
	for _, strategy := range reGroupStrategies {
		if strategy.RowNumber == strategy.Priority {
			continue
		}
		accessStrategy := model.AccessStrategy{}
		accessStrategy.ID = strategy.Id
		// 通过RowNumber 排序重排策略优先级
		accessStrategy.Priority = strategy.RowNumber
		changeList = append(changeList, accessStrategy)
	}
	err2 = s.strategyDb.UpdatesStrategyPriority(db, changeList)
	if err2 != nil {
		return aerrors.NewWithError(err2, common.OperateError)
	}
	return nil
}

// GetChangeGroupV2 获取需要改变的其他组和需要改变的当前组优先级
func (s strategyGroupService) GetChangeGroupV2(c *gin.Context, req vo.GroupMoveReq) (error, []strategy_model.DynamicStrategyGroup) {
	groups, err := s.db.GetAllGroup(c, req.GroupId)
	if err != nil {
		return err, nil
	}
	// 转换为可以 float 优先级, 以便进行排序
	var sortGroups []dto.SortStrategyGroup
	for i := range groups {
		sortGroups = append(sortGroups, dto.SortStrategyGroup{
			Id:       groups[i].ID,
			Priority: float32(groups[i].Priority),
		})
	}
	var newPriority float32
	if req.DestPriority == 0 {
		newPriority = -0.5
	} else if req.DestPriority == -1 {
		// 因为分组的优先级为0开始 所以即使排除了自己也不需要再加一
		newPriority = float32(len(sortGroups)) + 0.5
	} else {
		if req.Where == consts.Before {
			newPriority = float32(req.DestPriority) - 0.5
		} else {
			newPriority = float32(req.DestPriority) + 0.5
		}
	}
	moveDestGroup := dto.SortStrategyGroup{
		Id:       req.GroupId,
		Priority: newPriority,
	}

	sortGroups = append(sortGroups, moveDestGroup)
	sortGroupsCache := make(map[string]dto.SortStrategyGroup)
	for i := range sortGroups {
		sortGroupsCache[sortGroups[i].Id] = sortGroups[i]
	}
	var needChangeGroup []strategy_model.DynamicStrategyGroup
	// 重排优先级
	sort.SliceStable(sortGroups, func(i, j int) bool {
		return sortGroups[i].Priority < sortGroups[j].Priority
	})
	for i := range sortGroups {
		sortGroups[i].Priority = float32(i)
	}
	// 对比优先级发生变更的进行更新
	for i := range sortGroups {
		if sortGroups[i].Id == req.GroupId {
			needChangeGroup = append(needChangeGroup, strategy_model.DynamicStrategyGroup{
				ID:       sortGroups[i].Id,
				Priority: int32(sortGroups[i].Priority),
			})
			continue
		}
		oldPriority := sortGroupsCache[sortGroups[i].Id]
		if oldPriority.Priority != sortGroups[i].Priority {
			needChangeGroup = append(needChangeGroup, strategy_model.DynamicStrategyGroup{
				ID:       sortGroups[i].Id,
				Priority: int32(sortGroups[i].Priority),
			})
		}
	}

	return nil, needChangeGroup
}

func (s strategyGroupService) GroupList(c *gin.Context, query string) (vo.GroupListResp, aerrors.AError) {
	groupList, aError := s.db.GroupList(c, query)
	if aError != nil {
		return vo.GroupListResp{}, aError
	}
	resp := vo.GroupListResp{
		GroupName:     "全部策略",
		ChildrenGroup: groupList,
	}
	return resp, aError
}

func (s strategyGroupService) UpdateGroup(c *gin.Context, req vo.UpdateGroupReq) aerrors.AError {
	if req.GroupId == consts.DefaultGroupId {
		return aerrors.New("default group can not delete", consts.DefaultDynamicGroupErr)
	}
	duplicateName, err := s.db.CheckDuplicateName(c, req.GroupName)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	if duplicateName {
		return aerrors.New("duplicate group name", consts.DuplicateNameErr)
	}

	err = s.db.UpdateGroupName(c, req.GroupId, req.GroupName)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s strategyGroupService) DeleteGroup(c *gin.Context, deleteGroupReq vo.DeleteGroupReq) aerrors.AError {
	if deleteGroupReq.GroupId == consts.DefaultGroupId {
		return aerrors.New("default group can not delete", consts.DefaultDynamicGroupErr)
	}
	delGroup, err := s.db.GetGroupById(c, deleteGroupReq.GroupId)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	// 将删除组优先级更高的分组往前一位
	higherGroups, err := s.db.GetByPriorityCompare(c, delGroup.Priority, "higher")
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	for i := range higherGroups {
		higherGroups[i].Priority = higherGroups[i].Priority - 1
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {

		dbErr := s.db.DeleteGroup(tx, deleteGroupReq)
		if dbErr != nil {
			return dbErr
		}
		if len(higherGroups) <= 0 {
			return nil
		}
		dbErr = s.db.UpdateGroups(tx, higherGroups)
		if dbErr != nil {
			return dbErr
		}
		return nil
	})
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}

	// 分组内策略影响
	if deleteGroupReq.DeleteInside {
		// 删除组内的
		delList, err := s.strategyDb.StrategySimpleList(c, deleteGroupReq.GroupId)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		if len(delList) <= 0 {
			return nil
		}

		var delIds []string
		for _, resp := range delList {
			delIds = append(delIds, strconv.FormatUint(resp.Id, 10))
		}
		err = GetStrategyService().DelStrategy(c, vo.DelStrategyReq{Ids: delIds})
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}

	} else {
		// 将组内的移动到默认分组最后
		moveStrategyList, err := s.strategyDb.GerStrategySortPriority(c, 0, "", deleteGroupReq.GroupId)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		if len(moveStrategyList) <= 0 {
			return nil
		}

		// 所有带优先级的策略
		strategyPriorities, err := s.strategyDb.GerStrategySortPriority(c, 0, deleteGroupReq.GroupId, "")
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}

		// 获取移动到默认组的新优先级
		newPriority, err := s.strategyDb.GetStrategyPriorityByGroup(c, vo.StrategyMoveReq{GroupId: consts.DefaultGroupId, DestPriority: -1})
		// 如果组内没有策略,则通过新增的获取优先级方法 按照组所在的优先级分配一个优先级
		if newPriority == 0 {
			newPriority, err = s.strategyDb.GetPriorityByGroup(c, consts.DefaultGroupId)
			if err != nil {
				return aerrors.NewWithError(err, common.OperateError)
			}
		}

		// 使用float 用来重排优先级,避免累加步长超过1
		var addStep float32
		addStep = (float32(1) - 0.1) / float32(len(moveStrategyList))
		for i := range moveStrategyList {
			moveStrategyList[i].Priority = float32(newPriority) + float32(i+1)*addStep
		}

		// 更新分组id
		var _l []model.AccessStrategy
		for _, s := range moveStrategyList {
			ac := model.AccessStrategy{}
			ac.ID = s.Id
			ac.StrategyGroupId = consts.DefaultGroupId
			_l = append(_l, ac)
		}
		err = s.strategyDb.UpdatesStrategyGroupId(db, _l)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}

		// 整合后重排策略优先级
		strategyPriorities = append(strategyPriorities, moveStrategyList...)

		changePrioritys, _ := GetChangePriority(strategyPriorities, 0)
		err = s.strategyDb.UpdatesStrategyPriority(db, changePrioritys)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
	}

	return nil
}

func (s strategyGroupService) CreateGroup(c *gin.Context, createGroupReq vo.CreateGroupReq) aerrors.AError {
	// 检查重名
	existDuplicate, err := s.db.CheckDuplicateName(c, createGroupReq.GroupName)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	if existDuplicate {
		return aerrors.New("duplicate group name err:"+createGroupReq.GroupName, consts.DuplicateNameErr)
	}
	// 获取所有分组
	groups, err := s.db.GetAllGroup(c, "")
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	// 将所有分组的优先级下降一位
	for i := range groups {
		groups[i].Priority = groups[i].Priority + 1
	}

	strategyGroup := strategy_model.DynamicStrategyGroup{
		ID:        uuid.New().String(),
		GroupName: createGroupReq.GroupName,
		Priority:  0,
	}

	// 开启事务
	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		dbErr := s.db.SaveStrategyGroup(tx, strategyGroup)
		if dbErr != nil {
			return dbErr
		}
		if len(groups) <= 0 {
			return nil
		}
		dbErr = s.db.UpdateGroups(tx, groups)
		if dbErr != nil {
			return dbErr
		}
		return nil
	})
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func GetStrategyGroupService() StrategyGroupService {
	StrategyGroupServiceInit.Do(func() {
		strategyGroupServiceImpl = strategyGroupService{
			db:         repository.NewStrategyGroupRepository(),
			strategyDb: repository.NewStrategyRepository(),
		}
	})
	return strategyGroupServiceImpl
}

type StrategyGroupService interface {
	CreateGroup(c *gin.Context, createGroupReq vo.CreateGroupReq) aerrors.AError
	DeleteGroup(c *gin.Context, deleteGroupReq vo.DeleteGroupReq) aerrors.AError
	UpdateGroup(c *gin.Context, req vo.UpdateGroupReq) aerrors.AError
	GroupList(c *gin.Context, query string) (vo.GroupListResp, aerrors.AError)
	GroupMove(c *gin.Context, moveReq vo.GroupMoveReq) aerrors.AError
}
