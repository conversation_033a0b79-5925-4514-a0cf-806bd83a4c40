// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: appliance/v1/appliance_mgt.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ApplianceMgtClient is the client API for ApplianceMgt service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApplianceMgtClient interface {
	// appliance首次安装注册，返回applianceID
	Enroll(ctx context.Context, in *ApplianceEnrollReq, opts ...grpc.CallOption) (*ApplianceEnrollRes, error)
	Heartbeat(ctx context.Context, opts ...grpc.CallOption) (ApplianceMgt_HeartbeatClient, error)
	GetConfig(ctx context.Context, in *GetConfigReq, opts ...grpc.CallOption) (*Configs, error)
	// http接口
	SetUpgradeStatus(ctx context.Context, in *SetUpgradeStatusReq, opts ...grpc.CallOption) (*SetUpgradeStatusRes, error)
	// 任务汇报接口
	TaskReport(ctx context.Context, in *TaskReportReq, opts ...grpc.CallOption) (*TaskReportResp, error)
	// 任务汇报接口
	HeartbeatReport(ctx context.Context, in *HeartbeatReportReq, opts ...grpc.CallOption) (*HeartbeatReportRes, error)
}

type applianceMgtClient struct {
	cc grpc.ClientConnInterface
}

func NewApplianceMgtClient(cc grpc.ClientConnInterface) ApplianceMgtClient {
	return &applianceMgtClient{cc}
}

func (c *applianceMgtClient) Enroll(ctx context.Context, in *ApplianceEnrollReq, opts ...grpc.CallOption) (*ApplianceEnrollRes, error) {
	out := new(ApplianceEnrollRes)
	err := c.cc.Invoke(ctx, "/api.appliance.ApplianceMgt/Enroll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applianceMgtClient) Heartbeat(ctx context.Context, opts ...grpc.CallOption) (ApplianceMgt_HeartbeatClient, error) {
	stream, err := c.cc.NewStream(ctx, &ApplianceMgt_ServiceDesc.Streams[0], "/api.appliance.ApplianceMgt/Heartbeat", opts...)
	if err != nil {
		return nil, err
	}
	x := &applianceMgtHeartbeatClient{stream}
	return x, nil
}

type ApplianceMgt_HeartbeatClient interface {
	Send(*HeartbeatReq) error
	CloseAndRecv() (*HeartbeatRes, error)
	grpc.ClientStream
}

type applianceMgtHeartbeatClient struct {
	grpc.ClientStream
}

func (x *applianceMgtHeartbeatClient) Send(m *HeartbeatReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *applianceMgtHeartbeatClient) CloseAndRecv() (*HeartbeatRes, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(HeartbeatRes)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *applianceMgtClient) GetConfig(ctx context.Context, in *GetConfigReq, opts ...grpc.CallOption) (*Configs, error) {
	out := new(Configs)
	err := c.cc.Invoke(ctx, "/api.appliance.ApplianceMgt/GetConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applianceMgtClient) SetUpgradeStatus(ctx context.Context, in *SetUpgradeStatusReq, opts ...grpc.CallOption) (*SetUpgradeStatusRes, error) {
	out := new(SetUpgradeStatusRes)
	err := c.cc.Invoke(ctx, "/api.appliance.ApplianceMgt/SetUpgradeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applianceMgtClient) TaskReport(ctx context.Context, in *TaskReportReq, opts ...grpc.CallOption) (*TaskReportResp, error) {
	out := new(TaskReportResp)
	err := c.cc.Invoke(ctx, "/api.appliance.ApplianceMgt/TaskReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *applianceMgtClient) HeartbeatReport(ctx context.Context, in *HeartbeatReportReq, opts ...grpc.CallOption) (*HeartbeatReportRes, error) {
	out := new(HeartbeatReportRes)
	err := c.cc.Invoke(ctx, "/api.appliance.ApplianceMgt/HeartbeatReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApplianceMgtServer is the server API for ApplianceMgt service.
// All implementations must embed UnimplementedApplianceMgtServer
// for forward compatibility
type ApplianceMgtServer interface {
	// appliance首次安装注册，返回applianceID
	Enroll(context.Context, *ApplianceEnrollReq) (*ApplianceEnrollRes, error)
	Heartbeat(ApplianceMgt_HeartbeatServer) error
	GetConfig(context.Context, *GetConfigReq) (*Configs, error)
	// http接口
	SetUpgradeStatus(context.Context, *SetUpgradeStatusReq) (*SetUpgradeStatusRes, error)
	// 任务汇报接口
	TaskReport(context.Context, *TaskReportReq) (*TaskReportResp, error)
	// 任务汇报接口
	HeartbeatReport(context.Context, *HeartbeatReportReq) (*HeartbeatReportRes, error)
	mustEmbedUnimplementedApplianceMgtServer()
}

// UnimplementedApplianceMgtServer must be embedded to have forward compatible implementations.
type UnimplementedApplianceMgtServer struct {
}

func (UnimplementedApplianceMgtServer) Enroll(context.Context, *ApplianceEnrollReq) (*ApplianceEnrollRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Enroll not implemented")
}
func (UnimplementedApplianceMgtServer) Heartbeat(ApplianceMgt_HeartbeatServer) error {
	return status.Errorf(codes.Unimplemented, "method Heartbeat not implemented")
}
func (UnimplementedApplianceMgtServer) GetConfig(context.Context, *GetConfigReq) (*Configs, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfig not implemented")
}
func (UnimplementedApplianceMgtServer) SetUpgradeStatus(context.Context, *SetUpgradeStatusReq) (*SetUpgradeStatusRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUpgradeStatus not implemented")
}
func (UnimplementedApplianceMgtServer) TaskReport(context.Context, *TaskReportReq) (*TaskReportResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskReport not implemented")
}
func (UnimplementedApplianceMgtServer) HeartbeatReport(context.Context, *HeartbeatReportReq) (*HeartbeatReportRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HeartbeatReport not implemented")
}
func (UnimplementedApplianceMgtServer) mustEmbedUnimplementedApplianceMgtServer() {}

// UnsafeApplianceMgtServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApplianceMgtServer will
// result in compilation errors.
type UnsafeApplianceMgtServer interface {
	mustEmbedUnimplementedApplianceMgtServer()
}

func RegisterApplianceMgtServer(s grpc.ServiceRegistrar, srv ApplianceMgtServer) {
	s.RegisterService(&ApplianceMgt_ServiceDesc, srv)
}

func _ApplianceMgt_Enroll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplianceEnrollReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplianceMgtServer).Enroll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ApplianceMgt/Enroll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplianceMgtServer).Enroll(ctx, req.(*ApplianceEnrollReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplianceMgt_Heartbeat_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ApplianceMgtServer).Heartbeat(&applianceMgtHeartbeatServer{stream})
}

type ApplianceMgt_HeartbeatServer interface {
	SendAndClose(*HeartbeatRes) error
	Recv() (*HeartbeatReq, error)
	grpc.ServerStream
}

type applianceMgtHeartbeatServer struct {
	grpc.ServerStream
}

func (x *applianceMgtHeartbeatServer) SendAndClose(m *HeartbeatRes) error {
	return x.ServerStream.SendMsg(m)
}

func (x *applianceMgtHeartbeatServer) Recv() (*HeartbeatReq, error) {
	m := new(HeartbeatReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ApplianceMgt_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplianceMgtServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ApplianceMgt/GetConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplianceMgtServer).GetConfig(ctx, req.(*GetConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplianceMgt_SetUpgradeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUpgradeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplianceMgtServer).SetUpgradeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ApplianceMgt/SetUpgradeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplianceMgtServer).SetUpgradeStatus(ctx, req.(*SetUpgradeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplianceMgt_TaskReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplianceMgtServer).TaskReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ApplianceMgt/TaskReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplianceMgtServer).TaskReport(ctx, req.(*TaskReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ApplianceMgt_HeartbeatReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeartbeatReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplianceMgtServer).HeartbeatReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ApplianceMgt/HeartbeatReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplianceMgtServer).HeartbeatReport(ctx, req.(*HeartbeatReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ApplianceMgt_ServiceDesc is the grpc.ServiceDesc for ApplianceMgt service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApplianceMgt_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.appliance.ApplianceMgt",
	HandlerType: (*ApplianceMgtServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Enroll",
			Handler:    _ApplianceMgt_Enroll_Handler,
		},
		{
			MethodName: "GetConfig",
			Handler:    _ApplianceMgt_GetConfig_Handler,
		},
		{
			MethodName: "SetUpgradeStatus",
			Handler:    _ApplianceMgt_SetUpgradeStatus_Handler,
		},
		{
			MethodName: "TaskReport",
			Handler:    _ApplianceMgt_TaskReport_Handler,
		},
		{
			MethodName: "HeartbeatReport",
			Handler:    _ApplianceMgt_HeartbeatReport_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Heartbeat",
			Handler:       _ApplianceMgt_Heartbeat_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "appliance/v1/appliance_mgt.proto",
}
