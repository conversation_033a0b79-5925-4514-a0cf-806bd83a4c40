package biz

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/dto"
)

// ClientLimitUsecase 客户端限制业务逻辑
type ClientLimitUsecase struct {
	trackerRepo   SessionTrackerRepo
	blacklistRepo JWTBlacklistRepo
	policyRepo    AuthPolicyRepo
	userRepo      UserRepo
	userGroupRepo UserGroupRepo
	log           *log.Helper
}

// NewClientLimitUsecase 创建客户端限制业务逻辑
func NewClientLimitUsecase(
	trackerRepo SessionTrackerRepo,
	blacklistRepo JWTBlacklistRepo,
	policyRepo AuthPolicyRepo,
	userRepo UserRepo,
	userGroupRepo UserGroupRepo,
	logger log.Logger,
) *ClientLimitUsecase {
	return &ClientLimitUsecase{
		trackerRepo:   trackerRepo,
		blacklistRepo: blacklistRepo,
		policyRepo:    policyRepo,
		userRepo:      userRepo,
		userGroupRepo: userGroupRepo,
		log:           log.NewHelper(logger),
	}
}

// CheckClientLimit 检查客户端数量限制
func (c *ClientLimitUsecase) CheckClientLimit(ctx context.Context, param dto.ClientLimitCheckParam) error {
	// c.log.Infof("Starting client limit check: corpID=%s, userID=%s, clientType=%s, clientCategory=%s",
	// 	param.CorpID, param.UserID, param.ClientType, param.ClientCategory)

	// 1. 获取用户的认证策略
	policy, err := c.getUserAuthPolicy(ctx, param.CorpID, param.UserID)
	if err != nil {
		c.log.Errorf("Failed to get auth policy: %v", err)
		return err
	}

	// 2. 获取客户端限制配置
	limits := policy.ClientLimits
	if limits == nil {
		defaultLimits := dto.DefaultClientLimits()
		limits = &defaultLimits
	}

	// 3. 检查是否禁止该类型客户端
	clientCategory := param.ClientType.GetClientCategory()
	maxClients := limits.GetMaxClientsForType(clientCategory)
	// c.log.Infof("Client category %s max allowed: %d", clientCategory, maxClients)

	if maxClients == -1 {
		c.log.Warnf("Client type %s is forbidden", param.ClientType)
		return pb.ErrorClientTypeForbidden("client type %s is forbidden", param.ClientType)
	}

	// 4. 如果不限制数量，直接通过
	if maxClients == 0 {
		// c.log.Infof("No limit for client category %s, allowing login", clientCategory)
		return nil
	}

	// 5. 获取当前活跃会话数量（按客户端分类统计）
	activeCount, err := c.trackerRepo.GetActiveSessionCountByCategory(ctx, param.CorpID, param.UserID, clientCategory)
	if err != nil {
		c.log.Errorf("Failed to get active session count: %v", err)
		return err
	}

	// c.log.Infof("Current active sessions for category %s: %d, max allowed: %d", clientCategory, activeCount, maxClients)

	// 6. 如果未超限，直接通过
	if activeCount < maxClients {
		// c.log.Infof("Session count within limit, allowing login")
		return nil
	}

	// 7. 根据策略处理超限情况
	// c.log.Warnf("Session limit exceeded, applying overflow strategy: %s", limits.OverflowStrategy)
	return c.handleClientOverflow(ctx, param.CorpID, param.UserID, param.ClientType, limits.OverflowStrategy)
}

// CreateSessionTracker 创建会话跟踪记录
func (c *ClientLimitUsecase) CreateSessionTracker(ctx context.Context, param dto.CreateSessionTrackerParam) error {
	return c.trackerRepo.CreateSessionTracker(ctx, param)
}

// KickSession 踢出指定会话
func (c *ClientLimitUsecase) KickSession(ctx context.Context, jwtId string) error {
	// 1. 将JWT加入黑名单
	err := c.blacklistRepo.AddToBlacklist(ctx, jwtId, time.Now().Add(24*time.Hour))
	if err != nil {
		return err
	}

	// 2. 删除会话跟踪记录
	err = c.trackerRepo.DeleteSessionTracker(ctx, jwtId)
	if err != nil {
		c.log.Errorf("Failed to delete session tracker: %v", err)
		// 不返回错误，因为黑名单已经生效
	}

	return nil
}

// UpdateSessionActivity 更新会话活跃度
func (c *ClientLimitUsecase) UpdateSessionActivity(ctx context.Context, jwtId string) error {
	return c.trackerRepo.UpdateLastActiveTime(ctx, jwtId)
}

// CleanExpiredSessions 清理过期会话
func (c *ClientLimitUsecase) CleanExpiredSessions(ctx context.Context) error {
	// 清理过期的会话跟踪记录
	err := c.trackerRepo.DeleteExpiredTrackers(ctx)
	if err != nil {
		c.log.Errorf("Failed to delete expired trackers: %v", err)
	}

	// 清理过期的黑名单记录
	err = c.blacklistRepo.RemoveExpiredFromBlacklist(ctx)
	if err != nil {
		c.log.Errorf("Failed to remove expired blacklist: %v", err)
	}

	return nil
}

// handleClientOverflow 处理客户端超限
func (c *ClientLimitUsecase) handleClientOverflow(ctx context.Context, corpId, userId string, clientType dto.ClientType, strategy dto.OverflowStrategy) error {
	switch strategy {
	case dto.OverflowStrategyKickOldest:
		return c.kickOldestSession(ctx, corpId, userId, clientType)
	case dto.OverflowStrategyKickInactive:
		return c.kickInactiveSession(ctx, corpId, userId, clientType)
	case dto.OverflowStrategyRejectNew:
		return pb.ErrorClientLimitExceeded("client limit exceeded")
	default:
		return c.kickOldestSession(ctx, corpId, userId, clientType)
	}
}

// kickOldestSession 踢出最旧会话
func (c *ClientLimitUsecase) kickOldestSession(ctx context.Context, corpId, userId string, clientType dto.ClientType) error {
	// 按客户端分类获取最旧的JWT ID
	clientCategory := clientType.GetClientCategory()
	jwtId, err := c.trackerRepo.GetOldestJWTIdByCategory(ctx, corpId, userId, clientCategory)
	if err != nil {
		return err
	}

	if jwtId == "" {
		return nil // 没有找到会话
	}

	// 踢出会话
	return c.KickSession(ctx, jwtId)
}

// kickInactiveSession 踢出最不活跃会话
func (c *ClientLimitUsecase) kickInactiveSession(ctx context.Context, corpId, userId string, clientType dto.ClientType) error {
	// 按客户端分类获取最不活跃的JWT ID
	clientCategory := clientType.GetClientCategory()
	jwtId, err := c.trackerRepo.GetInactiveJWTIdByCategory(ctx, corpId, userId, clientCategory)
	if err != nil {
		return err
	}

	if jwtId == "" {
		return nil // 没有找到会话
	}

	// 踢出会话
	return c.KickSession(ctx, jwtId)
}

// getUserAuthPolicy 获取用户的认证策略
func (c *ClientLimitUsecase) getUserAuthPolicy(ctx context.Context, corpId, userId string) (*dto.AuthPolicyInfo, error) {
	c.log.Infof("getUserAuthPolicy called with corpId=%v, userId=%v", corpId, userId)

	// 1. 获取用户信息
	user, err := c.userRepo.QueryUserEntity(ctx, corpId, userId)
	if err != nil {
		c.log.Errorf("QueryUserEntity failed. err=%v, corpId=%v, userId=%v", err, corpId, userId)
		// 如果用户不存在，返回默认限制策略
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	c.log.Infof("Found user with GroupID=%v", user.GroupID)

	// 2. 尝试获取用户根组的默认认证策略
	defaultPolicy, err := c.policyRepo.GetDefaultPolicyInRootGroup(ctx, corpId, user.GroupID)
	if err != nil {
		c.log.Errorf("GetDefaultPolicyInRootGroup failed. err=%v, corpId=%v, groupId=%v", err, corpId, user.GroupID)
		// 如果获取策略失败，返回默认限制策略
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	// 3. 如果没有找到默认策略，返回默认限制策略
	if defaultPolicy.ID == "" {
		c.log.Warnf("No default policy found for user. corpId=%v, userId=%v, groupId=%v", corpId, userId, user.GroupID)
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	c.log.Infof("Found default policy: ID=%v, Name=%v", defaultPolicy.ID, defaultPolicy.Name)

	// 4. 使用 GetClientLimits 方法获取策略中的客户端限制配置
	clientLimits, err := c.policyRepo.GetClientLimits(ctx, corpId, defaultPolicy.ID)
	if err != nil {
		c.log.Errorf("GetClientLimits failed. err=%v, corpId=%v, policyId=%v", err, corpId, defaultPolicy.ID)
		// 如果获取客户端限制失败，返回默认限制策略
		defaultLimits := dto.DefaultClientLimits()
		return &dto.AuthPolicyInfo{
			ClientLimits: &defaultLimits,
		}, nil
	}

	// c.log.Infof("Retrieved client limits from policy: %+v", clientLimits)

	// 5. 构造并返回认证策略信息
	return &dto.AuthPolicyInfo{
		ID:           defaultPolicy.ID,
		Name:         defaultPolicy.Name,
		Description:  defaultPolicy.Description,
		ClientLimits: clientLimits,
	}, nil
}

// GenerateSessionID 生成会话ID
func (c *ClientLimitUsecase) GenerateSessionID() string {
	return uuid.New().String()
}

// GenerateJWTID 生成JWT ID
func (c *ClientLimitUsecase) GenerateJWTID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
