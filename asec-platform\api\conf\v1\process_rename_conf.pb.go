// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/process_rename_conf.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 进程重命名配置
type ProcessRenameConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 通用前缀名
	RenamePrefix string `protobuf:"bytes,1,opt,name=rename_prefix,json=renamePrefix,proto3" json:"rename_prefix,omitempty"`
	// 强制重启开关
	ForceRestart bool `protobuf:"varint,2,opt,name=force_restart,json=forceRestart,proto3" json:"force_restart,omitempty"`
}

func (x *ProcessRenameConf) Reset() {
	*x = ProcessRenameConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_process_rename_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessRenameConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessRenameConf) ProtoMessage() {}

func (x *ProcessRenameConf) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_process_rename_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessRenameConf.ProtoReflect.Descriptor instead.
func (*ProcessRenameConf) Descriptor() ([]byte, []int) {
	return file_conf_v1_process_rename_conf_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessRenameConf) GetRenamePrefix() string {
	if x != nil {
		return x.RenamePrefix
	}
	return ""
}

func (x *ProcessRenameConf) GetForceRestart() bool {
	if x != nil {
		return x.ForceRestart
	}
	return false
}

var File_conf_v1_process_rename_conf_proto protoreflect.FileDescriptor

var file_conf_v1_process_rename_conf_proto_rawDesc = []byte{
	0x0a, 0x21, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x72, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0x5d, 0x0a,
	0x11, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6e, 0x61, 0x6d,
	0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x5f, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x66, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x42, 0x29, 0x5a, 0x27,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e,
	0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_process_rename_conf_proto_rawDescOnce sync.Once
	file_conf_v1_process_rename_conf_proto_rawDescData = file_conf_v1_process_rename_conf_proto_rawDesc
)

func file_conf_v1_process_rename_conf_proto_rawDescGZIP() []byte {
	file_conf_v1_process_rename_conf_proto_rawDescOnce.Do(func() {
		file_conf_v1_process_rename_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_process_rename_conf_proto_rawDescData)
	})
	return file_conf_v1_process_rename_conf_proto_rawDescData
}

var file_conf_v1_process_rename_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_conf_v1_process_rename_conf_proto_goTypes = []interface{}{
	(*ProcessRenameConf)(nil), // 0: api.conf.ProcessRenameConf
}
var file_conf_v1_process_rename_conf_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_conf_v1_process_rename_conf_proto_init() }
func file_conf_v1_process_rename_conf_proto_init() {
	if File_conf_v1_process_rename_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_process_rename_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessRenameConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_process_rename_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_process_rename_conf_proto_goTypes,
		DependencyIndexes: file_conf_v1_process_rename_conf_proto_depIdxs,
		MessageInfos:      file_conf_v1_process_rename_conf_proto_msgTypes,
	}.Build()
	File_conf_v1_process_rename_conf_proto = out.File
	file_conf_v1_process_rename_conf_proto_rawDesc = nil
	file_conf_v1_process_rename_conf_proto_goTypes = nil
	file_conf_v1_process_rename_conf_proto_depIdxs = nil
}
