// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbCorp(db *gorm.DB, opts ...gen.DOOption) tbCorp {
	_tbCorp := tbCorp{}

	_tbCorp.tbCorpDo.UseDB(db, opts...)
	_tbCorp.tbCorpDo.UseModel(&model.TbCorp{})

	tableName := _tbCorp.tbCorpDo.TableName()
	_tbCorp.ALL = field.NewAsterisk(tableName)
	_tbCorp.ID = field.NewString(tableName, "id")
	_tbCorp.Name = field.NewString(tableName, "name")
	_tbCorp.CreatedAt = field.NewTime(tableName, "created_at")
	_tbCorp.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbCorp.fillFieldMap()

	return _tbCorp
}

type tbCorp struct {
	tbCorpDo tbCorpDo

	ALL       field.Asterisk
	ID        field.String // 租户id
	Name      field.String // 租户名
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (t tbCorp) Table(newTableName string) *tbCorp {
	t.tbCorpDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbCorp) As(alias string) *tbCorp {
	t.tbCorpDo.DO = *(t.tbCorpDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbCorp) updateTableName(table string) *tbCorp {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbCorp) WithContext(ctx context.Context) *tbCorpDo { return t.tbCorpDo.WithContext(ctx) }

func (t tbCorp) TableName() string { return t.tbCorpDo.TableName() }

func (t tbCorp) Alias() string { return t.tbCorpDo.Alias() }

func (t *tbCorp) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbCorp) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 4)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbCorp) clone(db *gorm.DB) tbCorp {
	t.tbCorpDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbCorp) replaceDB(db *gorm.DB) tbCorp {
	t.tbCorpDo.ReplaceDB(db)
	return t
}

type tbCorpDo struct{ gen.DO }

func (t tbCorpDo) Debug() *tbCorpDo {
	return t.withDO(t.DO.Debug())
}

func (t tbCorpDo) WithContext(ctx context.Context) *tbCorpDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbCorpDo) ReadDB() *tbCorpDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbCorpDo) WriteDB() *tbCorpDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbCorpDo) Session(config *gorm.Session) *tbCorpDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbCorpDo) Clauses(conds ...clause.Expression) *tbCorpDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbCorpDo) Returning(value interface{}, columns ...string) *tbCorpDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbCorpDo) Not(conds ...gen.Condition) *tbCorpDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbCorpDo) Or(conds ...gen.Condition) *tbCorpDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbCorpDo) Select(conds ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbCorpDo) Where(conds ...gen.Condition) *tbCorpDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbCorpDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbCorpDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbCorpDo) Order(conds ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbCorpDo) Distinct(cols ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbCorpDo) Omit(cols ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbCorpDo) Join(table schema.Tabler, on ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbCorpDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbCorpDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbCorpDo) Group(cols ...field.Expr) *tbCorpDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbCorpDo) Having(conds ...gen.Condition) *tbCorpDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbCorpDo) Limit(limit int) *tbCorpDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbCorpDo) Offset(offset int) *tbCorpDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbCorpDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbCorpDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbCorpDo) Unscoped() *tbCorpDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbCorpDo) Create(values ...*model.TbCorp) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbCorpDo) CreateInBatches(values []*model.TbCorp, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbCorpDo) Save(values ...*model.TbCorp) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbCorpDo) First() (*model.TbCorp, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCorp), nil
	}
}

func (t tbCorpDo) Take() (*model.TbCorp, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCorp), nil
	}
}

func (t tbCorpDo) Last() (*model.TbCorp, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCorp), nil
	}
}

func (t tbCorpDo) Find() ([]*model.TbCorp, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbCorp), err
}

func (t tbCorpDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbCorp, err error) {
	buf := make([]*model.TbCorp, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbCorpDo) FindInBatches(result *[]*model.TbCorp, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbCorpDo) Attrs(attrs ...field.AssignExpr) *tbCorpDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbCorpDo) Assign(attrs ...field.AssignExpr) *tbCorpDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbCorpDo) Joins(fields ...field.RelationField) *tbCorpDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbCorpDo) Preload(fields ...field.RelationField) *tbCorpDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbCorpDo) FirstOrInit() (*model.TbCorp, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCorp), nil
	}
}

func (t tbCorpDo) FirstOrCreate() (*model.TbCorp, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCorp), nil
	}
}

func (t tbCorpDo) FindByPage(offset int, limit int) (result []*model.TbCorp, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbCorpDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbCorpDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbCorpDo) Delete(models ...*model.TbCorp) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbCorpDo) withDO(do gen.Dao) *tbCorpDo {
	t.DO = *do.(*gen.DO)
	return t
}
