# 升级流程串行化实现方案

## 问题背景

在原有的升级系统中，存在一个潜在的竞态条件：
- Sidecar负责下载升级包（可能需要30分钟）
- LightSentinel负责安装升级包（安装开始1秒后会杀死Sidecar进程）
- 如果LightSentinel在Sidecar还在下载时开始安装，会导致下载中断和升级失败

## 解决方案

采用**升级流程串行化**方案，确保同一时间只有一个完整的升级流程（检查→下载→安装）在执行。

### 核心机制

使用单一的升级锁文件 `upgrade_lock.json` 来协调整个升级流程：

```json
{
  "status": "downloading",
  "version": "1.2.3",
  "start_time": 1640995200,
  "last_update": 1640995800,
  "process_id": 12345,
  "install_start_time": 1640996000
}
```

### 状态流转

1. **checking** - 正在检查升级
2. **downloading** - 正在下载升级包
3. **installing** - 正在安装升级包
4. **completed** - 升级完成（可清理）

## 实现细节

### Sidecar端实现

#### 文件修改
- `internal/constants/api_endpoints.go` - 添加升级锁文件常量
- `internal/upgrade/upgrade.go` - 实现升级锁管理逻辑

#### 关键函数
- `isUpgradeInProgress()` - 检查是否有升级在进行
- `createUpgradeLock()` - 创建升级锁
- `updateUpgradeLockStatus()` - 更新锁状态
- `cleanupUpgradeLock()` - 清理锁文件

#### 流程控制
```go
func doFullUpgradeCheck() {
    // 0. 检查是否已有升级流程在进行
    if isUpgradeInProgress() {
        return // 跳过本次检查
    }
    
    // 1. 检查升级
    checkInfo, err := checkUpgrade()
    
    // 2. 如果需要升级
    if checkInfo.UpgradeEnabled {
        // 创建升级锁
        createUpgradeLock(checkInfo.UpgradeVersion, "checking")
        
        // 更新状态为下载中
        updateUpgradeLockStatus("downloading")
        
        // 执行下载
        downloadSuccess := performUpgradeDownload(checkInfo)
        
        // 如果是强制升级，执行安装
        if checkInfo.ForceUpgrade {
            updateUpgradeLockStatus("installing")
            performUpgradeInstallation(checkInfo)
            cleanupUpgradeLock()
        } else {
            updateUpgradeLockStatus("completed")
        }
    }
}
```

### LightSentinel端实现

#### 文件修改
- `src/tasks/AsecInstallTask.cpp` - 添加升级锁状态更新
- `src/tasks/AsecInstallTask.hpp` - 添加函数声明

#### 关键函数
- `updateUpgradeLockStatus()` - 更新锁状态为"installing"
- `cleanupUpgradeLock()` - 安装完成后清理锁

#### 安装流程
```cpp
void AsecInstallTask::doTask() {
    // 安装开始时更新锁状态
    updateUpgradeLockStatus("installing");
    
    // 执行安装逻辑
    // ...
    
    // 安装完成后清理锁
    cleanupUpgradeLock();
}
```

## 安全机制

### 超时处理
- 锁文件超过2小时未更新视为过期，自动清理
- 防止进程异常退出导致的死锁

### 原子操作
- 使用临时文件+重命名确保文件写入的原子性
- 避免并发读写导致的数据损坏

### 错误恢复
- 无效的锁文件自动删除并重新创建
- 异常情况下的锁文件清理机制

## 测试验证

创建了 `test_upgrade_lock.go` 测试程序，验证：
- 锁文件的创建和读取
- 状态更新的原子性
- 锁文件的清理机制

测试结果：所有功能正常工作。

## 部署说明

1. **编译验证**：Sidecar和LightSentinel代码均编译通过
2. **向后兼容**：不影响现有功能，只是增加了协调机制
3. **配置要求**：无需额外配置，使用默认的config目录

## 优势

1. **简单可靠**：单一锁文件，逻辑清晰
2. **自动恢复**：异常情况下的自动清理机制
3. **性能友好**：只在升级时才创建锁文件
4. **易于调试**：锁文件内容可直接查看，便于问题排查

这个方案彻底解决了升级过程中的竞态条件问题，确保升级流程的可靠性和一致性。
