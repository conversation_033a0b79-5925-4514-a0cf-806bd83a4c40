// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbWxUser(db *gorm.DB, opts ...gen.DOOption) tbWxUser {
	_tbWxUser := tbWxUser{}

	_tbWxUser.tbWxUserDo.UseDB(db, opts...)
	_tbWxUser.tbWxUserDo.UseModel(&model.TbWxUser{})

	tableName := _tbWxUser.tbWxUserDo.TableName()
	_tbWxUser.ALL = field.NewAsterisk(tableName)
	_tbWxUser.WxCorpID = field.NewString(tableName, "wx_corp_id")
	_tbWxUser.LocalRootGroupID = field.NewString(tableName, "local_root_group_id")
	_tbWxUser.MainDepartment = field.NewInt64(tableName, "main_department")
	_tbWxUser.Userid = field.NewString(tableName, "userid")
	_tbWxUser.Name = field.NewString(tableName, "name")
	_tbWxUser.CreatedAt = field.NewTime(tableName, "created_at")
	_tbWxUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbWxUser.LocalUserID = field.NewString(tableName, "local_user_id")

	_tbWxUser.fillFieldMap()

	return _tbWxUser
}

type tbWxUser struct {
	tbWxUserDo tbWxUserDo

	ALL              field.Asterisk
	WxCorpID         field.String
	LocalRootGroupID field.String
	MainDepartment   field.Int64
	Userid           field.String
	Name             field.String
	CreatedAt        field.Time
	UpdatedAt        field.Time
	LocalUserID      field.String

	fieldMap map[string]field.Expr
}

func (t tbWxUser) Table(newTableName string) *tbWxUser {
	t.tbWxUserDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbWxUser) As(alias string) *tbWxUser {
	t.tbWxUserDo.DO = *(t.tbWxUserDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbWxUser) updateTableName(table string) *tbWxUser {
	t.ALL = field.NewAsterisk(table)
	t.WxCorpID = field.NewString(table, "wx_corp_id")
	t.LocalRootGroupID = field.NewString(table, "local_root_group_id")
	t.MainDepartment = field.NewInt64(table, "main_department")
	t.Userid = field.NewString(table, "userid")
	t.Name = field.NewString(table, "name")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.LocalUserID = field.NewString(table, "local_user_id")

	t.fillFieldMap()

	return t
}

func (t *tbWxUser) WithContext(ctx context.Context) *tbWxUserDo { return t.tbWxUserDo.WithContext(ctx) }

func (t tbWxUser) TableName() string { return t.tbWxUserDo.TableName() }

func (t tbWxUser) Alias() string { return t.tbWxUserDo.Alias() }

func (t *tbWxUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbWxUser) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 8)
	t.fieldMap["wx_corp_id"] = t.WxCorpID
	t.fieldMap["local_root_group_id"] = t.LocalRootGroupID
	t.fieldMap["main_department"] = t.MainDepartment
	t.fieldMap["userid"] = t.Userid
	t.fieldMap["name"] = t.Name
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["local_user_id"] = t.LocalUserID
}

func (t tbWxUser) clone(db *gorm.DB) tbWxUser {
	t.tbWxUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbWxUser) replaceDB(db *gorm.DB) tbWxUser {
	t.tbWxUserDo.ReplaceDB(db)
	return t
}

type tbWxUserDo struct{ gen.DO }

func (t tbWxUserDo) Debug() *tbWxUserDo {
	return t.withDO(t.DO.Debug())
}

func (t tbWxUserDo) WithContext(ctx context.Context) *tbWxUserDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbWxUserDo) ReadDB() *tbWxUserDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbWxUserDo) WriteDB() *tbWxUserDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbWxUserDo) Session(config *gorm.Session) *tbWxUserDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbWxUserDo) Clauses(conds ...clause.Expression) *tbWxUserDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbWxUserDo) Returning(value interface{}, columns ...string) *tbWxUserDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbWxUserDo) Not(conds ...gen.Condition) *tbWxUserDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbWxUserDo) Or(conds ...gen.Condition) *tbWxUserDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbWxUserDo) Select(conds ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbWxUserDo) Where(conds ...gen.Condition) *tbWxUserDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbWxUserDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbWxUserDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbWxUserDo) Order(conds ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbWxUserDo) Distinct(cols ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbWxUserDo) Omit(cols ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbWxUserDo) Join(table schema.Tabler, on ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbWxUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbWxUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbWxUserDo) Group(cols ...field.Expr) *tbWxUserDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbWxUserDo) Having(conds ...gen.Condition) *tbWxUserDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbWxUserDo) Limit(limit int) *tbWxUserDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbWxUserDo) Offset(offset int) *tbWxUserDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbWxUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbWxUserDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbWxUserDo) Unscoped() *tbWxUserDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbWxUserDo) Create(values ...*model.TbWxUser) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbWxUserDo) CreateInBatches(values []*model.TbWxUser, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbWxUserDo) Save(values ...*model.TbWxUser) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbWxUserDo) First() (*model.TbWxUser, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxUser), nil
	}
}

func (t tbWxUserDo) Take() (*model.TbWxUser, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxUser), nil
	}
}

func (t tbWxUserDo) Last() (*model.TbWxUser, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxUser), nil
	}
}

func (t tbWxUserDo) Find() ([]*model.TbWxUser, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbWxUser), err
}

func (t tbWxUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbWxUser, err error) {
	buf := make([]*model.TbWxUser, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbWxUserDo) FindInBatches(result *[]*model.TbWxUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbWxUserDo) Attrs(attrs ...field.AssignExpr) *tbWxUserDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbWxUserDo) Assign(attrs ...field.AssignExpr) *tbWxUserDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbWxUserDo) Joins(fields ...field.RelationField) *tbWxUserDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbWxUserDo) Preload(fields ...field.RelationField) *tbWxUserDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbWxUserDo) FirstOrInit() (*model.TbWxUser, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxUser), nil
	}
}

func (t tbWxUserDo) FirstOrCreate() (*model.TbWxUser, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxUser), nil
	}
}

func (t tbWxUserDo) FindByPage(offset int, limit int) (result []*model.TbWxUser, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbWxUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbWxUserDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbWxUserDo) Delete(models ...*model.TbWxUser) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbWxUserDo) withDO(do gen.Dao) *tbWxUserDo {
	t.DO = *do.(*gen.DO)
	return t
}
