package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/url"
	"strings"

	oidcPb "asdsec.com/asec/platform/api/auth/v1/oidc"
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/golang-jwt/jwt/v4"
	"google.golang.org/protobuf/types/known/structpb"
)

type OIDCService struct {
	oidcPb.UnimplementedOIDCServer
	oidc *biz.OIDCUsecase
	auth *biz.AuthUsecase
	log  *log.Helper
}

func NewOIDCService(oidc *biz.OIDCUsecase, auth *biz.AuthUsecase, logger log.Logger) *OIDCService {
	return &OIDCService{
		oidc: oidc,
		auth: auth,
		log:  log.NewHelper(logger),
	}
}

// Authorize OIDC授权端点
func (s *OIDCService) Authorize(ctx context.Context, req *oidcPb.AuthorizeRequest) (*oidcPb.AuthorizeReply, error) {
	// 1. 验证必需参数
	if req.ResponseType != dto.ResponseTypeCode {
		return nil, s.buildErrorRedirect(ctx, req.RedirectUri, req.State, dto.ErrUnsupportedResponseType, "不支持的响应类型")
	}

	if req.ClientId == "" {
		return nil, s.buildErrorRedirect(ctx, req.RedirectUri, req.State, dto.ErrInvalidRequest, "客户端ID不能为空")
	}

	// 2. 验证客户端
	client, err := s.oidc.ValidateClient(ctx, req.ClientId, "")
	if err != nil {
		// 客户端验证失败时，不能信任重定向URI，使用空字符串
		return s.handleErrorResponse(ctx, "", req.State, dto.ErrUnauthorizedClient, "无效的客户端", "提供的客户端ID不存在或未经授权")
	}

	// 3. 验证重定向URI
	if err := s.oidc.ValidateRedirectURI(client, req.RedirectUri); err != nil {
		// 重定向URI验证失败时，不能信任重定向URI，使用空字符串
		return s.handleErrorResponse(ctx, "", req.State, dto.ErrInvalidRequest, "非法的重定向URI", "重定向URI与应用配置的回调URI不匹配")
	}

	// 4. 验证scope
	_, err = s.oidc.ValidateScope(req.Scope)
	if err != nil {
		// scope验证失败，此时客户端和重定向URI都已验证通过，可以安全重定向
		return nil, s.buildErrorRedirectWithReason(ctx, req.RedirectUri, req.State, dto.ErrInvalidScope, "无效的权限范围", "请求的权限范围超出了客户端允许的范围")
	}

	// 5. 验证PKCE
	if err := s.oidc.ValidatePKCE(req.CodeChallenge, req.CodeChallengeMethod); err != nil {
		// PKCE验证失败，此时客户端和重定向URI都已验证通过，可以安全重定向
		return nil, s.buildErrorRedirectWithReason(ctx, req.RedirectUri, req.State, dto.ErrInvalidRequest, "无效的PKCE参数", "代码挑战参数格式不正确或方法不支持")
	}

	// 6. 检查用户是否已登录
	userID, err := s.getCurrentUserID(ctx)
	if err != nil || userID == "" {
		// 用户未登录，重定向到登录页面
		// 登录成功后会通过 oidc_redirect 参数回到当前OIDC授权端点
		// s.log.Debugf("用户未登录，重定向到登录页面进行OIDC授权流程，client_id: %s", req.ClientId)

		// 检查是否是带Authorization头的AJAX请求
		if s.hasAuthorizationHeader(ctx) {
			// AJAX请求，返回JSON格式的登录URL
			loginURL := s.buildLoginURL(ctx, req)
			return &oidcPb.AuthorizeReply{
				RedirectUrl: loginURL,
			}, nil
		} else {
			// 普通浏览器请求，返回302重定向到登录页面
			loginURL := s.buildLoginURL(ctx, req)
			return nil, errors.New(302, "Found", loginURL)
		}
	}

	// 用户已登录，记录日志并继续OIDC授权流程
	// s.log.Debugf("用户已登录(userID: %s)，继续OIDC授权流程，client_id: %s", userID, req.ClientId)

	// 7. 验证用户是否有访问该应用的权限
	hasPermission, err := s.validateUserPermission(ctx, req.ClientId, userID)
	if err != nil {
		s.log.Errorf("验证用户权限失败: %v", err)
		return nil, s.buildErrorRedirect(ctx, req.RedirectUri, req.State, dto.ErrServerError, "权限验证失败")
	}
	if !hasPermission {
		s.log.Warnf("用户 %s 无权访问客户端 %s", userID, req.ClientId)
		// 权限不足时，使用空字符串重定向URI，显示我们自己的错误页面
		return s.handleErrorResponse(ctx, "", req.State, dto.ErrAccessDenied, "访问被拒绝", "您没有权限访问此应用")
	}

	// 8. 生成授权码
	authReq := &biz.AuthorizeRequest{
		ResponseType:        req.ResponseType,
		ClientID:            req.ClientId,
		RedirectURI:         req.RedirectUri,
		Scope:               req.Scope,
		State:               req.State,
		CodeChallenge:       req.CodeChallenge,
		CodeChallengeMethod: req.CodeChallengeMethod,
		Nonce:               req.Nonce,
	}

	authCode, err := s.oidc.GenerateAuthorizationCode(ctx, authReq, userID)
	if err != nil {
		s.log.Errorf("生成授权码失败: %v", err)
		return nil, s.buildErrorRedirect(ctx, req.RedirectUri, req.State, dto.ErrServerError, "服务器内部错误")
	}

	// 9. 构建重定向URL
	redirectURL := s.buildSuccessRedirect(req.RedirectUri, authCode.Code, req.State)

	// 检查是否是带Authorization头的AJAX请求
	if s.hasAuthorizationHeader(ctx) {
		// AJAX请求，返回JSON格式的重定向URL
		return &oidcPb.AuthorizeReply{
			RedirectUrl: redirectURL,
		}, nil
	} else {
		// 普通浏览器请求，返回302重定向
		return nil, errors.New(302, "Found", redirectURL)
	}
}

// Token OIDC令牌端点
func (s *OIDCService) Token(ctx context.Context, req *oidcPb.TokenRequest) (*oidcPb.TokenReply, error) {
	// 1. 验证grant_type
	if req.GrantType != dto.GrantTypeAuthCode {
		return nil, errors.BadRequest(dto.ErrUnsupportedGrantType, "不支持的授权类型")
	}

	// 2. 验证客户端
	_, err := s.oidc.ValidateClient(ctx, req.ClientId, req.ClientSecret)
	if err != nil {
		return nil, errors.BadRequest(dto.ErrInvalidClient, "客户端认证失败")
	}

	// 3. 验证必需参数
	if req.Code == "" {
		return nil, errors.BadRequest(dto.ErrInvalidRequest, "授权码不能为空")
	}
	if req.RedirectUri == "" {
		return nil, errors.BadRequest(dto.ErrInvalidRequest, "重定向URI不能为空")
	}

	// 4. 交换令牌
	tokenReq := &biz.TokenRequest{
		GrantType:    req.GrantType,
		ClientID:     req.ClientId,
		ClientSecret: req.ClientSecret,
		Code:         req.Code,
		RedirectURI:  req.RedirectUri,
		CodeVerifier: req.CodeVerifier,
	}

	tokenResp, err := s.oidc.ExchangeCodeForTokens(ctx, tokenReq)
	if err != nil {
		s.log.Errorf("交换令牌失败: %v", err)
		return nil, err
	}

	return &oidcPb.TokenReply{
		AccessToken:  tokenResp.AccessToken,
		TokenType:    tokenResp.TokenType,
		ExpiresIn:    tokenResp.ExpiresIn,
		RefreshToken: tokenResp.RefreshToken,
		IdToken:      tokenResp.IDToken,
		Scope:        tokenResp.Scope,
	}, nil
}

// UserInfo OIDC用户信息端点
func (s *OIDCService) UserInfo(ctx context.Context, req *oidcPb.UserInfoRequest) (*oidcPb.UserInfoReply, error) {
	// 1. 从请求头获取访问令牌
	accessToken := req.AccessToken
	if accessToken == "" {
		// 尝试从Authorization头获取
		if tr, ok := transport.FromServerContext(ctx); ok {
			if httpTr, ok := tr.(interface{ RequestHeader() transport.Header }); ok {
				auth := httpTr.RequestHeader().Get("Authorization")
				if strings.HasPrefix(auth, "Bearer ") {
					accessToken = strings.TrimPrefix(auth, "Bearer ")
				}
			}
		}
	}

	if accessToken == "" {
		return nil, errors.Unauthorized("invalid_token", "访问令牌缺失")
	}

	// 2. 获取用户信息
	userInfo, err := s.oidc.GetUserInfo(ctx, accessToken)
	if err != nil {
		s.log.Errorf("获取用户信息失败: %v", err)
		return nil, err
	}

	// 3. 构建响应
	addressStruct, err := structpb.NewStruct(userInfo.Address)
	if err != nil {
		s.log.Errorf("构建address结构失败: %v", err)
		addressStruct = nil
	}

	return &oidcPb.UserInfoReply{
		Sub:                 userInfo.Sub,
		Name:                userInfo.Name,
		Nickname:            userInfo.Nickname,
		PreferredUsername:   userInfo.PreferredUsername,
		Picture:             userInfo.Picture,
		Email:               userInfo.Email,
		EmailVerified:       userInfo.EmailVerified,
		PhoneNumber:         userInfo.PhoneNumber,
		PhoneNumberVerified: userInfo.PhoneNumberVerified,
		Address:             addressStruct,
		UpdatedAt:           userInfo.UpdatedAt,
	}, nil
}

// Discovery OIDC发现端点
func (s *OIDCService) Discovery(ctx context.Context, req *oidcPb.DiscoveryRequest) (*oidcPb.DiscoveryReply, error) {
	discovery := s.oidc.GetDiscoveryDocument()

	return &oidcPb.DiscoveryReply{
		Issuer:                           discovery.Issuer,
		AuthorizationEndpoint:            discovery.AuthorizationEndpoint,
		TokenEndpoint:                    discovery.TokenEndpoint,
		UserinfoEndpoint:                 discovery.UserinfoEndpoint,
		JwksUri:                          discovery.JwksURI,
		RevocationEndpoint:               discovery.RevocationEndpoint,
		EndSessionEndpoint:               discovery.EndSessionEndpoint,
		ScopesSupported:                  discovery.ScopesSupported,
		ResponseTypesSupported:           discovery.ResponseTypesSupported,
		GrantTypesSupported:              discovery.GrantTypesSupported,
		SubjectTypesSupported:            discovery.SubjectTypesSupported,
		IdTokenSigningAlgValuesSupported: discovery.IDTokenSigningAlgValuesSupported,
		CodeChallengeMethodsSupported:    discovery.CodeChallengeMethodsSupported,
	}, nil
}

// JWKS OIDC JWKS端点
func (s *OIDCService) JWKS(ctx context.Context, req *oidcPb.JWKSRequest) (*oidcPb.JWKSReply, error) {
	jwks := s.oidc.GetJWKS()

	keys := make([]*oidcPb.JWK, len(jwks.Keys))
	for i, key := range jwks.Keys {
		keys[i] = &oidcPb.JWK{
			Kty: key.Kty,
			Use: key.Use,
			Kid: key.Kid,
			Alg: key.Alg,
			N:   key.N,
			E:   key.E,
		}
	}

	return &oidcPb.JWKSReply{
		Keys: keys,
	}, nil
}

// Revoke OIDC令牌撤销端点
func (s *OIDCService) Revoke(ctx context.Context, req *oidcPb.RevokeRequest) (*oidcPb.RevokeReply, error) {
	// 验证客户端
	if req.ClientId == "" || req.ClientSecret == "" {
		return nil, errors.BadRequest(dto.ErrInvalidClient, "客户端认证失败")
	}

	if req.Token == "" {
		return nil, errors.BadRequest(dto.ErrInvalidRequest, "令牌不能为空")
	}

	revokeReq := &biz.RevokeRequest{
		ClientID:      req.ClientId,
		ClientSecret:  req.ClientSecret,
		Token:         req.Token,
		TokenTypeHint: req.TokenTypeHint,
	}

	err := s.oidc.RevokeToken(ctx, revokeReq)
	if err != nil {
		s.log.Errorf("撤销令牌失败: %v", err)
		return &oidcPb.RevokeReply{
			Success: false,
			Message: "撤销失败",
		}, nil
	}

	return &oidcPb.RevokeReply{
		Success: true,
		Message: "撤销成功",
	}, nil
}

// Logout OIDC登出端点
func (s *OIDCService) Logout(ctx context.Context, req *oidcPb.LogoutRequest) (*oidcPb.LogoutReply, error) {
	// TODO: 实现登出逻辑
	// 1. 验证客户端
	// 2. 解析ID令牌获取用户信息
	// 3. 清理会话
	// 4. 构建登出后重定向URL

	redirectURL := req.PostLogoutRedirectUri
	if redirectURL == "" {
		redirectURL = "/"
	}

	if req.State != "" {
		if strings.Contains(redirectURL, "?") {
			redirectURL += "&state=" + url.QueryEscape(req.State)
		} else {
			redirectURL += "?state=" + url.QueryEscape(req.State)
		}
	}

	return &oidcPb.LogoutReply{
		RedirectUrl: redirectURL,
	}, nil
}

// 辅助方法

// handleErrorResponse 统一处理错误响应，根据请求类型返回JSON或302重定向
func (s *OIDCService) handleErrorResponse(ctx context.Context, redirectURI, state, errorCode, errorDesc, errorReason string) (*oidcPb.AuthorizeReply, error) {
	if s.hasAuthorizationHeader(ctx) {
		// AJAX请求，返回JSON格式的错误页面URL
		baseURL := s.getBaseURL(ctx)
		errorURL := fmt.Sprintf("%s/#/error?code=%s&message=%s",
			baseURL,
			url.QueryEscape(errorCode),
			url.QueryEscape(s.encodeURLSafeBase64(errorDesc)))

		// 添加错误原因（如果有的话）
		if errorReason != "" {
			errorURL += "&errorreason=" + url.QueryEscape(s.encodeURLSafeBase64(errorReason))
		}

		return &oidcPb.AuthorizeReply{
			RedirectUrl: errorURL,
		}, nil
	} else {
		// 普通浏览器请求，返回302重定向
		return nil, s.buildErrorRedirectWithReason(ctx, redirectURI, state, errorCode, errorDesc, errorReason)
	}
}

// encodeURLSafeBase64 将字符串编码为URL安全的base64
func (s *OIDCService) encodeURLSafeBase64(text string) string {
	if text == "" {
		return ""
	}
	// 使用标准base64编码，然后转换为URL-safe格式
	encoded := base64.StdEncoding.EncodeToString([]byte(text))
	// 替换字符：+ → -, / → _, 移除padding
	encoded = strings.ReplaceAll(encoded, "+", "-")
	encoded = strings.ReplaceAll(encoded, "/", "_")
	encoded = strings.TrimRight(encoded, "=")
	return encoded
}

// getCurrentUserID 从当前请求上下文获取用户ID
func (s *OIDCService) getCurrentUserID(ctx context.Context) (string, error) {
	// 尝试从JWT认证中间件获取用户信息（如果已应用认证中间件）
	claims, ok := common.GetClaim(ctx)
	if ok && claims != nil {
		// 从JWT claims中获取用户ID
		if jwtClaims, ok := claims.(jwt.MapClaims); ok {
			if sub, exists := jwtClaims["sub"]; exists {
				if userID, ok := sub.(string); ok && userID != "" {
					return userID, nil
				}
			}
		}
	}

	// 尝试从HTTP请求头获取Authorization Bearer token
	if tr, ok := transport.FromServerContext(ctx); ok {
		if httpTr, ok := tr.(interface{ RequestHeader() transport.Header }); ok {
			auth := httpTr.RequestHeader().Get("Authorization")
			if strings.HasPrefix(auth, "Bearer ") {
				token := strings.TrimPrefix(auth, "Bearer ")
				// 使用现有的auth业务逻辑验证JWT token
				claims, err := s.auth.ParseJwtToken(ctx, dto.AccessTokenTyp, token)
				if err != nil {
					s.log.Debugf("JWT token validation failed: %v", err)
				} else if claims != nil && claims.Subject != "" {
					return claims.Subject, nil
				}
			}

			// 尝试从Cookie中获取token（页面跳转时会自动携带Cookie）
			cookies := httpTr.RequestHeader().Get("Cookie")
			if cookies != "" {
				cookieList := strings.Split(cookies, ";")
				for _, cookie := range cookieList {
					parts := strings.Split(cookie, "=")
					if len(parts) >= 2 {
						cookieName := strings.TrimSpace(parts[0])
						if cookieName == "asec_token" { // 使用与auth.go相同的Cookie名称
							cookieValue := strings.TrimSpace(parts[1])
							// 使用现有的auth业务逻辑验证JWT token
							claims, err := s.auth.ParseJwtToken(ctx, dto.AccessTokenTyp, cookieValue)
							if err != nil {
								s.log.Debugf("Cookie token validation failed: %v", err)
								continue
							}
							// 从验证成功的claims中获取用户ID
							if claims != nil && claims.Subject != "" {
								return claims.Subject, nil
							}
						}
					}
				}
			}
		}
	}

	// 用户未登录
	return "", errors.Unauthorized("login_required", "需要登录")
}

// hasAuthorizationHeader 检查请求是否包含Authorization头（表示这是AJAX请求）
func (s *OIDCService) hasAuthorizationHeader(ctx context.Context) bool {
	if tr, ok := transport.FromServerContext(ctx); ok {
		if httpTr, ok := tr.(interface{ RequestHeader() transport.Header }); ok {
			auth := httpTr.RequestHeader().Get("Authorization")
			return auth != ""
		}
	}
	return false
}

// buildLoginURL 构建登录页面URL
func (s *OIDCService) buildLoginURL(ctx context.Context, req *oidcPb.AuthorizeRequest) string {
	// 获取当前请求的主机信息
	baseURL := s.getBaseURL(ctx)

	// 构建OIDC授权参数
	oidcParams := url.Values{}
	oidcParams.Set("response_type", req.ResponseType)
	oidcParams.Set("client_id", req.ClientId)
	oidcParams.Set("redirect_uri", req.RedirectUri)
	oidcParams.Set("scope", req.Scope)
	oidcParams.Set("state", req.State)
	if req.CodeChallenge != "" {
		oidcParams.Set("code_challenge", req.CodeChallenge)
		oidcParams.Set("code_challenge_method", req.CodeChallengeMethod)
	}
	if req.Nonce != "" {
		oidcParams.Set("nonce", req.Nonce)
	}

	// 构建完整的OIDC授权URL作为登录后的重定向目标
	oidcAuthURL := "/auth/login/v1/authorize/oidc/authorize?" + oidcParams.Encode()

	// 构建登录页面URL，使用oidc_redirect参数标记这是OIDC流程
	loginParams := url.Values{}
	loginParams.Set("oidc_redirect", oidcAuthURL)

	// 返回完整的URL，包含域名
	loginURL := baseURL + "/#/login?" + loginParams.Encode()
	return loginURL
}

// buildSuccessRedirect 构建成功的重定向URL
func (s *OIDCService) buildSuccessRedirect(redirectURI, code, state string) string {
	u, _ := url.Parse(redirectURI)
	q := u.Query()
	q.Set("code", code)
	if state != "" {
		q.Set("state", state)
	}
	u.RawQuery = q.Encode()
	// s.log.Debugf("构建的成功重定向URL: %s", u.String())
	return u.String()
}

// buildErrorRedirect 构建错误的重定向URL并返回302错误
func (s *OIDCService) buildErrorRedirect(ctx context.Context, redirectURI, state, errorCode, errorDesc string) error {
	return s.buildErrorRedirectWithReason(ctx, redirectURI, state, errorCode, errorDesc, "")
}

// buildErrorRedirectWithReason 构建带详细原因的错误重定向URL
func (s *OIDCService) buildErrorRedirectWithReason(ctx context.Context, redirectURI, state, errorCode, errorDesc, errorReason string) error {
	// s.log.Debugf("构建错误重定向: redirectURI=%s, errorCode=%s, errorDesc=%s, errorReason=%s",
	// 	redirectURI, errorCode, errorDesc, errorReason)

	if redirectURI == "" {
		// 如果没有重定向URI，返回前端错误页面，使用URL-safe base64编码
		baseURL := s.getBaseURL(ctx)
		errorURL := fmt.Sprintf("%s/#/error?code=%s&message=%s",
			baseURL,
			url.QueryEscape(errorCode),
			url.QueryEscape(s.encodeURLSafeBase64(errorDesc)))

		// 添加错误原因（如果有的话）
		if errorReason != "" {
			errorURL += "&errorreason=" + url.QueryEscape(s.encodeURLSafeBase64(errorReason))
		}

		// s.log.Debugf("使用前端错误页面: %s", errorURL)
		return errors.New(302, "Found", errorURL)
	}

	u, err := url.Parse(redirectURI)
	if err != nil {
		// 解析重定向URI失败，返回前端错误页面
		baseURL := s.getBaseURL(ctx)
		errorURL := fmt.Sprintf("%s/#/error?code=%s&message=%s&errorreason=%s",
			baseURL,
			url.QueryEscape(dto.ErrInvalidRequest),
			url.QueryEscape(s.encodeURLSafeBase64("无效URI")),
			url.QueryEscape(s.encodeURLSafeBase64("提供的重定向URI格式不正确")))
		s.log.Debugf("重定向URI解析失败，使用前端错误页面: %s", errorURL)
		return errors.New(302, "Found", errorURL)
	}

	q := u.Query()
	q.Set("error", errorCode)
	q.Set("error_description", errorDesc)
	if state != "" {
		q.Set("state", state)
	}
	u.RawQuery = q.Encode()

	// 检查最终URL长度，如果过长则使用前端错误页面
	finalURL := u.String()
	// s.log.Debugf("构建的最终重定向URL长度: %d, URL: %s", len(finalURL), finalURL)

	if len(finalURL) > 2000 { // 大多数浏览器的URL长度限制
		// URL过长时，使用前端错误页面显示原始错误信息，而不是"URL过长"错误
		baseURL := s.getBaseURL(ctx)
		errorURL := fmt.Sprintf("%s/#/error?code=%s&message=%s",
			baseURL,
			url.QueryEscape(errorCode), // 使用原始错误代码
			url.QueryEscape(s.encodeURLSafeBase64(errorDesc))) // 使用原始错误描述

		// 添加错误原因（如果有的话）
		if errorReason != "" {
			errorURL += "&errorreason=" + url.QueryEscape(s.encodeURLSafeBase64(errorReason))
		}

		if state != "" {
			errorURL += "&state=" + url.QueryEscape(state)
		}
		// s.log.Warnf("重定向URL过长 (%d 字符)，使用前端错误页面显示原始错误: %s", len(finalURL), errorURL)
		return errors.New(302, "Found", errorURL)
	}

	// s.log.Debugf("使用正常重定向: %s", finalURL)
	return errors.New(302, "Found", finalURL)
}

// getBaseURL 获取当前服务的基础URL
func (s *OIDCService) getBaseURL(ctx context.Context) string {
	// 使用common包的现有函数获取scheme和host
	scheme := common.GetClientScheme(ctx)
	host := common.GetClientHostPortWithConfig(ctx)

	// s.log.Debugf("获取到的scheme: %s, host: %s", scheme, host)

	if host != "" {
		baseURL := scheme + "://" + host
		// s.log.Debugf("构建的baseURL: %s", baseURL)
		return baseURL
	}

	// 如果无法从请求获取，使用OIDC业务逻辑的baseURL
	discovery := s.oidc.GetDiscoveryDocument()
	if discovery.Issuer != "" {
		// s.log.Debugf("使用discovery的Issuer: %s", discovery.Issuer)
		return discovery.Issuer
	}

	// 最后的fallback
	s.log.Warnf("无法从请求上下文或discovery文档获取baseURL，使用fallback URL: https://auth.infogo.com.cn")
	return "https://auth.infogo.com.cn"
}

// validateUserPermission 验证用户是否有访问指定客户端应用的权限
func (s *OIDCService) validateUserPermission(ctx context.Context, clientID, userID string) (bool, error) {
	// 1. 通过ClientID获取OAuth2Client信息，包括关联的AppID
	client, err := s.oidc.GetOAuth2ClientWithAppID(ctx, clientID)
	if err != nil {
		return false, fmt.Errorf("获取客户端信息失败: %w", err)
	}

	// 2. 检查是否有关联的应用ID
	if client.AppID == nil {
		s.log.Warnf("OAuth2客户端 %s 没有关联的应用ID，跳过权限验证", clientID)
		return true, nil // 向后兼容，没有应用ID的客户端允许访问
	}

	// 3. 调用动态策略引擎验证用户权限
	appID := fmt.Sprintf("%d", *client.AppID)
	hasPermission, err := s.oidc.ValidateUserAppPermission(ctx, userID, appID)
	if err != nil {
		s.log.Errorf("验证用户应用权限失败: userID=%s, appID=%s, error=%v", userID, appID, err)
		// 权限验证失败时，出于安全考虑应该拒绝访问
		return false, fmt.Errorf("权限验证失败: %w", err)
	}

	s.log.Debugf("用户权限验证结果: clientID=%s, userID=%s, appID=%s, hasPermission=%v",
		clientID, userID, appID, hasPermission)

	return hasPermission, nil
}
