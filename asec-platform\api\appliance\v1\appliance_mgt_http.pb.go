// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             v3.20.0
// source: appliance/v1/appliance_mgt.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationApplianceMgtHeartbeatReport = "/api.appliance.ApplianceMgt/HeartbeatReport"
const OperationApplianceMgtSetUpgradeStatus = "/api.appliance.ApplianceMgt/SetUpgradeStatus"
const OperationApplianceMgtTaskReport = "/api.appliance.ApplianceMgt/TaskReport"

type ApplianceMgtHTTPServer interface {
	// HeartbeatReport 任务汇报接口
	HeartbeatReport(context.Context, *HeartbeatReportReq) (*HeartbeatReportRes, error)
	// SetUpgradeStatus http接口
	// todo 后续将console中的 SetAgentUpgradeStatus 接口迁移过来
	SetUpgradeStatus(context.Context, *SetUpgradeStatusReq) (*SetUpgradeStatusRes, error)
	// TaskReport 任务汇报接口
	TaskReport(context.Context, *TaskReportReq) (*TaskReportResp, error)
}

func RegisterApplianceMgtHTTPServer(s *http.Server, srv ApplianceMgtHTTPServer) {
	r := s.Route("/")
	r.POST("/appliance/v1/agent/upgrade_status", _ApplianceMgt_SetUpgradeStatus0_HTTP_Handler(srv))
	r.POST("/appliance/v1/task/report", _ApplianceMgt_TaskReport0_HTTP_Handler(srv))
	r.POST("/appliance/v1/heartbeat", _ApplianceMgt_HeartbeatReport0_HTTP_Handler(srv))
}

func _ApplianceMgt_SetUpgradeStatus0_HTTP_Handler(srv ApplianceMgtHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetUpgradeStatusReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplianceMgtSetUpgradeStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetUpgradeStatus(ctx, req.(*SetUpgradeStatusReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SetUpgradeStatusRes)
		return ctx.Result(200, reply)
	}
}

func _ApplianceMgt_TaskReport0_HTTP_Handler(srv ApplianceMgtHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TaskReportReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplianceMgtTaskReport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TaskReport(ctx, req.(*TaskReportReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TaskReportResp)
		return ctx.Result(200, reply)
	}
}

func _ApplianceMgt_HeartbeatReport0_HTTP_Handler(srv ApplianceMgtHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HeartbeatReportReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationApplianceMgtHeartbeatReport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.HeartbeatReport(ctx, req.(*HeartbeatReportReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HeartbeatReportRes)
		return ctx.Result(200, reply)
	}
}

type ApplianceMgtHTTPClient interface {
	HeartbeatReport(ctx context.Context, req *HeartbeatReportReq, opts ...http.CallOption) (rsp *HeartbeatReportRes, err error)
	SetUpgradeStatus(ctx context.Context, req *SetUpgradeStatusReq, opts ...http.CallOption) (rsp *SetUpgradeStatusRes, err error)
	TaskReport(ctx context.Context, req *TaskReportReq, opts ...http.CallOption) (rsp *TaskReportResp, err error)
}

type ApplianceMgtHTTPClientImpl struct {
	cc *http.Client
}

func NewApplianceMgtHTTPClient(client *http.Client) ApplianceMgtHTTPClient {
	return &ApplianceMgtHTTPClientImpl{client}
}

func (c *ApplianceMgtHTTPClientImpl) HeartbeatReport(ctx context.Context, in *HeartbeatReportReq, opts ...http.CallOption) (*HeartbeatReportRes, error) {
	var out HeartbeatReportRes
	pattern := "/appliance/v1/heartbeat"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplianceMgtHeartbeatReport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplianceMgtHTTPClientImpl) SetUpgradeStatus(ctx context.Context, in *SetUpgradeStatusReq, opts ...http.CallOption) (*SetUpgradeStatusRes, error) {
	var out SetUpgradeStatusRes
	pattern := "/appliance/v1/agent/upgrade_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplianceMgtSetUpgradeStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ApplianceMgtHTTPClientImpl) TaskReport(ctx context.Context, in *TaskReportReq, opts ...http.CallOption) (*TaskReportResp, error) {
	var out TaskReportResp
	pattern := "/appliance/v1/task/report"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationApplianceMgtTaskReport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
