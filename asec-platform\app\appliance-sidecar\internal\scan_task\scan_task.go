package scan_task

import (
	v1 "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"context"
	"fmt"
	"google.golang.org/grpc"
	"strconv"
	"strings"
	"sync"
	"time"
)

const taskQuerySql = `select 
		   tb_task_result.id,
		   tb_task_result.task_id,
		   task_status,
		   host_name,
		   L1,
		   L2,
		   L3,
		   L4,
		   scan_count,
		   CASE WHEN GROUP_CONCAT(ttd.file_result_id) IS NULL THEN '' ELSE GROUP_CONCAT(ttd.file_result_id) END AS file_result_ids 
		from tb_task_result
		left join tb_task_details ttd on ttd.task_id = tb_task_result.task_id
		where tb_task_result.id > ? and task_status in ('finished','stopped')
		GROUP by tb_task_result.task_id
		limit ?`

const fileResultQuerySql = `select 
			id,
			file_path,
			file_sensit_info,
			trace_ids,
			sub_trace_ids,
			basic_src_paths,
			extension_name,
			md5,
			category_id,
			file_type_suffix
		from tb_file_result 
		where id in %s and file_sensit_info != ''`

func SendScanTask(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   doSendScanTask,
		RunType:      common.SimpleSend,
		WaitSecond:   common.Duration,
		RandomOffset: 2,
	}
	go cleanScanTask(8, 500000, 300000)
	common.Send(param)
}

func doSendScanTask(conn *grpc.ClientConn, ctx context.Context) error {
	if !common.DdrSwitch {
		return nil
	}
	stmt, err := global.TaskSqliteClient.Prepare(taskQuerySql)
	if stmt != nil {
		defer stmt.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("doSendScanTask SqliteClient Prepare:%v", err)
		return err
	}
	currentOffset, err := common.GetOffset(common.ScanTaskUploadOffset)
	if err != nil {
		global.Logger.Sugar().Errorln("get current offset for scan task err:%v", err)
		return err
	}

	rows, err := stmt.Query(currentOffset, common.Limit)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("stmt Query scan task err:%v", err)
		return err
	}

	tasks := v1.UpsetScanTaskReq{}
	//记录本次上报的offset
	var nextOffset = currentOffset
	userInfo := user.GetUserInfo()
	applianceId := strconv.FormatUint(global.ApplianceID, 10)
	taskFileMap := make(map[string]string)
	fileRetIds := make([]string, 0)
	for rows.Next() {
		var task v1.ScanTaskResult
		//当前上传扫描任务offset
		var curId int64
		var fileResultIdsStr string
		err = rows.Scan(&curId, &task.TaskId, &task.TaskStatus, &task.AgentName, &task.L1FileCount, &task.L2FileCount, &task.L3FileCount,
			&task.L4FileCount, &task.ScanCount, &fileResultIdsStr)
		if err != nil {
			global.Logger.Sugar().Errorln("doSendTask rows Scan err:%v", err)
		}
		fileResultIds := strings.Split(fileResultIdsStr, ",")
		for _, v := range fileResultIds {
			taskFileMap[v] = task.TaskId
			if v != "" {
				fileRetIds = append(fileRetIds, v)
			}
		}

		task.UserId = userInfo.UserId
		task.UserName = userInfo.UserName
		task.AgentId = applianceId
		tasks.TaskList = append(tasks.TaskList, &task)
		if curId > nextOffset {
			nextOffset = curId
		}
	}
	// 获取扫描结果
	stmtF, err := global.FileResultSqliteClient.Prepare(fmt.Sprintf(fileResultQuerySql, "("+strings.Join(fileRetIds, ",")+")"))
	if stmt != nil {
		defer stmtF.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("doSendScanFile SqliteClient Prepare:%v", err)
		return err
	}
	fRows, err := stmtF.Query()
	if fRows != nil {
		defer fRows.Close()
	}
	scanFileList := v1.CreateScanFileReq{}
	for fRows.Next() {
		var scanFile v1.ScanFile
		//当前上传事件offset
		var curId int64
		err = fRows.Scan(&curId, &scanFile.FilePath, &scanFile.FileSensitiveInfo, &scanFile.TraceIds, &scanFile.SubTraceIds,
			&scanFile.BasicSrcPaths, &scanFile.ExtensionName, &scanFile.Md5, &scanFile.FileCategoryId, &scanFile.FileTypeSuffix)
		if err != nil {
			global.Logger.Sugar().Errorln("doSendScanFile rows Scan err:%v", err)
		}
		scanFile.UserId = userInfo.UserId
		scanFile.AgentId = applianceId
		scanFile.TaskId = taskFileMap[strconv.FormatInt(curId, 10)]
		scanFileList.ScanFile = append(scanFileList.ScanFile, &scanFile)
	}

	if len(scanFileList.ScanFile) > 0 {
		_, err := v1.NewScanTaskClient(conn).CreateScanFile(ctx, &scanFileList)
		if err != nil {
			global.Logger.Sugar().Errorf("task file send close err:%v", err)
			return err
		}
	}

	if len(tasks.TaskList) > 0 {
		_, err := v1.NewScanTaskClient(conn).UpsetScanTask(ctx, &tasks)
		if err != nil {
			global.Logger.Sugar().Errorf("task send close err:%v", err)
		} else {
			// 上报失败不更新offset
			err := common.UpdateOffset(nextOffset, common.ScanTaskUploadOffset)
			if err != nil {
				global.Logger.Sugar().Errorf("task update offset err:%v", err)
			}
			return err
		}
	} else {
		// 没有查询结果时兜底offset. 防止客户端删了表不报事件了
		var res int64
		row := global.TaskSqliteClient.QueryRow("SELECT id from tb_task_result ORDER BY id desc limit 1")
		_ = row.Scan(&res)
		if currentOffset > res {
			err := common.UpdateOffset(res, common.ScanTaskUploadOffset)
			if err != nil {
				global.Logger.Sugar().Errorf("task update offset err:%v", err)
			}
			return err
		}
	}
	return nil
}

// cleanScanTask 定时清理task
//
// cleanHour 扫描间隔
//
// keepMaxCounts 删除阈值
//
// keepCount 保持数量
func cleanScanTask(cleanHour int64, keepMaxCounts int64, keepCount int64) {
	var err error
	for {
		var count int64
		var maxTaskDetailId int64
		var maxFileRetId int64
		if !common.DdrSwitch {
			goto WAIT
		}

		// 当前事件表数量
		count, err = common.SelectOneCount(global.TaskSqliteClient, "select count(1) from tb_task_details")
		if err != nil {
			global.Logger.Sugar().Errorln("cleanTaskFileResult getCurrentCount err:%v", err)
			goto WAIT
		}
		if count < keepMaxCounts {
			// no need del
			goto WAIT
		}

		// 当前最大id
		maxTaskDetailId, err = common.SelectOneCount(global.TaskSqliteClient, "select id from tb_task_details order by id desc limit 1")
		if err != nil {
			global.Logger.Sugar().Errorln("cleanTaskFileResult getCurrentTaskMaxId err:%v", err)
		}

		maxFileRetId, err = common.SelectOneCount(global.FileResultSqliteClient, "select id from tb_file_result order by id desc limit 1")
		if err != nil {
			global.Logger.Sugar().Errorln("cleanTaskFileResult getCurrentFileResultMaxId err:%v", err)
		}

		err = deleteScanTask(maxTaskDetailId, maxFileRetId, keepCount)
		if err != nil {
			global.Logger.Sugar().Errorln("cleanTaskFileResult deleteTaskFileResult err:%v", err)
		}

	WAIT:
		time.Sleep(time.Hour * time.Duration(cleanHour))
	}
}

func deleteScanTask(maxTaskDetailId int64, maxFileRetId int64, maxKeepCount int64) error {
	// 删除数据
	exec, err := global.TaskSqliteClient.Exec("delete from tb_task_details where id<=?", maxTaskDetailId-maxKeepCount)
	if err != nil {
		return err
	}
	affected, err := exec.RowsAffected()
	if err != nil {
		return err
	}
	global.Logger.Sugar().Infof("clean task details counts:%v", affected)

	// 删除数据
	fExec, err := global.FileResultSqliteClient.Exec("delete from tb_file_result where id<=?", maxFileRetId-maxKeepCount)
	if err != nil {
		return err
	}
	fAffected, err := fExec.RowsAffected()
	if err != nil {
		return err
	}

	global.Logger.Sugar().Infof("clean task file result counts:%v", fAffected)
	return nil
}
