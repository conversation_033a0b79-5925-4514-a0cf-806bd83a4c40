// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newTbExternalUser(db *gorm.DB, opts ...gen.DOOption) tbExternalUser {
	_tbExternalUser := tbExternalUser{}

	_tbExternalUser.tbExternalUserDo.UseDB(db, opts...)
	_tbExternalUser.tbExternalUserDo.UseModel(&model.TbExternalUser{})

	tableName := _tbExternalUser.tbExternalUserDo.TableName()
	_tbExternalUser.ALL = field.NewAsterisk(tableName)
	_tbExternalUser.LocalRootGroupID = field.NewString(tableName, "local_root_group_id")
	_tbExternalUser.LocalUserID = field.NewString(tableName, "local_user_id")
	_tbExternalUser.MainDepartment = field.NewString(tableName, "main_department")
	_tbExternalUser.Userid = field.NewString(tableName, "userid")
	_tbExternalUser.Name = field.NewString(tableName, "name")
	_tbExternalUser.NickName = field.NewString(tableName, "nick_name")
	_tbExternalUser.Email = field.NewString(tableName, "email")
	_tbExternalUser.Mobile = field.NewString(tableName, "mobile")
	_tbExternalUser.Status = field.NewBool(tableName, "status")
	_tbExternalUser.CreatedAt = field.NewTime(tableName, "created_at")
	_tbExternalUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbExternalUser.Type = field.NewString(tableName, "type")
	_tbExternalUser.UniqKey = field.NewString(tableName, "uniq_key")
	_tbExternalUser.DisplayName = field.NewString(tableName, "display_name")
	_tbExternalUser.TrueName = field.NewString(tableName, "true_name")

	_tbExternalUser.fillFieldMap()

	return _tbExternalUser
}

type tbExternalUser struct {
	tbExternalUserDo tbExternalUserDo

	ALL              field.Asterisk
	LocalRootGroupID field.String
	LocalUserID      field.String
	MainDepartment   field.String
	Userid           field.String
	Name             field.String
	NickName         field.String
	Email            field.String
	Mobile           field.String
	Status           field.Bool
	CreatedAt        field.Time
	UpdatedAt        field.Time
	Type             field.String
	UniqKey          field.String
	DisplayName      field.String
	TrueName         field.String

	fieldMap map[string]field.Expr
}

func (t tbExternalUser) Table(newTableName string) *tbExternalUser {
	t.tbExternalUserDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbExternalUser) As(alias string) *tbExternalUser {
	t.tbExternalUserDo.DO = *(t.tbExternalUserDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbExternalUser) updateTableName(table string) *tbExternalUser {
	t.ALL = field.NewAsterisk(table)
	t.LocalRootGroupID = field.NewString(table, "local_root_group_id")
	t.LocalUserID = field.NewString(table, "local_user_id")
	t.MainDepartment = field.NewString(table, "main_department")
	t.Userid = field.NewString(table, "userid")
	t.Name = field.NewString(table, "name")
	t.NickName = field.NewString(table, "nick_name")
	t.Email = field.NewString(table, "email")
	t.Mobile = field.NewString(table, "mobile")
	t.Status = field.NewBool(table, "status")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.Type = field.NewString(table, "type")
	t.UniqKey = field.NewString(table, "uniq_key")
	t.DisplayName = field.NewString(table, "display_name")
	t.TrueName = field.NewString(table, "true_name")

	t.fillFieldMap()

	return t
}

func (t *tbExternalUser) WithContext(ctx context.Context) *tbExternalUserDo {
	return t.tbExternalUserDo.WithContext(ctx)
}

func (t tbExternalUser) TableName() string { return t.tbExternalUserDo.TableName() }

func (t tbExternalUser) Alias() string { return t.tbExternalUserDo.Alias() }

func (t *tbExternalUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbExternalUser) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 15)
	t.fieldMap["local_root_group_id"] = t.LocalRootGroupID
	t.fieldMap["local_user_id"] = t.LocalUserID
	t.fieldMap["main_department"] = t.MainDepartment
	t.fieldMap["userid"] = t.Userid
	t.fieldMap["name"] = t.Name
	t.fieldMap["nick_name"] = t.NickName
	t.fieldMap["email"] = t.Email
	t.fieldMap["mobile"] = t.Mobile
	t.fieldMap["status"] = t.Status
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["type"] = t.Type
	t.fieldMap["uniq_key"] = t.UniqKey
	t.fieldMap["display_name"] = t.DisplayName
	t.fieldMap["true_name"] = t.TrueName
}

func (t tbExternalUser) clone(db *gorm.DB) tbExternalUser {
	t.tbExternalUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbExternalUser) replaceDB(db *gorm.DB) tbExternalUser {
	t.tbExternalUserDo.ReplaceDB(db)
	return t
}

type tbExternalUserDo struct{ gen.DO }

func (t tbExternalUserDo) Debug() *tbExternalUserDo {
	return t.withDO(t.DO.Debug())
}

func (t tbExternalUserDo) WithContext(ctx context.Context) *tbExternalUserDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbExternalUserDo) ReadDB() *tbExternalUserDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbExternalUserDo) WriteDB() *tbExternalUserDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbExternalUserDo) Session(config *gorm.Session) *tbExternalUserDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbExternalUserDo) Clauses(conds ...clause.Expression) *tbExternalUserDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbExternalUserDo) Returning(value interface{}, columns ...string) *tbExternalUserDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbExternalUserDo) Not(conds ...gen.Condition) *tbExternalUserDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbExternalUserDo) Or(conds ...gen.Condition) *tbExternalUserDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbExternalUserDo) Select(conds ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbExternalUserDo) Where(conds ...gen.Condition) *tbExternalUserDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbExternalUserDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbExternalUserDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbExternalUserDo) Order(conds ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbExternalUserDo) Distinct(cols ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbExternalUserDo) Omit(cols ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbExternalUserDo) Join(table schema.Tabler, on ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbExternalUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbExternalUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbExternalUserDo) Group(cols ...field.Expr) *tbExternalUserDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbExternalUserDo) Having(conds ...gen.Condition) *tbExternalUserDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbExternalUserDo) Limit(limit int) *tbExternalUserDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbExternalUserDo) Offset(offset int) *tbExternalUserDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbExternalUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbExternalUserDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbExternalUserDo) Unscoped() *tbExternalUserDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbExternalUserDo) Create(values ...*model.TbExternalUser) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbExternalUserDo) CreateInBatches(values []*model.TbExternalUser, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbExternalUserDo) Save(values ...*model.TbExternalUser) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbExternalUserDo) First() (*model.TbExternalUser, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalUser), nil
	}
}

func (t tbExternalUserDo) Take() (*model.TbExternalUser, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalUser), nil
	}
}

func (t tbExternalUserDo) Last() (*model.TbExternalUser, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalUser), nil
	}
}

func (t tbExternalUserDo) Find() ([]*model.TbExternalUser, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbExternalUser), err
}

func (t tbExternalUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbExternalUser, err error) {
	buf := make([]*model.TbExternalUser, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbExternalUserDo) FindInBatches(result *[]*model.TbExternalUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbExternalUserDo) Attrs(attrs ...field.AssignExpr) *tbExternalUserDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbExternalUserDo) Assign(attrs ...field.AssignExpr) *tbExternalUserDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbExternalUserDo) Joins(fields ...field.RelationField) *tbExternalUserDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbExternalUserDo) Preload(fields ...field.RelationField) *tbExternalUserDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbExternalUserDo) FirstOrInit() (*model.TbExternalUser, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalUser), nil
	}
}

func (t tbExternalUserDo) FirstOrCreate() (*model.TbExternalUser, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbExternalUser), nil
	}
}

func (t tbExternalUserDo) FindByPage(offset int, limit int) (result []*model.TbExternalUser, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbExternalUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbExternalUserDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbExternalUserDo) Delete(models ...*model.TbExternalUser) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbExternalUserDo) withDO(do gen.Dao) *tbExternalUserDo {
	t.DO = *do.(*gen.DO)
	return t
}
