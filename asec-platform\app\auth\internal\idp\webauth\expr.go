package webauth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/expr-lang/expr"
	"github.com/expr-lang/expr/vm"
)

// ExprEngine 表达式引擎
type ExprEngine struct {
	// 内置函数映射
	functions map[string]interface{}
	env       map[string]interface{}
}

// NewExprEngine 创建新的表达式引擎
func NewExprEngine() *ExprEngine {
	engine := &ExprEngine{
		functions: make(map[string]interface{}),
		env:       make(map[string]interface{}),
	}

	// 注册内置函数
	engine.registerBuiltInFunctions()
	return engine
}

// registerBuiltInFunctions 注册内置函数
func (e *ExprEngine) registerBuiltInFunctions() {
	// 字符串函数
	e.functions["contains"] = strings.Contains
	e.functions["hasPrefix"] = strings.HasPrefix
	e.functions["hasSuffix"] = strings.HasSuffix
	e.functions["toLower"] = strings.ToLower
	e.functions["toUpper"] = strings.ToUpper
	e.functions["trim"] = strings.TrimSpace
	e.functions["replace"] = strings.Replace
	e.functions["split"] = strings.Split
	e.functions["join"] = strings.Join

	// 类型转换函数
	e.functions["toString"] = func(v interface{}) string {
		return toString(v)
	}
	e.functions["toInt"] = func(v interface{}) (int, error) {
		switch val := v.(type) {
		case int:
			return val, nil
		case int64:
			return int(val), nil
		case float64:
			return int(val), nil
		case string:
			var i int
			_, err := fmt.Sscanf(val, "%d", &i)
			return i, err
		default:
			return 0, fmt.Errorf("无法转换为整数: %v", v)
		}
	}
	e.functions["toFloat"] = func(v interface{}) (float64, error) {
		switch val := v.(type) {
		case float64:
			return val, nil
		case int:
			return float64(val), nil
		case int64:
			return float64(val), nil
		case string:
			var f float64
			_, err := fmt.Sscanf(val, "%f", &f)
			return f, err
		default:
			return 0, fmt.Errorf("无法转换为浮点数: %v", v)
		}
	}

	// 数组和对象操作
	e.functions["get"] = func(m map[string]interface{}, key string) interface{} {
		return m[key]
	}
	e.functions["has"] = func(m map[string]interface{}, key string) bool {
		_, ok := m[key]
		return ok
	}

	// HMAC客户端 - 放入env
	e.env["HmacClient"] = func() *HmacClient {
		return NewHmacClient()
	}

	e.env["JSON"] = map[string]interface{}{
		"stringify": func(v interface{}) string {
			bytes, err := json.Marshal(v)
			if err != nil {
				return ""
			}
			return string(bytes)
		},
		"parse": func(s string) (interface{}, error) {
			var v interface{}
			err := json.Unmarshal([]byte(s), &v)
			return v, err
		},
	}

	// 加密相关函数 - 放入env
	e.env["hmacSha256"] = func(key string, data string) []byte {
		h := hmac.New(sha256.New, []byte(key))
		h.Write([]byte(data))
		return h.Sum(nil)
	}

	// Base64编码函数 - 放入env
	e.env["Base64"] = map[string]interface{}{
		"encode": func(data interface{}) string {
			var bytes []byte
			switch v := data.(type) {
			case []byte:
				bytes = v
			case string:
				bytes = []byte(v)
			default:
				return ""
			}
			return base64.StdEncoding.EncodeToString(bytes)
		},
		"decode": func(data string) string {
			bytes, err := base64.StdEncoding.DecodeString(data)
			if err != nil {
				return ""
			}
			return string(bytes)
		},
	}

	// 时间函数 - 放入env
	e.env["now"] = func() time.Time {
		return time.Now()
	}
	e.env["formatTime"] = func(t time.Time, layout string) string {
		return t.Format(layout)
	}

	//专用钉钉函数（浙政钉）
	e.functions["dingtalkFindMainDeptCode"] = func(data interface{}) string {
		if data == nil {
			return ""
		}

		items, ok := data.([]interface{})
		if !ok {
			return ""
		}

		for _, item := range items {
			if m, ok := item.(map[string]interface{}); ok {
				mainJob, exists := m["mainJob"]
				if exists && mainJob == true {
					if orgCode, ok := m["organizationCode"].(string); ok {
						return orgCode
					}
				}
			}
		}
		return ""
	}

	e.functions["dingTalkSignature"] = func(method, timestamp, nonce, uri string, paramsJSON, secret string) string {
		// 解析JSON格式参数到map
		var params map[string][]string
		if err := json.Unmarshal([]byte(paramsJSON), &params); err != nil {
			return ""
		}
		return BuildDingTalkSignature(method, timestamp, nonce, uri, params, secret)
	}

}

// Execute 执行表达式
func (e *ExprEngine) Execute(script string, contextEnv map[string]interface{}) (interface{}, error) {
	// 创建完整环境变量
	env := make(map[string]interface{})

	// 先添加内置env函数
	for name, val := range e.env {
		env[name] = val
	}

	// 添加内置functions函数
	for name, fn := range e.functions {
		env[name] = fn
	}

	for k, v := range contextEnv {
		env[k] = v
	}

	// 编译表达式
	program, err := expr.Compile(script, expr.Env(env))
	if err != nil {
		return nil, fmt.Errorf("编译表达式错误: %w", err)
	}

	// 运行表达式
	result, err := expr.Run(program, env)
	if err != nil {
		return nil, fmt.Errorf("执行表达式错误: %w", err)
	}

	return result, nil
}

// MustExecute 执行表达式，忽略错误
func (e *ExprEngine) MustExecute(script string, env map[string]interface{}) interface{} {
	result, _ := e.Execute(script, env)
	return result
}

// CompileAndSave 编译并保存表达式程序
func (e *ExprEngine) CompileAndSave(script string, env map[string]interface{}) (*vm.Program, error) {
	return expr.Compile(script, expr.Env(env))
}

// ExecuteSaved 执行预编译的表达式程序
func (e *ExprEngine) ExecuteSaved(program *vm.Program, env map[string]interface{}) (interface{}, error) {
	// 将内置函数添加到环境中
	for name, fn := range e.functions {
		env[name] = fn
	}
	return expr.Run(program, env)
}

// GetBuiltinFunctions 获取所有内置函数
func (e *ExprEngine) GetBuiltinFunctions() map[string]interface{} {
	// 复制函数映射
	funcs := make(map[string]interface{})

	// 复制函数
	for name, fn := range e.functions {
		funcs[name] = fn
	}

	// 复制环境变量中的函数
	for name, val := range e.env {
		funcs[name] = val
	}

	return funcs
}

// ValidateScript 静态方法-仅验证脚本语法是否正确
func ValidateScript(script string, mockEnv map[string]interface{}) error {
	engine := NewExprEngine()

	// 准备编译时环境
	env := make(map[string]interface{})
	// 1. 注入所有内置函数/变量
	for name, fn := range engine.GetBuiltinFunctions() {
		env[name] = fn
	}

	// 2. 注入 global
	if gm, ok := mockEnv["global"]; ok {
		env["global"] = gm
	} else {
		env["global"] = map[string]interface{}{
			"appsecret": "", "appkey": "", "depappsecret": "", "depappkey": "",
			"client_id": "", "client_url": "", "orgId": "", "redirect_uri": "",
			"code": "", "state": "", "AuthCode": "",
		}
	}
	// 3. 注入 user
	if um, ok := mockEnv["user"]; ok {
		env["user"] = um
	} else {
		env["user"] = map[string]interface{}{
			"AccessToken": "", "AuthCode": "", "Userid": "", "Name": "",
			"Nickname": "", "AceessExpires": "",
		}
	}
	// 4. **新增：注入 env**
	if em, ok := mockEnv["env"]; ok {
		env["env"] = em
	} else {
		env["env"] = map[string]interface{}{
			"timestamp":   "",
			"nonce":       "",
			"IP":          "",
			"Host":        "",
			"RedirectUri": "",
		}
	}
	// 5. 其它 mockEnv 中的键也一并注入
	for k, v := range mockEnv {
		if k != "global" && k != "user" && k != "env" {
			env[k] = v
		}
	}

	// 编译（不执行）脚本
	_, err := expr.Compile(script, expr.Env(env))
	return err
}
