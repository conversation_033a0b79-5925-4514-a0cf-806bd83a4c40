// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: auth/v1/user/user.proto

package user

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	User_GetLoginUserInfo_FullMethodName = "/api.auth.v1.user.User/GetLoginUserInfo"
	User_UpdatePasswd_FullMethodName     = "/api.auth.v1.user.User/UpdatePasswd"
	User_Logout_FullMethodName           = "/api.auth.v1.user.User/Logout"
	User_RedirectVerify_FullMethodName   = "/api.auth.v1.user.User/RedirectVerify"
)

// UserClient is the client API for User service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserClient interface {
	GetLoginUserInfo(ctx context.Context, in *GetLoginUserInfoReq, opts ...grpc.CallOption) (*GetLoginUserInfoReply, error)
	UpdatePasswd(ctx context.Context, in *UpdatePasswordReq, opts ...grpc.CallOption) (*UpdatePasswordReply, error)
	Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutReply, error)
	RedirectVerify(ctx context.Context, in *RedirectVerifyRequest, opts ...grpc.CallOption) (*RedirectVerifyReply, error)
}

type userClient struct {
	cc grpc.ClientConnInterface
}

func NewUserClient(cc grpc.ClientConnInterface) UserClient {
	return &userClient{cc}
}

func (c *userClient) GetLoginUserInfo(ctx context.Context, in *GetLoginUserInfoReq, opts ...grpc.CallOption) (*GetLoginUserInfoReply, error) {
	out := new(GetLoginUserInfoReply)
	err := c.cc.Invoke(ctx, User_GetLoginUserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdatePasswd(ctx context.Context, in *UpdatePasswordReq, opts ...grpc.CallOption) (*UpdatePasswordReply, error) {
	out := new(UpdatePasswordReply)
	err := c.cc.Invoke(ctx, User_UpdatePasswd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutReply, error) {
	out := new(LogoutReply)
	err := c.cc.Invoke(ctx, User_Logout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) RedirectVerify(ctx context.Context, in *RedirectVerifyRequest, opts ...grpc.CallOption) (*RedirectVerifyReply, error) {
	out := new(RedirectVerifyReply)
	err := c.cc.Invoke(ctx, User_RedirectVerify_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServer is the server API for User service.
// All implementations must embed UnimplementedUserServer
// for forward compatibility
type UserServer interface {
	GetLoginUserInfo(context.Context, *GetLoginUserInfoReq) (*GetLoginUserInfoReply, error)
	UpdatePasswd(context.Context, *UpdatePasswordReq) (*UpdatePasswordReply, error)
	Logout(context.Context, *LogoutReq) (*LogoutReply, error)
	RedirectVerify(context.Context, *RedirectVerifyRequest) (*RedirectVerifyReply, error)
	mustEmbedUnimplementedUserServer()
}

// UnimplementedUserServer must be embedded to have forward compatible implementations.
type UnimplementedUserServer struct {
}

func (UnimplementedUserServer) GetLoginUserInfo(context.Context, *GetLoginUserInfoReq) (*GetLoginUserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoginUserInfo not implemented")
}
func (UnimplementedUserServer) UpdatePasswd(context.Context, *UpdatePasswordReq) (*UpdatePasswordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePasswd not implemented")
}
func (UnimplementedUserServer) Logout(context.Context, *LogoutReq) (*LogoutReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedUserServer) RedirectVerify(context.Context, *RedirectVerifyRequest) (*RedirectVerifyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedirectVerify not implemented")
}
func (UnimplementedUserServer) mustEmbedUnimplementedUserServer() {}

// UnsafeUserServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServer will
// result in compilation errors.
type UnsafeUserServer interface {
	mustEmbedUnimplementedUserServer()
}

func RegisterUserServer(s grpc.ServiceRegistrar, srv UserServer) {
	s.RegisterService(&User_ServiceDesc, srv)
}

func _User_GetLoginUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoginUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetLoginUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_GetLoginUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetLoginUserInfo(ctx, req.(*GetLoginUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdatePasswd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdatePasswd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UpdatePasswd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdatePasswd(ctx, req.(*UpdatePasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).Logout(ctx, req.(*LogoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_RedirectVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedirectVerifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).RedirectVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_RedirectVerify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).RedirectVerify(ctx, req.(*RedirectVerifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// User_ServiceDesc is the grpc.ServiceDesc for User service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var User_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.auth.v1.user.User",
	HandlerType: (*UserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLoginUserInfo",
			Handler:    _User_GetLoginUserInfo_Handler,
		},
		{
			MethodName: "UpdatePasswd",
			Handler:    _User_UpdatePasswd_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _User_Logout_Handler,
		},
		{
			MethodName: "RedirectVerify",
			Handler:    _User_RedirectVerify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "auth/v1/user/user.proto",
}
