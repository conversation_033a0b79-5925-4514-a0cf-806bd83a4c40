package data

import (
	"context"

	authModel "asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"asdsec.com/asec/platform/pkg/ip2region/common"
	model "asdsec.com/asec/platform/pkg/model/strategy_model"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	commonUtils "asdsec.com/asec/platform/app/auth/internal/common"

	"github.com/go-kratos/kratos/v2/log"
)

type commonRepo struct {
	data *Data
	log  *log.Helper
}

func (a commonRepo) GetLastLoginIp(ctx context.Context, userId string) (authModel.TbUserLoginLog, error) {
	q := query.Use(a.data.db).TbUserLoginLog
	var result authModel.TbUserLoginLog
	err := q.WithContext(ctx).Where(q.UserID.Eq(userId)).Order(q.EventTime.Desc()).Limit(1).Scan(&result)
	return result, err
}

func (a commonRepo) GetFactorRegion(ctx context.Context, ids []string) ([]*model.UebaStrategyCondition, error) {
	facRegionT := model.UebaStrategyCondition{}.TableName()
	var res []*model.UebaStrategyCondition
	err := a.data.db.Table(facRegionT).Where("id IN ? OR pid IN ? AND condition_type = 'risk_region'", ids, ids).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (a commonRepo) GetFactorTime(ctx context.Context, ids []string) ([]*model.FactorTime, error) {
	facTimeT := model.FactorTime{}.TableName()
	var res []*model.FactorTime
	err := a.data.db.Table(facTimeT).Where("id IN ?", ids).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetAccessConfig 实现 commonUtils.AccessConfigProvider 接口
func (a commonRepo) GetAccessConfig(ctx context.Context) (commonUtils.AccessConfig, error) {
	q := query.Use(a.data.db).TbAccessConfig
	result, err := q.WithContext(ctx).First()
	if err != nil {
		// 如果没有找到记录，返回默认配置
		if err.Error() == "record not found" {
			return commonUtils.AccessConfig{}, nil
		}
		a.log.WithContext(ctx).Errorf("query access config error: %v", err)
		return commonUtils.AccessConfig{}, err
	}

	return commonUtils.AccessConfig{
		LanAddress:      result.LanAddress,
		InternetAddress: result.InternetAddress,
		AliasAddresses:  result.AliasAddresses,
	}, nil
}

// GetAccessConfigModel 获取原始数据库模型
func (a commonRepo) GetAccessConfigModel(ctx context.Context) (*authModel.TbAccessConfig, error) {
	q := query.Use(a.data.db).TbAccessConfig
	result, err := q.WithContext(ctx).First()
	if err != nil {
		// 如果没有找到记录，返回默认配置
		if err.Error() == "record not found" {
			return &authModel.TbAccessConfig{}, nil
		}
		a.log.WithContext(ctx).Errorf("query access config error: %v", err)
		return nil, err
	}
	return result, nil
}

func (a commonRepo) GetIp2Region() common.Ip2region {
	return a.data.ip2region
}
func NewCommonRepo(data *Data, logger log.Logger) biz.CommonRepo {
	repo := &commonRepo{
		data: data,
		log:  log.NewHelper(logger),
	}

	// 设置全局的访问配置提供者
	commonUtils.SetAccessConfigProvider(repo)

	return repo
}
