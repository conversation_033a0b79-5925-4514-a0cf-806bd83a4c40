package api

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	normErrors "errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"

	comm "asdsec.com/asec/platform/app/console/app/appliancemgt/common"
	"asdsec.com/asec/platform/app/console/app/appliancemgt/dto"
	"asdsec.com/asec/platform/app/console/app/appliancemgt/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	sysService "asdsec.com/asec/platform/app/console/app/system/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/global/errors"
	"asdsec.com/asec/platform/app/console/utils/web"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AppMgtReq struct {
	Limit  int `form:"limit" json:"limit" binding:"required,min=1,max=1000"`
	Offset int `form:"offset" json:"offset" binding:"min=0"`
}

// GetAgentList godoc
// @Summary 查询终端列表
// @Schemes
// @Description 查询终端列表
// @Tags        Appliance
// @Produce     application/json
// @Param       req body model.Pagination true "分页查询应用参数"
// @Success     200
// @Router      /v1/agents [POST]
// @success     200 {object} common.Response{data=model.Pagination} "application list"
func GetAgentList(c *gin.Context) {
	var req dto.GetAgentListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	data, err := service.GetAppService().GetAppList(c, req.Pagination, req)
	if err != nil {
		global.SysLog.Error("query list err", zap.Error(err))
		common.Fail(c, common.QueryDeviceErr)
		return
	}
	common.OkWithData(c, data)
}

// BindAgentUser godoc
// @Summary 终端绑定用户
// @Schemes
// @Description 终端绑定用户
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.AgentBindUserReq true "参数"
// @Success     200
// @Router      /v1/agents/bind [POST]
// @success     200 {object} common.Response{} "ok"
func BindAgentUser(c *gin.Context) {
	req := dto.AgentBindUserReq{}
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Sugar().Errorf("param err=%v", err)
		common.Fail(c, common.ParamInvalidError)
		return
	}
	aError := service.GetAppService().BindAgentUser(c, req)
	if aError != nil {
		global.SysLog.Error("UploadLog err", zap.Error(aError))
		common.FailWithMessage(c, -1, aError.Error())
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		var OperationType = common.OperateUnBind
		if req.UserId != "" {
			OperationType = common.OperateBind
		}
		oplog := model.Oprlog{
			ResourceType:   common.TerminalManagementTYpe,
			OperationType:  OperationType,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	common.Ok(c)
}

// DeleteAgentsRecord godoc
// @Summary 删除平台终端记录
// @Schemes
// @Description 删除平台终端记录
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.ReqIds true "参数"
// @Success     200
// @Router      /v1/agents/delete [POST]
// @success     200 {object} common.Response{} "ok"
func DeleteAgentsRecord(c *gin.Context) {
	req := dto.ReqIds{}
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Sugar().Errorf("param err=%v", err)
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if len(req.Ids) <= 0 {
		common.Ok(c)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.TerminalManagementTYpe,
			OperationType:  common.OperateDelete,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	aError := service.GetAppService().DeleteAgentsRecord(c, req)
	if aError != nil {
		global.SysLog.Error("UploadLog err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// UninstallAgents godoc
// @Summary 批量卸载终端
// @Schemes
// @Description 批量卸载终端
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.ReqIds true "参数"
// @Success     200
// @Router      /v1/agents/uninstall [POST]
// @success     200 {object} common.Response{} "ok"
func UninstallAgents(c *gin.Context) {
	req := dto.ReqIds{}
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Sugar().Errorf("param err=%v", err)
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if len(req.Ids) <= 0 {
		common.Ok(c)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.TerminalManagementTYpe,
			OperationType:  common.OperateUninstall,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetAppService().UninstallAgents(c, req)
	if aError != nil {
		global.SysLog.Error("UploadLog err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

const ChangePlatAddrAction = "change_plataddr"

func AgentActionStatusReport(c *gin.Context) {
	var req dto.SatusReq
	if err := c.ShouldBind(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.Action == ChangePlatAddrAction {
		common.Ok(c)
		return
	}
	//不是平台地址切换的任务状态需要上传ID
	if req.Id == "" {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	aError := service.GetAppService().AgentActionStatus(c, req)
	if aError != nil {
		global.SysLog.Error("UploadLog err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}
func AgentLogList(c *gin.Context) {
	var req dto.AgentLogsListReq
	if err := c.ShouldBind(&req); err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	resp, aError := service.GetAppService().AgentLogList(c, req)
	if aError != nil {
		global.SysLog.Error("get agent logs err", zap.Error(aError))
		common.FailWithMessage(c, -1, aError.Error())
		return
	}
	common.OkWithData(c, resp)
}

// GetAgentPas godoc
// @Summary 查询终端卸载密码
// @Schemes
// @Description 查询终端卸载密码
// @Tags        Appliance
// @Produce     application/json
// @Param       platform id string true "终端ID"
// @Success     200
// @Router      /v1/agents/pas [GET]
// @success     200 {object} common.Response{data=Pas} "卸载密码"
func GetAgentPas(c *gin.Context) {
	var req IdReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.FailDownload(c, common.ParamInvalidError)
		return
	}
	// 将int转换为[]byte
	agentIdHash := []byte(req.Id)

	// 计算MD5值
	hash := md5.Sum(agentIdHash)

	// 将MD5值转换为hex字符串
	md5str := hex.EncodeToString(hash[:])
	md5Title := strings.ToTitle(md5str)
	// 取前六位
	topSix := md5Title[:6]

	// 转码64
	encoded := base64.StdEncoding.EncodeToString([]byte(topSix))
	common.OkWithData(c, encoded)
}

// GetApplianceList godoc
// @Summary 获取设备列表
// @Schemes
// @Description 获取设备列表
// @Tags        Appliance
// @Produce     application/json
// @Param       req body model.Pagination true "获取设备列表"
// @Success     200
// @Router      /v1/appliances/list [POST]
// @success     200 {object} common.Response{data=model.Pagination} "cmd"
func GetApplianceList(c *gin.Context) {
	var req model.Pagination
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	connectors, err := service.GetAppService().GetAppliances(c, req)
	if err != nil {
		global.SysLog.Error("query list err", zap.Error(err))
		common.Fail(c, common.QueryDeviceErr)
		return
	}
	common.OkWithData(c, connectors)
}

type DownloadReq struct {
	Platform string `form:"platform" json:"platform" binding:"required,oneof=windows linux darwin iphone android"`
	Version  string `form:"version" json:"version" binding:"required,min=1"`
}

type IdReq struct {
	Id string `form:"id" json:"id" binding:"required,min=1"`
}

// DownloadAgent godoc
// @Summary 获取客户端安装包
// @Schemes
// @Description 获取客户端安装包
// @Tags        Appliance
// @Param       platform query string true "平台类型"
// @Success     200
// @Router      /v1/agents/installer [GET]
func DownloadAgent(c *gin.Context) {
	var req DownloadReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.FailDownload(c, common.ParamInvalidError)
		return
	}
	version := req.Version
	platform := req.Platform
	dirPath := filepath.Join(comm.AgentDir, platform, version)
	// 判断目录是否存在
	dirExists, _ := utils.FileExists(dirPath)
	if !dirExists {
		os.MkdirAll(dirPath, 0750)
	}
	agentFileName := comm.GetAgentNameByPlatform(platform)

	filePath := filepath.Join(dirPath, agentFileName)
	exists, _ := utils.FileExists(filePath)
	// 文件不存在，从oss下载
	if !exists {
		modeInfo, err := service.GetAppService().GetDeployChannel(c)
		if err != nil {
			global.SysLog.Error("GetDeployChannel err", zap.Error(err))
			common.FailDownload(c, common.GetDeployChannelErr)
			return
		}
		devMode := modeInfo.DevMode
		if devMode == "" {
			devMode = comm.ProductMode
		}
		configFilePath := filepath.Join(comm.AgentDir, platform, comm.VersionInfoFile)
		configExist, _ := utils.FileExists(configFilePath)
		if !configExist {
			global.SysLog.Error("config.json not exit")
			common.FailDownload(c, common.FileParseError)
			return
		}
		configData, err := ioutil.ReadFile(configFilePath)
		if err != nil {
			global.SysLog.Error("ReadFile err", zap.Error(err))
			common.FailDownload(c, common.FileParseError)
			return
		}
		var configs []dto.AgentUpgradeConfig
		err = json.Unmarshal(configData, &configs)
		if err != nil {
			global.SysLog.Error("Unmarshal err", zap.Error(err))
			common.FailDownload(c, common.FileParseError)
			return
		}
		//下载文件并且校验
		ObjectKey := path.Join(devMode, comm.AgentsDir, platform, version, agentFileName)
		err = service.GetFileFromOss(ObjectKey, filePath)
		if err != nil {
			global.SysLog.Error(fmt.Sprintf("ObjectKey:%s, Bucket:%s, err_msg", ObjectKey, global.SysConfig.Oss.Bucket), zap.Error(err))
			common.FailDownload(c, common.GetFileFromOssErr)
			return
		}
		data, err := os.ReadFile(filePath)
		if err != nil {
			return
		}
		matchVersionFlag := false
		for _, v := range configs {
			if v.Version == req.Version {
				matchVersionFlag = true
				md5Util := md5.New()
				md5Util.Write(data)
				fileMd5 := hex.EncodeToString(md5Util.Sum(nil))
				if v.Md5 != fileMd5 {
					global.SysLog.Error("Md5 not match", zap.String(v.Md5+":", fileMd5))
					global.SysLog.Error("download", zap.String("url:", ObjectKey))
					common.FailDownload(c, common.FileCheckError)
					return
				}
			}
		}
		// 没有匹配的版本
		if !matchVersionFlag {
			global.SysLog.Error("no match version",
				zap.String("req.version:", req.Version), zap.Any("configs", configs))
			common.FailDownload(c, common.FileCheckError)
			return
		}
	}
	f, err := os.Open(filePath)
	if err != nil {
		global.SysLog.Info("file not exists", zap.String("path", filePath), zap.Error(err))
		common.FailDownload(c, common.FileCheckError)
		return
	}
	defer f.Close()

	// 获取文件信息
	fileInfo, err := f.Stat()
	if err != nil {
		global.SysLog.Error("Failed to get file info", zap.Error(err))
		common.FailDownload(c, common.FileCheckError)
		return
	}

	// 设置 Content-Length 头
	c.Header("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	global.SysLog.Info("download file", zap.String("path", filePath))
	c.Header("Content-Type", "application/octet-stream")
	//urlFileName := url.QueryEscape(fmt.Sprintf(DownloadFileNameFmt, c.Request.Host))
	var ip = ""
	ipInfo, ignoreErr := sysService.GetSystemService().QueryPlatformIPNet(c)
	if ignoreErr != nil {
		global.SysLog.Sugar().Warnf("QueryPlatformIPNet failed. err=%v", ignoreErr)
	} else {
		ip = ipInfo.PlatformIP
	}
	port := web.GetReqServerPort(c)
	c.Header("Content-Disposition", "attachment; filename="+comm.FormatWebFileName(platform, version, ip, port))
	c.Header("Content-Transfer-Encoding", "binary")

	f.Seek(0, 0)
	io.Copy(c.Writer, f)
}

type AuthReq struct {
	AuthType string `form:"auth_type" json:"auth_type" binding:"required,oneof=otp gauth"`
}

func DownloadAuthFile(c *gin.Context) {
	var req AuthReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	var filePath string
	if req.AuthType == comm.OptName {
		filePath = filepath.Join(comm.AuthDir, comm.FreeOptFileName)
	} else {
		filePath = filepath.Join(comm.AuthDir, comm.GoogleAuthFileName)
	}
	f, err := os.Open(filePath)
	if err != nil {
		global.SysLog.Info("file not exists", zap.String("path", filePath), zap.Error(err))
		return
	}
	defer f.Close()
	global.SysLog.Info("download file", zap.String("path", filePath))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", "attachment; filename="+filepath.Base(filePath))
	c.Header("Content-Transfer-Encoding", "binary")

	f.Seek(0, 0)
	io.Copy(c.Writer, f)
}

// DownloadArchives godoc
// @Summary 获取安装包
// @Schemes
// @Description 获取安装包
// @Tags        Appliance
// @Success     200
// @Router      /archives/latest/:file [GET]
func DownloadArchives(c *gin.Context) {
	filename := c.Param("file")
	//防止目录穿越
	if strings.Contains(filename, "..") {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	filePath := filepath.Join(comm.BaseDir, filename)
	f, err := os.Open(filePath)
	if err != nil {
		global.SysLog.Info("file not exists", zap.String("path", filePath), zap.Error(err))
		return
	}
	defer f.Close()
	global.SysLog.Info("download file", zap.String("path", filePath))
	c.Header("Content-Type", "application/octet-stream")
	//urlFileName := url.QueryEscape(fmt.Sprintf(DownloadFileNameFmt, c.Request.Host))
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Transfer-Encoding", "binary")

	f.Seek(0, 0)
	io.Copy(c.Writer, f)
}

// DownloadGateway godoc
// @Summary 下载网关安装包
// @Schemes
// @Description 下载网关安装包
// @Tags        Appliance
// @Success     200
// @Router      /v1/gateway/installer [GET]
func DownloadGateway(c *gin.Context) {
	filePath := filepath.Join(comm.GatewayDir, "gateway.tar.gz")
	
	// 检查文件是否存在
	exists, _ := utils.FileExists(filePath)
	if !exists {
		global.SysLog.Error("gateway package not found", zap.String("path", filePath))
		common.FailDownload(c, common.GetFileFromServerErr)
		return
	}

	// 打开文件
	f, err := os.Open(filePath)
	if err != nil {
		global.SysLog.Error("open file failed", zap.Error(err))
		common.FailDownload(c, common.GetFileFromServerErr)
		return
	}
	defer f.Close()

	// 获取文件信息
	fileInfo, err := f.Stat()
	if err != nil {
		global.SysLog.Error("get file info failed", zap.Error(err))
		common.FailDownload(c, common.GetFileFromServerErr)
		return
	}

	global.SysLog.Info("download file", zap.String("path", filePath))
	
	// 设置响应头，确保文件名包含在双引号中
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))
	c.Header("Content-Disposition", `attachment; filename="gateway.tar.gz"`)
	c.Header("Content-Transfer-Encoding", "binary")

	// 重置文件指针到开始位置
	f.Seek(0, 0)
	// 使用io.Copy发送文件
	io.Copy(c.Writer, f)
}

// DeleteAppliance godoc
// @Summary 删除设备
// @Schemes
// @Description 查询应用详情
// @Tags        Appliance
// @Produce     application/json
// @Param       id query uint64 true "设备ID"
// @Success     200
// @Router      /v1/appliances [DELETE]
// @success     200 {object} common.Response{} ""
func DeleteAppliance(c *gin.Context) {
	idStr := c.Query("id")
	nameStr := c.Query("name")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AgentsSysTYpe,
			OperationType:  common.OperateDelete,
			Representation: nameStr,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err = service.GetAppService().Delete(c, id)
	if err != nil {
		errorLog = err.Error()
		switch err.(type) {
		case *errors.WrappedError:
			common.FailWithMessage(c, -1, err.Error())
		default:
			global.SysLog.Error("query list err", zap.Error(err))
			common.FailWithMessage(c, -1, "删除设备失败")
		}
		return
	}
	common.Ok(c)
}

// CheckAppQuote godoc
// @Summary 删除设备前置检查
// @Schemes
// @Description 删除设备前置检查
// @Tags        Appliance
// @Produce     application/json
// @Param       id query uint64 true "设备ID"
// @Success     200
// @Router      /v1/appliances [GET]
// @success     200 {object} common.Response{} ""
func CheckAppQuote(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetAppService().CheckAppQuote(c, id)
	if err != nil {
		common.FailWithMessage(c, -1, err.Error())
		return
	}
	common.OkWithData(c, data)
}

// GetInstallCMD godoc
// @Summary 获取设备安装命令
// @Schemes
// @Description 获取设备安装命令
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.ApplianceInstallReq true "获取设备安装命令参数"
// @Success     200
// @Router      /v1/appliances/install_cmd [POST]
// @success     200 {object} common.Response{data=model.ApplianceInstall} "cmd"
func GetInstallCMD(c *gin.Context) {
	var req dto.ApplianceInstallReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if len(req.GatewayIP) != 0 {
		if !comm.IsIPAddress(req.GatewayIP) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}
	if req.Type == 3 && len(req.GatewayLocalIP) != 0 {
		if !comm.IsIPAddress(req.GatewayLocalIP) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AgentsSysTYpe,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	host := web.GetServerHost(c)
	endpoint := strings.Split(host, ":")
	cmd, err := service.GetAppService().GenInstallCMD(c, req, endpoint[0])
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("query list err", zap.Error(err))
		common.FailWithMessage(c, -1, "获取安装命令失败")
		return
	}
	common.OkWithData(c, cmd)
}

// UpdateAppliance godoc
// @Summary 更新设备
// @Schemes
// @Description 更新设备
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.UpdateApplianceInstallReq true "更新设备参数"
// @Success     200
// @Router      /v1/appliances [PUT]
// @success     200 {object} common.Response{} "response"
func UpdateAppliance(c *gin.Context) {
	var req dto.UpdateApplianceInstallReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if len(req.GatewayIP) != 0 {
		if !comm.IsIPAddress(req.GatewayIP) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}
	if len(req.GatewayLocalIP) != 0 {
		if !comm.IsIPAddress(req.GatewayLocalIP) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
	}
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AgentsSysTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err := service.GetAppService().UpdateAppliance(c, req)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("query list err", zap.Error(err))
		common.FailWithMessage(c, -1, "修改组件失败")
		return
	}
	common.Ok(c)
}

// AgentVersionCheck godoc
// @Summary 检查终端版本
// @Schemes
// @Description 获取设备安装命令
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.AgentVersionCheckReq true "检查终端版本参数"
// @Success     200
// @Router      /v1/agents/version_check [POST]
// @success     200 {object} common.Response{data=dto.AgentVersionCheckResp} ""
// TODO cl 前端和客户端请求分为两个接口
func AgentVersionCheck(c *gin.Context) {
	var req dto.AgentVersionCheckReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	resp, err := service.GetAppService().AgentVersionCheck(c, comm.DefaultTenantID, req)
	if err != nil {
		if normErrors.Is(err, comm.ErrOssWrong) {
			common.Fail(c, common.GetFileFromOssErr)
		} else if normErrors.Is(err, comm.ErrFileParseWrong) {
			common.Fail(c, common.FileParseError)
		} else {
			common.Fail(c, common.OperateError)
		}
		return
	}
	common.OkWithData(c, resp)
}

// SetAgentUpgradeStatus 这个接口没有做鉴权就可以直接往数据库里插数据，只做了一些简单的参数校验，建议后续考虑做下接口签名验证
func SetAgentUpgradeStatus(c *gin.Context) {
	var req dto.SetAgentUpgradeStatusReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetAppService().SetAgentUpgradeStatus(c, comm.DefaultTenantID, req)
	if err != nil {
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

func GetUpgradePolicyDetail(c *gin.Context) {
	var req dto.GetUpgradePolicyDetailReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Sugar().Errorf("param err=%v", err)
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// TODO 除认证模块外其他模块的租户id都是 "0"，后续改造时要通过公共方法获取
	resp, err := service.GetAppService().GetUpgradePolicyDetail(c, comm.DefaultTenantID, req.Platform)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradePolicyDetail failed. err=%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, resp)
}

// SetUpgradePolicy godoc
// @Summary 设置灰度策略
// @Schemes
// @Description 设置灰度策略
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.SetUpgradePolicyReq true "设置灰度策略参数"
// @Success     200
// @Router      /v1/agents/upgrade_policy [POST]
// @success     200 {object} common.Response{} "ok"
func SetUpgradePolicy(c *gin.Context) {
	var req dto.SetUpgradePolicyReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Sugar().Errorf("param err=%v", err)
		common.Fail(c, common.ParamInvalidError)
		return
	}

	if err := service.GetAppService().SetUpgradePolicy(c, req); err != nil {
		global.SysLog.Sugar().Errorf("SetUpgradePolicy failed. err=%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		var Platform = "windows"
		if req.Platform == "darwin" {
			Platform = "mac客户端"
		}
		oplog := model.Oprlog{
			ResourceType:   common.PlatformUpgradeTYpe,
			OperationType:  common.OperateUpdate,
			Representation: Platform + "升级配置",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	common.Ok(c)
}

func GetUpgradeOverview(c *gin.Context) {
	var req dto.GetUpgradeOverviewReq
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Sugar().Errorf("param err=%v", err)
		common.Fail(c, common.ParamInvalidError)
		return
	}
	overview, err := service.GetAppService().GetUpgradeOverview(c, comm.DefaultTenantID, req.Platform)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradeOverview failed. err=%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, overview)
}

// UploadLog godoc
// @Summary 上传终端日志
// @Schemes
// @Description 上传终端日志
// @Tags        Appliance
// @Produce     application/json
// @Param       req body dto.UploadLogReq true "UploadLog参数"
// @Success     200
// @Router      /v1/agents/upload_log [POST]
// @success     200 {object} common.Response{} "ok"
func UploadLog(c *gin.Context) {
	req := dto.UploadLogReq{}
	if err := c.ShouldBind(&req); err != nil {
		global.SysLog.Sugar().Errorf("param err=%v", err)
		common.Fail(c, common.ParamInvalidError)
		return
	}
	aError := service.GetAppService().UploadLog(c, req)
	if aError != nil {
		global.SysLog.Error("UploadLog err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// 采集日志调用示例：
//  curl --location 'http://127.0.0.1:9000/console/v1/agents/upload_log' --header 'Accept: application/json, text/plain, */*' --header 'Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7' --header 'Authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' --header 'Cache-Control: no-cache' --header 'Connection: keep-alive' --header 'Pragma: no-cache' --header 'Sec-Fetch-Dest: empty' --header 'Sec-Fetch-Mode: cors' --header 'Content-Type: application/json' --data '{
//  "appliance_id":"478103044039376906",
//  "cmd":"base64"
//  }'
