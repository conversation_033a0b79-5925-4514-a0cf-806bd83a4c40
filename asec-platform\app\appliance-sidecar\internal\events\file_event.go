package events

import (
	pb "asdsec.com/asec/platform/api/appliance/v1"
	v1 "asdsec.com/asec/platform/api/events/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/network"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/grpc"
	"sync"
	"time"
)

const (
	DDREventType = "ddr_event"

	// EventSourceClient 事件来源:客户端
	EventSourceClient = "asec_client"

	// EventSourceSE 事件来源:边缘代理网关
	EventSourceSE = "asec_se"
)

const eventQuerySql = `select 
		   id,
		   uuid,
		   file_name,
		   file_path,
		   file_size,
		   owner,
		   extension_name,
		   file_create_time,
		   last_change_time,
		   file_category_id,
		   occur_time,
		   md5,
		   sha256,
		   channel,
		   channel_type,
		   name_match_info,
		   content_match_info,
		   software_path,
		   original_file_name,
		   original_file_path,
		   activity,
		   file_type,
		   real_extension_name,
		   dst_path,
		   trace_id,
		   sub_src_trace_id,
		   src_path,
		   sensitive_rule_id,sensitive_rule_name,sensitive_level,data_category,
		   source_id,source_name,source_type,
		   sensitive_info,file_hide_suffix,
		   process_info
		from tbl_file_monitor
		where id > ?
		limit ?`

func SendEvents(ctx context.Context, wg *sync.WaitGroup) {

	param := common.SendParam{
		Ctx:              ctx,
		Wg:               wg,
		DoStreamSendFunc: doSendEvents,
		RunType:          common.StreamSend,
		GetConnFun:       connection.GetLogCenterConnection,
		WaitSecond:       common.Duration,
		RandomOffset:     2,
	}
	go utils.RunPublicIp(60 * 10)
	go cleanFileEvent(8, 50000, 40000)
	common.Send(param)
}

func doSendEvents(conn *grpc.ClientConn, ctx context.Context, wg *sync.WaitGroup) error {
	if !common.DdrSwitch {
		return nil
	}
	stmt, err := global.SqliteClient.Prepare(eventQuerySql)
	if stmt != nil {
		defer stmt.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("doSendEvents SqliteClient Prepare:%v", err)
		return err
	}
	currentOffset, err := common.GetOffset(common.FileEventTypeOffset)
	if err != nil {
		global.Logger.Sugar().Errorln("get current offset for file events err:%v", err)
		return err
	}

	rows, err := stmt.Query(currentOffset, common.Limit)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("stmt Query file events err:%v", err)
		return err
	}

	var events v1.FileEventReq
	//记录本次上报的offset
	var nextOffset = currentOffset
	userInfo := user.GetUserInfo()

	for rows.Next() {
		var event v1.FileEvent
		//当前上传事件offset
		var curId int64

		var subSrcTraceIdText string
		var traceIdText string
		err = rows.Scan(&curId, &event.Uuid, &event.FileName, &event.FilePath, &event.FileSize, &event.Owner, &event.ExtensionName,
			&event.FileCreateTime, &event.LastChangeTime, &event.FileCategoryId, &event.OccurTime, &event.Md5,
			&event.Sha256, &event.Channel, &event.ChannelType, &event.NameMatchInfo, &event.ContentMatchInfo,
			&event.SoftwarePath, &event.OriginalFileName, &event.OriginalFilePath, &event.Activity, &event.FileType,
			&event.RealExtensionName, &event.DstPath, &traceIdText, &subSrcTraceIdText, &event.SrcPath,
			&event.SensitiveRuleId, &event.SensitiveRuleName, &event.SensitiveLevel, &event.DataCategory,
			&event.SourceId, &event.SourceName, &event.SourceType,
			&event.SensitiveInfo, &event.FileHideSuffix, &event.ProcessInfo)
		if err != nil {
			global.Logger.Sugar().Errorln("doSendEvents rows Scan err:%v", err)
		}

		event.AgentId = global.ApplianceID
		event.UserId = userInfo.UserId
		event.UserName = userInfo.UserName
		ips, macs := network.GetIpsAndMac()
		event.AgentIp = ips
		event.AgentMac = macs
		event.AgentName = global.ApplianceName
		event.PlatType = global.PlatType
		event.EventSource = getEventSource()
		event.EventType = DDREventType
		event.SubSrcTraceId = transJsonArrayStr(subSrcTraceIdText)
		event.TraceId = transJsonArrayStr(traceIdText)
		event.PublicIp = utils.GetPublicIp().PublicIp

		events.Events = append(events.Events, &event)
		if curId > nextOffset {
			nextOffset = curId
		}
	}

	if len(events.Events) > 0 {
		stream, err := v1.NewEventClient(conn).CreateLog(ctx)
		if err != nil {
			if stream != nil {
				stream.CloseAndRecv()
			}
			global.Logger.Sugar().Errorf("NewEventClient err %v", err)
			return err
		}
		err = stream.Send(&events)
		if err != nil {
			if stream != nil {
				stream.CloseAndRecv()
			}
			global.Logger.Sugar().Errorf("Send err %v", err)
			return err
		}
		_, err = stream.CloseAndRecv()
		if err != nil {
			global.Logger.Sugar().Errorf("event send close err:%v", err)
		} else {
			// 上报失败不更新offset
			err := common.UpdateOffset(nextOffset, common.FileEventTypeOffset)
			if err != nil {
				global.Logger.Sugar().Errorf("event update offset err:%v", err)
			}
			return err
		}
	} else {
		// 没有查询结果时兜底offset. 防止客户端删了表不报事件了
		var res int64
		row := global.SqliteClient.QueryRow("SELECT id from tbl_file_monitor ORDER BY id desc limit 1")
		_ = row.Scan(&res)
		if currentOffset > res {
			err := common.UpdateOffset(res, common.FileEventTypeOffset)
			if err != nil {
				global.Logger.Sugar().Errorf("event update offset err:%v", err)
			}
			return err
		}
	}
	return nil
}

// transSubSrcTraceIdText
// 将客户端的string[] 字符串 转为golang string[]
func transJsonArrayStr(dataStr string) []string {
	var res []string
	if dataStr == "" {
		return res
	}
	err := jsoniter.Unmarshal([]byte(dataStr), &res)
	if err != nil {
		global.Logger.Sugar().Errorln("transSubSrcTraceId err:%v", err)

	}
	return res
}

func getEventSource() string {
	if global.ApplianceType == pb.ApplianceType_SECURITY_EDGE || global.ApplianceType == pb.ApplianceType_GATEWAY {
		return EventSourceSE
	} else if global.ApplianceType == pb.ApplianceType_AGENT {
		return EventSourceClient
	} else {
		return ""
	}
}

// cleanFileEvent 定时清理event
//
// cleanHour 扫描间隔
//
// keepMaxCounts 删除阈值
//
// keepCount 保持数量
func cleanFileEvent(cleanHour int64, keepMaxCounts int64, keepCount int64) {
	var err error
	for {
		var count int64
		var nowOffset int64
		var removeIndex int64
		var committedOffset int64

		if !common.DdrSwitch {
			goto WAIT
		}

		// 当前事件表数量
		count, err = common.SelectOneCount(global.SqliteClient, "select count(1) from tbl_file_monitor")
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvent getCurrentCount err:%v", err)
			goto WAIT
		}
		if count < keepMaxCounts {
			// no need del
			goto WAIT
		}

		// 已提交位移
		committedOffset, err = common.GetOffset(common.FileEventTypeOffset)
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvent getCurrentOffset err:%v", err)
			goto WAIT
		}

		// 最新事件表位移
		nowOffset, err = common.SelectOneCount(global.SqliteClient, "select id from tbl_file_monitor order by id desc limit 1")
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvent getTotalCount err:%v", err)
			goto WAIT
		}
		// 超过最大数量直接删除
		removeIndex = nowOffset - keepCount
		global.Logger.Sugar().Info("start clean file event,count:%v,removeIndex:%v,keepMaxCounts:%v,keepCount:%v",
			count, removeIndex, keepMaxCounts, keepCount)
		err = deleteEvent(removeIndex, committedOffset)
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvent deleteEvent err:%v", err)
		}

	WAIT:
		time.Sleep(time.Hour * time.Duration(cleanHour))
	}
}

func deleteEvent(removeIndex int64, committedOffset int64) error {
	// 删除数据
	exec, err := global.SqliteClient.Exec("delete from tbl_file_monitor where id<=?", removeIndex)
	if err != nil {
		return err
	}
	affected, err := exec.RowsAffected()
	if err != nil {
		return err
	}
	// 如果删除了还未提交记录,则更新已提交offset
	if committedOffset < removeIndex {
		err = common.UpdateOffsetTx(removeIndex, common.FileEventTypeOffset)
		if err != nil {
			global.Logger.Sugar().Errorln("updateOffset err:%v", err)
			return err
		}
	}
	global.Logger.Sugar().Info("clean file event counts:%v", affected)
	return nil
}
