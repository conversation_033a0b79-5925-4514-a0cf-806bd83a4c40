package agent_conf

import (
	v1 "asdsec.com/asec/platform/api/appliance/v1"
	conf "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"context"
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"google.golang.org/grpc"
	"strconv"
)

type OssConfig struct {
	OssType    string `json:"oss_type"`
	Ak         string `json:"ak"`
	Sk         string `json:"sk"`
	Endpoint   string `json:"endpoint"`
	Bucket     string `json:"bucket"`
	CleanMode  int    `json:"clean_mode"`
	ExpireDay  int    `json:"expire_day"`
	Enable     int    `json:"enable"`
	CorpId     string `json:"corpId"`
	Key        string `json:"key"`
	Random     string `json:"random"`
	Region     string `json:"region"`
	DeployType string `json:"deploy_type"`
}

type SidecarConfig struct {
	ConfigPollInterval int `json:"config_poll_interval"`
}

const SidecarConfType = "sidecar_conf"

func GetSidecarConf() (SidecarConfig, error) {
	conn, err := connection.GetPlatformConnection(global.Context)
	if err != nil {
		global.Logger.Sugar().Warnf("[GetSidecarConf] get connection err:%v", err)
		return SidecarConfig{}, err
	}
	client := v1.NewConfCenterClient(conn)
	resp, err := client.PollConf(context.TODO(), &v1.PollConfReq{
		ApplianceId: strconv.FormatUint(global.ApplianceID, 10),
		ConfType:    SidecarConfType,
	})
	if err != nil {
		global.Logger.Sugar().Warnf("get oss conf err:%v", err)
		return SidecarConfig{}, err
	}
	if len(resp.AddConfList) == 0 {
		return SidecarConfig{}, nil
	}
	resConf := resp.AddConfList[0]
	config := SidecarConfig{}
	err = json.Unmarshal([]byte(resConf.ConfText), &config)
	if err != nil {
		return SidecarConfig{}, err
	}
	return config, nil
}

func GetOssConf(conn *grpc.ClientConn) (OssConfig, error) {
	client := v1.NewConfCenterClient(conn)
	resp, err := client.PollConf(context.TODO(), &v1.PollConfReq{
		ApplianceId: strconv.FormatUint(global.ApplianceID, 10),
		ConfType:    "oss",
	})
	if err != nil {
		global.Logger.Sugar().Warnf("get oss conf err:%v", err)
		return OssConfig{}, err
	}
	var ossConf *v1.AgentConf
	if len(resp.AddConfList) == 0 {
		return OssConfig{}, errors.New("no oss conf")
	}
	ossConf = resp.AddConfList[0]

	c := conf.AdditionConf{}
	err = proto.Unmarshal(ossConf.ConfData, &c)
	if err != nil {
		global.Logger.Sugar().Errorf("unmarshal additionConf err:%v", err)
		return OssConfig{}, err
	}
	var OssConf OssConfig
	{
	}
	OssConf.Ak = c.Ak
	OssConf.Sk = c.Sk
	OssConf.CorpId = c.CorpId
	OssConf.OssType = c.OssType

	OssConf.Endpoint = c.Endpoint
	OssConf.Bucket = c.Bucket
	OssConf.CleanMode = int(c.CleanMode)
	OssConf.ExpireDay = int(c.ExpireDay)

	OssConf.Enable = int(c.Enable)
	OssConf.Key = c.Key
	OssConf.Random = c.Random
	OssConf.Region = c.Region
	OssConf.DeployType = c.DeployType
	return OssConf, nil
}
