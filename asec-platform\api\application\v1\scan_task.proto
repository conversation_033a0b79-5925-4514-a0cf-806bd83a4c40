syntax = "proto3";
package asdsec.core.api.app;

option go_package = "asdsec.com/asec/platform/api/app/v1;v1";

service ScanTask{
  rpc UpsetScanTask(UpsetScanTaskReq) returns (CommonScanTaskResp);
  rpc CreateScanFile(CreateScanFileReq) returns (CommonScanTaskResp);
}

message UpsetScanTaskReq{
  repeated ScanTaskResult task_list = 1;
}

message ScanTaskResult{
  string task_id = 1;
  string task_date = 2;
  string user_id = 3;
  string user_name = 4;
  string agent_id = 5;
  string agent_name = 6;
  string task_status = 7;
  uint32 l1_file_count = 8;
  uint32 l2_file_count = 9;
  uint32 l3_file_count = 10;
  uint32 l4_file_count = 11;
  uint32 scan_count = 12;
}
message CommonScanTaskResp{
  uint32 code = 1;
  string message = 2;
}

message CreateScanFileReq{
  repeated ScanFile scan_file = 1;
}

message ScanFile{
  string id = 1;
  string task_id = 2;
  string agent_id = 3;
  string user_id = 4;
  string file_path = 5;
  string file_sensitive_info = 6;
  string trace_ids = 7;
  string sub_trace_ids = 8;
  string basic_src_paths = 9;
  string extension_name = 10;
  string md5 = 11;
  uint32 file_category_id = 12;
  uint32 file_type_suffix = 13;
}

message SensitiveInfo{
  string sensitive_id = 1;
  string sensitive_category_id = 2;
  uint32 sensitive_level = 3;
  string sensitive_name = 4;
}