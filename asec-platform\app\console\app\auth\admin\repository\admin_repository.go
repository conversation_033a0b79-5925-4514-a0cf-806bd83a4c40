package repository

import (
	"asdsec.com/asec/platform/app/console/app/auth/admin/constants"
	model "asdsec.com/asec/platform/app/console/app/auth/admin/model"
	global "asdsec.com/asec/platform/app/console/global"
	globalModel "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type AdminRepository interface {
	CreateAdmin(ctx context.Context, req model.CreateAdminReq) error
	DeleteAdmin(ctx context.Context, userId string, corpId string) error
	GetAdminList(ctx context.Context, req model.GetAdminListReq) (globalModel.Pagination, error)
	GetAdminById(ctx context.Context, userId string, corpId string) (model.GetAdminRsp, error)
	UpdateAdmin(ctx context.Context, req model.UpdateAdminReq) error
	UpdateAdminPassword(ctx context.Context, req model.UpdateSelfAdminReq) error
	GetAdminByCorpAndName(ctx context.Context, corpId, userName string) (model.Admin, error)
}

// NewAppRepository 创建接口实现接口实现
func NewAppRepository() AdminRepository {
	return &adminRepository{}
}

type adminRepository struct {
}

func (a *adminRepository) GetAdminByCorpAndName(ctx context.Context, corpId, userName string) (model.Admin, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model.Admin{}, err
	}
	var res model.Admin
	err = db.Model(model.Admin{}).Where("corp_id = ? AND name = ?", corpId, userName).Find(&res).Error
	return res, err
}

func (a *adminRepository) UpdateAdminPassword(ctx context.Context, req model.UpdateSelfAdminReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var adminUser model.Admin
	err = db.Model(model.Admin{}).
		Where("id = ? and corp_id = ? and (expire_time > now() OR expire_type = ?) and status = 1", req.UserId, req.CorpId, constants.NeverExpireType).
		Find(&adminUser).Error
	if err != nil {
		log.Errorf("get admin user failed: %w", err)
		return err
	}
	if adminUser.Id != req.UserId {
		log.Errorf("get admin user failed: %w", err)
		return errors.New("can not update password")
	}
	var cred model.AdminCredential
	err = db.Model(model.AdminCredential{}).
		Where("user_id = ? and corp_id = ?", adminUser.Id, adminUser.CorpId).
		Find(&cred).Error
	if err != nil {
		log.Errorf("get admin credential error: %w", err)
		return err
	}
	var credData model.CredData
	err = json.Unmarshal([]byte(cred.CredentialData), &credData)
	if err != nil {
		log.Errorf("unmarshal failed. err=%v", err)
		return err
	}
	if GetHashPassword(req.OldPassword, credData) != cred.SecretData {
		log.Errorf("check password failed, user-id=%s, pwd=%s. err=%v", req.UserId, req.OldPassword, err)
		return errors.New(constants.AdminLoginPwdInCorrectError)
	}
	return db.Transaction(func(tx *gorm.DB) error {
		if req.NewPassword != "" {
			err = tx.Where("user_id = ? and corp_id = ?", req.UserId, req.CorpId).Delete(model.AdminCredential{}).Error
			if err != nil {
				return err
			}
			err = CreateCredentialData(tx, model.CreateCredentialReq{
				UserId:   req.UserId,
				CorpId:   req.CorpId,
				Password: req.NewPassword,
			})
		}
		return err
	})
}

func (a *adminRepository) UpdateAdmin(ctx context.Context, req model.UpdateAdminReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var admin model.Admin
	err = db.Model(model.Admin{}).Where("id = ?", req.UserId).Find(&admin).Error
	if err != nil {
		return err
	}
	if admin.Id == "" {
		return errors.New("admin not exist")
	}
	copier.Copy(&admin, req)
	permissionStr, err := json.Marshal(req.Permissions)
	if err != nil {
		return err
	}
	admin.Permissions = string(permissionStr)
	local, _ := time.LoadLocation("Local")
	expireTime, err := time.ParseInLocation("2006-01-02", req.ExpireTime, local)
	admin.ExpireTime = expireTime
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(model.Admin{}).Where("id = ? and corp_id = ?", admin.Id, admin.CorpId).Updates(&admin).Error
		if err != nil {
			return err
		}
		if req.Password != "" {
			err = tx.Where("user_id = ? and corp_id = ?", admin.Id, admin.CorpId).Delete(model.AdminCredential{}).Error
			if err != nil {
				return err
			}
			err = CreateCredentialData(tx, model.CreateCredentialReq{
				UserId:   admin.Id,
				CorpId:   admin.CorpId,
				Password: req.Password,
			})
		}
		return err
	})
}

func (a *adminRepository) GetAdminById(ctx context.Context, userId string, corpId string) (model.GetAdminRsp, error) {
	var result model.GetAdminRsp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model.GetAdminRsp{}, err
	}
	subQuery := `SELECT role_name,id,role_type FROM tb_admin_role WHERE corp_id = ? AND status = 1`
	err = db.Model(model.Admin{}).
		Select("tb_admin_entity.id,tb_admin_entity.name,tb_admin_entity.corp_id,tb_admin_entity.phone,tb_admin_entity.email,"+
			"tb_admin_entity.status,tb_admin_entity.permissions as permissions_str,tb_admin_entity.role_id,tb_admin_entity.expire_type,tb_admin_entity.login_failed_times,"+
			"to_char(tb_admin_entity.expire_time,'yyyy-mm-dd') AS expire_time,tb_admin_entity.desc,"+
			"to_char(tb_admin_entity.lock_time,'yyyy-mm-dd') AS lock_time,tar.role_name,tac.name as corp_name,tar.role_type").
		Joins(fmt.Sprintf("Left Join (%s) tar on tar.id = tb_admin_entity.role_id", subQuery), corpId).
		Joins("Left Join tb_admin_corp tac on tac.id = tb_admin_entity.corp_id").
		Where("tb_admin_entity.corp_id = ? and tb_admin_entity.id = ?", corpId, userId).
		Find(&result).Error
	if result.PermissionsStr != "" {
		err = json.Unmarshal([]byte(result.PermissionsStr), &result.Permissions)
	}
	return result, err
}

func (a *adminRepository) GetAdminList(ctx context.Context, req model.GetAdminListReq) (globalModel.Pagination, error) {
	var result []model.GetAdminRsp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return globalModel.Pagination{}, err
	}
	subQuery := `SELECT role_name,id,role_type FROM tb_admin_role WHERE corp_id = ? AND status = 1`
	db = db.Model(model.Admin{}).
		Select("tb_admin_entity.id,tb_admin_entity.name,tb_admin_entity.corp_id,tb_admin_entity.phone,tb_admin_entity.email,"+
			"tb_admin_entity.status,tb_admin_entity.role_id,tb_admin_entity.expire_type,tb_admin_entity.login_failed_times,"+
			"to_char(tb_admin_entity.expire_time,'yyyy-mm-dd') AS expire_time,tb_admin_entity.desc,"+
			"to_char(tb_admin_entity.lock_time,'yyyy-mm-dd') AS lock_time,tar.role_name,tac.name as corp_name,tar.role_type").
		Joins(fmt.Sprintf("Left Join (%s) tar on tar.id = tb_admin_entity.role_id", subQuery), req.CorpId).
		Joins("Left Join tb_admin_corp tac on tac.id = tb_admin_entity.corp_id").
		Where("tb_admin_entity.corp_id = ? and tb_admin_entity.id != ?", req.CorpId, req.UserId)
	var res = req.Pagination
	if req.Search != "" {
		res.SearchColumns = []string{"tb_admin_entity.name"}
	}
	return globalModel.Paginate(&result, &res, db)
}

func (a *adminRepository) DeleteAdmin(ctx context.Context, userId string, corpId string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	//todo 删除该用户和角色的关系表的记录
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(model.Admin{}).Delete(model.Admin{Id: userId, CorpId: corpId}).Error
		if err != nil {
			return err
		}
		err = tx.Where("user_id = ? and corp_id = ?", userId, corpId).Delete(model.AdminCredential{}).Error
		return err
	})
}

func (a *adminRepository) CreateAdmin(ctx context.Context, req model.CreateAdminReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Sugar().Error(err.Error())
		return errors.New("数据库异常")
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		global.SysLog.Sugar().Error(err.Error())
		return errors.New("操作失败")
	}
	local, _ := time.LoadLocation("Local")
	expireTime, err := time.ParseInLocation("2006-01-02", req.ExpireTime, local)
	permissionStr, err := json.Marshal(req.Permissions)
	if err != nil {
		return err
	}
	admin := model.Admin{
		Id:          strconv.FormatUint(id, 10),
		ExpireTime:  expireTime,
		Name:        req.Name,
		CorpId:      req.CorpId,
		Phone:       req.Phone,
		Email:       req.Email,
		Status:      req.Status,
		RoleId:      req.RoleId,
		ExpireType:  req.ExpireType,
		Permissions: string(permissionStr),
		CreateAt:    time.Now(),
		UpdateAt:    time.Now(),
		Desc:        req.Desc,
	}
	var itemAdmin model.Admin
	err = db.Model(model.Admin{}).Where("name = ? and corp_id = ?", req.Name, req.CorpId).Find(&itemAdmin).Error
	if err != nil {
		global.SysLog.Sugar().Error(err.Error())
		return errors.New("数据库异常")
	}
	if itemAdmin.Id != "" {
		return fmt.Errorf("用户名已存在")
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Model(model.Admin{}).Create(&admin).Error
		if err != nil {
			return nil
		}
		createCredentialReq := model.CreateCredentialReq{
			UserId:   admin.Id,
			Password: req.Password,
			CorpId:   req.CorpId,
		}
		err = CreateCredentialData(tx, createCredentialReq)
		return err
	})
}

func CreateCredentialData(tx *gorm.DB, req model.CreateCredentialReq) error {
	// 随机生成盐值
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		log.Errorf("rand.Read failed. err=%v", err)
		return err
	}
	userSalt := base64.StdEncoding.EncodeToString(salt)

	// 加密生成密码hash
	credData := model.CredData{
		UserSalt: userSalt,
		Iter:     constants.DefaultIter,
		KeyLen:   constants.DefaultKeyLen,
	}

	secretData := GetHashPassword(req.Password, credData)

	// 存储到用户凭证表
	credDataStr, err := json.Marshal(credData)
	if err != nil {
		log.Errorf("json.Marshal failed. err=%v", err)
		return err
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return err
	}
	adminCredential := model.AdminCredential{
		Id:             strconv.FormatUint(id, 10),
		CorpId:         req.CorpId,
		UserId:         req.UserId,
		Type:           constants.CredTypePassword,
		SecretData:     secretData,
		CredentialData: string(credDataStr),
		CreateAt:       time.Now(),
		UpdateAt:       time.Now(),
	}

	err = tx.Create(&adminCredential).Error
	return err
}
