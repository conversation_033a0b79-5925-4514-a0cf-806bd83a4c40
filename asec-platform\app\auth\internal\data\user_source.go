package data

import (
	"context"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type userSourceRepo struct {
	data *Data
	log  *log.Helper
}

func (u userSourceRepo) CreateUserSource(ctx context.Context, corpId, id, name, sourceType string) error {
	us := query.Use(u.data.db).TbUserSource
	return us.WithContext(ctx).Create(&model.TbUserSource{
		ID:         id,
		Name:       name,
		SourceType: sourceType,
		CorpID:     corpId,
	})
}

func (u userSourceRepo) UpdateUserSource(ctx context.Context, corpId, id, name string) error {
	us := query.Use(u.data.db).TbUserSource
	_, err := us.WithContext(ctx).Where(us.CorpID.Eq(corpId), us.ID.Eq(id)).Update(us.Name, name)
	return err
}
func (u userSourceRepo) GetUserSource(ctx context.Context, corpId, id string) (*model.TbUserSource, error) {
	us := query.Use(u.data.db).TbUserSource
	result, err := us.WithContext(ctx).Where(us.CorpID.Eq(corpId), us.ID.Eq(id)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserSource{}, pb.ErrorRecordNotFound("sourceId=%v not found", id)
		}
		return &model.TbUserSource{}, err
	}
	return result, nil
}

func (u userSourceRepo) GetUserSourceByName(ctx context.Context, corpId, name string) (*model.TbUserSource, error) {
	us := query.Use(u.data.db).TbUserSource
	result, err := us.WithContext(ctx).Where(us.CorpID.Eq(corpId), us.Name.Eq(name)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserSource{}, pb.ErrorRecordNotFound("corpId=%v, name=%v not found.", corpId, name)
		}
		return &model.TbUserSource{}, err
	}
	return result, nil
}

func (u userSourceRepo) ListUserSource(ctx context.Context, corpId string) ([]*model.TbUserSource, error) {
	us := query.Use(u.data.db).TbUserSource
	return us.WithContext(ctx).Where(us.CorpID.Eq(corpId)).Order(us.CreatedAt).Find()
}

func (u userSourceRepo) GetUserSourceByType(ctx context.Context, corpId, sourceType, templateType string) (*model.TbUserSource, error) {
	var result *model.TbUserSource
	var err error
	q := query.Use(u.data.db)
	ts := q.TbIdentityProviderTemplate
	us := q.TbUserSource
	if templateType != "" {
		result, err = us.WithContext(ctx).LeftJoin(ts, ts.SourceID.EqCol(us.ID)).Where(us.CorpID.Eq(corpId), ts.Type.Eq(sourceType), us.TemplateType.Eq(templateType)).First()
	} else {
		result, err = us.WithContext(ctx).Where(us.CorpID.Eq(corpId), us.SourceType.Eq(sourceType)).First()
	}
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserSource{}, pb.ErrorRecordNotFound("corpId=%v, sourceType=%v not found.", corpId, sourceType)
		}
		return &model.TbUserSource{}, err
	}
	return result, nil
}

func NewUserSourceRepo(data *Data, logger log.Logger) biz.UserSourceRepo {
	return &userSourceRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
