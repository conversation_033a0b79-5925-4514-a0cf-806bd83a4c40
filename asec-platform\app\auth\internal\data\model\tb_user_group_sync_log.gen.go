// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserGroupSyncLog = "tb_user_group_sync_log"

// TbUserGroupSyncLog mapped from table <tb_user_group_sync_log>
type TbUserGroupSyncLog struct {
	CorpID     string    `gorm:"column:corp_id" json:"corp_id"`
	GroupID    string    `gorm:"column:group_id;not null" json:"group_id"`
	Type       string    `gorm:"column:type;not null" json:"type"`
	SyncStatus string    `gorm:"column:sync_status;not null" json:"sync_status"`
	SyncInfo   string    `gorm:"column:sync_info" json:"sync_info"`
	Operator   string    `gorm:"column:operator" json:"operator"`
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
}

// TableName TbUserGroupSyncLog's table name
func (*TbUserGroupSyncLog) TableName() string {
	return TableNameTbUserGroupSyncLog
}
