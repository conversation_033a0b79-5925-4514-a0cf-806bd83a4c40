package data

import (
	"context"
	"fmt"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/common"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/google/uuid"

	"github.com/go-redis/redis/v8"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type idpRepo struct {
	data *Data
	log  *log.Helper
}

func (i idpRepo) ListIDPTemplate(ctx context.Context, corpId string) ([]*model.TbIdentityProviderTemplate, error) {
	var result []*model.TbIdentityProviderTemplate
	q := query.Use(i.data.db)
	idpt := q.TbIdentityProviderTemplate
	err := idpt.WithContext(ctx).
		Where(idpt.CorpID.Eq(corpId)).Order(idpt.CreatedAt.Desc()).
		Select(idpt.ALL).Scan(&result)
	return result, err
}

func (i idpRepo) DeleteIDP(ctx context.Context, corpId, idpId, name string) error {
	q := query.Use(i.data.db)
	return q.Transaction(func(tx *query.Query) error {
		authPolicyMaper := tx.TbAuthPolicyIdpMapper
		groupMapper := tx.TbIdpGroupMapper
		idpAttr := tx.TbIdentityProviderAttribute
		idp := tx.TbIdentityProvider
		// 删除策略关联
		if _, err := authPolicyMaper.WithContext(ctx).
			Where(authPolicyMaper.CorpID.Eq(corpId), authPolicyMaper.IdpID.Eq(idpId)).
			Delete(); err != nil {
			return err
		}
		// 删除用户组关联
		if _, err := groupMapper.WithContext(ctx).
			Where(groupMapper.CorpID.Eq(corpId), groupMapper.ProviderID.Eq(idpId)).
			Delete(); err != nil {
			return err
		}
		// 删除idp属性
		if _, err := idpAttr.WithContext(ctx).
			Where(idpAttr.ProviderID.Eq(idpId)).
			Delete(); err != nil {
			return err
		}
		// 删除idp
		_, err := idp.WithContext(ctx).
			Where(idp.CorpID.Eq(corpId), idp.ID.Eq(idpId)).
			Delete()
		if err != nil {
			return err
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.IdpType,
				OperationType:  common.OperateDelete,
				Representation: name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			i.data.db.Create(&oplog)
		}()
		return nil
	})
}

func (i idpRepo) GetDefaultLocalIDP(ctx context.Context, corpId string) (*model.TbIdentityProvider, error) {
	idp := query.Use(i.data.db).TbIdentityProvider
	result, err := idp.WithContext(ctx).Where(idp.CorpID.Eq(corpId), idp.IsDefault.Is(true)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbIdentityProvider{}, pb.ErrorRecordNotFound("default idp not found. corpId=%v", corpId)
		}
		return &model.TbIdentityProvider{}, err
	}
	return result, nil
}

func (i idpRepo) CountIDP(ctx context.Context, corpId string) (int64, error) {
	idp := query.Use(i.data.db).TbIdentityProvider
	return idp.WithContext(ctx).Where(idp.CorpID.Eq(corpId)).Count()
}

func (i idpRepo) SetWxWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error {
	accessTokenKey := fmt.Sprintf("%v%v", dto.WxWebAccessTokenPrefix, providerId)
	return i.data.rdb.Set(ctx, accessTokenKey, accessToken, ttl).Err()
}

func (i idpRepo) GetWxWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error) {
	accessTokenKey := fmt.Sprintf("%v%v", dto.WxWebAccessTokenPrefix, providerId)
	result, err := i.data.rdb.Get(ctx, accessTokenKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", pb.ErrorRecordNotFound("access token not in cache. providerId=%v", providerId)
		}
		return "", err
	}
	return result, nil
}

func (i idpRepo) SetFsWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error {
	accessTokenKey := fmt.Sprintf("%v%v", dto.FsWebAccessTokenPrefix, providerId)
	return i.data.rdb.Set(ctx, accessTokenKey, accessToken, ttl).Err()
}

func (i idpRepo) GetFsWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error) {
	accessTokenKey := fmt.Sprintf("%v%v", dto.FsWebAccessTokenPrefix, providerId)
	result, err := i.data.rdb.Get(ctx, accessTokenKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", pb.ErrorRecordNotFound("access token not in cache. providerId=%v", providerId)
		}
		return "", err
	}
	return result, nil
}

func (i idpRepo) GetIDPAttrValue(ctx context.Context, idpId, key string) (*model.TbIdentityProviderAttribute, error) {
	attr := query.Use(i.data.db).TbIdentityProviderAttribute
	result, err := attr.WithContext(ctx).Where(attr.ProviderID.Eq(idpId), attr.Key.Eq(key)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbIdentityProviderAttribute{}, pb.ErrorRecordNotFound("idpId=%v, %v's value not found", idpId, key)
		}
		return &model.TbIdentityProviderAttribute{}, err
	}
	return result, nil
}

func (i idpRepo) GetIDPAttr(ctx context.Context, idpId string) ([]dto.KV, error) {
	var result []dto.KV
	attr := query.Use(i.data.db).TbIdentityProviderAttribute
	err := attr.WithContext(ctx).Where(attr.ProviderID.Eq(idpId)).Select(attr.Key, attr.Value).Scan(&result)
	return result, err
}

func (i idpRepo) QueryAuthPolicy(ctx context.Context, corpId, idpId string) ([]*model.TbAuthPolicy, error) {
	var result []*model.TbAuthPolicy
	err := i.data.db.WithContext(ctx).Model(&model.TbAuthPolicy{}).
		Select("tb_auth_policy.id, tb_auth_policy.name, tb_auth_policy.enable_all_user,tb_auth_policy.group_ids, tb_auth_policy.user_ids, tb_auth_policy.corp_id,tb_auth_policy.root_group_id").
		Joins("left join tb_auth_policy_idp_mapper on tb_auth_policy_idp_mapper.policy_id = tb_auth_policy.id").
		Where("tb_auth_policy.enable = true and tb_auth_policy_idp_mapper.corp_id=? and tb_auth_policy_idp_mapper.idp_id=?", corpId, idpId).
		Order("tb_auth_policy_idp_mapper.created_at desc").Scan(&result).Error
	return result, err
}

func (i idpRepo) GetAllSourceOfBind(ctx context.Context, corpId, idpId string) ([]dto.SourceGroupResult, error) {
	var result []dto.SourceGroupResult
	m := query.Use(i.data.db).TbIdpGroupMapper
	g := query.Use(i.data.db).TbUserGroup
	err := m.WithContext(ctx).Select(g.SourceID).
		LeftJoin(g, m.GroupID.EqCol(g.ID)).
		Where(m.ProviderID.Eq(idpId), m.CorpID.Eq(corpId)).
		Group(g.SourceID).Scan(&result)
	return result, err
}

func (i idpRepo) CreateIDP(ctx context.Context, param dto.CreateIDPDaoParam) error {
	q := query.Use(i.data.db)
	idp := q.TbIdentityProvider
	idpAttr := q.TbIdentityProviderAttribute
	return q.Transaction(func(tx *query.Query) error {
		err := idp.WithContext(ctx).Create(&model.TbIdentityProvider{
			ID:           param.Id,
			Name:         param.Name,
			SourceID:     param.SourceId,
			CorpID:       param.CorpId,
			Type:         param.Type,
			TemplateType: param.TemplateType,
			Description:  param.Description,
			Enable:       param.Enable,
		})
		if err != nil {
			return err
		}
		var entries []*model.TbIdentityProviderAttribute
		for _, a := range param.Attr {
			entries = append(entries, &model.TbIdentityProviderAttribute{
				Key:        a.Key,
				Value:      a.Value,
				ProviderID: param.Id,
			})
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         param.CorpId,
				ResourceType:   common.IdpType,
				OperationType:  common.OperateCreate,
				Representation: param.Name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			i.data.db.Create(&oplog)
		}()
		return idpAttr.WithContext(ctx).Create(entries...)
	})
}

func (i idpRepo) UpdateIDP(ctx context.Context, param dto.UpdateIDPDaoParam, corpId string) error {
	q := query.Use(i.data.db)
	return q.Transaction(func(tx *query.Query) error {
		// 更新基本信息
		idp := tx.TbIdentityProvider
		idpAttr := tx.TbIdentityProviderAttribute
		_, err := idp.WithContext(ctx).Select(idp.Name, idp.Description).Where(idp.ID.Eq(param.Id)).Updates(&model.TbIdentityProvider{
			Name:        param.Name,
			Description: param.Description,
		})
		if err != nil {
			return err
		}
		if _, err := idp.WithContext(ctx).Where(idp.ID.Eq(param.Id)).
			UpdateColumnSimple(idp.Enable.Value(param.Enable)); err != nil {
			return err
		}

		// 更新属性
		for _, kv := range param.Attr {
			if _, err := idpAttr.WithContext(ctx).
				Where(idpAttr.ProviderID.Eq(param.Id), idpAttr.Key.Eq(kv.Key)).
				Updates(&model.TbIdentityProviderAttribute{
					Value: kv.Value,
				}); err != nil {
				return err
			}
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.IdpType,
				OperationType:  common.OperateUpdate,
				Representation: param.Name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			i.data.db.Create(&oplog)
		}()
		return nil
	})
}

func (i idpRepo) BindIDPAndGroups(ctx context.Context, corpId, idpId string, groupIds []string) error {
	m := query.Use(i.data.db).TbIdpGroupMapper
	var idpGroups []*model.TbIdpGroupMapper
	for _, g := range groupIds {
		idpGroups = append(idpGroups, &model.TbIdpGroupMapper{
			ProviderID: idpId,
			GroupID:    g,
			CorpID:     corpId,
		})
	}

	return m.WithContext(ctx).Create(idpGroups...)
}

func (i idpRepo) GetIDPBindGroup(ctx context.Context, corpId, idpId string) ([]*model.TbUserGroup, error) {
	q := query.Use(i.data.db)
	m := q.TbIdpGroupMapper
	ug := q.TbUserGroup

	var groups []*model.TbUserGroup
	err := m.WithContext(ctx).LeftJoin(ug, m.GroupID.EqCol(ug.ID)).
		Where(m.CorpID.Eq(corpId), m.ProviderID.Eq(idpId)).Select(ug.ALL).Scan(&groups)
	return groups, err
}

func (i idpRepo) GetIDPByName(ctx context.Context, corpId, name string) (*model.TbIdentityProvider, error) {
	idp := query.Use(i.data.db).TbIdentityProvider
	result, err := idp.WithContext(ctx).Where(idp.Name.Eq(name), idp.CorpID.Eq(corpId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbIdentityProvider{}, pb.ErrorRecordNotFound("corpId=%v, name=%v not found", corpId, name)
		}
		return &model.TbIdentityProvider{}, err
	}
	return result, nil
}

func (i idpRepo) GetIDP(ctx context.Context, corpId, id string) (*model.TbIdentityProvider, error) {
	idp := query.Use(i.data.db).TbIdentityProvider
	result, err := idp.WithContext(ctx).Where(idp.ID.Eq(id), idp.CorpID.Eq(corpId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbIdentityProvider{}, pb.ErrorRecordNotFound("not found, corpId=%v, id=%v", corpId, id)
		}
		return &model.TbIdentityProvider{}, err
	}
	return result, nil
}

// GetIDPByType 根据类型查询IDP，如果有多个则返回第一个（按创建时间倒序）
func (i idpRepo) GetIDPByType(ctx context.Context, corpId, idpType string) (*model.TbIdentityProvider, error) {
	idp := query.Use(i.data.db).TbIdentityProvider
	result, err := idp.WithContext(ctx).Where(idp.CorpID.Eq(corpId), idp.Type.Eq(idpType), idp.Enable.Is(true)).Order(idp.CreatedAt.Desc()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbIdentityProvider{}, pb.ErrorRecordNotFound("IDP not found, corpId=%v, type=%v", corpId, idpType)
		}
		return &model.TbIdentityProvider{}, err
	}
	return result, nil
}

func (i idpRepo) CountIDPInSource(ctx context.Context, corpId, sourceId string) (int64, error) {
	idp := query.Use(i.data.db).TbIdentityProvider
	return idp.WithContext(ctx).Where(idp.SourceID.Eq(sourceId), idp.CorpID.Eq(corpId)).Count()
}

func (i idpRepo) CountIDPInType(ctx context.Context, corpId, idpType string) (int64, error) {
	idp := query.Use(i.data.db).TbIdentityProvider
	return idp.WithContext(ctx).Where(idp.CorpID.Eq(corpId), idp.Type.Eq(idpType), idp.IsDefault.Is(false)).Count()
}

func (i idpRepo) ListIDPWithGroup(ctx context.Context, corpId string, limit, offset int) ([]dto.IDPWithGroup, error) {
	var result []dto.IDPWithGroup
	q := query.Use(i.data.db)
	idp := q.TbIdentityProvider
	mapper := q.TbIdpGroupMapper
	group := q.TbUserGroup
	template := q.TbIdentityProviderTemplate
	err := idp.WithContext(ctx).LeftJoin(mapper, idp.ID.EqCol(mapper.ProviderID)).LeftJoin(group, group.ID.EqCol(mapper.GroupID)).
		LeftJoin(template, template.Type.EqCol(idp.Type)).
		Where(idp.CorpID.Eq(corpId)).Or(idp.CorpID.Eq(corpId), mapper.ProviderID.IsNull()).
		Limit(limit).Offset(offset).Order(idp.CreatedAt.Desc()).
		Select(idp.ALL, template.Name.As("TypeName"), group.ID.As("GroupID"), group.Name.As("GroupName")).Scan(&result)
	return result, err
}

func (i idpRepo) ListIDPWithoutGroup(ctx context.Context, corpId string) ([]*model.TbIdentityProvider, error) {
	q := query.Use(i.data.db)
	idp := q.TbIdentityProvider
	mapper := q.TbIdpGroupMapper
	return idp.WithContext(ctx).LeftJoin(mapper, idp.ID.EqCol(mapper.ProviderID)).Where(idp.CorpID.Eq(corpId), mapper.ProviderID.IsNull()).Select(idp.ALL).Find()
}

func (i idpRepo) ListBindIDPWithAttrs(ctx context.Context, corpId string) ([]dto.IDPWithAttr, error) {
	var result []dto.IDPWithAttr
	err := i.data.db.Table("tb_auth_policy").
		Select("tb_identity_provider.*,tb_identity_provider_attribute.key,tb_identity_provider_attribute.value").
		Joins("join tb_auth_policy_idp_mapper on tb_auth_policy.id = tb_auth_policy_idp_mapper.policy_id and tb_auth_policy.enable is true and tb_auth_policy.corp_id = ?", corpId).
		Joins("join tb_identity_provider on tb_auth_policy_idp_mapper.idp_id = tb_identity_provider.id AND tb_identity_provider.enable is true AND tb_identity_provider.type not in (?)", dto.AssistIDPType).
		Joins("join tb_identity_provider_attribute on tb_identity_provider.id = tb_identity_provider_attribute.provider_id").
		Scan(&result)
	return result, err.Error
}

func (i idpRepo) TemplateAttr(ctx context.Context) ([]*model.TbIdpTemplateAttribute, error) {
	q := query.Use(i.data.db)
	m := q.TbIdpTemplateAttribute

	var attr []*model.TbIdpTemplateAttribute
	err := m.WithContext(ctx).Scan(&attr)
	return attr, err
}

func NewIdpRepo(data *Data, logger log.Logger) biz.IdpRepo {
	return &idpRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
