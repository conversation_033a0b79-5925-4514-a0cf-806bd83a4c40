package service

import (
	"asdsec.com/asec/platform/app/console/app/alert_event/constants"
	"asdsec.com/asec/platform/app/console/app/alert_event/model"
	repository1 "asdsec.com/asec/platform/app/console/app/alert_event/repository"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"sync"
)

var AlertEventServiceImpl AlertEventService

// AlertEventServiceInit 单例对象
var AlertEventServiceInit sync.Once

type AlertEventService interface {
	GetAlertEventTotal(ctx *gin.Context, req model.GetAlertEventReq) ([]model.GetAlertEventRsp, error)
	GetAlertEventDetail(ctx *gin.Context, uuid string) (model.GetAlertEventDetailResp, error)
	GetAlertEventList(ctx *gin.Context, req model.GetAlertEventListReq) (modelTable.Pagination, error)
	Export(ctx *gin.Context, req model.GetAlertEventListReq) (string, map[string]string, []model.GetAlertEventListResp, error)
	UpdateScoreConfig(ctx *gin.Context, req model.UpdateScoreConfig) error
}

type alertService struct {
	db repository1.AlertEventRepository
}

func (a *alertService) UpdateScoreConfig(ctx *gin.Context, req model.UpdateScoreConfig) error {
	return a.db.UpdateScoreConfig(ctx, req)
}

func (a *alertService) Export(ctx *gin.Context, req model.GetAlertEventListReq) (string, map[string]string, []model.GetAlertEventListResp, error) {
	// title
	title := constants.AlertEventExcel
	// query condition
	searchCondition := make(map[string]string, 0)
	// result
	req.Limit = constants.MaxExportNum
	req.Offset = 0
	data, err := a.db.GetAlertEventList(ctx, req)
	if err != nil {
		return title, searchCondition, nil, err
	}
	var res *[]model.GetAlertEventListResp
	if data.Rows != nil {
		res = data.Rows.(*[]model.GetAlertEventListResp)
	}
	db, err := global.GetDBClient(ctx)
	var channelList []modelTable.ChannelType
	db.Model(modelTable.ChannelType{}).Find(&channelList)
	channelMap := make(map[string]string)
	for _, v := range channelList {
		channelMap[v.Channel] = v.ChannelName
	}
	var ret []model.GetAlertEventListResp
	for _, value := range *res {
		var item model.GetAlertEventListResp
		copier.Copy(&item, &value)
		alertSummary := fmt.Sprintf("%s 通过 %s 外发 ：%s", value.UserName, channelMap[value.ChannelType], value.FileName)
		item.AlertSummary = alertSummary
		ret = append(ret, item)
	}
	return title, searchCondition, ret, nil
}

func (a *alertService) GetAlertEventTotal(ctx *gin.Context, req model.GetAlertEventReq) ([]model.GetAlertEventRsp, error) {
	return a.db.GetAlertEventTotal(ctx, req)
}

func (a *alertService) GetAlertEventDetail(ctx *gin.Context, eventId string) (model.GetAlertEventDetailResp, error) {
	return a.db.GetAlertEventDetail(ctx, eventId)
}

func (a *alertService) GetAlertEventList(ctx *gin.Context, req model.GetAlertEventListReq) (modelTable.Pagination, error) {
	return a.db.GetAlertEventList(ctx, req)
}

func GetAlertEventService() AlertEventService {
	AlertEventServiceInit.Do(func() {
		AlertEventServiceImpl = &alertService{db: repository1.NewAppRepository()}
	})
	return AlertEventServiceImpl
}
