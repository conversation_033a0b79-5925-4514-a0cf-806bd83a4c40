package constants

const (
	WebWatermarkConf            = "web_watermark_conf"
	WebDarkWatermarkConf        = "web_dark_watermark"
	WebClearWatermarkConf       = "web_clear_watermark"
	ContentUserName             = "user_name"
	ContentTime                 = "time"
	ContentLastPhoneNumber      = "last_phone_number"
	ContentFullPhoneNumber      = "full_phone_number"
	ContentPrefixEmail          = "prefix_email"
	ContentFullEmail            = "full_email"
	ApisixGlobalRuleRoute       = "/apisix/admin/global_rules/1"
	ApisixWechatVerifyRuleRoute = "/apisix/admin/global_rules/wechatverify"
	ApisixWatermarkPlugin       = "asec-watermark"
	GateWayAppType              = 3
	HttpSchema                  = "http"
	SdpInValidError             = "SdpInValidError"
)

var WebWatermarkSupportMap = map[string]bool{
	ContentUserName:        true,
	ContentTime:            true,
	ContentFullEmail:       true,
	ContentFullPhoneNumber: true,
	ContentPrefixEmail:     true,
	ContentLastPhoneNumber: true,
}
