// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbCorp = "tb_corp"

// TbCorp mapped from table <tb_corp>
type TbCorp struct {
	ID        string    `gorm:"column:id;primaryKey" json:"id"`   // 租户id
	Name      string    `gorm:"column:name;not null" json:"name"` // 租户名
	CreatedAt time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbCorp's table name
func (*TbCorp) TableName() string {
	return TableNameTbCorp
}
