package model

import (
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"time"
)

type AlertRuleTemplate struct {
	Id             string         `gorm:"column:id" json:"id"`
	CorpId         string         `gorm:"column:corp_id" db:"corp_id" json:"corp_id"`
	Name           string         `gorm:"column:name" json:"name"`
	Description    string         `gorm:"column:description" json:"description"`
	Enable         int            `gorm:"column:enable" json:"enable"`
	EnableAnalysis int            `gorm:"column:enable_analysis" json:"enable_analysis"`
	BuiltIn        int            `gorm:"column:built_in" json:"built_in"`
	SeverityIds    pq.Int64Array  `gorm:"column:severity_ids;type:int" json:"severity_ids"`
	UserIds        pq.StringArray `gorm:"column:user_ids;type:int" json:"user_ids"`
	UserGroupIds   pq.StringArray `gorm:"column:user_group_ids;type:int" json:"user_group_ids"`
	ChannelTypes   pq.StringArray `gorm:"column:channel_types;type:string" json:"channel_types"`
	SensitiveIds   pq.StringArray `gorm:"column:sensitive_ids;type:int" json:"sensitive_ids"`
	SensitiveLevel pq.Int64Array  `gorm:"column:sensitive_level;type:int" json:"sensitive_level"`
	Time           pgtype.JSONB   `gorm:"column:time" json:"time"`
	CreateAt       time.Time      `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"create_at"`
	UpdateAt       time.Time      `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"update_at"`
}

func (AlertRuleTemplate) TableName() string {
	return "tb_alert_rule_template"
}
