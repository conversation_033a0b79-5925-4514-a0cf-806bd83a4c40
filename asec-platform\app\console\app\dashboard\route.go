package dashboard

import (
	"asdsec.com/asec/platform/app/console/app/dashboard/api"
	"github.com/gin-gonic/gin"
)

func Api(r *gin.RouterGroup) {
	v := r.Group("/v1/dashboard")
	{
		v.GET("/counts", api.GetCount)
		v.GET("/access_log/trend", api.AccessLogTopN)
		v.GET("/event_type", api.IncidentTypeTopN)
		v.GET("/outgoing", api.SendTopN)
		v.GET("/sensitive_outgoing", api.SensitiveSendTopN)
		v.GET("/access_log/data_trend", api.DataAccessLogTopN)
	}
}
