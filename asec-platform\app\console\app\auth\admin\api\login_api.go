package api

import (
	"asdsec.com/asec/platform/app/console/app/auth/admin/constants"
	"asdsec.com/asec/platform/app/console/app/auth/admin/model"
	"asdsec.com/asec/platform/app/console/app/auth/admin/service"
	"asdsec.com/asec/platform/app/console/app/user/dto"
	userService "asdsec.com/asec/platform/app/console/app/user/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	"asdsec.com/asec/platform/pkg/utils/encrypt"
	"asdsec.com/asec/platform/pkg/utils/license"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strings"
	"time"
)

// RefreshToken godoc
// @Summary 刷新token
// @Schemes
// @Description 刷新token
// @Tags        Admin
// @Produce     application/json
// @Success     200
// @Router      /v1/refresh/token [GET]
// @success     200 {object} common.Response{data=model.RefreshTokenReq} "refresh token"
func RefreshToken(ctx *gin.Context) {
	userId := web.GetCurrentAdminId(ctx)
	corpId := web.GetAdminCorpId(ctx)
	auths := strings.SplitN(ctx.GetHeader(constants.TokenAuthKey), " ", 2)
	refreshToken := auths[1]
	claim, err := web.GetCurrentAdminTokenClaim(ctx)
	if err != nil {
		global.SysLog.Error("get current claim failed. err=%v", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	req := model.RefreshTokenReq{UserId: userId, CorpId: corpId, RefreshToken: refreshToken, Claim: claim}
	token, err := service.GetLoginService().RefreshToken(ctx, req)
	if err != nil {
		global.SysLog.Error("refresh token error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, token)
}

// AdminLogin godoc
// @Summary 管理员登录接口
// @Schemes
// @Description 管理员登录接口
// @Tags        Admin
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/login [POST]
// @success     200 {object} common.Response{data=model.AdminLoginReq} "admin login"
func AdminLogin(ctx *gin.Context) {
	req := model.AdminLoginReq{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	req.CorpId = web.GetAdminCorpId(ctx)

	if req.IsEncrypt {
		decryptedPassword, err := encrypt.RsaDecrypt(req.Password)
		if err != nil {
			global.SysLog.Error("密码解密失败", zap.Error(err))
			common.Fail(ctx, common.OperateError)
			return
		}
		req.Password = decryptedPassword
	}

	defer func() {
		host := web.GetClientHost(ctx)
		admin, err := service.GetAdminService().GetAdminByCorpAndName(ctx, req.CorpId, req.Name)
		if err != nil {
			global.SysLog.Error("get login user failed", zap.Error(err))
		}
		loginLogType := constants.LoginLogType
		errMsg := ""
		if err != nil {
			loginLogType = constants.LoginLogErrType
			errMsg = err.Error()
		}

		loginLogReq := dto.AdminLoginLogReq{
			UserId:    admin.Id,
			CorpId:    req.CorpId,
			Error:     errMsg,
			IpAddress: host,
			AuthType:  constants.LoginAdminAuthType,
			ClientId:  constants.LoginClient,
			EventTime: time.Now().UnixMilli(),
			Type:      loginLogType,
		}
		err = userService.GetUserService().CreateAdminLoginLog(ctx, loginLogReq)
		if err != nil {
			global.SysLog.Error("record login log failed", zap.Error(err))
		}
	}()
	// 先获取授权信息
	licenseStatusRsp, err := service.GetLoginService().GetLicenseInfo(ctx, req.CorpId)
	if err != nil {
		global.SysLog.Error("admin login error", zap.Error(err))
		common.Fail(ctx, common.AuthorizationError)
		return
	}
	// 授权过期
	if licenseStatusRsp.LicenseStatus == license.ExpireLicenseStatus || licenseStatusRsp.LicenseStatus == license.CanNotConnectRemoteLicenseStatus {
		common.OkWithData(ctx, model.Token{LicenseStatus: licenseStatusRsp.LicenseStatus})
		return
	}

	token, err := service.GetLoginService().Login(ctx, req)
	if err != nil {
		global.SysLog.Error("admin login error", zap.Error(err))
		common.FailWithMessage(ctx, -1, err.Error())
		return
	}
	token.LicenseStatus = licenseStatusRsp.LicenseStatus
	token.AgentLimit = licenseStatusRsp.AgentLimit
	token.UsedCount = licenseStatusRsp.UsedAgentCount
	common.OkWithData(ctx, token)
}

// AdminLogOut godoc
// @Summary 管理员登出
// @Schemes
// @Description 管理员登出接口
// @Tags        Admin
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/logout [POST]
// @success     200 {object} common.Response{} "admin logout"
func AdminLogOut(ctx *gin.Context) {
	userId := web.GetCurrentAdminId(ctx)
	auths := strings.SplitN(ctx.GetHeader("Authorization"), " ", 2)
	token := auths[1]
	defer func() {
		host := web.GetClientHost(ctx)
		corpId := web.GetAdminCorpId(ctx)
		admin, err := service.GetAdminService().GetAdminById(ctx, userId, corpId)
		if err != nil {
			global.SysLog.Error("get login user failed", zap.Error(err))
		}
		loginLogType := constants.LogoutLogType
		errMsg := ""
		if err != nil {
			errMsg = err.Error()
		}
		loginLogReq := dto.AdminLoginLogReq{
			UserId:    admin.Id,
			CorpId:    corpId,
			Error:     errMsg,
			IpAddress: host,
			AuthType:  constants.LoginAdminAuthType,
			ClientId:  constants.LoginClient,
			EventTime: time.Now().UnixMilli(),
			Type:      loginLogType,
		}
		err = userService.GetUserService().CreateAdminLoginLog(ctx, loginLogReq)
		if err != nil {
			global.SysLog.Error("record login log failed", zap.Error(err))
		}
	}()
	claim, err := web.GetCurrentAdminTokenClaim(ctx)
	if err != nil {
		global.SysLog.Error("get current claim failed. err=%v", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	err = service.GetLoginService().Logout(ctx, userId, token, claim)
	if err != nil {
		global.SysLog.Error("admin logout error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}
