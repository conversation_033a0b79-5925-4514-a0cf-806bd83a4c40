// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 为某个枚举单独设置错误码
func IsRecordNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_RECORD_NOT_FOUND.String() && e.Code == 200
}

// 为某个枚举单独设置错误码
func ErrorRecordNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_RECORD_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

func IsNameConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NAME_CONFLICT.String() && e.Code == 200
}

func ErrorNameConflict(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_NAME_CONFLICT.String(), fmt.Sprintf(format, args...))
}

func IsSourceTypeConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_SOURCE_TYPE_CONFLICT.String() && e.Code == 200
}

func ErrorSourceTypeConflict(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_SOURCE_TYPE_CONFLICT.String(), fmt.Sprintf(format, args...))
}

func IsIdpSourceNumLimit(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_IDP_SOURCE_NUM_LIMIT.String() && e.Code == 200
}

func ErrorIdpSourceNumLimit(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_IDP_SOURCE_NUM_LIMIT.String(), fmt.Sprintf(format, args...))
}

func IsOnlyRootGroupCanBindIdp(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ONLY_ROOT_GROUP_CAN_BIND_IDP.String() && e.Code == 200
}

func ErrorOnlyRootGroupCanBindIdp(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_ONLY_ROOT_GROUP_CAN_BIND_IDP.String(), fmt.Sprintf(format, args...))
}

func IsMainIdpOnlyBindOneSource(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_MAIN_IDP_ONLY_BIND_ONE_SOURCE.String() && e.Code == 200
}

func ErrorMainIdpOnlyBindOneSource(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_MAIN_IDP_ONLY_BIND_ONE_SOURCE.String(), fmt.Sprintf(format, args...))
}

func IsUserEntityUniqueConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_USER_ENTITY_UNIQUE_CONFLICT.String() && e.Code == 200
}

func ErrorUserEntityUniqueConflict(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_USER_ENTITY_UNIQUE_CONFLICT.String(), fmt.Sprintf(format, args...))
}

func IsSourceTypeNotSupport(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_SOURCE_TYPE_NOT_SUPPORT.String() && e.Code == 200
}

func ErrorSourceTypeNotSupport(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_SOURCE_TYPE_NOT_SUPPORT.String(), fmt.Sprintf(format, args...))
}

func IsIdpSourceTypeError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_IDP_SOURCE_TYPE_ERROR.String() && e.Code == 200
}

func ErrorIdpSourceTypeError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_IDP_SOURCE_TYPE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsOnlyMainIdpCanBind(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ONLY_MAIN_IDP_CAN_BIND.String() && e.Code == 200
}

func ErrorOnlyMainIdpCanBind(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_ONLY_MAIN_IDP_CAN_BIND.String(), fmt.Sprintf(format, args...))
}

func IsUserSourceConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_USER_SOURCE_CONFLICT.String() && e.Code == 200
}

func ErrorUserSourceConflict(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_USER_SOURCE_CONFLICT.String(), fmt.Sprintf(format, args...))
}

func IsUserGroupDuplicate(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_USER_GROUP_DUPLICATE.String() && e.Code == 200
}

func ErrorUserGroupDuplicate(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_USER_GROUP_DUPLICATE.String(), fmt.Sprintf(format, args...))
}

func IsUserGroupCircle(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_USER_GROUP_CIRCLE.String() && e.Code == 200
}

func ErrorUserGroupCircle(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_USER_GROUP_CIRCLE.String(), fmt.Sprintf(format, args...))
}

func IsGroupNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_GROUP_NOT_FOUND.String() && e.Code == 200
}

func ErrorGroupNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_GROUP_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

func IsUserGroupConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_USER_GROUP_CONFLICT.String() && e.Code == 200
}

func ErrorUserGroupConflict(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_USER_GROUP_CONFLICT.String(), fmt.Sprintf(format, args...))
}

func IsGroupNotSupportIdp(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_GROUP_NOT_SUPPORT_IDP.String() && e.Code == 200
}

func ErrorGroupNotSupportIdp(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_GROUP_NOT_SUPPORT_IDP.String(), fmt.Sprintf(format, args...))
}

func IsAuthObjectEmpty(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_OBJECT_EMPTY.String() && e.Code == 200
}

func ErrorAuthObjectEmpty(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_AUTH_OBJECT_EMPTY.String(), fmt.Sprintf(format, args...))
}

func IsCredError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CRED_ERROR.String() && e.Code == 200
}

func ErrorCredError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_CRED_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsLoginError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_LOGIN_ERROR.String() && e.Code == 200
}

func ErrorLoginError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_LOGIN_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsAuthPolicyError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_POLICY_ERROR.String() && e.Code == 200
}

func ErrorAuthPolicyError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_AUTH_POLICY_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsTypeError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TYPE_ERROR.String() && e.Code == 200
}

func ErrorTypeError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_TYPE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsParamError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PARAM_ERROR.String() && e.Code == 200
}

func ErrorParamError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_PARAM_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsTokenExpire(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TOKEN_EXPIRE.String() && e.Code == 401
}

func ErrorTokenExpire(format string, args ...interface{}) *errors.Error {
	return errors.New(401, ErrorReason_TOKEN_EXPIRE.String(), fmt.Sprintf(format, args...))
}

func IsTokenInvalid(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TOKEN_INVALID.String() && e.Code == 401
}

func ErrorTokenInvalid(format string, args ...interface{}) *errors.Error {
	return errors.New(401, ErrorReason_TOKEN_INVALID.String(), fmt.Sprintf(format, args...))
}

func IsTokenParseFailed(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TOKEN_PARSE_FAILED.String() && e.Code == 401
}

func ErrorTokenParseFailed(format string, args ...interface{}) *errors.Error {
	return errors.New(401, ErrorReason_TOKEN_PARSE_FAILED.String(), fmt.Sprintf(format, args...))
}

func IsPassWordNotCorrect(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PASS_WORD_NOT_CORRECT.String() && e.Code == 200
}

func ErrorPassWordNotCorrect(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_PASS_WORD_NOT_CORRECT.String(), fmt.Sprintf(format, args...))
}

func IsRootSourceError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ROOT_SOURCE_ERROR.String() && e.Code == 200
}

func ErrorRootSourceError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_ROOT_SOURCE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsDisableError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DISABLE_ERROR.String() && e.Code == 200
}

func ErrorDisableError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_DISABLE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsDefaultDataConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_DEFAULT_DATA_CONFLICT.String() && e.Code == 200
}

func ErrorDefaultDataConflict(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_DEFAULT_DATA_CONFLICT.String(), fmt.Sprintf(format, args...))
}

func IsQiyewxTrustedIpError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_QIYEWX_TRUSTED_IP_ERROR.String() && e.Code == 200
}

func ErrorQiyewxTrustedIpError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_QIYEWX_TRUSTED_IP_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsQiyewxConfigError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_QIYEWX_CONFIG_ERROR.String() && e.Code == 200
}

func ErrorQiyewxConfigError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_QIYEWX_CONFIG_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsAccountOrPasswordError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ACCOUNT_OR_PASSWORD_ERROR.String() && e.Code == 200
}

func ErrorAccountOrPasswordError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_ACCOUNT_OR_PASSWORD_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsCacheError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CACHE_ERROR.String() && e.Code == 200
}

func ErrorCacheError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_CACHE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsExpireError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_EXPIRE_ERROR.String() && e.Code == 200
}

func ErrorExpireError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_EXPIRE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsFieldMapConfigError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_FIELD_MAP_CONFIG_ERROR.String() && e.Code == 200
}

func ErrorFieldMapConfigError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_FIELD_MAP_CONFIG_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsSmsCodeInvalidError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_SMS_CODE_INVALID_ERROR.String() && e.Code == 200
}

func ErrorSmsCodeInvalidError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_SMS_CODE_INVALID_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsSmsCodeError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_SMS_CODE_ERROR.String() && e.Code == 200
}

func ErrorSmsCodeError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_SMS_CODE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsAuthChainFailure(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_CHAIN_FAILURE.String() && e.Code == 200
}

func ErrorAuthChainFailure(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_AUTH_CHAIN_FAILURE.String(), fmt.Sprintf(format, args...))
}

func IsNotMainAuth(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NOT_MAIN_AUTH.String() && e.Code == 200
}

func ErrorNotMainAuth(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_NOT_MAIN_AUTH.String(), fmt.Sprintf(format, args...))
}

func IsNotPhone(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NOT_PHONE.String() && e.Code == 200
}

func ErrorNotPhone(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_NOT_PHONE.String(), fmt.Sprintf(format, args...))
}

func IsFeishuSyncError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_FEISHU_SYNC_ERROR.String() && e.Code == 200
}

func ErrorFeishuSyncError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_FEISHU_SYNC_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsSendSmsError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_SEND_SMS_ERROR.String() && e.Code == 200
}

func ErrorSendSmsError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_SEND_SMS_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsTokenVerify(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_TOKEN_VERIFY.String() && e.Code == 302
}

func ErrorTokenVerify(format string, args ...interface{}) *errors.Error {
	return errors.New(302, ErrorReason_TOKEN_VERIFY.String(), fmt.Sprintf(format, args...))
}

func IsCodeVerifyError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CODE_VERIFY_ERROR.String() && e.Code == 302
}

func ErrorCodeVerifyError(format string, args ...interface{}) *errors.Error {
	return errors.New(302, ErrorReason_CODE_VERIFY_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsParamVerifyError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PARAM_VERIFY_ERROR.String() && e.Code == 400
}

func ErrorParamVerifyError(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_PARAM_VERIFY_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsOauth2AuthorizeError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_OAUTH2_AUTHORIZE_ERROR.String() && e.Code == 302
}

func ErrorOauth2AuthorizeError(format string, args ...interface{}) *errors.Error {
	return errors.New(302, ErrorReason_OAUTH2_AUTHORIZE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsOauth2CallbackError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_OAUTH2_CALLBACK_ERROR.String() && e.Code == 302
}

func ErrorOauth2CallbackError(format string, args ...interface{}) *errors.Error {
	return errors.New(302, ErrorReason_OAUTH2_CALLBACK_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsUserNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_USER_NOT_FOUND.String() && e.Code == 200
}

func ErrorUserNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_USER_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

func IsEmailNotExists(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_EMAIL_NOT_EXISTS.String() && e.Code == 200
}

func ErrorEmailNotExists(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_EMAIL_NOT_EXISTS.String(), fmt.Sprintf(format, args...))
}

func IsEmailFormatInvalid(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_EMAIL_FORMAT_INVALID.String() && e.Code == 200
}

func ErrorEmailFormatInvalid(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_EMAIL_FORMAT_INVALID.String(), fmt.Sprintf(format, args...))
}

func IsEmailCodeInvalidError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_EMAIL_CODE_INVALID_ERROR.String() && e.Code == 200
}

func ErrorEmailCodeInvalidError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_EMAIL_CODE_INVALID_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsEmailCodeError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_EMAIL_CODE_ERROR.String() && e.Code == 200
}

func ErrorEmailCodeError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_EMAIL_CODE_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsEmailSendError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_EMAIL_SEND_ERROR.String() && e.Code == 200
}

func ErrorEmailSendError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_EMAIL_SEND_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsEmailAuthFailure(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_EMAIL_AUTH_FAILURE.String() && e.Code == 200
}

func ErrorEmailAuthFailure(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_EMAIL_AUTH_FAILURE.String(), fmt.Sprintf(format, args...))
}

// 认证服务器配置错误
func IsAuthServerConfigError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_SERVER_CONFIG_ERROR.String() && e.Code == 200
}

// 认证服务器配置错误
func ErrorAuthServerConfigError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_AUTH_SERVER_CONFIG_ERROR.String(), fmt.Sprintf(format, args...))
}

// 认证服务器连接错误
func IsAuthServerConnectError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_SERVER_CONNECT_ERROR.String() && e.Code == 200
}

// 认证服务器连接错误
func ErrorAuthServerConnectError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_AUTH_SERVER_CONNECT_ERROR.String(), fmt.Sprintf(format, args...))
}

// 网络连接错误
func IsNetworkConnectionError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NETWORK_CONNECTION_ERROR.String() && e.Code == 200
}

// 网络连接错误
func ErrorNetworkConnectionError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_NETWORK_CONNECTION_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsAuthSearchNotUnique(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_SEARCH_NOT_UNIQUE.String() && e.Code == 200
}

func ErrorAuthSearchNotUnique(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_AUTH_SEARCH_NOT_UNIQUE.String(), fmt.Sprintf(format, args...))
}

func IsAuthUserLock(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_USER_LOCK.String() && e.Code == 200
}

func ErrorAuthUserLock(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_AUTH_USER_LOCK.String(), fmt.Sprintf(format, args...))
}

func IsSecurityCodeError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_SECURITY_CODE_ERROR.String() && e.Code == 200
}

func ErrorSecurityCodeError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_SECURITY_CODE_ERROR.String(), fmt.Sprintf(format, args...))
}

// 客户端登录限制相关错误
func IsClientTypeForbidden(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CLIENT_TYPE_FORBIDDEN.String() && e.Code == 200
}

// 客户端登录限制相关错误
func ErrorClientTypeForbidden(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_CLIENT_TYPE_FORBIDDEN.String(), fmt.Sprintf(format, args...))
}

// 客户端数量超限
func IsClientLimitExceeded(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CLIENT_LIMIT_EXCEEDED.String() && e.Code == 200
}

// 客户端数量超限
func ErrorClientLimitExceeded(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_CLIENT_LIMIT_EXCEEDED.String(), fmt.Sprintf(format, args...))
}
