package repository

import (
	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/app/console/app/alert_rule/model"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"github.com/golang/protobuf/proto"
	"github.com/jinzhu/copier"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type AlertRuleRepository interface {
	GetAlertRuleList(ctx context.Context, req model.GetAlertRuleListReq) (modelTable.Pagination, error)
	GetAlertRuleTemplateList(ctx context.Context) ([]model.GetAlertRuleListResp, error)
	Create(ctx context.Context, req *modelTable.AlertRule) error
	CreateTemplate(ctx context.Context, req *model.AlertRuleTemplate) error
	Update(ctx context.Context, req *modelTable.AlertRule) error
	Delete(ctx context.Context, id string) error
}

const UserDefinitionKey = 2

// NewAppRepository 创建接口实现接口实现
func NewAppRepository() AlertRuleRepository {
	return &alertRuleRepository{}
}

type alertRuleRepository struct {
}

func (a *alertRuleRepository) GetAlertRuleTemplateList(ctx context.Context) ([]model.GetAlertRuleListResp, error) {
	var ret []model.GetAlertRuleListResp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(model.AlertRuleTemplate{}).
		Select("tb_alert_rule_template.*,array (select unnest (array_agg(distinct jsonb_build_object('id',ue.id,'name',ue.name)) filter(where ue.id is not null))) as user_name_list," +
			"array (select unnest (array_agg(distinct jsonb_build_object('id',kg.id,'name',kg.\"name\")) filter(where kg.id is not null))) as group_name_list," +
			"array (select unnest (array_agg(distinct tct.channel_name) filter(where tct.channel_name is not null))) as channel_type_list," +
			"array (select unnest (array_agg(distinct jsonb_build_object('id',tss.id,'rule_name',tss.rule_name,'sensitive_level',tss.sensitive_level)) filter(where tss.id is not null))) as sensitive").
		Joins("left join tb_user_entity ue on ue.id = any (tb_alert_rule_template.user_ids)").
		Joins("left join tb_user_group kg on kg.id = any (tb_alert_rule_template.user_group_ids)").
		Joins("left join tb_channel_type tct on tct.channel  = any (tb_alert_rule_template.channel_types)").
		Joins("left join tb_sensitive_strategy tss on tss.id = any (tb_alert_rule_template.sensitive_ids)").
		Group("tb_alert_rule_template.id").Order("tb_alert_rule_template.created_at desc").Find(&ret).Error
	return ret, err
}

func (a *alertRuleRepository) CreateTemplate(ctx context.Context, req *model.AlertRuleTemplate) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	id, err := snowflake.Sf.GetId()
	tmp := model.AlertRuleTemplate{
		Id:             strconv.Itoa(int(id)),
		CorpId:         "",
		Name:           req.Name,
		Description:    req.Description,
		CreateAt:       time.Now(),
		UpdateAt:       time.Now(),
		Enable:         1,
		EnableAnalysis: 1,
		BuiltIn:        2,
		SensitiveIds:   req.SensitiveIds,
		SeverityIds:    req.SeverityIds,
		SensitiveLevel: req.SensitiveLevel,
		ChannelTypes:   req.ChannelTypes,
		UserGroupIds:   req.UserGroupIds,
		UserIds:        req.UserIds,
		Time:           req.Time,
	}
	result := db.Model(&model.AlertRuleTemplate{}).Create(&tmp)
	return result.Error
}

func (a *alertRuleRepository) GetAlertRuleList(ctx context.Context, req model.GetAlertRuleListReq) (retPage modelTable.Pagination, err error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return modelTable.Pagination{}, err
	}
	db = db.Model(modelTable.AlertRule{}).
		Select("tb_alert_rule.*,array (select unnest (array_agg(distinct jsonb_build_object('id',ue.id,'name',CASE WHEN (ue.display_name is null or ue.display_name ='') THEN ue.name ELSE ue.display_name END, 'path', ue.path)) filter(where ue.id is not null))) as user_name_list," +
			"array (select unnest (array_agg(distinct jsonb_build_object('id',kg.id,'name',kg.\"name\",'path', kg.path, 'source_type', us.source_type)) filter(where kg.id is not null))) as group_name_list," +
			"array (select unnest (array_agg(distinct tct.channel_name) filter(where tct.channel_name is not null))) as channel_type_list," +
			"array (select unnest (array_agg(distinct jsonb_build_object('id',tss.id,'rule_name',tss.rule_name,'sensitive_level',tss.sensitive_level)) filter(where tss.id is not null))) as sensitive," +
			"array (select unnest (array_agg(distinct tsc.name) filter(where tsc.name is not null))) as sensitive_category_list")
	if req.Search != "" {
		keyword := "%" + strings.ToLower(dbutil.EscapeForLike(req.Search)) + "%"
		db = db.Where("LOWER(tb_alert_rule.name) like ?", keyword).
			Or("user_ids && (select array (select unnest (array_agg(id))) as id from tb_user_entity where LOWER(name) like ? or LOWER(display_name) like ?)", keyword, keyword).
			Or("user_group_ids && (select array (select unnest (array_agg(id))) as id from tb_user_group where LOWER(name) like ?)", keyword)
	}
	db = db.Joins("left join (select ue.*, CASE WHEN ug.PATH = '/' THEN concat ( ug.PATH, ug.\"name\" ) ELSE concat ( ug.PATH, '/', ug.\"name\" )\nEND AS PATH from tb_user_entity ue INNER JOIN tb_user_group ug on ue.group_id = ug.id) ue on ue.id = any (tb_alert_rule.user_ids)").
		Joins("left join tb_user_group kg on kg.id = any (tb_alert_rule.user_group_ids)").
		Joins("left join tb_user_source us on kg.source_id = us.id").
		Joins("left join tb_channel_type tct on tct.channel  = any (tb_alert_rule.channel_types)").
		Joins("left join tb_sensitive_strategy tss on tss.id = any (tb_alert_rule.sensitive_ids)").
		Joins("left join tb_sensitive_category tsc on tsc.id = any (tb_alert_rule.sensitive_category)").
		Group("tb_alert_rule.id").Order("tb_alert_rule.created_at desc")
	if req.BuiltIn != 0 {
		db = db.Where("tb_alert_rule.built_in = ?", req.BuiltIn)
	}
	copier.Copy(&retPage, &req)
	var rsp []model.GetAlertRuleListResp
	return modelTable.Paginate(&rsp, &retPage, db)
}

func (a *alertRuleRepository) Create(ctx context.Context, req *modelTable.AlertRule) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return err
	}
	req.Id = strconv.Itoa(int(id))
	req.CreateAt = time.Now()
	req.UpdateAt = time.Now()
	req.BuiltIn = 2
	err = db.Transaction(func(tx *gorm.DB) error {
		e := tx.Model(&modelTable.AlertRule{}).Create(&req).Error
		if e != nil {
			return e
		}
		confReq, e := req2ConfReq(req, tx, conf_center.AddConf)
		if e != nil {
			return e
		}
		e = conf_center.ConfChange(confReq)
		if e != nil {
			return e
		}
		return nil
	})
	return err
}

func (a *alertRuleRepository) Update(ctx context.Context, req *modelTable.AlertRule) error {

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var builtIn int
	err = db.Model(modelTable.AlertRule{}).Select("built_in").Where("id = ?", req.Id).Find(&builtIn).Error
	if err != nil {
		return err
	}
	req.BuiltIn = builtIn
	req.UpdateAt = time.Now()
	err = db.Transaction(func(tx *gorm.DB) error {
		e := tx.Select("description").Select("*").Omit("created_at").Updates(&req).Error
		if e != nil {
			return e
		}
		e = tx.Omit("created_at").Updates(&req).Error
		if e != nil {
			return e
		}
		confReq, e := req2ConfReq(req, tx, conf_center.UpdateConf)
		if e != nil {
			return e
		}
		e = conf_center.ConfChange(confReq)
		if e != nil {
			return e
		}
		return nil
	})
	return err
}

func (a *alertRuleRepository) Delete(ctx context.Context, id string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	item := modelTable.AlertRule{
		Id: id,
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		e := tx.Model(&modelTable.AlertRule{}).Delete(&item).Error
		if e != nil {
			return e
		}
		e = conf_center.ConfChange(conf_center.ConfChangeReq{
			ConfBizId: id, ConfType: "dlp", Tx: tx, RedisCli: global.SysRedisClient,
			ChangeType: conf_center.DelConf})
		if e != nil {
			return e
		}
		return nil
	})
	return err
}

type timeReq struct {
	All  int      `json:"all"`
	Rule []string `json:"rule"`
}

func req2ConfReq(req *modelTable.AlertRule, tx *gorm.DB, changeType conf_center.ConfChangeType) (conf_center.ConfChangeReq, error) {
	granularity := conf_center.NonGlobalConf
	if (len(req.UserIds) > 0 && req.UserIds[0] == "0") || (len(req.UserGroupIds) > 0 && req.UserGroupIds[0] == "0") {
		granularity = conf_center.GlobalConf
	}
	t := timeReq{}
	err := jsoniter.Unmarshal(req.Time.Bytes, &t)
	if err != nil {
		return conf_center.ConfChangeReq{}, err
	}

	sequence := v1.TimeSequence{
		All:  uint32(t.All),
		Rule: t.Rule,
	}

	rule := v1.DlpRule{
		RuleId:             req.Id,
		RuleName:           req.Name,
		EnableAnalysis:     uint32(req.EnableAnalysis),
		ChannelTypes:       req.ChannelTypes,
		SensitiveIds:       req.SensitiveIds,
		SensitiveLevel:     req.SensitiveLevel,
		AlertType:          uint32(req.AlertType),
		GitContainOption:   uint32(req.GitContainOption),
		GitPath:            req.GitPath,
		SvnContainOption:   uint32(req.SvnContainOption),
		SvnPath:            req.SvnPath,
		DisposeAction:      uint32(req.DisposeAction),
		RulePriority:       0,
		Time:               &sequence,
		Enable:             uint32(req.Enable),
		SensitiveCategory:  req.SensitiveCategory,
		EnableNotification: req.EnableNotification,
		NotificationId:     req.NotificationId,
	}
	ruleBytes, err := proto.Marshal(&rule)
	if err != nil {
		return conf_center.ConfChangeReq{}, err
	}

	changeReq := conf_center.ConfChangeReq{
		ConfBizId:       req.Id,
		ConfType:        "dlp",
		ConfData:        ruleBytes,
		ConfGranularity: granularity,
		Tx:              tx,
		RedisCli:        global.SysRedisClient,
		ChangeType:      changeType,
	}
	if granularity == conf_center.GlobalConf {
		return changeReq, nil
	}
	relation := conf_center.ConfRelation{
		UserGroupId: req.UserGroupIds,
		UserId:      req.UserIds,
	}
	changeReq.ConfRelation = &relation
	return changeReq, nil
}
