// 表单代填插件 - APISIX静态资源版本
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        define([], factory());
    } else if (typeof module === 'object' && module.exports) {
        module.exports = factory();
    } else {
        root.FormFill = factory();
    }
}(this, function () {
    var FormFill = {
        State: 0,
        config: {},
        debug: false // 关闭调试模式，设为true开启
    };

    // 日志输出函数 - 只在debug为true时输出
    function log(message, data) {
        if (FormFill.debug === true) {
            if (data !== undefined) {
                console.log('[FormFill] ' + message, data);
            } else {
                console.log('[FormFill] ' + message);
            }
        }
        // debug为false时，完全不执行任何操作
    }

    // 查找元素的通用函数
    function findElementBySelector(selector) {
        log('尝试通过选择器查找元素: ' + selector);
        try {
            var element = document.querySelector(selector);
            if (element) {
                log('通过选择器找到元素: ' + selector, element);
                return element;
            } else {
                log('选择器未找到元素: ' + selector);
                return null;
            }
        } catch (e) {
            log('选择器查找失败: ' + selector + ', 错误: ' + e.message);
            return null;
        }
    }

    // 通过属性数组查找元素
    function findElementByArray(searchArray, elementType) {
        if (!searchArray || !Array.isArray(searchArray)) {
            log('搜索数组无效');
            return null;
        }
        
        var elements = document.querySelectorAll(elementType);
        log('搜索元素类型: ' + elementType + ', 找到 ' + elements.length + ' 个元素');
        
        var matchedElements = [];
        
        for (var i = 0; i < elements.length; i++) {
            var element = elements[i];
            
            // 跳过不可见元素
            if (element.offsetParent === null && element.style.display === 'none') {
                continue;
            }
            
            var elementId = element.id ? element.id.toLowerCase() : '';
            var elementName = element.name ? element.name.toLowerCase() : '';
            var elementClass = element.className ? element.className.toLowerCase() : '';
            var elementValue = element.value ? element.value.toLowerCase() : '';
            var elementPlaceholder = element.placeholder ? element.placeholder.toLowerCase() : '';
            
            for (var j = 0; j < searchArray.length; j++) {
                var searchTerm = searchArray[j].toLowerCase();
                if (elementId.indexOf(searchTerm) !== -1 ||
                    elementName.indexOf(searchTerm) !== -1 ||
                    elementClass.indexOf(searchTerm) !== -1 ||
                    elementValue.indexOf(searchTerm) !== -1 ||
                    elementPlaceholder.indexOf(searchTerm) !== -1) {
                    log('找到匹配元素，匹配关键词: ' + searchTerm, element);
                    matchedElements.push(element);
                    break; // 找到匹配就跳出内层循环，避免同一元素被重复添加
                }
            }
        }
        
        if (matchedElements.length === 0) {
            log('通过属性数组未找到匹配元素');
            return null;
        } else if (matchedElements.length === 1) {
            log('找到唯一匹配元素');
            return matchedElements[0];
        } else {
            log('找到多个匹配元素(' + matchedElements.length + '个)，返回第一个');
            return matchedElements[0];
        }
    }

    // 填充输入框并触发事件
    function fillInput(element, value) {
        log('尝试填充输入框: ' + (element ? element.name || element.id || '未知元素' : '空元素') + ' = ' + value);
        
        if (!element || !value) {
            log('填充输入框失败：元素或值无效 - element: ' + !!element + ', value: ' + !!value);
            return false;
        }
        
        element.value = value;
        log('填充输入框成功: ' + (element.name || element.id || '未知') + ' = ' + value);
        
        // 触发输入事件，确保表单验证和响应式更新
        var events = ['input', 'change', 'blur'];
        events.forEach(function(eventType) {
            try {
                var event = new Event(eventType, { bubbles: true });
                element.dispatchEvent(event);
                log('触发事件成功: ' + eventType);
            } catch (e) {
                // 兼容旧浏览器
                try {
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent(eventType, true, false);
                    element.dispatchEvent(event);
                    log('触发事件成功(兼容模式): ' + eventType);
                } catch (e2) {
                    log('触发事件失败: ' + eventType + ', 错误: ' + e2.message);
                }
            }
        });
        
        return true;
    }

    // Cookie操作函数
    function getCookie(name) {
        var value = "; " + document.cookie;
        var parts = value.split("; " + name + "=");
        if (parts.length == 2) return parts.pop().split(";").shift();
        return null;
    }

    function addCookie(name, value, seconds) {
        var expires = "";
        if (seconds) {
            var date = new Date();
            date.setTime(date.getTime() + (seconds * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    // 精确模式表单填充
    function customFillForm(settings) {
        log('开始精确模式表单填充');
        
        var accountInput = null;
        var passwordInput = null;
        var submitButton = null;

        // 查找账号输入框 - 优先使用精确配置，回退到默认属性
        if (settings.AccountInputType === 'fixed' && settings.AccountInputValue) {
            accountInput = findElementBySelector(settings.AccountInputValue);
            if (!accountInput) {
                log('精确配置的账号选择器未找到元素，回退到默认属性匹配');
                accountInput = findElementByArray(settings.autoAccountInputArr, 'input[type="text"],input[type="email"],input:not([type])');
            }
        } else {
            // 如果没有配置精确选择器，直接使用默认属性
            accountInput = findElementByArray(settings.autoAccountInputArr, 'input[type="text"],input[type="email"],input:not([type])');
        }

        // 查找密码输入框 - 优先使用精确配置，回退到默认属性
        if (settings.PwdInputType === 'fixed' && settings.PwdInputValue) {
            passwordInput = findElementBySelector(settings.PwdInputValue);
            if (!passwordInput) {
                log('精确配置的密码选择器未找到元素，回退到默认属性匹配');
                passwordInput = findElementByArray(settings.autoPwdInputArr, 'input[type="password"]');
            }
        } else {
            // 如果没有配置精确选择器，直接使用默认属性
            passwordInput = findElementByArray(settings.autoPwdInputArr, 'input[type="password"]');
        }

        // 查找提交按钮 - 优先使用精确配置，回退到默认属性
        if (settings.SubmitInputType === 'fixed' && settings.SubmitInputValue) {
            submitButton = findElementBySelector(settings.SubmitInputValue);
            if (!submitButton) {
                log('精确配置的提交按钮选择器未找到元素，回退到默认属性匹配');
                submitButton = findElementByArray(settings.autoSubmitInputArr, 'button,input[type="submit"],input[type="button"]');
            }
        } else {
            // 如果没有配置精确选择器，直接使用默认属性
            submitButton = findElementByArray(settings.autoSubmitInputArr, 'button,input[type="submit"],input[type="button"]');
        }

        var accountFilled = false;
        var passwordFilled = false;
        
        // 填充账号
        if (accountInput && settings.AccountDataValue) {
            accountFilled = fillInput(accountInput, settings.AccountDataValue);
        }
        
        // 填充密码
        if (passwordInput && settings.PwdDataValue) {
            passwordFilled = fillInput(passwordInput, settings.PwdDataValue);
        }

        // 精确模式的自动登录逻辑（需要检查AutoLogin开关）
        if (settings.AutoLogin === '1' && submitButton && accountFilled && passwordFilled) {
            var autoLoginPed = parseInt(settings.AutoLoginPed) || 300;
            var lastLoginTime = getCookie('asec_last_login_time') || '0';
            var currentTime = Math.floor(Date.now() / 1000);
            
            log('精确模式自动登录检查 - AutoLogin: ' + settings.AutoLogin + ', AutoLoginPed: ' + autoLoginPed);
            
            if (currentTime - parseInt(lastLoginTime) > autoLoginPed) {
                log('精确模式满足自动登录条件，延迟执行');
                setTimeout(function() {
                    if (accountInput.value && passwordInput.value) {
                        addCookie('asec_last_login_time', currentTime.toString(), autoLoginPed);
                        log('执行精确模式自动登录');
                        submitButton.click();
                    }
                }, 1000);
            } else {
                log('精确模式自动登录间隔未到，跳过自动提交。剩余时间: ' + (autoLoginPed - (currentTime - parseInt(lastLoginTime))) + '秒');
            }
        } else {
            log('精确模式自动登录条件不满足 - AutoLogin: ' + settings.AutoLogin + ', submitButton: ' + !!submitButton + ', accountFilled: ' + accountFilled + ', passwordFilled: ' + passwordFilled);
        }
        
        return accountFilled && passwordFilled;
    }

    // 智能模式表单填充
    function autoFillForm(settings) {
        log('开始智能模式表单填充');
        
        var accountInput = findElementByArray(settings.autoAccountInputArr, 'input[type="text"],input[type="email"],input:not([type])');
        var passwordInput = findElementByArray(settings.autoPwdInputArr, 'input[type="password"]');
        var submitButton = findElementByArray(settings.autoSubmitInputArr, 'button,input[type="submit"],input[type="button"]');

        if (!accountInput || !passwordInput) {
            log('智能模式未找到必要的表单元素');
            return false;
        }

        var accountFilled = false;
        var passwordFilled = false;
        
        // 填充账号
        if (settings.AccountDataValue) {
            accountFilled = fillInput(accountInput, settings.AccountDataValue);
        }
        
        // 填充密码
        if (settings.PwdDataValue) {
            passwordFilled = fillInput(passwordInput, settings.PwdDataValue);
        }

        // 智能模式的自动登录逻辑
        if (settings.AutoLogin === '1' && accountFilled && passwordFilled && submitButton) {
            var autoLoginPed = parseInt(settings.AutoLoginPed) || 300;
            var lastLoginTime = getCookie('asec_last_login_time') || '0';
            var currentTime = Math.floor(Date.now() / 1000);
            
            log('智能模式自动登录检查 - AutoLogin: ' + settings.AutoLogin + ', AutoLoginPed: ' + autoLoginPed);
            
            if (currentTime - parseInt(lastLoginTime) > autoLoginPed) {
                log('满足自动登录条件，延迟执行');
                setTimeout(function() {
                    // 再次验证元素和值存在
                    if (accountInput.value && passwordInput.value) {
                        var currentSubmitButton = findElementByArray(settings.autoSubmitInputArr, 'button,input[type="submit"],input[type="button"]');
                        if (currentSubmitButton) {
                            addCookie('asec_last_login_time', currentTime.toString(), autoLoginPed);
                            log('执行智能模式自动登录');
                            currentSubmitButton.click();
                        }
                    }
                }, 1000);
            } else {
                log('自动登录间隔未到，跳过自动提交。剩余时间: ' + (autoLoginPed - (currentTime - parseInt(lastLoginTime))) + '秒');
            }
        } else {
            log('智能模式自动登录条件不满足 - AutoLogin: ' + settings.AutoLogin + ', accountFilled: ' + accountFilled + ', passwordFilled: ' + passwordFilled + ', submitButton: ' + !!submitButton);
        }
        
        return accountFilled && passwordFilled;
    }

    // 主填充逻辑
    function loadForm(settings) {
        log('开始加载表单填充，模式: ' + settings.APPSubType);
        
        var success = false;
        if (settings.APPSubType === 'custom_fill') {
            success = customFillForm(settings);
        } else {
            success = autoFillForm(settings);
        }
        
        if (success) {
            FormFill.State = 1;
            log('表单填充完成');
        } else {
            log('表单填充失败');
        }
        
        return success;
    }

    // URL匹配检查（支持多个URL，换行符分隔）
    function checkURLMatch(loginUrl) {
        log('开始URL匹配检查');
        log('当前URL: ' + window.location.href);
        log('配置的登录URL: ' + loginUrl);
        
        if (!loginUrl || loginUrl === "") {
            log('登录URL为空，跳过URL匹配');
            return true;
        }
        
        var currentUrl = window.location.href;
        
        // 处理包含换行符的多个登录URL
        var loginUrls = loginUrl.split(/[\r\n]+/);
        log('解析出 ' + loginUrls.length + ' 个登录URL');
        
        for (var i = 0; i < loginUrls.length; i++) {
            var singleLoginUrl = loginUrls[i].trim();
            if (singleLoginUrl === "") continue;
            
            log('检查单个URL: ' + currentUrl + ' vs ' + singleLoginUrl);
            
            // 简单的包含检查
            if (currentUrl.indexOf(singleLoginUrl) !== -1 || singleLoginUrl.indexOf(currentUrl) !== -1) {
                log('URL包含匹配成功: ' + singleLoginUrl);
                return true;
            }
            
            try {
                var currentPath = new URL(currentUrl).pathname;
                var loginPath = new URL(singleLoginUrl).pathname;
                log('路径比较: ' + currentPath + ' vs ' + loginPath);
                
                if (currentPath === loginPath) {
                    log('URL路径精确匹配成功: ' + singleLoginUrl);
                    return true;
                }
            } catch (e) {
                log('URL解析失败，继续检查下一个URL: ' + singleLoginUrl + ', 错误: ' + e.message);
                continue;
            }
        }
        
        log('所有URL匹配都失败');
        return false;
    }

    // DOM就绪检查
    function pageReady(fn) {
        log('检查DOM状态: ' + document.readyState);
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            log('DOM已就绪，立即执行');
            setTimeout(fn, 1);
        } else {
            log('等待DOM加载完成');
            document.addEventListener('DOMContentLoaded', function() {
                log('DOMContentLoaded事件触发');
                fn();
            });
        }
    }

    // 主初始化函数
    FormFill.init = function (settings) {
        log('===== FormFill初始化开始 =====');
        log('接收到的配置:', settings);
        
        FormFill.config = settings || {};
        
        if (!settings || !settings.APPType) {
            log('配置无效，终止初始化 - 缺少APPType');
            return;
        }
        log('APPType验证通过: ' + settings.APPType);
        
        // 添加自动登录相关配置的调试信息
        log('自动登录配置 - AutoLogin: ' + settings.AutoLogin + ', AutoLoginPed: ' + settings.AutoLoginPed);
        
        if (!settings.AccountDataValue || !settings.PwdDataValue) {
            log('未获取到登录凭证，终止表单填充');
            log('AccountDataValue: ' + !!settings.AccountDataValue + ', PwdDataValue: ' + !!settings.PwdDataValue);
            return;
        }
        log('登录凭证验证通过');
        
        if (!checkURLMatch(settings.LoginUrl)) {
            log('URL不匹配，终止填充');
            return;
        }
        log('URL匹配验证通过');
        
        log('所有验证通过，等待DOM加载完成');
        pageReady(function() {
            log('DOM加载完成，准备开始表单填充，延迟1秒执行');
            // 等待页面渲染完成
            setTimeout(function() {
                log('开始执行表单填充逻辑');
                loadForm(FormFill.config);
            }, 1000);
        });
    };

    return FormFill;
}));
