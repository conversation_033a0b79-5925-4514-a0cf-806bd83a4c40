package common

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"database/sql"
)

const (
	FileEventTypeOffset  = "file-events"
	DlpAlertOffset       = "dlp-alert"
	ScanTaskUploadOffset = "scan_task"
	// 上传等待间隔
	Duration = 10
	// 单次上传最大条数
	Limit = 500
)

func UpdateOffset(offset int64, offsetType string) error {
	prepare, err := global.OffsetSqliteClient.Prepare("update tb_events_offset set current_offset = ? where event_type = ?")
	defer global.CloseStatement(prepare)
	if err != nil {
		return err
	}
	_, err = prepare.Exec(offset, offsetType)
	return err
}

func UpdateOffsetTx(offset int64, offsetType string) error {
	prepare, err := global.OffsetSqliteClient.Prepare("update tb_events_offset set current_offset = ? where event_type = ?")
	defer global.CloseStatement(prepare)
	if err != nil {
		return err
	}

	_, err = prepare.Exec(offset, offsetType)
	return err
}

func GetOffset(offsetType string) (int64, error) {
	query, err := global.OffsetSqliteClient.Prepare("select current_offset from tb_events_offset where event_type = ?")
	defer global.CloseStatement(query)
	if err != nil {
		return 0, err
	}
	var offset int64
	err = query.QueryRow(offsetType).Scan(&offset)
	if err != nil {
		//记录不存在
		if err == sql.ErrNoRows {
			//插入的错误忽略掉
			_, err = global.OffsetSqliteClient.Exec("INSERT into tb_events_offset (current_offset,event_type) values (? ,?)", 0, offsetType)
			global.Logger.Sugar().Infof("INSERT init offset result: %v", err)
			return 0, nil
		}
		return 0, err
	}

	return offset, err
}

func SelectOneCount(cli *sql.DB, sql string) (int64, error) {
	var count int64
	err := cli.QueryRow(sql).Scan(&count)
	if err != nil {
		return 0, err
	}
	return count, nil
}
