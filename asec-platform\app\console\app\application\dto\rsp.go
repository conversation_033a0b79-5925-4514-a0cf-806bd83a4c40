package dto

import (
	"asdsec.com/asec/platform/pkg/model"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
)

type ApplicationDetailRsp struct {
	model.TenantModel
	AppName          string            `gorm:"column:app_name;type:varchar;comment:应用名称" json:"app_name"`
	AppDescribe      string            `gorm:"column:app_describe;type:varchar;comment:应用描述" json:"app_desc"`
	WebUrl           string            `gorm:"column:web_url;type:varchar;comment:web入口" json:"web_url"`
	IconUrl          string            `gorm:"column:icon_url;type:varchar;comment:图标url" json:"icon_url" `
	Maintenance      int               `gorm:"column:maintenance;type:varchar;comment:状态" json:"maintenance"`
	AppStatus        int               `gorm:"column:app_status;type:int4;comment:应用状态(1启用/2禁用)" json:"app_status"`
	GroupName        string            `gorm:"column:group_name" json:"group_name"`
	AppAddressesByte pgtype.JSONBArray `gorm:"column:app_addresses;type:[]jsonb" json:"-"`
	AppAddresses     []AppAddress      `gorm:"-" json:"app_addresses"`
	BindSE           []SEInfo          `gorm:"-" json:"bind_se"`

	GroupInfo               pgtype.JSONBArray `gorm:"column:group_info;type:[]jsonb" json:"-"`
	ServerAddress           string            `gorm:"column:server_address;type:varchar;comment:服务器地址" json:"server_address"`
	ServerSchema            string            `gorm:"column:server_schema;type:varchar;comment:服务器地址协议" json:"server_schema"`
	PublishAddress          string            `gorm:"column:publish_address;type:varchar;comment:发布地址" json:"publish_address"`
	PublishSchema           string            `gorm:"column:publish_schema;type:varchar;comment:发布地址协议" json:"publish_schema"`
	WebCompatibleConfigByte []byte            `gorm:"column:web_compatible_config;type:jsonb;comment:web兼容性配置" json:"-"`
	HealthConfigByte        []byte            `gorm:"column:health_config;type:jsonb;comment:健康检查配置" json:"-"`
	OpenConfigByte          []byte            `gorm:"column:open_config;type:jsonb;comment:打开方式配置" json:"-"`
	AppType                 string            `gorm:"app_type" json:"app_type"`
	WebCompatibleConfig     CompatibleConfig  `gorm:"-" json:"web_compatible_config"`
	GroupRelationship       []GroupInfo       `gorm:"-" json:"group_relationship"`
	Uri                     string            `gorm:"column:uri;type:varchar;comment:应用地址路径" json:"uri"`
	IdpId                   string            `gorm:"column:idp_id;type:varchar;comment:idp_id" json:"idp_id"`
	HealthConfig            HealthConfig      `gorm:"-" json:"health_config"`
	OpenConfig              OpenConfig        `gorm:"-" json:"open_config"`
	ShowStatus              int               `gorm:"column:show_status;type:int4;comment:应用展示状态" json:"show_status"`
	PortalShowName          string            `gorm:"column:portal_show_name;type:varchar;comment:应用展示名称" json:"portal_show_name"`
	PortalDesc              string            `gorm:"column:portal_desc;type:varchar;comment:应用展描述" json:"portal_desc"`
	CertificateID           string            `gorm:"column:certificate_id;type:varchar;comment:证书ID" json:"certificate_id"`
}

type AppAddress struct {
	Address  string `json:"address"`
	Port     string `json:"port"`
	Protocol string `json:"protocol"`
}

type GroupInfo struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type GetApplicationListRsp struct {
	model.CommonPage
	Data []ApplicationGroupItem `json:"data"`
}
type ApplicationGroupItem struct {
	Id                      string                 `gorm:"id" json:"id"`
	Name                    string                 `gorm:"name" json:"name"`
	AppName                 string                 `gorm:"app_name" json:"app_name"`
	AppStatus               int                    `gorm:"column:app_status;type:varchar;" json:"app_status"`
	ApplicationAddress      pq.StringArray         `gorm:"application_address;type:varchar" json:"application_address"`
	PublishEndpoint         string                 `gorm:"publish_endpoint" json:"publish_endpoint"`
	SdpList                 pq.StringArray         `gorm:"sdp_list;type:varchar" json:"sdp_list"`
	OldSdpList              pq.StringArray         `gorm:"old_sdp_list;type:varchar" json:"old_sdp_list"`
	AppType                 string                 `gorm:"app_type" json:"app_type"`
	AccessStrategy          []AccessStrategy       `gorm:"-" json:"access_strategy"`
	AccessStrategyByte      pgtype.JSONBArray      `gorm:"access_strategy_byte;type:[]jsonb" json:"-"`
	GroupIds                pq.Int64Array          `gorm:"column:group_ids;type:varchar;comment:应用标签" json:"group_ids"`
	Type                    string                 `gorm:"-" json:"type"`
	ChildGroup              []ApplicationGroupItem `gorm:"-" json:"child_group"`
	PortalShowName          string                 `json:"portal_show_name"`
	Uri                     string                 `json:"uri"`
	WebCompatibleConfigByte []byte                 `gorm:"column:web_compatible_config;type:jsonb;comment:web兼容性配置" json:"-"`
	WebCompatibleConfig     CompatibleConfig       `gorm:"-" json:"web_compatible_config"`
	PortalDesc              string                 `json:"portal_desc"`
	ShowStatus              int                    `json:"show_status"`
	CertificateID           string                 `gorm:"column:certificate_id;type:varchar;comment:证书ID" json:"certificate_id"`
}

type AppDownload struct {
	AppType        string `gorm:"app_type" excelColumn:"A" excelDesc:"App.AppData.AppType" excelWidth:"30" json:"app_type"`
	Name           string `gorm:"name" excelColumn:"B" excelDesc:"App.AppData.Name" excelWidth:"20" json:"name"`
	AppStatus      string `gorm:"app_status" excelColumn:"C" excelDesc:"App.AppData.AppStatus" json:"app_status"`
	SdpList        string `gorm:"sdp_list" excelColumn:"D" excelDesc:"App.AppData.SdpList" excelWidth:"30" json:"sdp_list"`
	ServerAddress  string `gorm:"server_address" excelColumn:"E" excelDesc:"App.Template.ServerAddress" excelWidth:"20" json:"server_address"` //服务器地址
	PublishAddress string `gorm:"publish_address" excelColumn:"F" excelDesc:"App.Template.PublishAddress" excelWidth:"20" json:"publish_address"`
	Uri            string `gorm:"uri" excelColumn:"G" excelDesc:"App.Template.Uri" excelWidth:"20" json:"uri"`
	SmartRewrite   string `gorm:"smart_rewrite" excelColumn:"H" excelDesc:"App.Template.SmartRewrite" excelWidth:"20" json:"smart_rewrite"`
	DependSite     string `gorm:"depend_site" excelColumn:"I" excelDesc:"App.Template.DependSite" excelWidth:"20" json:"depend_site"`
	ShowStatus     string `gorm:"show_status" excelColumn:"J" excelDesc:"App.Template.ShowStatus" excelWidth:"20" json:"show_status"`               //应用是否可见
	PortalShowName string `gorm:"portal_show_name" excelColumn:"K" excelDesc:"App.Template.PortalShowName" excelWidth:"20" json:"portal_show_name"` //门户展示名
	PortalDesc     string `gorm:"portal_desc" excelColumn:"L" excelDesc:"App.Template.PortalDesc" excelWidth:"20" json:"portal_desc"`               //描述
}

type ApplicationItem struct {
}

type AccessStrategy struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type GetAppGroupListRsp struct {
	Id         string               `gorm:"id" json:"id"`
	Name       string               `gorm:"name" json:"name"`
	AppCount   int                  `gorm:"app_count" json:"app_count"`
	ChildGroup []GetAppGroupListRsp `gorm:"-" json:"child_group"`
	IsDefault  bool                 `gorm:"is_default" json:"is_default"`
}

type GroupAppMap struct {
	GroupId   int64         `gorm:"group_id" json:"group_id"`
	GroupName string        `gorm:"group_name" json:"group_name"`
	AppIds    pq.Int64Array `gorm:"app_ids;type:bigInt" json:"app_ids"`
}

// 证书信息结构体
type CertificateInfo struct {
	ID        string         `json:"id" gorm:"column:id"`
	Name      string         `json:"name" gorm:"column:name"`
	Domain    pq.StringArray `json:"domain" gorm:"column:domain;type:varchar[]"`
	CreatedAt string         `json:"created_at" gorm:"column:created_at"`
	UpdatedAt string         `json:"updated_at" gorm:"column:updated_at"`
}

// 证书列表响应结构体
type CertificateListResponse struct {
	Data  []CertificateInfo `json:"data"`
	Total int64             `json:"total"`
}
