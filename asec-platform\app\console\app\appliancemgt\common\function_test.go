package common

import (
	"testing"
)

func TestCheckVersion(t *testing.T) {
	type args struct {
		cloudVer string
		reqVer   string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// 仅比对版本，通道的控制由dev-mode，oss控制
		{name: "1", args: args{cloudVer: "1.0.8", reqVer: "1.0.8"}, want: false},
		{name: "2", args: args{cloudVer: "1.0.8", reqVer: "1.0.7"}, want: true},
		{name: "3", args: args{cloudVer: "1.0.1.19-custom-hcit Build15(20230704)", reqVer: "1.0.1.19-custom-hcit Build11(20230504)"}, want: true},
		{name: "4", args: args{cloudVer: "1.0.1.19-custom-hcit Build15(20230704)", reqVer: "1.0.8"}, want: true},
		{name: "5", args: args{cloudVer: "1.0.1.19-custom-hcit Build11(20230504)", reqVer: "1.0.1.19-custom-hcit Build15(20230704)"}, want: false},
		{name: "6", args: args{cloudVer: "1.0.1.19-custom-hcit Build11(20230504)", reqVer: "1.0.1.19-custom-hcit"}, want: true},
		{name: "7", args: args{cloudVer: "1.0.1.19-custom-hcit", reqVer: "1.0.1.19-custom-hcit Build11(20230504)"}, want: false},
		{name: "8", args: args{cloudVer: "1.0.1.19-custom-hcit Build11", reqVer: "1.0.1.19-custom-hcit Build9"}, want: true},
		//大版本号不同，请求版本大版本号高于平台版本号
		{name: "8", args: args{cloudVer: "1.0.9.0-dev Build4", reqVer: "1.0.10.0-dev Build20"}, want: false},
		//大版本号不同，请求版本大版本号低于平台版本号
		{name: "8", args: args{cloudVer: "1.0.11.0-dev Build4", reqVer: "1.0.10.0-dev Build20"}, want: true},
		{name: "8", args: args{cloudVer: "1.0.9.0-dev Build4", reqVer: "1.0.9.0-dev Build20"}, want: false},
		{name: "9", args: args{cloudVer: "1.0.12.0-custom-pbwear Build5", reqVer: "1.0.12.0-dev Build65"}, want: true},
		//大版本号相同，build号请求build大于平台build，不升级
		{name: "9", args: args{cloudVer: "********-dev Build 75", reqVer: "********-dev Build80"}, want: false},
		{name: "9", args: args{cloudVer: "********-dev Build 75", reqVer: "********-dev Build80"}, want: false},
		{name: "9", args: args{cloudVer: "********-dev Build75", reqVer: "********-dev Build80"}, want: false},
		{name: "9", args: args{cloudVer: "********-dev Build75", reqVer: "********-dev build75"}, want: false},
		{name: "9", args: args{cloudVer: "********-dev build75", reqVer: "********-dev Build75"}, want: false},
		{name: "9", args: args{cloudVer: "********-dev build75", reqVer: "********-dev Build43"}, want: true},
		{name: "9", args: args{cloudVer: "********-dev Build75", reqVer: "********-dev build43"}, want: true},
		//大版本号相同，build号请求build小于平台build，升级
		{name: "9", args: args{cloudVer: "********-dev Build 60", reqVer: "********-dev Build40"}, want: false},
		{name: "9", args: args{cloudVer: "********-dev Build22", reqVer: "********-dev build22"}, want: false},
		{name: "9", args: args{cloudVer: "********-dev Build79", reqVer: "********-dev Build1"}, want: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CheckNewVersion(tt.args.cloudVer, tt.args.reqVer); got != tt.want {
				t.Errorf("CheckNewVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsIPAddress(t *testing.T) {
	type args struct {
		addr string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "test", args: args{addr: "sdp.aierchina.com"}, want: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsIPAddress(tt.args.addr); got != tt.want {
				t.Errorf("IsIPAddress() = %v, want %v", got, tt.want)
			}
		})
	}
}
