package server

import (
	http2 "net/http"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/dto"

	"github.com/go-kratos/kratos/v2/encoding"

	adminPb "asdsec.com/asec/platform/api/auth/v1/admin"
	authPb "asdsec.com/asec/platform/api/auth/v1/auth"
	oidcPb "asdsec.com/asec/platform/api/auth/v1/oidc"
	userPb "asdsec.com/asec/platform/api/auth/v1/user"
	"asdsec.com/asec/platform/app/auth/internal/conf"
	custom_middleware "asdsec.com/asec/platform/app/auth/internal/custom-middleware"
	"asdsec.com/asec/platform/app/auth/internal/service"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/go-kratos/swagger-api/openapiv2"
	"github.com/gorilla/handlers"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

const (
	OIDCName = "oidc_codec"
)

type oidcCodec struct{}

func (c *oidcCodec) Marshal(v interface{}) ([]byte, error) {
	if m, ok := v.(proto.Message); ok {
		return protojson.MarshalOptions{
			UseProtoNames: true,
		}.Marshal(m)
	}
	return encoding.GetCodec("json").Marshal(v)
}

func (c *oidcCodec) Unmarshal(data []byte, v interface{}) error {
	return encoding.GetCodec("json").Unmarshal(data, v)
}

func (c *oidcCodec) Name() string {
	return OIDCName
}

func init() {
	encoding.RegisterCodec(&oidcCodec{})
}

func oidcResponseEncoder(w http.ResponseWriter, r *http.Request, v interface{}) error {
	codec := encoding.GetCodec(OIDCName)
	data, err := codec.Marshal(v)
	if err != nil {
		return err
	}
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	_, err = w.Write(data)
	if err != nil {
		return err
	}
	return nil
}

const (
	ErrorCode = -1
)

type CustomError struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
}

// CustomErrorEncoder encodes the error to the HTTP response.
//
// 和console错误返回保持一致
//
//	{
//	 "code": -1,
//	 "msg": "参数错误",
//	}
func CustomErrorEncoder(w http.ResponseWriter, r *http.Request, err error) {
	// 出现错误http状态码为200，code为-1，方便前端标识错误
	se := errors.FromError(err)
	// 对302重定向请求特殊处理
	if se.Code == http2.StatusFound {
		http2.Redirect(w, r, se.Message, http2.StatusFound)
		return
	}
	var customErr CustomError
	customErr.Code = ErrorCode
	reason, ok := dto.ErrReasonChineseMap[se.Reason]
	if ok {
		customErr.Message = reason
	} else {
		customErr.Message = dto.ErrReasonChineseMap[pb.ErrorReason_PARAM_ERROR.String()]
	}
	codec := encoding.GetCodec("json")
	body, err := codec.Marshal(customErr)
	if err != nil {
		w.WriteHeader(http2.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "text/html;charset=UTF-8")
	w.WriteHeader(int(se.Code))
	_, _ = w.Write(body)
}

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, authService *service.AuthService, userService *service.UserService, adminService *service.AdminService, oidcService *service.OIDCService, logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			custom_middleware.SetCorp(adminService, logger),
		),
		http.Filter(handlers.CORS(
			handlers.AllowedOrigins([]string{"*"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
			handlers.AllowedHeaders([]string{"Content-Type", "Authorization", "Cookie"}),
		),
		),
		http.ErrorEncoder(CustomErrorEncoder),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}

	srv := http.NewServer(opts...)

	openAPIhandler := openapiv2.NewHandler()
	srv.HandlePrefix("/q/", openAPIhandler)

	adminPb.RegisterAdminHTTPServer(srv, adminService)
	userPb.RegisterUserHTTPServer(srv, userService)
	authPb.RegisterAuthHTTPServer(srv, authService)

	// 创建OIDC专用子服务器，使用snake_case JSON格式
	oidcSrv := http.NewServer(
		http.Middleware(
			recovery.Recovery(),
			custom_middleware.SetCorp(adminService, logger),
		),
		http.Filter(handlers.CORS(
			handlers.AllowedOrigins([]string{"*"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
			handlers.AllowedHeaders([]string{"Content-Type", "Authorization", "Cookie"}),
		),
		),
		http.ResponseEncoder(oidcResponseEncoder),
		http.ErrorEncoder(CustomErrorEncoder),
	)
	oidcPb.RegisterOIDCHTTPServer(oidcSrv, oidcService)

	// 将OIDC路径前缀路由到专用服务器
	srv.HandlePrefix("/auth/login/v1/authorize/oidc/", oidcSrv)

	srv.Use("/api.auth.v1.user.User/*", custom_middleware.JwtAccessAuth(userService, logger))
	srv.Use("/api.auth.v1.admin.Admin/*", custom_middleware.JwtAccessAdminAuth(userService, logger))
	srv.Use("/api.auth.v1.auth.Auth/RefreshToken", custom_middleware.JwtRefreshAuth(userService, logger))
	return srv
}
