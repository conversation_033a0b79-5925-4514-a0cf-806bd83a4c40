//go:build windows

package cmd_exec

import (
	"archive/zip"
	"context"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"
)

var (
	appLogPath      = GetConfigDir() + "\\applicationlog.evtx"
	sysLogPath      = GetConfigDir() + "\\systemlog.evtx"
	zipPath         = GetConfigDir() + "\\compressed.zip"
	serviceListPath = GetConfigDir() + "\\service_list.log"
	activeConnPath  = GetConfigDir() + "\\active_conn.log"
	sysInfoPath     = GetConfigDir() + "\\sys_info.log"
	cmdExecLog      = GetConfigDir() + "\\cmd_exec.log"
)

//var (
//	r int
//)

var tempFilePaths = []string{appLogPath, sysLogPath, zipPath, serviceListPath, activeConnPath, sysInfoPath}

//func init() {
//	flag.IntVar(&r, "r", 0, "back run mode")
//	flag.Parse()
//}

func main() {
	_ = UploadLog(0, "")
}

func UploadLog(runMode int, filePath string) error {
	removeTempFile()
	fmt.Println("安数达日志收集服务启动...")

	if filePath == "" {
		err := collectLog()
		if err != nil {
			return errorExit(err, runMode)
		}
	}
	err := compressLog(filePath)
	if err != nil {
		return errorExit(err, runMode)
	}

	err = sendLog(zipPath)
	if err != nil {
		return errorExit(err, runMode)
	}
	removeTempFile()
	fmt.Println("日志上报完成,相关临时文件清理完成.")

	return nil
}

func errorExit(err error, runMode int) error {
	removeTempFile()
	if runMode == 0 {
		os.Exit(-1)
	}
	return err
}

func removeTempFile() {
	for _, path := range tempFilePaths {
		_ = os.Remove(path)
	}
}
func compressLog(filePath string) error {
	// 指定需要压缩的文件和文件夹
	asecLogDir := GetConfigDir()
	// 指定路径压缩并且上传
	if filePath != "" {
		asecLogDir = filePath
	}

	zipFile, err := os.Create(zipPath)
	if err != nil {
		fmt.Println(err)
		return err
	}
	defer zipFile.Close()

	// 创建一个zip writer，用于将文件写入zip文件
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 将文件夹添加到zip文件中
	err = filepath.Walk(asecLogDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 获取相对路径
		var relPath string
		if filePath != "" {
			relPath = "cmd_exec.log"
		} else {
			relPath, err = filepath.Rel(asecLogDir, path)
			if err != nil {
				return err
			}
		}

		// 如果是文件夹，则跳过
		if info.IsDir() {
			return nil
		}

		// 过滤
		if strings.Contains(path, "upgrade") || strings.Contains(path, "compressed") || strings.Contains(path, "match") || strings.Contains(path, "filecache") {
			return nil
		}

		// 创建一个zip文件的条目
		zipEntry, err := zipWriter.Create(relPath)
		if err != nil {
			return nil
		}

		// 打开需要压缩的文件
		file, err := os.Open(path)
		if err != nil {
			return nil
		}
		defer file.Close()

		// 将文件内容写入zip文件中
		_, err = io.Copy(zipEntry, file)
		if err != nil {
			return nil
		}

		return nil
	})
	if err != nil {
		fmt.Println(err)
		return err
	}
	//_ = addFileToZip(appLogPath, zipWriter, "applicationlog.evtx")
	//_ = addFileToZip(sysLogPath, zipWriter, "systemlog.evtx")
	//_ = addFileToZip(serviceListPath, zipWriter, "service_list.log")
	//_ = addFileToZip(activeConnPath, zipWriter, "active_conn.log")
	//_ = addFileToZip(sysInfoPath, zipWriter, "sys_info.log")

	// 压缩完成
	fmt.Println("压缩完成")
	if filePath != "" {
		_ = os.Remove(cmdExecLog)
	}
	return nil
}

// 将文件拷贝到zip中
func addFileToZip(filePath string, zipWriter *zip.Writer, zipFileName string) error {
	filePathFile, err := os.Open(filePath)
	if err != nil {
		fmt.Println(err)
		return err
	}
	defer filePathFile.Close()

	appLogEntry, err := zipWriter.Create(zipFileName)
	if err != nil {
		fmt.Println(err)
		return err
	}

	_, err = io.Copy(appLogEntry, filePathFile)
	if err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func collectLog() error {
	fmt.Println("收集操作系统日志中...")
	err := collectSysLog()
	if err != nil {
		fmt.Println("收集操作系统日志错误")
		return err
	}
	fmt.Println("收集操作系统日志成功")

	fmt.Println("收集相关环境信息中...")
	ExecuteCmdToFile("driverquery /V", serviceListPath)
	ExecuteCmdToFile("netstat -an", activeConnPath)
	WriteSysInfo(sysInfoPath)
	fmt.Println("环境信息日志收集完毕...")
	return nil
}

func collectSysLog() error {
	// 生成批处理脚本内容
	batContent := `
	@echo off
	wevtutil epl System %s /q:"*[System[TimeCreated[timediff(@SystemTime) <= 259200000]]]" /ow:true
	wevtutil epl Application %s "/q:*[System[TimeCreated[timediff(@SystemTime) <= 259200000]]]" /ow:true
	`
	batContent = fmt.Sprintf(batContent, sysLogPath, appLogPath)
	// 将脚本内容写入临时文件
	tempBatFile, err := os.CreateTemp("", "script*.bat")
	defer os.Remove(tempBatFile.Name()) // 确保程序结束时删除临时文件
	if err != nil {
		return err
	}

	if _, err := tempBatFile.WriteString(batContent); err != nil {
		return err
	}
	if err := tempBatFile.Close(); err != nil {
		return err
	}

	// 执行批处理脚本
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	cmd := exec.CommandContext(ctx, "cmd.exe", "/C", tempBatFile.Name())
	err = cmd.Run()
	if err != nil {
		return err
	}
	return nil
}

func sendLog(zipPath string) error {
	fmt.Println("日志上报中...")
	time.Sleep(time.Second * 1)
	// 收集日志
	hostName, _ := os.Hostname()
	bucketPath := "client-log"
	timeStr := time.Now().Format("20060102")
	bucketUpPath := fmt.Sprintf("%v/%v/%v.zip", bucketPath, timeStr, hostName+"_"+strconv.FormatInt(time.Now().Unix(), 10))
	id := "LTAI5tKZ6TrGtGYJFLZ4gyzq"
	str := "******************************"
	client, err := oss.New("oss-cn-shenzhen.aliyuncs.com", id, str)
	if err != nil {
		fmt.Println("日志收集失败,请联系管理员,错误码：-1")
		return err
	}

	bucketName := "asec-client-log"
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		fmt.Println("日志收集失败,请联系管理员,错误码：-2")
		fmt.Println(err.Error())
		return err
	}

	//objectAcl := oss.ObjectACL(oss.ACLPrivate)

	err = bucket.PutObjectFromFile(bucketUpPath, zipPath)
	if err != nil {
		fmt.Println("日志收集失败,请联系管理员,错误码：-3")
		return err
	}
	return nil
}

func GetConfigDir() string {

	var dir string
	dir = ""
	switch runtime.GOOS {
	case "windows":
		dir = os.Getenv("ProgramData")

	case "darwin", "ios":
		dir += "/Library/Application Support"
	}

	//如果系统没有定义AppData目录,则返回temp
	if dir == "" {
		return os.Getenv("tmp")
	}
	return filepath.Join(dir, "Asec")
}
