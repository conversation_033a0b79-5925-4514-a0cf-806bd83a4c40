package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/http/httptrace"
	"time"
)

func main() {
	targetURL := "https://*************:4430" // 目标URL

	// 记录各阶段时间戳
	var (
		dnsStart, dnsDone          time.Time
		connStart, connDone        time.Time
		tlsStart, tlsDone          time.Time
		reqStart, reqDone          time.Time
		respFirstByte, respDone    time.Time
	)

	// 定义请求各阶段的回调函数（钩子）
	trace := &httptrace.ClientTrace{
		// DNS 解析阶段
		DNSStart: func(info httptrace.DNSStartInfo) {
			dnsStart = time.Now()
			fmt.Printf("开始DNS解析: %s\n", info.Host)
		},
		DNSDone: func(info httptrace.DNSDoneInfo) {
			dnsDone = time.Now()
			fmt.Printf("DNS解析完成: 耗时 %v, 解析结果: %v\n", dnsDone.Sub(dnsStart), info.Addrs)
		},

		// TCP 连接阶段
		ConnectStart: func(network, addr string) {
			connStart = time.Now()
			fmt.Printf("开始TCP连接: %s %s\n", network, addr)
		},
		ConnectDone: func(network, addr string, err error) {
			connDone = time.Now()
			if err != nil {
				fmt.Printf("TCP连接失败: %v\n", err)
			} else {
				fmt.Printf("TCP连接完成: 耗时 %v\n", connDone.Sub(connStart))
			}
		},

		// TLS 握手阶段（仅HTTPS）
		TLSHandshakeStart: func() {
			tlsStart = time.Now()
			fmt.Println("开始TLS握手")
		},
		TLSHandshakeDone: func(state tls.ConnectionState, err error) {
			tlsDone = time.Now()
			if err != nil {
				fmt.Printf("TLS握手失败: %v\n", err)
			} else {
				fmt.Printf("TLS握手完成: 耗时 %v, 协议版本: %s\n", tlsDone.Sub(tlsStart), state.Version)
			}
		},

		// 请求发送阶段
		WroteRequest: func(info httptrace.WroteRequestInfo) {
			reqDone = time.Now()
			if info.Err != nil {
				fmt.Printf("请求发送失败: %v\n", info.Err)
			} else {
				fmt.Printf("请求发送完成: 耗时 %v\n", reqDone.Sub(reqStart))
			}
		},

		// 响应接收阶段
		GotFirstResponseByte: func() {
			respFirstByte = time.Now()
			fmt.Printf("收到第一个响应字节: 服务器处理耗时 %v\n", respFirstByte.Sub(reqDone))
		},
	}

	// 将trace绑定到context
	ctx := httptrace.WithClientTrace(context.Background(), trace)

	// 创建HTTP请求（使用带trace的context）
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}
	reqStart = time.Now() // 记录请求开始时间

	// 发送请求并记录总耗时
	start := time.Now()
	resp, err := http.DefaultClient.Do(req)
	total := time.Since(start)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	respDone = time.Now() // 记录响应接收完成时间

	// 输出总耗时和响应信息
	fmt.Printf("响应接收完成: 耗时 %v\n", respDone.Sub(respFirstByte))
	fmt.Printf("总耗时: %v, 响应状态: %s\n", total, resp.Status)
}