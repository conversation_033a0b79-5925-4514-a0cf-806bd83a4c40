package email

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/smtp"
	"strconv"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/emersion/go-imap/client"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/jordan-wright/email"
	"github.com/taknb2nch/go-pop3"
)

// 定义常量
const (
	DefaultExpireMinutes  = 10
	DefaultSubject        = "您的验证码"
	DefaultTemplate       = "<div style='text-align:center'><h1>您的验证码是: {code}</h1><p>验证码有效期为{time}分钟</p></div>"
	DefaultSMTPPort       = 25
	DefaultSMTPTLSPort    = 465
	DefaultIMAPPort       = 143
	DefaultIMAPTLSPort    = 993
	DefaultPOP3Port       = 110
	DefaultPOP3TLSPort    = 995
	DefaultConnectTimeout = 10 // 连接超时时间（秒）
)

// 定义结构体表示配置数据
type EmailConfig struct {
	// 邮件服务器配置
	AuthType string `json:"auth_type"` // smtp, imap, pop3
	Host     string `json:"host"`      // 邮箱服务器地址
	Port     int    `json:"port"`      // 端口号
	UseTLS   bool   `json:"use_tls"`   // 是否使用TLS

	// 发送验证码的邮箱账号配置
	Username       string `json:"username"`        // 发件人邮箱
	Password       string `json:"password"`        // 发件人密码
	VerifySubject  string `json:"verify_subject"`  // 验证码邮件标题
	VerifyTemplate string `json:"verify_template"` // // 验证码文本模板 (支持变量: {code} - 验证码, {time}/{minutes} - 有效期分钟数)
	TemplateType   string `json:"template_type"`   // 模板类型 (默认: "default", 可选: "blue", "corporate", "simple", "custom")
	CustomHTML     string `json:"custom_html"`     // 自定义HTML模板，仅当template_type为"custom"时使用
	ExpirationTime int32  `json:"expiration_time"` // 验证码过期时间(分钟)
}

// EmailProvider 实现邮箱认证
type EmailProvider struct {
	providerId string
	name       string
	logger     *log.Helper
	config     EmailConfig
}

// NewEmailProvider 创建邮箱认证提供者
func NewEmailProvider(
	providerId string,
	name string,
	configData string,
	logger log.Logger,
) (*EmailProvider, error) {
	// 创建提供者实例
	provider := &EmailProvider{
		providerId: providerId,
		name:       name,
		logger:     log.NewHelper(logger),
	}

	provider.logger.Debugf("创建邮箱认证提供者: %s, ID: %s", name, providerId)
	provider.logger.Debugf("邮箱配置原始数据: %s", configData)

	// 解析JSON配置
	var config EmailConfig
	if err := json.Unmarshal([]byte(configData), &config); err != nil {
		provider.logger.Errorf("解析邮箱配置失败: %v", err)
		return nil, fmt.Errorf("解析邮箱配置失败: %w", err)
	}

	configJson, _ := json.Marshal(config)
	provider.logger.Debugf("解析后的邮箱配置: %s", string(configJson))

	// 验证必要配置
	if config.Host == "" {
		return nil, errors.New("邮箱服务器地址未配置")
	}

	// 设置默认认证类型
	if config.AuthType == "" {
		provider.logger.Infof("认证类型为空，使用默认类型: smtp")
		config.AuthType = "smtp"
	}

	// 设置默认端口
	if config.Port == 0 {
		switch config.AuthType {
		case "smtp":
			if config.UseTLS {
				config.Port = DefaultSMTPTLSPort
			} else {
				config.Port = DefaultSMTPPort
			}
		case "imap":
			if config.UseTLS {
				config.Port = DefaultIMAPTLSPort
			} else {
				config.Port = DefaultIMAPPort
			}
		case "pop3":
			if config.UseTLS {
				config.Port = DefaultPOP3TLSPort
			} else {
				config.Port = DefaultPOP3Port
			}
		}
	}

	// 设置默认验证码配置
	if config.VerifySubject == "" {
		config.VerifySubject = DefaultSubject
	}

	// 设置默认模板类型
	if config.TemplateType == "" {
		config.TemplateType = DefaultTemplateType
	}

	// 设置默认验证码文本模板
	if config.VerifyTemplate == "" {
		config.VerifyTemplate = "您的验证码是: %v，请勿泄露给他人，有效期 %d 分钟"
	}

	// 设置过期时间
	if config.ExpirationTime == 0 {
		config.ExpirationTime = DefaultExpireMinutes
	}

	provider.config = config

	switch config.AuthType {
	case "smtp":
		if err := provider.testSMTPConnection(); err != nil {
			provider.logger.Warnf("SMTP服务器连接测试失败: %v", err)
		}
	case "imap":
		if err := provider.testIMAPConnection(); err != nil {
			provider.logger.Warnf("IMAP服务器连接测试失败: %v", err)
		}
	case "pop3":
		if err := provider.testPOP3Connection(); err != nil {
			provider.logger.Warnf("POP3服务器连接测试失败: %v", err)
		}
	}

	return provider, nil
}

// 新增测试连接方法
func (p *EmailProvider) testSMTPConnection() error {
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))

	// 设置连接超时
	dialer := &net.Dialer{Timeout: 10 * time.Second}

	var conn net.Conn
	var err error

	if p.config.UseTLS {
		tlsConfig := &tls.Config{
			ServerName:         p.config.Host,
			InsecureSkipVerify: true,
		}
		conn, err = tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
	} else {
		conn, err = dialer.Dial("tcp", addr)
	}

	if err != nil {
		return fmt.Errorf("连接SMTP服务器失败: %w", err)
	}
	defer conn.Close()

	// 尝试与SMTP服务器握手
	client, err := smtp.NewClient(conn, p.config.Host)
	if err != nil {
		return fmt.Errorf("创建SMTP客户端失败: %w", err)
	}
	defer client.Quit()

	// 检查是否为认证模式（用户名密码为空）
	if p.config.Username == "" || p.config.Password == "" {
		// 认证模式：只需要确保能连接到SMTP服务器即可
		p.logger.Infof("SMTP认证模式：服务器连接测试成功")
		return nil
	} else {
		// 发送邮件模式：需要测试发件人账号认证
		p.logger.Infof("SMTP发送模式：测试发件人账号认证")
		if err := client.Auth(smtp.PlainAuth("", p.config.Username, p.config.Password, p.config.Host)); err != nil {
			return fmt.Errorf("SMTP发件人账号认证失败: %w", err)
		}
		p.logger.Infof("SMTP发件人账号认证成功")
		return nil
	}
}

// testIMAPConnection 测试IMAP连接
func (p *EmailProvider) testIMAPConnection() error {
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))

	var c *client.Client
	var err error

	if p.config.UseTLS {
		tlsConfig := &tls.Config{
			ServerName:         p.config.Host,
			InsecureSkipVerify: true,
		}
		c, err = client.DialTLS(addr, tlsConfig)
	} else {
		c, err = client.Dial(addr)
	}

	if err != nil {
		return fmt.Errorf("连接IMAP服务器失败: %w", err)
	}
	defer c.Logout()

	return nil
}

// testPOP3Connection 测试POP3连接
func (p *EmailProvider) testPOP3Connection() error {
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))

	var conn net.Conn
	var err error

	dialer := &net.Dialer{Timeout: 10 * time.Second}

	if p.config.UseTLS {
		tlsConfig := &tls.Config{
			ServerName:         p.config.Host,
			InsecureSkipVerify: true,
		}
		conn, err = tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
	} else {
		conn, err = dialer.Dial("tcp", addr)
	}

	if err != nil {
		return fmt.Errorf("连接POP3服务器失败: %w", err)
	}
	defer conn.Close()

	_, err = pop3.NewClient(conn)
	if err != nil {
		return fmt.Errorf("创建POP3客户端失败: %w", err)
	}

	return nil
}

// pingServer 测试服务器连接
func (p *EmailProvider) pingServer() error {
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))

	// 简单的TCP连接测试
	conn, err := net.DialTimeout("tcp", addr, DefaultConnectTimeout*time.Second)
	if err != nil {
		return fmt.Errorf("服务器连接失败: %w", err)
	}
	conn.Close()
	return nil
}

// SendVerificationCode 发送验证码到指定邮箱
func (p *EmailProvider) SendVerificationCode(ctx context.Context, toEmail string, code string) error {
	// 验证发件人配置
	if p.config.Username == "" || p.config.Password == "" {
		return errors.New("发件人邮箱账号或密码未配置")
	}

	// 创建邮件
	e := email.NewEmail()
	e.From = fmt.Sprintf("%s <%s>", p.name, p.config.Username)
	e.To = []string{toEmail}
	e.Subject = p.config.VerifySubject

	// 获取验证码文本消息
	message := "验证码为您的账号安全而设计，请勿泄露给他人。"
	if p.config.VerifyTemplate != "" {
		// 直接使用用户自定义的模板内容，支持变量替换
		message = p.config.VerifyTemplate
		// 支持 {code} 和 {time}/{minutes} 形式的变量替换
		message = strings.Replace(message, "{code}", code, -1)
		message = strings.Replace(message, "{time}", fmt.Sprintf("%d", p.config.ExpirationTime), -1)
		message = strings.Replace(message, "{minutes}", fmt.Sprintf("%d", p.config.ExpirationTime), -1)
	}

	// 根据模板类型选择HTML模板
	var htmlTemplate string
	switch p.config.TemplateType {
	case "blue":
		htmlTemplate = BlueHTMLTemplate
	case "corporate":
		htmlTemplate = CorporateHTMLTemplate
	case "simple":
		htmlTemplate = SimpleHTMLTemplate
	case "custom":
		if p.config.CustomHTML != "" {
			htmlTemplate = p.config.CustomHTML
		} else {
			htmlTemplate = DefaultHTMLTemplate
		}
	default:
		htmlTemplate = DefaultHTMLTemplate
	}

	// 替换HTML模板中的变量
	body := strings.Replace(htmlTemplate, "{code}", code, -1)
	body = strings.Replace(body, "{minutes}", fmt.Sprintf("%d", p.config.ExpirationTime), -1)
	body = strings.Replace(body, "{message}", message, -1)
	e.HTML = []byte(body)

	// 配置SMTP选项
	auth := smtp.PlainAuth("", p.config.Username, p.config.Password, p.config.Host)
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))

	// 设置超时上下文
	sendCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 使用goroutine和通道处理可能的超时
	errChan := make(chan error, 1)
	go func() {
		var err error
		if p.config.UseTLS {
			// 使用TLS配置
			tlsConfig := &tls.Config{
				ServerName:         p.config.Host,
				InsecureSkipVerify: true, // 开发环境可用，生产环境应移除
			}
			err = e.SendWithTLS(addr, auth, tlsConfig)
		} else {
			err = e.Send(addr, auth)
		}
		errChan <- err
	}()

	// 等待发送完成或超时
	select {
	case err := <-errChan:
		if err != nil {
			p.logger.Errorf("发送邮件错误: %v", err)
			return fmt.Errorf("发送验证码失败: %w", err)
		}
		p.logger.Infof("成功发送验证码邮件到: %s", toEmail)
		return nil
	case <-sendCtx.Done():
		return fmt.Errorf("发送验证码超时: %v", sendCtx.Err())
	}
}

// ValidateCredentials 验证邮箱凭据
func (p *EmailProvider) ValidateCredentials(ctx context.Context, username string, password string) (bool, error) {
	p.logger.Infof("正在验证邮箱凭据, 类型: %s, 用户: %s, 密码：%s", p.config.AuthType, username, password)

	// 检查邮箱格式
	if !strings.Contains(username, "@") {
		p.logger.Warn("无效的邮箱格式")
		return false, nil
	}

	var result bool
	var err error

	// 根据认证类型选择不同的验证方法
	switch p.config.AuthType {
	case "smtp":
		result, err = p.validateSMTP(username, password)
	case "imap":
		result, err = p.validateIMAP(username, password)
	case "pop3":
		result, err = p.validatePOP3(username, password)
	default:
		return false, fmt.Errorf("不支持的认证类型: %s", p.config.AuthType)
	}

	if err != nil {
		p.logger.Errorf("验证邮箱凭据出错: %v", err)
		return false, err
	}

	if result {
		p.logger.Infof("邮箱凭据验证成功: %s", username)
	} else {
		p.logger.Infof("邮箱凭据验证失败: %s", username)
	}

	return result, nil
}

// validateSMTP 通过SMTP协议验证凭据
func (p *EmailProvider) validateSMTP(username, password string) (bool, error) {
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))

	var c *smtp.Client
	var err error

	if p.config.UseTLS {
		// 直接使用TLS连接（适用于465端口）
		p.logger.Debugf("使用TLS连接SMTP服务器")
		tlsConfig := &tls.Config{
			ServerName:         p.config.Host,
			InsecureSkipVerify: true,
		}

		dialer := &net.Dialer{Timeout: DefaultConnectTimeout * time.Second}
		conn, dialErr := tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
		if dialErr != nil {
			return false, fmt.Errorf("TLS连接SMTP服务器失败: %w", dialErr)
		}

		c, err = smtp.NewClient(conn, p.config.Host)
	} else {
		// 使用明文连接，然后可能升级到STARTTLS
		p.logger.Debugf("使用非TLS连接SMTP服务器")
		c, err = smtp.Dial(addr)

		if err == nil && c != nil {
			// 尝试启用STARTTLS
			tlsConfig := &tls.Config{
				ServerName:         p.config.Host,
				InsecureSkipVerify: true,
			}
			if startTLSErr := c.StartTLS(tlsConfig); startTLSErr != nil {
				p.logger.Warnf("STARTTLS启用失败,继续使用非加密连接: %v", startTLSErr)
			} else {
				p.logger.Infof("STARTTLS启用成功")
			}
		}
	}

	if err != nil {
		return false, fmt.Errorf("连接SMTP服务器失败: %w", err)
	}
	defer c.Close()

	// 使用用户提供的凭据进行认证
	auth := smtp.PlainAuth("", username, password, p.config.Host)
	if err = c.Auth(auth); err != nil {
		p.logger.Infof("SMTP认证失败: %v", err)
		return false, nil
	}

	p.logger.Infof("SMTP认证成功")
	return true, nil
}

// validatePOP3 通过POP3协议验证凭据
func (p *EmailProvider) validatePOP3(username, password string) (bool, error) {
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))

	// 首先建立基础网络连接
	var conn net.Conn
	var err error

	// 设置连接超时
	dialer := &net.Dialer{
		Timeout: DefaultConnectTimeout * time.Second,
	}

	// 根据是否使用TLS创建不同类型的连接
	if p.config.UseTLS {
		tlsConfig := &tls.Config{
			ServerName:         p.config.Host,
			InsecureSkipVerify: true, // 在生产环境中应该移除此设置
		}
		conn, err = tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
	} else {
		conn, err = dialer.Dial("tcp", addr)
	}

	if err != nil {
		return false, fmt.Errorf("连接POP3服务器失败: %w", err)
	}
	defer conn.Close()

	// 使用连接创建POP3客户端
	pop3Client, err := pop3.NewClient(conn)
	if err != nil {
		return false, fmt.Errorf("创建POP3客户端失败: %w", err)
	}
	defer pop3Client.Quit()

	// 尝试登录验证 -
	if err = pop3Client.User(username); err != nil {
		p.logger.Infof("POP3用户名不正确: %v", err)
		return false, nil
	}

	if err = pop3Client.Pass(password); err != nil {
		p.logger.Infof("POP3密码不正确: %v", err)
		return false, nil
	}

	return true, nil
}

// validateIMAP 通过IMAP协议验证凭据
func (p *EmailProvider) validateIMAP(username, password string) (bool, error) {
	addr := net.JoinHostPort(p.config.Host, strconv.Itoa(p.config.Port))
	p.logger.Debugf("开始IMAP连接: %s, TLS: %v", addr, p.config.UseTLS)

	// 设置连接选项
	tlsConfig := &tls.Config{
		ServerName:         p.config.Host,
		InsecureSkipVerify: true,
	}

	// 设置超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), DefaultConnectTimeout*time.Second)
	defer cancel()

	// 创建连接通道和错误通道
	clientCh := make(chan *client.Client, 1)
	errCh := make(chan error, 1)

	// 在goroutine中尝试连接
	go func() {
		var c *client.Client
		var err error

		// 连接到IMAP服务器
		if p.config.UseTLS {
			p.logger.Debugf("使用TLS连接IMAP服务器")
			c, err = client.DialTLS(addr, tlsConfig)
		} else {
			p.logger.Debugf("使用非TLS连接IMAP服务器")
			c, err = client.Dial(addr)
			// 如果连接成功，尝试启用STARTTLS
			if err == nil && c != nil {
				if startTLSErr := c.StartTLS(tlsConfig); startTLSErr != nil {
					p.logger.Warnf("STARTTLS启用失败,继续使用非加密连接: %v", startTLSErr)
				} else {
					p.logger.Infof("STARTTLS启用成功")
				}
			}
		}

		if err != nil {
			errCh <- fmt.Errorf("连接IMAP服务器失败: %w", err)
			return
		}

		clientCh <- c
	}()

	// 等待连接完成或超时
	select {
	case err := <-errCh:
		return false, err
	case c := <-clientCh:
		defer c.Logout()
		p.logger.Infof("IMAP服务器连接成功，尝试登录: %s", username)

		// 设置登录超时
		loginCtx, loginCancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer loginCancel()

		// 使用goroutine处理登录
		loginErrCh := make(chan error, 1)
		go func() {
			loginErrCh <- c.Login(username, password)
		}()

		// 等待登录完成或超时
		select {
		case <-loginCtx.Done():
			return false, fmt.Errorf("IMAP登录超时")
		case err := <-loginErrCh:
			if err != nil {
				p.logger.Infof("IMAP登录失败: %v", err)
				return false, nil
			}
			p.logger.Infof("IMAP登录成功")
			return true, nil
		}
	case <-ctx.Done():
		return false, fmt.Errorf("IMAP连接超时")
	}
}

// HandleAuthorization 处理授权认证
func (p *EmailProvider) HandleAuthorization(ctx context.Context, username, password string) (*dto.ExternalUser, error) {
	// 验证邮箱凭据
	valid, err := p.ValidateCredentials(ctx, username, password)
	if err != nil {
		return nil, err
	}

	if !valid {
		return nil, errors.New("邮箱凭据验证失败")
	}

	// 创建外部用户信息
	displayName := username
	if parts := strings.Split(username, "@"); len(parts) > 0 {
		displayName = parts[0]
	}

	// 使用邮箱地址生成确定性UUID
	// 使用uuidv5 函数函数，其中用DNS命名空间UUID作为基础，确保生成的UUID是确定的且唯一的
	emailNamespace := uuid.MustParse("6ba7b810-9dad-11d1-80b4-00c04fd430c8") // DNS命名空间UUID
	uid := uuid.NewSHA1(emailNamespace, []byte(strings.ToLower(username))).String()

	externalUser := &dto.ExternalUser{
		Userid:      uid,
		Name:        username,
		Email:       username,
		DisplayName: displayName,
		NickName:    displayName,
		Mobile:      "", // 邮箱登录通常没有手机号
		Type:        "email",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	return externalUser, nil
}
