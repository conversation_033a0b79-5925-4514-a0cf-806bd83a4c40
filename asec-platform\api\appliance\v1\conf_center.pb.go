// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.20.0
// source: appliance/v1/conf_center.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DataFormat int32

const (
	DataFormat_Protobuf DataFormat = 0
	DataFormat_Json     DataFormat = 1
)

// Enum value maps for DataFormat.
var (
	DataFormat_name = map[int32]string{
		0: "Protobuf",
		1: "<PERSON><PERSON>",
	}
	DataFormat_value = map[string]int32{
		"Protobuf": 0,
		"Json":     1,
	}
)

func (x DataFormat) Enum() *DataFormat {
	p := new(DataFormat)
	*p = x
	return p
}

func (x DataFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_conf_center_proto_enumTypes[0].Descriptor()
}

func (DataFormat) Type() protoreflect.EnumType {
	return &file_appliance_v1_conf_center_proto_enumTypes[0]
}

func (x DataFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataFormat.Descriptor instead.
func (DataFormat) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{0}
}

type DelConfReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfType  string `protobuf:"bytes,1,opt,name=conf_type,json=confType,proto3" json:"conf_type,omitempty"`
	ConfBizId string `protobuf:"bytes,2,opt,name=conf_biz_id,json=confBizId,proto3" json:"conf_biz_id,omitempty"`
}

func (x *DelConfReq) Reset() {
	*x = DelConfReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelConfReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelConfReq) ProtoMessage() {}

func (x *DelConfReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelConfReq.ProtoReflect.Descriptor instead.
func (*DelConfReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{0}
}

func (x *DelConfReq) GetConfType() string {
	if x != nil {
		return x.ConfType
	}
	return ""
}

func (x *DelConfReq) GetConfBizId() string {
	if x != nil {
		return x.ConfBizId
	}
	return ""
}

type DelConfResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelConfResp) Reset() {
	*x = DelConfResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelConfResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelConfResp) ProtoMessage() {}

func (x *DelConfResp) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelConfResp.ProtoReflect.Descriptor instead.
func (*DelConfResp) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{1}
}

type SyncConfTypeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfTypes []string `protobuf:"bytes,1,rep,name=confTypes,proto3" json:"confTypes,omitempty"`
}

func (x *SyncConfTypeResp) Reset() {
	*x = SyncConfTypeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncConfTypeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncConfTypeResp) ProtoMessage() {}

func (x *SyncConfTypeResp) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncConfTypeResp.ProtoReflect.Descriptor instead.
func (*SyncConfTypeResp) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{2}
}

func (x *SyncConfTypeResp) GetConfTypes() []string {
	if x != nil {
		return x.ConfTypes
	}
	return nil
}

type SyncConfTypeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncConfTypeReq) Reset() {
	*x = SyncConfTypeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncConfTypeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncConfTypeReq) ProtoMessage() {}

func (x *SyncConfTypeReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncConfTypeReq.ProtoReflect.Descriptor instead.
func (*SyncConfTypeReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{3}
}

type SniffUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfType    string `protobuf:"bytes,1,opt,name=conf_type,json=confType,proto3" json:"conf_type,omitempty"`
	ConfVersion uint32 `protobuf:"varint,2,opt,name=conf_version,json=confVersion,proto3" json:"conf_version,omitempty"`
}

func (x *SniffUpdateReq) Reset() {
	*x = SniffUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SniffUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SniffUpdateReq) ProtoMessage() {}

func (x *SniffUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SniffUpdateReq.ProtoReflect.Descriptor instead.
func (*SniffUpdateReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{4}
}

func (x *SniffUpdateReq) GetConfType() string {
	if x != nil {
		return x.ConfType
	}
	return ""
}

func (x *SniffUpdateReq) GetConfVersion() uint32 {
	if x != nil {
		return x.ConfVersion
	}
	return 0
}

type SniffUpdateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdateSignal bool  `protobuf:"varint,1,opt,name=update_signal,json=updateSignal,proto3" json:"update_signal,omitempty"`
	ConfVersion  int32 `protobuf:"varint,2,opt,name=conf_version,json=confVersion,proto3" json:"conf_version,omitempty"`
}

func (x *SniffUpdateResp) Reset() {
	*x = SniffUpdateResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SniffUpdateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SniffUpdateResp) ProtoMessage() {}

func (x *SniffUpdateResp) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SniffUpdateResp.ProtoReflect.Descriptor instead.
func (*SniffUpdateResp) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{5}
}

func (x *SniffUpdateResp) GetUpdateSignal() bool {
	if x != nil {
		return x.UpdateSignal
	}
	return false
}

func (x *SniffUpdateResp) GetConfVersion() int32 {
	if x != nil {
		return x.ConfVersion
	}
	return 0
}

type ExistConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfId  string `protobuf:"bytes,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	ConfMd5 string `protobuf:"bytes,2,opt,name=conf_md5,json=confMd5,proto3" json:"conf_md5,omitempty"`
}

func (x *ExistConf) Reset() {
	*x = ExistConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExistConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExistConf) ProtoMessage() {}

func (x *ExistConf) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExistConf.ProtoReflect.Descriptor instead.
func (*ExistConf) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{6}
}

func (x *ExistConf) GetConfId() string {
	if x != nil {
		return x.ConfId
	}
	return ""
}

func (x *ExistConf) GetConfMd5() string {
	if x != nil {
		return x.ConfMd5
	}
	return ""
}

type PollConfReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId string       `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	UserId      string       `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ConfType    string       `protobuf:"bytes,3,opt,name=conf_type,json=confType,proto3" json:"conf_type,omitempty"`
	ExistConf   []*ExistConf `protobuf:"bytes,4,rep,name=exist_conf,json=existConf,proto3" json:"exist_conf,omitempty"`
}

func (x *PollConfReq) Reset() {
	*x = PollConfReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollConfReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollConfReq) ProtoMessage() {}

func (x *PollConfReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollConfReq.ProtoReflect.Descriptor instead.
func (*PollConfReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{7}
}

func (x *PollConfReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

func (x *PollConfReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PollConfReq) GetConfType() string {
	if x != nil {
		return x.ConfType
	}
	return ""
}

func (x *PollConfReq) GetExistConf() []*ExistConf {
	if x != nil {
		return x.ExistConf
	}
	return nil
}

type PollConfResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NeedUpdate     bool         `protobuf:"varint,1,opt,name=need_update,json=needUpdate,proto3" json:"need_update,omitempty"`
	AddConfList    []*AgentConf `protobuf:"bytes,2,rep,name=add_conf_list,json=addConfList,proto3" json:"add_conf_list,omitempty"`
	DelConfIds     []string     `protobuf:"bytes,3,rep,name=del_conf_ids,json=delConfIds,proto3" json:"del_conf_ids,omitempty"`
	UpdateConfList []*AgentConf `protobuf:"bytes,4,rep,name=update_conf_list,json=updateConfList,proto3" json:"update_conf_list,omitempty"`
}

func (x *PollConfResp) Reset() {
	*x = PollConfResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollConfResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollConfResp) ProtoMessage() {}

func (x *PollConfResp) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollConfResp.ProtoReflect.Descriptor instead.
func (*PollConfResp) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{8}
}

func (x *PollConfResp) GetNeedUpdate() bool {
	if x != nil {
		return x.NeedUpdate
	}
	return false
}

func (x *PollConfResp) GetAddConfList() []*AgentConf {
	if x != nil {
		return x.AddConfList
	}
	return nil
}

func (x *PollConfResp) GetDelConfIds() []string {
	if x != nil {
		return x.DelConfIds
	}
	return nil
}

func (x *PollConfResp) GetUpdateConfList() []*AgentConf {
	if x != nil {
		return x.UpdateConfList
	}
	return nil
}

type AgentConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfId     string     `protobuf:"bytes,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	ConfType   string     `protobuf:"bytes,2,opt,name=conf_type,json=confType,proto3" json:"conf_type,omitempty"`
	ConfMd5    string     `protobuf:"bytes,3,opt,name=conf_md5,json=confMd5,proto3" json:"conf_md5,omitempty"`
	ConfData   []byte     `protobuf:"bytes,4,opt,name=conf_data,json=confData,proto3" json:"conf_data,omitempty"`
	DataFormat DataFormat `protobuf:"varint,5,opt,name=data_format,json=dataFormat,proto3,enum=api.appliance.DataFormat" json:"data_format,omitempty"`
	ConfText   string     `protobuf:"bytes,6,opt,name=conf_text,json=confText,proto3" json:"conf_text,omitempty"`
}

func (x *AgentConf) Reset() {
	*x = AgentConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_conf_center_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentConf) ProtoMessage() {}

func (x *AgentConf) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_conf_center_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentConf.ProtoReflect.Descriptor instead.
func (*AgentConf) Descriptor() ([]byte, []int) {
	return file_appliance_v1_conf_center_proto_rawDescGZIP(), []int{9}
}

func (x *AgentConf) GetConfId() string {
	if x != nil {
		return x.ConfId
	}
	return ""
}

func (x *AgentConf) GetConfType() string {
	if x != nil {
		return x.ConfType
	}
	return ""
}

func (x *AgentConf) GetConfMd5() string {
	if x != nil {
		return x.ConfMd5
	}
	return ""
}

func (x *AgentConf) GetConfData() []byte {
	if x != nil {
		return x.ConfData
	}
	return nil
}

func (x *AgentConf) GetDataFormat() DataFormat {
	if x != nil {
		return x.DataFormat
	}
	return DataFormat_Protobuf
}

func (x *AgentConf) GetConfText() string {
	if x != nil {
		return x.ConfText
	}
	return ""
}

var File_appliance_v1_conf_center_proto protoreflect.FileDescriptor

var file_appliance_v1_conf_center_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x22,
	0x49, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x6f,
	0x6e, 0x66, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x66, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x0d, 0x0a, 0x0b, 0x44, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x73, 0x70, 0x22, 0x30, 0x0a, 0x10, 0x53, 0x79, 0x6e,
	0x63, 0x43, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x11, 0x0a, 0x0f, 0x53,
	0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x22, 0x50,
	0x0a, 0x0e, 0x53, 0x6e, 0x69, 0x66, 0x66, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x59, 0x0a, 0x0f, 0x53, 0x6e, 0x69, 0x66, 0x66, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x66, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3f, 0x0a, 0x09, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x4d, 0x64, 0x35, 0x22, 0x9f, 0x01, 0x0a,
	0x0b, 0x50, 0x6f, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e,
	0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x65, 0x78, 0x69, 0x73, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x45, 0x78, 0x69, 0x73, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x52, 0x09, 0x65, 0x78, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x22, 0xd3,
	0x01, 0x0a, 0x0c, 0x50, 0x6f, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6e, 0x65, 0x65, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x3c, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0c, 0x64, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x49, 0x64, 0x73,
	0x12, 0x42, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x52, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0xd2, 0x01, 0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x6f, 0x6e, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x66,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66,
	0x4d, 0x64, 0x35, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x66, 0x54, 0x65, 0x78, 0x74, 0x2a, 0x24, 0x0a, 0x0a, 0x44, 0x61, 0x74,
	0x61, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4a, 0x73, 0x6f, 0x6e, 0x10, 0x01, 0x32,
	0xb2, 0x02, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x4c,
	0x0a, 0x0b, 0x53, 0x6e, 0x69, 0x66, 0x66, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x6e,
	0x69, 0x66, 0x66, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x6e, 0x69,
	0x66, 0x66, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x08,
	0x50, 0x6f, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x4f, 0x0a, 0x0c, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x40, 0x0a, 0x07, 0x44, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x44, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x52, 0x65, 0x73, 0x70, 0x42, 0x2e, 0x5a, 0x2c, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76,
	0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_appliance_v1_conf_center_proto_rawDescOnce sync.Once
	file_appliance_v1_conf_center_proto_rawDescData = file_appliance_v1_conf_center_proto_rawDesc
)

func file_appliance_v1_conf_center_proto_rawDescGZIP() []byte {
	file_appliance_v1_conf_center_proto_rawDescOnce.Do(func() {
		file_appliance_v1_conf_center_proto_rawDescData = protoimpl.X.CompressGZIP(file_appliance_v1_conf_center_proto_rawDescData)
	})
	return file_appliance_v1_conf_center_proto_rawDescData
}

var file_appliance_v1_conf_center_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_appliance_v1_conf_center_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_appliance_v1_conf_center_proto_goTypes = []interface{}{
	(DataFormat)(0),          // 0: api.appliance.DataFormat
	(*DelConfReq)(nil),       // 1: api.appliance.DelConfReq
	(*DelConfResp)(nil),      // 2: api.appliance.DelConfResp
	(*SyncConfTypeResp)(nil), // 3: api.appliance.SyncConfTypeResp
	(*SyncConfTypeReq)(nil),  // 4: api.appliance.SyncConfTypeReq
	(*SniffUpdateReq)(nil),   // 5: api.appliance.SniffUpdateReq
	(*SniffUpdateResp)(nil),  // 6: api.appliance.SniffUpdateResp
	(*ExistConf)(nil),        // 7: api.appliance.ExistConf
	(*PollConfReq)(nil),      // 8: api.appliance.PollConfReq
	(*PollConfResp)(nil),     // 9: api.appliance.PollConfResp
	(*AgentConf)(nil),        // 10: api.appliance.AgentConf
}
var file_appliance_v1_conf_center_proto_depIdxs = []int32{
	7,  // 0: api.appliance.PollConfReq.exist_conf:type_name -> api.appliance.ExistConf
	10, // 1: api.appliance.PollConfResp.add_conf_list:type_name -> api.appliance.AgentConf
	10, // 2: api.appliance.PollConfResp.update_conf_list:type_name -> api.appliance.AgentConf
	0,  // 3: api.appliance.AgentConf.data_format:type_name -> api.appliance.DataFormat
	5,  // 4: api.appliance.ConfCenter.SniffUpdate:input_type -> api.appliance.SniffUpdateReq
	8,  // 5: api.appliance.ConfCenter.PollConf:input_type -> api.appliance.PollConfReq
	4,  // 6: api.appliance.ConfCenter.SyncConfType:input_type -> api.appliance.SyncConfTypeReq
	1,  // 7: api.appliance.ConfCenter.DelConf:input_type -> api.appliance.DelConfReq
	6,  // 8: api.appliance.ConfCenter.SniffUpdate:output_type -> api.appliance.SniffUpdateResp
	9,  // 9: api.appliance.ConfCenter.PollConf:output_type -> api.appliance.PollConfResp
	3,  // 10: api.appliance.ConfCenter.SyncConfType:output_type -> api.appliance.SyncConfTypeResp
	2,  // 11: api.appliance.ConfCenter.DelConf:output_type -> api.appliance.DelConfResp
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_appliance_v1_conf_center_proto_init() }
func file_appliance_v1_conf_center_proto_init() {
	if File_appliance_v1_conf_center_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_appliance_v1_conf_center_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelConfReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelConfResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncConfTypeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncConfTypeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SniffUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SniffUpdateResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExistConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollConfReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollConfResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_conf_center_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_appliance_v1_conf_center_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_appliance_v1_conf_center_proto_goTypes,
		DependencyIndexes: file_appliance_v1_conf_center_proto_depIdxs,
		EnumInfos:         file_appliance_v1_conf_center_proto_enumTypes,
		MessageInfos:      file_appliance_v1_conf_center_proto_msgTypes,
	}.Build()
	File_appliance_v1_conf_center_proto = out.File
	file_appliance_v1_conf_center_proto_rawDesc = nil
	file_appliance_v1_conf_center_proto_goTypes = nil
	file_appliance_v1_conf_center_proto_depIdxs = nil
}
