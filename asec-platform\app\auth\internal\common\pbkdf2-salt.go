package common

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"

	"golang.org/x/crypto/pbkdf2"
)

const (
	DefaultIter   = 27500
	DefaultKeyLen = 64
)

type CredData struct {
	UserSalt string
	Iter     int
	KeyLen   int
}

type Credential struct {
	SecretDataStr string
	CredDataStr   string
}

type Pbkdf2SaltCredManager struct{}

func NewPbkdf2SaltCredManager() *Pbkdf2SaltCredManager {
	cm := &Pbkdf2SaltCredManager{}
	return cm
}

func (cm *Pbkdf2SaltCredManager) GetHashedPassword(password string, credData CredData) string {
	decodedSalt, _ := base64.StdEncoding.DecodeString(credData.UserSalt)
	res := pbkdf2.Key([]byte(password), decodedSalt, credData.Iter, credData.KeyLen, sha256.New)
	return base64.StdEncoding.EncodeToString(res)
}

func (cm *Pbkdf2SaltCredManager) IsPasswordCorrect(plainPwd, hashedPwd string, credData CredData) bool {
	return hashedPwd == cm.GetHashedPassword(plainPwd, credData)
}

func (cm *Pbkdf2SaltCredManager) NewCredential(password string) (Credential, error) {
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return Credential{}, err
	}
	userSalt := base64.StdEncoding.EncodeToString(salt)

	// 加密生成密码hash
	credData := CredData{
		UserSalt: userSalt,
		Iter:     DefaultIter,
		KeyLen:   DefaultKeyLen,
	}
	secretDataStr := cm.GetHashedPassword(password, credData)

	credDataByte, err := json.Marshal(credData)
	if err != nil {
		return Credential{}, err
	}
	return Credential{
		SecretDataStr: secretDataStr,
		CredDataStr:   string(credDataByte),
	}, nil
}
