package common

import (
	"asdsec.com/asec/platform/pkg/utils"
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/miekg/dns"
)

func IsIPAddress(addr string) bool {
	ip := net.ParseIP(addr)
	if ip == nil {
		_, _, err := net.ParseCIDR(addr)
		if err != nil {
			_, IsDomain := dns.IsDomainName(addr)
			if !IsDomain {
				return false
			}
		}
	}
	return true
}

func IntArrayToString(intArray []int64) []string {
	var result []string
	for _, e := range intArray {
		result = append(result, strconv.FormatInt(e, 10))
	}
	return result
}

func StringArrayToInt(strArray []string) ([]int64, error) {
	var result []int64
	for _, e := range strArray {
		i, err := strconv.ParseInt(e, 10, 64)
		if err != nil {
			return []int64{}, err
		}
		result = append(result, i)
	}
	return result, nil
}

func CalculatePeriod(duration int32, durationUnit string) (time.Duration, error) {
	if duration <= 0 {
		return 0, fmt.Errorf("duration=%d, err=%w", duration, ErrParamError)
	}
	switch durationUnit {
	case DurationUnitDay:
		return time.Duration(duration) * time.Hour * 24, nil
	case DurationUnitWeek:
		return time.Duration(duration) * time.Hour * 24 * 7, nil
	case DurationUnitHour:
		return time.Duration(duration) * time.Hour, nil
	case DurationUnitMinute:
		return time.Duration(duration) * time.Minute, nil
	default:
		return 0, ErrDurationUnitNotSupport
	}
}

func CheckNewVersion(cloudVer, reqVer string) bool {
	if reqVer == "" || cloudVer == "" {
		return false
	}

	var cBuild, rBuild string
	cVerSplit := strings.Split(cloudVer, "-")
	rVerSplit := strings.Split(reqVer, "-")
	if len(cVerSplit) < 1 || len(rVerSplit) < 1 {
		return false
	}
	if cVerSplit[0] == rVerSplit[0] {
		// 大版本相同才判断小版本
		cBuildSplit := strings.Split(cloudVer, " ")
		rBuildSplit := strings.Split(reqVer, " ")
		if len(cBuildSplit) > 1 {
			cBuild = cBuildSplit[1]
		}
		if len(rBuildSplit) > 1 {
			rBuild = rBuildSplit[1]
		}
		if len(cVerSplit) >= 2 && len(rVerSplit) >= 2 {
			// 定制版升主线/主线升定制版可以要升级.比如dev能升级到release，忽略build号差异
			cBranch := strings.Split(cVerSplit[1], " ")[0]
			rBranch := strings.Split(rVerSplit[1], " ")[0]
			if !strings.EqualFold(cBranch, rBranch) {
				return true
			}
		}

		if strings.EqualFold(cBuild, rBuild) {
			return false
		}
		cbv := strings.Split(cBuild, "(")
		rbv := strings.Split(rBuild, "(")
		var c, r string
		if len(cbv) > 0 {
			c = strings.ToLower(cbv[0])
		}
		if len(rbv) > 0 {
			r = strings.ToLower(rbv[0])
		}
		if c == r {
			return false
		}
		if len(c) > len(r) || (len(c) == len(r) && (c > r)) {
			return true
		}
		return false
	} else {
		cVerArr := strings.Split(cVerSplit[0], ".")
		rVerArr := strings.Split(rVerSplit[0], ".")
		if len(cVerArr) > len(rVerArr) {
			return true
		}
		if len(cVerArr) < len(rVerArr) {
			return false
		}
		// 转为 数字数组后在比较
		var parseErr error
		cVerIntArr := utils.SliceMap(cVerArr, func(t1 string) int {
			parseInt, err := strconv.ParseInt(t1, 10, 64)
			if err != nil {
				parseErr = err
			}
			return int(parseInt)
		})
		if parseErr != nil {
			return false
		}
		rVerIntArr := utils.SliceMap(rVerArr, func(t1 string) int {
			parseInt, err := strconv.ParseInt(t1, 10, 64)
			if err != nil {
				parseErr = err
			}
			return int(parseInt)
		})
		if parseErr != nil {
			return false
		}
		for i := 0; i < len(cVerIntArr); i++ {
			if cVerIntArr[i] < rVerIntArr[i] {
				return false
			}
		}
		return true
	}
}

func RandomSelect(list []int64, count int) []int64 {
	// 所需数量大于列表长度，直接返回所有元素
	if count > len(list) {
		return list
	}

	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	// 创建一个map用于检查元素是否已选择
	selectedMap := make(map[int]bool)

	// 选择元素
	selected := make([]int64, 0, count)
	for len(selected) < count {
		// 生成随机索引
		index := rand.Intn(len(list))
		if selectedMap[index] {
			continue // 如果已选择过该索引，则跳过本次循环
		}

		// 添加到已选择列表中
		selected = append(selected, list[index])
		selectedMap[index] = true
	}

	return selected
}

func FilterList(list []int64, blackFilter []int64) []int64 {
	blackMap := make(map[int64]bool)
	for _, f := range blackFilter {
		blackMap[f] = true
	}

	var result []int64
	for _, e := range list {
		if blackMap[e] {
			continue
		}
		result = append(result, e)
	}
	return result

}

func GetDeduplicationList(list []int64, otherList []int64) []int64 {
	blackMap := make(map[int64]bool)
	for _, f := range otherList {
		blackMap[f] = true
	}

	var result []int64
	for _, e := range list {
		if blackMap[e] {
			result = append(result, e)
		}
	}
	return result

}

func Deduplication(list []int64) []int64 {
	if len(list) == 0 {
		return list
	}
	// 创建一个map用于存储已出现的元素
	seen := make(map[int64]bool)
	result := make([]int64, 0)

	for _, num := range list {
		// 如果元素已经出现过，则跳过
		if seen[num] {
			continue
		}

		// 将元素添加到结果列表中，并标记为已出现
		result = append(result, num)
		seen[num] = true
	}

	return result
}
