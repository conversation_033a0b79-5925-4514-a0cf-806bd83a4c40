package data

import (
	"context"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	pkgModel "asdsec.com/asec/platform/pkg/model"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

type appRepo struct {
	data *Data
	log  *log.Helper
}

// NewAppRepo 创建应用仓库实现
func NewAppRepo(data *Data, logger log.Logger) biz.AppRepo {
	return &appRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// GetMyApp 获取用户有权访问的应用列表
func (r *appRepo) GetMyApp(ctx context.Context, userId string) ([]biz.Application, error) {
	r.log.Debugf("获取用户应用列表: userID=%s", userId)

	var applications []pkgModel.Application

	// 这里复制了原来的GetMyApp逻辑，但简化为只返回应用ID和名称
	// 1. 查询用户的所有策略
	var userIdStrategy []pkgModel.AccessStrategy
	strategyTable := pkgModel.AccessStrategy{}.TableName()
	err := r.data.db.Table(strategyTable).Where("(user_ids @> ? or enable_all_user = 1) and strategy_status = 1 and action != 'block'", pq.StringArray{userId}).Find(&userIdStrategy).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		r.log.Errorf("查询用户策略失败: userID=%s, error=%v", userId, err)
		return nil, err
	}

	// 1.1 查询用户角色的策略
	var roleIds []string
	var roleStrategy []pkgModel.AccessStrategy
	roleMappingTable := "tb_user_role" // auth_model.TableNameTbUserRole
	err = r.data.db.Table(roleMappingTable).Select("role_id").Where("user_id = ?", userId).Find(&roleIds).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		r.log.Errorf("查询用户角色失败: userID=%s, error=%v", userId, err)
		return nil, err
	}
	if len(roleIds) > 0 {
		err = r.data.db.Table(strategyTable).Where("(role_ids && ? or enable_all_user = 1) and strategy_status = 1 and action != 'block'", pq.StringArray(roleIds)).Find(&roleStrategy).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			r.log.Errorf("查询角色策略失败: userID=%s, roleIds=%v, error=%v", userId, roleIds, err)
			return nil, err
		}
	}

	// 2. 查询用户所在组的策略
	var user model.TbUserEntity
	err = r.data.db.Table("tb_user_entity").First(&user, "id = ?", userId).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		r.log.Errorf("查询用户信息失败: userID=%s, error=%v", userId, err)
		return nil, err
	}

	var allGroups []string
	var groupStrategy []pkgModel.AccessStrategy

	// 获取用户组层次关系
	groups := r.getParentInfo(ctx, user.GroupID)
	for _, v := range groups {
		allGroups = append(allGroups, v.ID)
	}

	if len(allGroups) > 0 {
		err = r.data.db.Table(strategyTable).Where("(user_group_ids && ? or enable_all_user = 1) and strategy_status = 1 and action != 'block'", pq.StringArray(allGroups)).Find(&groupStrategy).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			r.log.Errorf("查询用户组策略失败: userID=%s, groupIds=%v, error=%v", userId, allGroups, err)
			return nil, err
		}
	}

	// 3. 过滤排除用户：检查每个策略是否排除了当前用户
	var validStrategies []pkgModel.AccessStrategy

	// 检查用户直接策略
	for _, strategy := range userIdStrategy {
		if !r.isUserExcludedFromStrategy(userId, roleIds, allGroups, strategy) {
			validStrategies = append(validStrategies, strategy)
		}
	}

	// 检查角色策略
	for _, strategy := range roleStrategy {
		if !r.isUserExcludedFromStrategy(userId, roleIds, allGroups, strategy) {
			validStrategies = append(validStrategies, strategy)
		}
	}

	// 检查用户组策略
	for _, strategy := range groupStrategy {
		if !r.isUserExcludedFromStrategy(userId, roleIds, allGroups, strategy) {
			validStrategies = append(validStrategies, strategy)
		}
	}

	// 4. 汇总有效策略，获取应用ID和应用分组ID
	var appIds []int64
	var appGroupIds []int64

	for _, strategy := range validStrategies {
		appIds = append(appIds, strategy.AppIds...)
		appGroupIds = append(appGroupIds, strategy.AppGroupIds...)
	}

	// 5. 根据应用分组ID获取应用
	if len(appGroupIds) > 0 {
		var groupApps []pkgModel.Application
		err = r.data.db.Model(&pkgModel.Application{}).Where("group_ids && ? and app_status in (1,2) and show_status = 1", pq.Int64Array(appGroupIds)).Find(&groupApps).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			r.log.Errorf("根据应用分组查询应用失败: userID=%s, appGroupIds=%v, error=%v", userId, appGroupIds, err)
			return nil, err
		}
		applications = append(applications, groupApps...)
	}

	// 6. 根据应用ID直接获取应用
	if len(appIds) > 0 {
		var directApps []pkgModel.Application
		err = r.data.db.Model(&pkgModel.Application{}).Where("id = any(?) and app_status in (1,2) and show_status = 1", pq.Int64Array(appIds)).Find(&directApps).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			r.log.Errorf("根据应用ID查询应用失败: userID=%s, appIds=%v, error=%v", userId, appIds, err)
			return nil, err
		}
		applications = append(applications, directApps...)
	}

	// 7. 转换为biz层需要的格式，并去重
	appMap := make(map[uint64]biz.Application)
	for _, app := range applications {
		appMap[app.ID] = biz.Application{
			ID:   app.ID,
			Name: app.AppName,
		}
	}

	var result []biz.Application
	for _, app := range appMap {
		result = append(result, app)
	}

	r.log.Debugf("用户应用列表查询完成: userID=%s, 应用数量=%d", userId, len(result))
	return result, nil
}

// isUserExcludedFromStrategy 检查用户是否被策略排除
func (r *appRepo) isUserExcludedFromStrategy(userId string, userRoleIds []string, userGroupIds []string, strategy pkgModel.AccessStrategy) bool {
	// 1. 检查用户是否直接被排除
	for _, excludeUserId := range strategy.ExcludeUserIds {
		if excludeUserId == userId {
			return true
		}
	}

	// 2. 检查用户的角色是否被排除
	for _, userRoleId := range userRoleIds {
		for _, excludeRoleId := range strategy.ExcludeUserRoleIds {
			if excludeRoleId == userRoleId {
				return true
			}
		}
	}

	// 3. 检查用户的用户组是否被排除
	for _, userGroupId := range userGroupIds {
		for _, excludeGroupId := range strategy.ExcludeUserGroupIds {
			if excludeGroupId == userGroupId {
				return true
			}
		}
	}

	return false
}

// TbUserGroup 用户组结构体（简化版）
type TbUserGroup struct {
	ID            string `gorm:"column:id"`
	Name          string `gorm:"column:name"`
	ParentGroupID string `gorm:"column:parent_group_id"`
}

// getParentInfo 获取用户组的父级组信息（递归）
func (r *appRepo) getParentInfo(ctx context.Context, groupId string) []TbUserGroup {
	var tmp TbUserGroup
	var groups []TbUserGroup

	err := r.data.db.Table("tb_user_group").Where("id = ?", groupId).Find(&tmp).Error
	if err != nil {
		r.log.Errorf("查询用户组失败: groupId=%s, error=%v", groupId, err)
		return groups
	}

	groups = append(groups, tmp)

	if tmp.ParentGroupID != "" && tmp.ParentGroupID != "0" { // 假设根组ID为"0"
		parentGroups := r.getParentInfo(ctx, tmp.ParentGroupID)
		groups = append(groups, parentGroups...)
	}

	return groups
}
