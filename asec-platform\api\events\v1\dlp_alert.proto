syntax = "proto3";
package api.asdsec.file_event;
import "events/v1/file_event.proto";
option go_package = "asdsec.com/asec/platform/api/events/v1;v1";
option java_package = "com.asdsec.core.api";

service DlpAlertReport {
  rpc CreateDlpLog (stream DlpAlertReq) returns (Reply);

}
message DlpAlertReq{
  repeated DlpAlert alert = 1;
}


message DlpAlert {
  string uuid = 1;
  string corp_id = 2;
  string event_type = 3;
  string event_sub_type = 4;
  string event_source = 5;
  string user_id = 6;
  string user_name = 7;
  uint64 agent_id = 8;
  string agent_name = 9;
  repeated string agent_ip = 10;
  repeated string agent_mac = 11;
  string file_name = 12;
  string file_type = 13;
  string file_path = 14;
  string original_file_name = 15;
  string original_file_path = 16;
  int64 file_size = 17;
  string owner = 18;
  int64 file_create_time = 19;
  int64 last_change_time = 20;
  string extension_name = 21;
  int64 file_category_id = 22;
  string real_extension_name = 23;
  string name_match_info = 24;
  string content_match_info = 25;
  string md5 = 26;
  string sha256 = 27;
  string activity = 28;
  int64 occur_time = 29;
  string channel = 30;
  string channel_type = 31;
  string software_path = 32;
  string dst_path = 33;
  int32 score = 34;
  string sensitive_rule_id = 35;
  string sensitive_rule_name = 36;
  int32 sensitive_level = 37;
  string data_category = 38;
  Severity severity = 39;
  int32 severity_id = 40;
  string plat_type = 41;
  repeated string trace_id = 42;
  int32 compress_encrypt = 43;
  repeated string user_tags = 44;
  int64  ingestion_time = 45;
  int64  create_time = 46;
  repeated string  file_properties = 47;

  repeated string sub_src_trace_id = 48;
  string public_ip = 49;
  string score_reason = 50;
  string src_path = 51;
  string source_id = 52;
  string source_name = 53;
  string source_type = 54;
  string sensitive_info = 55;
  uint32 file_hide_suffix = 56;

  string file_event_id = 57;
  string policy_id = 58;
  string policy_name = 59;
  string alert_type = 60;
  string engine_name = 61;
  uint32 enable_analysis = 62;
  uint32 dispose_action = 63;
  string data_category_name = 64;
}
