// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserSessionTracker = "tb_user_session_tracker"

// TbUserSessionTracker mapped from table <tb_user_session_tracker>
type TbUserSessionTracker struct {
	ID             string    `gorm:"column:id;primaryKey;comment:会话跟踪记录唯一ID" json:"id"`                                                                  // 会话跟踪记录唯一ID
	CorpID         string    `gorm:"column:corp_id;not null;comment:企业ID" json:"corp_id"`                                                                // 企业ID
	UserID         string    `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                                                                // 用户ID
	ClientType     string    `gorm:"column:client_type;not null;comment:详细的操作系统类型: windows, macos, linux, android, ios, etc." json:"client_type"`        // 详细的操作系统类型: windows, macos, linux, android, ios, etc.
	ClientCategory string    `gorm:"column:client_category;not null;comment:抽象客户端分类: pc/mobile" json:"client_category"`                                  // 抽象客户端分类: pc/mobile
	JwtID          string    `gorm:"column:jwt_id;not null;comment:access token的jti字段，用于关联现有黑名单" json:"jwt_id"`                                          // access token的jti字段，用于关联现有黑名单
	RefreshJwtID   string    `gorm:"column:refresh_jwt_id;not null;comment:refresh token的jti字段，用于拉黑refresh token" json:"refresh_jwt_id"`                 // refresh token的jti字段，用于拉黑refresh token
	DeviceID       string    `gorm:"column:device_id;comment:设备ID，用于标识唯一设备" json:"device_id"`                                                            // 设备ID，用于标识唯一设备
	IPAddress      string    `gorm:"column:ip_address;comment:登录IP地址（支持IPv4和IPv6）" json:"ip_address"`                                                    // 登录IP地址（支持IPv4和IPv6）
	Status         string    `gorm:"column:status;not null;default:active;comment:会话状态: active(活跃)/kicked(被踢出)/expired(已过期)/logout(主动登出)" json:"status"` // 会话状态: active(活跃)/kicked(被踢出)/expired(已过期)/logout(主动登出)
	KickReason     string    `gorm:"column:kick_reason;comment:踢出原因" json:"kick_reason"`                                                                 // 踢出原因
	LoginTime      time.Time `gorm:"column:login_time;not null;default:CURRENT_TIMESTAMP;comment:登录时间" json:"login_time"`                                // 登录时间
	LastActiveTime time.Time `gorm:"column:last_active_time;not null;default:CURRENT_TIMESTAMP;comment:最后活跃时间" json:"last_active_time"`                  // 最后活跃时间
	ExpiresAt      time.Time `gorm:"column:expires_at;not null;comment:JWT过期时间" json:"expires_at"`                                                       // JWT过期时间
	LogoutTime     time.Time `gorm:"column:logout_time;comment:登出时间（踢出/主动登出）" json:"logout_time"`                                                        // 登出时间（踢出/主动登出）
	CreatedAt      time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName TbUserSessionTracker's table name
func (*TbUserSessionTracker) TableName() string {
	return TableNameTbUserSessionTracker
}
