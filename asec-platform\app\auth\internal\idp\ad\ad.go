package ad

import (
	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp"
	"encoding/hex"
	"fmt"
	"github.com/go-ldap/ldap/v3"
	auth "github.com/korylprince/go-ad-auth/v3"
	"net/url"
	"strconv"
	"strings"
)

const (
	userSearchFilter = "(sAMAccountName=%s)"
)

var commonAttr = []string{"mail", "ou", "uid", "uidNumber", "telephoneNumber", "objectGUID", "userAccountControl", "sAMAccountName", "description", "distinguishedName", "userAccountControl"}

type ActiveDirectory struct {
	Server      string `json:"server"`
	DN          string `json:"dn"`
	UserName    string `json:"user_name"`
	Pwd         string `json:"pwd"`
	UserFilter  string `json:"user_filter"`
	GroupFilter string `json:"group_filter"`
	FieldMap    []dto.KV
}

func GetAllGroupAndUser(ad ActiveDirectory, rootGroupId, adtype string) (ret []*dto.ExternalDepartment, allUser []*dto.ExternalUser, err error) {
	// 解析服务器地址
	cf, err := ParseLDAPServerAddress(ad.Server)
	if err != nil {
		return nil, nil, err
	}
	conn, err := ldap.DialURL(fmt.Sprintf("%s://%s:%d", cf.Protocol, cf.Host, cf.Port))
	if err != nil {
		return nil, nil, err
	}
	defer conn.Close()
	err = conn.Bind(ad.UserName, ad.Pwd)
	if err != nil {
		return nil, nil, err
	}
	extDepts, groupMap, err := GetExtDept(ad, conn, rootGroupId, adtype)
	if err != nil {
		return nil, nil, err
	}
	extUsers, err := GetExtUser(ad, conn, groupMap, rootGroupId, adtype)
	if err != nil {
		return nil, nil, err
	}
	return extDepts, extUsers, nil
}

func GetExtUser(ad ActiveDirectory, conn *ldap.Conn, groupMap map[string]map[string]string, rootGroupId string, adtype string) ([]*dto.ExternalUser, error) {
	var cookie []byte
	var allEntries []*ldap.Entry
	objectGUIDAttr := getLDAPDepartmentAttr(ad.FieldMap, "user_id")
	email := getLDAPDepartmentAttr(ad.FieldMap, "email")
	phone := getLDAPDepartmentAttr(ad.FieldMap, "phone")
	username := getLDAPDepartmentAttr(ad.FieldMap, "name")
	displayName := getLDAPDepartmentAttr(ad.FieldMap, "display_name")
	trueName := getLDAPDepartmentAttr(ad.FieldMap, "true_name")
	nickName := getLDAPDepartmentAttr(ad.FieldMap, "nick_name")
	commonAttr = append(commonAttr, displayName, trueName, nickName)

	for {
		// 创建分页控制结构
		pagingControl := &ldap.ControlPaging{PagingSize: uint32(500), Cookie: cookie}

		// 构建搜索请求并附加分页控制
		req := ldap.NewSearchRequest(
			ad.DN,
			ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
			ad.UserFilter,
			commonAttr,
			[]ldap.Control{pagingControl},
		)

		sr, err := conn.Search(req)
		if err != nil {
			return nil, err
		}

		allEntries = append(allEntries, sr.Entries...)

		// 提取下一页的 cookie
		cookie = nil
		for _, control := range sr.Controls {
			if ctrl, ok := control.(*ldap.ControlPaging); ok {
				cookie = ctrl.Cookie
				break
			}
		}

		if len(cookie) == 0 {
			break
		}
	}

	extUsers := make([]*dto.ExternalUser, 0)
	idMap := make(map[string]bool)

	for _, entry := range allEntries {
		distinguishedName := entry.DN
		guidStr := entry.GetAttributeValue(objectGUIDAttr)
		if adtype == "msad" {
			guidStr = fmt.Sprintf("%x", guidStr)
		}
		mail := entry.GetAttributeValue(email)
		telephoneNumber := entry.GetAttributeValue(phone)
		sAMAccountName := entry.GetAttributeValue(username)
		displayN := entry.GetAttributeValues(displayName)
		trName := entry.GetAttributeValues(trueName)
		nicN := entry.GetAttributeValues(nickName)
		// 提取 OU 信息作为部门
		distinguishedNameList := strings.Split(distinguishedName, ",")
		pG := distinguishedNameList[1:]
		if !strings.Contains(distinguishedName, "OU") && adtype == "msad" {
			continue
		}
		extUser := dto.ExternalUser{
			Userid:           guidStr,
			MainDepartment:   groupMap[strings.Join(pG, ",")]["id"],
			Email:            mail,
			Mobile:           telephoneNumber,
			Name:             sAMAccountName,
			LocalRootGroupID: rootGroupId,
			DisplayName:      strings.Join(displayN, ", "),
			TrueName:         strings.Join(trName, ", "),
			NickName:         strings.Join(nicN, ", "),
		}
		extUser.UniqKey = idp.GetKey(&extUser)

		if !idMap[guidStr] {
			extUsers = append(extUsers, &extUser)
			idMap[guidStr] = true
		}
	}

	return extUsers, nil
}

// 获取 ou 对应的属性名（比如可能不是 "ou" 而是其他自定义字段）
func getLDAPDepartmentAttr(fieldMap []dto.KV, AttrName string) string {
	attr := ""
	for _, kv := range fieldMap {
		if kv.Value == AttrName {
			attr = kv.Key
		}
	}
	if attr == "" {
		mapD := dto.MsadFieldMap["sync"]
		for _, kv := range mapD {
			if kv.Value == AttrName {
				attr = kv.Key
			}
		}
	}
	return attr
}

func GetExtDept(ad ActiveDirectory, conn *ldap.Conn, rootGroupId string, adtype string) ([]*dto.ExternalDepartment, map[string]map[string]string, error) {

	objectGUIDAttr := getLDAPDepartmentAttr(ad.FieldMap, "user_id")
	ouAttr := getLDAPDepartmentAttr(ad.FieldMap, "group_name")

	req := ldap.NewSearchRequest(
		ad.DN,
		ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
		ad.GroupFilter,
		commonAttr,
		nil,
	)

	sr, err := conn.Search(req)
	if err != nil {
		return nil, nil, err
	}

	groupMap := make(map[string]map[string]string)
	fGroupMap := make(map[string]string)

	for _, v := range sr.Entries {
		distinguishedName := v.DN
		guid := v.GetAttributeValue(objectGUIDAttr)
		if guid == "" {
			guid = hex.EncodeToString([]byte(distinguishedName))
		}
		guidStr := fmt.Sprintf("%x", guid)
		groupName := v.GetAttributeValue(ouAttr)
		distinguishedNameList := strings.Split(distinguishedName, ",")
		pG := distinguishedNameList[1:]
		var fGroup string
		for _, value := range distinguishedNameList {
			if strings.Contains(value, "OU") {
				if strings.Split(value, "=")[1] != groupName {
					fGroup = strings.Split(value, "=")[1]
					break
				}
			}
		}
		//不处理搜索入口目录
		if strings.ToLower(groupName) == strings.ToLower(ad.DN) {
			continue
		}
		// 父目录为搜索目录入口直接转为根目录
		if strings.ToLower(fGroup) == strings.ToLower(ad.DN) {
			fGroupMap[distinguishedName] = rootGroupId
		} else {
			fGroupMap[distinguishedName] = strings.Join(pG, ",")
		}
		gNameIDMap := map[string]string{
			"id":   guidStr,
			"name": groupName,
		}
		groupMap[distinguishedName] = gNameIDMap
	}
	extDept := make([]*dto.ExternalDepartment, 0)
	idMap := make(map[string]bool)
	for k, value := range groupMap {
		id := value["id"]
		groupName := value["name"]
		fGroup := groupMap[fGroupMap[k]]["id"]
		if fGroup == "" {
			fGroup = rootGroupId
		}
		item := dto.ExternalDepartment{
			ID:               id,
			Name:             groupName,
			Parentid:         fGroup,
			Type:             adtype,
			LocalRootGroupID: rootGroupId,
		}
		item.UniqKey = idp.GetKey(item)
		if !idMap[id] {
			extDept = append(extDept, &item)
			idMap[id] = true
		}
	}

	return extDept, groupMap, nil
}

// LDAPServerInfo 保存解析后的协议、主机和端口信息
type LDAPServerInfo struct {
	Protocol string
	Host     string
	Port     int
}

// ParseLDAPServerAddress 解析 ldap(s) 地址，返回协议、主机、端口
func ParseLDAPServerAddress(address string) (LDAPServerInfo, error) {
	// 默认值（使用 int 类型）
	defaultPorts := map[string]int{
		"ldap":  389,
		"ldaps": 636,
	}

	// 解析 URL
	u, err := url.Parse(address)
	if err != nil {
		return LDAPServerInfo{}, fmt.Errorf("invalid LDAP server address: %v", err)
	}

	// 提取协议（scheme）
	protocol := strings.ToLower(u.Scheme)

	// 判断是否是合法的 scheme
	if protocol != "ldap" && protocol != "ldaps" {
		return LDAPServerInfo{}, fmt.Errorf("unsupported protocol: %s", protocol)
	}

	// 提取 host 和 port
	host := u.Hostname()
	portStr := u.Port()

	var port int
	if portStr == "" {
		// 如果没有指定端口，使用默认端口
		port = defaultPorts[protocol]
	} else {
		// 否则解析字符串端口为整数
		portInt, err := strconv.Atoi(portStr)
		if err != nil {
			return LDAPServerInfo{}, fmt.Errorf("invalid port number: %s", portStr)
		}
		port = portInt
	}

	return LDAPServerInfo{
		Protocol: protocol,
		Host:     host,
		Port:     port,
	}, nil
}

func GetLdapUser(ad ActiveDirectory, userAccount, pwd string) (dto.ExternalUser, error) {
	var externalUser dto.ExternalUser
	// 解析服务器地址
	cf, err := ParseLDAPServerAddress(ad.Server)
	if err != nil {
		return externalUser, err
	}
	// 连接 LDAP 服务器
	conn, err := ldap.DialURL(fmt.Sprintf("%s://%s:%d", cf.Protocol, cf.Host, cf.Port))
	if err != nil {
		return externalUser, err
	}
	defer conn.Close()
	// 第一步：尝试匿名绑定（有些 LDAP 需要先匿名搜索用户 DN）
	searchReq := ldap.NewSearchRequest(
		ad.DN, // BaseDN，比如 dc=example,dc=com
		ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
		fmt.Sprintf("(uid=%s)", strings.Split(userAccount, "@")[0]),
		[]string{"dn"},
		nil,
	)
	searchResult, err := conn.Search(searchReq)
	if err != nil {
		return externalUser, err
	}
	if len(searchResult.Entries) != 1 {
		return externalUser, fmt.Errorf("user not found or multiple entries")
	}
	userDN := searchResult.Entries[0].DN
	// 第二步：使用完整 DN + 密码进行 Bind
	err = conn.Bind(userDN, pwd)
	if err != nil {
		return externalUser, pb.ErrorAccountOrPasswordError("account or password error")
	}
	// 第三步：获取用户信息
	userEntry, err := conn.Search(ldap.NewSearchRequest(
		userDN,
		ldap.ScopeBaseObject, ldap.NeverDerefAliases, 0, 0, false,
		"(objectClass=*)",
		[]string{"uid", "uidNumber"},
		nil,
	))
	if err != nil || len(userEntry.Entries) == 0 {
		return externalUser, err
	}
	user := userEntry.Entries[0]
	externalUser.Name = user.GetAttributeValue("uid")
	externalUser.Userid = user.GetAttributeValue("uidNumber")
	return externalUser, nil
}

func GetAdUser(ad ActiveDirectory, userAccount, pwd string) (dto.ExternalUser, error) {
	var externalUser = dto.ExternalUser{}
	cf, err := ParseLDAPServerAddress(ad.Server)
	if err != nil {
		return dto.ExternalUser{}, err
	}
	config := auth.Config{
		Server: cf.Host,
		Port:   cf.Port,
		BaseDN: ad.DN,
	}

	userName := strings.Split(userAccount, "@")[0]
	status, err := auth.Authenticate(&config, userName, pwd)
	if err != nil {
		return dto.ExternalUser{}, err
	}
	if !status {
		return dto.ExternalUser{}, pb.ErrorAccountOrPasswordError("account or password error")
	}
	conn, err := config.Connect()
	if err != nil {
		return dto.ExternalUser{}, err
	}
	defer conn.Conn.Close()

	status, err = conn.Bind(ad.UserName, ad.Pwd)
	if err != nil {
		return dto.ExternalUser{}, err
	}
	if !status {
		return dto.ExternalUser{}, err
	}
	user, err := conn.Search(fmt.Sprintf(userSearchFilter, userName), commonAttr, 30000)
	if err != nil {
		return dto.ExternalUser{}, err
	}
	if len(user) == 0 {
		return dto.ExternalUser{}, err
	}

	guid := user[0].GetAttributeValue("objectGUID")
	guidStr := fmt.Sprintf("%x", guid)
	Name := user[0].GetAttributeValue("sAMAccountName")
	externalUser.Name = Name
	externalUser.Userid = guidStr
	return externalUser, nil
}
