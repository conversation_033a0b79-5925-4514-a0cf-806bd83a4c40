<!doctype html><html lang="zh-cn" class="qt-env"><head><undefined></undefined><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="./assets/favicon.2521b69c.ico"><title></title><style>body,html{height:100%;width:100%}HTML,blockquote,body,dd,div,dl,dt,fieldset,form,li,ol,p,pre,table,td,th,ul{border:none;font-family:PingFang SC,HarmonyOS_Medium,Helvetica Neue,Microsoft YaHei,sans-serif;font-size:14px;margin:0;padding:0}li,ol,ul{list-style-type:none}#app{background:#eee;height:100vh;overflow:hidden;font-weight:400!important}.loading-layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.loading-layout-page .loading-layout-header{width:100%;height:42px;z-index:10;display:flex;justify-content:space-between;align-items:center;background:linear-gradient(315deg,#536ce6,#647be9);box-shadow:0 2px 6px 0 rgba(46,60,128,.2);color:#fff}.loading-layout-header .loading-header-logo{margin-left:16px;height:42px;display:flex;align-items:center}.loading-layout-header .loading-header-logo img{max-width:79px;max-height:28px}.loading-layout-header .loading-u-electron-drag{display:flex;flex:1;height:100%;-webkit-app-region:drag}.loading-layout-header .loading-right-wrapper{display:flex;align-items:center;height:100%}.loading-right-wrapper .loading-base-dropdown{position:relative;display:inline-block}.loading-right-wrapper .loading-user-info{display:flex;align-items:center;height:42px;padding:0 14px;cursor:pointer}.loading-user-info .loading-user-face{width:32px;height:32px;border-radius:50%;overflow:hidden;margin-right:6px}.loading-user-info .loading-user-face img{width:100%;height:100%;display:block}.loading-user-info .loading-user-name{color:#fff;display:inline-block;max-width:100px;min-width:50px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;word-break:break-all}.loading-right-wrapper .loading-user-divider{width:1px;height:14px;margin-left:16px;margin-right:16px;background:#e6e6e6}.loading-right-wrapper .loading-window-operate{width:24px;height:100%;margin-left:4px;filter:brightness(1.5);display:flex;align-items:center;justify-content:center;cursor:pointer;vertical-align:middle}.loading-layout-page .loading-layout-wrap{width:100%;height:calc(100% - 0px);display:flex}.loading-layout-wrap .loading-layout-aside{width:56px;height:100%;background:#f5f5f7;overflow:auto;z-index:10}.loading-layout-aside .loading-menu-wrapper{padding-bottom:60px;padding-top:24px;margin:0}.loading-menu-wrapper .loading-menu-item{position:relative;color:#686e84;text-decoration:none;cursor:pointer;transition:all .3s;border-bottom:1px solid rgba(255,255,255,.1);height:64px;font-size:13px;font-weight:400;display:flex;flex-direction:column;align-items:center;justify-content:center}.loading-menu-item .loading-menu-item-icon{transform:scaleY(-1);height:17px;width:18px;margin-bottom:6px;fill:currentColor;display:inline-block;text-align:center;font-size:17px;color:currentcolor}.loading-menu-item .loading-menu-item-title{height:17px;width:24px;font-size:12px;font-family:PingFang SC,PingFang SC-Medium;display:inline-block;transition:all .3s}.loading-menu-wrapper .loading-active-menu-item{border-radius:4px;color:#536ce6}.loading-layout-wrap .loading-layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}.app-loading{position:fixed;display:none;top:0;left:0;width:100%;height:100%;z-index:9999;background:#fff;transition:opacity .5s ease-out,visibility .5s ease-out}.app-loading.fade-out{opacity:0;visibility:hidden}.app-container{position:fixed;top:0;left:0;width:100%;height:100%;background:#fff;transition:opacity .5s ease-in;z-index:9998}.app-container.fade-in{opacity:1}body{margin:0;padding:0;background:#fff}.qt-env .app-loading{display:block}.qt-env .app-container{display:none}</style><script>// 实现日志打印封装方法
        class Logger {
            constructor(name, debug) {
                this.name = name
                this.debug = 1
            }
            log(...args) {
                if (!this.debug) {
                    return
                }
                const date = new Date()
                const currTime = date.toLocaleString('zh-CN') + ' ' + date.getMilliseconds()
                console.log(currTime, this.name, ...args)
            }
            error(...args) {
                const date = new Date()
                const currTime = date.toLocaleString('zh-CN') + ' ' + date.getMilliseconds()
                console.error(currTime, this.name, ...args)
            }
            warn(...args) {
                const date = new Date()
                const currTime = date.toLocaleString('zh-CN') + ' ' + date.getMilliseconds()
                console.warn(currTime, this.name, ...args)
            }
        }
        const globalUrlHashParams = new URLSearchParams(window.location.hash)
        const logger = new Logger('portal', globalUrlHashParams.get('AsecDebug'))
        logger.log('启动')</script><script type="module" crossorigin src="./assets/index.a794166c.js"></script><link rel="stylesheet" href="./assets/index.c3b165d5.css"><script type="module">try{import.meta.url;import("_").catch(()=>1);}catch(e){}window.__vite_is_modern_browser=true;</script><script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy build because dynamic import or import.meta.url is unsupported, syntax error above should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script></head><body><div id="app_loading" class="app-loading"><div id="app"><div class="loading-layout-page"><div class="loading-layout-header"><div class="loading-header-logo"><img src="./assets/logo.8c820367.png" alt="" onload="this.style.display = 'block'" onerror="this.style.display = 'none'" style="display:block"></div><div class="loading-u-electron-drag"></div><ul class="loading-right-wrapper"><li><div class="loading-base-dropdown"><div class="loading-user-info"><div class="loading-user-face"><img src="./assets/avator.bd83723a.png" alt="" onload="this.style.display = 'block'" onerror="this.style.display = 'none'" style="display:block"></div><span class="loading-user-name">未登录</span></div></div></li><div class="loading-user-divider"></div><i class="loading-window-operate" style="font-size:16px;color:currentcolor"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor"><path d="M177.536 385.728c25.216 0 46.08-18.88 49.088-43.264l0.384-6.272v-237.184h237.184c22.976 0 42.24-15.616 47.872-36.8l1.28-6.464 0.384-6.208c0-25.28-18.88-46.08-43.264-49.152L464.192 0h-286.72a49.536 49.536 0 0 0-49.088 43.328L128 49.536v286.72c0 27.328 22.144 49.472 49.536 49.472zM846.464 768c25.28 0 46.08-18.88 49.152-43.328l0.384-6.208v-286.72a49.536 49.536 0 0 0-98.624-6.208l-0.384 6.272V669.056l-237.184-0.064a49.536 49.536 0 0 0-47.872 36.8l-1.28 6.464-0.384 6.208c0 25.28 18.88 46.08 43.264 49.152L559.808 768h286.72z"></path></svg> </i><i class="loading-window-operate" style="font-size:16px;color:currentcolor"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor"><path d="M909.824 345.6H114.176A50.752 50.752 0 0 0 64 396.8c0 28.288 22.464 51.2 50.176 51.2h795.648c27.712 0 50.176-22.912 50.176-51.2 0-28.288-22.464-51.2-50.176-51.2z"></path></svg> </i><i class="loading-window-operate" style="font-size:16px;color:currentcolor;margin-right:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor"><path d="M581.824 383.936l299.712 299.648a49.472 49.472 0 0 1-69.888 69.888L511.936 453.824 212.48 753.472a49.472 49.472 0 0 1-69.888-69.888L441.984 384l-299.52-299.648a49.472 49.472 0 1 1 69.952-69.952L512 313.984l299.52-299.52a49.152 49.152 0 0 1 69.888 0 49.472 49.472 0 0 1 0 69.952l-299.52 299.52z"></path></svg></i></ul></div><div class="loading-layout-wrap"><div class="loading-layout-aside"><ul class="loading-menu-wrapper"><li class="loading-menu-item loading-active-menu-item"><i class="loading-menu-item-icon"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor"><path d="M63.994976-128C28.669749-128 0-97.947959 0-60.882069v257.208609c0 37.06589 28.669749 67.117931 63.994976 67.169128h895.92967c35.325227-0.051196 63.994976-30.103237 63.994976-67.169128v-257.208609c0-37.06589-28.669749-67.117931-63.994976-67.117931H63.994976z m277.32863 215.739864v-39.932865c0-6.706674 2.508603-13.106171 7.01385-17.867397a23.447759 23.447759 0 0 1 16.945869-7.372222h463.989177a23.447759 23.447759 0 0 1 16.94587 7.372222 25.802774 25.802774 0 0 1 7.065045 17.816201v39.984061c0 6.655478-2.559799 13.106171-7.065045 17.816202a23.447759 23.447759 0 0 1-16.94587 7.372221H365.283325a24.574071 24.574071 0 0 1-23.959719-25.188423z m-199.152366-19.966432c0.25598-24.727659 19.454473-44.540504 43.004624-44.386916 23.498955 0.153588 42.492664 20.222413 42.390272 44.898876-0.102392 24.676463-19.147297 44.642896-42.697448 44.642895-23.652543-0.153588-42.748644-20.376-42.646252-45.154855z m314.957675 364.003426a57.953851 57.953851 0 1 0 57.032323-47.817047 58.670594 58.670594 0 0 0-57.032323 47.817047z m240.109152 176.882114c19.608061-18.942513 20.376-50.172061 1.689467-69.984906a46.946715 46.946715 0 0 0-35.120443-15.256402 43.209408 43.209408 0 0 0-32.765428 13.720523c-38.294594 37.014694-77.357127 55.496444-116.470857 55.496443h-2.355015c-65.428464-1.638271-115.702917-53.090232-116.470857-53.909368a49.608906 49.608906 0 0 0-84.985329 32.458252 48.840966 48.840966 0 0 0 13.208563 35.069247l1.79186 2.047839C338.047063 621.201988 409.567849 688.524703 507.403369 691.49407c68.602615 2.406211 131.624867-25.751579 189.885894-82.835098z m157.888406 133.570315c19.608061-18.942513 20.324805-50.172061 1.638271-69.984906a48.840966 48.840966 0 0 0-69.370554-1.638272c-87.749912 86.009248-181.080185 128.706697-276.81667 126.198094C355.044129 793.52766 239.341212 673.729064 238.47088 672.141989a50.018474 50.018474 0 0 0-36.246755-15.717166 47.407479 47.407479 0 0 0-33.021407 13.413347 49.864886 49.864886 0 0 0-2.457408 69.984906l2.04784 2.047839 4.249266 4.300463C202.736085 775.301891 330.82843 891.567964 506.533037 895.81723c122.870355 2.457407 240.109151-49.04575 348.644632-153.587943z"/></svg></i><div class="loading-menu-item-title">接入</div></li><li class="loading-menu-item"><i class="loading-menu-item-icon"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor"><path d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 0 0 9.3-35.2l-.9-2.6a443.74 443.74 0 0 0-79.7-137.9l-1.8-2.1a32.12 32.12 0 0 0-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 0 0-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 0 0-25.8 25.7l-15.8 85.4a351.86 351.86 0 0 0-99 57.4l-81.9-29.1a32 32 0 0 0-35.1 9.5l-1.8 2.1a446.02 446.02 0 0 0-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 0 0-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0 0 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0 0 25.8 25.7l2.7.5a449.4 449.4 0 0 0 159 0l2.7-.5a32.05 32.05 0 0 0 25.8-25.7l15.7-85a350 350 0 0 0 99.7-57.6l81.3 28.9a32 32 0 0 0 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM512 701c-104.9 0-190-85.1-190-190s85.1-190 190-190 190 85.1 190 190-85.1 190-190 190z"/></svg></i><div class="loading-menu-item-title">设置</div></li></ul></div><div class="loading-layout-main"><div class="loading-login-page"></div></div></div></div></div></div><div id="app_container" class="app-container"></div><script>try {
            window.globalData = JSON.parse('<?- global_data_string ?>');
        } catch (error) {

        }
        window.getQRCode = function(config){
            new WwLogin(config);
        };</script><script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script><script nomodule crossorigin id="vite-legacy-polyfill" src="./assets/polyfills-legacy.ae1c7de3.js"></script><script nomodule crossorigin id="vite-legacy-entry" data-src="./assets/index-legacy.b871e767.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script></body><script>// 检测浏览器是否为IE
    var isIE = (!!window.ActiveXObject || "ActiveXObject" in window)

    // 如果是IE浏览器，则提示不支持
    if (isIE) {
        var unsupportedMessage = "<div style='padding: 20px; background-color: #f8d7da; color: #721c24;'>" +
            "<h3>对不起，您正在使用的浏览器版本过低。</h3>" +
            "<p>本网站不支持IE浏览器，请使用其它内核浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。</p>" +
            "<p>如果使用360浏览器、搜狗浏览器等请不要使用兼容模式。</p>" +
            "</div>";

        var body = document.getElementsByTagName("body")[0];
        body.innerHTML = unsupportedMessage;
    }</script></html>