package dto

import (
	"time"

	pb "asdsec.com/asec/platform/api/auth/v1/admin"
)

// UserEntity 用户实体（对应proto的UserEntity）
type UserEntity struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	GroupID      string         `json:"group_id"`
	SourceID     string         `json:"source_id"`
	Phone        string         `json:"phone"`
	Email        string         `json:"email"`
	Roles        []*pb.RoleInfo `json:"roles"`
	Enable       bool           `json:"enable"`
	ExpireType   string         `json:"expire_type"`
	ExpireEnd    string         `json:"expire_end"`
	GroupName    string         `json:"group_name"`
	DisplayName  string         `json:"display_name"`
	SourceType   string         `json:"source_type"`
	Identifier   string         `json:"identifier"`
	AuthType     string         `json:"auth_type"`
	ActiveTime   string         `json:"active_time"`
	IdleDay      string         `json:"idle_day"`
	LockStatus   bool           `json:"lock_status"`
	SecurityCode string         `json:"security_code"`
}

// ClientCategory 客户端分类
type ClientCategory string

const (
	ClientCategoryPC     ClientCategory = "pc"
	ClientCategoryMobile ClientCategory = "mobile"
)

// ClientType 详细的客户端类型（操作系统）
type ClientType string

const (
	// PC端操作系统
	ClientTypeWindows ClientType = "windows"
	ClientTypeMacOS   ClientType = "macos"
	ClientTypeLinux   ClientType = "linux"
	ClientTypeUnix    ClientType = "unix"
	ClientTypeFreeBSD ClientType = "freebsd"

	// 移动端操作系统
	ClientTypeAndroid ClientType = "android"
	ClientTypeIOS     ClientType = "ios"
	ClientTypeIPadOS  ClientType = "ipados"

	// 其他类型
	ClientTypeUnknown ClientType = "unknown"
	ClientTypeOther   ClientType = "other"
)

// GetClientCategory 根据详细类型获取抽象分类
func (ct ClientType) GetClientCategory() ClientCategory {
	switch ct {
	case ClientTypeWindows, ClientTypeMacOS, ClientTypeLinux, ClientTypeUnix, ClientTypeFreeBSD:
		return ClientCategoryPC
	case ClientTypeAndroid, ClientTypeIOS, ClientTypeIPadOS:
		return ClientCategoryMobile
	default:
		return ClientCategoryPC // 默认归类为PC
	}
}

// IsPC 判断是否为PC端
func (ct ClientType) IsPC() bool {
	return ct.GetClientCategory() == ClientCategoryPC
}

// IsMobile 判断是否为移动端
func (ct ClientType) IsMobile() bool {
	return ct.GetClientCategory() == ClientCategoryMobile
}

// UserAgentInfo 用户代理信息
type UserAgentInfo struct {
	UserAgent      string         `json:"user_agent"`      // 原始User-Agent
	Browser        string         `json:"browser"`         // 浏览器名称
	BrowserVer     string         `json:"browser_ver"`     // 浏览器版本
	OS             string         `json:"os"`              // 操作系统名称
	OSVersion      string         `json:"os_version"`      // 操作系统版本
	DeviceType     string         `json:"device_type"`     // 设备类型
	DeviceBrand    string         `json:"device_brand"`    // 设备品牌
	DeviceModel    string         `json:"device_model"`    // 设备型号
	ClientType     ClientType     `json:"client_type"`     // 详细客户端类型
	ClientCategory ClientCategory `json:"client_category"` // 抽象客户端分类
}

// OverflowStrategy 超限处理策略
type OverflowStrategy string

const (
	OverflowStrategyKickOldest   OverflowStrategy = "kick_oldest"
	OverflowStrategyKickInactive OverflowStrategy = "kick_inactive"
	OverflowStrategyRejectNew    OverflowStrategy = "reject_new"
)

// SessionStatus 会话状态
type SessionStatus string

const (
	SessionStatusActive  SessionStatus = "active"  // 活跃
	SessionStatusKicked  SessionStatus = "kicked"  // 被踢出
	SessionStatusExpired SessionStatus = "expired" // 已过期
	SessionStatusLogout  SessionStatus = "logout"  // 主动登出
)

// ClientLimits 客户端数量限制配置
type ClientLimits struct {
	PCMaxClients     int              `json:"pc_max_clients"`     // PC端最大客户端数量，0表示不限制，-1表示禁止
	MobileMaxClients int              `json:"mobile_max_clients"` // 移动端最大客户端数量，0表示不限制，-1表示禁止
	OverflowStrategy OverflowStrategy `json:"overflow_strategy"`  // 超限处理策略
}

// DefaultClientLimits 默认客户端限制配置
func DefaultClientLimits() ClientLimits {
	return ClientLimits{
		PCMaxClients:     5,
		MobileMaxClients: 3,
		OverflowStrategy: OverflowStrategyKickOldest,
	}
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	DeviceID   string `json:"device_id"`
	DeviceName string `json:"device_name"`
	OS         string `json:"os"`
	OSVersion  string `json:"os_version"`
	AppVersion string `json:"app_version"`
}

// UserSessionInfo 用户会话信息
type UserSessionInfo struct {
	ID             string         `json:"id"`
	CorpID         string         `json:"corp_id"`
	UserID         string         `json:"user_id"`
	UserName       string         `json:"user_name"`    // 新增：用户名
	DisplayName    string         `json:"display_name"` // 新增：显示名
	Users          []UserEntity   `json:"users"`        // 新增：用户实体列表（适配proto）
	ClientType     ClientType     `json:"client_type"`
	ClientCategory ClientCategory `json:"client_category"` // 新增：客户端分类
	SessionToken   string         `json:"session_token"`
	DeviceInfo     string         `json:"device_info"` // 修改为字符串类型，方便显示
	IPAddress      string         `json:"ip_address"`
	UserAgent      string         `json:"user_agent"`
	LoginTime      time.Time      `json:"login_time"`
	LastActiveTime time.Time      `json:"last_active_time"`
	ExpiresAt      time.Time      `json:"expires_at"`
	Status         string         `json:"status"`
	JWTId          string         `json:"jwt_id"`           // 新增：JWT ID
	RefreshJWTId   string         `json:"refresh_jwt_id"`   // 新增：刷新令牌的JWT ID
	PolicyID       string         `json:"policy_id"`        // 新增：策略ID
}

// CreateUserSessionParam 创建用户会话参数
type CreateUserSessionParam struct {
	CorpID       string      `json:"corp_id"`
	UserID       string      `json:"user_id"`
	ClientType   ClientType  `json:"client_type"`
	SessionToken string      `json:"session_token"`
	DeviceInfo   *DeviceInfo `json:"device_info"`
	IPAddress    string      `json:"ip_address"`
	UserAgent    string      `json:"user_agent"`
	ExpiresAt    time.Time   `json:"expires_at"`
}

// ListUserSessionParam 查询用户会话参数
type ListUserSessionParam struct {
	CorpID     string     `json:"corp_id"`
	UserID     string     `json:"user_id"`
	ClientType ClientType `json:"client_type,omitempty"`
	Status     string     `json:"status,omitempty"`
	Limit      int        `json:"limit"`
	Offset     int        `json:"offset"`
}

// UserSessionStats 用户会话统计
type UserSessionStats struct {
	UserID             string `json:"user_id"`
	PCSessionCount     int    `json:"pc_session_count"`
	MobileSessionCount int    `json:"mobile_session_count"`
	TotalSessionCount  int    `json:"total_session_count"`
}

// SessionTrackerInfo 会话跟踪信息
type SessionTrackerInfo struct {
	ID             string     `json:"id"`
	CorpID         string     `json:"corp_id"`
	UserID         string     `json:"user_id"`
	ClientType     ClientType `json:"client_type"`
	JWTId          string     `json:"jwt_id"`
	RefreshJWTId   string     `json:"refresh_jwt_id"` // 新增：刷新令牌的JWT ID
	DeviceID       string     `json:"device_id"`
	LoginTime      time.Time  `json:"login_time"`
	LastActiveTime time.Time  `json:"last_active_time"`
	ExpiresAt      time.Time  `json:"expires_at"`
}

// CreateSessionTrackerParam 创建会话跟踪参数
type CreateSessionTrackerParam struct {
	ID             string         `json:"id"`
	CorpID         string         `json:"corp_id"`
	UserID         string         `json:"user_id"`
	ClientType     ClientType     `json:"client_type"`     // 详细操作系统类型
	ClientCategory ClientCategory `json:"client_category"` // 抽象客户端分类
	JWTId          string         `json:"jwt_id"`
	RefreshJWTId   string         `json:"refresh_jwt_id"`  // 新增：刷新令牌的JWT ID
	DeviceID       string         `json:"device_id"`
	IPAddress      string         `json:"ip_address"` // 新增：IP地址
	ExpiresAt      time.Time      `json:"expires_at"`
}

// LoginParam 登录参数（扩展）
type LoginParam struct {
	Username   string      `json:"username"`
	Password   string      `json:"password"`
	CorpID     string      `json:"corp_id"`
	ClientType ClientType  `json:"client_type"`
	DeviceID   string      `json:"device_id"`
	DeviceInfo *DeviceInfo `json:"device_info"`
	IPAddress  string      `json:"ip_address"`
	UserAgent  string      `json:"user_agent"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string      `json:"token"`
	SessionID string      `json:"session_id"`
	ExpiresAt int64       `json:"expires_at"`
	UserInfo  interface{} `json:"user_info"`
}

// AuthClaims JWT Claims结构（扩展现有结构）
type AuthClaims struct {
	UserID     string `json:"user_id"`
	CorpID     string `json:"corp_id"`
	ClientType string `json:"client_type"` // 新增
	DeviceID   string `json:"device_id"`   // 新增
	SessionID  string `json:"session_id"`  // 新增
	// jwt.StandardClaims 会在实际实现中使用
}

// ClientLimitCheckParam 客户端限制检查参数
type ClientLimitCheckParam struct {
	CorpID         string         `json:"corp_id"`
	UserID         string         `json:"user_id"`
	ClientType     ClientType     `json:"client_type"`     // 详细客户端类型
	ClientCategory ClientCategory `json:"client_category"` // 抽象客户端分类
}

// GetMaxClientsForType 获取指定客户端类型的最大数量
func (c *ClientLimits) GetMaxClientsForType(clientCategory ClientCategory) int {
	switch clientCategory {
	case ClientCategoryPC:
		return c.PCMaxClients
	case ClientCategoryMobile:
		return c.MobileMaxClients
	default:
		return 0
	}
}

// GetMaxClientsForClientType 根据详细客户端类型获取最大数量（兼容方法）
func (c *ClientLimits) GetMaxClientsForClientType(clientType ClientType) int {
	return c.GetMaxClientsForType(clientType.GetClientCategory())
}
