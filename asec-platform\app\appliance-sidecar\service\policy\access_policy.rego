package asd
import future.keywords.in

default allow:=false
default user_allow:=false
default app_allow:=false
default time_allow:=false

# 判断用户是否存在 data中
user_allow{
    some u in data.user_ids
    u==input.user_id
}
user_allow{
	data.enable_all_user
}

# 判断app是否存在 data中
app_allow{
    some i in data.app_ids
    i==input.app_id
}
app_allow{
	data.enable_all_app
}

# 判断时间是否为空
ts=data.time_start
te=data.time_end
time_allow = true{
    not ts
    not te
}

# 判断时间是否在区间内 与为空 为 or
time_allow = true{
    data.time_start <= input.time
    data.time_end >= input.time
}

# and
allow{
    user_allow
    app_allow
    time_allow
}

