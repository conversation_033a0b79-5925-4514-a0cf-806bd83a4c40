package se

import (
	"context"
	"sync"

	"asdsec.com/asec/platform/pkg/biz/strategy_engine"
	"asdsec.com/asec/platform/pkg/biz/strategy_engine/custom_rego"

	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/service"
	"google.golang.org/grpc"
)

func GetStrategy(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   GetStrategySe,
		RunType:      common.SimpleSend,
		WaitSecond:   30,
		RandomOffset: 3,
	}
	custom_rego.RegisterBuiltin()
	common.Send(param)
}

func GetStrategySe(conn *grpc.ClientConn, ctx context.Context) error {
	// getV2()
	req := pb.WebAccessInfoReq{}
	resp, err := pb.NewAppClient(conn).WebAccessInfo(ctx, &req)
	// 由于响应日志包含大量用户id，暂不打印
	// global.Logger.Debug("seGetStrategy resp:", zap.Any("resp", resp))
	if err != nil {
		global.Logger.Sugar().Errorf("SeGetStrategy err %v", err)
		return err
	}
	// 打印简化的策略响应日志（不包含详细用户信息）
	global.Logger.Sugar().Debugf("策略引擎-获取策略信息成功: 策略数量=%d", len(resp.StrategyInfo))
	service.AccessInfo = make([]strategy_engine.AccessModel, 0)
	// 置空
	for _, strategyInfo := range resp.StrategyInfo {
		accessModel, err := strategy_engine.TransStrategy(strategyInfo)
		if err != nil {
			global.Logger.Sugar().Errorf("trans accessModel err:%v,id:%v", err, strategyInfo.StrategyId)
			continue
		}
		service.AccessInfo = append(service.AccessInfo, accessModel)
	}
	global.Logger.Sugar().Debugf("策略引擎-成功处理策略信息: 有效策略数量=%d", len(service.AccessInfo))

	// uci
	//service.UciInfo = make(map[string]strategy_engine.UciInfo)
	uciReq := pb.UciUserInfoReq{}
	uciResp, err := pb.NewAppClient(conn).UciUserInfo(ctx, &uciReq)
	if err != nil {
		global.Logger.Sugar().Errorf("SeGetStrategy err %v", err)
		return err
	}
	// 打印简化的UCI响应日志（不包含详细用户信息）
	global.Logger.Sugar().Debugf("策略引擎-获取用户评分信息成功: 用户数量=%d", len(uciResp.UciUserInfo))
	for _, v := range uciResp.UciUserInfo {
		service.UciInfo.Store(v.UserId, strategy_engine.UciInfo{
			Score:     v.Score,
			RiskLevel: v.RiskLevel,
			UserId:    v.UserId,
		})
	}
	global.Logger.Sugar().Debugf("策略引擎-成功处理用户评分信息: 已加载用户数量=%d", len(uciResp.UciUserInfo))
	return nil
}
