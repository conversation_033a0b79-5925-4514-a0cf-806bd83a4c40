// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbSpecialConfig(db *gorm.DB, opts ...gen.DOOption) tbSpecialConfig {
	_tbSpecialConfig := tbSpecialConfig{}

	_tbSpecialConfig.tbSpecialConfigDo.UseDB(db, opts...)
	_tbSpecialConfig.tbSpecialConfigDo.UseModel(&model.TbSpecialConfig{})

	tableName := _tbSpecialConfig.tbSpecialConfigDo.TableName()
	_tbSpecialConfig.ALL = field.NewAsterisk(tableName)
	_tbSpecialConfig.ID = field.NewString(tableName, "id")
	_tbSpecialConfig.Type = field.NewString(tableName, "type")
	_tbSpecialConfig.Value = field.NewString(tableName, "value")
	_tbSpecialConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_tbSpecialConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbSpecialConfig.Key = field.NewString(tableName, "key")

	_tbSpecialConfig.fillFieldMap()

	return _tbSpecialConfig
}

type tbSpecialConfig struct {
	tbSpecialConfigDo tbSpecialConfigDo

	ALL       field.Asterisk
	ID        field.String
	Type      field.String
	Value     field.String
	CreatedAt field.Time
	UpdatedAt field.Time
	Key       field.String

	fieldMap map[string]field.Expr
}

func (t tbSpecialConfig) Table(newTableName string) *tbSpecialConfig {
	t.tbSpecialConfigDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbSpecialConfig) As(alias string) *tbSpecialConfig {
	t.tbSpecialConfigDo.DO = *(t.tbSpecialConfigDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbSpecialConfig) updateTableName(table string) *tbSpecialConfig {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Type = field.NewString(table, "type")
	t.Value = field.NewString(table, "value")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.Key = field.NewString(table, "key")

	t.fillFieldMap()

	return t
}

func (t *tbSpecialConfig) WithContext(ctx context.Context) *tbSpecialConfigDo {
	return t.tbSpecialConfigDo.WithContext(ctx)
}

func (t tbSpecialConfig) TableName() string { return t.tbSpecialConfigDo.TableName() }

func (t tbSpecialConfig) Alias() string { return t.tbSpecialConfigDo.Alias() }

func (t *tbSpecialConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbSpecialConfig) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 6)
	t.fieldMap["id"] = t.ID
	t.fieldMap["type"] = t.Type
	t.fieldMap["value"] = t.Value
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["key"] = t.Key
}

func (t tbSpecialConfig) clone(db *gorm.DB) tbSpecialConfig {
	t.tbSpecialConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbSpecialConfig) replaceDB(db *gorm.DB) tbSpecialConfig {
	t.tbSpecialConfigDo.ReplaceDB(db)
	return t
}

type tbSpecialConfigDo struct{ gen.DO }

func (t tbSpecialConfigDo) Debug() *tbSpecialConfigDo {
	return t.withDO(t.DO.Debug())
}

func (t tbSpecialConfigDo) WithContext(ctx context.Context) *tbSpecialConfigDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbSpecialConfigDo) ReadDB() *tbSpecialConfigDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbSpecialConfigDo) WriteDB() *tbSpecialConfigDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbSpecialConfigDo) Session(config *gorm.Session) *tbSpecialConfigDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbSpecialConfigDo) Clauses(conds ...clause.Expression) *tbSpecialConfigDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbSpecialConfigDo) Returning(value interface{}, columns ...string) *tbSpecialConfigDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbSpecialConfigDo) Not(conds ...gen.Condition) *tbSpecialConfigDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbSpecialConfigDo) Or(conds ...gen.Condition) *tbSpecialConfigDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbSpecialConfigDo) Select(conds ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbSpecialConfigDo) Where(conds ...gen.Condition) *tbSpecialConfigDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbSpecialConfigDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbSpecialConfigDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbSpecialConfigDo) Order(conds ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbSpecialConfigDo) Distinct(cols ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbSpecialConfigDo) Omit(cols ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbSpecialConfigDo) Join(table schema.Tabler, on ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbSpecialConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbSpecialConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbSpecialConfigDo) Group(cols ...field.Expr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbSpecialConfigDo) Having(conds ...gen.Condition) *tbSpecialConfigDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbSpecialConfigDo) Limit(limit int) *tbSpecialConfigDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbSpecialConfigDo) Offset(offset int) *tbSpecialConfigDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbSpecialConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbSpecialConfigDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbSpecialConfigDo) Unscoped() *tbSpecialConfigDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbSpecialConfigDo) Create(values ...*model.TbSpecialConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbSpecialConfigDo) CreateInBatches(values []*model.TbSpecialConfig, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbSpecialConfigDo) Save(values ...*model.TbSpecialConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbSpecialConfigDo) First() (*model.TbSpecialConfig, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbSpecialConfig), nil
	}
}

func (t tbSpecialConfigDo) Take() (*model.TbSpecialConfig, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbSpecialConfig), nil
	}
}

func (t tbSpecialConfigDo) Last() (*model.TbSpecialConfig, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbSpecialConfig), nil
	}
}

func (t tbSpecialConfigDo) Find() ([]*model.TbSpecialConfig, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbSpecialConfig), err
}

func (t tbSpecialConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbSpecialConfig, err error) {
	buf := make([]*model.TbSpecialConfig, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbSpecialConfigDo) FindInBatches(result *[]*model.TbSpecialConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbSpecialConfigDo) Attrs(attrs ...field.AssignExpr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbSpecialConfigDo) Assign(attrs ...field.AssignExpr) *tbSpecialConfigDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbSpecialConfigDo) Joins(fields ...field.RelationField) *tbSpecialConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbSpecialConfigDo) Preload(fields ...field.RelationField) *tbSpecialConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbSpecialConfigDo) FirstOrInit() (*model.TbSpecialConfig, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbSpecialConfig), nil
	}
}

func (t tbSpecialConfigDo) FirstOrCreate() (*model.TbSpecialConfig, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbSpecialConfig), nil
	}
}

func (t tbSpecialConfigDo) FindByPage(offset int, limit int) (result []*model.TbSpecialConfig, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbSpecialConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbSpecialConfigDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbSpecialConfigDo) Delete(models ...*model.TbSpecialConfig) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbSpecialConfigDo) withDO(do gen.Dao) *tbSpecialConfigDo {
	t.DO = *do.(*gen.DO)
	return t
}
