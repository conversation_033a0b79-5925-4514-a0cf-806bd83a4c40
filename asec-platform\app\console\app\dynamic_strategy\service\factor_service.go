package service

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/consts"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/dto"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/repository"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	accessModel "asdsec.com/asec/platform/pkg/model"
	model "asdsec.com/asec/platform/pkg/model/strategy_model"
	"asdsec.com/asec/platform/pkg/utils"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
	"sync"
)

var factorServiceImpl FactorService

var FactorServiceInit sync.Once

type factorService struct {
	db repository.FactorRepository
}

func (s factorService) CheckDelTimeQuote(c *gin.Context, ids []string) ([]string, []string, error) {
	return s.db.CheckDelTimeQuote(c, ids)
}

func (s factorService) CheckDelQuote(c *gin.Context, ids []string) ([]string, error) {
	return s.db.CheckDelQuote(c, ids)
}

func (s factorService) FactorNetLocation(c *gin.Context) ([]vo.FactorNetLocationResp, aerrors.AError) {
	resp, err := s.db.FactorNetLocation(c)
	if err != nil {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	return resp, nil
}

func (s factorService) FactorProcess(c *gin.Context) ([]vo.FactorProcessResp, aerrors.AError) {
	resp, err := s.db.FactorProcess(c)
	if err != nil {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	return resp, nil
}

func (s factorService) FactorIp(c *gin.Context) ([]vo.FactorIpResp, aerrors.AError) {
	resp, err := s.db.FactorIp(c)
	if err != nil {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	return resp, nil
}

func (s factorService) CreateFactorProcess(c *gin.Context, req vo.CreateFactorProcessReq) aerrors.AError {
	factorProcess := model.FactorProcess{
		Id:           uuid.NewString(),
		SoftwareName: req.SoftwareName,
		ProcessName:  req.ProcessName,
		ProcessDesc:  req.ProcessDesc,
	}
	err := s.db.CreateFactorProcess(c, factorProcess)
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		return aerrors.New("duplicateNameErr,name:"+req.SoftwareName, consts.DuplicateNameErr)
	}
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s factorService) FactorProcessList(c *gin.Context, req vo.ListReq) (vo.FactorProcessListResp, aerrors.AError) {
	resp := vo.FactorProcessListResp{}
	resp, err := s.db.FactorProcessList(c, req)
	if err != nil {
		return resp, aerrors.NewWithError(err, common.OperateError)
	}
	return resp, nil
}

func (s factorService) UpdateFactorProcess(c *gin.Context, req vo.UpdateFactorProcessReq) aerrors.AError {
	factorProcess := model.FactorProcess{
		Id:           req.Id,
		SoftwareName: req.SoftwareName,
		ProcessName:  req.ProcessName,
		ProcessDesc:  req.ProcessDesc,
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	tx := db.Begin()
	// 获取策略变动
	strategyChange, err := s.db.GetStrategyChange(c, req.Id)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	if len(strategyChange) > 0 {
		strategyChange, err = getChangeProcessFactor(strategyChange, req)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		// 更新策略变动
		err = s.saveStrategyChange(tx, strategyChange)
		if err != nil {
			tx.Rollback()
			return aerrors.NewWithError(err, common.OperateError)
		}
	}

	err = s.db.UpdateFactorProcess(tx, factorProcess)
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		tx.Rollback()
		return aerrors.New("duplicateNameErr,name:"+req.SoftwareName, consts.DuplicateNameErr)
	}
	if err != nil {
		tx.Rollback()
		return aerrors.NewWithError(err, common.OperateError)
	}
	tx.Commit()
	return nil
}

func getChangeProcessFactor(strategyChange []dto.StrategyChange, req vo.UpdateFactorProcessReq) ([]dto.StrategyChange, error) {
	for index := range strategyChange {
		rule := dto.DynamicRule{}

		err := jsoniter.Unmarshal(strategyChange[index].DynamicRule.Bytes, &rule)
		if err != nil {
			return nil, err
		}
		for i := range rule.Rule {
			for j := range rule.Rule[i].Condition {
				id := utils.MapGetStr(rule.Rule[i].Condition[j], "id")
				if id == "" || id != req.Id {
					continue
				}
				rule.Rule[i].Condition[j]["process_name"] = req.ProcessName
				rule.Rule[i].Condition[j]["software_name"] = req.SoftwareName
			}
		}
		bytes, _ := json.Marshal(rule)
		err = strategyChange[index].DynamicRule.Set(bytes)
		if err != nil {
			return nil, err
		}
	}
	return strategyChange, nil

}

func (s factorService) DelFactorProcess(c *gin.Context, req vo.DelReq) aerrors.AError {
	return aerrors.ReturnWithError(s.db.DelFactorProcess(c, req), common.OperateError)
}

func (s factorService) UpdateNetLocation(c *gin.Context, req vo.UpdateNetLocationReq) aerrors.AError {
	netLocation := model.FactorNetLocation{
		Id:              req.Id,
		NetLocationName: req.NetLocationName,
		NetLocationDesc: req.NetLocationDesc,
		PublicIp:        req.PublicIp,
		PrivateIp:       req.PrivateIp,
		Dns:             req.Dns,
		WifiSsd:         req.WifiSsd,
	}

	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	tx := db.Begin()
	// 获取策略变动
	strategyChange, err := s.db.GetStrategyChange(c, req.Id)
	if len(strategyChange) > 0 {
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		strategyChange, err = getChangeLocationFactor(strategyChange, req)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		// 更新策略变动
		err = s.saveStrategyChange(tx, strategyChange)
		if err != nil {
			tx.Rollback()
			return aerrors.NewWithError(err, common.OperateError)
		}
	}

	err = s.db.UpdateNetLocation(tx, netLocation)
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		tx.Rollback()
		return aerrors.New("duplicateNameErr,name:"+req.NetLocationName, consts.DuplicateNameErr)
	}
	if err != nil {
		tx.Rollback()
		return aerrors.NewWithError(err, common.OperateError)
	}
	tx.Commit()
	return nil
}

func getChangeLocationFactor(strategyChange []dto.StrategyChange, req vo.UpdateNetLocationReq) ([]dto.StrategyChange, error) {
	for index := range strategyChange {
		rule := dto.DynamicRule{}

		err := jsoniter.Unmarshal(strategyChange[index].DynamicRule.Bytes, &rule)
		if err != nil {
			return nil, err
		}
		for i := range rule.Rule {
			for j := range rule.Rule[i].Condition {
				id := utils.MapGetStr(rule.Rule[i].Condition[j], "id")
				if id == "" || id != req.Id {
					continue
				}
				if len(req.Dns) > 0 {
					rule.Rule[i].Condition[j]["dns"] = req.Dns
				} else {
					delete(rule.Rule[i].Condition[j], "dns")
				}

				if len(req.WifiSsd) > 0 {
					rule.Rule[i].Condition[j]["wifi_ssd"] = req.WifiSsd
				} else {
					delete(rule.Rule[i].Condition[j], "wifi_ssd")
				}

				if len(req.PublicIp) > 0 {
					rule.Rule[i].Condition[j]["public_ip"] = req.PublicIp
				} else {
					delete(rule.Rule[i].Condition[j], "public_ip")
				}

				if len(req.PrivateIp) > 0 {
					rule.Rule[i].Condition[j]["private_ip"] = req.PrivateIp
				} else {
					delete(rule.Rule[i].Condition[j], "private_ip")
				}

				rule.Rule[i].Condition[j]["net_location_name"] = req.NetLocationName
			}
		}
		bytes, _ := json.Marshal(rule)
		err = strategyChange[index].DynamicRule.Set(bytes)
		if err != nil {
			return nil, err
		}
	}
	return strategyChange, nil
}

func (s factorService) DelNetLocation(c *gin.Context, req vo.DelReq) aerrors.AError {
	return aerrors.ReturnWithError(s.db.DelNetLocation(c, req), common.OperateError)
}

func (s factorService) NetLocationList(c *gin.Context, req vo.ListReq) (vo.NetLocationListResp, aerrors.AError) {
	resp := vo.NetLocationListResp{}
	resp, err := s.db.NetLocationList(c, req)
	if err != nil {
		return resp, aerrors.NewWithError(err, common.OperateError)
	}
	return resp, nil
}

func (s factorService) CreateNetLocation(c *gin.Context, req vo.CreateNetLocationReq) aerrors.AError {
	netLocation := model.FactorNetLocation{
		Id:              uuid.NewString(),
		NetLocationName: req.NetLocationName,
		NetLocationDesc: req.NetLocationDesc,
		PublicIp:        req.PublicIp,
		PrivateIp:       req.PrivateIp,
		Dns:             req.Dns,
		WifiSsd:         req.WifiSsd,
	}
	err := s.db.CreateNetLocation(c, netLocation)
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		return aerrors.New("duplicateNameErr,name:"+req.NetLocationName, consts.DuplicateNameErr)
	}
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s factorService) DelFactorIp(c *gin.Context, req vo.DelReq) aerrors.AError {
	err := s.db.DelFactorIp(c, req)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s factorService) UpdateFactorIp(c *gin.Context, req vo.UpdateFactorIpReq) aerrors.AError {
	factorIp := model.FactorIp{
		ID:        req.Id,
		IpName:    req.IpName,
		IpDesc:    req.IpDesc,
		IpContent: req.IpContent,
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	tx := db.Begin()
	// 获取策略变动
	strategyChange, err := s.db.GetStrategyChange(c, req.Id)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	if len(strategyChange) > 0 {
		strategyChange, err = getChangeIpFactor(strategyChange, req)
		if err != nil {
			return aerrors.NewWithError(err, common.OperateError)
		}
		// 更新策略变动
		err = s.saveStrategyChange(tx, strategyChange)
		if err != nil {
			tx.Rollback()
			return aerrors.NewWithError(err, common.OperateError)
		}
	}

	err = s.db.UpdateFactorIp(tx, factorIp)
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		tx.Rollback()
		return aerrors.New("duplicateNameErr,name:"+req.CreateFactorIpReq.IpName, consts.DuplicateNameErr)
	}
	if err != nil {
		tx.Rollback()
		return aerrors.NewWithError(err, common.OperateError)
	}
	tx.Commit()
	return nil
}

func getChangeIpFactor(strategyChange []dto.StrategyChange, req vo.UpdateFactorIpReq) ([]dto.StrategyChange, error) {
	for index := range strategyChange {
		rule := dto.DynamicRule{}

		err := jsoniter.Unmarshal(strategyChange[index].DynamicRule.Bytes, &rule)
		if err != nil {
			return nil, err
		}
		for i := range rule.Rule {
			for j := range rule.Rule[i].Condition {
				id := utils.MapGetStr(rule.Rule[i].Condition[j], "id")
				if id == "" || id != req.Id {
					continue
				}
				rule.Rule[i].Condition[j]["ip_content"] = req.IpContent
				rule.Rule[i].Condition[j]["ip_name"] = req.IpName
			}
		}
		bytes, _ := json.Marshal(rule)
		err = strategyChange[index].DynamicRule.Set(bytes)
		if err != nil {
			return nil, err
		}
	}
	return strategyChange, nil
}

func (s factorService) FactorIpList(c *gin.Context, req vo.ListReq) (vo.FactorIpListResp, aerrors.AError) {
	resp, err := s.db.FactorIpList(c, req)
	if err != nil {
		return vo.FactorIpListResp{}, aerrors.NewWithError(err, common.OperateError)
	}
	return resp, nil
}

func (s factorService) CreateFactorIp(c *gin.Context, req vo.CreateFactorIpReq) aerrors.AError {
	factorIp := model.FactorIp{
		ID:        uuid.NewString(),
		IpName:    req.IpName,
		IpDesc:    req.IpDesc,
		IpContent: req.IpContent,
	}
	err := s.db.CreateFactorIp(c, factorIp)
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		return aerrors.New("duplicateNameErr,name:"+req.IpName, consts.DuplicateNameErr)
	}
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s factorService) DelFactorTime(c *gin.Context, req vo.DelReq) aerrors.AError {
	return s.db.DelFactorTime(c, req)
}

func (s factorService) FactorTimeListPage(c *gin.Context, req vo.FactorTimeListPageReq) (vo.FactorTimeListPageResp, aerrors.AError) {
	listPage, err := s.db.FactorTimeListPage(c, req)
	if err != nil {
		return vo.FactorTimeListPageResp{}, aerrors.NewWithError(err, common.OperateError)
	}
	return listPage, nil
}

func (s factorService) UpdateFactorTime(c *gin.Context, req vo.UpdateFactorTimeReq) aerrors.AError {
	factorTime := model.FactorTime{
		Id:             req.Id,
		IntervalType:   req.CreateFactorTimeReq.IntervalType,
		Sunday:         req.CreateFactorTimeReq.Sunday,
		Monday:         req.CreateFactorTimeReq.Monday,
		Thursday:       req.CreateFactorTimeReq.Thursday,
		Wednesday:      req.CreateFactorTimeReq.Wednesday,
		Tuesday:        req.CreateFactorTimeReq.Tuesday,
		Friday:         req.CreateFactorTimeReq.Friday,
		Saturday:       req.CreateFactorTimeReq.Saturday,
		DailyStartTime: req.CreateFactorTimeReq.DailyStartTime,
		DailyEndTime:   req.CreateFactorTimeReq.DailyEndTime,
		AllDayEnable:   req.CreateFactorTimeReq.AllDayEnable,
		GapName:        req.CreateFactorTimeReq.GapName,
	}
	err := s.db.UpdateFactorTime(c, factorTime)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s factorService) FactorTimeList(c *gin.Context) ([]vo.FactorTimeListResp, aerrors.AError) {
	list, err := s.db.FactorTimeList(c)
	if err != nil {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	return list, nil
}

func (s factorService) AddFactorTime(c *gin.Context, req vo.CreateFactorTimeReq) aerrors.AError {
	factorTime := model.FactorTime{
		Id:             uuid.New().String(),
		IntervalType:   req.IntervalType,
		Sunday:         req.Sunday,
		Monday:         req.Monday,
		Thursday:       req.Thursday,
		Wednesday:      req.Wednesday,
		Tuesday:        req.Tuesday,
		Friday:         req.Friday,
		Saturday:       req.Saturday,
		DailyStartTime: req.DailyStartTime,
		DailyEndTime:   req.DailyEndTime,
		AllDayEnable:   req.AllDayEnable,
		GapName:        req.GapName,
	}
	err := s.db.AddFactorTime(c, factorTime)
	if err != nil && aerrors.IsPgErrorCode(err, aerrors.UniqueViolationErr) {
		return aerrors.New("duplicateNameErr,name:"+req.GapName, consts.DuplicateNameErr)
	}
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

func (s factorService) FactorList(c *gin.Context) ([]vo.FactorListResp, aerrors.AError) {
	list, err := s.db.FactorList(c)
	if err != nil {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	return list, nil
}

func (s factorService) saveStrategyChange(c *gorm.DB, strategyChange []dto.StrategyChange) error {
	var needUpdates []accessModel.AccessStrategy
	for _, s := range strategyChange {

		rego, err := DynamicRuleToRego(s.DynamicRule)
		if err != nil {
			return err
		}
		changeUpdate := accessModel.AccessStrategy{}
		changeUpdate.ID = s.Id

		changeUpdate.DynamicRule = s.DynamicRule
		changeUpdate.RegoFile = rego
		needUpdates = append(needUpdates, changeUpdate)
	}
	return s.db.StrategyRuleChange(c, needUpdates)
}

func GetFactorService() FactorService {
	FactorServiceInit.Do(func() {
		factorServiceImpl = factorService{db: repository.NewFactorRepository()}
	})
	return factorServiceImpl
}

type FactorService interface {
	FactorList(c *gin.Context) ([]vo.FactorListResp, aerrors.AError)
	AddFactorTime(c *gin.Context, req vo.CreateFactorTimeReq) aerrors.AError
	FactorTimeList(c *gin.Context) ([]vo.FactorTimeListResp, aerrors.AError)
	FactorTimeListPage(c *gin.Context, req vo.FactorTimeListPageReq) (vo.FactorTimeListPageResp, aerrors.AError)
	UpdateFactorTime(c *gin.Context, req vo.UpdateFactorTimeReq) aerrors.AError
	DelFactorTime(c *gin.Context, req vo.DelReq) aerrors.AError
	CreateFactorIp(c *gin.Context, req vo.CreateFactorIpReq) aerrors.AError
	FactorIpList(c *gin.Context, req vo.ListReq) (vo.FactorIpListResp, aerrors.AError)
	UpdateFactorIp(c *gin.Context, req vo.UpdateFactorIpReq) aerrors.AError
	DelFactorIp(c *gin.Context, req vo.DelReq) aerrors.AError
	CreateNetLocation(c *gin.Context, req vo.CreateNetLocationReq) aerrors.AError
	NetLocationList(c *gin.Context, req vo.ListReq) (vo.NetLocationListResp, aerrors.AError)
	UpdateNetLocation(c *gin.Context, req vo.UpdateNetLocationReq) aerrors.AError
	DelNetLocation(c *gin.Context, req vo.DelReq) aerrors.AError
	CreateFactorProcess(c *gin.Context, req vo.CreateFactorProcessReq) aerrors.AError
	FactorProcessList(c *gin.Context, req vo.ListReq) (vo.FactorProcessListResp, aerrors.AError)
	UpdateFactorProcess(c *gin.Context, req vo.UpdateFactorProcessReq) aerrors.AError
	DelFactorProcess(c *gin.Context, req vo.DelReq) aerrors.AError
	FactorIp(c *gin.Context) ([]vo.FactorIpResp, aerrors.AError)
	FactorProcess(c *gin.Context) ([]vo.FactorProcessResp, aerrors.AError)
	FactorNetLocation(c *gin.Context) ([]vo.FactorNetLocationResp, aerrors.AError)
	CheckDelQuote(c *gin.Context, ids []string) ([]string, error)
	CheckDelTimeQuote(c *gin.Context, ids []string) ([]string, []string, error)
}
