<template>
  <div :class="asideClass" :style="asideStyle">
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  width: {
    type: String,
    default: '180px'
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  collapsedWidth: {
    type: String,
    default: '54px'
  }
})

const asideClass = computed(() => {
  const classes = ['aside']

  if (props.collapsed) {
    classes.push('collapsed')
  }

  return classes.join(' ')
})

const asideStyle = computed(() => {
  return {
    width: props.collapsed ? props.collapsedWidth : props.width
  }
})
</script>

<style scoped>
.aside {
  -webkit-transition: width 0.3s ease;
  -moz-transition: width 0.3s ease;
  -ms-transition: width 0.3s ease;
  -o-transition: width 0.3s ease;
  transition: width 0.3s ease;
  overflow: hidden;
  -webkit-box-flex: 0;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
