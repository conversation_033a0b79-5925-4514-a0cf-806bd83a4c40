package repository

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/dto"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/auth_model"
	"asdsec.com/asec/platform/pkg/model/strategy_model"
	"context"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
	"strings"
)

var (
	emptyJson = pgtype.JSONB{
		Bytes:  []byte("{}"),
		Status: pgtype.Present,
	}
)

const agentAppCacheKey = "agent:app:cache"

type strategyRepository struct {
}

func (s strategyRepository) ResetAccessStrategyCache(ctx context.Context) error {
	rdb, err := global.GetRedisClient(ctx)
	if err != nil {
		return err
	}
	return rdb.Del(ctx, agentAppCache<PERSON>ey).Err()
}

var countSql = `select strategy_id as id, sum(count) as count, max(count_data) as max_count_data
	from (
			 select strategy_id, sum(hits) as count, max(date) as count_data
			 from asec.four_log_count
			 where strategy_id in ? and date>= today()-interval 7 DAY
			 group by strategy_id
	
			 union all
	
			 select strategy_id_int as strategy_id, sum(hits), max(date) as count_data
			 from asec.seven_log_count
			 where strategy_id in ? and date>= today()-interval 7 DAY
			 group by strategy_id)
	group by strategy_id`

func (s strategyRepository) ListMatchCount(c *gin.Context, ids []int64) ([]dto.MatchCountDto, error) {
	var _l []dto.MatchCountDto
	if len(ids) <= 0 {
		return _l, nil
	}
	db, err := global.GetCkClient(c)
	if err != nil {
		return _l, err
	}
	err = db.Raw(countSql, ids, ids).Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (s strategyRepository) GetTimeDetail(c *gin.Context, timeId string) (*strategy_model.FactorTime, error) {
	_r := strategy_model.FactorTime{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return &_r, err
	}
	err = db.Model(&strategy_model.FactorTime{}).Where("id = ?", timeId).Find(&_r).Error
	return &_r, err
}

func (s strategyRepository) CheckTimeId(c *gin.Context, timeId string) (bool, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return false, err
	}
	var exists bool
	err = db.Raw("select exists (select id from tb_factor_time where id=?)", timeId).
		Find(&exists).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}
	return exists, nil
}

func (s strategyRepository) ReGroupByRowNumber(c *gin.Context) ([]dto.ReGroupStrategy, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []dto.ReGroupStrategy
	err = db.Table("tb_access_strategy t1").
		Select("ROW_NUMBER() OVER (ORDER BY t2.priority, t1.priority) AS row_number," +
			"t1.ID as id,t1.priority as priority").
		Joins("INNER JOIN tb_dynamic_strategy_group t2 ON t1.strategy_group_id = t2.ID ").
		Order("t2.priority,t1.priority").
		Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (s strategyRepository) GerStrategySortPriority(c *gin.Context, excludeId uint64, excludeGroupId string, groupId string) ([]dto.StrategyPriority, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []dto.StrategyPriority
	tx := db.Model(&model.AccessStrategy{}).Select("id , priority")
	if excludeId != 0 {
		tx = tx.Where("id != ?", excludeId)
	}
	if excludeGroupId != "" {
		tx = tx.Where("strategy_group_id != ?", excludeGroupId)
	}
	if groupId != "" {
		tx = tx.Where("strategy_group_id = ?", groupId)
	}

	err = tx.Order("priority").Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

var (
	arrayAgg = `COALESCE(array_agg(distinct CASE WHEN (u_i.display_name is null or u_i.display_name ='') THEN u_i.name ELSE u_i.display_name END ) filter ( where u_i.id is not null ) ,'{}') as user_names,
       COALESCE ( array_agg(distinct u_ei.name) filter ( where u_ei.id is not null ), '{}' )      as exclude_user_names,
       COALESCE ( array_agg(distinct ug_i.name) filter ( where ug_i.id is not null ), '{}' )      as user_group_names,
       COALESCE ( array_agg(distinct ug_ei.name) filter ( where ug_ei.id is not null ), '{}' )    as exclude_user_group_names,
       COALESCE ( array_agg(distinct ur_i.name) filter ( where ur_i.id is not null ), '{}' )      as user_role_names,
       COALESCE ( array_agg(distinct ur_ei.name) filter ( where ur_ei.id is not null ), '{}' )    as exclude_user_role_names,
       COALESCE ( json_agg( DISTINCT jsonb_build_object('app_name',ta.app_name,'app_type',ta.app_type) ) FILTER ( WHERE ta.app_name IS NOT NULL )  ,'[]' )as app,
       COALESCE ( array_agg(distinct tag.group_name) filter ( where tag.id is not null ), '{}' )  as app_tag_names`
	strategyListSelect = `t1.*,tft.*,
       t3.group_name,` + arrayAgg
	strategyDetailSelect = `t1.*,t2.group_name as strategy_group_name,
		COALESCE ( json_agg( DISTINCT jsonb_build_object('app_name',ta.app_name,'app_type',ta.app_type) ) FILTER ( WHERE ta.app_name IS NOT NULL )  ,'[]' )as app,
		COALESCE ( array_agg(distinct tag.group_name) filter ( where tag.id is not null ), '{}' )  as app_tag_names`
)

func (s strategyRepository) StrategyDetail(c *gin.Context, id uint64) (vo.StrategyDetailResp, error) {
	resp := vo.StrategyDetailResp{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return resp, err
	}
	err = db.Table("tb_access_strategy t1").
		Select(strategyDetailSelect).
		Joins("inner join tb_dynamic_strategy_group t2 on t1.strategy_group_id=t2.id").
		Joins("left outer join tb_application ta on ta.id = any (t1.app_ids)").
		Joins("left outer join tb_app_group tag on tag.id = any (t1.app_group_ids)").
		Where("t1.id=?", id).
		Group("t1.id,t2.id").Find(&resp).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return resp, err
	}

	var count int
	err = db.Model(&strategy_model.DynamicStrategyMatch{}).Select("CASE WHEN SUM ( match_count ) IS NOT NULL THEN SUM ( match_count ) ELSE 0  END CASE").Where("strategy_id = ?", id).Find(&count).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return resp, err
	}

	resp.MatchCount = count
	return resp, nil
}

func (s strategyRepository) StrategyList(c *gin.Context, req vo.StrategyListReq) ([]dto.StrategyListDto, int64, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, 0, err
	}

	// 1. COUNT 查询
	countQuery := db.Table("tb_access_strategy t1").
		Select("COUNT(DISTINCT t1.id)").
		Joins("inner join tb_dynamic_strategy_group t3 on t1.strategy_group_id = t3.id")

	// 2. 基础策略查询
	baseQuery := db.Table("tb_access_strategy t1").
		Select("t1.*, t3.group_name, tft.*").
		Joins("inner join tb_dynamic_strategy_group t3 on t1.strategy_group_id = t3.id").
		Joins("left join tb_factor_time tft on t1.time_id = tft.id")

	// 添加基础条件
	if req.GroupId != "" {
		countQuery = countQuery.Where("t1.strategy_group_id = ?", req.GroupId)
		baseQuery = baseQuery.Where("t1.strategy_group_id = ?", req.GroupId)
	}
	if req.Id != 0 {
		countQuery = countQuery.Where("t1.id = ?", req.Id)
		baseQuery = baseQuery.Where("t1.id = ?", req.Id)
	}

	// 处理搜索条件
	if req.Search != "" {
		searchTerm := fmt.Sprintf("%%%v%%", strings.ToLower(req.Search))
		searchCondition := db.Where("LOWER(t1.strategy_name) LIKE ? OR LOWER(t1.strategy_detail) LIKE ?", searchTerm, searchTerm)

		// 精确搜索用户名
		var users []auth_model.TbUserEntity
		_ = db.Table("tb_user_entity").Where("LOWER(display_name) = ? OR LOWER(name) = ?", strings.ToLower(req.Search), strings.ToLower(req.Search)).Find(&users).Error
		var userIds []string
		var groupIds []string
		var allGroupIds []string
		var roleIds []string
		for _, user := range users {
			userIds = append(userIds, user.ID)
			groupIds = append(groupIds, user.GroupID)
		}
		// 基于userIds查询roleIds
		_ = db.Table("tb_user_role").Select("DISTINCT role_id").Where("user_id IN ?", userIds).Find(&roleIds).Error

		// 基于groupIds扩展查询其所有父级组
		groupSql := `
            WITH RECURSIVE cte AS (
                SELECT ID,
                    NAME,
                    parent_group_id,
                    root_group_id 
                FROM
                    tb_user_group 
                WHERE
                    ID = ANY ( ? ) UNION ALL
                SELECT T
                    .ID,
                    T.NAME,
                    T.parent_group_id,
                    T.root_group_id 
                FROM
                    cte
                    JOIN tb_user_group T ON T.ID = cte.parent_group_id 
                WHERE
                    T.parent_group_id != T.root_group_id 
                ) SELECT ID FROM cte;
            `
		db.Raw(groupSql, pq.Array(groupIds)).Scan(&allGroupIds)

		// 基于用户ID、组ID以及角色ID查询策略交集
		if len(userIds) > 0 || len(groupIds) > 0 || len(roleIds) > 0 {
			var conditions []string
			var params []interface{}

			if len(userIds) > 0 {
				conditions = append(conditions, "t1.user_ids && ?")
				params = append(params, pq.Array(userIds))
			}
			if len(groupIds) > 0 {
				conditions = append(conditions, "t1.user_group_ids && ?")
				params = append(params, pq.Array(allGroupIds))
			}
			if len(roleIds) > 0 {
				conditions = append(conditions, "t1.role_ids && ?")
				params = append(params, pq.Array(roleIds))
			}

			searchCondition = searchCondition.Or(strings.Join(conditions, " OR "), params...)
		}

		countQuery = countQuery.Where(searchCondition)
		baseQuery = baseQuery.Where(searchCondition)
	}

	// 执行Count查询
	var total int64
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 执行基础策略查询
	baseQuery = baseQuery.Order("t3.priority, t1.priority").
		Limit(int(req.Limit)).Offset(int(req.Offset))

	var strategies []dto.StrategyListDto
	if err := baseQuery.Find(&strategies).Error; err != nil {
		return nil, 0, err
	}

	// 如果没有数据，直接返回
	if len(strategies) == 0 {
		return strategies, total, nil
	}

	// 收集策略ID
	var strategyIds []uint64
	for _, s := range strategies {
		id, err := strconv.ParseUint(s.Id, 10, 64)
		if err != nil {
			continue
		}
		strategyIds = append(strategyIds, id)
	}

	// 3. 查询用户相关信息
	var userInfos []struct {
		ID                uint64         `gorm:"column:id"`
		UserNames         pq.StringArray `gorm:"column:user_names;type:text[]"`
		ExcludeUserNames  pq.StringArray `gorm:"column:exclude_user_names;type:text[]"`
		UserGroupNames    pq.StringArray `gorm:"column:user_group_names;type:text[]"`
		ExcludeGroupNames pq.StringArray `gorm:"column:exclude_user_group_names;type:text[]"`
		UserRoleNames     pq.StringArray `gorm:"column:user_role_names;type:text[]"`
		ExcludeRoleNames  pq.StringArray `gorm:"column:exclude_user_role_names;type:text[]"`
	}

	userQuery := db.Table("tb_access_strategy t1").
		Select(`
			t1.id,
			COALESCE(array_agg(distinct CASE WHEN (u_i.display_name is null or u_i.display_name ='') 
				THEN u_i.name ELSE u_i.display_name END) filter (where u_i.id is not null), '{}') as user_names,
			COALESCE(array_agg(distinct u_ei.name) filter (where u_ei.id is not null), '{}') as exclude_user_names,
			COALESCE(array_agg(distinct ug_i.name) filter (where ug_i.id is not null), '{}') as user_group_names,
			COALESCE(array_agg(distinct ug_ei.name) filter (where ug_ei.id is not null), '{}') as exclude_user_group_names,
			COALESCE(array_agg(distinct ur_i.name) filter (where ur_i.id is not null), '{}') as user_role_names,
			COALESCE(array_agg(distinct ur_ei.name) filter (where ur_ei.id is not null), '{}') as exclude_user_role_names
		`).
		Joins("left join tb_user_entity u_i on u_i.id = ANY (t1.user_ids)").
		Joins("left join tb_user_entity u_ei on u_ei.id = ANY (t1.exclude_user_ids)").
		Joins("left join tb_user_group ug_i on ug_i.id = any (t1.user_group_ids)").
		Joins("left join tb_user_group ug_ei on ug_ei.id = any (t1.exclude_user_group_ids)").
		Joins("left join tb_role ur_i on ur_i.id = any (t1.role_ids)").
		Joins("left join tb_role ur_ei on ur_ei.id = any (t1.exclude_user_role_ids)").
		Where("t1.id IN ?", strategyIds).
		Group("t1.id")

	if err := userQuery.Find(&userInfos).Error; err != nil {
		return nil, 0, err
	}

	// 4. 查询应用信息
	appQuery := db.Table("tb_access_strategy t1").
		Select(`
			t1.id,
			COALESCE(json_agg(DISTINCT jsonb_build_object('app_name',ta.app_name,'app_type',ta.app_type)) 
				FILTER (WHERE ta.app_name IS NOT NULL), '[]') as app,
			COALESCE(array_agg(distinct tag.group_name) filter (where tag.id is not null), '{}') as app_tag_names
		`).
		Joins("left join tb_application ta on ta.id = any (t1.app_ids)").
		Joins("left join tb_app_group tag on tag.id = any (t1.app_group_ids)").
		Where("t1.id IN ?", strategyIds).
		Group("t1.id")

	var appInfos []struct {
		ID          uint64         `gorm:"column:id"`
		App         pgtype.JSONB   `gorm:"column:app"`
		AppTagNames pq.StringArray `gorm:"column:app_tag_names;type:text[]"`
	}
	if err := appQuery.Find(&appInfos).Error; err != nil {
		return nil, 0, err
	}

	// 5. 合并数据
	userInfoMap := make(map[uint64]struct {
		UserNames         []string
		ExcludeUserNames  []string
		UserGroupNames    []string
		ExcludeGroupNames []string
		UserRoleNames     []string
		ExcludeRoleNames  []string
	})
	for _, info := range userInfos {
		userInfoMap[info.ID] = struct {
			UserNames         []string
			ExcludeUserNames  []string
			UserGroupNames    []string
			ExcludeGroupNames []string
			UserRoleNames     []string
			ExcludeRoleNames  []string
		}{
			UserNames:         info.UserNames,
			ExcludeUserNames:  info.ExcludeUserNames,
			UserGroupNames:    info.UserGroupNames,
			ExcludeGroupNames: info.ExcludeGroupNames,
			UserRoleNames:     info.UserRoleNames,
			ExcludeRoleNames:  info.ExcludeRoleNames,
		}
	}

	appInfoMap := make(map[uint64]struct {
		App         pgtype.JSONB
		AppTagNames []string
	})
	for _, info := range appInfos {
		appInfoMap[info.ID] = struct {
			App         pgtype.JSONB
			AppTagNames []string
		}{
			App:         info.App,
			AppTagNames: info.AppTagNames,
		}
	}

	// 6. 更新策略数据
	for i := range strategies {
		id, err := strconv.ParseUint(strategies[i].Id, 10, 64)
		if err != nil {
			continue
		}

		if info, ok := userInfoMap[id]; ok {
			strategies[i].UserNames = info.UserNames
			strategies[i].ExcludeUserNames = info.ExcludeUserNames
			strategies[i].UserGroupNames = info.UserGroupNames
			strategies[i].ExcludeUserGroupNames = info.ExcludeGroupNames
			strategies[i].UserRoleNames = info.UserRoleNames
			strategies[i].ExcludeUserRoleNames = info.ExcludeRoleNames
		}

		if info, ok := appInfoMap[id]; ok {
			strategies[i].App = info.App
			strategies[i].AppTagNames = info.AppTagNames
		}
	}

	return strategies, total, nil
}

func (s strategyRepository) StrategyMove(c *gin.Context, changeStrategy []model.AccessStrategy, strategy model.AccessStrategy) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		if len(changeStrategy) > 0 {
			dbErr := s.UpdatesStrategyPriority(tx, changeStrategy)
			if dbErr != nil {
				return dbErr
			}
		}
		dbErr := tx.Model(&model.AccessStrategy{}).Where("id=?", strategy.ID).Updates(strategy).Error
		if dbErr != nil {
			return dbErr
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// GetStrategyPriorityByGroup 查询某个组内最高或者最低的优先级
func (s strategyRepository) GetStrategyPriorityByGroup(c *gin.Context, moveReq vo.StrategyMoveReq) (int, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return 0, err
	}
	tx := db.Model(&model.AccessStrategy{}).Select("priority").Where("strategy_group_id=?", moveReq.GroupId)
	// 置顶
	if moveReq.DestPriority == 0 {
		tx = tx.Order("priority").Limit(1)
	} else if moveReq.DestPriority == -1 {
		tx = tx.Order("priority desc").Limit(1)
	}
	var p int
	err = tx.Find(&p).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	return p, err
}

func (s strategyRepository) StrategySimpleList(c *gin.Context, groupId string) ([]vo.StrategySimpleListResp, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []vo.StrategySimpleListResp
	err = db.Model(&model.AccessStrategy{}).Select("id,strategy_name,priority").Where("strategy_group_id=?", groupId).Order("priority").Find(&_l).Error
	if err != nil {
		return nil, err
	}
	return _l, nil
}

var (
	delFourSql  = `alter table asec.four_log_count delete where strategy_id in ?`
	delSevenSql = `alter table asec.seven_log_count delete where strategy_id_int in ?`
)

func (s strategyRepository) ClearStrategyMatch(c *gin.Context, req dto.DelStrategyDto) error {
	db, err := global.GetCkClient(c)
	if err != nil {
		return err
	}
	err = db.Exec(delFourSql, req.Ids).Error
	if err != nil {
		return err
	}
	err = db.Exec(delSevenSql, req.Ids).Error
	if err != nil {
		return err
	}

	// 将创建时间 更改为最新,否则会出现清除计数后变成七天未访问的情况
	pDb, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	err = pDb.Model(&model.AccessStrategy{}).Where("id in ?", req.Ids).Update("created_at", "now()").Error
	return err
}

// GetPriorityByGroup 根据组的优先级关系,通过连表查询获取新增的策略的优先级,原生SQL:
// select t1.priority
// from tb_access_strategy t1
// inner join tb_dynamic_strategy_group t2 on t1.strategy_group_id = t2.id
// where t2.priority > (select priority from tb_dynamic_strategy_group where id = '1')
// order by t2.priority, t1.priority;
func (s strategyRepository) GetPriorityByGroup(c *gin.Context, groupId string) (int, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return 0, err
	}

	var p int
	tx := db.Model(&strategy_model.DynamicStrategyGroup{}).Select("priority").Where("id=?", groupId)
	// 比当前组优先级更低的策略,需要降级的
	err = db.Table("tb_access_strategy t1").
		Select("t1.priority").
		Joins("inner join tb_dynamic_strategy_group t2 on t1.strategy_group_id = t2.id").
		Where("t2.priority >= (?)", tx).Order("t2.priority, t1.priority").Limit(1).
		Find(&p).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	// 不存在比当前组优先级更低的策略,则新增的策略优先级最低
	if p == 0 {
		var count int
		err := db.Model(&model.AccessStrategy{}).Select("count(1)").Find(&count).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, err
		}
		p = count + 1
	}
	return p, nil
}

// UpdatesStrategyPriority 只批量更新优先级
func (s strategyRepository) UpdatesStrategyPriority(tx *gorm.DB, list []model.AccessStrategy) error {
	if len(list) <= 0 {
		return nil
	}
	return tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"priority"}),
	}).Create(&list).Error
}

// UpdatesStrategyGroupId 只批量分组id
func (s strategyRepository) UpdatesStrategyGroupId(tx *gorm.DB, list []model.AccessStrategy) error {
	if len(list) <= 0 {
		return nil
	}
	return tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"strategy_group_id"}),
	}).Create(&list).Error
}

func (s strategyRepository) GetStrategyByPriority(c *gin.Context, priority int, compare string) ([]model.AccessStrategy, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var _l []model.AccessStrategy
	tx := db.Model(&model.AccessStrategy{}).Select("id,priority")
	if compare == "higher" {
		tx = tx.Where("priority>?", priority)
	} else if compare == "lower" {
		tx = tx.Where("priority<?", priority)
	}
	err = tx.Order("priority").Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (s strategyRepository) GetStrategyDetail(c *gin.Context, id uint64) (model.AccessStrategy, error) {
	strategy := model.AccessStrategy{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return strategy, err
	}
	err = db.Model(&model.AccessStrategy{}).Select("*").Where("id = ?", id).Find(&strategy).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return strategy, err
	}
	return strategy, nil
}

func (s strategyRepository) EnableStrategy(c *gin.Context, req dto.EnableStrategyDto) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	if len(req.Ids) <= 0 {
		return nil
	}
	return db.Model(&model.AccessStrategy{}).Where("id in ?", req.Ids).Update("strategy_status", req.Enable).Error
}

func (s strategyRepository) DelStrategy(c *gin.Context, req dto.DelStrategyDto) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		// 找出批量删除中优先级最高的那一个
		var priority int
		dbErr := tx.Model(&model.AccessStrategy{}).
			Select("priority").Where("id in ?", req.Ids).Order("priority").
			Limit(1).Find(&priority).Error
		if dbErr != nil {
			return dbErr
		}

		// 在事务中进行删除
		dbErr = tx.Where("id in ?", req.Ids).Delete(&model.AccessStrategy{}).Error
		if dbErr != nil {
			return dbErr
		}

		// 删除后将剩下的低优先级的策略重新 前移优先级
		var changeList []model.AccessStrategy
		dbErr = tx.Model(&model.AccessStrategy{}).
			Select("id,priority").Where("priority>?", priority).Order("priority").
			Find(&changeList).Error
		if dbErr != nil {
			return dbErr
		}
		// 重排并前移剩下策略的优先级
		for i := range changeList {
			changeList[i].Priority = priority + i
		}
		return s.UpdatesStrategyPriority(tx, changeList)
	})

	if err != nil {
		return err
	}
	return nil
}

func (s strategyRepository) UpdateStrategy(c *gin.Context, strategy model.AccessStrategy) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	if len(strategy.AccessConstraint.Bytes) <= 0 {
		strategy.AccessConstraint = emptyJson
	}
	if len(strategy.ActionConfig.Bytes) <= 0 {
		strategy.ActionConfig = emptyJson
	}
	if len(strategy.DynamicRule.Bytes) <= 0 {
		strategy.DynamicRule = emptyJson
	}
	if len(strategy.DenyInfo.Bytes) <= 0 {
		strategy.DenyInfo = emptyJson
	}
	if len(strategy.UserRiskRule.Bytes) <= 0 {
		strategy.UserRiskRule = emptyJson
	}
	err = db.Omit("created_at", "priority").Save(&strategy).Error
	if err != nil {
		return err
	}
	return nil
}

func (s strategyRepository) AddStrategy(tx *gorm.DB, strategy model.AccessStrategy) error {
	err := tx.Model(&model.AccessStrategy{}).Create(&strategy).Error
	if err != nil {
		return err
	}
	return nil
}

func NewStrategyRepository() StrategyRepository {
	return strategyRepository{}
}

type StrategyRepository interface {
	AddStrategy(tx *gorm.DB, strategy model.AccessStrategy) error
	UpdateStrategy(c *gin.Context, strategy model.AccessStrategy) error
	DelStrategy(c *gin.Context, req dto.DelStrategyDto) error
	EnableStrategy(c *gin.Context, req dto.EnableStrategyDto) error
	GetStrategyDetail(c *gin.Context, id uint64) (model.AccessStrategy, error)
	GetStrategyByPriority(c *gin.Context, priority int, compare string) ([]model.AccessStrategy, error)
	UpdatesStrategyPriority(tx *gorm.DB, list []model.AccessStrategy) error
	GetPriorityByGroup(c *gin.Context, groupId string) (int, error)
	ClearStrategyMatch(c *gin.Context, req dto.DelStrategyDto) error
	StrategySimpleList(c *gin.Context, groupId string) ([]vo.StrategySimpleListResp, error)
	GetStrategyPriorityByGroup(c *gin.Context, moveReq vo.StrategyMoveReq) (int, error)
	StrategyMove(c *gin.Context, changeStrategy []model.AccessStrategy, strategy model.AccessStrategy) error
	StrategyList(c *gin.Context, req vo.StrategyListReq) ([]dto.StrategyListDto, int64, error)
	StrategyDetail(c *gin.Context, id uint64) (vo.StrategyDetailResp, error)
	GerStrategySortPriority(c *gin.Context, excludeId uint64, excludeGroupId string, groupId string) ([]dto.StrategyPriority, error)
	UpdatesStrategyGroupId(tx *gorm.DB, list []model.AccessStrategy) error
	ReGroupByRowNumber(c *gin.Context) ([]dto.ReGroupStrategy, error)
	CheckTimeId(c *gin.Context, timeId string) (bool, error)
	GetTimeDetail(c *gin.Context, timeId string) (*strategy_model.FactorTime, error)
	ListMatchCount(c *gin.Context, ids []int64) ([]dto.MatchCountDto, error)
	ResetAccessStrategyCache(ctx context.Context) error
}
