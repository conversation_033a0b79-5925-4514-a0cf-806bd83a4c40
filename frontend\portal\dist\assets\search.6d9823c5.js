/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import{_ as a,E as e,$ as s,r as l,h as o,a as u,b as c,j as n,w as t,I as r,d as v,F as i,A as d,y as p,k as b,J as m,W as f,e as k,l as y,S as h,K as g}from"./index.a794166c.js";import x from"./index.2cf5602b.js";const I={class:"search-component"},_={class:"transition-box",style:{display:"inline-block"}},j={key:0,class:"user-box"},w={key:1,class:"user-box"},C={key:2,class:"user-box"},B={key:3,class:"user-box"},V=a(Object.assign({name:"BtnBox"},{setup(a){const V=e(),q=s(),T=l(""),A=()=>{V.push({name:T.value}),T.value=""},E=l(!1),F=l(!0),J=()=>{E.value=!1,setTimeout((()=>{F.value=!0}),500)},K=l(null),L=async()=>{F.value=!1,E.value=!0,await h(),K.value.focus()},O=l(!1),S=()=>{O.value=!0,g.emit("reload"),setTimeout((()=>{O.value=!1}),500)},U=()=>{window.open("https://support.qq.com/product/371961")};return(a,e)=>{const s=o("base-option"),l=o("base-select");return u(),c("div",I,[n(f,{name:"el-fade-in-linear"},{default:t((()=>[r(v("div",_,[n(l,{ref_key:"searchInput",ref:K,modelValue:T.value,"onUpdate:modelValue":e[0]||(e[0]=a=>T.value=a),filterable:"",placeholder:"请选择",onBlur:J,onChange:A},{default:t((()=>[(u(!0),c(i,null,d(p(q).routerList,(a=>(u(),b(s,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])],512),[[m,E.value]])])),_:1}),F.value?(u(),c("div",j,[v("div",{class:k(["gvaIcon gvaIcon-refresh",[O.value?"reloading":""]]),onClick:S},null,2)])):y("",!0),F.value?(u(),c("div",w,[v("div",{class:"gvaIcon gvaIcon-search",onClick:L})])):y("",!0),F.value?(u(),c("div",C,[n(x,{class:"search-icon",style:{cursor:"pointer"}})])):y("",!0),F.value?(u(),c("div",B,[v("div",{class:"gvaIcon gvaIcon-customer-service",onClick:U})])):y("",!0)])}}}),[["__scopeId","data-v-97ccbcef"]]);export{V as default};
