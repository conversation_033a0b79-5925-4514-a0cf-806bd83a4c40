package utils

import (
	"errors"
	"math/big"
	"net"
	"net/netip"
	"net/url"
	"regexp"
	"strconv"
	"strings"
)

var ipRegex = regexp.MustCompile(`^(((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))\-(((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))$`)
var domainRegex = regexp.MustCompile(`^((\*\.)?[a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$`)

func GetOutboundIP() (string, error) {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "", err
	}
	defer conn.Close()
	return conn.LocalAddr().(*net.UDPAddr).IP.String(), nil
}

func GetIP() (net.IP, error) {
	ifaces, err := net.Interfaces()
	if err != nil {
		return nil, err
	}
	for _, iface := range ifaces {
		if iface.Flags&net.FlagUp == 0 {
			continue // interface down
		}
		if iface.Flags&net.FlagLoopback != 0 {
			continue // loopback interface
		}
		addrs, err := iface.Addrs()
		if err != nil {
			return nil, err
		}
		for _, addr := range addrs {
			ip := getIpFromAddr(addr)
			if ip == nil {
				continue
			}
			return ip, nil
		}
	}
	return nil, errors.New("connected to the network?")
}

func getIpFromAddr(addr net.Addr) net.IP {
	var ip net.IP
	switch v := addr.(type) {
	case *net.IPNet:
		ip = v.IP
	case *net.IPAddr:
		ip = v.IP
	}
	if ip == nil || ip.IsLoopback() {
		return nil
	}
	ip = ip.To4()
	if ip == nil {
		return nil // not an ipv4 address
	}

	return ip
}

type URLInfo struct {
	Scheme   string
	Hostname string
	Port     int32
	Path     string
}

func GetURLInfo(rawURL string) (URLInfo, error) {
	u, err := url.Parse(rawURL)
	if err != nil {
		return URLInfo{}, err
	}

	var port int32
	if u.Port() == "" {
		if u.Scheme == "http" {
			port = 80
		} else if u.Scheme == "https" {
			port = 443
		}
	} else {
		portI, err := strconv.ParseInt(u.Port(), 10, 64)
		if err != nil {
			return URLInfo{}, err
		}
		port = int32(portI)
	}
	info := URLInfo{
		u.Scheme,
		u.Hostname(),
		port,
		u.Path,
	}
	return info, nil
}

// IsValidIp checks if the given string is a valid IPv4 or IPv6 address.
func IsValidIp(ip string) bool {
	parsedIp := net.ParseIP(ip)
	// If net.ParseIP returns nil, it means the IP is not valid
	return parsedIp != nil
}

// IpRangeToCIDR ip范围转 cidr切片
func IpRangeToCIDR(cidr []string, start, end string) ([]string, error) {
	ips, err := netip.ParseAddr(start)
	if err != nil {
		return nil, err
	}
	ipe, err := netip.ParseAddr(end)
	if err != nil {
		return nil, err
	}

	isV4 := ips.Is4()
	if isV4 != ipe.Is4() {
		return nil, errors.New("start and end types are different")
	}
	if ips.Compare(ipe) > 0 {
		return nil, errors.New("start > end")
	}

	var (
		ipsInt = new(big.Int).SetBytes(ips.AsSlice())
		ipeInt = new(big.Int).SetBytes(ipe.AsSlice())
		tmpInt = new(big.Int)
		mask   = new(big.Int)
		one    = big.NewInt(1)
		buf    []byte

		bits, maxBit uint
	)
	if isV4 {
		maxBit = 32
		buf = make([]byte, 4)
	} else {
		maxBit = 128
		buf = make([]byte, 16)
	}

	for {
		bits = 1
		mask.SetUint64(1)
		for bits < maxBit {
			if (tmpInt.Or(ipsInt, mask).Cmp(ipeInt) > 0) ||
				(tmpInt.Lsh(tmpInt.Rsh(ipsInt, bits), bits).Cmp(ipsInt) != 0) {
				bits--
				mask.Rsh(mask, 1)
				break
			}
			bits++
			mask.Add(mask.Lsh(mask, 1), one)
		}

		addr, _ := netip.AddrFromSlice(ipsInt.FillBytes(buf))
		cidr = append(cidr, addr.String()+"/"+strconv.FormatUint(uint64(maxBit-bits), 10))

		if tmpInt.Or(ipsInt, mask); tmpInt.Cmp(ipeInt) >= 0 {
			break
		}
		ipsInt.Add(tmpInt, one)
	}
	return cidr, nil
}

// NeedIpRangeToCIDR 判断是否需要ip转换
func NeedIpRangeToCIDR(address string) bool {
	if address == "" {
		return false
	}
	if !strings.Contains(address, "-") {
		return false
	}
	splitIp := strings.Split(address, "-")
	if len(splitIp) > 2 || len(splitIp) <= 0 {
		return false
	}
	ip := net.ParseIP(splitIp[0])
	if ip == nil {
		return false
	}
	ip = net.ParseIP(splitIp[1])
	if ip == nil {
		return false
	}
	return true
}

// DeterminingAsecAddressLegitimacy TODO cl
func DeterminingAsecAddressLegitimacy(address string) bool {
	// 判断是否是IP
	ip := net.ParseIP(address)
	if ip != nil {
		return true
	}
	// 判断是否是IP段
	_, _, err := net.ParseCIDR(address)
	if err == nil {
		return true
	}
	// 判断是否是IP范围
	if NeedIpRangeToCIDR(address) {
		return true
	}
	// isdomin
	return true
}

func DeterminingAsecPortLegitimacy(port string) bool {
	return true
}

func CheckTunAppSite(addr, port, protocol string) bool {
	if !checkTunAddr(addr) {
		return false
	}
	if protocol == "icmp" {
		return true
	}
	portList := strings.Split(port, ",")
	if !checkTunPort(portList) {
		return false
	}
	return true
}

func checkTunAddr(addr string) bool {
	ip := net.ParseIP(addr)
	if ip != nil {
		return true
	}
	if isIPSegment(addr) {
		return true
	} else if isDomain(addr) {
		return true
	}
	return false
}

func checkTunPort(portList []string) bool {
	for _, v := range portList {
		ports := strings.Split(v, "-")
		if len(ports) > 2 {
			return false
		}
		for _, portStr := range ports {
			port, err := strconv.Atoi(portStr)
			if err != nil {
				return false
			}
			if port < 1 || port > 65535 {
				return false
			}

		}
	}
	return true
}

func isIPSegment(ipSegment string) bool {
	if ip := net.ParseIP(ipSegment); ip != nil {
		return true
	}
	if _, _, err := net.ParseCIDR(ipSegment); err == nil {
		return true
	}
	return ipRegex.MatchString(ipSegment)
}

func isDomain(domain string) bool {
	return domainRegex.MatchString(domain)
}
