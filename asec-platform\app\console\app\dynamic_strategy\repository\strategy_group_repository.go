package repository

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model/strategy_model"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type strategyGroupRepository struct {
}

func (s strategyGroupRepository) GetByPriority(c *gin.Context, priority int32) (strategy_model.DynamicStrategyGroup, error) {
	group := strategy_model.DynamicStrategyGroup{}
	db, err := global.GetDBClient(c)
	if err != nil {
		return group, err
	}
	err = db.Model(&strategy_model.DynamicStrategyGroup{}).Select("*").Where("priority=?", priority).Find(&group).Error
	return group, err
}

func (s strategyGroupRepository) UpdateGroupsPriority(tx *gorm.DB, strategyGroups []strategy_model.DynamicStrategyGroup) error {
	if len(strategyGroups) <= 0 {
		return nil
	}

	return tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"priority"}),
	}).Create(&strategyGroups).Error
}

func (s strategyGroupRepository) UpdateGroupPriority(tx *gorm.DB, priority int, groupId string) error {
	return tx.Model(&strategy_model.DynamicStrategyGroup{}).Where("id=?", groupId).Update("priority", priority).Error
}

func (s strategyGroupRepository) GroupList(c *gin.Context, query string) ([]vo.ChildrenGroup, aerrors.AError) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	tx := db.Table("tb_dynamic_strategy_group t1").
		Select("t1.id as group_id,t1.group_name,t1.priority,COUNT ( t2.ID ) as strategy_count").
		Joins("LEFT JOIN tb_access_strategy t2 ON t1.id = t2.strategy_group_id ")
	if query != "" {
		tx = tx.Where("group_name like ?", fmt.Sprintf("%%%v%%", query))
	}
	var res []vo.ChildrenGroup
	err = tx.Group("t1.id").Order("priority").Find(&res).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, aerrors.NewWithError(err, common.OperateError)
	}
	return res, nil
}

func (s strategyGroupRepository) UpdateGroupName(c *gin.Context, groupId string, groupName string) error {
	db, err := global.GetDBClient(c)
	if err != nil {
		return err
	}
	return db.Model(&strategy_model.DynamicStrategyGroup{}).Where("id=?", groupId).Update("group_name", groupName).Error
}

func (s strategyGroupRepository) GetGroupById(c *gin.Context, id string) (strategy_model.DynamicStrategyGroup, error) {
	db, err := global.GetDBClient(c)
	group := strategy_model.DynamicStrategyGroup{}
	if err != nil {

		return group, err
	}
	err = db.Model(&strategy_model.DynamicStrategyGroup{}).Select("*").Where("id=?", id).Find(&group).Error
	return group, err
}

func (s strategyGroupRepository) GetByPriorityCompare(c *gin.Context, priority int32, compare string) ([]strategy_model.DynamicStrategyGroup, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	tx := db.Model(&strategy_model.DynamicStrategyGroup{})
	if compare == "higher" {
		tx = tx.Where("priority>?", priority)
	} else if compare == "lower" {
		tx = tx.Where("priority<?", priority)
	}
	var groups []strategy_model.DynamicStrategyGroup
	err = tx.Order("priority").Find(&groups).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return groups, nil
}

func (s strategyGroupRepository) DeleteGroup(tx *gorm.DB, req vo.DeleteGroupReq) error {
	return tx.Where("id=?", req.GroupId).Delete(&strategy_model.DynamicStrategyGroup{}).Error
}

func (s strategyGroupRepository) CheckDuplicateName(c *gin.Context, groupName string) (bool, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return false, err
	}
	var exists bool
	err = db.Raw("select exists (select group_name from tb_dynamic_strategy_group where group_name=?)", groupName).
		Find(&exists).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}
	return exists, nil
}

func (s strategyGroupRepository) GetAllGroup(c *gin.Context, excludeId string) ([]strategy_model.DynamicStrategyGroup, error) {
	db, err := global.GetDBClient(c)
	if err != nil {
		return nil, err
	}
	var resList []strategy_model.DynamicStrategyGroup
	tx := db.Model(&strategy_model.DynamicStrategyGroup{}).
		Select("id,group_name,priority")
	if excludeId != "" {
		tx = tx.Where("id!=?", excludeId)
	}
	err = tx.Order("priority").Find(&resList).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return resList, err
}

func (s strategyGroupRepository) SaveStrategyGroup(tx *gorm.DB, strategyGroup strategy_model.DynamicStrategyGroup) error {
	return tx.Create(&strategyGroup).Error
}

func (s strategyGroupRepository) UpdateGroups(tx *gorm.DB, strategyGroups []strategy_model.DynamicStrategyGroup) error {
	if len(strategyGroups) <= 0 {
		return nil
	}

	return tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"group_name", "priority"}),
	}).Create(&strategyGroups).Error
}

func NewStrategyGroupRepository() StrategyGroupRepository {
	return strategyGroupRepository{}
}

type StrategyGroupRepository interface {
	CheckDuplicateName(c *gin.Context, groupName string) (bool, error)
	GetAllGroup(c *gin.Context, excludeId string) ([]strategy_model.DynamicStrategyGroup, error)
	SaveStrategyGroup(tx *gorm.DB, strategyGroup strategy_model.DynamicStrategyGroup) error
	UpdateGroups(tx *gorm.DB, strategyGroups []strategy_model.DynamicStrategyGroup) error
	DeleteGroup(tx *gorm.DB, req vo.DeleteGroupReq) error
	GetGroupById(c *gin.Context, id string) (strategy_model.DynamicStrategyGroup, error)
	GetByPriorityCompare(c *gin.Context, priority int32, compare string) ([]strategy_model.DynamicStrategyGroup, error)
	UpdateGroupName(c *gin.Context, groupId string, groupName string) error
	GroupList(c *gin.Context, query string) ([]vo.ChildrenGroup, aerrors.AError)
	UpdateGroupPriority(tx *gorm.DB, priority int, groupId string) error
	UpdateGroupsPriority(tx *gorm.DB, strategyGroups []strategy_model.DynamicStrategyGroup) error
	GetByPriority(c *gin.Context, priority int32) (strategy_model.DynamicStrategyGroup, error)
}
