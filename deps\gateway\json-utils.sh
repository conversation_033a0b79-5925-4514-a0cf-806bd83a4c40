#!/bin/bash
# 获取JSON字段值
json_get_value() {
    local file=$1
    local key=$2

    [ ! -f "$file" ] && return 1

    # 使用sed提取值
    sed -n "s/.*\"$key\"[[:space:]]*:[[:space:]]*\"\([^\"]*\)\".*/\1/p" "$file" | head -n1
}

# 设置JSON字段值
json_set_value() {
    local file=$1
    local key=$2
    local value=$3

    # 创建文件（如果不存在）
    [ ! -f "$file" ] && echo "{}" > "$file"

    # 备份
    cp "$file" "${file}.bak"

    # 如果字段已存在，更新它
    if grep -q "\"$key\"[[:space:]]*:" "$file"; then
        sed -i "s/\"$key\"[[:space:]]*:[[:space:]]*\"[^\"]*\"/\"$key\": \"$value\"/g" "$file"
    else
        # 添加新字段
        if grep -q '^[[:space:]]*{[[:space:]]*}[[:space:]]*$' "$file"; then
            # 空JSON
            echo "{\"$key\": \"$value\"}" > "$file"
        else
            # 在{后添加
            sed -i "0,/{/{s/{/{\"$key\": \"$value\",/}" "$file"
        fi
    fi

    # 简单验证：检查大括号匹配
    if [ "$(grep -o '{' "$file" | wc -l)" = "$(grep -o '}' "$file" | wc -l)" ]; then
        rm -f "${file}.bak"
        return 0
    else
        mv "${file}.bak" "$file"
        return 1
    fi
}

json_has_key() {
    local file=$1
    local key=$2

    [ ! -f "$file" ] && return 1
    grep -q "\"$key\"[[:space:]]*:" "$file"
}

json_delete_key() {
    local file=$1
    local key=$2

    [ ! -f "$file" ] && return 1

    cp "$file" "${file}.bak"

    # 删除包含该key的行
    sed -i "/\"$key\"[[:space:]]*:/d" "$file"

    # 清理多余的逗号
    sed -i 's/,\([[:space:]]*}\)/\1/g' "$file"
    sed -i 's/{\([[:space:]]*,\)/{\1/g' "$file"

    rm -f "${file}.bak"
}
