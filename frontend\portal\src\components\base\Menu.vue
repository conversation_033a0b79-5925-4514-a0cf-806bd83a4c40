<template>
  <nav
    :class="[
      'base-menu',
      { 'base-menu--collapse': collapse },
      { 'base-menu--vertical': mode === 'vertical' },
      { 'base-menu--horizontal': mode === 'horizontal' }
    ]"
    :style="menuStyle"
  >
    <slot />
  </nav>
</template>

<script>
export default {
  name: 'BaseMenu',
  provide() {
    return {
      menu: this,
      activeIndex: () => this.activeIndex,
      setActiveIndex: this.setActiveIndex,
      collapse: () => this.collapse,
      mode: () => this.mode
    }
  },
  props: {
    mode: {
      type: String,
      default: 'vertical',
      validator: (value) => ['vertical', 'horizontal'].includes(value)
    },
    collapse: {
      type: Boolean,
      default: false
    },
    backgroundColor: {
      type: String,
      default: '#273444'
    },
    textColor: {
      type: String,
      default: '#ffffff'
    },
    activeTextColor: {
      type: String,
      default: '#536ce6'
    },
    defaultActive: {
      type: String,
      default: ''
    },
    uniqueOpened: {
      type: Boolean,
      default: false
    },
    collapseTransition: {
      type: Boolean,
      default: true
    }
  },
  emits: ['select'],
  data() {
    return {
      activeIndex: this.defaultActive
    }
  },

  computed: {
    menuStyle() {
      return {
        backgroundColor: this.backgroundColor,
        color: this.textColor,
        '--menu-active-color': this.activeTextColor,
        '--menu-text-color': this.textColor,
        '--menu-bg-color': this.backgroundColor
      }
    }
  },
  methods: {
    setActiveIndex(index) {
      this.activeIndex = index
      this.$emit('select', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.base-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  background-color: var(--menu-bg-color);
  color: var(--menu-text-color);
  transition: all 0.3s ease;

  &--vertical {
    width: 180px;

    &.base-menu--collapse {
      width: 54px;
    }
  }

  &--horizontal {
    display: flex;
    align-items: center;
    height: 60px;
  }
}
</style>
