<template>
  <div class="access-main">
    <ul class="content-wrapper">
      <li class="access-app">
        <AppPage
          class="access-app-page"
        />
      </li>
    </ul>
  </div>
</template>

<script>
import AppPage from '@/view/app/index.vue'

export default {
  name: 'BowserAccess',
  components: {
    AppPage
  },
  data() {
    return {}
  },
  async mounted() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.access-main {
  width: 100%;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  .content-wrapper {
    margin: 0px;
    padding: 0; /* 清除ul默认的padding */
    list-style: none; /* 清除ul默认的list-style */
    width: 100%;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .access-app {
      flex: 1;
      min-height: 0;
      font-size: 13px;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 4px;
      overflow: hidden;
      .access-app-page {
        width: 100%;
        height: 100%;
        flex: 1;
        min-height: 0;
      }
    }
  }
}
</style>
