-- 创建账户认证策略表
CREATE TABLE IF NOT EXISTS tb_auth_account_policy (
    id VARCHAR(64) PRIMARY KEY DEFAULT gen_random_uuid()::VARCHAR,
    corp_id VARCHAR(36) NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    
    -- 密码策略相关字段
    password_min_length INT DEFAULT 8,
    password_max_length INT DEFAULT 20,
    password_validity_days INT DEFAULT 90,
    password_history_count INT DEFAULT 5,
    password_complexity_uppercase BOOLEAN DEFAULT false,
    password_complexity_lowercase BOOLEAN DEFAULT false,
    password_complexity_numbers BOOLEAN DEFAULT false,
    password_complexity_special_chars B<PERSON><PERSON>EAN DEFAULT false,
    password_complexity_no_username BO<PERSON>EAN DEFAULT true,
    
    -- 防暴力破解策略 - 密码错误次数限制
    password_failure_enabled BOOLEAN DEFAULT false,
    password_max_failure_count INT DEFAULT 5,
    password_lockout_duration_sec INT DEFAULT 300,
    password_reset_failure_window_sec INT DEFAULT 900,
    
    -- 防暴力破解策略 - IP错误次数限制
    ip_failure_enabled BOOLEAN DEFAULT false,
    ip_max_failure_count INT DEFAULT 32,
    ip_lockout_duration_sec INT DEFAULT 300,
    ip_reset_failure_window_sec INT DEFAULT 1800,
    
    -- 通用字段
    enable BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    priority INT DEFAULT 0,
    
    -- 时间戳
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- 约束
    CONSTRAINT uk_corp_id_name UNIQUE(corp_id, name)
);

-- 添加字段注释
COMMENT ON COLUMN tb_auth_account_policy.id IS '策略ID';
COMMENT ON COLUMN tb_auth_account_policy.corp_id IS '租户ID';
COMMENT ON COLUMN tb_auth_account_policy.name IS '策略名称';
COMMENT ON COLUMN tb_auth_account_policy.description IS '策略描述';

COMMENT ON COLUMN tb_auth_account_policy.password_min_length IS '密码最小位数';
COMMENT ON COLUMN tb_auth_account_policy.password_max_length IS '密码最大位数';
COMMENT ON COLUMN tb_auth_account_policy.password_validity_days IS '密码有效期（天）';
COMMENT ON COLUMN tb_auth_account_policy.password_history_count IS '密码历史个数';
COMMENT ON COLUMN tb_auth_account_policy.password_complexity_uppercase IS '密码复杂度-大写字母';
COMMENT ON COLUMN tb_auth_account_policy.password_complexity_lowercase IS '密码复杂度-小写字母';
COMMENT ON COLUMN tb_auth_account_policy.password_complexity_numbers IS '密码复杂度-数字';
COMMENT ON COLUMN tb_auth_account_policy.password_complexity_special_chars IS '密码复杂度-特殊字符';
COMMENT ON COLUMN tb_auth_account_policy.password_complexity_no_username IS '密码复杂度-不包含账户名';

COMMENT ON COLUMN tb_auth_account_policy.password_failure_enabled IS '是否启用密码错误次数限制';
COMMENT ON COLUMN tb_auth_account_policy.password_max_failure_count IS '连续密码错误最大次数';
COMMENT ON COLUMN tb_auth_account_policy.password_lockout_duration_sec IS '锁定时长（秒）';
COMMENT ON COLUMN tb_auth_account_policy.password_reset_failure_window_sec IS '密码错误次数重置窗口（秒）';

COMMENT ON COLUMN tb_auth_account_policy.ip_failure_enabled IS '是否启用IP错误次数限制';
COMMENT ON COLUMN tb_auth_account_policy.ip_max_failure_count IS '同IP连续错误最大次数';
COMMENT ON COLUMN tb_auth_account_policy.ip_lockout_duration_sec IS 'IP锁定时长（秒）';
COMMENT ON COLUMN tb_auth_account_policy.ip_reset_failure_window_sec IS 'IP错误次数重置窗口（秒）';

COMMENT ON COLUMN tb_auth_account_policy.enable IS '是否启用该策略';
COMMENT ON COLUMN tb_auth_account_policy.is_default IS '是否为默认策略';
COMMENT ON COLUMN tb_auth_account_policy.priority IS '策略优先级，数值越大优先级越高';

COMMENT ON COLUMN tb_auth_account_policy.created_at IS '创建时间';
COMMENT ON COLUMN tb_auth_account_policy.updated_at IS '更新时间';

-- 创建索引（使用 IF NOT EXISTS 避免重复创建）
CREATE INDEX IF NOT EXISTS idx_auth_account_policy_corp_id ON tb_auth_account_policy(corp_id);
CREATE INDEX IF NOT EXISTS idx_auth_account_policy_enable ON tb_auth_account_policy(enable);
CREATE INDEX IF NOT EXISTS idx_auth_account_policy_default ON tb_auth_account_policy(is_default);
CREATE INDEX IF NOT EXISTS idx_auth_account_policy_priority ON tb_auth_account_policy(priority DESC);

-- 添加表注释
COMMENT ON TABLE tb_auth_account_policy IS '账户认证策略表';

-- 为每个租户创建默认策略（可选，在应用层处理）
-- INSERT INTO tb_auth_account_policy (corp_id, name, description, is_default, priority) 
-- VALUES ('default', '默认账户策略', '系统默认的账户认证策略', true, 1);


-- 设置默认账户策略（只在表为空时插入）
INSERT INTO tb_auth_account_policy (
    id,
    corp_id,
    name,
    description,
    
    -- 密码策略相关字段
    password_min_length,
    password_max_length,
    password_validity_days,
    password_history_count,
    password_complexity_uppercase,
    password_complexity_lowercase,
    password_complexity_numbers,
    password_complexity_special_chars,
    password_complexity_no_username,
    
    -- 防暴力破解策略 - 密码错误次数限制
    password_failure_enabled,
    password_max_failure_count,
    password_lockout_duration_sec,
    password_reset_failure_window_sec,
    
    -- 防暴力破解策略 - IP错误次数限制
    ip_failure_enabled,
    ip_max_failure_count,
    ip_lockout_duration_sec,
    ip_reset_failure_window_sec,
    
    -- 通用字段
    enable,
    is_default,
    priority,
    created_at,
    updated_at
)
SELECT
    gen_random_uuid()::VARCHAR,
    '2efd2601-d800-48b3-9035-9ed23694ba0f',
    '默认账户策略',
    '默认账户策略',
    
    -- 密码策略设置
    8,     -- 密码最小长度
    63,    -- 密码最大长度
    90,    -- 密码有效期90天
    5,     -- 密码历史记录5个
    true,  -- 要求大写字母
    true,  -- 要求小写字母
    true,  -- 要求数字
    false, -- 不要求特殊字符
    false, -- 不包含用户名
    
    -- 密码锁定策略设置（启用）
    true,  -- 启用密码错误次数限制
    3,     -- 连续错误3次后锁定（便于测试）
    300,   -- 锁定5分钟（300秒）
    900,   -- 错误次数重置窗口15分钟（900秒）
    
    -- IP锁定策略设置（不启用）
    false, -- 不启用IP错误次数限制
    10,    -- IP错误次数阈值
    1800,  -- IP锁定时长30分钟
    3600,  -- IP错误次数重置窗口1小时
    
    -- 通用设置
    true,  -- 启用策略
    true,  -- 设为默认策略
    100,   -- 高优先级
    now(), -- 创建时间
    now()  -- 更新时间
WHERE NOT EXISTS (
    SELECT 1 FROM tb_auth_account_policy 
    WHERE corp_id = '2efd2601-d800-48b3-9035-9ed23694ba0f' 
    AND is_default = true
);