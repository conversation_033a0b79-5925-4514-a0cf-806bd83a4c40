package dto

import "github.com/lib/pq"

type SourceListDto struct {
	Id              string         `gorm:"column:id"`
	SourceName      string         `gorm:"column:source_name"`
	SourceType      string         `gorm:"column:source_type"`
	UrlAddr         pq.StringArray `gorm:"column:url_addr"`
	UrlPort         pq.StringArray `gorm:"column:url_port"`
	UrlRoute        pq.StringArray `gorm:"column:url_route"`
	SoftwareName    pq.StringArray `gorm:"column:software_name"`
	ProcessName     pq.StringArray `gorm:"column:process_name"`
	IncludeFilePath pq.StringArray `gorm:"column:include_file_path"`
	ExcludeFilePath pq.StringArray `gorm:"column:exclude_file_path"`
	GitUrl          string         `gorm:"column:git_url"`
	Status          int            `gorm:"column:status"`
}

type SourceList struct {
	CurrentPage    int             `json:"current_page"`
	PageSize       int             `json:"page_size"`
	TotalNum       int             `json:"total_num"`
	SourceListData []SourceListDto `json:"source_list_data"`
}
