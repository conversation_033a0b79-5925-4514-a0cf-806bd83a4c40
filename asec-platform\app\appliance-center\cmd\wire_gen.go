// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"asdsec.com/asec/platform/app/appliance-center/internal/conf"
	"asdsec.com/asec/platform/app/appliance-center/internal/data"
	"asdsec.com/asec/platform/app/appliance-center/internal/server"
	"asdsec.com/asec/platform/pkg/biz"
	"asdsec.com/asec/platform/pkg/biz/cfg_biz"
	data2 "asdsec.com/asec/platform/pkg/data"
	"asdsec.com/asec/platform/pkg/data/cfg_data"
	"asdsec.com/asec/platform/pkg/service"
	"asdsec.com/asec/platform/pkg/service/cfg_service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	db := data.NewGormClient(confData, logger)
	dataData, cleanup, err := data.NewData(confData, logger, db)
	if err != nil {
		return nil, nil, err
	}
	applianceRepo := data2.NewApplianceRepo(dataData, logger)
	applianceUsecase := biz.NewApplianceUsecase(applianceRepo, logger)
	applianceMgtService := service.NewApplianceMgtService(applianceUsecase)
	appRepo := data2.NewAppRepo(dataData, logger)
	appUsecase := biz.NewAppUsecase(appRepo, logger)
	appService := service.NewAppService(appUsecase)
	agentAppRepo := data2.NewAgentAppRepo(dataData, logger)
	agentAppUsecase := biz.NewAgentAppUsecase(agentAppRepo, logger)
	agentAppService := service.NewAgentAppService(agentAppUsecase)
	senElemRepo := data2.NewSenElemRepo(dataData, logger)
	senElemUsecase := biz.NewSenElmUsecase(senElemRepo, logger)
	senElmService := service.NewSenElemService(senElemUsecase)
	controlRepo := data2.NewControlRepo(dataData, logger)
	controlUsecase := biz.NewControlUsecase(controlRepo, logger)
	controlService := service.NewControlService(controlUsecase)
	es256KeyRepo := data2.NewES256KeyRepo(dataData, logger)
	es256KeyUsecase := biz.NewES256KeyUsecase(es256KeyRepo, logger)
	es256KeyService := service.NewES256KeyService(es256KeyUsecase)
	applianceCfgRepo := cfg_data.NewAppRepo(dataData, logger)
	applianceCfgUsecase := cfg_biz.NewApplianceCfgUsecase(applianceCfgRepo, logger)
	applianceCfgService := cfg_service.NewApplianceCfgService(applianceCfgUsecase, logger)
	specialConfigRepo := data2.NewSpecialConfigRepo(dataData, logger)
	specialConfigUsecase := biz.NewSpecialConfigUsecase(specialConfigRepo, logger)
	specialConfigService := service.NewSpecialConfigService(specialConfigUsecase)
	scanTaskRepo := data2.NewScanTaskRepo(dataData, logger)
	scanTaskUsecase := biz.NewScanTaskUsecase(scanTaskRepo, logger)
	scanTaskService := service.NewScanTaskService(scanTaskUsecase)
	grpcServer := server.NewGRPCServer(confServer, applianceMgtService, appService, agentAppService, senElmService, controlService, es256KeyService, applianceCfgService, specialConfigService, scanTaskService, logger)
	httpServer := server.NewHTTPServer(confServer, controlService, applianceMgtService, logger)
	app := newApp(logger, grpcServer, httpServer)
	return app, func() {
		cleanup()
	}, nil
}
