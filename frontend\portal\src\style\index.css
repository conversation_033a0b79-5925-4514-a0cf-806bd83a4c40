.login-page {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/login_background.png');
  background-size: cover;
  background-position: center;
  min-height: 100vh;
}

.header {
  height: 60px;
  display: flex;
  align-items: center;
  /* justify-content: center; */
  background-color: rgba(255, 255, 255, 0.8);
}

.logo {
  height: 20px;
  margin-left: 50px;
  margin-right: 10px;
}

.separator {
  width: 1px;
  height: 14px;
  background-color: #ccc;
  margin: 0 10px;
}

.company-name {
  font-size: 24px;
}

.header-text {
   font-size: 12px;
   opacity: 0.6;
}

.content {
  display: flex;
  height: calc(100% - 60px);
}

.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* align-items: center; */
  justify-content: center;
  padding: 20px;
  margin-left: 310px;
}

.slogan {
  font-size: 36px;
  margin-bottom: 20px;
}

.image {
  width: 718px;
  /* max-width: 800px; */
  height: 470px;
  margin-bottom: 20px;
}

.icons {
  display: flex;
  justify-content: space-between;
  width: 150px;
}

.icons img {
  width: 30px;
  height: 30px;
}

.right-panel {
  width: auto;
  height: auto;
  min-height: 560px;
  max-height: 560px;
  box-sizing: border-box;
  min-width: 450px;
  max-width: 450px;
  margin-right: 310px;
  margin-bottom: auto;
  padding: 40px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 2px 16px 0px rgba(16,36,66,0.10);
  backdrop-filter: blur(2px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 76%;
  transform: translate(-50%, -50%);
}

.title {
  height: 57px;
  font-size: 18px;
  text-align: center;
}

.login_panel {
  display: flex;
  flex-direction: column;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.label {
  font-size: 16px;
  margin-bottom: 5px;
}

.input-field {
  height: 40px;
  padding: 5px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.login-submit-button {
  width: 100%;
  height: 40px;
  margin-top: 12px;
  font-size: 16px;
  color: #fff;
  background-color: #536ce6;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-bottom: 60px;
}

.submit-button:hover {
  background-color: #536ce6;
}

.submit-button:active {
  background-color: #536ce6;;
}

