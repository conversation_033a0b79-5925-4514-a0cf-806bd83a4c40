package heartbeat

import (
	"context"
	"io"
	"runtime"
	"sync"
	"time"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/recover_panic"
	"google.golang.org/grpc"
)

func StartHeartbeat(ctx context.Context, pids []int32, wg *sync.WaitGroup) {
	defer wg.Done()
	defer recover_panic.RecoverStartHeartbeat(global.Logger, StartHeartbeat, ctx, pids, wg)
	subWg := &sync.WaitGroup{}
	retries := 0
	defer subWg.Wait()

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	for {
		// 获取当前SPA状态用于日志 - 只在Windows客户端模式下生效
		currentSpaCode := ""
		spaStatus := "disabled"
		if global.ApplianceType == v1.ApplianceType_AGENT && runtime.GOOS == "windows" {
			currentSpaCode = global.GetProcessedSpaCode()
			if currentSpaCode != "" {
				spaStatus = "enabled"
			}
		}

		conn, err := connection.GetPlatformConnection(ctx)
		if err != nil {
			global.Logger.Sugar().Warnf("wait to get next connection for 5 seconds, current retry times: %v, SPA status: %s, err: %v",
				retries, spaStatus, err)
			select {
			case <-ctx.Done():
				return
			case <-time.After(time.Second * 5):
				retries++
				continue
			}
		}
		retries = 0
		global.Logger.Sugar().Infof("heartbeat connection established successfully, SPA status: %s", spaStatus)

		subCtx, cancel := context.WithCancel(ctx)
		subWg.Add(1)
		go handleSend(subCtx, conn, pids, subWg)
		subWg.Wait()

		cancel()
		global.Logger.Sugar().Infof("heartbeat connection lost, will reconnect in 5 seconds (SPA status may have changed)")

		select {
		case <-ctx.Done():
			return
		case <-time.After(time.Second * 5):
		}
		connection.CloseConnection(conn)
	}

}

func handleSend(ctx context.Context, conn *grpc.ClientConn, pids []int32, wg *sync.WaitGroup) {
	defer wg.Done()
	defer recover_panic.RecoverHeartbeatSend(global.Logger, handleSend, ctx, conn, pids, wg)
	//确保心跳间隔不小于5S
	interval := global.Conf.Heartbeat.Interval
	if interval < 5 {
		interval = 5
	}
	ticker := time.NewTicker(time.Duration(interval) * time.Second)
	defer ticker.Stop()

	// 记录当前连接建立时的SPA状态，用于检测SPA状态变化 - 只在Windows客户端模式下生效
	currentSpaCode := ""
	if global.ApplianceType == v1.ApplianceType_AGENT && runtime.GOOS == "windows" {
		currentSpaCode = global.GetProcessedSpaCode()
	}

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			{
				// 检查SPA状态是否发生变化 - 只在Windows客户端模式下生效
				if global.ApplianceType == v1.ApplianceType_AGENT && runtime.GOOS == "windows" {
					newSpaCode := global.GetProcessedSpaCode()
					if newSpaCode != currentSpaCode {
						global.Logger.Sugar().Infof("SPA code changed from '%s' to '%s', need to reconnect",
							maskSpaCode(currentSpaCode), maskSpaCode(newSpaCode))
						return // 触发外层重新建立连接
					}
				}

				if conn == nil {
					global.Logger.Error("grpc conn is nil when handling heartbeat send")
					return
				}
				client := v1.NewApplianceMgtClient(conn)
				if client == nil {
					global.Logger.Error("grpc client is nil when handling heartbeat send")
					return
				}
				stream, err := client.Heartbeat(ctx)
				if err != nil {
					if stream != nil {
						stream.CloseAndRecv()
					}
					global.Logger.Sugar().Errorf("client.Heartbeat failed. err=%v", err)
					return
				}

				err = stream.Send(GetHeartbeatPacket(time.Now(), pids))
				if err != nil {
					if stream != nil {
						stream.CloseAndRecv()
					}
					if err != io.EOF {
						global.Logger.Sugar().Errorf("StartHeartbeat Send heartbeat packet error : %s", err)
					}
					global.Logger.Sugar().Debugf("stream.Send get io.EOF, may be server close stream")
					return
				}
				_, err = stream.CloseAndRecv()
				if err != nil {
					global.Logger.Sugar().Errorf("stream.CloseSend failed. err=%v", err)
					return
				}
			}
		}

	}
}

// maskSpaCode 用于日志输出时隐藏敏感信息
func maskSpaCode(code string) string {
	if code == "" {
		return "<empty>"
	}
	if len(code) <= 4 {
		return "***"
	}
	return code[:2] + "***" + code[len(code)-2:]
}
