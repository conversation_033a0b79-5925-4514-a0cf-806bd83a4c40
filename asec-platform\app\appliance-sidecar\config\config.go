package config

import (
	"asdsec.com/asec/platform/pkg/config"
)

type SidecarConfig struct {
	//传输相关配置
	Transport Transport `yaml:"transport"`
	Location  Location  `yaml:"location"`
	//平台地址发现
	Endpoints Endpoints `yaml:"endpoints"`
	//心跳配置
	Heartbeat Heartbeat `yaml:"heartbeat"`
	// 授权检查
	LicenseCheck LicenseCheck `yaml:"license-check"`
	// 网关接入日志
	AccessLog AccessLog  `yaml:"accessLog"`
	Logbeat   Logbeat    `yaml:"logbeat"`
	Zap       config.Zap `yaml:"zap"`
	// ip to region
	Region      Region      `yaml:"region"`
	WebGWConfig WebGWConfig `yaml:"webGwConfig"`
	ConfigPollInterval int         `json:"config_poll_interval"` //seconds
	//虚拟IP配置
	VirtualIPConfig VirtualIPConfig `yaml:"virtualip" json:"virtualip"`
}

type Region struct {
	Ip2regionType string `yaml:"ip2region-type"`
	AiwenKey      string `yaml:"aiwen-key"`
}

// Transport 传输相关控制，比如限速配置等
type Transport struct {
	Insecure bool `yaml:"insecure"`
}

type Location struct {
	Region string `yaml:"region"`
}

type Endpoints struct {
	// 服务发现列表，key为Region
	ServiceDiscoveryHost string `yaml:"service-discovery" mapstructure:"service-discovery" `
	// 内网通信地址
	PrivateHost string `yaml:"private-host" mapstructure:"private-host" `
	// 公网通信地址
	PublicHost    string `yaml:"public-host" mapstructure:"public-host" `
	LogCenterHost string `yaml:"log-center-host" mapstructure:"log-center-host"`
}

type Heartbeat struct {
	// 心跳开关
	Enable bool `yaml:"enable"`
	// 心跳上报间隔
	Interval int `yaml:"interval"`
	// 进程信息采集开关
	ProcStatEnable bool `yaml:"proc-stat-enable"`
	// 进程信息采集间隔
	ProcStatInterval int `yaml:"proc-stat-interval"`
}

type LicenseCheck struct {
	//授权检查开关
	Enable   bool `yaml:"enable"`
	Interval int  `yaml:"interval"`
}

type AccessLog struct {
	//访问日志
	Enable   bool `yaml:"enable"`
	Interval int  `yaml:"interval"`
}
type Logbeat struct {
	Enable   bool   `yaml:"enable"`
	Interval int    `yaml:"interval"`
	Level    string `yaml:"level"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
}
type WebGWConfig struct {
	Enable  bool `yaml:"enable"`
	Timeout int  `yaml:"timeout"`
}

type VirtualIPConfig struct {
	Enable         bool    `yaml:"enable" json:"enable"`
	SyncInterval   float64 `yaml:"sync-interval" json:"sync_interval"`
	ConfigFilePath string  `yaml:"config-file-path" json:"config_file_path"`
}
