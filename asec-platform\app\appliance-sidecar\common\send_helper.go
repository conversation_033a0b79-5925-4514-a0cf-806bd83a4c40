package common

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"google.golang.org/grpc"
	"io"
	"math/rand"
	"sync"
	"time"
)

type SendParam struct {
	Ctx              context.Context
	Wg               *sync.WaitGroup
	DoSendFunc       DoSend       // 普通grpc调用方法
	DoStreamSendFunc DoSendStream // 流式grpc调用方法
	RunType          RunType
	GetConnFun       GetConn //获取gRPC连接方法
	WaitSecond       float64
	RandomTime       bool
	RandomOffset     float64
}

type SideCar func(sendParam SendParam)

type GetConn func(ctx context.Context) (*grpc.ClientConn, error)
type DoSend func(conn *grpc.ClientConn, ctx context.Context) error
type DoSendStream func(conn *grpc.ClientConn, ctx context.Context, wg *sync.WaitGroup) error

type RunType int

const (
	SimpleSend RunType = iota // 普通grpc调用方法
	StreamSend                // 流式grpc调用方法
)

// Send 通用grpc 发送封装
func Send(sendParam SendParam) {
	defer RecoverSideCarPanic(Send, sendParam)
	ctx, cancel := context.WithCancel(sendParam.Ctx)
	defer sendParam.Wg.Done()
	defer cancel()

	connFunc := sendParam.GetConnFun
	if connFunc == nil {
		connFunc = connection.GetPlatformConnection
	}
	waitEx := 1
	for {
		conn, err := connFunc(ctx)
		if err != nil || conn == nil {
			goto WAIT
		}
		err = SendReal(ctx, conn, sendParam)
		if err != nil {
			waitEx++
			goto WAIT
		} else {
			waitEx = 1
		}
	WAIT:
		connection.CloseConnection(conn)
		select {
		case <-ctx.Done():
			return
		case <-time.After(exponentialBackoffTime(waitEx, 5, 600)):
			continue
		}
	}
}

func SendReal(ctx context.Context, conn *grpc.ClientConn, sendParam SendParam) error {
	var subWg sync.WaitGroup
	if sendParam.RunType == StreamSend {
		subWg := &sync.WaitGroup{}
		defer subWg.Wait()
	}
	waitTime := getWaitTime(sendParam)

	for {
		// 初次进入时调用一次,保证某些缓存场景重启后无需等待ticker
		var err error
		if sendParam.RunType == StreamSend {
			err = sendParam.DoStreamSendFunc(conn, ctx, &subWg)
		} else {
			err = sendParam.DoSendFunc(conn, ctx)
		}
		if err != nil {
			//EOF错误不打日志
			if err != io.EOF {
				global.Logger.Sugar().Warnf("do Send func error : %s", err)
			}
			return err
		}

		select {
		case <-ctx.Done():
			return nil
		case <-time.After(time.Second * time.Duration(waitTime)):
			continue
		}

	}
}

func getWaitTime(sendParam SendParam) float64 {
	if !sendParam.RandomTime {
		return sendParam.WaitSecond
	}
	return rand.NormFloat64()*sendParam.RandomOffset + sendParam.WaitSecond
}

func RecoverSideCarPanic(car SideCar, sendParam SendParam) {
	if err := recover(); err != nil {
		utils.WritePanicLog(err, global.Logger)
		defer RecoverSideCarPanic(car, sendParam)
		time.Sleep(time.Second * 5)
		// re do function
		car(sendParam)
	}
}

// exponentialBackoffTime 获取Exponential backoff 的时间间隔(s)
//
// errNums 累积错误次数
// exponential 退避指数
// maxWait 最大等待时长(s),防止错误次数太多
func exponentialBackoffTime(errNums int, exponential int, maxWait int) time.Duration {
	waitSecond := errNums * exponential
	if waitSecond > maxWait {
		return time.Second * time.Duration(maxWait)
	}
	return time.Second * time.Duration(waitSecond)
}
