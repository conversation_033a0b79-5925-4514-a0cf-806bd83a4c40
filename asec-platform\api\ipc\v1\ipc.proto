syntax = "proto3";

package api.ipc;

option go_package = "asdsec.com/asec/platform/api/ipc/v1;v1";

message UpgradeMessage {

    enum UpMsgType {
        UT_CHECK_UPGRADE             = 0;
        UT_START_DOWNLOAD_PACKAGE    = 1;
        UT_DOWNLOAD_PROGRESS         = 2;
        UT_DOWNLOAD_FINISHED         = 3;
        UT_START_TIMING_CHECK        = 4;
    }

    string      clientVersion       = 1;
    string      sidecarVersion      = 2;
    string      tuncliVersion       = 3;
    string      upgradeVersion      = 4;
    string      upgradeVersionDes   = 5;
    string      upgradePackagePath  = 6;

    int32       downloadProgress    = 7;
    bool        forceUpgrade        = 8;
    
    UpMsgType   upMsgType           = 9;
}

message HostMessage {

    enum HostType {
        HT_UNKNOWN = 0;

        HT_CLIENTANDTUNNEL = 1001;

        HT_CLIENTANDSIDECAR = 2001;

        HT_CLIENTANDUPGRADE = 3001;

        HT_CLIENTANDUI      = 4001;

        HT_CLIENT_DDR       = 5001;

        HT_CLIENT_SETUP     = 6001;
    }

    enum Actions {
        ACT_NO_ACT          = 0;
        ACT_DATA_READY      = 1;
        ACT_DATA_FINISHED   = 2;

        ACT_SHUTDOWN_TUN    = 3;
        ACT_SHUTDOWN_SIDECAR= 4;

        ACT_START_SIDECAR   = 5;
        ACT_START_TUN       = 6;
        ACT_SHUTDOWN_UI     = 7;

        ACT_PLATADDR_CHANGE = 8;

        // ddr act          20-40
        ACT_SHUTDOWN_DDR    = 20;
        ACT_START_DDR       = 21;

        //Service服务
        ACT_SET_SERVICE_CAN_STOP = 30;
        ACT_SET_SERVICE_CAN_NOT_STOP = 31;
    }

    enum State {
        UNKNOWN_STATE       = 0;

        //UI消息
        UI_ONLINE           = 1;
        UI_OFFLINE          = 2;

        //TUN消息
        TUN_ONLINE          = 100;
        TUN_CONNECTING      = 101;
        TUN_OFFLINE         = 102;


        //SIDECAR消息
        SIDECAR_ONLINE      = 200;
        SIDECAR_OFFLINE     = 201;

        //DDR消息
        DDR_ONLINE          = 300;
        DDR_OFFLINE         = 301;

        //SETUP消息
        SETUP_ONLINE        = 400;
        SETUP_OFFLINE       = 401;
    }
    //发送消息的原因
    enum Reason {
        DEFAULT_REASON       = 0;
        CREATE_TUN_FAILED   = 1;
        CHK_TUN_MGRPORT_FAILED  = 2;
        GET_TUN_ROUTE_FAILED  = 3;
        CHK_TUN_DATAPORT_FAILED = 4;
        UNKNOW_REASON       = 65535;

    }
    HostType        hostType     = 1;
    State           processState = 2;
    Actions         action       = 3;
    UpgradeMessage  upMsg        = 4;
    Reason			    reason		 = 5;
}