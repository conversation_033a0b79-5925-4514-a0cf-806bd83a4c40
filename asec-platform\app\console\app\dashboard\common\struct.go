package common

import (
	"time"
)

type TimeAggData struct {
	Time  string `json:"time"`
	Count int    `json:"count"`
}

type SensitiveData struct {
	SensitiveLevel    int    `json:"sensitive_level"`
	SensitiveRuleId   string `json:"sensitive_rule_id"`
	SensitiveRuleName string `json:"sensitive_rule_name"`
}

type SendTopItem struct {
	Count   int           `json:"count"`
	Size    int           `json:"size"`
	Code    string        `json:"code"`
	BuiltIn int           `json:"built_in"`
	Data    []TimeAggData `json:"data"`
}

type SensitiveSendTopItem struct {
	Count             int           `json:"count"`
	Size              int64         `json:"size"`
	SensitiveLevel    int           `json:"sensitive_level"`
	SensitiveRuleId   string        `json:"sensitive_rule_id"`
	SensitiveRuleName string        `json:"sensitive_rule_name"`
	UserName          []string      `json:"user_name"`
	Data              []TimeAggData `json:"data"`
}

type DataAccessLogTopItem struct {
	Count         int             `json:"count"`
	Size          int64           `json:"size"`
	SourceId      string          `json:"source_id"`
	SourceName    string          `json:"source_name"`
	SourceType    string          `json:"source_type"`
	UserIds       []string        `json:"user_ids,omitempty"`
	Data          []TimeAggData   `json:"data,omitempty"`
	SensitiveData []SensitiveData `json:"sensitive_data,omitempty"`
}

type QueryTimeParam struct {
	StartTime string `form:"start_time" json:"start_time"`
	EndTime   string `form:"end_time" json:"end_time"`
}

type SensitiveDownloadRsp struct {
	SourceId          string    `gorm:"source_id" json:"source_id"`
	SourceType        string    `gorm:"source_type" json:"source_type"`
	SourceName        string    `gorm:"source_name" json:"source_name"`
	SensitiveRuleId   string    `gorm:"column:sensitive_rule_id;type:varchar;comment:命中的数据敏感规则ID" json:"sensitive_rule_id"`
	SensitiveRuleName string    `gorm:"column:sensitive_rule_name;type:varchar;comment:命中的数据敏感规则名称" json:"sensitive_rule_name"`
	SensitiveLevel    int       `gorm:"column:sensitive_level;type:int;comment:数据分类等级" json:"sensitive_level"`
	SrcPath           string    `gorm:"column:src_path;type:varchar;comment:src源路径" json:"src_path"`
	SrcHost           string    `gorm:"column:src_host;type:varchar;comment:src源路径" json:"src_host"`
	UserId            string    `gorm:"column:user_id;type:varchar;comment:用户ID" json:"user_id,omitempty"`
	FileSize          int64     `gorm:"column:file_size;type:int64;comment:文件大小" json:"file_size"`
	OccurTime         time.Time `gorm:"column:occur_time;type:timestamptz;comment:行为发生时间" json:"occur_time"`
}
type SensitiveSendRsp struct {
	SensitiveLevel    int       `gorm:"column:sensitive_level;type:int;comment:敏感等级"`
	SensitiveRuleId   string    `gorm:"column:sensitive_rule_id;type:varchar;comment:敏感类型ID"`
	SensitiveRuleName string    `gorm:"column:sensitive_rule_name;type:varchar;comment:敏感类型名称"`
	UserId            string    `gorm:"column:user_id;type:varchar;comment:用户Id"`
	FileSize          int64     `gorm:"column:file_size;comment:外发的总文件大小byte为单位"`
	OccurTime         time.Time `gorm:"column:occur_time;type:timestamptz;comment:行为发生时间" json:"occur_time"`
}
