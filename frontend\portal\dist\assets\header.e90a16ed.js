/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import{_ as e,f as o,E as n,C as t,G as i,L as s,M as a,h as r,H as l,a as d,b as g,d as c,I as u,e as A,t as m,j as h,J as p}from"./index.a794166c.js";import{l as w}from"./logo.b56ac4ae.js";const C=""+new URL("avator.bd83723a.png",import.meta.url).href,f={class:"layout-header"},y={class:"header-logo"},L={src:w,alt:"",draggable:"false",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none'"},v={id:"u-header-menu",class:"right-wrapper"},b={id:"u-avator",ref:"countMenu"},I={class:"user-face"},M=["src"],W={class:"user-name"},R={class:"dropdown-menu header-count-menu"};const z=e({name:"ClientHeader",setup:()=>({userStore:o(),router:n()}),data:()=>({countCommand:"changePassword",isMaxWindow:!1,dropdownVisible:!1}),computed:{isLoggedIn(){return!!this.userStore.token},userAvatar(){return this.isLoggedIn?C:"data:image/png;base64,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"},displayUsername(){if(this.isLoggedIn){const e=this.userStore.userInfo;return e.displayName?e.displayName:e.name}return"未登录"}},watch:{userId(e,o){logger.log("用户id变动",e,o),console.debug("用户id变动")}},mounted(){this.setupClientLogoutListener()},beforeDestroy(){this.removeClientLogoutListener()},methods:{minimizeWnd(){t.minimizeWnd()},maximizeWndOrNot(){this.isMaxWindow?(t.normalnizeWnd(),this.isMaxWindow=!1):(t.maximizeWnd(),this.isMaxWindow=!0)},handleUserInfoClick(){this.isLoggedIn&&this.toggleDropdown()},toggleDropdown(){this.dropdownVisible=!this.dropdownVisible},closeDropdown(){this.dropdownVisible=!1},dropdownVisiHandle(){},async closeWnd(){t.hideWend()},userMenuHandle(e){if(this.closeDropdown(),this.countCommand=e,"lougOut"===e)this.handleLogoutConfirm()},async handleLogoutConfirm(){try{await i.confirm("确认注销登录吗？","提示",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"}),await this.performLogout()}catch(e){logger.log("用户取消注销操作")}},async performLogout(){logger.log("开始注销，显示Loading");const e=s.service({fullscreen:!0,text:"正在注销登录..."});try{logger.log("开始注销登录..."),logger.log("正在断开隧道连接..."),t.disconnectTunnel(),this.userStore.setTunState(0),logger.log("断开连接API调用成功，开始轮询状态..."),logger.log("隧道连接断开完成，继续注销流程"),t.setLoginStatus({Token:""});try{await this.userStore.ClearStorage(),this.userStore.LoginOut(),logger.log("服务器注销登录成功")}catch(o){console.error("服务器注销登录失败:",o)}this.router.push({name:"ClientNewLogin",query:t.getClientParams()})}catch(o){console.error("注销登录失败:",o),a.error("注销失败，请重试")}finally{e.close()}},waitForDisconnect:async()=>new Promise((e=>{let o=0;const n=setInterval((async()=>{try{o++,logger.log(`断开连接状态轮询第${o}次...`);const i=await t.getChannelStatus();if(logger.log("轮询状态响应:",i),i&&102===i.TunState)return logger.log("断开连接成功！"),clearInterval(n),void e(!0);o>=10&&(logger.log("断开连接超时"),clearInterval(n),e(!1))}catch(i){console.error("轮询状态失败:",i),o++,o>=10&&(clearInterval(n),e(!1))}}),1e3)})),async getCountMenuWidth(){const e=this.isZtpUser?44:0,o=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0);try{await t.init(),await t.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(o)+e})}catch(n){console.warn("设置标题尺寸失败:",n)}},hdEventHandle(e){if("router"===e.type)this.userMenuHandle(e.val)},setupClientLogoutListener(){"undefined"!=typeof window&&(this.clientLogoutHandler=async e=>{if(logger.log("收到客户端退出登录事件:",e.detail),this.isLoggedIn){logger.log("客户端已登录，开始执行退出登录流程");try{await this.performLogout()}catch(o){logger.log("处理客户端退出登录事件失败:",o)}}else logger.log("用户未登录，跳过退出登录处理")},window.addEventListener("clientLogoutReceived",this.clientLogoutHandler),logger.log("已注册客户端退出登录事件监听器"))},removeClientLogoutListener(){"undefined"!=typeof window&&this.clientLogoutHandler&&(window.removeEventListener("clientLogoutReceived",this.clientLogoutHandler),this.clientLogoutHandler=null,logger.log("已移除客户端退出登录事件监听器"))}}},[["render",function(e,o,n,t,i,s){const a=r("base-icon"),w=l("prevent-drag"),C=l("click-outside");return d(),g("div",f,[c("div",y,[u(c("img",L,null,512),[[w]])]),o[4]||(o[4]=c("div",{id:"u-electron-drag"},null,-1)),c("ul",v,[c("li",b,[u((d(),g("div",{id:"ui-headNav-header-div-account_info",class:"base-dropdown",onClick:o[1]||(o[1]=(...e)=>s.handleUserInfoClick&&s.handleUserInfoClick(...e))},[c("div",{class:A(["user-info",{"not-logged-in":!s.isLoggedIn}])},[c("div",I,[u(c("img",{src:s.userAvatar,alt:"",draggable:"false",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none'"},null,8,M),[[w]])]),c("span",W,m(s.displayUsername),1)],2),u(c("div",R,[c("div",{id:"ui-headNav-header-li-cancel_account",class:"dropdown-item",onClick:o[0]||(o[0]=e=>s.userMenuHandle("lougOut"))},[h(a,{class:"dropdown-item-icon",name:"logout"}),o[2]||(o[2]=c("span",{class:"dropdown-item-text"},"注销登录",-1))])],512),[[p,i.dropdownVisible&&s.isLoggedIn]])])),[[C,s.closeDropdown]])],512),o[3]||(o[3]=c("div",{class:"user-divider"},null,-1)),h(a,{class:"window-operate",name:"minus",onClick:s.minimizeWnd},null,8,["onClick"]),h(a,{class:"window-operate",name:i.isMaxWindow?"fullscreen_exit":"fullscreen",onClick:s.maximizeWndOrNot},null,8,["name","onClick"]),h(a,{class:"window-operate",name:"close",style:{"margin-right":"16px"},onClick:s.closeWnd},null,8,["onClick"])])])}],["__scopeId","data-v-515e76c2"]]);export{z as default};
