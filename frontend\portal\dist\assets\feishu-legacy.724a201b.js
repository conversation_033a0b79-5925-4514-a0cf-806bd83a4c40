/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function l(e,o,a,i){var l=o&&o.prototype instanceof u?o:u,s=Object.create(l.prototype);return n(s,"_invoke",function(e,n,o){var a,i,l,u=0,s=o||[],d=!1,g={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,n){return a=e,i=0,l=t,g.n=n,c}};function f(e,n){for(i=e,l=n,r=0;!d&&u&&!o&&r<s.length;r++){var o,a=s[r],f=g.p,v=a[2];e>3?(o=v===n)&&(l=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=t):a[0]<=f&&((o=e<2&&f<a[1])?(i=0,g.v=n,g.n=a[1]):f<v&&(o=e<3||a[0]>n||n>v)&&(a[4]=e,a[5]=n,g.n=v,i=0))}if(o||e>1)return c;throw d=!0,n}return function(o,s,v){if(u>1)throw TypeError("Generator is already running");for(d&&1===s&&f(s,v),i=s,l=v;(r=i<2?t:l)||!d;){a||(i?i<3?(i>1&&(g.n=-1),f(i,l)):g.n=l:g.v=l);try{if(u=2,a){if(i||(o="next"),r=a[o]){if(!(r=r.call(a,l)))throw TypeError("iterator result is not an object");if(!r.done)return r;l=r.value,i<2&&(i=0)}else 1===i&&(r=a.return)&&r.call(a),i<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=t}else if((r=(d=g.n<0)?l:e.call(n,g))!==c)break}catch(r){a=t,i=1,l=r}finally{u=1}}return{value:r,done:d}}}(e,a,i),!0),s}var c={};function u(){}function s(){}function d(){}r=Object.getPrototypeOf;var g=[][a]?r(r([][a]())):(n(r={},a,(function(){return this})),r),f=d.prototype=u.prototype=Object.create(g);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,n(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=d,n(f,"constructor",d),n(d,"constructor",s),s.displayName="GeneratorFunction",n(d,i,"GeneratorFunction"),n(f),n(f,i,"Generator"),n(f,a,(function(){return this})),n(f,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:v}})()}function n(e,t,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}n=function(e,t,r,o){if(t)a?a(e,t,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[t]=r;else{var i=function(t,r){n(e,t,(function(e){return this._invoke(t,r,e)}))};i("next",0),i("throw",1),i("return",2)}},n(e,t,r,o)}function t(e,n,t,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void t(e)}l.done?n(c):Promise.resolve(c).then(r,o)}function r(e){return function(){var n=this,r=arguments;return new Promise((function(o,a){var i=e.apply(n,r);function l(e){t(i,o,a,l,c,"next",e)}function c(e){t(i,o,a,l,c,"throw",e)}l(void 0)}))}}System.register(["./index-legacy.b871e767.js","./secondaryAuth-legacy.03ff7a1a.js"],(function(n,t){"use strict";var o,a,i,l,c,u,s,d,g,f,v,p,h,y,m=document.createElement("style");return m.textContent='@charset "UTF-8";.feishu-qrcode-container[data-v-6a1b273c]{padding-top:15px;overflow:hidden;text-align:center}\n',document.head.appendChild(m),{setters:[function(e){o=e._,a=e.f,i=e.r,l=e.o,c=e.P,u=e.v,s=e.a,d=e.b,g=e.d,f=e.i,v=e.Q,p=e.L,h=e.C},function(e){y=e.u}],execute:function(){var t={style:{"text-align":"center"}},m={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},b={"aria-hidden":"true",class:"icon",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},w=["src"],k=Object.assign({name:"Feishu"},{props:{authInfo:{type:Array,default:function(){return[]}},authId:{type:String,default:function(){return""}}},setup:function(n){var o=a(),k=y().handleSecondaryAuthResponse,_=i(0),x=i("https://passport.feishu.cn/suite/passport/oauth/authorize"),E=i(""),L=i(null),I=i(""),S=i(null),j=i(""),T=i(0),O=i(null),F=i(""),P=i(!1),R=i(null),A=i(0),C=n,Q=function(){var n=r(e().m((function n(){var t,r,o,a,i;return e().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,logger.log("开始绘制飞书二维码"),P.value=!1,R.value=null,A.value=0,U(),I.value=(new Date).getTime(),t=C.authInfo.fsAppId){e.n=1;break}return console.error("飞书配置缺失:",{appid:t}),B("飞书配置缺失"),e.a(2);case 1:return r={deviceid:"web_"+Date.now(),appid:t,time:I.value},o=v(),j.value="".concat(o,"/auth_callback/?auth_type=feishu&").concat(new URLSearchParams(r).toString()),a={client_id:t,state:C.authId,redirect_uri:j.value,response_type:"code"},F.value=x.value+"?"+new URLSearchParams(a).toString(),logger.log("飞书认证参数:",{appid:t,redirectUrl:j.value,goto:F.value}),document.getElementById("feishu_qrcode_login").innerHTML="",T.value=0,e.n=2,new Promise((function(e,n){if(window.QRLogin)e(window.QRLogin);else{var t=document.createElement("script");t.src=h.getNativeJsPath("/js/feishu.js"),t.async=!0,t.onload=function(){window.QRLogin?(logger.log("飞书SDK加载成功"),e(window.QRLogin)):n(new Error("飞书SDK加载失败：QRLogin未定义"))},t.onerror=function(){n(new Error("飞书SDK加载失败：网络错误"))},document.head.appendChild(t)}}));case 2:O.value=window.QRLogin({id:"feishu_qrcode_login",goto:F.value,width:"260",height:"300",style:"border:none;background-color:#FFFFFF;"}),void 0!==window.addEventListener?window.addEventListener("message",q,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",q),logger.log("飞书二维码绘制完成"),e.n=4;break;case 3:e.p=3,i=e.v,console.error("飞书二维码绘制失败:",i),B("二维码绘制失败: "+i.message);case 4:return e.a(2)}}),n,null,[[0,3]])})));return function(){return n.apply(this,arguments)}}(),q=function(){var n=r(e().m((function n(t){var r,o,a,i,l,c,u,s;return e().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,logger.log("收到飞书消息:",t),t&&t.data&&"[tea-sdk]ready"!==t.data){e.n=1;break}return logger.log("飞书其他消息:",t.data),e.a(2);case 1:if(r=t.origin,!(O.value&&O.value.matchOrigin&&O.value.matchOrigin(r))){e.n=2;break}S.value=p.service({fullscreen:!0,text:"登录中，请稍候..."}),logger.log("飞书消息数据:",t.data),o=t.data,logger.log("收到飞书loginTmpCode:",o),a="".concat(F.value,"&tmp_code=").concat(o),logger.log("飞书认证URL:",a),D(a),e.n=8;break;case 2:if(!t.data||"feishu_auth_callback"!==t.data.type){e.n=8;break}if(logger.log("收到callback.html认证结果:",t.data),i=t.data,l=i.code,c=i.state,!(u=i.error)){e.n=3;break}return console.error("飞书认证失败:",u),B("认证失败: "+u),e.a(2);case 3:if(!l||!c){e.n=7;break}if(!P.value){e.n=4;break}return logger.log("飞书认证正在进行中，忽略callback消息"),e.a(2);case 4:if(R.value!==l){e.n=5;break}return logger.log("飞书认证重复的callback消息，忽略"),e.a(2);case 5:return logger.log("飞书认证成功，code:",l,"state:",c),e.n=6,G(l,c);case 6:e.n=8;break;case 7:console.error("认证结果缺少必要参数"),B("认证结果无效");case 8:e.n=10;break;case 9:e.p=9,s=e.v,console.error("飞书消息处理失败:",s);case 10:return e.a(2)}}),n,null,[[0,9]])})));return function(e){return n.apply(this,arguments)}}(),D=function(e){logger.log("创建认证回调iframe:",e);var n=document.getElementById("feishu-callback-iframe");n&&n.remove();var t=document.createElement("iframe");t.id="feishu-callback-iframe",t.src=e,t.style.display="none",t.style.width="0",t.style.height="0",t.style.border="none",document.body.appendChild(t),logger.log("认证回调iframe已创建")},G=function(){var n=r(e().m((function n(t,r){var a,i,l,c;return e().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,a=Date.now(),logger.log("飞书认证回调触发:",{code:t,state:r,currentTime:a}),!P.value){e.n=1;break}return logger.log("飞书认证正在进行中，忽略重复调用 - 状态检查"),e.a(2);case 1:if(R.value!==t){e.n=2;break}return logger.log("飞书认证重复的authCode，忽略重复调用 - 认证码检查"),e.a(2);case 2:if(!(a-A.value<2e3)){e.n=3;break}return logger.log("飞书认证时间间隔过短，忽略重复调用 - 时间检查"),e.a(2);case 3:return P.value=!0,R.value=t,A.value=a,logger.log("飞书认证成功，开始处理:",{code:t,state:r}),i={clientId:"client_portal",grantType:"implicit",redirect_uri:"".concat(v(),"/auth_callback/"),idpId:Array.isArray(r)?r[0]:r,authWeb:{authWebCode:Array.isArray(t)?t[0]:t}},logger.log("调用登录接口，参数:",i),S.value&&S.value.close(),e.n=4,o.LoginIn(i,"feishu",C.authId);case 4:if(!(l=e.v)||-1===l.code){e.n=7;break}return e.n=5,k(l);case 5:if(!e.v){e.n=6;break}return logger.log("飞书登录成功，进入双因子验证"),e.a(2);case 6:logger.log("飞书登录成功"),e.n=8;break;case 7:console.error("飞书登录失败"),B("登录失败，请重试"),Q();case 8:setTimeout((function(){P.value=!1,logger.log("飞书认证状态已重置")}),5e3),e.n=10;break;case 9:e.p=9,c=e.v,console.error("飞书认证处理失败:",c),B("认证处理失败: "+c.message),Q(),P.value=!1,R.value=null;case 10:return e.a(2)}}),n,null,[[0,9]])})));return function(e,t){return n.apply(this,arguments)}}(),U=function(){T.value=0,L.value&&(clearInterval(L.value),L.value=null)},B=function(e){var n=document.getElementById("feishu_qrcode_login");n&&(n.innerHTML='\n      <div style="text-align: center; padding: 20px; color: #f56c6c;">\n        <div style="margin-bottom: 10px;">飞书认证失败</div>\n        <div style="font-size: 12px; color: #909399;">'.concat(e,'</div>\n        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">\n          重试\n        </button>\n      </div>\n    '))};return l((function(){logger.log("飞书认证组件挂载"),Q()})),c((function(){logger.log("飞书认证组件卸载"),P.value=!1,R.value=null,A.value=0;var e=document.getElementById("feishu-callback-iframe");e&&e.remove(),void 0!==window.addEventListener?window.removeEventListener("message",q,!1):void 0!==window.attachEvent&&window.detachEvent("onmessage",q)})),u(C,(function(){logger.log("飞书认证props变化，重新绘制二维码"),_.value++,Q()})),function(e,n){return s(),d("div",{key:_.value},[g("div",t,[g("span",m,[(s(),d("svg",b,n[0]||(n[0]=[g("use",{"xlink:href":"#icon-auth-feishu"},null,-1)]))),n[1]||(n[1]=f(" 飞书认证 "))])]),n[2]||(n[2]=g("div",{id:"feishu_qrcode_login",class:"feishu-qrcode-container"},null,-1)),g("iframe",{src:E.value,style:{display:"none"}},null,8,w)])}}});n("default",o(k,[["__scopeId","data-v-6a1b273c"]]))}}}))}();
