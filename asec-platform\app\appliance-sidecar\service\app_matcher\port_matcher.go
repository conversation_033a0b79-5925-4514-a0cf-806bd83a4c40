package app_matcher

import (
	"fmt"
	"strconv"
	"strings"
)

const (
	DirectMatcher PortMatcherType = iota // 直接单个匹配
	RangerMatcher                        // 范围匹配
)

type PortMatcher interface {
	GetPortMatcherType() PortMatcherType
	// DoMatcher
	// srcPort 匹配来源端口
	DoMatcher(srcPort int) (res bool, noMatchDetail string)
}

// DirectPortMatcherAdapter 直接匹配
type DirectPortMatcherAdapter struct {
	DirectPort int
}

func (p DirectPortMatcherAdapter) DoMatcher(srcPort int) (res bool, noMatchDetail string) {
	if p.DirectPort == srcPort {
		return true, ""
	}
	return false, fmt.Sprintf("port not match,require:%v, get:%v", srcPort, p.DirectPort)
}

func (p DirectPortMatcherAdapter) GetPortMatcherType() PortMatcherType {
	return DirectMatcher
}

// RangerPortMatcherAdapter 范围匹配
type RangerPortMatcherAdapter struct {
	StartPort int
	EndPort   int
}

func (r RangerPortMatcherAdapter) GetPortMatcherType() PortMatcherType {
	return RangerMatcher
}

func (r RangerPortMatcherAdapter) DoMatcher(srcPort int) (res bool, noMatchDetail string) {
	if r.StartPort <= srcPort && r.EndPort >= srcPort {
		return true, ""
	}
	return false, fmt.Sprintf("port not in range,require:[%v-%v],get:%v", r.StartPort, r.EndPort, srcPort)

}

// GetPortMatchers 获取 port规则 port匹配器
func GetPortMatchers(portRules string) ([]PortMatcher, error) {
	var pms []PortMatcher
	// , 分割
	ps := strings.Split(portRules, ",")
	for _, p := range ps {
		// 端口段
		if strings.Contains(p, "-") {
			split := strings.Split(p, "-")
			portStart, err := strconv.Atoi(split[0])
			portEnd, err := strconv.Atoi(split[1])
			if err != nil {
				return nil, err
			}
			pms = append(pms, RangerPortMatcherAdapter{
				StartPort: portStart,
				EndPort:   portEnd,
			})

		} else {
			// 单独端口
			port, err := strconv.Atoi(p)
			if err != nil {
				return nil, err
			}
			pms = append(pms, DirectPortMatcherAdapter{DirectPort: port})
		}
	}
	return pms, nil
}
