// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: application/v1/scan_task.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ScanTask_UpsetScanTask_FullMethodName  = "/asdsec.core.api.app.ScanTask/UpsetScanTask"
	ScanTask_CreateScanFile_FullMethodName = "/asdsec.core.api.app.ScanTask/CreateScanFile"
)

// ScanTaskClient is the client API for ScanTask service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScanTaskClient interface {
	UpsetScanTask(ctx context.Context, in *UpsetScanTaskReq, opts ...grpc.CallOption) (*CommonScanTaskResp, error)
	CreateScanFile(ctx context.Context, in *CreateScanFileReq, opts ...grpc.CallOption) (*CommonScanTaskResp, error)
}

type scanTaskClient struct {
	cc grpc.ClientConnInterface
}

func NewScanTaskClient(cc grpc.ClientConnInterface) ScanTaskClient {
	return &scanTaskClient{cc}
}

func (c *scanTaskClient) UpsetScanTask(ctx context.Context, in *UpsetScanTaskReq, opts ...grpc.CallOption) (*CommonScanTaskResp, error) {
	out := new(CommonScanTaskResp)
	err := c.cc.Invoke(ctx, ScanTask_UpsetScanTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scanTaskClient) CreateScanFile(ctx context.Context, in *CreateScanFileReq, opts ...grpc.CallOption) (*CommonScanTaskResp, error) {
	out := new(CommonScanTaskResp)
	err := c.cc.Invoke(ctx, ScanTask_CreateScanFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScanTaskServer is the server API for ScanTask service.
// All implementations must embed UnimplementedScanTaskServer
// for forward compatibility
type ScanTaskServer interface {
	UpsetScanTask(context.Context, *UpsetScanTaskReq) (*CommonScanTaskResp, error)
	CreateScanFile(context.Context, *CreateScanFileReq) (*CommonScanTaskResp, error)
	mustEmbedUnimplementedScanTaskServer()
}

// UnimplementedScanTaskServer must be embedded to have forward compatible implementations.
type UnimplementedScanTaskServer struct {
}

func (UnimplementedScanTaskServer) UpsetScanTask(context.Context, *UpsetScanTaskReq) (*CommonScanTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsetScanTask not implemented")
}
func (UnimplementedScanTaskServer) CreateScanFile(context.Context, *CreateScanFileReq) (*CommonScanTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateScanFile not implemented")
}
func (UnimplementedScanTaskServer) mustEmbedUnimplementedScanTaskServer() {}

// UnsafeScanTaskServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScanTaskServer will
// result in compilation errors.
type UnsafeScanTaskServer interface {
	mustEmbedUnimplementedScanTaskServer()
}

func RegisterScanTaskServer(s grpc.ServiceRegistrar, srv ScanTaskServer) {
	s.RegisterService(&ScanTask_ServiceDesc, srv)
}

func _ScanTask_UpsetScanTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsetScanTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScanTaskServer).UpsetScanTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScanTask_UpsetScanTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScanTaskServer).UpsetScanTask(ctx, req.(*UpsetScanTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScanTask_CreateScanFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateScanFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScanTaskServer).CreateScanFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScanTask_CreateScanFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScanTaskServer).CreateScanFile(ctx, req.(*CreateScanFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ScanTask_ServiceDesc is the grpc.ServiceDesc for ScanTask service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ScanTask_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "asdsec.core.api.app.ScanTask",
	HandlerType: (*ScanTaskServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsetScanTask",
			Handler:    _ScanTask_UpsetScanTask_Handler,
		},
		{
			MethodName: "CreateScanFile",
			Handler:    _ScanTask_CreateScanFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "application/v1/scan_task.proto",
}
