package service

import (
	"context"
	"encoding/json"
	normErrors "errors"
	"fmt"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	v1 "asdsec.com/asec/platform/api/conf/v1"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model/conf_model"
	"asdsec.com/asec/platform/pkg/model/push_model"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"github.com/golang/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"

	"asdsec.com/asec/platform/app/console/utils/web"
	"github.com/gin-gonic/gin"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"go.uber.org/zap"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"gopkg.in/ini.v1"

	"asdsec.com/asec/platform/app/console/app/appliancemgt/common"
	"asdsec.com/asec/platform/app/console/app/appliancemgt/dto"
	"asdsec.com/asec/platform/app/console/app/appliancemgt/repository"
	comm "asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/global/errors"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"

	taskDto "asdsec.com/asec/platform/app/console/app/agent_task/dto"
	taskSvc "asdsec.com/asec/platform/app/console/app/agent_task/service"
	moduleDto "asdsec.com/asec/platform/app/console/app/module_switch/dto"
	moduleService "asdsec.com/asec/platform/app/console/app/module_switch/service"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
)

var AppServiceImpl AppService

// AppServiceInit 单例对象
var AppServiceInit sync.Once

type AppService interface {
	GetById(ctx context.Context, id uint64) (model.Appliance, error)
	GetAppListByID(ctx context.Context, ids []string) ([]dto.AgentResp, error)
	GetAppList(ctx context.Context, pagination model.Pagination, req dto.GetAgentListReq) (dto.GetAgentListRsp, error)
	GenInstallCMD(ctx context.Context, req dto.ApplianceInstallReq, host string) (model.ApplianceInstall, error)
	GetAppliances(ctx context.Context, req model.Pagination) (model.Pagination, error)
	Delete(ctx context.Context, applianceId uint64) error
	DeviceCount(ctx context.Context) ([]dto.DeviceCount, error)
	UpdateAppliance(ctx context.Context, req dto.UpdateApplianceInstallReq) error
	CheckAppQuote(ctx context.Context, applianceId uint64) ([]string, error)
	GetDeployChannel(ctx context.Context) (model.DeployModeInfo, error)
	GetUpgradePolicyDetail(ctx context.Context, corpId, platform string) (dto.GetUpgradePolicyDetailResp, error)
	SetUpgradePolicy(ctx context.Context, req dto.SetUpgradePolicyReq) error
	GetUpgradeOverview(ctx context.Context, corpId, platform string) (dto.GetUpgradeOverviewResp, error)
	RestartGrayTask(ctx context.Context, id, oldVersion, newVersion string) error
	CleanGrayTask(ctx context.Context, id, version string) error
	AgentVersionCheck(ctx *gin.Context, corpId string, req dto.AgentVersionCheckReq) (dto.AgentVersionCheckResp, error)
	SetAgentUpgradeStatus(ctx context.Context, corpId string, req dto.SetAgentUpgradeStatusReq) error
	SetUpgradeLatestVersion() error
	CheckUpgradeTask() error
	CheckOffline(ctx context.Context) error
	UploadLog(ctx *gin.Context, req dto.UploadLogReq) aerrors.AError
	DeleteAgentsRecord(ctx context.Context, req dto.ReqIds) aerrors.AError
	UninstallAgents(ctx context.Context, req dto.ReqIds) aerrors.AError
	AgentActionStatus(c context.Context, agentId dto.SatusReq) aerrors.AError
	BindAgentUser(c context.Context, req dto.AgentBindUserReq) error
	AgentLogList(ctx context.Context, req dto.AgentLogsListReq) (model.Pagination, error)
	CheckAndUpdateGrayAgents(ctx context.Context) error
}

type appService struct {
	db repository.AppRepository
}

func (a *appService) CheckOffline(ctx context.Context) error {
	return a.db.CheckOffline(ctx)
}

func (a *appService) AgentLogList(ctx context.Context, req dto.AgentLogsListReq) (model.Pagination, error) {
	return a.db.AgentLogList(ctx, req)
}

func (a *appService) BindAgentUser(c context.Context, req dto.AgentBindUserReq) error {
	return a.db.BindAgentUser(c, req)
}

func (a *appService) AgentActionStatus(c context.Context, req dto.SatusReq) aerrors.AError {
	// 更新agent task status
	_ = taskSvc.GetAgentTaskService().UpdateAgentTaskStatus(c, req)
	global.SysLog.Sugar().Infof("agent action status. id=%s, status=%s, action=%s, desc=%s, time=%s, task_id=%s",
		req.Id, req.Status, req.Action, req.Desc, req.Time, req.TaskId)
	//如果卸载成功，则删除agent记录
	if strings.EqualFold(req.Status, common.StatusSuccess) && strings.EqualFold(req.Action, common.UninstallTaskType) {
		// 删除终端记录
		err := a.db.DeleteAgentsRecord(c, dto.ReqIds{Ids: []string{req.Id}})
		if err != nil {
			return aerrors.NewWithError(err, "Delete Agent Record Error")
		}
	}
	return nil
}

func (a *appService) DeleteAgentsRecord(ctx context.Context, req dto.ReqIds) aerrors.AError {
	return a.db.DeleteAgentsRecord(ctx, req)
}

func (a *appService) UninstallAgents(ctx context.Context, req dto.ReqIds) aerrors.AError {
	for _, agentId := range req.Ids {
		taskInfo := taskDto.TaskInfo{Cmd: common.UninstallTaskType, AgentId: agentId, Timeout: common.UninstallTimeout, TaskDesc: "下发卸载命令，等待执行"}
		taskReq := taskDto.CreateAgentTaskReq{TaskList: []taskDto.TaskInfo{taskInfo}}
		err := taskSvc.GetAgentTaskService().CreateAgentTask(ctx, taskReq)
		if err != nil {
			return aerrors.NewWithError(err, fmt.Sprintf("create uninstall agent id:%s task failed", agentId))
		}
	}
	return nil
}

func (a *appService) UploadLog(ctx *gin.Context, req dto.UploadLogReq) aerrors.AError {
	stringAgentId := strconv.FormatUint(req.ApplianceId, 10)
	pushCmd := v1.PushCmd{
		PushType: v1.PushType_UploadLog,
	}
	if req.Cmd != "" {
		exec := push_model.CmdExec{Cmd: req.Cmd}
		bytes, err := jsoniter.Marshal(exec)
		if err != nil {
			return aerrors.NewWithError(err, comm.OperateError)
		}
		pushCmd.CmdData = bytes
	}
	bytes, err := proto.Marshal(&pushCmd)
	if err != nil {
		return aerrors.NewWithError(err, comm.OperateError)
	}

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, comm.OperateError)
	}

	// 防重提交
	var count int64
	err = db.Model(&conf_model.ConfAgent{}).Where("conf_biz_id = ? AND conf_type = 'upload_log' ", stringAgentId).Count(&count).Error
	if err != nil {
		return aerrors.NewWithError(err, comm.OperateError)
	}
	if count > 0 {
		return aerrors.NewWithError(normErrors.New("duplicate submissions"), "DownloadLogDuplicateErr")
	}
	rDb, err := global.GetRedisClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, comm.OperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		AppIds := []string{stringAgentId}
		changeReq := conf_center.ConfChangeReq{
			ConfBizId:       stringAgentId,
			ConfType:        "upload_log",
			ConfData:        bytes,
			ConfGranularity: conf_center.NonGlobalConf,
			ConfRelation: &conf_center.ConfRelation{
				ApplianceId: AppIds,
			},
			Tx:         tx,
			RedisCli:   rDb,
			ChangeType: conf_center.AddConf,
		}
		err = conf_center.ConfChange(changeReq)
		if err != nil {
			return aerrors.NewWithError(err, comm.OperateError)
		}
		return nil
	})
	if err != nil {
		return aerrors.NewWithError(err, comm.OperateError)
	}
	return nil
}

func (a *appService) GetDeployChannel(ctx context.Context) (model.DeployModeInfo, error) {
	return a.db.GetDeployChannel(ctx)
}

func (a *appService) SetAgentUpgradeStatus(ctx context.Context, corpId string, req dto.SetAgentUpgradeStatusReq) error {
	lastVersion, _ := formatVersion(req.LastVersion)
	nextVersion, _ := formatVersion(req.NextVersion)
	req.LastVersion = lastVersion
	req.NextVersion = nextVersion

	agentId, err := strconv.ParseInt(req.ApplianceId, 10, 64)
	if err != nil {
		global.SysLog.Sugar().Errorf("ParseInt failed. err=%v", err)
		return err
	}

	upgradeTask, taskErr := a.db.GetUpgradeLog(ctx, agentId, req.NextVersion)
	if taskErr != nil && !normErrors.Is(taskErr, common.ErrRecordNotFound) {
		global.SysLog.Sugar().Errorf("GetUpgradeLog failed. err=%v", taskErr)
		return taskErr
	}

	upgradePolicy, err := a.db.GetUpgradePolicy(ctx, corpId, req.Platform)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradePolicy failed. err=%v", err)
		return err
	}

	// 不允许客户端版本比当前最新版本还要高
	if upgradePolicy.LatestVersion != req.NextVersion && common.CheckNewVersion(req.NextVersion, upgradePolicy.LatestVersion) {
		global.SysLog.Sugar().Errorf("req version=%s is higer than policy's=%s", req.NextVersion, upgradePolicy.LatestVersion)
		return normErrors.New("req version is higer than policy's")
	}

	if normErrors.Is(taskErr, common.ErrRecordNotFound) { // 之前就没有记录
		if err = a.CreateUpgradeLogFromClient(ctx, upgradePolicy, req); err != nil {
			global.SysLog.Sugar().Errorf("CreateUpgradeLogFromClient failed. err=%v", err)
			return err
		}
		return nil
	}
	if upgradeTask.Status == common.UpdateLogFailed { // 之前记录为失败状态
		// 判断是否达到最大限制，防止客户端多次调用刷爆数据库
		reached, err := a.IsReachFailedMax(ctx, upgradeTask, upgradePolicy)
		if err != nil {
			global.SysLog.Sugar().Errorf("IsReachFailedMax failed. err=%v", err)
			return err
		}
		if reached {
			global.SysLog.Sugar().Infof("reached max failed limit. req=%+v", req)
			return nil
		}
		// 增加一条失败记录
		if err = a.CreateUpgradeLogFromClient(ctx, upgradePolicy, req); err != nil {
			global.SysLog.Sugar().Errorf("CreateUpgradeLogFromClient failed. err=%v", err)
			return err
		}
		return nil
	}

	// 有记录
	if upgradeTask.Status == common.UpdateLogSuccess { // 成功状态 有可能是重复调用，直接返回
		return nil
	}
	if upgradeTask.Status == common.UpdateLogPending { // 这种情况不可能出现，除非服务端bug，或者客户端调用了version_check请求后，没有得到升级的指示，直接调用这个接口
		global.SysLog.Error("this case upgradeTask.Status=pending should not appear")
		return normErrors.New("this case upgradeTask.Status=pending should not appear")
	}
	if upgradeTask.Status != common.UpdateLogProcessing {
		global.SysLog.Sugar().Errorf("upgradeTask.Status=%s not support", upgradeTask.Status)
		return common.ErrUpgradeStatusNotSupport
	}
	// 升级中状态
	if err = a.SetUpgradeLogFromClient(ctx, upgradeTask, req); err != nil {
		global.SysLog.Sugar().Error("SetUpgradeLogFromClient failed. err=%v", err)
		return err
	}
	return nil
}

func (a *appService) CreateUpgradeLogFromClient(ctx context.Context, upgradePolicy dto.TbAgentUpgradePolicy, req dto.SetAgentUpgradeStatusReq) error {
	agentId, err := strconv.ParseInt(req.ApplianceId, 10, 64)
	if err != nil {
		global.SysLog.Sugar().Errorf("ParseInt failed. err=%v", err)
		return err
	}
	logId, err := snowflake.Sf.GetId()
	if err != nil {
		global.SysLog.Sugar().Errorf("snowflake generate failed. err=%v", err)
		return err
	}
	failedReason := ""
	failedSource := ""
	if req.Status == common.UpdateLogFailed {
		failedReason = req.FailedReason
		failedSource = common.SourceClient
	}

	if err = a.db.CreateUpgradeLog(ctx, dto.TbAgentUpgradeLog{
		ID:           int64(logId),
		PolicyID:     upgradePolicy.ID,
		AgentID:      agentId,
		Platform:     req.Platform,
		Status:       req.Status,
		LastVersion:  req.LastVersion,
		NextVersion:  req.NextVersion,
		FailedReason: failedReason,
		FailedSource: failedSource,
	}); err != nil {
		global.SysLog.Sugar().Errorf("CreateUpgradeLog failed. err=%v", err)
		return err
	}
	return nil
}

func (a *appService) SetUpgradeLogFromClient(ctx context.Context, updateLog dto.TbAgentUpgradeLog, req dto.SetAgentUpgradeStatusReq) error {
	switch req.Status {
	case common.UpdateLogSuccess:
		if err := a.db.SetUpgradeLogStatus(ctx, updateLog.ID, common.UpdateLogSuccess); err != nil {
			global.SysLog.Sugar().Errorf("SetUpgradeLogStatus failed. err=%v", err)
			return err
		}
	case common.UpdateLogFailed:
		if err := a.db.SetTaskFailed(ctx, updateLog.ID, req.FailedReason, common.SourceClient); err != nil {
			global.SysLog.Sugar().Errorf("SetTaskFailed failed. err=%v", err)
			return err
		}
	default:
		return common.ErrUpgradeStatusNotSupport
	}

	return nil
}

func formatVersion(version string) (string, error) {
	if version == "" {
		return "", fmt.Errorf("version is nil. %w", common.ErrParamError)
	}
	return strings.Split(version, "(")[0], nil
}

func webDownloadVersionCheck(ctx *gin.Context, service *appService, req dto.AgentVersionCheckReq) (dto.AgentVersionCheckResp, error) {
	modeInfo, err := service.GetDeployChannel(ctx) // 获取部署通道配置
	if err != nil {
		global.SysLog.Sugar().Errorf("GetDeployChannel failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	latestUpgradeConfig, err := service.ReadCloudLatestUpgradeConfig(req.Platform, modeInfo)
	if err != nil {
		global.SysLog.Sugar().Errorf("ReadCloudLatestUpgradeConfig failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	if latestUpgradeConfig.Version == "" { // 可能手误导致
		global.SysLog.Sugar().Errorf("latestUpgradeConfig.Version is nil. maybe cloud config is wrong")
		return dto.AgentVersionCheckResp{}, common.ErrConfigWrong
	}
	return formatAgentVersionCheckResp(ctx, latestUpgradeConfig, req.Platform, req.Version, modeInfo), nil
}

func agentNoNewversion(ctx *gin.Context, service *appService, req dto.AgentVersionCheckReq, upgradePolicy dto.TbAgentUpgradePolicy) (dto.AgentVersionCheckResp, error) {
	if req.Version != upgradePolicy.LatestVersion { // 客户端版本高于最新版本，正常情况是不应该出现的
		global.SysLog.Sugar().Warnf("client version=%s is higer than latest=%s", req.Version, upgradePolicy.LatestVersion)
		return dto.AgentVersionCheckResp{NeedUpgrade: false, LatestVersion: upgradePolicy.LatestVersion}, common.ErrVersionConflict
	}

	// 无新版本，查找对应升级任务的状态
	if req.ApplianceId == "" {
		return dto.AgentVersionCheckResp{NeedUpgrade: false, LatestVersion: upgradePolicy.LatestVersion}, nil
	}

	agentId, err := strconv.ParseInt(req.ApplianceId, 10, 64)
	if err != nil {
		global.SysLog.Sugar().Errorf("ParseInt failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	upgradeLog, err := service.db.GetUpgradeLog(ctx, agentId, req.Version)
	if err != nil && !normErrors.Is(err, common.ErrRecordNotFound) {
		global.SysLog.Sugar().Errorf("GetUpgradeLog failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	if normErrors.Is(err, common.ErrRecordNotFound) || upgradeLog.Status == common.UpdateLogFailed { // 之前没有记录，或者之前为失败，添加一条成功日志
		if _, err = service.NewUpgradeLogWithStatus(ctx, req.ApplianceId, "", upgradePolicy, common.UpdateLogSuccess); err != nil {
			global.SysLog.Sugar().Errorf("NewUpgradeLogWithStatus failed. err=%v", err)
			return dto.AgentVersionCheckResp{}, err
		}
	}
	if upgradeLog.Status == common.UpdateLogPending || upgradeLog.Status == common.UpdateLogProcessing { // 待处理和处理中更新为成功状态
		if err := service.db.SetUpgradeLogStatus(ctx, upgradeLog.ID, common.UpdateLogSuccess); err != nil {
			global.SysLog.Sugar().Errorf("SetUpgradeLogStatus failed. err=%v", err)
			return dto.AgentVersionCheckResp{}, err
		}
	}
	return dto.AgentVersionCheckResp{NeedUpgrade: false, LatestVersion: upgradePolicy.LatestVersion}, nil
}

// AgentVersionCheck
// TODO cl 1.数据库操作过多；2.数据库操作未考虑一致性事务，某个事务失败未考虑之前事务状态
func (a *appService) AgentVersionCheck(ctx *gin.Context, corpId string, req dto.AgentVersionCheckReq) (dto.AgentVersionCheckResp, error) {
	// 页面调用情况
	if req.Version == "" {
		return webDownloadVersionCheck(ctx, a, req)
	}

	// 客户端调用情况
	// 去掉版本号的日期（约定日期只是用来展示，不参与版本比较）
	version, err := formatVersion(req.Version)
	if err != nil {
		global.SysLog.Sugar().Errorf("formatVersion failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	req.Version = version

	// 先看本地升级配置
	upgradePolicy, err := a.db.GetUpgradePolicy(ctx, corpId, req.Platform)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradePolicyDetail failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	if upgradePolicy.LatestVersion == "" { // 按理说不应该出现，但远端同步过来可能为空
		global.SysLog.Sugar().Errorf("upgradePolicy.LatestVersion is nil")
		return dto.AgentVersionCheckResp{}, err
	}

	//客户端请求携带的当前版本和平台版本一致情况，说明升级成功，进行升级任务状态更新兜底。
	//测试模式下，如果开启了允许重复升级，则进行重复升级
	if !common.CheckNewVersion(upgradePolicy.LatestVersion, req.Version) && global.SysConfig.ServerInfo.AllowRepeatUpgrade != true {
		resp, err := agentNoNewversion(ctx, a, req, upgradePolicy)
		return resp, err
	}

	// 有新版本
	// 读取云端配置
	modeInfo, err := a.GetDeployChannel(ctx) // 获取部署通道配置
	if err != nil {
		global.SysLog.Sugar().Errorf("GetDeployChannel failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	latestUpgradeConfig, err := a.ReadCloudLatestUpgradeConfig(req.Platform, modeInfo)
	if err != nil {
		global.SysLog.Sugar().Errorf("ReadCloudLatestUpgradeConfig failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	if latestUpgradeConfig.Version == "" { // 可能手误导致
		global.SysLog.Sugar().Errorf("latestUpgradeConfig.Version is nil. maybe cloud config is wrong")
		return dto.AgentVersionCheckResp{}, common.ErrConfigWrong
	}

	// 判断本地和云端配置是否同步
	if common.CheckNewVersion(latestUpgradeConfig.Version, upgradePolicy.LatestVersion) { // 云端新版本还没更新到本地
		// 更新本地最新版本
		if err = a.SetSinglePlatLatestVersion(ctx, req.Platform); err != nil {
			global.SysLog.Sugar().Errorf("SetUpgradeLatestVersion failed. err=%v", err)
			return dto.AgentVersionCheckResp{}, err
		}
	} else if latestUpgradeConfig.Version != upgradePolicy.LatestVersion { // 云端版本低于本地最新版本，这种情况不应该发生，除非人为在云端配错了
		global.SysLog.Sugar().Errorf("latestUpgradeConfig.Version=%s is lower than upgradePolicy.LatestVersion=%s. may cloud config is wrong", latestUpgradeConfig.Version, upgradePolicy.LatestVersion)
		return dto.AgentVersionCheckResp{}, common.ErrConfigWrong
	}

	if req.ApplianceId == "" { // 特殊情况 客户端id没有，直接返回升级信息
		return formatAgentVersionCheckResp(ctx, latestUpgradeConfig, req.Platform, req.Version, modeInfo), nil
	}

	// 查找客户端对应升级任务
	agentId, err := strconv.ParseInt(req.ApplianceId, 10, 64)
	if err != nil {
		global.SysLog.Sugar().Errorf("ParseInt failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	upgradeLog, err := a.db.GetUpgradeLog(ctx, agentId, upgradePolicy.LatestVersion)
	if err != nil && !normErrors.Is(err, common.ErrRecordNotFound) {
		global.SysLog.Sugar().Errorf("GetUpgradeLog failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	if normErrors.Is(err, common.ErrRecordNotFound) {
		// 新增pending状态的upgradeLog
		upgradeLog, err = a.NewUpgradeLogWithStatus(ctx, req.ApplianceId, req.Version, upgradePolicy, common.UpdateLogPending)
		if err != nil {
			global.SysLog.Sugar().Errorf("NewUpgradeLogWithStatus failed. err=%v", err)
			return dto.AgentVersionCheckResp{}, err
		}
	}

	if upgradeLog.Status == common.UpdateLogFailed { // 失败状态
		reached, err := a.IsReachFailedMax(ctx, upgradeLog, upgradePolicy)
		if err != nil {
			global.SysLog.Sugar().Errorf("IsReachFailedMax failed. err=%v", err)
			return dto.AgentVersionCheckResp{}, err
		}
		if !reached { // 没有达到失败次数限制新增一条待处理
			upgradeLog, err = a.NewUpgradeLogWithStatus(ctx, req.ApplianceId, req.Version, upgradePolicy, common.UpdateLogPending)
			if err != nil {
				global.SysLog.Sugar().Errorf("NewUpgradeLogWithStatus failed. err=%v", err)
				return dto.AgentVersionCheckResp{}, err
			}
		} else {
			return dto.AgentVersionCheckResp{NeedUpgrade: false}, nil
		}
	}

	if upgradeLog.Status == common.UpdateLogProcessing { // 已经是处理中状态，可能是多次调用导致，直接返回升级信息
		return formatAgentVersionCheckResp(ctx, latestUpgradeConfig, req.Platform, req.Version, modeInfo), nil
	}

	//已经升级成功的状态如果标记为允许重复升级则跳过校验
	if upgradeLog.Status == common.UpdateLogSuccess && global.SysConfig.ServerInfo.AllowRepeatUpgrade != true { // 已经是成功状态，可能获取到了旧的请求，无需升级
		return dto.AgentVersionCheckResp{NeedUpgrade: false}, nil
	}

	if upgradeLog.Status != common.UpdateLogPending && global.SysConfig.ServerInfo.AllowRepeatUpgrade != true {
		global.SysLog.Sugar().Errorf("upgradeLog.Status=%s not support", upgradeLog.Status)
		return dto.AgentVersionCheckResp{}, common.ErrUpgradeStatusNotSupport
	}

	// 待处理状态
	switchToProcess, err := a.NeedSwitchToProcess(ctx, upgradeLog, upgradePolicy)
	if err != nil {
		global.SysLog.Sugar().Errorf("NeedSwitchToProcess failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	if !switchToProcess { // 返回无需升级
		return dto.AgentVersionCheckResp{NeedUpgrade: false}, nil
	}
	err = a.PushConcurrencyQueue(ctx, upgradeLog, upgradePolicy)
	if err != nil && !normErrors.Is(err, common.ErrQueueIsNotAvailable) {
		global.SysLog.Sugar().Errorf("NeedSwitchToProcess failed. err=%v", err)
		return dto.AgentVersionCheckResp{}, err
	}
	if normErrors.Is(err, common.ErrQueueIsNotAvailable) {
		global.SysLog.Sugar().Infof("queue(max=%d) is not available. please wait...", upgradePolicy.ConcurrencyCount)
		return dto.AgentVersionCheckResp{NeedUpgrade: false}, nil
	}
	return formatAgentVersionCheckResp(ctx, latestUpgradeConfig, req.Platform, req.Version, modeInfo), nil
}

func formatAgentVersionCheckResp(c *gin.Context, latestUpgradeConfig dto.AgentUpgradeConfig, platform, clientVersion string, modelInfo model.DeployModeInfo) dto.AgentVersionCheckResp {
	var res dto.AgentVersionCheckResp
	res.LatestVersion = latestUpgradeConfig.Version
	res.NeedUpgrade = true
	downloadUrl := fmt.Sprintf("%s/console/v1/agents/installer?platform=%s&version=%s",
		web.GetServerEndpoint(c), platform, latestUpgradeConfig.Version)
	if modelInfo.DeployMode == common.DeployModePublic {
		var agentFileName = common.GetAgentNameByPlatform(platform)
		escapedString := url.QueryEscape(latestUpgradeConfig.Version)
		tmpURL := fmt.Sprintf("%s/%s/%s/%s/%s/%s", global.SysConfig.Oss.OssPrefixURL, latestUpgradeConfig.DevMode, common.AgentsDir, platform, escapedString, agentFileName)
		downloadUrl = tmpURL
	}
	res.DownloadUrl = downloadUrl
	if platform == common.IosPlatformType {
		res.DownloadUrl = common.IOSAppStoreUrl
	}

	res.NextVersion = latestUpgradeConfig.NextVersion
	// 为了让老版本的适配升级上来
	if len(strings.Split(clientVersion, ".")) == 3 {
		res.LatestVersion = clientVersion + "1"
	}
	res.ForceUpdate = true
	res.ReleaseNotes = latestUpgradeConfig.Note
	res.MD5SUM = latestUpgradeConfig.Md5
	res.SHA256SUM = latestUpgradeConfig.Sha256

	host := strings.Split(web.GetServerHost(c), ":")
	ip := host[0]
	port := web.GetReqServerPort(c)
	res.LatestFilename = common.FormatWebFileName(platform, latestUpgradeConfig.Version, ip, port)
	dUrl, err := url.Parse(res.DownloadUrl)
	if err != nil {
		return res
	}
	downloadQ := dUrl.Query()
	res.DownloadUrl = fmt.Sprintf("%s://%s%s?%s", dUrl.Scheme, dUrl.Host, dUrl.Path, downloadQ.Encode())
	return res
}

func (a *appService) NewUpgradeLogWithStatus(ctx context.Context, agentId, lastVersion string, upgradePolicy dto.TbAgentUpgradePolicy, status string) (dto.TbAgentUpgradeLog, error) {
	intAgentId, err := strconv.ParseInt(agentId, 10, 64)
	if err != nil {
		global.SysLog.Sugar().Errorf("ParseInt failed. err=%v", err)
		return dto.TbAgentUpgradeLog{}, err
	}
	logId, err := snowflake.Sf.GetId()
	if err != nil {
		global.SysLog.Sugar().Errorf("snowflake generate failed. err=%v", err)
		return dto.TbAgentUpgradeLog{}, err
	}
	upgradeLog := dto.TbAgentUpgradeLog{
		ID:          int64(logId),
		PolicyID:    upgradePolicy.ID,
		AgentID:     intAgentId,
		Platform:    upgradePolicy.Platform,
		Status:      status,
		LastVersion: lastVersion,
		NextVersion: upgradePolicy.LatestVersion,
		IsGray:      false,
	}
	if err = a.db.CreateUpgradeLog(ctx, upgradeLog); err != nil {
		global.SysLog.Sugar().Errorf("CreateUpgradeLog failed. err=%v", err)
		return dto.TbAgentUpgradeLog{}, err
	}
	return upgradeLog, nil
}

func (a *appService) IsReachFailedMax(ctx context.Context, upgradeLog dto.TbAgentUpgradeLog, upgradePolicy dto.TbAgentUpgradePolicy) (bool, error) {
	failedCount, err := a.db.CountFailedAgentUpgradeLogs(ctx, upgradeLog.AgentID, upgradePolicy.LatestVersion)
	if err != nil {
		global.SysLog.Sugar().Infof("CountAgentUpgradeLogsOfStatus failed. err=%v", err)
		return false, err
	}
	if failedCount >= int64(upgradePolicy.FailedLimit) {
		return true, nil
	}
	return false, nil
}

func (a *appService) NeedSwitchToProcess(ctx context.Context, upgradeLog dto.TbAgentUpgradeLog, upgradePolicy dto.TbAgentUpgradePolicy) (bool, error) {

	grayEnable, err := a.GrayCheck(ctx, upgradePolicy)
	if err != nil {
		global.SysLog.Sugar().Errorf("GrayCheck failed. err=%v", err)
		return false, err
	}
	if !grayEnable { // 没开启灰度
		return true, nil
	}
	// 开启了灰度
	if upgradeLog.IsGray { // 灰度任务
		return true, nil
	}
	return false, nil
}

func (a *appService) GrayCheck(ctx context.Context, upgradePolicy dto.TbAgentUpgradePolicy) (bool, error) {
	if !upgradePolicy.GrayEnable { // 未开启灰度
		return false, nil
	}
	grayPolicy, err := a.db.GetGrayPolicy(ctx, upgradePolicy.GrayPolicyID)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetGrayPolicy failed. err=%v", err)
		return false, err
	}
	now := time.Now().Local()
	if !(grayPolicy.StartTime.Local().Before(now) && grayPolicy.EndTime.Local().After(now)) { // 不在当前灰度范围内
		return false, nil
	}
	return true, nil
}

func (a *appService) IsConcurrencyQueueAvailable(ctx context.Context, upgradePolicy dto.TbAgentUpgradePolicy) (bool, error) {
	processCount, err := a.db.CountUpgradeLogsOfStatus(ctx, upgradePolicy.ID, upgradePolicy.LatestVersion, common.UpdateLogProcessing)
	if err != nil {
		global.SysLog.Sugar().Errorf("CountUpgradeLogsOfStatus failed. err=%v", err)
		return false, err
	}
	if processCount >= int64(upgradePolicy.ConcurrencyCount) { // 并发队列已满
		return false, nil
	}
	return true, nil
}

func (a *appService) PushConcurrencyQueue(ctx context.Context, upgradeLog dto.TbAgentUpgradeLog, upgradePolicy dto.TbAgentUpgradePolicy) error {
	available, err := a.IsConcurrencyQueueAvailable(ctx, upgradePolicy)
	if err != nil {
		global.SysLog.Sugar().Errorf("IsConcurrencyQueueAvailable failed. err=%v", err)
		return err
	}
	if !available {
		return common.ErrQueueIsNotAvailable
	}
	if err = a.db.SetUpgradeLogStatus(ctx, upgradeLog.ID, common.UpdateLogProcessing); err != nil {
		global.SysLog.Sugar().Errorf("SetUpgradeLogStatus failed. err=%v", err)
		return err
	}
	return nil
}

func (a *appService) SetUpgradeLatestVersion() error {
	var ctx = context.Background()
	var platforms = []string{common.MacPlatformType, common.WindowsPlatformType, common.AndroidPlatformType}
	var err error
	for _, plat := range platforms {
		err = a.SetSinglePlatLatestVersion(ctx, plat)
		if err != nil {
			continue
		}
	}
	if err != nil {
		return err
	}
	return nil
}

func (a *appService) SetSinglePlatLatestVersion(ctx context.Context, plat string) error {
	// 获取云端配置
	modeInfo, err := a.GetDeployChannel(ctx) // 获取部署通道配置
	if err != nil {
		global.SysLog.Sugar().Errorf("GetDeployChannel failed. err=%v", err)
		return err
	}
	latestUpgradeConfig, err := a.ReadCloudLatestUpgradeConfig(plat, modeInfo)
	if err != nil {
		global.SysLog.Sugar().Errorf("ReadCloudLatestUpgradeConfig failed. err=%v, plat=%s", err, plat)
		return err
	}
	// 获取本地策略配置
	policy, err := a.GetUpgradePolicyDetail(ctx, common.DefaultTenantID, plat)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradePolicyDetail failed. err=%v, plat=%s", err, plat)
		//return err
	}
	// 判断是否要更新
	if policy.LatestVersion == common.DefaultVersion || common.CheckNewVersion(latestUpgradeConfig.Version, policy.LatestVersion) {
		global.SysLog.Sugar().Infof("start to update agent version. platform: %s,local version:%s,cloudVersion:%s", plat, policy.LatestVersion, latestUpgradeConfig.Version)
		// 更新本地策略的最新版本和云端保持一致
		if err := a.db.SetPolicyLatestVersion(ctx, policy.ID, latestUpgradeConfig.Version); err != nil {
			global.SysLog.Sugar().Errorf("SetPolicyLatestVersion failed. err=%v id=%s", err, policy.ID)
			return err
		}
		// 更新升级时间
		if err := a.db.SetPolicyUpgradeTime(ctx, policy.ID); err != nil {
			global.SysLog.Sugar().Errorf("SetPolicyUpgradeTime failed. err=%v id=%s", err, policy.ID)
			return err
		}

		// 重启对应灰度任务
		if err := a.RestartGrayTask(ctx, policy.ID, policy.LatestVersion, latestUpgradeConfig.Version); err != nil {
			global.SysLog.Sugar().Errorf("RestartGrayTask failed. err=%v, plat=%s", err, plat)
			return err
		}
	}
	return nil
}
func (a *appService) CheckUpgradeTask() error {
	var ctx = context.Background()
	var platforms = []string{common.MacPlatformType, common.WindowsPlatformType, common.AndroidPlatformType}
	for _, plat := range platforms {
		// 获取对应全局配置
		policy, err := a.db.GetUpgradePolicy(ctx, common.DefaultTenantID, plat)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetUpgradePolicy failed. err=%v, plat=%s", err, plat)
			return err
		}
		// 获取对应正在执行的任务
		tasks, err := a.db.GetUpgradeLogsOfStatus(ctx, policy.ID, policy.LatestVersion, common.UpdateLogProcessing)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetUpgradeLogsOfStatus failed. err=%v", err)
			return err
		}
		now := time.Now().Local()
		for _, task := range tasks {
			if task.UpdatedAt.Add(time.Duration(policy.TaskTimeOutSecs) * time.Second).After(now) {
				continue
			}
			// 超时了设置任务失败
			if err := a.db.SetTaskFailed(ctx, task.ID, string(common.UpgradeFailedTimeout), common.SourceServer); err != nil {
				global.SysLog.Sugar().Errorf("SetUpgradeLogStatus failed. err=%v", err)
				return err
			}
		}
	}
	return nil
}

func (a *appService) ReadCloudLatestUpgradeConfig(platform string, modeInfo model.DeployModeInfo) (dto.AgentUpgradeConfig, error) {
	var latestVersion dto.AgentUpgradeConfig
	if platform == common.IosPlatformType {
		latestVersion.Version = "-" //占位,防止后续条件判断报错。TODO ios增加更新机制后移除
		return latestVersion, nil
	}

	devMode := modeInfo.DevMode
	if devMode == "" {
		devMode = common.ProductMode
	}

	configs, err := a.ReadCloudUpgradeConfig(platform, devMode, modeInfo.DeployMode)
	if err != nil {
		global.SysLog.Sugar().Errorf("ReadCloudUpgradeConfig failed. err=%v", err)
		return dto.AgentUpgradeConfig{}, err
	}
	for _, v := range configs { // 老代码，这里的约定要明确一点
		if v.NextVersion == "" {
			latestVersion = v
		}
	}
	latestVersion.DevMode = devMode
	return latestVersion, nil
}

func (a *appService) ReadCloudUpgradeConfig(platform string, devMode string, deployMode string) ([]dto.AgentUpgradeConfig, error) {
	dirPath := filepath.Join(common.AgentDir, platform)
	// 判断目录是否存在
	dirExists, _ := utils.FileExists(dirPath)
	if !dirExists {
		if err := os.MkdirAll(dirPath, 0750); err != nil {
			return []dto.AgentUpgradeConfig{}, err
		}
	}
	filePath := filepath.Join(dirPath, common.VersionInfoFile)
	//如果是在线下载客户端模式，先从OSS读取
	if deployMode == common.DeployModePublic {
		ObjectKey := path.Join(devMode, common.AgentsDir, platform, common.VersionInfoFile)
		err := GetFileFromOss(ObjectKey, filePath)
		if err != nil {
			global.SysLog.Error(fmt.Sprintf("ObjectKey:%s, Bucket:%s, err_msg:", ObjectKey, global.SysConfig.Oss.Bucket), zap.Error(err))
			return []dto.AgentUpgradeConfig{}, common.ErrOssWrong
		}
	}
	data, err := os.ReadFile(filePath)
	if err != nil {
		global.SysLog.Error("ReadFile err", zap.Error(err))
		return []dto.AgentUpgradeConfig{}, common.ErrFileParseWrong
	}
	var configs []dto.AgentUpgradeConfig
	err = json.Unmarshal(data, &configs)
	if err != nil {
		global.SysLog.Error("Unmarshal err", zap.Error(err))
		return []dto.AgentUpgradeConfig{}, common.ErrFileParseWrong
	}
	return configs, nil
}

func GetFileFromOss(ObjectKey, filePath string) error {
	client, err := oss.New(global.SysConfig.Oss.Endpoint, global.SysConfig.Oss.AccessKey, global.SysConfig.Oss.AccessKeySecret)
	if err != nil {
		global.SysLog.Error("client err", zap.Error(err))
		return err
	}
	bucket, err := client.Bucket(global.SysConfig.Oss.Bucket)
	err = bucket.GetObjectToFile(ObjectKey, filePath)
	if err != nil {
		return err
	}
	return nil
}

func (a *appService) GetUpgradeOverview(ctx context.Context, corpId, platform string) (dto.GetUpgradeOverviewResp, error) {
	// 获取所有客户端
	allAgents, err := a.GetAllAgentResp(ctx)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetAllAgentResp failed. err=%v", err)
		return dto.GetUpgradeOverviewResp{}, err
	}

	// 记录客户端版本以及对应的数量
	var others = 0
	var versionCountMap = make(map[string]int)
	for _, agent := range allAgents {
		if agent.AppPlat != platform {
			continue
		}
		if agent.Version == "" {
			others = others + 1
		} else {
			if _, ok := versionCountMap[agent.Version]; ok {
				versionCountMap[agent.Version] = versionCountMap[agent.Version] + 1
			} else {
				versionCountMap[agent.Version] = 1
			}
		}
	}
	var versions []dto.VersionCount
	for version, count := range versionCountMap {
		versions = append(versions, dto.VersionCount{
			Version: version,
			Count:   count,
		})
	}
	versions = append(versions, dto.VersionCount{
		Version: common.OtherVersion,
		Count:   others,
	})

	return dto.GetUpgradeOverviewResp{
		Versions: versions,
	}, nil
}

func (a *appService) GetUpgradePolicyDetail(ctx context.Context, corpId, platform string) (dto.GetUpgradePolicyDetailResp, error) {
	// 获取全局策略信息
	overallPolicy, err := a.db.GetUpgradePolicy(ctx, corpId, platform)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradePolicy failed. err=%v", err)
		return dto.GetUpgradePolicyDetailResp{}, err
	}
	result, err := overallPolicy.ToUpgradePolicyDetailResp()
	if err != nil {
		global.SysLog.Sugar().Errorf("ToUpgradePolicyDetailResp failed. err=%v", err)
		return dto.GetUpgradePolicyDetailResp{}, err
	}
	if overallPolicy.GrayPolicyID == "" { // 没有关联灰度直接返回
		return result, nil
	}

	// 获取灰度信息
	grayPolicy, err := a.db.GetGrayPolicy(ctx, overallPolicy.GrayPolicyID)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetGrayPolicy failed. err=%v, id=%s", err, overallPolicy.GrayPolicyID)
		return dto.GetUpgradePolicyDetailResp{}, err
	}

	// 填充灰度信息
	result.GrayMode = grayPolicy.Mode
	result.Duration = grayPolicy.Duration
	result.DurationUnit = grayPolicy.DurationUnit
	result.RandomCount = grayPolicy.RandomCount
	result.GroupIds, err = a.db.QueryGroupDetails(ctx, grayPolicy.GroupIds)
	result.UserIds, err = a.db.QueryUserDetails(ctx, grayPolicy.UserIds)
	result.AgentIds, err = a.db.QueryAgentDetails(ctx, grayPolicy.AgentIds)

	return result, err
}

func (a *appService) SetUpgradePolicy(ctx context.Context, req dto.SetUpgradePolicyReq) error {
	policy, err := a.db.GetUpgradePolicyByID(ctx, req.ID)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradePolicyByID failed. err=%v", err)
		return err
	}

	// 更新全局策略配置
	policy.Mode = req.UpgradeMode
	policy.ConcurrencyCount = req.ConcurrencyCount
	policy.GrayEnable = req.GrayEnable
	if err = a.db.UpdateUpgradePolicy(ctx, policy); err != nil {
		global.SysLog.Sugar().Errorf("UpdateUpgradePolicy failed. err=%v", err)
		return err
	}

	if !policy.GrayEnable {
		if err = a.CleanGrayTask(ctx, policy.ID, policy.LatestVersion); err != nil {
			global.SysLog.Sugar().Errorf("CleanGrayTask failed. err=%v", err)
			return err
		}
		return nil
	}

	// 更新灰度策略配置
	// 前端设置待更新参数
	var grayPolicy dto.TbAgentUpgradeGrayPolicy
	if err = copier.Copy(&grayPolicy, &req); err != nil {
		global.SysLog.Sugar().Errorf("Copy failed. err=%v", err)
		return err
	}

	// 收集所有的 agent_ids
	var allAgentIds []int64

	// 1. 处理直接传入的 AgentIds
	if len(req.AgentIds) > 0 {
		intAgentIds, err := common.StringArrayToInt(req.AgentIds)
		if err != nil {
			global.SysLog.Sugar().Errorf("StringArrayToInt failed. err=%v", err)
			return err
		}
		allAgentIds = append(allAgentIds, intAgentIds...)
	}

	// 2. 处理 GroupIds - 通过 group_id 获取所有 user_id，再获取 agent_id
	if len(req.GroupIds) > 0 {
		groupAgentIds, err := a.GetAgentIdsByGroupIds(ctx, req.GroupIds)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAgentIdsByGroupIds failed. err=%v", err)
			return err
		}
		allAgentIds = append(allAgentIds, groupAgentIds...)
	}

	// 3. 处理 UserIds - 通过 user_id 获取 agent_id
	if len(req.UserIds) > 0 {
		userAgentIds, err := a.GetAgentIdsByUserIds(ctx, req.UserIds)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAgentIdsByUserIds failed. err=%v", err)
			return err
		}
		allAgentIds = append(allAgentIds, userAgentIds...)
	}

	// 去重处理
	deduplicatedAgentIds := common.Deduplication(allAgentIds)

	grayPolicy.Mode = req.GrayMode
	grayPolicy.AgentIds = deduplicatedAgentIds
	grayPolicy.UpgradePolicyID = policy.ID

	// 设置灰度开启和结束时间
	now := time.Now().Local()
	period, err := common.CalculatePeriod(grayPolicy.Duration, grayPolicy.DurationUnit)
	if err != nil {
		global.SysLog.Sugar().Errorf("CalculatePeriod failed. err=%v", err)
		return err
	}
	grayPolicy.StartTime = now
	grayPolicy.EndTime = now.Add(period)

	// 新增/更新对应配置
	if policy.GrayPolicyID == "" { // 未配置过灰度
		grayPolicy.ID = uuid.New().String()
		if err = a.db.CreateGrayPolicy(ctx, policy.ID, grayPolicy); err != nil {
			global.SysLog.Sugar().Errorf("CreateGrayPolicy failed. err=%v", err)
			return err
		}
	} else { // 更新灰度配置
		grayPolicy.ID = policy.GrayPolicyID
		if err = a.db.UpdateGrayPolicy(ctx, grayPolicy); err != nil {
			global.SysLog.Sugar().Errorf("CreateGrayPolicy failed. err=%v", err)
			return err
		}
	}

	if err = a.RestartGrayTask(ctx, policy.ID, common.VersionUnknown, policy.LatestVersion); err != nil {
		global.SysLog.Sugar().Errorf("RestartGrayTask failed. err=%v", err)
		return err
	}
	return nil
}

func (a *appService) CleanGrayTask(ctx context.Context, policyID, version string) error {
	// 还未升级的任务修改为失败状态
	if err := a.db.BatchCleanPendingGrayTask(ctx, policyID, version); err != nil {
		global.SysLog.Sugar().Errorf("BatchCleanPendingGrayTask failed. err=%v", err)
		return err
	}
	return nil
}

func (a *appService) RestartGrayTask(ctx context.Context, policyID, oldVersion, newVersion string) error {
	// 先把以前的任务给清了
	// 清除升级旧版本的任务 -- 对应检测到有新版本发布，但灰度还没完成的情况
	if err := a.CleanGrayTask(ctx, policyID, oldVersion); err != nil {
		global.SysLog.Sugar().Errorf("CleanGrayTask failed. err=%v", err)
		return err
	}
	// 清除升级新版本的任务 -- 对应用户修改灰度策略，重新进行灰度的情况
	if err := a.CleanGrayTask(ctx, policyID, newVersion); err != nil {
		global.SysLog.Sugar().Errorf("CleanGrayTask failed. err=%v", err)
		return err
	}

	// 根据灰度策略选择对应agent_ids
	grayPolicy, err := a.db.GetGrayPolicyByUpgradeID(ctx, policyID)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetGrayPolicy failed. err=%v", err)
		return err
	}
	agentIds, err := a.ChooseGrayAgents(ctx, grayPolicy)
	if err != nil {
		global.SysLog.Sugar().Errorf("ChooseGrayAgents failed. err=%v", err)
		return err
	}

	// 新增新的灰度任务
	overallPolicy, err := a.db.GetUpgradePolicyByID(ctx, policyID)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUpgradePolicyByID failed. err=%v", err)
		return err
	}
	var tasks []dto.TbAgentUpgradeLog
	for _, agentId := range agentIds {
		id, err := snowflake.Sf.GetId()
		if err != nil {
			global.SysLog.Sugar().Errorf("GetId failed. err=%v", err)
			return err
		}
		tasks = append(tasks, dto.TbAgentUpgradeLog{
			ID:          int64(id),
			PolicyID:    policyID,
			AgentID:     agentId,
			Platform:    overallPolicy.Platform,
			Status:      common.UpdateLogPending,
			LastVersion: oldVersion,
			NextVersion: newVersion,
			IsGray:      true,
		})
	}
	if err = a.db.BatchCreateGrayTask(ctx, tasks); err != nil {
		global.SysLog.Sugar().Errorf("BatchCreateGrayTask failed. err=%v", err)
		return err
	}

	// 重新开启灰度任务后需要重设灰度开启和结束时间
	now := time.Now().Local()
	period, err := common.CalculatePeriod(grayPolicy.Duration, grayPolicy.DurationUnit)
	if err != nil {
		global.SysLog.Sugar().Errorf("CalculatePeriod failed. err=%v", err)
		return err
	}
	if err = a.db.UpdateGrayPolicyPeriod(ctx, grayPolicy.ID, now, now.Add(period)); err != nil {
		global.SysLog.Sugar().Errorf("UpdateGrayPolicyPeriod failed. err=%v", err)
		return err
	}
	return nil
}

func (a *appService) ChooseGrayAgents(ctx context.Context, grayPolicy dto.TbAgentUpgradeGrayPolicy) ([]int64, error) {
	// 需要过滤当前版本处于【待处理】、【正在处理】和【成功】状态的 agent_ids
	switch grayPolicy.Mode {
	case common.GrayModeRandom:
		upgradePolicy, err := a.db.GetUpgradePolicyByID(ctx, grayPolicy.UpgradePolicyID)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAllAgentIDs failed. err=%v", err)
			return []int64{}, err
		}
		// 获取对应平台所有agents
		allAgentIDs, err := a.GetAllAgentIDs(ctx, upgradePolicy.Platform)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAllAgentIDs failed. err=%v", err)
			return []int64{}, err
		}

		// 查找 【待处理】、【正在处理】和【成功】状态的 agent_ids
		agentIDsToFilter, err := a.db.GetAgentIDsOfStatus(ctx, upgradePolicy.ID, upgradePolicy.LatestVersion, []string{common.UpdateLogPending, common.UpdateLogProcessing, common.UpdateLogSuccess})
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAgentIDsOfStatus failed. err=%v", err)
			return []int64{}, err
		}
		// 过滤后，按心跳更新顺序从最新到最晚选择
		agentIDsToSelect := common.FilterList(allAgentIDs, agentIDsToFilter)
		//randomAgentIDs := common.RandomSelect(agentIDsToSelect, int(grayPolicy.RandomCount))
		if len(agentIDsToSelect) > 0 {
			if int(grayPolicy.RandomCount) >= len(agentIDsToSelect) {
				return agentIDsToSelect, nil
			}
			randomAgentIDs := agentIDsToSelect[:int(grayPolicy.RandomCount)]
			return randomAgentIDs, nil
		}
		return nil, nil

	case common.GrayModeCustom:
		var allCustomAgentIds []int64

		// 1. 处理 GroupIds - 通过 group_id 获取 agent_id
		if len(grayPolicy.GroupIds) > 0 {
			for _, g := range grayPolicy.GroupIds {
				agentIds, err := a.GetAgentIDsOfGroup(ctx, g)
				if err != nil {
					global.SysLog.Sugar().Errorf("GetAgentIDsOfGroup failed. err=%v", err)
					return []int64{}, err
				}
				allCustomAgentIds = append(allCustomAgentIds, agentIds...)
			}
		}

		// 2. 处理 UserIds - 通过 user_id 获取 agent_id
		if len(grayPolicy.UserIds) > 0 {
			userAgentIds, err := a.GetAgentIdsByUserIds(ctx, grayPolicy.UserIds)
			if err != nil {
				global.SysLog.Sugar().Errorf("GetAgentIdsByUserIds failed. err=%v", err)
				return []int64{}, err
			}
			allCustomAgentIds = append(allCustomAgentIds, userAgentIds...)
		}

		// 3. 处理直接指定的 AgentIds
		allCustomAgentIds = append(allCustomAgentIds, grayPolicy.AgentIds...)

		// 去重处理
		deDupAgentIds := common.Deduplication(allCustomAgentIds)

		upgradePolicy, err := a.db.GetUpgradePolicyByID(ctx, grayPolicy.UpgradePolicyID)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAllAgentIDs failed. err=%v", err)
			return []int64{}, err
		}
		// fix 查找【待处理】的agent_ids设置为灰度任务 -- @zr 测试发现：如果该agent为全局任务的pending状态（并发队列满情况），那该任务必须要等到灰度任务生效时间过期之后才会被处理
		pendingAgentIds, err := a.db.GetAgentIDsOfStatus(ctx, upgradePolicy.ID, upgradePolicy.LatestVersion, []string{common.UpdateLogPending})
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAgentIDsOfStatus failed. err=%v", err)
			return []int64{}, err
		}
		// 获取交集，只有需要灰度的id才能被置为灰度
		needGrayAgentIds := common.GetDeduplicationList(pendingAgentIds, deDupAgentIds)
		if err = a.db.SetAgentsToGray(ctx, upgradePolicy.ID, needGrayAgentIds, upgradePolicy.LatestVersion); err != nil {
			global.SysLog.Sugar().Errorf("SetAgentsToGray failed. err=%v", err)
			return []int64{}, err
		}

		// 查找 【待处理】、 【正在处理】和【成功】状态的 agent_ids
		agentIDsToFilter, err := a.db.GetAgentIDsOfStatus(ctx, upgradePolicy.ID, upgradePolicy.LatestVersion, []string{common.UpdateLogPending, common.UpdateLogProcessing, common.UpdateLogSuccess})
		if err != nil {
			global.SysLog.Sugar().Errorf("GetAgentIDsOfStatus failed. err=%v", err)
			return []int64{}, err
		}
		return common.FilterList(deDupAgentIds, agentIDsToFilter), nil
	default:
		return []int64{}, fmt.Errorf("mode=%s err=%w", grayPolicy.Mode, common.ErrGrayModeNotSupport)
	}
}

func (a *appService) GetAllAgentIDs(ctx context.Context, platform string) ([]int64, error) {
	// 获取所有agents
	allAgents, err := a.GetAllAgentResp(ctx)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetAllAgentResp failed. err=%v", err)
		return []int64{}, err
	}

	var result []int64
	if platform == common.AllPlatform {
		for _, agent := range allAgents {
			result = append(result, int64(agent.ApplianceId))
		}
	} else {
		for _, agent := range allAgents {
			if agent.AppPlat == platform {
				result = append(result, int64(agent.ApplianceId))
			}
		}
	}
	return result, nil
}

func (a *appService) GetAllAgentResp(ctx context.Context) ([]dto.AgentResp, error) {
	// 获取所有agents
	allAgents, err := a.db.GetAgents(ctx, model.Pagination{Limit: -1, Offset: -1, Sort: "tb_agent_heartbeat.updated_at desc"}, dto.GetAgentListReq{Status: 0})
	if err != nil {
		global.SysLog.Sugar().Errorf("GetAgents failed. err=%v", err)
		return []dto.AgentResp{}, err
	}
	agents, ok := allAgents.Rows.(*[]dto.AgentResp)
	if !ok {
		global.SysLog.Sugar().Error("format agents failed.")
		return []dto.AgentResp{}, normErrors.New("format agents failed")
	}
	if agents == nil {
		global.SysLog.Sugar().Info("no agents")
		return []dto.AgentResp{}, nil
	}
	return *agents, nil
}

func (a *appService) GetAgentIDsOfGroup(ctx context.Context, groupId string) ([]int64, error) {
	// TODO 这里先直接用模块开关的service层接口，后续重新设计时这个接口时这里也需要修改
	service := moduleService.GetSwitchService()
	if groupId == moduleService.UnbindGroup { // 未绑定终端
		unbindAgents, err := service.GetUnbindAgent(ctx, "", common.AllPlatform)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetUnbindAgent failed. err=%v", err)
			return []int64{}, err
		}
		var unbindAgentIds []int64
		for _, agent := range unbindAgents {
			unbindAgentIds = append(unbindAgentIds, int64(agent.ApplianceId))
		}
		return unbindAgentIds, nil
	}
	groupName, err := a.db.GetGroupNameByID(ctx, groupId)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetGroupNameByID failed. err=%v", err)
		return []int64{}, err
	}

	// ModuleCode 为 module switch 模块依赖，由于是必填参数，所以和前端保持一致
	groupAndAgents, err := service.GetGroupAndAgents(ctx, moduleDto.EffectUserReq{ModuleCode: 2, GroupName: groupName})
	if err != nil {
		global.SysLog.Sugar().Errorf("GetGroupAndAgents failed. err=%v", err)
		return []int64{}, err
	}
	// 上面是模糊查询，可能获取同名分组，还需要根据groupId过滤
	group, err := getModuleSwitchGroupByID(groupAndAgents, groupId)
	if err != nil {
		global.SysLog.Sugar().Errorf("getModuleSwitchGroupByID failed. err=%v groupId=%s", err, groupId)
		return []int64{}, err
	}

	// 获取分组下所有的agent_ids
	agentIDs, err := traverseAgentsFromModuleSwitchGroup([]moduleDto.GroupAndAgents{group})
	if err != nil {
		global.SysLog.Sugar().Errorf("traverseAgentsFromModuleSwitchGroup failed. err=%v", err)
		return []int64{}, err
	}
	return agentIDs, nil
}

func getModuleSwitchGroupByID(moduleSwitchGroups []moduleDto.GroupAndAgents, groupId string) (moduleDto.GroupAndAgents, error) {
	if len(moduleSwitchGroups) == 0 {
		return moduleDto.GroupAndAgents{}, common.ErrRecordNotFound
	}
	for _, g := range moduleSwitchGroups {
		if !g.IsDir {
			continue
		}
		if g.Id == groupId {
			return g, nil
		}
		result, err := getModuleSwitchGroupByID(g.Children, groupId)
		if err != nil {
			continue
		}
		return result, nil
	}
	return moduleDto.GroupAndAgents{}, common.ErrRecordNotFound
}

func traverseAgentsFromModuleSwitchGroup(moduleSwitchGroups []moduleDto.GroupAndAgents) ([]int64, error) {
	var result []int64
	if len(moduleSwitchGroups) == 0 {
		return result, nil
	}
	for _, g := range moduleSwitchGroups {
		if g.IsDir {
			subAgents, err := traverseAgentsFromModuleSwitchGroup(g.Children)
			if err != nil {
				global.SysLog.Sugar().Errorf("traverseAgentsFromModuleSwitchGroup failed. err=%v", err)
				return []int64{}, err
			}
			result = append(result, subAgents...)
		} else {
			agentID, err := strconv.ParseInt(g.Id, 10, 64)
			if err != nil {
				global.SysLog.Sugar().Errorf("GetGroupAndAgents failed. err=%v", err)
				return []int64{}, err
			}
			result = append(result, agentID)
		}
	}
	return result, nil
}

func (a *appService) UpdateAppliance(ctx context.Context, req dto.UpdateApplianceInstallReq) error {
	applianceId, err := strconv.ParseUint(req.ApplianceId, 10, 64)
	if err != nil {
		return err
	}

	appliance, err := a.GetById(ctx, applianceId)
	if err != nil {
		// 如果获取失败，可能还没安装，忽略错误继续更新安装命令
		global.SysLog.Debug("get appliance by id failed, maybe not installed yet: " + err.Error())
	}
	//已经安装，tb_appliance有记录
	if appliance.ApplianceId > 0 {
		// 避免空值覆盖：只更新非空字段
		if req.Name != "" {
			appliance.AppName = req.Name
		}
		if req.Desc != "" {
			appliance.Desc = req.Desc
		}
		if req.GatewayIP != "" {
			appliance.SeIp = req.GatewayIP
		}
		if req.GatewayPort != 0 {
			appliance.SePort = int(req.GatewayPort)
		}
		if req.GatewayProtocol != "" {
			appliance.Protocol = req.GatewayProtocol
		}
		err := a.db.UpdateAppliance(ctx, appliance)
		if err != nil {
			return err
		}
	}
	//更新安装命令
	err = a.db.UpdateInstallCMD(ctx, applianceId, req.Name, req.Desc, req.GatewayIP, req.GatewayPort, req.GatewayLocalIP, req.PlatformDomain)
	return err
}

func (a *appService) DeviceCount(ctx context.Context) ([]dto.DeviceCount, error) {
	counts, err := a.db.DeviceCount(ctx)

	//判断对应系统类型客户端是否存在,不存在则填充0
	winExist := utils.SliceExist(counts, func(item dto.DeviceCount) bool {
		return item.PlatType == common.WindowsPlatformType
	})

	if !winExist {
		counts = append(counts, dto.DeviceCount{
			Count:    0,
			PlatType: common.WindowsPlatformType,
		})
	}

	macExist := utils.SliceExist(counts, func(item dto.DeviceCount) bool {
		return item.PlatType == common.MacPlatformType
	})
	if !macExist {
		counts = append(counts, dto.DeviceCount{
			Count:    0,
			PlatType: common.MacPlatformType,
		})
	}

	linuxExist := utils.SliceExist(counts, func(item dto.DeviceCount) bool {
		return item.PlatType == common.LinuxPlatformType
	})
	if !linuxExist {
		counts = append(counts, dto.DeviceCount{
			Count:    0,
			PlatType: common.LinuxPlatformType,
		})
	}

	androidExist := utils.SliceExist(counts, func(item dto.DeviceCount) bool {
		return item.PlatType == common.AndroidPlatformType
	})
	if !androidExist {
		counts = append(counts, dto.DeviceCount{
			Count:    0,
			PlatType: common.AndroidPlatformType,
		})
	}

	iosExist := utils.SliceExist(counts, func(item dto.DeviceCount) bool {
		return item.PlatType == common.IosPlatformType
	})
	if !iosExist {
		counts = append(counts, dto.DeviceCount{
			Count:    0,
			PlatType: common.IosPlatformType,
		})
	}

	return counts, err
}

func (a *appService) Delete(ctx context.Context, applianceId uint64) error {

	relations, err := a.CheckAppQuote(ctx, applianceId)
	if err != nil {
		return err
	}
	if len(relations) > 0 {
		names := strings.Join(relations, " ")
		errMsg := fmt.Sprintf("无法删除设备,设备依然关联在应用%s中", names)
		return &errors.WrappedError{
			Info: errMsg,
		}
	}

	return a.db.DeleteById(ctx, applianceId)
}

func (a *appService) CheckAppQuote(ctx context.Context, applianceId uint64) ([]string, error) {
	//检测在应用中绑定关系
	relations, err := a.db.GetSEAppRelation(ctx, applianceId)
	if err != nil {
		return nil, err
	}
	if len(relations) > 0 {
		var names []string
		for _, relation := range relations {
			if relation.AppName != "" {
				names = append(names, relation.AppName)
			}
		}
		return names, nil
	}
	return nil, nil
}

func (a *appService) GetAppliances(ctx context.Context, req model.Pagination) (model.Pagination, error) {
	return a.db.GeAppliances(ctx, req)
}

// const LinuxCMDFormatter = "wget \"https://asec-public.oss-cn-hangzhou.aliyuncs.com/gateway/%s/gateway.tar.gz\" && "+
//
//	"tar -zxf gateway.tar.gz && cd gateway && chmod +x env.sh && chmod +x %s && ./env.sh -o $(pwd)/asec_env.sh && %s -e asec_env.sh"
const LinuxCMDFormatter = "wget --no-check-certificate \"https://%s/console/v1/gateway/gateway.tar.gz\" && mkdir -p /opt/gateway && " +
	"tar -zxf gateway.tar.gz -C /opt/gateway --strip-components=1 && cd /opt/gateway && chmod +x env.sh && chmod +x %s && ./env.sh -o $(pwd)/asec_env.sh && %s -e asec_env.sh"

const WindowsCMDFormatter = "& $([scriptblock]::Create((New-Object System.Net.WebClient).DownloadString('%s/console/archives/latest/connector_install_windows.ps1'))) -CODE %d"
const DefaultValidDays = 7

func getPlatformAddress() (string, error) {
	cfg, err := ini.Load(common.ConfigPath)
	if err != nil {
		return "", fmt.Errorf("failed to load config file: %v", err)
	}

	// 从default section获取plat_ip和user_port
	section := cfg.Section("default")
	platIP := section.Key("plat_ip").String()
	userPort := section.Key("user_port").String()

	if platIP == "" || userPort == "" {
		return "", fmt.Errorf("plat_ip or user_port not found in config")
	}

	// 组合成ip:port格式
	return fmt.Sprintf("%s:%s", platIP, userPort), nil
}

func (a *appService) GenInstallCMD(ctx context.Context, req dto.ApplianceInstallReq, host string) (model.ApplianceInstall, error) {
	cmd, err := a.db.GetInstallCMD(ctx, req.Name)
	//查询出错,直接返回,避免重复生成
	if err != nil {
		return cmd, err
	}
	// 如果查询出已经有有效的安装命令
	if cmd.ApplianceID > 0 {
		return cmd, nil
	}

	//生成新的安装命令
	if req.ValidDays <= 0 {
		req.ValidDays = DefaultValidDays
	}
	// sysInfo, err := a.db.GetSystemVersionByName(ctx, "platform")
	// if err != nil {
	// 	return cmd, err
	// }

	//2022/12/2 update 直接生成appliance id
	//因为新增应用可以选择已生成命令但是未安装的connector和网关，所以这里选择生成命令就直接生成appliance id
	applianceId, err := snowflake.Sf.GetId()
	if err != nil {
		return cmd, err
	}

	var command string
	var script string //执行的脚本名称

	// 获取平台地址信息
	address, err := getPlatformAddress()
	if err != nil {
		// 使用项目的zap日志
		global.SysLog.Error("获取平台地址失败", zap.Error(err))
		address = "127.0.0.1:443"
	}

	// 提取平台IP（不带端口）用于 -p 参数
	platformIP := strings.Split(address, ":")[0]

	if req.Type == 2 {
		command = fmt.Sprintf("./install-connector.sh -s %s -p %s -c %d", host, host, applianceId)
		script = "install-connector.sh"
	} else {
		// 对于网关类型，网关本地IP是必选的
		if req.Type == 3 && req.GatewayLocalIP == "" {
			return model.ApplianceInstall{}, fmt.Errorf("缺少网关本地IP参数")
		}

		// 构建 install.sh 命令，使用平台IP（不带端口）作为 -p 参数
		baseCommand := fmt.Sprintf("./install.sh -m gateway -s run -p %s -c %d", platformIP, applianceId)
		if req.GatewayLocalIP != "" {
			baseCommand += fmt.Sprintf(" -g %s", req.GatewayLocalIP)
		}
		if req.PlatformDomain != "" {
			baseCommand += fmt.Sprintf(" -d %s", req.PlatformDomain)
		}
		command = baseCommand
		script = "install.sh"
	}

	// if len(sysInfo.Version) < 2 {
	// 	return model.ApplianceInstall{}, fmt.Errorf("get incorrect system version. system-version=%s", sysInfo.Version)
	// }
	cmd = model.ApplianceInstall{
		ApplianceID: applianceId,
		CorpId:      0, //TODO
		Name:        req.Name,
		Type:        req.Type,
		Desc:        req.Desc,
		Platform:    req.Platform,
		CommandLinux: fmt.Sprintf(
			LinuxCMDFormatter,
			address,
			//sysInfo.Version,
			script,
			command,
		),
		CommandWindows:  fmt.Sprintf(WindowsCMDFormatter, host, applianceId),
		ExpireTime:      time.Now().Add(time.Duration(req.ValidDays) * 24 * time.Hour),
		GatewayIP:       req.GatewayIP,
		GatewayPort:     req.GatewayPort,
		GatewayProtocol: req.GatewayProtocol,
		GatewayLocalIP:  req.GatewayLocalIP,
		PlatformDomain:  req.PlatformDomain,
	}
	return a.db.CreateInstallCMD(ctx, cmd)
}

func (a *appService) GetById(ctx context.Context, id uint64) (model.Appliance, error) {
	return a.db.GetById(ctx, id)
}

func (a *appService) GetAppList(ctx context.Context, pagination model.Pagination, req dto.GetAgentListReq) (dto.GetAgentListRsp, error) {
	res, err := a.db.GetAgents(ctx, pagination, req)
	if err != nil {
		return dto.GetAgentListRsp{}, err
	}
	countRsp, err := a.db.GetAgentCount(ctx, pagination)
	if err != nil {
		return dto.GetAgentListRsp{}, err
	}
	result := dto.GetAgentListRsp{
		AgentCountRsp: countRsp,
		Pagination:    res,
	}
	return result, nil
}

func (a *appService) GetAppListByID(ctx context.Context, ids []string) ([]dto.AgentResp, error) {
	return a.db.GetAppListByID(ctx, ids)
}

// GetAgentIdsByUserIds 通过 user_id 列表获取对应的 agent_id 列表
func (a *appService) GetAgentIdsByUserIds(ctx context.Context, userIds []string) ([]int64, error) {
	if len(userIds) == 0 {
		return []int64{}, nil
	}

	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetDBClient failed. err=%v", err)
		return []int64{}, err
	}

	var agentIds []int64
	err = db.Model(model.AgentHeartbeat{}).Select("agent_id").
		Where("user_id in (?)", userIds).Find(&agentIds).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Sugar().Errorf("get agent_ids by user_ids failed. err=%v", err)
		return []int64{}, err
	}

	return agentIds, nil
}

// GetAgentIdsByGroupIds 通过 group_id 列表获取对应的 agent_id 列表
func (a *appService) GetAgentIdsByGroupIds(ctx context.Context, groupIds []string) ([]int64, error) {
	if len(groupIds) == 0 {
		return []int64{}, nil
	}

	// 1. 通过 group_id 获取所有 user_id（包括子分组）
	userIds, err := commonApi.GetUserIdByGroups(ctx, groupIds)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetUserIdByGroups failed. err=%v", err)
		return []int64{}, err
	}

	if len(userIds) == 0 {
		return []int64{}, nil
	}

	// 2. 通过 user_id 获取 agent_id
	agentIds, err := a.GetAgentIdsByUserIds(ctx, userIds)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetAgentIdsByUserIds failed. err=%v", err)
		return []int64{}, err
	}

	return agentIds, nil
}

// CheckAndUpdateGrayAgents 检查并更新灰度代理列表
func (a *appService) CheckAndUpdateGrayAgents(ctx context.Context) error {
	// 获取所有启用灰度的升级策略
	policies, err := a.db.GetEnabledGrayPolicies(ctx)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetEnabledGrayPolicies failed. err=%v", err)
		return err
	}

	for _, policy := range policies {
		if policy.GrayPolicyID == "" {
			continue // 没有灰度策略，跳过
		}

		// 获取灰度策略详情
		grayPolicy, err := a.db.GetGrayPolicyByID(ctx, policy.GrayPolicyID)
		if err != nil {
			global.SysLog.Sugar().Errorf("GetGrayPolicyByID failed. err=%v, policyID=%s", err, policy.GrayPolicyID)
			continue
		}

		// 检查灰度策略是否为自定义模式
		if grayPolicy.Mode != common.GrayModeCustom {
			continue // 只处理自定义模式的灰度策略
		}

		// 重新计算应该包含的 agent_ids
		newAgentIds, err := a.calculateGrayAgentIds(ctx, grayPolicy)
		if err != nil {
			global.SysLog.Sugar().Errorf("calculateGrayAgentIds failed. err=%v, policyID=%s", err, policy.ID)
			continue
		}

		// 比较新旧 agent_ids，如果有变化则更新
		if !a.isAgentIdsEqual(grayPolicy.AgentIds, newAgentIds) {
			global.SysLog.Sugar().Infof("Gray agents changed for policy %s, updating from %v to %v",
				policy.ID, grayPolicy.AgentIds, newAgentIds)

			// 更新灰度策略中的 agent_ids
			grayPolicy.AgentIds = newAgentIds
			if err := a.db.UpdateGrayPolicy(ctx, grayPolicy); err != nil {
				global.SysLog.Sugar().Errorf("UpdateGrayPolicy failed. err=%v, policyID=%s", err, policy.ID)
				continue
			}

			// 为新增的 agent 创建灰度任务
			if err := a.createGrayTasksForNewAgents(ctx, policy, grayPolicy.AgentIds); err != nil {
				global.SysLog.Sugar().Errorf("createGrayTasksForNewAgents failed. err=%v, policyID=%s", err, policy.ID)
				continue
			}
		}
	}

	return nil
}

// calculateGrayAgentIds 重新计算灰度策略应该包含的 agent_ids
func (a *appService) calculateGrayAgentIds(ctx context.Context, grayPolicy dto.TbAgentUpgradeGrayPolicy) ([]int64, error) {
	var allAgentIds []int64

	// 1. 处理 GroupIds
	if len(grayPolicy.GroupIds) > 0 {
		groupAgentIds, err := a.GetAgentIdsByGroupIds(ctx, grayPolicy.GroupIds)
		if err != nil {
			return nil, err
		}
		allAgentIds = append(allAgentIds, groupAgentIds...)
	}

	// 2. 处理 UserIds
	if len(grayPolicy.UserIds) > 0 {
		userAgentIds, err := a.GetAgentIdsByUserIds(ctx, grayPolicy.UserIds)
		if err != nil {
			return nil, err
		}
		allAgentIds = append(allAgentIds, userAgentIds...)
	}

	// 3. 处理直接指定的 AgentIds（这部分通常不变，但为了完整性也包含）
	// 注意：这里我们不包含原有的 AgentIds，因为我们要重新计算所有的
	// 如果需要保留原有的直接指定的 AgentIds，需要额外的逻辑来区分

	// 去重处理
	deduplicatedAgentIds := common.Deduplication(allAgentIds)
	return deduplicatedAgentIds, nil
}

// isAgentIdsEqual 比较两个 agent_ids 切片是否相等
func (a *appService) isAgentIdsEqual(oldIds, newIds []int64) bool {
	if len(oldIds) != len(newIds) {
		return false
	}

	// 创建 map 用于快速查找
	oldMap := make(map[int64]bool)
	for _, id := range oldIds {
		oldMap[id] = true
	}

	// 检查新的 ids 是否都在旧的 map 中
	for _, id := range newIds {
		if !oldMap[id] {
			return false
		}
	}

	return true
}

// createGrayTasksForNewAgents 为新增的 agent 创建灰度任务
func (a *appService) createGrayTasksForNewAgents(ctx context.Context, policy dto.TbAgentUpgradePolicy, newAgentIds []int64) error {
	// 获取已存在的灰度任务
	existingTasks, err := a.db.GetGrayTasksByPolicyID(ctx, policy.ID)
	if err != nil {
		return err
	}

	// 创建已存在任务的 agent_id 映射
	existingAgentIds := make(map[int64]bool)
	for _, task := range existingTasks {
		existingAgentIds[task.AgentID] = true
	}

	// 找出需要创建任务的新 agent_ids
	var needCreateAgentIds []int64
	for _, agentId := range newAgentIds {
		if !existingAgentIds[agentId] {
			needCreateAgentIds = append(needCreateAgentIds, agentId)
		}
	}

	if len(needCreateAgentIds) == 0 {
		return nil // 没有新的 agent 需要创建任务
	}

	// 构建灰度任务列表
	var tasks []dto.TbAgentUpgradeLog
	for _, agentId := range needCreateAgentIds {
		id, err := snowflake.Sf.GetIdInt64()
		if err != nil {
			return err
		}
		task := dto.TbAgentUpgradeLog{
			ID:          id,
			PolicyID:    policy.ID,
			AgentID:     agentId,
			LastVersion: common.VersionUnknown,
			NextVersion: policy.LatestVersion,
			Status:      common.UpdateLogPending,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		tasks = append(tasks, task)
	}

	// 批量创建灰度任务
	return a.db.BatchCreateGrayTask(ctx, tasks)
}

func GetAppService() AppService {
	AppServiceInit.Do(func() {
		AppServiceImpl = &appService{db: repository.NewAppRepository()}
	})
	return AppServiceImpl
}
