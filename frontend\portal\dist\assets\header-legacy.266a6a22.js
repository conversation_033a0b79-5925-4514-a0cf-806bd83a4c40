/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,o,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function l(e,r,i,a){var l=r&&r.prototype instanceof c?r:c,s=Object.create(l.prototype);return t(s,"_invoke",function(e,t,r){var i,a,l,c=0,s=r||[],u=!1,p={p:0,n:0,v:n,a:g,f:g.bind(n,4),d:function(e,t){return i=e,a=0,l=n,p.n=t,d}};function g(e,t){for(a=e,l=t,o=0;!u&&c&&!r&&o<s.length;o++){var r,i=s[o],g=p.p,f=i[2];e>3?(r=f===t)&&(l=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=n):i[0]<=g&&((r=e<2&&g<i[1])?(a=0,p.v=t,p.n=i[1]):g<f&&(r=e<3||i[0]>t||t>f)&&(i[4]=e,i[5]=t,p.n=f,a=0))}if(r||e>1)return d;throw u=!0,t}return function(r,s,f){if(c>1)throw TypeError("Generator is already running");for(u&&1===s&&g(s,f),a=s,l=f;(o=a<2?n:l)||!u;){i||(a?a<3?(a>1&&(p.n=-1),g(a,l)):p.n=l:p.v=l);try{if(c=2,i){if(a||(r="next"),o=i[r]){if(!(o=o.call(i,l)))throw TypeError("iterator result is not an object");if(!o.done)return o;l=o.value,a<2&&(a=0)}else 1===a&&(o=i.return)&&o.call(i),a<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),a=1);i=n}else if((o=(u=p.n<0)?l:e.call(t,p))!==d)break}catch(o){i=n,a=1,l=o}finally{c=1}}return{value:o,done:u}}}(e,i,a),!0),s}var d={};function c(){}function s(){}function u(){}o=Object.getPrototypeOf;var p=[][i]?o(o([][i]())):(t(o={},i,(function(){return this})),o),g=u.prototype=c.prototype=Object.create(p);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,t(e,a,"GeneratorFunction")),e.prototype=Object.create(g),e}return s.prototype=u,t(g,"constructor",u),t(u,"constructor",s),s.displayName="GeneratorFunction",t(u,a,"GeneratorFunction"),t(g),t(g,a,"Generator"),t(g,i,(function(){return this})),t(g,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:f}})()}function t(e,n,o,r){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}t=function(e,n,o,r){if(n)i?i(e,n,{value:o,enumerable:!r,configurable:!r,writable:!r}):e[n]=o;else{var a=function(n,o){t(e,n,(function(e){return this._invoke(n,o,e)}))};a("next",0),a("throw",1),a("return",2)}},t(e,n,o,r)}function n(e,t,n,o,r,i,a){try{var l=e[i](a),d=l.value}catch(e){return void n(e)}l.done?t(d):Promise.resolve(d).then(o,r)}function o(e){return function(){var t=this,o=arguments;return new Promise((function(r,i){var a=e.apply(t,o);function l(e){n(a,r,i,l,d,"next",e)}function d(e){n(a,r,i,l,d,"throw",e)}l(void 0)}))}}System.register(["./index-legacy.b871e767.js","./logo-legacy.17ee3a24.js"],(function(t,n){"use strict";var r,i,a,l,d,c,s,u,p,g,f,h,m,w,x,v,y,b,A=document.createElement("style");return A.textContent='@charset "UTF-8";.layout-header[data-v-515e76c2]{height:42px;display:flex;justify-content:space-between;align-items:center;background:linear-gradient(315deg,#536CE6,#647be9);box-shadow:0 2px 6px rgba(46,60,128,.2);color:#fff}.layout-header .header-title[data-v-515e76c2]{line-height:42px;font-size:18px;font-weight:500}.layout-header .header-logo[data-v-515e76c2]{margin-left:16px;height:42px;display:flex;align-items:center}.layout-header .header-logo img[data-v-515e76c2]{max-width:79px;max-height:28px}.layout-header #u-electron-drag[data-v-515e76c2]{display:flex;flex:1;height:100%;-webkit-app-region:drag}.layout-header .right-wrapper[data-v-515e76c2]{display:flex;align-items:center;height:100%}.layout-header .right-wrapper>li[data-v-515e76c2]:hover{background:#4256b8}.layout-header .right-wrapper .user-divider[data-v-515e76c2]{width:1px;height:14px;margin-left:16px;margin-right:16px;background:#e6e6e6}.layout-header .right-wrapper .base-dropdown[data-v-515e76c2]{position:relative;display:inline-block}.layout-header .right-wrapper .user-info[data-v-515e76c2]{display:flex;align-items:center;height:42px;padding:0 14px;cursor:pointer}.layout-header .right-wrapper .user-info.not-logged-in[data-v-515e76c2]{cursor:default;opacity:.8}.layout-header .right-wrapper .user-info.not-logged-in[data-v-515e76c2]:hover{background:none!important}.layout-header .right-wrapper .user-info .user-face[data-v-515e76c2]{width:32px;height:32px;border-radius:50%;overflow:hidden;margin-right:6px}.layout-header .right-wrapper .user-info .user-face img[data-v-515e76c2]{width:100%;height:100%;display:block}.layout-header .right-wrapper .user-info .user-name[data-v-515e76c2]{color:#fff;display:inline-block;max-width:100px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;word-break:break-all}.layout-header .right-wrapper .dropdown-menu[data-v-515e76c2]{position:absolute;top:100%;z-index:1000;width:114px;background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1)}.layout-header .right-wrapper .dropdown-menu .dropdown-item[data-v-515e76c2]{display:flex;padding-left:40px;position:relative;align-items:center;justify-content:flex-start;width:98px;height:28px;margin:8px;border-radius:4px;font-size:14px;color:#282a33;cursor:pointer;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:Regular}.layout-header .right-wrapper .dropdown-menu .dropdown-item .dropdown-item-icon[data-v-515e76c2]{position:absolute;transform:rotate(180deg);font-size:14px;display:flex;align-items:center;width:14px;height:14px;top:6px;left:10px}.layout-header .right-wrapper .dropdown-menu .dropdown-item .dropdown-item-text[data-v-515e76c2]{width:56px;height:20px;position:absolute;right:8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:Regular!important;text-align:left;line-height:20px}.layout-header .right-wrapper .dropdown-menu .dropdown-item[data-v-515e76c2]:hover{background-color:#ff4d4d;color:#fff}.layout-header .right-wrapper .dropdown-menu .dropdown-item[data-v-515e76c2]:active{background-color:#e6e8eb}.layout-header .right-wrapper .set-icon-wrapper[data-v-515e76c2],.layout-header .right-wrapper .menu-msg[data-v-515e76c2]{width:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;height:42px;position:relative}.layout-header .right-wrapper .set-icon-wrapper .icon-shezhi[data-v-515e76c2],.layout-header .right-wrapper .menu-msg .icon-shezhi[data-v-515e76c2]{color:#bac4f5;font-size:18px}.layout-header .right-wrapper .window-operate[data-v-515e76c2]{width:24px;height:24px;margin-left:4px;padding-top:4px;filter:brightness(1.5);display:flex;align-items:center;justify-content:center;cursor:pointer}.layout-header .right-wrapper .window-operate[data-v-515e76c2]:hover{background:#4256b8;border-radius:4px}.layout-header .right-wrapper .window-operate svg[data-v-515e76c2]{width:12px;height:12px}.count-title[data-v-515e76c2]{color:#686e84}.count-title i[data-v-515e76c2]{font-style:normal;color:#3c404d}.s-title[data-v-515e76c2]{margin-top:18px;margin-left:18px;font-size:13px;line-height:18px;font-weight:500;color:#3c404d}.s-content[data-v-515e76c2]{padding:24px 32px 29px;font-size:13px;line-height:18px}.s-content .s-text[data-v-515e76c2]{color:#686e84}.change-reg-info[data-v-515e76c2]{padding-left:8px;line-height:20px;font-size:14px;font-weight:500;color:#3c404d}body .el-dialog-ip-box{width:260px}body .el-dialog-ip-box .el-message-box__content{padding:20px 15px}.s-content .el-radio{margin-right:13px}.s-content .el-radio .el-radio__label{padding-left:8px;font-size:13px;color:#3c404d;line-height:18px}#ip-info-dialog .ip-content{margin-top:24px;margin-bottom:24px;padding:0 24px;line-height:20px;font-size:14px;color:#3c404d}#ip-info-dialog .netcard-list{margin-top:16px;padding:0 24px}#ip-info-dialog .netcard-list li{display:flex;align-items:center;line-height:20px;font-size:14px;color:#3c404d;margin-bottom:10px}#ip-info-dialog .netcard-list li:last-child{margin-bottom:24px}#ip-info-dialog .netcard-list li i{font-size:16px;margin-left:16px}#ip-info-dialog .netcard-list li .icon-lianjie{color:#29cc88}#ip-info-dialog .netcard-list li .icon-duankailianjie{color:#e65353}#ip-info-dialog .el-dialog__footer button{height:40px;line-height:40px;border-bottom-right-radius:4px}.loginout-m-confirm-dialog .v-header{line-height:45px;border-bottom:1px solid #EDEDF1;padding:0 24px;font-size:16px;color:#3c404d}.loginout-m-confirm-dialog .v-header i{font-size:16px;color:#ffbf00;margin-right:6px;font-weight:400}.loginout-m-confirm-dialog .outline-tips{padding:24px;line-height:20px;color:#3c404d;font-size:14px}\n',document.head.appendChild(A),{setters:[function(e){r=e._,i=e.f,a=e.E,l=e.C,d=e.G,c=e.L,s=e.M,u=e.h,p=e.H,g=e.a,f=e.b,h=e.d,m=e.I,w=e.e,x=e.t,v=e.j,y=e.J},function(e){b=e.l}],execute:function(){var A=""+new URL("avator.bd83723a.png",n.meta.url).href,C={name:"ClientHeader",setup:function(){return{userStore:i(),router:a()}},data:function(){return{countCommand:"changePassword",isMaxWindow:!1,dropdownVisible:!1}},computed:{isLoggedIn:function(){return!!this.userStore.token},userAvatar:function(){return this.isLoggedIn?A:"data:image/png;base64,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"},displayUsername:function(){if(this.isLoggedIn){var e=this.userStore.userInfo;return e.displayName?e.displayName:e.name}return"未登录"}},watch:{userId:function(e,t){logger.log("用户id变动",e,t),console.debug("用户id变动")}},mounted:function(){this.setupClientLogoutListener()},beforeDestroy:function(){this.removeClientLogoutListener()},methods:{minimizeWnd:function(){l.minimizeWnd()},maximizeWndOrNot:function(){this.isMaxWindow?(l.normalnizeWnd(),this.isMaxWindow=!1):(l.maximizeWnd(),this.isMaxWindow=!0)},handleUserInfoClick:function(){this.isLoggedIn&&this.toggleDropdown()},toggleDropdown:function(){this.dropdownVisible=!this.dropdownVisible},closeDropdown:function(){this.dropdownVisible=!1},dropdownVisiHandle:function(){},closeWnd:function(){return o(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:l.hideWend();case 1:return e.a(2)}}),t)})))()},userMenuHandle:function(e){if(this.closeDropdown(),this.countCommand=e,"lougOut"===e)this.handleLogoutConfirm()},handleLogoutConfirm:function(){var t=this;return o(e().m((function n(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,d.confirm("确认注销登录吗？","提示",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"});case 1:return e.n=2,t.performLogout();case 2:e.n=4;break;case 3:e.p=3,e.v,logger.log("用户取消注销操作");case 4:return e.a(2)}}),n,null,[[0,3]])})))()},performLogout:function(){var t=this;return o(e().m((function n(){var o,r,i;return e().w((function(e){for(;;)switch(e.n){case 0:return logger.log("开始注销，显示Loading"),o=c.service({fullscreen:!0,text:"正在注销登录..."}),e.p=1,logger.log("开始注销登录..."),logger.log("正在断开隧道连接..."),l.disconnectTunnel(),t.userStore.setTunState(0),logger.log("断开连接API调用成功，开始轮询状态..."),logger.log("隧道连接断开完成，继续注销流程"),l.setLoginStatus({Token:""}),e.p=2,e.n=3,t.userStore.ClearStorage();case 3:t.userStore.LoginOut(),logger.log("服务器注销登录成功"),e.n=5;break;case 4:e.p=4,r=e.v,console.error("服务器注销登录失败:",r);case 5:t.router.push({name:"ClientNewLogin",query:l.getClientParams()}),e.n=7;break;case 6:e.p=6,i=e.v,console.error("注销登录失败:",i),s.error("注销失败，请重试");case 7:return e.p=7,o.close(),e.f(7);case 8:return e.a(2)}}),n,null,[[2,4],[1,6,7,8]])})))()},waitForDisconnect:function(){return o(e().m((function t(){return e().w((function(t){for(;;)if(0===t.n)return t.a(2,new Promise((function(t){var n=0,r=setInterval(o(e().m((function o(){var i,a;return e().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,n++,logger.log("断开连接状态轮询第".concat(n,"次...")),e.n=1,l.getChannelStatus();case 1:if(i=e.v,logger.log("轮询状态响应:",i),!i||102!==i.TunState){e.n=2;break}return logger.log("断开连接成功！"),clearInterval(r),t(!0),e.a(2);case 2:n>=10&&(logger.log("断开连接超时"),clearInterval(r),t(!1)),e.n=4;break;case 3:e.p=3,a=e.v,console.error("轮询状态失败:",a),++n>=10&&(clearInterval(r),t(!1));case 4:return e.a(2)}}),o,null,[[0,3]])}))),1e3)})))}),t)})))()},getCountMenuWidth:function(){var t=this;return o(e().m((function n(){var o,r,i;return e().w((function(e){for(;;)switch(e.n){case 0:return o=t.isZtpUser?44:0,r=parseInt(document.getElementById("u-avator")?document.getElementById("u-avator").offsetWidth:0),e.p=1,e.n=2,l.init();case 2:return e.n=3,l.ipcClient.$ipcSend("UIPlatform_Window","SetTitleDimension",{nHeight:50,nNameWidth:parseFloat(r)+o});case 3:e.n=5;break;case 4:e.p=4,i=e.v,console.warn("设置标题尺寸失败:",i);case 5:return e.a(2)}}),n,null,[[1,4]])})))()},hdEventHandle:function(e){if("router"===e.type)this.userMenuHandle(e.val)},setupClientLogoutListener:function(){var t=this;"undefined"!=typeof window&&(this.clientLogoutHandler=function(){var n=o(e().m((function n(o){var r;return e().w((function(e){for(;;)switch(e.n){case 0:if(logger.log("收到客户端退出登录事件:",o.detail),t.isLoggedIn){e.n=1;break}return logger.log("用户未登录，跳过退出登录处理"),e.a(2);case 1:return logger.log("客户端已登录，开始执行退出登录流程"),e.p=2,e.n=3,t.performLogout();case 3:e.n=5;break;case 4:e.p=4,r=e.v,logger.log("处理客户端退出登录事件失败:",r);case 5:return e.a(2)}}),n,null,[[2,4]])})));return function(e){return n.apply(this,arguments)}}(),window.addEventListener("clientLogoutReceived",this.clientLogoutHandler),logger.log("已注册客户端退出登录事件监听器"))},removeClientLogoutListener:function(){"undefined"!=typeof window&&this.clientLogoutHandler&&(window.removeEventListener("clientLogoutReceived",this.clientLogoutHandler),this.clientLogoutHandler=null,logger.log("已移除客户端退出登录事件监听器"))}}},k={class:"layout-header"},z={class:"header-logo"},L={src:b,alt:"",draggable:"false",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none'"},I={id:"u-header-menu",class:"right-wrapper"},S={id:"u-avator",ref:"countMenu"},M={class:"user-face"},R=["src"],W={class:"user-name"},j={class:"dropdown-menu header-count-menu"};t("default",r(C,[["render",function(e,t,n,o,r,i){var a=u("base-icon"),l=p("prevent-drag"),d=p("click-outside");return g(),f("div",k,[h("div",z,[m(h("img",L,null,512),[[l]])]),t[4]||(t[4]=h("div",{id:"u-electron-drag"},null,-1)),h("ul",I,[h("li",S,[m((g(),f("div",{id:"ui-headNav-header-div-account_info",class:"base-dropdown",onClick:t[1]||(t[1]=function(){return i.handleUserInfoClick&&i.handleUserInfoClick.apply(i,arguments)})},[h("div",{class:w(["user-info",{"not-logged-in":!i.isLoggedIn}])},[h("div",M,[m(h("img",{src:i.userAvatar,alt:"",draggable:"false",onload:"this.style.display = 'block'",onerror:"this.style.display = 'none'"},null,8,R),[[l]])]),h("span",W,x(i.displayUsername),1)],2),m(h("div",j,[h("div",{id:"ui-headNav-header-li-cancel_account",class:"dropdown-item",onClick:t[0]||(t[0]=function(e){return i.userMenuHandle("lougOut")})},[v(a,{class:"dropdown-item-icon",name:"logout"}),t[2]||(t[2]=h("span",{class:"dropdown-item-text"},"注销登录",-1))])],512),[[y,r.dropdownVisible&&i.isLoggedIn]])])),[[d,i.closeDropdown]])],512),t[3]||(t[3]=h("div",{class:"user-divider"},null,-1)),v(a,{class:"window-operate",name:"minus",onClick:i.minimizeWnd},null,8,["onClick"]),v(a,{class:"window-operate",name:r.isMaxWindow?"fullscreen_exit":"fullscreen",onClick:i.maximizeWndOrNot},null,8,["name","onClick"]),v(a,{class:"window-operate",name:"close",style:{"margin-right":"16px"},onClick:i.closeWnd},null,8,["onClick"])])])}],["__scopeId","data-v-515e76c2"]]))}}}))}();
