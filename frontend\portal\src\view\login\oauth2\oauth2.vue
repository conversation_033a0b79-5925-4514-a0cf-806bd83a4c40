<template>
  <div>
    <div v-if="isForceBrowser" class="sso-warpper">
      <span class="sso-title">
        <svg aria-hidden="true" class="icon">
          <use :xlink:href="'#icon-auth-'+ authInfo.authType" />
        </svg>
      </span>
      <base-button
        class="login_submit_button"
        type="primary"
        @click="clickSubmit"
      >授权登录</base-button>
    </div>
    <div v-else class="sso-warpper">
      <iframe :src="iframeSrc" class="sso-iframe" frameborder="0" />
      <iframe :src="callbackIframeSrc" class="sso-callback-iframe" />
    </div>
  </div>
</template>
<script setup>
import { useRoute } from 'vue-router'
import agentApi from '@/api/agentApi'
import { getCurrentHost } from '@/utils/request'
import { useUserStore } from '@/pinia/modules/user'
import { generateCodeChallenge, generateRandomString } from '@/utils/crypto'
import { useSecondaryAuth } from '@/utils/secondaryAuth'
import { Message } from '@/components/base'
import { apiMessage } from '@/utils/message'

</script>
<script>

export default {
  props: {
    authId: {
      type: String,
      default: function() {
        return ''
      },
    },
    authInfo: {
      type: Object,
      default: function() {
        return {}
      },
    }
  },
  data() {
    return {
      iframeSrc: '',
      callbackIframeSrc: '',
      isListen: false,
      isThirdBack: false,
      isAutoLogin: false,
      isListenShowApp: false,
      route: useRoute(),
      userStore: useUserStore(),
      secondaryAuth: useSecondaryAuth(),
      loading: false
    }
  },
  computed: {
    /**
     * 是否强制打开浏览器认证
     */
    isForceBrowser() {
      if (this.authInfo.authType === 'cas') {
        return parseInt(this.authInfo.casOpenType) === 1
      }
      return parseInt(this.authInfo.oauth2OpenType) === 1
    },

    /**
     * 是否是OAuth2回调模式
     */
    isCallbackMode() {
      return this.oauth_callbak || this.route.query.oauth_callbak
    }
  },
  watch: {
    authId: {
      handler() {
        this.init()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 检查是否是OAuth2回调模式
    if (this.isCallbackMode) {
      this.handleOAuth2Callback()
    }
  },
  destroyed() {
    this.unListenGoBack()
    this.clearLoading()
    if (this.isListen) {
      this.removeEvent(window, 'message', this.listenHandle)
    }
  },
  methods: {
    // 检测是否在iframe中
    isInIframe() {
      try {
        return window.self !== window.top
      } catch (e) {
        return true
      }
    },
    sendMessageToParent(data) {
      try {
        logger.log('向父页面发送消息:', data)

        if (window.parent && window.parent !== window) {
          window.parent.postMessage(data, '*')
          logger.log('已向parent发送消息')
        }

        if (window.top && window.top !== window) {
          window.top.postMessage(data, '*')
          logger.log('已向top发送消息')
        }
      } catch (error) {
        console.error('发送消息失败:', error)
      }
    },
    // OAuth2回调处理
    async handleOAuth2Callback() {
      try {
        logger.log('开始处理OAuth2回调')

        // 检测是否在iframe中
        if (this.isInIframe()) {
          logger.log('在iframe中，发送消息给父窗口')
          const callbackData = {
            idp_id: this.route.query.idp_id,
            redirect: this.route.query.redirect,
            auth_token: this.route.query.auth_token,
            login_type: this.route.query.login_type,
            auth_error: this.route.query.auth_error,
            state: this.route.query.state
          }

          // 发送消息给父页面
          this.sendMessageToParent({
            type: 'oauth2_auth_callback',
            code: callbackData.auth_token,
            state: callbackData.state,
            auth_error: callbackData.auth_error,
          })
          return
        }

        // 直接处理回调
        await this.processOAuth2Callback()
      } catch (error) {
        logger.log('OAuth2回调处理失败:', error)
        Message({
          type: 'error',
          message: '认证失败，请重试',
          showClose: true,
        })
      }
    },

    // 处理OAuth2回调逻辑
    async processOAuth2Callback() {
      const authError = this.route.query.auth_error

      if (authError) {
        logger.log('OAuth2认证错误:', authError)
        const msg = apiMessage(authError)
        Message({
          type: 'error',
          message: '认证失败: ' + msg,
          showClose: true,
        })
        return
      }

      const authToken = this.route.query.auth_token

      if (!authToken) {
        logger.log('缺少认证令牌')
        Message({
          type: 'error',
          message: '认证失败: 缺少认证令牌',
          showClose: true,
        })
        return
      }

      // 构建登录参数
      const authData = {
        clientId: 'client_portal',
        grantType: 'implicit',
        redirect_uri: '',
        idpId: this.authId,
        authWeb: {
          authWebToken: authToken // 使用code作为token
        }
      }

      logger.log('调用登录接口，参数:', authData)

      // 调用登录接口
      const loginResult = await this.userStore.LoginIn(authData, 'oauth2', this.authId)
      logger.log('登录结果:', loginResult)

      if (loginResult === true || (typeof loginResult === 'object' && loginResult !== null && loginResult.code !== -1)) {
        // 双因子验证
        const needSecondaryAuth = await this.secondaryAuth.handleSecondaryAuthResponse(loginResult)
        if (needSecondaryAuth) {
          logger.log('企业微信登录成功，进入双因子验证')
          return
        }
        logger.log('OAuth2登录成功')
      } else {
        logger.log('OAuth2登录失败:', loginResult)
      }
    },

    init() {
      if (!this.isForceBrowser) {
        this.clickSubmit()
      }
    },
    async clickSubmit() {
      // 生成并存储 code_verifier
      const codeVerifier = generateRandomString()
      sessionStorage.setItem('oauth2_code_verifier', codeVerifier)
      // 计算安全加强 code_challenge
      const codeChallenge = await generateCodeChallenge(codeVerifier)
      this.submit({ code_challenge: encodeURIComponent(codeChallenge), code_challenge_method: 'S256' })
    },
    async submit(data) {
      // let authType = 'oauth2'
      // if (this.authInfo.authType === 'cas') {
      //   authType = 'cas'
      // }
      let url = getCurrentHost() + '/auth/login/v1/callback/' + this.authId
      // const state = {}
      // url后拼接data字典的参数
      if (data) {
        const urlList = []
        for (const key in data) {
          urlList.push(key + '=' + encodeURIComponent(data[key]))
        }
        url += '?' + urlList.join('&')
        if (this.route.query?.redirect) {
          const redirect = this.route.query?.redirect
          url += '&redirect=/' + encodeURIComponent(redirect)
        }
      }
      // 构建state对象
      /*
      state.type = agentApi.isClient() ? 'client' : 'web'
      state.timestamp = Date.now()
      // 将state对象转换为JSON再进行base64编码
      const stateJson = JSON.stringify(state)
      const stateBase64 = btoa(encodeURIComponent(stateJson))

      // 将state参数添加到URL中
      const separator = url.includes('?') ? '&' : '?'
      url = url + separator + 'state=' + encodeURIComponent(stateBase64)
      */
      if (this.isForceBrowser) {
        logger.log('强制浏览器授权认证URL:', url)
        if (agentApi.isClient()) {
          await agentApi.openAsecPage(url)
        } else {
          window.location.href = url
        }
      } else {
        logger.log('iframe授权认证URL:', url)
        if (this.isListen) {
          logger.log('iframe授权监听:')
          // 回调
          if (url.includes('code=') || url.includes('token=') || url.includes('auth_success=true')) {
            logger.log('iframe授权回调')
            this.callbackIframeSrc = url
          } else {
            this.iframeSrc = url // 认证过程中，保持在iframe内
          }
          return
        }
        this.iframeSrc = url
        logger.log('iframe初始地址', this.iframeSrc)
        this.isListen = true
        this.addEvent(window, 'message', this.listenHandle)
      }
    },
    async listenHandle(e) {
      logger.log('sso触发监听：', e.data)

      if (e.data.type === 'oauth2_auth_callback') {
        this.handleOAuth2Message(e.data.code, e.data.auth_error)
        return
      }

      const eventName = e.data.event
      if (this.isThirdAppWakeup(eventName)) { // 第三发唤起app做授权认证
        this.wakeupApp(e)
        return
      }
      if (e.data) {
        this.submit(e.data)
      }
    },

    // 处理来自oauth2_result页面的消息
    async handleOAuth2Message(code, auth_error) {
      try {
        logger.log('收到oauth2_result页面的消息:', { code })
        if (!code) {
          if (auth_error && auth_error.includes('message =')) {
            const msg = apiMessage(auth_error)
            Message({
              type: 'error',
              message: '认证失败: ' + msg,
              showClose: true,
            })
            this.init()
          }
          logger.log('消息缺少必要参数:', { code })
          return
        }

        // 构建登录参数
        const authData = {
          clientId: 'client_portal',
          grantType: 'implicit',
          redirect_uri: '',
          idpId: this.authId,
          authWeb: {
            authWebToken: code // 使用code作为token
          }
        }

        logger.log('调用登录接口，参数:', authData)

        // 调用登录接口
        const loginResult = await this.userStore.LoginIn(authData, 'oauth2', this.authId)

        logger.log('登录结果:', loginResult)

        if (loginResult === true || (typeof loginResult === 'object' && loginResult !== null && loginResult.code !== -1)) {
          // 双因子验证
          const needSecondaryAuth = await this.secondaryAuth.handleSecondaryAuthResponse(loginResult)
          if (needSecondaryAuth) {
            logger.log('企业微信登录成功，进入双因子验证')
            return
          }
          logger.log('OAuth2登录成功')
        } else {
          logger.log('OAuth2登录失败:', loginResult)
        }
      } catch (error) {
        logger.log('处理OAuth2消息失败:', error)
      }
    },
    addEvent(element, event, handler) {
      if (element.addEventListener) { // 现代浏览器
        element.addEventListener(event, handler, false)
      } else if (element.attachEvent) { // IE8及以下
        element.attachEvent('on' + event, function() {
          // 修复this指向问题
          handler.call(element, window.event)
        })
      }
    },
    removeEvent(element, event, handlerWrapper) {
      if (element.removeEventListener) {
        element.removeEventListener(event, handlerWrapper)
      } else if (element.detachEvent) {
        element.detachEvent('on' + event, handlerWrapper)
      }
    },
    // ios浏览器iframe里面唤起第三方app失败所以逻辑放到外面
    isThirdAppWakeup(eventName) {
      return eventName === 'wakeup-app'
    },
    wakeupApp(e) {
      const url = e.data.params.url
      if (url) {
        window.location.href = url
      }
    },
    clearLoading() {
      if (this.loading) {
        this.loading.clear()
        this.loading = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.sso-warpper{
  padding: 30px 5px 0 5px;
  overflow: visible;
  position: relative;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .sso-title {
    width: 64px;
    height: 64px;
    line-height: 64px;
    padding: 12px 15px 12px 13px;
    margin: 0 auto 34px auto;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0px 2px 20px 0px rgba(46,60,128,0.10);
    color: #0082ef;
    font-size: 20px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .icon {
    height: 40px;
    width: 36px;
    vertical-align: top;
    display: inline-block;
  }
  .sso-img {
    width: 120px;
    height: 120px;
    display: block;
    margin: 0 auto;
  }
  .sso-callback-iframe {
    display: none;
    width: 0;
    height: 0;
    border: none;
  }
  .sso-iframe {
    transform: translateZ(0); /* 强制硬件加速 */
    backface-visibility: hidden;
    height: 320px;
    width: 100%;
  }
  .login_submit_button {
    width: 100%;
    height: 40px;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
