package dynamic_strategy

import (
	"fmt"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"

	"asdsec.com/asec/platform/app/dynamic_strategy_engine/config"
	"asdsec.com/asec/platform/pkg/biz/strategy_engine"
	"asdsec.com/asec/platform/pkg/ip2region"
	"asdsec.com/asec/platform/pkg/ip2region/aiwen"
	"asdsec.com/asec/platform/pkg/ip2region/common"
	pkgHTTP "github.com/apache/apisix-go-plugin-runner/pkg/http"
	"github.com/apache/apisix-go-plugin-runner/pkg/log"
	jsoniter "github.com/json-iterator/go"
)

var Ip2region common.Ip2region
var regionOnce sync.Once

const (
	block              = "block"
	allow              = "allow"
	virtualUserID      = "virtual_user"
	freeAuthHeader     = "Asec_Free_Auth"
	forbidAccessHeader = "Asec_Forbid_Access"
	blockHtml          = `
			<!DOCTYPE html>
            <html lang="en">
            <head>
            <meta charset="UTF-8">
            <title>拒绝访问</title>
            </head>
          <body style="display: flex;justify-content: center;align-items: center;height: 100vh;margin: 0;padding: 0">
          <div style="width: 520px;height: 241px;">
                               <div style="display: flex;border-bottom: 1px solid #EDEDED;align-items: center;">
                                                      <div class="circle">
                                                      <div class="circle-progress"></div>
                                                      <div class="circle-cut"></div>
                                                      </div>
                                                      <div style="display: flex;font-size: 24px;color: #D23030;height: 48px;align-items: center;">
                                                      很抱歉，您无权访问此页面，错误代码：403
                                                      </div>
            </div>
            <div>
                             <div style="font-size: 16px;margin: 15px 0px">访问阻断</div>
                             <div style="font-size: 14px;color: #929298;line-height: 2;">
                                                      您可能遇到了以下情况之一：<br>
                                                      您未经授权或未登录，无法访问所请求的页面。<br>
            </div>
            </div>
            </div>
            <script>
            // 清理当前域名下的cookie
            function clearAllCookies() {
                var cookies = document.cookie.split(";");
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i];
                    var eqPos = cookie.indexOf("=");
                    var name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                    if (name) {
                        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                    }
                }
            }
            // 页面加载时自动清理cookie
            clearAllCookies();
            </script>
            </body>
            </html>
            
            <style>
            .circle {
          width: 22px;
          height: 22px;
          border-radius: 50%;
          background: #fff;
          position: relative;
          margin-right: 10px;
          margin-top: 3px;
          }
            
            .circle-progress {
          width: 22px;
          height: 22px;
          border: 3px solid #D23030;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          }
            
            .circle-cut {
          width: 22px;
          height: 3px;
          background: #D23030;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(135deg);
          }
            </style>
`
)

type StrategyPluginConfig struct {
	Enable           bool   `json:"enable"`
	IsDependSite     bool   `json:"is_depend_site,omitempty"`    // 是否为依赖站点
	MainRouteId      string `json:"main_route_id,omitempty"`     // 主路由ID，用于依赖站点查找主应用信息
	AllowDegradation bool   `json:"allow_degradation,omitempty"` // 是否允许降级模式，当认证服务异常时默认放行
}

type StrategyPlugin struct {
}

func (s StrategyPlugin) Name() string {
	return "strategy_plugin"
}

func (s StrategyPlugin) ParseConf(in []byte) (conf interface{}, err error) {
	config := StrategyPluginConfig{}
	err = jsoniter.Unmarshal(in, &config)
	return config, err
}

func (s StrategyPlugin) RequestFilter(conf interface{}, w http.ResponseWriter, r pkgHTTP.Request) {
	regionOnce.Do(func() {
		// 注册地理位置转换
		if config.GlobalCfg.AiwenKey != "" {
			Ip2region, _ = ip2region.New(common.ImplType(config.GlobalCfg.Ip2regionType), aiwen.WithKey(config.GlobalCfg.AiwenKey))
		} else {
			Ip2region, _ = ip2region.New(common.OpenSourceImpl)
		}
	})
	c := conf.(StrategyPluginConfig)
	if !c.Enable {
		return
	}

	// 获取请求信息
	uri := string(r.Path())
	port, _ := r.Var("server_port")
	scheme, _ := r.Var("scheme")
	host, _ := r.Var("host")
	portInt, _ := strconv.ParseInt(string(port), 10, 64)

	var finalPort int64
	var finalHostname string

	// 获取客户端实际访问的Host头信息
	originalHost := r.Header().Get("Host")

	if originalHost != "" {
		// 尝试解析Host头中的端口（处理域名:端口格式）
		host, port, err := net.SplitHostPort(originalHost)
		if err == nil && port != "" {
			// Host头包含端口，使用这个端口（实际访问端口）
			if externalPort, parseErr := strconv.ParseInt(port, 10, 64); parseErr == nil {
				finalPort = externalPort
				finalHostname = host
			}
		} else {
			// Host头没有端口，使用协议默认端口
			finalHostname = originalHost
			schemeStr := string(scheme)
			if schemeStr == "https" {
				finalPort = 443
			} else if schemeStr == "http" {
				finalPort = 80
			} else {
				// 降级到内部端口
				finalPort = portInt
			}
		}
	} else {
		// 没有Host头，使用内部信息
		finalPort = portInt
		finalHostname = string(host)
	}

	// 使用一致的主机名和端口构建重定向URL
	redirectUrl := fmt.Sprintf("%s://%s:%d%s", string(scheme), finalHostname, finalPort, uri)
	// 检查是否为免认证访问
	freeAuthFlag := r.Header().Get(freeAuthHeader)
	forbidAccessFlag := r.Header().Get(forbidAccessHeader)
	userId := r.Header().Get("Asec_User_Id")

	// 处理禁止访问逻辑（优先级最高）
	if forbidAccessFlag == "1" {
		// log.Debugf("禁止访问路径: %s", uri)

		// 匹配应用以便记录完整的审计信息
		var appMatches map[uint64]strategy_engine.MatchAppInfo
		if c.IsDependSite && c.MainRouteId != "" {
			// 依赖站点：查找主应用信息
			appMatches = strategy_engine.AppMatchForDependSite(c.MainRouteId, AppInfo)
		} else {
			// 普通应用：直接匹配
			urlInfo := strategy_engine.URLInfo{
				Hostname: finalHostname,
				Scheme:   string(scheme),
				Port:     int32(finalPort),
				Path:     uri,
			}
			appMatches = strategy_engine.AppMatch(urlInfo, AppInfo)
		}

		if len(appMatches) > 0 {
			// 获取第一个匹配的应用ID
			var appId uint64
			for k := range appMatches {
				appId = k
				break
			}

			writeMathInfo(w, r, strategy_engine.StrategyMatchRes{
				Action:       block,
				AppId:        appId,
				StrategyId:   0, // 禁止访问没有策略ID
				StrategyName: "forbid_access",
			}, appMatches)

			if c.IsDependSite {
				log.Debugf("禁止访问 - 依赖站点应用: %s, 路径: %s", appMatches[appId].AppName, uri)
			} else {
				log.Debugf("禁止访问 - 应用: %s, 路径: %s", appMatches[appId].AppName, uri)
			}
		} else {
			// 未匹配到应用，记录基本信息
			writeMathInfo(w, r, strategy_engine.StrategyMatchRes{
				Action:       block,
				AppId:        0,
				StrategyId:   0,
				StrategyName: "forbid_access",
			}, nil)

			if c.IsDependSite {
				log.Debugf("禁止访问但未匹配到依赖站点主应用 - 路径: %s", uri)
			} else {
				log.Debugf("禁止访问但未匹配到应用 - 路径: %s", uri)
			}
		}

		writeBlock(w)
		return
	}

	// 处理免认证访问逻辑
	if freeAuthFlag == "1" && userId == virtualUserID {
		log.Debugf("处理免认证访问，路径: %s", uri)

		// 匹配应用
		var appMatches map[uint64]strategy_engine.MatchAppInfo
		if c.IsDependSite && c.MainRouteId != "" {
			// 依赖站点：查找主应用信息
			appMatches = strategy_engine.AppMatchForDependSite(c.MainRouteId, AppInfo)
		} else {
			// 普通应用：直接匹配
			urlInfo := strategy_engine.URLInfo{
				Hostname: finalHostname,
				Scheme:   string(scheme),
				Port:     int32(finalPort),
				Path:     uri,
			}
			appMatches = strategy_engine.AppMatch(urlInfo, AppInfo)
		}

		if len(appMatches) <= 0 {
			if c.IsDependSite {
				log.Debugf("免认证访问但未匹配到依赖站点主应用信息")
			} else {
				log.Debugf("免认证访问但未匹配到应用信息")
			}
			writeMathInfo(w, r, strategy_engine.StrategyMatchRes{
				Action: block,
			}, nil)
			writeBlock(w)
			return
		}

		// 确认应用匹配且是免认证资源，设置虚拟用户
		var appIds []uint64
		for k := range appMatches {
			appIds = append(appIds, k)
		}

		// 为免认证访问记录日志
		writeFreeAuthInfo(w, r, appMatches, appIds[0])
		if c.IsDependSite {
			log.Debugf("免认证访问允许通过，依赖站点应用: %s, 路径: %s", appMatches[appIds[0]].AppName, uri)
		} else {
			log.Debugf("免认证访问允许通过，应用: %s, 路径: %s", appMatches[appIds[0]].AppName, uri)
		}
		return
	}

	// 处理降级模式逻辑
	if userId == "" && c.AllowDegradation {
		log.Debugf("降级模式：认证服务异常，允许访问继续，路径: %s", uri)

		// 匹配应用以便记录审计信息
		var appMatches map[uint64]strategy_engine.MatchAppInfo
		if c.IsDependSite && c.MainRouteId != "" {
			// 依赖站点：查找主应用信息
			appMatches = strategy_engine.AppMatchForDependSite(c.MainRouteId, AppInfo)
		} else {
			// 普通应用：直接匹配
			urlInfo := strategy_engine.URLInfo{
				Hostname: finalHostname,
				Scheme:   string(scheme),
				Port:     int32(finalPort),
				Path:     uri,
			}
			appMatches = strategy_engine.AppMatch(urlInfo, AppInfo)
		}

		if len(appMatches) > 0 {
			// 获取第一个匹配的应用ID
			var appId uint64
			for k := range appMatches {
				appId = k
				break
			}
			// 记录降级访问日志，允许通过
			writeDegradationInfo(w, r, appMatches, appId)
			if c.IsDependSite {
				log.Debugf("降级模式允许通过，依赖站点应用: %s, 路径: %s", appMatches[appId].AppName, uri)
			} else {
				log.Debugf("降级模式允许通过，应用: %s, 路径: %s", appMatches[appId].AppName, uri)
			}
		} else {
			// 未匹配到应用，记录基本信息但不允许通过
			writeMathInfo(w, r, strategy_engine.StrategyMatchRes{
				Action:       block,
				AppId:        0,
				StrategyId:   0,
				StrategyName: "降级模式未匹配应用",
			}, nil)
			if c.IsDependSite {
				log.Debugf("降级模式但未匹配到依赖站点主应用，拒绝访问，路径: %s", uri)
			} else {
				log.Debugf("降级模式但未匹配到应用，拒绝访问，路径: %s", uri)
			}
			writeBlock(w)
			return
		}
		return
	}

	// 原有的认证用户处理逻辑
	if userId == "" {
		log.Infof("not found userInfo")
		writeMathInfo(w, r, strategy_engine.StrategyMatchRes{
			Action: block,
		}, nil)
		writeBlock(w)
		return
	}

	urlInfo := strategy_engine.URLInfo{
		Hostname: finalHostname,
		Scheme:   string(scheme),
		Port:     int32(finalPort),
		Path:     uri,
	}

	// 处理依赖站点的应用匹配逻辑
	var appMatches map[uint64]strategy_engine.MatchAppInfo
	if c.IsDependSite && c.MainRouteId != "" {
		// 依赖站点：需要根据主路由ID查找对应的主应用信息
		appMatches = strategy_engine.AppMatchForDependSite(c.MainRouteId, AppInfo)
		if len(appMatches) <= 0 {
			log.Debugf("依赖站点未找到对应的主应用信息，主路由ID: %s", c.MainRouteId)
			writeMathInfo(w, r, strategy_engine.StrategyMatchRes{
				Action: block,
			}, nil)
			writeBlock(w)
			return
		}
		log.Debugf("依赖站点访问，使用主应用信息进行匹配，主路由ID: %s", c.MainRouteId)
	} else {
		// 普通应用：直接进行应用匹配
		appMatches = strategy_engine.AppMatch(urlInfo, AppInfo)
		if len(appMatches) <= 0 {
			writeMathInfo(w, r, strategy_engine.StrategyMatchRes{
				Action: block,
			}, nil)
			log.Infof("not match app info")
			writeBlock(w)
			return
		}
	}
	// 匹配策略
	var appIds []uint64
	for k := range appMatches {
		appIds = append(appIds, k)
	}
	timeIso8601, _ := r.Var("time_iso8601")
	input := strategy_engine.CollectWebInput(r, Ip2region)
	matchInfo := strategy_engine.MatchInfo{
		AppIds:     appIds,
		UserId:     userId,
		AccessTime: string(timeIso8601),
		EvalInput:  input,
	}
	matchRes := strategy_engine.StrategyMatch(matchInfo, AccessInfo, &UciUserInfo)
	log.Infof("matchRes:%v", matchRes)

	// 添加依赖站点的调试信息
	if c.IsDependSite {
		log.Debugf("依赖站点策略匹配完成 - 主路由ID: %s, 结果: %s, 应用: %s",
			c.MainRouteId, matchRes.Action, appMatches[matchRes.AppId].AppName)
	}

	writeMathInfo(w, r, matchRes, appMatches)
	// 二次认证
	log.Infof("matchRes:%v", matchRes)
	if appMatches[matchRes.AppId].IdpId != "" {
		smsIdpList := strings.Split(r.Header().Get("Asec_Sms_Idp"), ",")
		needSecondary := true
		for _, idp := range smsIdpList {
			if idp == appMatches[matchRes.AppId].IdpId {
				needSecondary = false
			}
		}
		if needSecondary {
			writeSecondaryAuth(w, r, userId, appMatches[matchRes.AppId].IdpId, redirectUrl)
		}
		log.Infof("web idp:%v , app idp:%v", smsIdpList, appMatches[matchRes.AppId].IdpId)
	}
	if matchRes.Action == block {
		log.Infof("Strategy block")
		writeBlock(w)
	}
}

// writeSecondaryAuth
func writeSecondaryAuth(w http.ResponseWriter, r pkgHTTP.Request, userId, idpIp, redirectUrl string) {
	log.Infof("redirectUrl:%v", redirectUrl)
	w.WriteHeader(302)
	host := strings.Split(config.GlobalCfg.PrivateHost, ":")[0]
	url := fmt.Sprintf("https://%s/#/appverify?user_id=%s&idp_id=%s&redirect_url=%s", host, userId, idpIp, redirectUrl)
	log.Infof("real url:%v", url)
	w.Header().Set("Location", url)
}

// writeMathInfo 往header中写入匹配信息,供日志插件使用
func writeMathInfo(w http.ResponseWriter, r pkgHTTP.Request, matchRes strategy_engine.StrategyMatchRes, matches map[uint64]strategy_engine.MatchAppInfo) {
	if matchRes.Action == block {
		w.Header().Set("app_name", matches[matchRes.AppId].AppName)
		w.Header().Set("app_id", strconv.FormatUint(matchRes.AppId, 10))
		w.Header().Set("strategy_id", strconv.FormatUint(matchRes.StrategyId, 10))
		w.Header().Set("strategy_name", matchRes.StrategyName)
		w.Header().Set("action", matchRes.Action)
		w.Header().Set("app_type", "web")
		w.Header().Set("server_addr", matches[matchRes.AppId].ServerAddress)

		log.Debugf("app_name:%v", w.Header().Get("app_name"))
		log.Debugf("app_id:%v", w.Header().Get("app_id"))
		log.Debugf("strategy_id:%v", w.Header().Get("strategy_id"))
		log.Debugf("strategy_name:%v", w.Header().Get("strategy_name"))
		log.Debugf("action:%v", w.Header().Get("action"))
		log.Debugf("app_type:%v", w.Header().Get("app_type"))
		log.Debugf("server_addr:%v", w.Header().Get("server_addr"))
	} else {
		r.Header().Set("app_name", matches[matchRes.AppId].AppName)
		r.Header().Set("app_id", strconv.FormatUint(matchRes.AppId, 10))
		r.Header().Set("strategy_id", strconv.FormatUint(matchRes.StrategyId, 10))
		r.Header().Set("strategy_name", matchRes.StrategyName)
		r.Header().Set("action", matchRes.Action)
		r.Header().Set("app_type", "web")
		r.Header().Set("server_addr", matches[matchRes.AppId].ServerAddress)

		log.Debugf("app_name:%v", r.Header().Get("app_name"))
		log.Debugf("app_id:%v", r.Header().Get("app_id"))
		log.Debugf("strategy_id:%v", r.Header().Get("strategy_id"))
		log.Debugf("strategy_name:%v", r.Header().Get("strategy_name"))
		log.Debugf("action:%v", r.Header().Get("action"))
		log.Debugf("app_type:%v", r.Header().Get("app_type"))
		log.Debugf("server_addr:%v", r.Header().Get("server_addr"))
	}

}

func writeBlock(w http.ResponseWriter) {
	w.WriteHeader(403)
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(blockHtml))
}

func (s StrategyPlugin) ResponseFilter(conf interface{}, w pkgHTTP.Response) {
	// do nothing
}

// writeFreeAuthInfo 为免认证访问写入匹配信息,供日志插件使用
func writeFreeAuthInfo(_ http.ResponseWriter, r pkgHTTP.Request, matches map[uint64]strategy_engine.MatchAppInfo, appId uint64) {
	healthCheckFlag := r.Header().Get("Asec_Health_Check")
	var strategyName string
	if healthCheckFlag != "" {
			strategyName = "健康检查"
	} else {
			strategyName = "URL路径白名单"
	}
	
	r.Header().Set("app_name", matches[appId].AppName)
	r.Header().Set("app_id", strconv.FormatUint(appId, 10))
	r.Header().Set("strategy_id", "0") // 免认证没有策略ID
	r.Header().Set("strategy_name", strategyName)
	r.Header().Set("action", allow)
	r.Header().Set("app_type", "web")
	r.Header().Set("server_addr", matches[appId].ServerAddress)
	r.Header().Set("auth_type", "free_auth") // 标识免认证类型

	log.Debugf("免认证访问 - app_name:%v", r.Header().Get("app_name"))
	log.Debugf("免认证访问 - app_id:%v", r.Header().Get("app_id"))
	log.Debugf("免认证访问 - strategy_name:%v", r.Header().Get("strategy_name"))
	log.Debugf("免认证访问 - action:%v", r.Header().Get("action"))
	log.Debugf("免认证访问 - app_type:%v", r.Header().Get("app_type"))
	log.Debugf("免认证访问 - server_addr:%v", r.Header().Get("server_addr"))
}

// writeDegradationInfo 为降级模式访问写入匹配信息,供日志插件使用
func writeDegradationInfo(_ http.ResponseWriter, r pkgHTTP.Request, matches map[uint64]strategy_engine.MatchAppInfo, appId uint64) {
	if matches != nil && appId > 0 {
		r.Header().Set("app_name", matches[appId].AppName)
		r.Header().Set("app_id", strconv.FormatUint(appId, 10))
		r.Header().Set("server_addr", matches[appId].ServerAddress)
	} else {
		r.Header().Set("app_name", "未匹配应用")
		r.Header().Set("app_id", "0")
		r.Header().Set("server_addr", "")
	}
	r.Header().Set("strategy_id", "0") // 降级模式没有策略ID
	r.Header().Set("strategy_name", "认证服务降级")
	r.Header().Set("action", allow)
	r.Header().Set("app_type", "web")
	r.Header().Set("auth_type", "degradation") // 标识降级模式类型

	log.Debugf("降级模式访问 - app_name:%v", r.Header().Get("app_name"))
	log.Debugf("降级模式访问 - app_id:%v", r.Header().Get("app_id"))
	log.Debugf("降级模式访问 - strategy_name:%v", r.Header().Get("strategy_name"))
	log.Debugf("降级模式访问 - action:%v", r.Header().Get("action"))
	log.Debugf("降级模式访问 - app_type:%v", r.Header().Get("app_type"))
	log.Debugf("降级模式访问 - server_addr:%v", r.Header().Get("server_addr"))
}
