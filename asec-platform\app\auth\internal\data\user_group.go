package data

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/common"
	modelTable "asdsec.com/asec/platform/pkg/model"

	"github.com/jinzhu/copier"

	"github.com/google/uuid"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"github.com/go-kratos/kratos/v2/errors"
	"gorm.io/gorm"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
)

type userGroupRepo struct {
	data *Data
	log  *log.Helper
}

func (u userGroupRepo) GetGroupBasicInfo(ctx context.Context, corpId, groupId string) (dto.GroupBasicInfo, error) {
	var result dto.GroupBasicInfo
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	us := q.TbUserSource
	err := ug.WithContext(ctx).LeftJoin(us, us.ID.EqCol(ug.SourceID)).
		Where(ug.CorpID.Eq(corpId), ug.ID.Eq(groupId)).
		Select(ug.ALL, us.SourceType.As("SourceType"), us.TemplateType.As("TemplateType"), us.Name.As("SourceName")).Scan(&result)
	return result, err
}

func (u userGroupRepo) CreateSyncLog(ctx context.Context, param dto.CreateSyncLogParam) error {
	syncLog := query.Use(u.data.db).TbUserGroupSyncLog
	var temp model.TbUserGroupSyncLog
	if err := copier.Copy(&temp, &param); err != nil {
		return err
	}
	return syncLog.WithContext(ctx).Create(&temp)
}

func (u userGroupRepo) CountGroupSyncLog(ctx context.Context, corpId, groupId string) (int64, error) {
	syncLog := query.Use(u.data.db).TbUserGroupSyncLog
	return syncLog.WithContext(ctx).Where(syncLog.CorpID.Eq(corpId), syncLog.GroupID.Eq(groupId)).Count()
}

func (u userGroupRepo) ListGroupSyncLog(ctx context.Context, corpId, groupId string, limit, offset int) ([]*model.TbUserGroupSyncLog, error) {
	syncLog := query.Use(u.data.db).TbUserGroupSyncLog
	return syncLog.WithContext(ctx).Where(syncLog.CorpID.Eq(corpId), syncLog.GroupID.Eq(groupId)).
		Limit(limit).Offset(offset).Order(syncLog.CreatedAt.Desc()).Find()
}

func (u userGroupRepo) GetRootGroupSync(ctx context.Context, rootGroupId string) (*model.TbUserGroupSync, error) {
	sync := query.Use(u.data.db).TbUserGroupSync
	result, err := sync.WithContext(ctx).Where(sync.GroupID.Eq(rootGroupId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserGroupSync{}, pb.ErrorRecordNotFound("group sync not found. rootGroupId=%v", rootGroupId)
		}
		return &model.TbUserGroupSync{}, err
	}
	return result, nil
}
func (u userGroupRepo) GetRunningRootGroupSync(ctx context.Context, rootGroupId string) (int64, error) {
	var count int
	err := u.data.db.Model(model.TbUserGroupSync{}).Debug().
		Select("count(id) as count").Where("group_id = ? AND sync_status = ?", rootGroupId, dto.SyncRunning).Find(&count).Error
	return int64(count), err
}

func (u userGroupRepo) UpdateRootGroupSync(ctx context.Context, rootGroupId string, lastSyncTime, nextSyncTime time.Time, syncStatus string) error {
	sync := query.Use(u.data.db).TbUserGroupSync
	_, err := sync.WithContext(ctx).Where(sync.GroupID.Eq(rootGroupId)).Updates(model.TbUserGroupSync{
		LastSyncTime: lastSyncTime,
		NextSyncTime: nextSyncTime,
		SyncStatus:   syncStatus,
	})
	return err
}

func (u userGroupRepo) ListAllRootGroupWithSync(ctx context.Context) ([]*dto.RootGroupSync, error) {
	var result []*dto.RootGroupSync
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	sync := q.TbUserGroupSync
	err := ug.WithContext(ctx).LeftJoin(sync, sync.GroupID.EqCol(ug.ID)).
		Where(sync.GroupID.IsNotNull(), sync.GroupID.Neq(""), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId)).
		Select(ug.CorpID, sync.GroupID, sync.AutoSync,
			sync.LastSyncTime, sync.NextSyncTime, sync.SyncStatus, sync.SyncCycle, sync.SyncUnit).Scan(&result)
	return result, err
}

func (u userGroupRepo) UpdateRootGroup(ctx context.Context, corpId string, param dto.UpdateGroupParam, parentGroup *model.TbUserGroup, group *model.TbUserGroup) error {
	var path = group.Path
	// 默认使用原先的path
	if parentGroup != nil {
		if parentGroup.Path == "/" {
			path = parentGroup.Path + parentGroup.Name
		} else {
			path = parentGroup.Path + "/" + parentGroup.Name
		}
	}

	var historyPath string
	if group.Path == "/" {
		historyPath = group.Path + group.Name
	} else {
		historyPath = group.Path + "/" + group.Name
	}

	var newPath string

	return u.data.db.Transaction(func(tx *gorm.DB) error {
		// 父级组织和名称只要一个修改所有的子级path都需要调整
		if parentGroup != nil || param.Name != group.Name {
			if path == "/" {
				newPath = path + param.Name
			} else {
				newPath = path + "/" + param.Name
			}
			err := tx.WithContext(ctx).Model(model.TbUserGroup{}).
				Where("path like ?", fmt.Sprintf("%v%%", historyPath)).
				Update("path", gorm.Expr("REPLACE(path, ?, ?)", historyPath, newPath)).
				Error
			if err != nil {
				return err
			}
		}

		err := tx.WithContext(ctx).Model(model.TbUserGroup{}).
			Where("corp_id = ? and id = ?", corpId, param.GroupId).
			Updates(model.TbUserGroup{
				ID:            param.GroupId,
				Name:          param.Name,
				CorpID:        param.CorpId,
				Path:          path,
				Description:   param.Description,
				ParentGroupID: param.ParentGroupId,
			}).Error

		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.UserGroupType,
				OperationType:  common.OperateUpdate,
				Representation: param.Name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			u.data.db.Create(&oplog)

		}()

		return err
	})
}

func (u userGroupRepo) DeleteSubGroupsOfRoot(ctx context.Context, corpId, rootGroupId string) error {
	ug := query.Use(u.data.db).TbUserGroup
	_, err := ug.WithContext(ctx).
		Where(ug.CorpID.Eq(corpId), ug.RootGroupID.Eq(rootGroupId), ug.ParentGroupID.Neq(dto.FakeRootUserGroupId)).Delete()
	return err
}

func (u userGroupRepo) GetRootGroupSourceType(ctx context.Context, corpId string, rootGroupId string) (string, error) {
	var result string
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	us := q.TbUserSource
	err := ug.WithContext(ctx).LeftJoin(us, us.ID.EqCol(ug.SourceID)).
		Where(ug.CorpID.Eq(corpId), ug.ID.Eq(rootGroupId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId)).
		Select(us.SourceType).Scan(&result)
	return result, err
}

func (u userGroupRepo) GetGroupSyncConfig(ctx context.Context, corpId string, groupId string) ([]dto.KV, error) {
	var result []dto.KV
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	sync := q.TbUserGroupSync
	conf := q.TbUserGroupSyncConfig
	err := ug.WithContext(ctx).LeftJoin(sync, sync.GroupID.EqCol(ug.ID)).LeftJoin(conf, conf.SyncID.EqCol(sync.ID)).
		Where(ug.CorpID.Eq(corpId), ug.ID.Eq(groupId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId)).
		Select(conf.Key, conf.Value).Scan(&result)
	return result, err
}

func (u userGroupRepo) DeleteGroup(ctx context.Context, corpId string, groupIds, userIds []string, sourceType, name string) error {
	return u.data.db.Transaction(func(tx *gorm.DB) error {
		// 删除用户
		err := u.DeleteBatchUser(ctx, corpId, userIds, groupIds, tx)
		if err != nil {
			u.log.Errorf("DeleteUserOfGroup Failed. err=%v", err)
			return err
		}
		// 企业微信等则需要删除同步配置
		err = u.DeleteGroupSyncConfig(ctx, corpId, groupIds, tx)
		if err != nil {
			u.log.Errorf("DeleteUserGroupSync Failed. err=%v", err)
			return err
		}
		// 删除idp和group的关联关系
		err = u.DeleteGroupIdpMap(ctx, corpId, groupIds, tx)
		if err != nil {
			u.log.Errorf("DeleteIdpGroupMap Failed. err=%v", err)
			return err
		}
		// 删除用户目录
		err = u.DeleteUserGroup(ctx, corpId, groupIds, tx)
		if err != nil {
			u.log.Errorf("DeleteUserGroup Failed. err=%v", err)
			return err
		}

		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		//err := tx.WithContext(ctx).Model(model.TbCredential{})
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.UserGroupType,
				OperationType:  common.OperateDelete,
				Representation: name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			u.data.db.Create(&oplog)

		}()

		return nil
	})
}

func (u userGroupRepo) DeleteGroupIdpMap(ctx context.Context, corpId string, groupIds []string, tx *gorm.DB) error {
	return tx.Transaction(func(tx *gorm.DB) error {
		// 删除idp和group的关联关系
		if err := tx.WithContext(ctx).Model(model.TbIdpGroupMapper{}).
			Where("group_id in ? and corp_id = ?", groupIds, corpId).
			Delete(&model.TbIdpGroupMapper{}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (u userGroupRepo) DeleteGroupSyncConfig(ctx context.Context, corpId string, groupIds []string, tx *gorm.DB) error {
	return tx.Transaction(func(tx *gorm.DB) error {
		var syncIds []string
		err := tx.WithContext(ctx).Model(model.TbUserGroupSync{}).Select("id").
			Where("group_id in ?", groupIds).Find(&syncIds).Error
		if err != nil {
			u.log.Errorf("GetSyncIds Failed. err=%v", err)
			return err
		}
		// 删除企业微信目录同步配置
		if err := tx.WithContext(ctx).Model(model.TbUserGroupSyncConfig{}).
			Where("sync_id in ?", syncIds).
			Delete(&model.TbUserGroupSyncConfig{}).Error; err != nil {
			return err
		}
		// 删除企业微信目录同步关系
		if err := tx.WithContext(ctx).Model(model.TbUserGroupSync{}).
			Where("group_id in ?", groupIds).
			Delete(&model.TbUserGroupSync{}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (u userGroupRepo) DeleteUserGroup(ctx context.Context, corpId string, groupIds []string, tx *gorm.DB) error {
	return tx.Transaction(func(tx *gorm.DB) error {
		// 删除用户目录
		if err := tx.WithContext(ctx).Model(model.TbUserGroup{}).
			Where("corp_id = ? and id in ?", corpId, groupIds).
			Delete(&model.TbUserGroup{}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (u userGroupRepo) DeleteBatchUser(ctx context.Context, corpId string, userIds []string, groupIds []string, tx *gorm.DB) error {
	return tx.Transaction(func(tx *gorm.DB) error {
		// 删除凭证
		if err := tx.WithContext(ctx).Model(model.TbCredential{}).
			Where("corp_id = ? and user_id in ?", corpId, userIds).
			Delete(&model.TbCredential{}).Error; err != nil {
			return err
		}
		// 删除角色关系
		if err := tx.WithContext(ctx).Model(model.TbUserRole{}).
			Where("corp_id = ? and user_id in ?", corpId, userIds).
			Delete(&model.TbUserRole{}).Error; err != nil {
			return err
		}
		// 删除用户实体
		if err := tx.WithContext(ctx).Model(model.TbUserEntity{}).
			Where("corp_id = ? and id in ?", corpId, userIds).
			Delete(&model.TbUserEntity{}).Error; err != nil {
			return err
		}

		//删除外部用户表
		if err := tx.WithContext(ctx).Model(model.TbExternalUser{}).
			Where(" local_user_id in ?", userIds).
			Delete(&model.TbExternalUser{}).Error; err != nil {
			return err
		}

		// 删除策略绑定关系 用户
		for _, v := range userIds {
			err := tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
				Where("corp_id = ?", corpId).
				Update("user_ids", gorm.Expr("array_remove(user_ids, ?)", v)).Error
			if err != nil {
				u.log.Errorf("RemoveAuthPolicyUser Failed. err=%v", err)
				return err
			}
		}
		// 删除策略绑定关系 用户组
		for _, v := range groupIds {
			err := tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
				Where("corp_id = ?", corpId).
				Update("group_ids", gorm.Expr("array_remove(group_ids, ?)", v)).Error
			if err != nil {
				u.log.Errorf("RemoveAuthPolicyUser Failed. err=%v", err)
				return err
			}
		}
		// 删除根组策略
		if err := tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
			Where("corp_id = ? and root_group_id in ?", corpId, groupIds).
			Delete(&model.TbAuthPolicy{}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (u userGroupRepo) ListRootGroupBindMainIdp(ctx context.Context, corpId, groupId string) ([]dto.IDPBasic, error) {
	var result []dto.IDPBasic
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	mapper := q.TbIdpGroupMapper
	idp := q.TbIdentityProvider
	err := ug.WithContext(ctx).Join(mapper, mapper.GroupID.EqCol(ug.ID)).Join(idp, idp.ID.EqCol(mapper.ProviderID)).
		Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId), ug.ID.Eq(groupId)).
		Select(idp.ALL).Scan(&result)
	return result, err
}

func (u userGroupRepo) ListRootGroupBindAssistIdp(ctx context.Context, corpId string) ([]dto.IDPBasic, error) {
	var result []dto.IDPBasic
	idp := query.Use(u.data.db).TbIdentityProvider
	err := idp.WithContext(ctx).Where(idp.Type.In(dto.AssistIDPType...), idp.CorpID.Eq(corpId)).
		Select(idp.ALL).Scan(&result)
	return result, err
}

func (u userGroupRepo) CountRootGroupInGroupIds(ctx context.Context, corpId string, groupIds []string) (int64, error) {
	ug := query.Use(u.data.db).TbUserGroup
	return ug.WithContext(ctx).
		Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId), ug.ID.In(groupIds...)).Count()
}

func (u userGroupRepo) GetAllSourceInGroupIds(ctx context.Context, corpId string, groupIds []string) ([]string, error) {
	var result []string
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	err := ug.WithContext(ctx).
		Where(ug.CorpID.Eq(corpId), ug.ID.In(groupIds...)).Select(ug.SourceID).Group(ug.SourceID).Scan(&result)
	return result, err
}

func (u userGroupRepo) SwitchAutoSync(ctx context.Context, groupId string, autoSync bool) error {
	q := query.Use(u.data.db)
	sync := q.TbUserGroupSync
	_, err := sync.WithContext(ctx).Where(sync.GroupID.Eq(groupId)).Update(sync.AutoSync, autoSync)
	return err
}

func (u userGroupRepo) CreateUserGroupWithSync(ctx context.Context, param dto.CreateRootGroupDaoParam) error {
	q := query.Use(u.data.db)
	return q.Transaction(func(tx *query.Query) error {
		if err := tx.TbUserGroup.WithContext(ctx).Create(&model.TbUserGroup{
			ID:            param.ID,
			Name:          param.Name,
			ParentGroupID: dto.FakeRootUserGroupId,
			CorpID:        param.CorpId,
			SourceID:      param.SourceId,
			Description:   param.Description,
			RootGroupID:   param.ID,
			Path:          "/",
		}); err != nil {
			return err
		}
		syncId := uuid.New().String()
		if err := tx.TbUserGroupSync.WithContext(ctx).Create(&model.TbUserGroupSync{
			ID:        syncId,
			GroupID:   param.ID,
			AutoSync:  param.AutoSync,
			SyncCycle: param.SyncCycle,
			SyncUnit:  string(param.SyncUnit),
		}); err != nil {
			return err
		}
		var syncConfigs []*model.TbUserGroupSyncConfig
		for _, config := range param.Configs {
			syncConfigs = append(syncConfigs, &model.TbUserGroupSyncConfig{
				SyncID: syncId,
				Key:    config.Key,
				Value:  config.Value,
			})
		}
		if err := tx.TbUserGroupSyncConfig.WithContext(ctx).Create(syncConfigs...); err != nil {
			return err
		}
		return nil
	})
}

func (u userGroupRepo) UpdateThirdRootGroup(ctx context.Context, corpId string, param dto.UpdateGroupParam, thirdType string, groupName string) error {
	q := query.Use(u.data.db)
	return q.Transaction(func(tx *query.Query) error {
		ug := q.TbUserGroup
		// 名称修改则修改所有自己的path
		if groupName != param.Name {
			if _, err := ug.WithContext(ctx).
				Where(ug.Path.Like(fmt.Sprintf("/%v%%", groupName))).
				Update(ug.Path, gorm.Expr("REPLACE(path, ?, ?)", "/"+groupName, "/"+param.Name)); err != nil {
				return err
			}
		}
		// 组信息修改
		if _, err := ug.WithContext(ctx).Select(ug.Name, ug.Description).Where(ug.ID.Eq(param.GroupId), ug.CorpID.Eq(corpId)).Updates(&model.TbUserGroup{
			ID:          param.GroupId,
			Name:        param.Name,
			CorpID:      corpId,
			SourceID:    param.SourceId,
			Description: param.Description,
		}); err != nil {
			return err
		}
		// 同步配置信息修改
		ugs := q.TbUserGroupSync
		if _, err := ugs.WithContext(ctx).Where(ugs.GroupID.Eq(param.GroupId)).Updates(&model.TbUserGroupSync{
			GroupID:   param.GroupId,
			SyncCycle: param.SyncCycle,
			SyncUnit:  string(param.SyncUnit),
		}); err != nil {
			return err
		}

		if _, err := ugs.WithContext(ctx).Where(ugs.GroupID.Eq(param.GroupId)).UpdateColumnSimple(ugs.AutoSync.Value(param.AutoSync)); err != nil {
			return err
		}
		// 同步配置字段KV修改
		userGroupSync, err := ugs.WithContext(ctx).Where(ugs.GroupID.Eq(param.GroupId)).First()
		if err != nil {
			return err
		}
		// 删除所有的配置KV
		ugsc := q.TbUserGroupSyncConfig
		if _, err := ugsc.WithContext(ctx).Where(ugsc.SyncID.Eq(userGroupSync.ID)).Delete(&model.TbUserGroupSyncConfig{}); err != nil {
			return err
		}
		var configs []dto.KV
		switch thirdType {
		case string(dto.CacheTypeWxIdp):
			configs = append(configs, param.WxConfig.ToKVs()...)
		case string(dto.CacheTypeFsIdp):
			configs = append(configs, param.FeishuConfig.ToKVs()...)
		case string(dto.CacheTypeDingtalkIdp):
			configs = append(configs, param.DingtalkConfig.ToKVs()...)
		case string(dto.CacheTypeLdapIdp):
			configs = append(configs, param.LdapConfig.ToKVs()...)
		case string(dto.CacheTypeMsadkIdp):
			configs = append(configs, param.LdapConfig.ToKVs()...)
		case string(dto.CacheTypeInfogoIdp):
			configs = append(configs, param.InfogoConfig.ToKVs()...)
		case string(dto.CacheTypeOAuth2Idp), string(dto.CacheTypeCasIdp), string(dto.CacheTypeWebIdp):
			configs = append(configs, param.OAuth2Config.ToKVs()...)
		default:
			u.log.Errorf("Unknown type. type=%v", thirdType)
			return pb.ErrorParamError("第三方类型错误%v", thirdType)
		}

		fieldMap, err := json.Marshal(param.FieldMap)
		if err != nil {
			u.log.Errorf("Marshal failed. err=%v", err)
			return err
		}
		switch thirdType {
		case string(dto.CacheTypeWxIdp):
			configs = append(configs, dto.KV{
				Key:   dto.AttrKeyWxFieldMap,
				Value: string(fieldMap),
			})
		case string(dto.CacheTypeFsIdp):
			configs = append(configs, dto.KV{
				Key:   dto.AttrKeyFeiShuFieldMap,
				Value: string(fieldMap),
			})
		case string(dto.CacheTypeDingtalkIdp):
			configs = append(configs, dto.KV{
				Key:   dto.AttrKeyDingtalkFieldMap,
				Value: string(fieldMap),
			})
		case string(dto.CacheTypeLdapIdp):
			configs = append(configs, dto.KV{
				Key:   dto.AttrKeyAdFieldMap,
				Value: string(fieldMap),
			})
		case string(dto.CacheTypeMsadkIdp):
			configs = append(configs, dto.KV{
				Key:   dto.AttrKeyAdFieldMap,
				Value: string(fieldMap),
			})
		case string(dto.CacheTypeInfogoIdp):
			configs = append(configs, dto.KV{
				Key:   dto.AttrKeyInfogoFieldMap,
				Value: string(fieldMap),
			})
		case string(dto.CacheTypeOAuth2Idp):
			configs = append(configs, dto.KV{
				Key:   dto.AttrKeyOAuth2FieldMap,
				Value: string(fieldMap),
			})
		}

		var syncConfigs []*model.TbUserGroupSyncConfig
		for _, config := range configs {
			syncConfigs = append(syncConfigs, &model.TbUserGroupSyncConfig{
				SyncID: userGroupSync.ID,
				Key:    config.Key,
				Value:  config.Value,
			})
		}
		if err := tx.TbUserGroupSyncConfig.WithContext(ctx).Create(syncConfigs...); err != nil {
			return err
		}
		return nil
	})
}

func (u userGroupRepo) ListRootGroupWithoutSync(ctx context.Context, corpId string) ([]dto.RootGroup, error) {
	var result []dto.RootGroup
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	us := q.TbUserSource
	sync := q.TbUserGroupSync
	err := ug.WithContext(ctx).LeftJoin(us, us.ID.EqCol(ug.SourceID)).LeftJoin(sync, sync.GroupID.EqCol(ug.ID)).
		Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId), sync.GroupID.IsNull()).
		Select(ug.ALL, us.ID.As("SourceId"), us.SourceType, us.Name.As("SourceName")).Scan(&result)
	return result, err
}

func (u userGroupRepo) ListRootGroup(ctx context.Context, corpId string, limit, offset int) ([]dto.RootGroup, error) {
	var result []dto.RootGroup
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	us := q.TbUserSource
	sync := q.TbUserGroupSync
	err := ug.WithContext(ctx).LeftJoin(us, us.ID.EqCol(ug.SourceID)).LeftJoin(sync, sync.GroupID.EqCol(ug.ID)).
		Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId)).
		Or(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId), sync.GroupID.IsNull()).
		Select(ug.ALL,
			us.ID.As("SourceId"), us.SourceType, us.TemplateType, us.Name.As("SourceName"),
			sync.SyncStatus, sync.AutoSync, sync.LastSyncTime.As("SyncTime")).Order(ug.CreatedAt.Desc()).
		Limit(limit).Offset(offset).
		Scan(&result)
	return result, err
}

func (u userGroupRepo) ListRootGroupIds(ctx context.Context, corpId string, limit, offset int) ([]string, error) {
	var result []string
	ug := query.Use(u.data.db).TbUserGroup
	err := ug.WithContext(ctx).Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId)).
		Limit(limit).Offset(offset).Order(ug.CreatedAt.Desc()).
		Select(ug.ID).Scan(&result)
	return result, err
}

func (u userGroupRepo) CountRootGroup(ctx context.Context, corpId string) (int64, error) {
	ug := query.Use(u.data.db).TbUserGroup
	return ug.WithContext(ctx).Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(dto.FakeRootUserGroupId)).Count()
}

func (u userGroupRepo) CountGroupInParent(ctx context.Context, corpId string, parentGroupId string) (int64, error) {
	ug := query.Use(u.data.db).TbUserGroup
	return ug.WithContext(ctx).Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.Eq(parentGroupId)).Count()

}

func (u userGroupRepo) ListBindGroupInfo(ctx context.Context, corpId string, groupIds []string) ([]dto.BindGroupInfo, error) {
	var result []dto.BindGroupInfo
	ug := query.Use(u.data.db).TbUserGroup
	us := query.Use(u.data.db).TbUserSource
	err := ug.WithContext(ctx).
		Where(ug.CorpID.Eq(corpId), ug.ID.In(groupIds...)).
		Join(us, ug.SourceID.EqCol(us.ID)).
		Select(ug.ID, ug.Name, ug.Path, us.SourceType).
		Scan(&result)
	return result, err
}

func (u userGroupRepo) CreateUserGroup(ctx context.Context, param dto.CreateGroupDaoParam) error {
	ug := query.Use(u.data.db).TbUserGroup

	//日志操作
	var errorLog = ""
	authUserID, _ := common.GetUserId(ctx)
	defer func() {
		//if err != nil {
		//	errorLog = err.Error()
		//}
		oplog := modelTable.Oprlog{
			Id:             uuid.New().String(),
			CorpId:         param.CorpId,
			ResourceType:   common.UserGroupType,
			OperationType:  common.OperateCreate,
			Representation: param.Name,
			Error:          errorLog,
			AuthUserID:     authUserID,
			AdminEventTime: time.Now().UnixMilli(),
			IpAddress:      common.GetClientHost(ctx),
		}
		u.data.db.Create(&oplog)

	}()

	return ug.WithContext(ctx).Create(&model.TbUserGroup{
		ID:            param.Id,
		Name:          param.Name,
		Description:   param.Description,
		ParentGroupID: param.ParentGroupId,
		Path:          param.Path,
		CorpID:        param.CorpId,
		SourceID:      param.SourceId,
		RootGroupID:   param.RootGroupID,
	})
}

func (u userGroupRepo) BatchInsertGroup(ctx context.Context, groups []*dto.CreateGroupDaoParam) error {
	ug := query.Use(u.data.db).TbUserGroup
	var groupsToInsert []*model.TbUserGroup
	for _, g := range groups {
		groupsToInsert = append(groupsToInsert, &model.TbUserGroup{
			ID:            g.Id,
			Name:          g.Name,
			Description:   g.Description,
			ParentGroupID: g.ParentGroupId,
			Path:          g.Path,
			CorpID:        g.CorpId,
			SourceID:      g.SourceId,
			RootGroupID:   g.RootGroupID,
		})
	}
	return ug.WithContext(ctx).Create(groupsToInsert...)
}

func (u userGroupRepo) GetUserGroupByNameInParent(ctx context.Context, corpId, name, parentGroupId string) (*model.TbUserGroup, error) {
	ug := query.Use(u.data.db).TbUserGroup
	result, err := ug.WithContext(ctx).Where(ug.ParentGroupID.Eq(parentGroupId), ug.Name.Eq(name), ug.CorpID.Eq(corpId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserGroup{}, pb.ErrorRecordNotFound("corpId=%v, name=%v, groupId=%v not found.", corpId, name, parentGroupId)
		}
		return &model.TbUserGroup{}, err
	}
	return result, nil
}

func (u userGroupRepo) GetUserGroup(ctx context.Context, corpId, id string) (*model.TbUserGroup, error) {
	ug := query.Use(u.data.db).TbUserGroup
	result, err := ug.WithContext(ctx).Where(ug.CorpID.Eq(corpId), ug.ID.Eq(id)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserGroup{}, pb.ErrorRecordNotFound("corpId=%v, groupId=%v not found", corpId, id)
		}
		return &model.TbUserGroup{}, err
	}
	return result, nil
}

func (u userGroupRepo) ListUserGroup(ctx context.Context, corpId string) ([]*model.TbUserGroup, error) {
	ug := query.Use(u.data.db).TbUserGroup
	return ug.WithContext(ctx).Where(ug.CorpID.Eq(corpId)).Find()
}

func (u userGroupRepo) ListUserGroupByGroupIds(ctx context.Context, corpId string, groupIds []string) ([]*model.TbUserGroup, error) {
	ug := query.Use(u.data.db).TbUserGroup
	return ug.WithContext(ctx).Where(ug.CorpID.Eq(corpId), ug.ID.In(groupIds...)).Find()
}

func (u userGroupRepo) ListUserGroupByParGroupId(ctx context.Context, corpId string, parGroupIds []string) ([]*model.TbUserGroup, error) {
	ug := query.Use(u.data.db).TbUserGroup
	return ug.WithContext(ctx).Where(ug.CorpID.Eq(corpId), ug.ParentGroupID.In(parGroupIds...)).Find()
}

func (u userGroupRepo) ListGroupNodes(ctx context.Context, corpId string) ([]*dto.UserGroupNode, error) {
	var result []*dto.UserGroupNode
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	us := q.TbUserSource
	err := ug.WithContext(ctx).LeftJoin(us, us.ID.EqCol(ug.SourceID)).
		Where(ug.CorpID.Eq(corpId)).
		Select(ug.ID.As("Id"), ug.Name, ug.ParentGroupID.As("ParentGroupId"), ug.Description,
			ug.CorpID.As("CorpId"), ug.SourceID.As("SourceId"), ug.IsDefault,
			us.SourceType, us.TemplateType, us.Name.As("SourceName"), ug.CreatedAt,
		).Order(ug.CreatedAt.Desc()).Scan(&result)
	return result, err
}

func (u userGroupRepo) ListUserGroupWithIDP(ctx context.Context, corpId string) ([]dto.UserGroupWithIDP, error) {
	var result []dto.UserGroupWithIDP
	q := query.Use(u.data.db)
	ug := q.TbUserGroup
	idp := q.TbIdentityProvider
	mapper := q.TbIdpGroupMapper
	err := ug.WithContext(ctx).Join(mapper, ug.ID.EqCol(mapper.GroupID)).Join(idp, idp.ID.EqCol(mapper.ProviderID)).
		Where(ug.CorpID.Eq(corpId)).Select(ug.ALL, idp.ID.As("IDPId"), idp.Name.As("IDPName")).Scan(&result)
	return result, err
}

func (u userGroupRepo) GetIDPIdsOfGroup(ctx context.Context, corpId, id string) ([]string, error) {
	mapper := query.Use(u.data.db).TbIdpGroupMapper
	var result []string
	err := mapper.WithContext(ctx).Where(mapper.CorpID.Eq(corpId), mapper.GroupID.Eq(id)).Select(mapper.ProviderID).Scan(&result)
	return result, err
}

func NewUserGroupRepo(data *Data, logger log.Logger) biz.UserGroupRepo {
	return &userGroupRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
