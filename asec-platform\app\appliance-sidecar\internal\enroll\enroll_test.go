package enroll

import (
	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/machine"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/network"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestAppEnroll(t *testing.T) {
	type args struct {
		ctx     context.Context
		appType v1.ApplianceType
	}
	tests := []struct {
		name    string
		args    args
		want    uint64
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := AppEnroll(tt.args.ctx, tt.args.appType, 0)
			if (err != nil) != tt.wantErr {
				t.Errorf("AppEnroll() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AppEnroll() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWriteIDFile(t *testing.T) {
	writeIDFile("1.1.1.1", uint64(12342312313))
	tmp, _ := global.FromIDFile(filepath.Join(utils.GetConfigDir(), ".appliance-id"), "1.1.1.1")
	t.Log("====", tmp)
}

func TestWD(t *testing.T) {
	d, _ := os.Getwd()
	fmt.Print(d)
	fmt.Print(GetCurrentDirectory())
}

func GetCurrentDirectory() string {
	//返回绝对路径  filepath.Dir(os.Args[0])去除最后一个元素的路径
	dir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
	}

	//将\替换成/
	return strings.Replace(dir, "\\", "/", -1)
}

func TestGetMac(t *testing.T) {
	t.Log(network.GetMac())
	t.Log(GetCurUser())
	t.Log(global.ApplianceName)
}

func TestGetCpuId(t *testing.T) {
	serialNumber, err := machine.GetSerialNumber()
	if err != nil {
		t.Log(err.Error())
	}
	t.Log("serialNumber = ", serialNumber)

	uuid, err := machine.GetPlatformUUID()
	if err != nil {
		t.Log(err.Error())
	}
	t.Log("uuid = ", uuid)

	cpuid, err := machine.GetCpuId()
	if err != nil {
		t.Log(err.Error())
	}
	t.Log("cpuid = ", cpuid)

	macInfo, err := machine.GetMACAddress()
	if err != nil {
		t.Log(err.Error())
	}
	t.Log("mac = ", macInfo)
}

func TestGetPublicIP(t *testing.T) {

	resp, err := http.Get("http://ifconfig.co")

	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(string(body))

}

func TestGetPrivateIP(t *testing.T) {
	// Get a list of all network interfaces on the system
	ifaces, err := net.Interfaces()
	if err != nil {
		fmt.Println("Error: ", err)
		return
	}

	// Loop through the interfaces
	for _, i := range ifaces {
		addrs, err := i.Addrs()
		// Get a list of addresses for each interface
		if err != nil {
			fmt.Println("Error: ", err)
			continue
		}

		fmt.Printf("名称:%s IP地址: %v ,FlagUp标记 %v \n", i.Name, addrs, i.Flags)
		if i.Flags&(net.FlagUp|net.FlagLoopback|net.FlagPointToPoint) != net.FlagUp {
			continue
		}

		// Loop through the addresses
		for _, addr := range addrs {
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}

			// Check if the address is a valid IP address and is not a loopback address
			if ip != nil && !ip.IsLoopback() && ip.To4() != nil {
				// Print the IP address
				fmt.Println(ip.String())
			}
		}

	}
}

func TestGetOutboundIP(t *testing.T) {
	conn, err := net.Dial("udp", "*************:443")
	if err != nil {
		fmt.Errorf("%s", err.Error())
	}
	defer conn.Close()
	fmt.Printf("%s", conn.LocalAddr().(*net.UDPAddr).IP.String())

}
