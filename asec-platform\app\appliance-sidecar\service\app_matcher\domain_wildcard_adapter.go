package app_matcher

import (
	pb "asdsec.com/asec/platform/api/application/v1"
	"strings"
)

type DomainWildcardAdapter struct {
	baseMatcher BaseMatcher
}

func (d DomainWildcardAdapter) GetAppInfo() *pb.AppInfo {
	return d.baseMatcher.AppInfo
}

func (d DomainWildcardAdapter) GetMatcherType() UrlMatcherType {
	return DomainWildcardMatcher
}

func (d DomainWildcardAdapter) DoMatcher(srcUrl string, srcPort int) error {
	if !(strings.HasSuffix(srcUrl, "."+d.baseMatcher.UrlRule) || srcUrl == d.baseMatcher.UrlRule) {
		return d.baseMatcher.NoMatchError(srcUrl, srcPort, "wildcard not match")
	}
	matchPort, detail := d.baseMatcher.CommonMatchPort(srcPort)
	if !matchPort {
		return d.baseMatcher.NoMatchError(srcUrl, srcPort, detail)
	}
	return nil
}

func NewDomainWildcardAdapter(appInfo *pb.AppInfo) (DomainWildcardAdapter, error) {
	split := strings.Split(appInfo.Address, "*.")
	urlRule := split[1]
	ps, err := GetPortMatchers(appInfo.Port)
	if err != nil {
		return DomainWildcardAdapter{}, err
	}

	return DomainWildcardAdapter{
		baseMatcher: BaseMatcher{
			UrlRule:  urlRule,
			PortRule: ps,
			AppInfo:  appInfo,
		},
	}, nil

}
