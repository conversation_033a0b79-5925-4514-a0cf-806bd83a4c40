package model

import "github.com/lib/pq"

type Application struct {
	TenantModel
	AppName      string       `gorm:"column:app_name;type:varchar;comment:应用名称" json:"app_name"`
	AppDescribe  string       `gorm:"column:app_describe;type:varchar;comment:应用描述" json:"app_desc"`
	WebUrl       string       `gorm:"column:web_url;type:varchar;comment:web入口" json:"web_url"`
	IconURL      string       `gorm:"column:icon_url;type:varchar;comment:图标url" json:"icon_url"`
	AppStatus    int          `gorm:"column:app_status;type:int4;comment:应用状态(1启用/2维护/3禁用)" json:"app_status"`
	GroupName    string       `gorm:"->;-:migration" json:"group_name"`
	AppAddresses []AppAddress `gorm:"foreignKey:AppId;constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"app_sites"`

	GroupIds            pq.Int64Array `gorm:"column:group_ids;type:varchar;comment:应用标签" json:"group_ids"`
	ServerAddress       string        `gorm:"column:server_address;type:varchar;comment:服务器地址" json:"server_address"`
	ServerSchema        string        `gorm:"column:server_schema;type:varchar;comment:服务器地址协议" json:"server_schema"`
	PublishAddress      string        `gorm:"column:publish_address;type:varchar;comment:发布地址" json:"publish_address"`
	PublishSchema       string        `gorm:"column:publish_schema;type:varchar;comment:发布地址协议" json:"publish_schema"`
	WebCompatibleConfig []byte        `gorm:"column:web_compatible_config;type:jsonb;comment:web兼容性配置" json:"web_compatible_config"`
	AppType             string        `gorm:"app_type" json:"app_type"`
	GatewayRs           []byte        `gorm:"gateway_rs;type:jsonb" json:"gateway_rs"`
	Uri                 string        `gorm:"column:uri;type:varchar;comment:应用地址路径" json:"uri"`
	IdpId               string        `gorm:"column:idp_id;type:varchar;comment:二次认证idp" json:"idp_id"`
	Maintenance         int           `gorm:"column:maintenance;type:int4;comment:维护状态" json:"maintenance"`
	HealthConfig        []byte        `gorm:"column:health_config;type:jsonb;comment:健康检查配置" json:"health_config"`
	OpenConfig          []byte        `gorm:"column:open_config;type:jsonb;comment:打开方式" json:"open_config"`
	ShowStatus          int           `gorm:"column:show_status;type:int4;comment:应用展示状态" json:"show_status"`
	HealthStatus        int           `gorm:"column:health_status;type:int4;comment:健康状态" json:"health_status"`
	PortalShowName      string        `gorm:"column:portal_show_name;type:varchar;comment:应用展示名称" json:"portal_show_name"`
	PortalDesc          string        `gorm:"column:portal_desc;type:varchar;comment:应用展描述" json:"portal_desc"`
	FormFillEnabled     int           `gorm:"column:form_fill_enabled;type:int4;default:0;comment:表单代填启用状态(0禁用/1启用)" json:"form_fill_enabled"`
	CertificateID       *string       `gorm:"column:certificate_id;type:varchar;comment:SSL证书ID" json:"certificate_id"`
}

func (Application) TableName() string {
	return "tb_application"
}
