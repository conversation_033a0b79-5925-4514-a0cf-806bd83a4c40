// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.28.0
// source: agentcontrol/v1/agent_control.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AgentControl_PullTask_FullMethodName         = "/api.asdsec.agentcontrol.AgentControl/PullTask"
	AgentControl_ReportState_FullMethodName      = "/api.asdsec.agentcontrol.AgentControl/ReportState"
	AgentControl_FileUpload_FullMethodName       = "/api.asdsec.agentcontrol.AgentControl/FileUpload"
	AgentControl_PullPlatAddr_FullMethodName     = "/api.asdsec.agentcontrol.AgentControl/PullPlatAddr"
	AgentControl_PullModuleSwitch_FullMethodName = "/api.asdsec.agentcontrol.AgentControl/PullModuleSwitch"
	AgentControl_GetModuleSwitch_FullMethodName  = "/api.asdsec.agentcontrol.AgentControl/GetModuleSwitch"
)

// AgentControlClient is the client API for AgentControl service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgentControlClient interface {
	// 拉取平台任务
	PullTask(ctx context.Context, in *TaskPullReq, opts ...grpc.CallOption) (*TaskPullResp, error)
	// 上报任务状态
	ReportState(ctx context.Context, in *TaskStateReq, opts ...grpc.CallOption) (*TaskStateResp, error)
	// 文件上传
	FileUpload(ctx context.Context, in *FileUploadReq, opts ...grpc.CallOption) (*FileUploadResp, error)
	// 获取平台地址
	PullPlatAddr(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*PlatAddrResp, error)
	// 拉取模块开关
	PullModuleSwitch(ctx context.Context, in *ModuleSwitchReq, opts ...grpc.CallOption) (*ModuleSwitchResp, error)
	GetModuleSwitch(ctx context.Context, in *ModuleSwitchReq, opts ...grpc.CallOption) (*ModuleSwitchResp, error)
}

type agentControlClient struct {
	cc grpc.ClientConnInterface
}

func NewAgentControlClient(cc grpc.ClientConnInterface) AgentControlClient {
	return &agentControlClient{cc}
}

func (c *agentControlClient) PullTask(ctx context.Context, in *TaskPullReq, opts ...grpc.CallOption) (*TaskPullResp, error) {
	out := new(TaskPullResp)
	err := c.cc.Invoke(ctx, AgentControl_PullTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentControlClient) ReportState(ctx context.Context, in *TaskStateReq, opts ...grpc.CallOption) (*TaskStateResp, error) {
	out := new(TaskStateResp)
	err := c.cc.Invoke(ctx, AgentControl_ReportState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentControlClient) FileUpload(ctx context.Context, in *FileUploadReq, opts ...grpc.CallOption) (*FileUploadResp, error) {
	out := new(FileUploadResp)
	err := c.cc.Invoke(ctx, AgentControl_FileUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentControlClient) PullPlatAddr(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*PlatAddrResp, error) {
	out := new(PlatAddrResp)
	err := c.cc.Invoke(ctx, AgentControl_PullPlatAddr_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentControlClient) PullModuleSwitch(ctx context.Context, in *ModuleSwitchReq, opts ...grpc.CallOption) (*ModuleSwitchResp, error) {
	out := new(ModuleSwitchResp)
	err := c.cc.Invoke(ctx, AgentControl_PullModuleSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentControlClient) GetModuleSwitch(ctx context.Context, in *ModuleSwitchReq, opts ...grpc.CallOption) (*ModuleSwitchResp, error) {
	out := new(ModuleSwitchResp)
	err := c.cc.Invoke(ctx, AgentControl_GetModuleSwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgentControlServer is the server API for AgentControl service.
// All implementations must embed UnimplementedAgentControlServer
// for forward compatibility
type AgentControlServer interface {
	// 拉取平台任务
	PullTask(context.Context, *TaskPullReq) (*TaskPullResp, error)
	// 上报任务状态
	ReportState(context.Context, *TaskStateReq) (*TaskStateResp, error)
	// 文件上传
	FileUpload(context.Context, *FileUploadReq) (*FileUploadResp, error)
	// 获取平台地址
	PullPlatAddr(context.Context, *Empty) (*PlatAddrResp, error)
	// 拉取模块开关
	PullModuleSwitch(context.Context, *ModuleSwitchReq) (*ModuleSwitchResp, error)
	GetModuleSwitch(context.Context, *ModuleSwitchReq) (*ModuleSwitchResp, error)
	mustEmbedUnimplementedAgentControlServer()
}

// UnimplementedAgentControlServer must be embedded to have forward compatible implementations.
type UnimplementedAgentControlServer struct {
}

func (UnimplementedAgentControlServer) PullTask(context.Context, *TaskPullReq) (*TaskPullResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PullTask not implemented")
}
func (UnimplementedAgentControlServer) ReportState(context.Context, *TaskStateReq) (*TaskStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportState not implemented")
}
func (UnimplementedAgentControlServer) FileUpload(context.Context, *FileUploadReq) (*FileUploadResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FileUpload not implemented")
}
func (UnimplementedAgentControlServer) PullPlatAddr(context.Context, *Empty) (*PlatAddrResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PullPlatAddr not implemented")
}
func (UnimplementedAgentControlServer) PullModuleSwitch(context.Context, *ModuleSwitchReq) (*ModuleSwitchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PullModuleSwitch not implemented")
}
func (UnimplementedAgentControlServer) GetModuleSwitch(context.Context, *ModuleSwitchReq) (*ModuleSwitchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModuleSwitch not implemented")
}
func (UnimplementedAgentControlServer) mustEmbedUnimplementedAgentControlServer() {}

// UnsafeAgentControlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgentControlServer will
// result in compilation errors.
type UnsafeAgentControlServer interface {
	mustEmbedUnimplementedAgentControlServer()
}

func RegisterAgentControlServer(s grpc.ServiceRegistrar, srv AgentControlServer) {
	s.RegisterService(&AgentControl_ServiceDesc, srv)
}

func _AgentControl_PullTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskPullReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentControlServer).PullTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentControl_PullTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentControlServer).PullTask(ctx, req.(*TaskPullReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentControl_ReportState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentControlServer).ReportState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentControl_ReportState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentControlServer).ReportState(ctx, req.(*TaskStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentControl_FileUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FileUploadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentControlServer).FileUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentControl_FileUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentControlServer).FileUpload(ctx, req.(*FileUploadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentControl_PullPlatAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentControlServer).PullPlatAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentControl_PullPlatAddr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentControlServer).PullPlatAddr(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentControl_PullModuleSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentControlServer).PullModuleSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentControl_PullModuleSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentControlServer).PullModuleSwitch(ctx, req.(*ModuleSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentControl_GetModuleSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentControlServer).GetModuleSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentControl_GetModuleSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentControlServer).GetModuleSwitch(ctx, req.(*ModuleSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AgentControl_ServiceDesc is the grpc.ServiceDesc for AgentControl service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgentControl_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.asdsec.agentcontrol.AgentControl",
	HandlerType: (*AgentControlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PullTask",
			Handler:    _AgentControl_PullTask_Handler,
		},
		{
			MethodName: "ReportState",
			Handler:    _AgentControl_ReportState_Handler,
		},
		{
			MethodName: "FileUpload",
			Handler:    _AgentControl_FileUpload_Handler,
		},
		{
			MethodName: "PullPlatAddr",
			Handler:    _AgentControl_PullPlatAddr_Handler,
		},
		{
			MethodName: "PullModuleSwitch",
			Handler:    _AgentControl_PullModuleSwitch_Handler,
		},
		{
			MethodName: "GetModuleSwitch",
			Handler:    _AgentControl_GetModuleSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "agentcontrol/v1/agent_control.proto",
}
