package cmd_exec

import (
	"bufio"
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"os/exec"
	"time"
)

func ExecuteCmdToFile(cmdStr string, outputPath string) error {
	// 创建一个执行命令的命令行对象
	cmd := exec.Command("cmd", "/c", cmdStr)

	// 打开输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %s", err.Error())
	}
	defer outputFile.Close()

	// 获取命令输出管道
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to get command stdout: %s", err.Error())
	}

	// 创建一个 Scanner 读取标准输出管道中的行
	scanner := bufio.NewScanner(stdout)
	go func() {
		for scanner.Scan() {
			line := scanner.Text()
			// 将输出行写入文件
			_, err := fmt.Fprintln(outputFile, line)
			if err != nil {
				fmt.Printf("failed to write output to file: %s\n", err.Error())
				return
			}
		}
	}()

	// 启动命令
	err = cmd.Start()
	if err != nil {
		return fmt.Errorf("failed to start command: %s", err.Error())
	}

	// 等待命令执行完成
	err = cmd.Wait()
	if err != nil {
		return fmt.Errorf("command failed: %s", err.Error())
	}

	return nil
}

var tmpBatFile = GetConfigDir() + "\\cmd.bat"

func ExecuteCmdWriteBat(cmdStr string, outputPath string) error {
	cmdByte, err := base64.StdEncoding.DecodeString(cmdStr)
	if err != nil {
		return fmt.Errorf("failed to create output file: %s", err.Error())
	}
	os.WriteFile(tmpBatFile, cmdByte, 0664)
	// 删除该文件
	defer os.Remove(tmpBatFile)

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	cmd := exec.CommandContext(ctx, tmpBatFile)

	// 打开输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %s", err.Error())
	}
	defer outputFile.Close()

	// 获取命令输出管道
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to get command stdout: %s", err.Error())
	}

	// 创建一个 Scanner 读取标准输出管道中的行
	scanner := bufio.NewScanner(stdout)
	go func() {
		for scanner.Scan() {
			line := scanner.Text()
			// 将输出行写入文件
			_, err := fmt.Fprintln(outputFile, line)
			if err != nil {
				fmt.Printf("failed to write output to file: %s\n", err.Error())
				return
			}
		}
	}()

	// 启动命令
	err = cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to start command: %s", err.Error())
	}

	return nil
}
