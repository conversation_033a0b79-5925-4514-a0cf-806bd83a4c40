// 检测是否为QT环境 - 通过HTML元素的class判断
const isQT = document.documentElement.classList.contains('qt-env')
// 引入基础组件
import BaseComponents, { Loading } from '@/components/base'
let loadingInstance = null

if (isQT) {
  loadingInstance = Loading.service({
    fullscreen: true,
    text: ''
  })
}

import { createApp } from 'vue'
import './style/element_visiable.scss'
import './style/base.css'
import './style/menu.css'
import './style/browser-compatibility.css'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import clickOutside from '@/directive/clickOutside'
import { store } from '@/pinia'
import App from './App.vue'
// 轻量级 SVG 图标替代 iconfont
import loadSvgIcons from '@/assets/icons'
// 防拖拽工具函数
import { initGlobalPreventDrag } from '@/utils/preventDrag'

logger.log(navigator.userAgent)
logger.log(document.location.href)

/**
 * 无需在这块结束，会在路由中间件中结束此块内容
 * */

// 检测浏览器是否为IE
const isIE = /msie|trident/i.test(navigator.userAgent)
// 如果是IE浏览器，则提示不支持
if (isIE) {
  const unsupportedMessage = `
    对不起，您正在使用的浏览器版本过低。
    本网站不支持IE浏览器，请使用现代浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。
  `
  alert(unsupportedMessage)
}

const app = createApp(App)
app.config.productionTip = false

// 加载轻量级 SVG 图标
loadSvgIcons()

// 初始化防拖拽功能 - 防止图标等元素被拖动到本地变成文件
initGlobalPreventDrag()

// 禁用 Ctrl+鼠标滚轮缩放功能 - 防止客户端页面缩放导致布局异常
function preventZoom() {
  // 阻止 Ctrl+滚轮缩放
  document.addEventListener('wheel', function(e) {
    if (e.ctrlKey) {
      e.preventDefault()
    }
  }, { passive: false })

  // 阻止 Ctrl+加号/减号缩放
  document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '=' || e.key === '0')) {
      e.preventDefault()
    }
  })

  // 阻止双指缩放手势（触摸屏设备）
  document.addEventListener('gesturestart', function(e) {
    e.preventDefault()
  })

  document.addEventListener('gesturechange', function(e) {
    e.preventDefault()
  })

  document.addEventListener('gestureend', function(e) {
    e.preventDefault()
  })
}

// 客户端初始化防缩放功能
if (isQT) {
  preventZoom()
}

// 挂载应用
app
  .use(run)
  .use(store)
  .use(auth)
  .use(clickOutside)
  .use(router)
  .use(BaseComponents)
  .mount('#app_container')

// 初始化登录同步功能
import { initializeLoginSync } from '@/utils/loginSync'

// 应用挂载完成后初始化登录同步
setTimeout(async() => {
  try {
    await initializeLoginSync({
      port: 50001
    })
    logger.log('登录同步功能已启动')
  } catch (error) {
    logger.log('登录同步功能启动失败:', error)
  }
}, 10) // 延迟10毫秒

// 应用挂载完成后的处理 - 只在QT环境中执行过渡效果
if (isQT) {
  setTimeout(() => {
    logger.log('QT环境：开始页面切换过渡效果')

    // 等待一帧确保DOM渲染完成
    requestAnimationFrame(() => {
      const appContainer = document.getElementById('app_container')
      const appLoading = document.getElementById('app_loading')

      if (appContainer && appLoading) {
        logger.log('QT环境：找到页面元素，开始切换')

        // 第一步：显示app_container但保持透明
        appContainer.style.display = 'block'

        // 第二步：等待两帧确保布局稳定
        requestAnimationFrame(() => {
          logger.log('QT环境：开始淡入淡出效果')

          // 同时开始淡入app_container和淡出app_loading
          appContainer.classList.add('fade-in')
          appLoading.classList.add('fade-out')

          // 第三步：等待过渡动画完成后移除app_loading
          setTimeout(() => {
            if (appLoading && appLoading.parentNode) {
              logger.log('QT环境：移除加载页面')
              appLoading.parentNode.removeChild(appLoading)
              // 关闭loading后再返回
              loadingInstance && loadingInstance.close()
            }
          }, 1) // 稍微延长一点时间确保动画完成
        })
      } else {
        console.error('QT环境：未找到页面元素:', { appContainer, appLoading })
      }
    })
  }, 1) // 稍微延长等待时间确保Vue应用完全渲染
} else {
  logger.log('浏览器环境：跳过加载页面过渡效果')
}

export default app
