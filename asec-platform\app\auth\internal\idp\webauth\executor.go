package webauth

import (
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
)

// StepExecutor 多步请求执行器
type StepExecutor struct {
	client     *http.Client
	logger     *log.Helper
	steps      []Step
	userInfo   map[string]interface{}
	globalVars map[string]string
	logs       []string
	envVars    map[string]string
	ctx        context.Context // 添加上下文字段
}

// NewStepExecutor 创建多步请求执行器
func NewStepExecutor(logger log.Logger) *StepExecutor {
	return &StepExecutor{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true}, // 忽略证书验证
			},
		},
		logger:     log.NewHelper(logger),
		userInfo:   make(map[string]interface{}),
		globalVars: make(map[string]string),
		logs:       make([]string, 0),
		envVars:    make(map[string]string), // 初始化环境变量映射
	}
}

// SetEnvVar 设置环境变量
func (e *StepExecutor) SetEnvVar(key, value string) {
	e.envVars[key] = value
}

// SetGlobalVars 设置全局变量
func (e *StepExecutor) SetGlobalVars(vars map[string]string) {
	e.globalVars = vars
}

// SetSteps 设置执行步骤
func (e *StepExecutor) SetSteps(steps []Step) {
	e.steps = steps
}

// GetUserInfo 获取收集到的用户信息
func (e *StepExecutor) GetUserInfo() map[string]interface{} {
	return e.userInfo
}

// GetALLVariables 获取所有变量
func (e *StepExecutor) GetAllVariables() map[string]string {
	return e.getVariables()
}

// GetEnvVars 返回当前执行器中的环境变量
func (e *StepExecutor) GetEnvVars() map[string]string {
	return e.envVars
}

// GetGlobalVars 返回当前执行器中的全局变量
func (e *StepExecutor) GetGlobalVars() map[string]string {
	return e.globalVars
}

// GetLogs 获取执行日志
func (e *StepExecutor) GetLogs() []string {
	return e.logs
}

// Execute 执行所有步骤
func (e *StepExecutor) Execute(ctx context.Context) error {
	e.ctx = ctx
	for i, step := range e.steps {
		e.logStep(i + 1)

		if err := e.executeStep(ctx, step); err != nil {
			e.logger.Errorf("步骤 %d 执行失败: %v", i+1, err)
			return err
		}

		e.logger.Infof("步骤 %d 执行成功", i+1)
		e.logUserInfo()
	}

	return nil
}

// 预处理步骤中的变量
func (e *StepExecutor) PreprocessStep(step Step) Step {
	stepCopy := step
	variables := e.getAllVariables(e.globalVars)

	// 处理请求体
	if stepCopy.Input.Body != "" {
		original := stepCopy.Input.Body
		stepCopy.Input.Body = e.replaceAllVariables(stepCopy.Input.Body, variables)
		if original != stepCopy.Input.Body {
			e.logger.Infof("请求体变量替换: %s -> %s", original, stepCopy.Input.Body)
		}
	}

	// 处理URL参数
	for j, param := range stepCopy.Input.Gets {
		stepCopy.Input.Gets[j].Value = e.replaceAllVariables(param.Value, variables)
	}

	// 处理请求头
	for j, header := range stepCopy.Input.Headers {
		stepCopy.Input.Headers[j].Value = e.replaceAllVariables(header.Value, variables)
	}

	// 处理Cookie
	for j, cookie := range stepCopy.Input.Cookies {
		stepCopy.Input.Cookies[j].Value = e.replaceAllVariables(cookie.Value, variables)
	}

	// 处理表单数据
	for j, form := range stepCopy.Input.Formdata {
		stepCopy.Input.Formdata[j].Value = e.replaceAllVariables(form.Value, variables)
	}

	return stepCopy
}

// 获取所有可用变量
func (e *StepExecutor) getAllVariables(globalVars map[string]string) map[string]string {
	variables := make(map[string]string)

	// 添加全局变量
	for k, v := range globalVars {
		variables["Global."+k] = v
	}

	// 添加用户变量
	userInfo := e.GetUserInfo()
	for k, v := range userInfo {
		if strVal, ok := v.(string); ok {
			variables["User."+k] = strVal
		}
	}

	return variables
}

func (e *StepExecutor) replaceAllVariables(content string, variables map[string]string) string {
	// 如果内容为空，直接返回
	if content == "" {
		return content
	}

	result := content

	// 遍历所有变量
	for key, value := range variables {
		// 替换所有 {变量名} 为实际值
		placeholder := "{" + key + "}"
		result = strings.Replace(result, placeholder, value, -1)
	}

	return result
}

// ExecuteStepOne 执行单个配置步骤
func (e *StepExecutor) ExecuteStepOne(ctx context.Context, step Step, index int) error {
	// 直接记录正确的步骤索引
	e.logStep(index + 1)

	// 直接执行步骤
	e.logger.Infof("执行OAuth2认证步骤 %d: %s %s", index+1, step.Method, step.URL)
	return e.executeStep(ctx, step)
}

// ConvertToExternalUser 将收集到的用户信息转换为标准格式
func (e *StepExecutor) ConvertToExternalUser() *dto.ExternalUser {
	// 获取执行器中收集的用户信息
	userInfo := e.GetUserInfo()
	e.logger.Infof("转换用户信息: %v", userInfo)

	// 创建外部用户对象
	externalUser := &dto.ExternalUser{}

	// 尝试获取不同格式的字段值
	externalUser.Userid = e.extractStringField(userInfo, "Userid")
	externalUser.NickName = e.extractStringField(userInfo, "Nickname")
	externalUser.Name = e.extractStringField(userInfo, "Name")
	externalUser.DisplayName = e.extractStringField(userInfo, "DisplayName")
	externalUser.TrueName = e.extractStringField(userInfo, "RealName")
	externalUser.Email = e.extractStringField(userInfo, "Email")
	externalUser.Mobile = e.extractStringField(userInfo, "Mobile")
	externalUser.MainDepartment = e.extractStringField(userInfo, "MainDepartment")
	externalUser.LocalUserID = e.extractStringField(userInfo, "LocalUserID")
	externalUser.LocalRootGroupID = e.extractStringField(userInfo, "LocalRootGroupID")

	// 生成唯一键
	externalUser.UniqKey = e.generateUniqueKey(externalUser)

	// e.logger.Infof("转换后的用户信息: %v", externalUser)
	return externalUser
}

// generateUniqueKey 生成用户唯一标识
func (e *StepExecutor) generateUniqueKey(user *dto.ExternalUser) string {
	// 实现生成用户唯一标识的逻辑
	return fmt.Sprintf("%s:%s:%s", user.Userid, user.Name, user.Email)
}

// extractStringField 以统一方式从userInfo中提取字符串字段
func (e *StepExecutor) extractStringField(userInfo map[string]interface{}, fieldName string) string {
	// e.logger.Infof("正在从userInfo中提取字段: %s", fieldName)

	// 首先尝试标准格式 {User.XXX}
	key := "{User." + fieldName + "}"
	if val, exists := userInfo[key]; exists {
		// 类型转换为字符串
		return e.convertToString(val)
	}

	// 然后尝试不带花括号的格式 (有些实现可能直接用字段名)
	if val, exists := userInfo[fieldName]; exists {
		// 类型转换为字符串
		return e.convertToString(val)
	}

	return ""
}

// convertToString 将任意类型转换为字符串
func (e *StepExecutor) convertToString(val interface{}) string {
	if val == nil {
		return ""
	}

	switch v := val.(type) {
	case string:
		return v
	case float64:
		// 对于整数形式的浮点数，去掉小数部分
		if v == float64(int64(v)) {
			return fmt.Sprintf("%d", int64(v))
		}
		return fmt.Sprintf("%g", v)
	case int, int64, int32, int8:
		return fmt.Sprintf("%d", v)
	case bool:
		return fmt.Sprintf("%t", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// executeStep 执行单个步骤
func (e *StepExecutor) executeStep(ctx context.Context, step Step) error {
	e.logf("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	e.logf("┃ 执行步骤开始: %s %s", step.Method, step.URL)
	e.logf("┣━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// 1. 获取变量
	variables := e.getVariables()
	e.logAllVariables(variables)

	// 2. 处理输入脚本
	if step.Input.OpenScript && step.Input.Script != "" {
		e.logf("┃ [脚本] 执行输入处理脚本")
		env := map[string]interface{}{
			"user":   e.userInfo,
			"global": e.globalVars,
		}

		if err := e.executeInputScript(step.Input.Script, env, &step); err != nil {
			e.logf("┃ [预处理脚本] 脚本执行失败: %v", err)
			e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
			return err
		}
		e.logf("│ [预处理脚本] 脚本执行成功")
	}

	// 3. 构建请求
	req, err := e.buildRequest(ctx, step, variables)
	if err != nil {
		e.logf("┃ [构建请求]  构建请求失败: %v", err)
		e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
		return err
	}

	e.logf("┃")
	e.logf("┃ [请求] %s %s", step.Method, req.URL.String())

	// 记录请求头信息(减少缩进)
	if len(req.Header) > 0 {
		e.logf("┃ [请求] 请求头:")

		// 收集并排序所有请求头键名
		headerKeys := make([]string, 0, len(req.Header))
		for key := range req.Header {
			headerKeys = append(headerKeys, key)
		}
		sort.Strings(headerKeys)

		// 按排序后的顺序记录请求头
		for _, key := range headerKeys {
			e.logf("┃       %s: %s", key, strings.Join(req.Header[key], ", "))
		}
	}

	// 其他请求信息记录
	if step.Input.BodyType == 1 && len(step.Input.Formdata) > 0 {
		e.logf("┃ [请求] 表单数据类型: application/x-www-form-urlencoded")
	} else if req.Header.Get("Content-Type") != "" {
		e.logf("┃ [请求] 内容类型: %s", req.Header.Get("Content-Type"))
	}

	if step.Input.Body != "" {
		e.logf("┃ [请求] 请求体: %s", e.replaceVariables(step.Input.Body, variables))
	}

	e.logf("┃")
	e.logf("┃ [发送] 正在发送请求...")
	// 4. 发送请求
	e.client.Timeout = time.Duration(step.Timeout) * time.Second
	resp, err := e.client.Do(req)
	if err != nil {
		e.logf("┃ [发送]  请求失败: %v", err)
		e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
		return err
	}
	defer resp.Body.Close()

	// 5. 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		e.logf("┃ [响应] 读取响应失败: %v", err)
		e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
		return err
	}

	// e.logf("响应内容: %s", string(respBody))
	e.logf("┃")
	e.logf("┃ [响应] 响应状态码: %d, 响应内容长度: %d", resp.StatusCode, len(respBody))

	// e.logf("响应头信息:")
	// for key, vals := range resp.Header {
	// 	for _, val := range vals {
	// 		e.logf("    %s: %s", key, val)
	// 	}
	// }
	e.logf("┃ [响应体] : %s", string(respBody))

	// 处理响应Cookie (调用新的独立函数)
	e.handleResponseCookies(resp, step)

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		e.logf("┃ [响应] 响应失败: %s", string(respBody))
		e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
		return fmt.Errorf("HTTP请求失败, 状态码: %d", resp.StatusCode)
	}

	// 6. 处理响应
	err = e.handleResponse(ctx, step, string(respBody), variables)
	if err != nil {
		e.logf("┃ [处理] 响应处理失败: %v", err)
		e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
		return err
	}

	e.logf("┃ [处理] 响应处理成功")
	e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	return nil
}

// 是否使用get参数作为post普通表单
func (e *StepExecutor) isPostWithGet(step Step) bool {
	// 判断step.Input.Gets中每行数据是否为空，为空则从step.Input.Gets去除
	for i, line := range step.Input.Gets {
		if line.Key == "" && line.Value == "" {
			step.Input.Gets = append(step.Input.Gets[:i], step.Input.Gets[i+1:]...)
		}
	}
	// 判断是否为POST或PUT请求，并且GET参数不为空
	return (step.Method == "POST" || step.Method == "PUT") &&
		len(step.Input.Gets) > 0
}

// buildRequest 构建HTTP请求
func (e *StepExecutor) buildRequest(ctx context.Context, step Step, variables map[string]string) (*http.Request, error) {
	isPostWithGetParams := e.isPostWithGet(step)
	// 调试信息，帮助排查条件判断
	if step.Method == "POST" || step.Method == "PUT" {
		// e.logf("POST/PUT 请求参数条件检查:")
		// e.logf("  - 有GET参数: %v (%d个)", len(step.Input.Gets) > 0, len(step.Input.Gets))
		// e.logf("  - Formdata为空: %v (%d个)", len(step.Input.Formdata) == 0, len(step.Input.Formdata))
		// e.logf("  - isPostWithGetParams结果: %v", isPostWithGetParams)
	}
	var requestURL string
	if isPostWithGetParams {
		// 仅替换URL变量，不添加GET参数
		requestURL = e.replaceVariables(step.URL, variables)
		// e.logf("POST请求:参数将作为表单发送,URL中不添加参数")
	} else {
		// 正常处理URL和GET参数
		requestURL = e.buildURL(step.URL, step.Input.Gets, variables)
	}

	// 构建请求体
	var bodyReader io.Reader
	var contentType string

	if step.Method == "POST" || step.Method == "PUT" {
		if isPostWithGetParams {
			// 处理表单数据
			form := url.Values{}
			skippedParams := 0

			for _, param := range step.Input.Gets {
				if param.Key == "" {
					// e.logf("警告：跳过空键名的参数")
					skippedParams++
					continue
				}

				value := e.replaceVariables(param.Value, variables)

				// 检查是否仍有未替换的变量，但不再跳过，而是进行记录
				if strings.Contains(value, "{User.") || strings.Contains(value, "{Global.") {
					e.logf("┃ [参数转换] 警告：参数值包含未替换的变量，尝试继续处理: %s = %s", param.Key, value)
				}

				// 特殊处理 redirect_uri 参数
				if param.Key == "redirect_uri" {
					// 确保URL格式正确且正确编码
					parsedURL, err := url.Parse(value)
					if err == nil {
						value = parsedURL.String()
					}
				}

				form.Add(param.Key, value)
				// e.logf("将GET参数转为表单字段: %s = %s", param.Key, value)
			}

			bodyReader = strings.NewReader(form.Encode())
			contentType = "application/x-www-form-urlencoded"
			e.logf("┃ [参数转换] 识别到POST请求,将Param参数转换为表单发送: %s (共处理 %d/%d 个参数，跳过 %d 个)",
				form.Encode(), len(form), len(step.Input.Gets), skippedParams)
		} else if step.Input.BodyType == 1 && len(step.Input.Formdata) > 0 {
			// 原来的表单数据处理逻辑
			form := url.Values{}
			for _, param := range step.Input.Formdata {
				value := e.replaceVariables(param.Value, variables)
				form.Add(param.Key, value)
				e.logf("┃ [参数转换] 表单字段: %s = %s", param.Key, value)
			}
			bodyReader = strings.NewReader(form.Encode())
			contentType = "application/x-www-form-urlencoded"
			e.logf("┃ [参数转换] 请求表单: %s", form.Encode())
		} else if step.Input.Body != "" {
			// 处理普通请求体
			bodyContent := e.replaceVariables(step.Input.Body, variables)
			bodyReader = strings.NewReader(bodyContent)
			// e.logf("┃ [参数转换] 请求体: %s", bodyContent)
		}
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, step.Method, requestURL, bodyReader)
	if err != nil {
		return nil, err
	}

	// 添加请求头
	for _, header := range step.Input.Headers {
		// 跳过空的请求头键名
		if header.Key == "" {
			e.logf("┃ [请求头] 警告：跳过空的请求头键名")
			continue
		}

		value := e.replaceVariables(header.Value, variables)

		// 检查值是否正确替换，避免使用未定义的变量
		if strings.Contains(value, "{User.") || strings.Contains(value, "{Global.") {
			e.logf("┃ [请求头] 警告：请求头值包含未替换的变量: %s = %s", header.Key, value)
			continue // 跳过包含未替换变量的请求头
		}

		req.Header.Add(header.Key, value)
		// e.logf("添加请求头: %s = %s", header.Key, value)
	}

	// 如果设置了表单内容类型，添加到头信息
	if contentType != "" {
		// 检查是否已经设置了Content-Type，避免重复
		if req.Header.Get("Content-Type") == "" {
			req.Header.Set("Content-Type", contentType)
		}
	}

	// 添加Cookie (原有代码)
	if len(step.Input.Cookies) > 0 {
		cookieStr := e.buildCookieString(step.Input.Cookies, variables)
		if cookieStr != "" {
			req.Header.Add("Cookie", cookieStr)
		}
	}

	// e.logf("发送请求: %s %s", step.Method, requestURL)
	return req, nil
}

// handleResponse 处理HTTP响应
func (e *StepExecutor) handleResponse(ctx context.Context, step Step, responseBody string, variables map[string]string) error {
	// 1. 解析响应数据
	var respData map[string]interface{}
	var err error

	switch step.Output.Type {
	case 0: // JSON
		if err = json.Unmarshal([]byte(responseBody), &respData); err != nil {
			e.logf("┃ [解析] JSON解析失败: %v", err)
			return err
		}
	case 1: // JWT
		// JWT解析逻辑
		respData, err = parseJWT(responseBody)
		if err != nil {
			e.logf("┃ [解析] JWT解析失败: %v", err)
			return err
		}
	case 2: // XML
		// XML解析逻辑 (实际需要实现)
		respData, err = parseXML(responseBody)
		if err != nil {
			e.logf("┃ [解析] XML解析失败: %v", err)
			return err
		}
	case 3: // Text
		respData = map[string]interface{}{"text": responseBody}
		e.logf("┃ [解析] 以纯文本方式处理响应")
	default:
		e.logf("┃ [解析] 不支持的响应类型: %d", step.Output.Type)
		return fmt.Errorf("不支持的响应类型: %d", step.Output.Type)
	}

	// 2. 处理输出脚本
	if step.Output.OpenScript && step.Output.Script != "" {
		env := map[string]interface{}{
			"response": respData,
			"user":     e.userInfo,
			"global":   e.globalVars,
		}

		if err := e.executeOutputScript(step.Output.Script, env); err != nil {
			e.logf("┃ [脚本] 输出脚本执行失败: %v", err)
			return err
		}
	} else {
		// 3. 使用参数映射
		if len(step.Output.Params) > 0 {
			e.logf("┃ [参数] 处理响应参数映射")
			for _, param := range step.Output.Params {
				value := e.getValueFromPath(respData, param.Key)
				if value != nil {
					// 检查参数名是否已包含花括号
					paramName := param.Select
					if !strings.HasPrefix(paramName, "{") {
						paramName = "{User." + paramName + "}"
					}
					e.userInfo[paramName] = value

					// 对过长的值做截断处理
					valueStr := e.convertToString(value)
					if len(valueStr) > 100 {
						e.logf("┃ [参数] 保存: %s = %s...(共%d字节)", paramName, valueStr[:100], len(valueStr))
					} else {
						e.logf("┃ [参数] 保存: %s = %s", paramName, valueStr)
					}
				} else {
					e.logf("┃ [参数] 未找到: %s", param.Key)
				}
			}
		} else {
			e.logf("┃ [参数] 没有配置参数映射")
		}
	}

	// 4. 验证条件
	if step.Output.Column != "" {
		e.logf("┃ [验证] 检查条件: %s %s %s", step.Output.Column, step.Output.JudgeCond, step.Output.Value)
		if !e.checkCondition(respData, step.Output) {
			e.logf("┃ [验证] 条件验证失败")
			return fmt.Errorf("响应条件验证失败")
		}
		e.logf("┃ [验证] 条件验证通过")
	}

	return nil
}

// 其他辅助方法...
func (e *StepExecutor) buildURL(baseURL string, params []KeyValue, variables map[string]string) string {
	urlStr := e.replaceVariables(baseURL, variables)

	if len(params) == 0 {
		return urlStr
	}

	var queryParams []string
	for _, param := range params {
		if param.Key == "" {
			// e.logf("警告:跳过空键名的URL参数")
			continue
		}
		value := e.replaceVariables(param.Value, variables)
		if strings.Contains(value, "{User.") || strings.Contains(value, "{Global.") {
			e.logf("┃ 警告:URL参数值包含未替换的变量: %s = %s", param.Key, value)
			continue // 跳过包含未替换变量的参数
		}

		queryParams = append(queryParams, fmt.Sprintf("%s=%s",
			param.Key, url.QueryEscape(value)))
	}

	if strings.Contains(urlStr, "?") {
		return urlStr + "&" + strings.Join(queryParams, "&")
	}
	return urlStr + "?" + strings.Join(queryParams, "&")
}

func (e *StepExecutor) buildCookieString(cookies []KeyValue, variables map[string]string) string {
	var parts []string
	for _, cookie := range cookies {
		value := e.replaceVariables(cookie.Value, variables)
		parts = append(parts, fmt.Sprintf("%s=%s", cookie.Key, value))
	}
	return strings.Join(parts, "; ")
}

func (e *StepExecutor) getVariables() map[string]string {
	variables := make(map[string]string)

	// 用户变量 - 处理可能已经包含花括号的键
	for k, v := range e.userInfo {
		// 如果键已经是花括号格式，直接使用
		if strings.HasPrefix(k, "{") && strings.HasSuffix(k, "}") {
			// 去除花括号，添加到变量表中
			key := k[1 : len(k)-1]
			variables[key] = toString(v)
		} else {
			// 否则，添加User.前缀
			variables["User."+k] = toString(v)
		}
	}

	// 全局变量
	for k, v := range e.globalVars {
		variables["Global."+k] = v
	}

	// 环境变量 - 添加系统级动态参数
	now := time.Now()
	variables["Env.IP"] = e.getClientIP()
	variables["Env.Time"] = fmt.Sprintf("%d", now.Unix())
	variables["Env.MTime"] = fmt.Sprintf("%d", now.UnixNano()/int64(time.Millisecond))
	variables["Env.Host"] = e.getServerHost()
	variables["Env.RedirectUri"] = e.getDefaultRedirectURI()

	for k, v := range e.envVars {
		variables["Env."+k] = v
	}

	// 特殊处理回调地址: 优先从envVars获取，然后是全局变量，最后是默认值
	if redirectUri, exists := e.envVars["RedirectUri"]; exists && redirectUri != "" {
		variables["Env.RedirectUri"] = redirectUri
	} else if redirectUri, exists := e.globalVars["redirect_uri"]; exists && redirectUri != "" {
		variables["Env.RedirectUri"] = redirectUri
	} else if redirectUri, exists := e.globalVars["callback_url"]; exists && redirectUri != "" {
		variables["Env.RedirectUri"] = redirectUri
	} else {
		// 如果没有配置，返回默认值
		variables["Env.RedirectUri"] = e.getDefaultRedirectURI()
	}

	return variables
}

func (e *StepExecutor) replaceVariables(content string, variables map[string]string) string {
	// 如果内容为空，直接返回
	if content == "" {
		return content
	}

	result := content

	// 遍历所有变量，查找并替换花括号格式的变量引用
	for k, v := range variables {
		// 只替换格式为 {key} 的变量
		placeholder := "{" + k + "}"
		result = strings.Replace(result, placeholder, v, -1)
	}

	// 检查是否还有未替换的变量（格式为 {User.xxx} 或 {Global.xxx}）
	if strings.Contains(result, "{User.") || strings.Contains(result, "{Global.") {
		// 记录原始值用于日志
		originalValue := result

		// 使用正则表达式查找并替换所有未处理的变量
		re := regexp.MustCompile(`\{(User|Global)\.[^}]+\}`)
		matches := re.FindAllString(result, -1)

		if len(matches) > 0 {
			// 记录找到的未替换变量
			for _, match := range matches {
				e.logf("检测到未替换的变量: %s", match)
			}

			// 替换所有未替换变量为空字符串
			result = re.ReplaceAllString(result, "")
			e.logf("已将未替换的变量替换为空值: %s -> %s", originalValue, result)
		}
	}

	return result
}

// logStep 记录步骤执行信息
func (e *StepExecutor) logStep(index int) {
	stepNames := []string{"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"}
	stepName := fmt.Sprintf("%d", index)
	if index > 0 && index <= len(stepNames) {
		stepName = stepNames[index-1]
	}

	e.logf("")
	e.logf("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	e.logf("┃ 第%s步 - 开始执行", stepName)
	e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// e.logs = append(e.logs, fmt.Sprintf("第%s步请求", stepName))
	e.logger.Infof("第%s步请求", stepName)
}

// logUserInfo 记录当前用户信息
func (e *StepExecutor) logUserInfo() {
	if len(e.userInfo) == 0 {
		return
	}

	e.logf("")
	e.logf("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	e.logf("┃ 当前收集的用户信息")
	e.logf("┣━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// 格式化显示用户信息(每个字段一行)
	for k, v := range e.userInfo {
		// 美化键名
		niceKey := k
		if strings.HasPrefix(k, "{User.") && strings.HasSuffix(k, "}") {
			niceKey = k[6 : len(k)-1] // 去除{User.和}
		}
		e.logf("┃ %s: %v", niceKey, v)
	}

	e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// 保持原有日志记录兼容性
	userInfoBytes, _ := json.MarshalIndent(e.userInfo, "", "  ")
	logMsg := fmt.Sprintf("当前用户信息: \n%s", string(userInfoBytes))
	e.logs = append(e.logs, logMsg)
	e.logger.Infof(logMsg)
}

// logf 格式化记录日志
func (e *StepExecutor) logf(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	e.logs = append(e.logs, msg)
	e.logger.Infof(msg)
}

// executeInputScript 执行输入处理脚本
func (e *StepExecutor) executeInputScript(script string, env map[string]interface{}, step *Step) error {
	e.logf("执行输入脚本: %s", script)

	// 创建表达式引擎
	exprEngine := NewExprEngine()

	// 添加步骤相关变量到环境
	scriptEnv := make(map[string]interface{})
	for k, v := range env {
		scriptEnv[k] = v
	}

	//注册时间变量
	now := time.Now()
	timestamp := now.Format("2006-01-02T15:04:05.000+08:00")
	nonce := fmt.Sprintf("%d0001", now.UnixNano()/int64(time.Millisecond))
	envMap := map[string]interface{}{
		"timestamp":   timestamp,
		"nonce":       nonce,
		"IP":          e.getClientIP(),
		"Time":        fmt.Sprintf("%d", now.Unix()),
		"MTime":       fmt.Sprintf("%d", now.UnixNano()/int64(time.Millisecond)),
		"Host":        e.getServerHost(),
		"RedirectUri": e.getDefaultRedirectURI(),
	}

	// 添加用户设置的环境变量
	for k, v := range e.envVars {
		envMap[k] = v
	}

	scriptEnv["env"] = envMap
	e.envVars["timestamp"] = timestamp
	e.envVars["nonce"] = nonce

	// 添加步骤信息
	stepInfo := map[string]interface{}{
		"url":    step.URL,
		"method": step.Method,
		"input": map[string]interface{}{
			"headers": getKeyValuesAsMap(step.Input.Headers),
			"gets":    getKeyValuesAsMap(step.Input.Gets),
			"cookies": getKeyValuesAsMap(step.Input.Cookies),
			"body":    step.Input.Body,
		},
	}
	scriptEnv["step"] = stepInfo

	// 执行脚本
	result, err := exprEngine.Execute(script, scriptEnv)
	if err != nil {
		e.logf("┃ 脚本执行失败: %v", err)
		return err
	}

	// 处理脚本执行结果
	if resultMap, ok := result.(map[string]interface{}); ok {
		// 更新URL
		if url, ok := resultMap["url"].(string); ok && url != "" {
			step.URL = url
			e.logf("┃ 脚本更新URL: %s", url)
		}

		// 更新Method
		if method, ok := resultMap["method"].(string); ok && method != "" {
			step.Method = method
			e.logf("┃ 脚本更新Method: %s", method)
		}

		// 更新Headers
		if headers, ok := resultMap["headers"].(map[string]interface{}); ok {
			// 将原有headers转换为map，便于合并
			headersMap := make(map[string]string)
			for _, h := range step.Input.Headers {
				headersMap[h.Key] = h.Value
			}

			e.logf("┃ 原始配置headers: %v", headersMap)

			// 合并脚本返回的headers
			tempHeaders := make([]string, 0, len(headers))
			for k := range headers {
				tempHeaders = append(tempHeaders, k)
			}
			sort.Strings(tempHeaders) // 先排序
			for _, k := range tempHeaders {
				v := headers[k]
				headersMap[k] = toString(v)
				e.logf("┃ 添加/更新动态header: %s = %v", k, toString(v))
			}

			// 将合并后的map转回KeyValue数组
			keys := make([]string, 0, len(headersMap))
			for k := range headersMap {
				keys = append(keys, k)
			}
			sort.Strings(keys) // 按键名字母顺序排序

			updatedHeaders := []KeyValue{}
			for _, k := range keys {
				updatedHeaders = append(updatedHeaders, KeyValue{
					Key:   k,
					Value: headersMap[k],
				})
			}

			step.Input.Headers = updatedHeaders
			e.logf("┃ 合并后的最终headers: %v", step.Input.Headers)
		}

		// 更新Gets
		if gets, ok := resultMap["gets"].(map[string]interface{}); ok {
			// 将原有gets转换为map，便于合并
			getsMap := make(map[string]string)
			for _, g := range step.Input.Gets {
				if g.Key != "" { // 只添加有键的项
					getsMap[g.Key] = g.Value
				}
			}

			e.logf("┃ 原始配置gets: %v", getsMap)

			// 合并脚本返回的gets
			for k, v := range gets {
				getsMap[k] = toString(v)
				e.logf("┃ 添加/更新动态get参数: %s = %v", k, toString(v))
			}

			// 将合并后的map转回KeyValue数组
			updatedGets := []KeyValue{}
			for k, v := range getsMap {
				updatedGets = append(updatedGets, KeyValue{
					Key:   k,
					Value: v,
				})
			}

			step.Input.Gets = updatedGets
			e.logf("┃ 合并后的最终gets: %v", step.Input.Gets)
		}

		// 更新Cookies - 修改为与Headers相同的合并策略
		if cookies, ok := resultMap["cookies"].(map[string]interface{}); ok {
			// 将原有cookies转换为map，便于合并
			cookiesMap := make(map[string]string)
			for _, c := range step.Input.Cookies {
				cookiesMap[c.Key] = c.Value
			}

			e.logf("┃ 原始配置cookies: %v", cookiesMap)

			// 合并脚本返回的cookies
			for k, v := range cookies {
				cookiesMap[k] = toString(v)
				e.logf("┃ 添加/更新动态cookie: %s = %v", k, toString(v))
			}

			// 将合并后的map转回KeyValue数组
			updatedCookies := []KeyValue{}
			for k, v := range cookiesMap {
				updatedCookies = append(updatedCookies, KeyValue{
					Key:   k,
					Value: v,
				})
			}

			step.Input.Cookies = updatedCookies
			e.logf("┃ 合并后的最终cookies: %v", step.Input.Cookies)
		}

		// 更新Body
		if body, ok := resultMap["body"].(string); ok {
			step.Input.Body = body
			e.logf("┃ 脚本更新Body: %s", body)
		}

		// 更新用户信息
		if userInfo, ok := resultMap["userInfo"].(map[string]interface{}); ok {
			for k, v := range userInfo {
				e.userInfo[k] = v
				e.logf("┃ 脚本更新用户信息: %s = %v", k, v)
			}
		}

		// 更新全局变量
		if globals, ok := resultMap["globals"].(map[string]interface{}); ok {
			for k, v := range globals {
				e.globalVars[k] = toString(v)
				e.logf("┃ 脚本更新全局变量: %s = %v", k, v)
			}
		}
	} else {
		e.logf("┃ 脚本返回值不是字典类型，忽略结果")
	}

	return nil
}

// ExecuteAuthUrlScript 专门处理授权URL阶段的脚本，只处理GET参数
func (e *StepExecutor) ExecuteAuthUrlScript(script string, env map[string]interface{}, gets []KeyValue) ([]KeyValue, error) {
	e.logf("执行授权URL参数脚本: %s", script)

	// 创建表达式引擎
	exprEngine := NewExprEngine()

	// 将原有gets参数转换为map，便于合并
	getsMap := make(map[string]string)
	for _, g := range gets {
		getsMap[g.Key] = g.Value
	}

	e.logf("原始授权参数: %v", getsMap)

	// 添加gets到环境变量中
	scriptEnv := make(map[string]interface{})
	for k, v := range env {
		scriptEnv[k] = v
	}
	scriptEnv["gets"] = getsMap

	// 执行脚本
	result, err := exprEngine.Execute(script, scriptEnv)
	if err != nil {
		e.logf("┃ 授权URL参数脚本执行失败: %v", err)
		return gets, err
	}

	// 处理脚本执行结果
	if resultMap, ok := result.(map[string]interface{}); ok {
		// 更新Gets
		if scriptGets, ok := resultMap["gets"].(map[string]interface{}); ok {
			// 合并脚本返回的gets
			for k, v := range scriptGets {
				getsMap[k] = toString(v)
				e.logf("┃ 脚本添加/更新授权参数: %s = %v", k, toString(v))
			}
		}

		// 更新全局变量 (如果有)
		if globals, ok := resultMap["globals"].(map[string]interface{}); ok {
			for k, v := range globals {
				e.globalVars[k] = toString(v)
				e.logf("┃ 脚本更新全局变量: %s = %v", k, v)
			}
		}
	} else {
		e.logf("┃ 脚本返回值不是字典类型，忽略结果")
	}

	// 将合并后的map转回KeyValue数组
	updatedGets := []KeyValue{}
	for k, v := range getsMap {
		updatedGets = append(updatedGets, KeyValue{
			Key:   k,
			Value: v,
		})
	}

	return updatedGets, nil
}

// executeOutputScript 执行输出处理脚本
func (e *StepExecutor) executeOutputScript(script string, env map[string]interface{}) error {
	e.logf("执行输出脚本: %s", script)

	// 创建表达式引擎
	exprEngine := NewExprEngine()

	// 执行脚本
	result, err := exprEngine.Execute(script, env)
	if err != nil {
		e.logf("┃ 脚本执行失败: %v", err)
		return err
	}

	// 处理脚本执行结果
	if resultMap, ok := result.(map[string]interface{}); ok {
		// 更新用户信息
		if userInfo, ok := resultMap["userInfo"].(map[string]interface{}); ok {
			for k, v := range userInfo {
				e.userInfo[k] = v
				e.logf("┃ 脚本更新用户信息: %s = %v", k, v)
			}
		}

		// 更新全局变量
		if globals, ok := resultMap["globals"].(map[string]interface{}); ok {
			for k, v := range globals {
				e.globalVars[k] = toString(v)
				e.logf("┃ 脚本更新全局变量: %s = %v", k, v)
			}
		}

		// 处理错误信息
		if errMsg, ok := resultMap["error"].(string); ok && errMsg != "" {
			e.logf("┃ 脚本返回错误: %s", errMsg)
			return fmt.Errorf("┃ 脚本错误: %s", errMsg)
		}
	} else {
		e.logf("┃ 脚本返回值不是字典类型，忽略结果")
	}

	return nil
}

// getKeyValuesAsMap 将KeyValue数组转换为map
func getKeyValuesAsMap(keyValues []KeyValue) map[string]string {
	result := make(map[string]string)
	for _, kv := range keyValues {
		result[kv.Key] = kv.Value
	}
	return result
}

// getValueFromPath 从嵌套结构中获取值
func (e *StepExecutor) getValueFromPath(data map[string]interface{}, path string) interface{} {
	if path == "" {
		return nil
	}

	parts := strings.Split(path, ".")
	var current interface{} = data

	for i, part := range parts {
		if i == len(parts)-1 {
			// 获取最终节点值
			switch c := current.(type) {
			case map[string]interface{}:
				return c[part]
			case []interface{}:
				// 如果当前节点是数组，先检查part是否为数字索引
				if index, err := strconv.Atoi(part); err == nil && index >= 0 && index < len(c) {
					return c[index] // 如果是有效的数字索引，直接返回对应元素
				} else if len(c) > 0 {
					// 如果是最后一个部分且不是数字索引，尝试从第一个元素获取
					if element, ok := c[0].(map[string]interface{}); ok {
						return element[part] // 从第一个元素中查找属性
					}
				}
			}
			return nil
		}

		// 处理中间节点
		switch c := current.(type) {
		case map[string]interface{}:
			current = c[part]
		case []interface{}:
			// 检查part是否为数字索引
			if index, err := strconv.Atoi(part); err == nil && index >= 0 && index < len(c) {
				e.logf("┃ [参数] 访问数组索引 %s [%d]", part, index)
				current = c[index]
			} else {
				// 重要：恢复原有行为 - 如果不是数字索引且当前是数组，自动使用第一个元素
				if len(c) > 0 {
					e.logf("┃ [参数] 当前节点是数组但索引不是数字, 自动使用第一个元素处理")
					current = c[0]
				} else {
					e.logf("┃ [参数] 数组为空，无法获取元素")
					return nil
				}
			}
		default:
			if current == nil {
				e.logf("┃ 路径节点为nil: %s (在 %s)", part, path)
				return nil
			}
			e.logf("┃ 不支持的类型: %T 在节点 %s (路径 %s)", current, part, path)
			return nil
		}
	}

	return current
}

// checkCondition 检查条件是否满足
func (e *StepExecutor) checkCondition(data map[string]interface{}, output StepOutput) bool {
	if output.Column == "" {
		return true
	}

	value := e.getValueFromPath(data, output.Column)
	strValue := toString(value)

	switch output.JudgeCond {
	case "Equal":
		return strValue == output.Value
	case "NotEqual":
		return strValue != output.Value
	case "Contain":
		return strings.Contains(strValue, output.Value)
	case "NotContain":
		return !strings.Contains(strValue, output.Value)
	case "Empty":
		return strValue == ""
	case "NotEmpty":
		return strValue != ""
	default:
		return false
	}
}

// parseJWT 解析JWT令牌
func parseJWT(tokenStr string) (map[string]interface{}, error) {
	// 简单实现，只处理不加密的JWT
	parts := strings.Split(tokenStr, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("无效的JWT格式")
	}

	// 解码payload部分
	payload := parts[1]
	// 处理padding
	for len(payload)%4 != 0 {
		payload += "="
	}

	// Base64解码
	decoded, err := base64.URLEncoding.DecodeString(payload)
	if err != nil {
		return nil, fmt.Errorf("解码JWT失败: %v", err)
	}

	// JSON解析
	var claims map[string]interface{}
	if err := json.Unmarshal(decoded, &claims); err != nil {
		return nil, fmt.Errorf("解析JWT payload失败: %v", err)
	}

	return claims, nil
}

// SetUserInfo 设置用户信息
func (e *StepExecutor) SetUserInfo(key string, value interface{}) {
	// 检查是否已经是花括号格式
	if !strings.HasPrefix(key, "{") {
		key = "{User." + key + "}"
	}
	e.userInfo[key] = value
}

// getServerHost 获取服务器地址
func (e *StepExecutor) getServerHost() string {
	// 1. 优先使用上下文中的主机信息
	if e.ctx != nil {
		hostPort := common.GetClientHostPort(e.ctx)
		if hostPort != "" {
			// 只取主机部分，移除端口
			if colonIndex := strings.Index(hostPort, ":"); colonIndex != -1 {
				return hostPort[:colonIndex]
			}
			return hostPort
		}
	}

	// 2. 其次使用全局变量中的配置
	if host, exists := e.globalVars["host"]; exists && host != "" {
		return host
	}

	// 3. 再次尝试从环境变量获取
	if host := os.Getenv("SERVER_HOST"); host != "" {
		return host
	}

	// 4. 最后使用默认值
	return "localhost"
}

// 重新实现 getDefaultRedirectURI 方法
func (e *StepExecutor) getDefaultRedirectURI() string {
	// 1. 动态从上下文生成重定向URI
	if e.ctx != nil {
		scheme := common.GetClientScheme(e.ctx)
		hostPort := common.GetClientHostPortWithConfig(e.ctx)
		if scheme != "" && hostPort != "" {
			// 检查idpId是否在用户信息或全局变量中
			idpId := ""
			if val, ok := e.userInfo["{User.IdpId}"]; ok {
				idpId = toString(val)
			} else if val, ok := e.userInfo["IdpId"]; ok {
				idpId = toString(val)
			} else if val, ok := e.globalVars["idp_id"]; ok {
				idpId = val
			}

			// 构建重定向URI
			if idpId != "" {
				return fmt.Sprintf("%s://%s/auth/login/v1/callback/%s", scheme, hostPort, idpId)
			}
			return fmt.Sprintf("%s://%s/auth/login/v1/callback/", scheme, hostPort)
		}
	}

	// 2. 从环境变量中获取
	if uri, exists := e.envVars["RedirectUri"]; exists && uri != "" {
		return uri
	}

	// 3. 从全局变量中获取
	if uri, exists := e.globalVars["redirect_uri"]; exists && uri != "" {
		return uri
	}

	if uri, exists := e.globalVars["callback_url"]; exists && uri != "" {
		return uri
	}

	// 4. 使用默认构造方式
	host := e.getServerHost()
	scheme := "https"

	if e.ctx != nil {
		scheme = common.GetClientScheme(e.ctx)
		if scheme == "" {
			scheme = "https"
		}
	}

	return fmt.Sprintf("%s://%s/callback", scheme, host)
}

func (e *StepExecutor) getClientIP() string {
	// 1. 优先使用上下文中的客户端IP
	if e.ctx != nil {
		ip := common.GetClientHost(e.ctx)
		if ip != "" {
			return ip
		}
	}

	// 2. 其次使用通过SetEnvVar手动设置的IP
	if ip, exists := e.envVars["IP"]; exists && ip != "" {
		return ip
	}

	// 3. 最后使用默认IP
	return "127.0.0.1"
}

// SetEnvVars 批量设置环境变量
func (e *StepExecutor) SetEnvVars(vars map[string]string) {
	for k, v := range vars {
		e.envVars[k] = v
	}
}

func (e *StepExecutor) logAllVariables(variables map[string]string) {
	e.logf("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	e.logf("┃ 当前可用变量列表")
	e.logf("┣━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// 分类显示变量
	e.logf("┃ [环境变量]")
	for k, v := range variables {
		if strings.HasPrefix(k, "Env.") {
			e.logf("┃   %s = %s", k, v)
		}
	}

	e.logf("┃ [全局变量]")
	for k, v := range variables {
		if strings.HasPrefix(k, "Global.") {
			e.logf("┃   %s = %s", k, v)
		}
	}

	e.logf("┃ [用户变量]")
	for k, v := range variables {
		if strings.HasPrefix(k, "User.") {
			e.logf("┃   %s = %s", k, v)
		}
	}

	e.logf("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
}

// ClearLogs 清除已收集的日志
func (e *StepExecutor) ClearLogs() {
	e.logs = make([]string, 0)
}

// parseXML 解析XML字符串为map结构
func parseXML(xmlStr string) (map[string]interface{}, error) {
	decoder := xml.NewDecoder(strings.NewReader(xmlStr))
	result := make(map[string]interface{})

	// 记录当前处理的元素路径
	var elementStack []string
	// 记录每个层级对应的map
	mapStack := []map[string]interface{}{result}
	// 当前文本内容
	var textContent strings.Builder

	for {
		token, err := decoder.Token()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("解析XML失败: %v", err)
		}

		switch t := token.(type) {
		case xml.StartElement:
			// 记录当前元素名到路径栈
			elementStack = append(elementStack, t.Name.Local)
			textContent.Reset()

			// 为新元素创建map
			newMap := make(map[string]interface{})

			// 处理属性
			if len(t.Attr) > 0 {
				attrs := make(map[string]string)
				for _, attr := range t.Attr {
					attrs[attr.Name.Local] = attr.Value
				}
				newMap["_attrs"] = attrs
			}

			// 将新元素添加到父元素中
			currentMap := mapStack[len(mapStack)-1]

			// 检查同名元素是否已存在
			if existing, exists := currentMap[t.Name.Local]; exists {
				// 如果已存在同名元素，转换为数组
				switch existingVal := existing.(type) {
				case []interface{}:
					// 已经是数组，直接添加
					currentMap[t.Name.Local] = append(existingVal, newMap)
				default:
					// 转换为数组
					currentMap[t.Name.Local] = []interface{}{existingVal, newMap}
				}
			} else {
				// 不存在同名元素，直接添加
				currentMap[t.Name.Local] = newMap
			}

			// 将新map压入栈
			mapStack = append(mapStack, newMap)

		case xml.EndElement:
			// 处理文本内容
			if textContent.Len() > 0 {
				text := strings.TrimSpace(textContent.String())
				if text != "" {
					currentMap := mapStack[len(mapStack)-1]
					currentMap["_text"] = text
				}
			}

			// 弹出当前元素
			elementStack = elementStack[:len(elementStack)-1]
			mapStack = mapStack[:len(mapStack)-1]

		case xml.CharData:
			// 收集文本内容
			textContent.Write(t)
		}
	}

	return result, nil
}

// handleResponseCookies 处理响应中的Cookie并映射到用户信息
func (e *StepExecutor) handleResponseCookies(resp *http.Response, step Step) {
	// 如果没有配置Cookie映射，直接返回
	if len(step.Output.Cookies) == 0 {
		return
	}

	// 获取所有响应Cookie
	cookies := resp.Cookies()
	if len(cookies) == 0 {
		e.logf("┃ [响应] 没有发现Cookie")
		return
	}

	e.logf("┃ [响应] 处理Cookie: 发现 %d 个", len(cookies))

	// 解析所有Cookie到映射
	cookieMap := make(map[string]*http.Cookie)
	for _, cookie := range cookies {
		cookieMap[cookie.Name] = cookie
		e.logf("┃ [Cookie] %s = %s", cookie.Name, cookie.Value)
		// 日志输出Cookie的主要属性
		e.logf("┃ [Cookie] %s 的属性:", cookie.Name)
		if cookie.Path != "" {
			e.logf("┃   - Path: %s", cookie.Path)
		}
		if cookie.Domain != "" {
			e.logf("┃   - Domain: %s", cookie.Domain)
		}
		if !cookie.Expires.IsZero() {
			e.logf("┃   - Expires: %s", cookie.Expires.Format(time.RFC1123))
		}
		if cookie.MaxAge > 0 {
			e.logf("┃   - MaxAge: %d", cookie.MaxAge)
		}
		if cookie.HttpOnly {
			e.logf("┃   - HttpOnly: true")
		}
		if cookie.Secure {
			e.logf("┃   - Secure: true")
		}
		if cookie.SameSite != 0 {
			e.logf("┃   - SameSite: %s", sameSiteToString(cookie.SameSite))
		}
	}

	// 根据配置映射Cookie到用户信息
	for _, cookieParam := range step.Output.Cookies {
		if cookieParam.Key == "" || cookieParam.Select == "" {
			e.logf("┃ [Cookie] 警告: 跳过无效的Cookie映射配置")
			continue
		}

		// 检查是否是特殊格式：name.attr (例如 cookie1.path)
		parts := strings.Split(cookieParam.Key, ".")
		cookieName := parts[0]

		if cookie, exists := cookieMap[cookieName]; exists {
			var value string

			// 如果有指定属性，获取该属性值
			if len(parts) > 1 {
				attr := strings.ToLower(parts[1])
				switch attr {
				case "path":
					value = cookie.Path
				case "domain":
					value = cookie.Domain
				case "expires":
					if !cookie.Expires.IsZero() {
						value = cookie.Expires.Format(time.RFC1123)
					}
				case "maxage", "max-age":
					value = strconv.Itoa(cookie.MaxAge)
				case "httponly":
					value = strconv.FormatBool(cookie.HttpOnly)
				case "secure":
					value = strconv.FormatBool(cookie.Secure)
				case "samesite":
					value = sameSiteToString(cookie.SameSite)
				default:
					value = cookie.Value
				}
			} else {
				// 默认获取Cookie值
				value = cookie.Value
			}

			// 规范化参数名
			paramName := cookieParam.Select
			if !strings.HasPrefix(paramName, "{") {
				paramName = "{User." + paramName + "}"
			}

			e.userInfo[paramName] = value
			e.logf("┃ [Cookie] 保存用户信息: %s = %s", paramName, value)
		} else {
			e.logf("┃ [Cookie] 未找到配置的Cookie: %s", cookieParam.Key)
		}
	}
}

// sameSiteToString 将http.SameSite转换为字符串
func sameSiteToString(sameSite http.SameSite) string {
	switch sameSite {
	case http.SameSiteDefaultMode:
		return "Default"
	case http.SameSiteNoneMode:
		return "None"
	case http.SameSiteLaxMode:
		return "Lax"
	case http.SameSiteStrictMode:
		return "Strict"
	default:
		return fmt.Sprintf("Unknown(%d)", sameSite)
	}
}
