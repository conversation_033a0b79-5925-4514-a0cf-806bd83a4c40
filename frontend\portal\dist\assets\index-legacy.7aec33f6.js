/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}System.register(["./index-legacy.b871e767.js"],(function(t,o){"use strict";var n;return{setters:[function(e){n=e.X}],execute:function(){function o(e,t){for(var o=function(){var o=t[n];if("string"!=typeof o&&!Array.isArray(o)){var r=function(t){if("default"!==t&&!(t in e)){var n=Object.getOwnPropertyDescriptor(o,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:function(){return o[t]}})}};for(var s in o)r(s)}},n=0;n<t.length;n++)o();return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var r={},s=Object.prototype.hasOwnProperty;function a(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(t){return null}}function c(e){try{return encodeURIComponent(e)}catch(t){return null}}r.stringify=function(e,t){t=t||"";var o,n,r=[];for(n in"string"!=typeof t&&(t="?"),e)if(s.call(e,n)){if((o=e[n])||null!=o&&!isNaN(o)||(o=""),n=c(n),o=c(o),null===n||null===o)continue;r.push(n+"="+o)}return r.length?t+r.join("&"):""},r.parse=function(e){for(var t,o=/([^=?#&]+)=?([^&]*)/g,n={};t=o.exec(e);){var r=a(t[1]),s=a(t[2]);null===r||null===s||r in n||(n[r]=s)}return n};var p=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e},l=r,i=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,u=/[\n\r\t]/g,h=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,f=/:\d+$/,m=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,d=/^[a-zA-Z]:/;function y(e){return(e||"").toString().replace(i,"")}var g=[["#","hash"],["?","query"],function(e,t){return b(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],w={hash:1,query:1};function v(t){var o,r=("undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:{}).location||{},s={},a=e(t=t||r);if("blob:"===t.protocol)s=new I(unescape(t.pathname),{});else if("string"===a)for(o in s=new I(t,{}),w)delete s[o];else if("object"===a){for(o in t)o in w||(s[o]=t[o]);void 0===s.slashes&&(s.slashes=h.test(t.href))}return s}function b(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function C(e,t){e=(e=y(e)).replace(u,""),t=t||{};var o,n=m.exec(e),r=n[1]?n[1].toLowerCase():"",s=!!n[2],a=!!n[3],c=0;return s?a?(o=n[2]+n[3]+n[4],c=n[2].length+n[3].length):(o=n[2]+n[4],c=n[2].length):a?(o=n[3]+n[4],c=n[3].length):o=n[4],"file:"===r?c>=2&&(o=o.slice(2)):b(r)?o=n[4]:r?s&&(o=o.slice(2)):c>=2&&b(t.protocol)&&(o=n[4]),{protocol:r,slashes:s||b(r),slashesCount:c,rest:o}}function I(t,o,n){if(t=(t=y(t)).replace(u,""),!(this instanceof I))return new I(t,o,n);var r,s,a,c,i,h,f=g.slice(),m=e(o),w=this,R=0;for("object"!==m&&"string"!==m&&(n=o,o=null),n&&"function"!=typeof n&&(n=l.parse),r=!(s=C(t||"",o=v(o))).protocol&&!s.slashes,w.slashes=s.slashes||r&&o.slashes,w.protocol=s.protocol||o.protocol||"",t=s.rest,("file:"===s.protocol&&(2!==s.slashesCount||d.test(t))||!s.slashes&&(s.protocol||s.slashesCount<2||!b(w.protocol)))&&(f[3]=[/(.*)/,"pathname"]);R<f.length;R++)"function"!=typeof(c=f[R])?(a=c[0],h=c[1],a!=a?w[h]=t:"string"==typeof a?~(i="@"===a?t.lastIndexOf(a):t.indexOf(a))&&("number"==typeof c[2]?(w[h]=t.slice(0,i),t=t.slice(i+c[2])):(w[h]=t.slice(i),t=t.slice(0,i))):(i=a.exec(t))&&(w[h]=i[1],t=t.slice(0,i.index)),w[h]=w[h]||r&&c[3]&&o[h]||"",c[4]&&(w[h]=w[h].toLowerCase())):t=c(t,w);n&&(w.query=n(w.query)),r&&o.slashes&&"/"!==w.pathname.charAt(0)&&(""!==w.pathname||""!==o.pathname)&&(w.pathname=function(e,t){if(""===e)return t;for(var o=(t||"/").split("/").slice(0,-1).concat(e.split("/")),n=o.length,r=o[n-1],s=!1,a=0;n--;)"."===o[n]?o.splice(n,1):".."===o[n]?(o.splice(n,1),a++):a&&(0===n&&(s=!0),o.splice(n,1),a--);return s&&o.unshift(""),"."!==r&&".."!==r||o.push(""),o.join("/")}(w.pathname,o.pathname)),"/"!==w.pathname.charAt(0)&&b(w.protocol)&&(w.pathname="/"+w.pathname),p(w.port,w.protocol)||(w.host=w.hostname,w.port=""),w.username=w.password="",w.auth&&(~(i=w.auth.indexOf(":"))?(w.username=w.auth.slice(0,i),w.username=encodeURIComponent(decodeURIComponent(w.username)),w.password=w.auth.slice(i+1),w.password=encodeURIComponent(decodeURIComponent(w.password))):w.username=encodeURIComponent(decodeURIComponent(w.auth)),w.auth=w.password?w.username+":"+w.password:w.username),w.origin="file:"!==w.protocol&&b(w.protocol)&&w.host?w.protocol+"//"+w.host:"null",w.href=w.toString()}I.prototype={set:function(e,t,o){var n=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(o||l.parse)(t)),n[e]=t;break;case"port":n[e]=t,p(t,n.protocol)?t&&(n.host=n.hostname+":"+t):(n.host=n.hostname,n[e]="");break;case"hostname":n[e]=t,n.port&&(t+=":"+n.port),n.host=t;break;case"host":n[e]=t,f.test(t)?(t=t.split(":"),n.port=t.pop(),n.hostname=t.join(":")):(n.hostname=t,n.port="");break;case"protocol":n.protocol=t.toLowerCase(),n.slashes=!o;break;case"pathname":case"hash":if(t){var r="pathname"===e?"/":"#";n[e]=t.charAt(0)!==r?r+t:t}else n[e]=t;break;case"username":case"password":n[e]=encodeURIComponent(t);break;case"auth":var s=t.indexOf(":");~s?(n.username=t.slice(0,s),n.username=encodeURIComponent(decodeURIComponent(n.username)),n.password=t.slice(s+1),n.password=encodeURIComponent(decodeURIComponent(n.password))):n.username=encodeURIComponent(decodeURIComponent(t))}for(var a=0;a<g.length;a++){var c=g[a];c[4]&&(n[c[1]]=n[c[1]].toLowerCase())}return n.auth=n.password?n.username+":"+n.password:n.username,n.origin="file:"!==n.protocol&&b(n.protocol)&&n.host?n.protocol+"//"+n.host:"null",n.href=n.toString(),n},toString:function(t){t&&"function"==typeof t||(t=l.stringify);var o,n=this,r=n.host,s=n.protocol;s&&":"!==s.charAt(s.length-1)&&(s+=":");var a=s+(n.protocol&&n.slashes||b(n.protocol)?"//":"");return n.username?(a+=n.username,n.password&&(a+=":"+n.password),a+="@"):n.password?(a+=":"+n.password,a+="@"):"file:"!==n.protocol&&b(n.protocol)&&!r&&"/"!==n.pathname&&(a+="@"),(":"===r[r.length-1]||f.test(n.hostname)&&!n.port)&&(r+=":"),a+=r+n.pathname,(o="object"===e(n.query)?t(n.query):n.query)&&(a+="?"!==o.charAt(0)?"?"+o:o),n.hash&&(a+=n.hash),a}},I.extractProtocol=C,I.location=v,I.trimLeft=y,I.qs=l;t("i",o({__proto__:null,default:I},[I]))}}}))}();
