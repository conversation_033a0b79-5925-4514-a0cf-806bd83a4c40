package dto

import "asdsec.com/asec/platform/pkg/model"

type GetAgentListReq struct {
	model.Pagination
	Status    int    `json:"status"` //0全部 1-在线 2-不在线
	AgentName string `json:"agent_name"`
	OSType    string `json:"os_type"`
	Mac       string `json:"mac"`
	IP        string `json:"ip"`
	UserName  string `json:"user_name"`
	OrderBy   string `json:"order_by"`
	Order     string `json:"order"`
	Version   string `json:"version"`
}
type SatusReq struct {
	Id     string `form:"id" json:"id" ` //终端ID
	Status string `json:"status"`
	Action string `json:"action"`
	Desc   string `json:"desc"`
	Time   string `json:"time"`
	TaskId string `json:"task_id"`
}

type AgentLogsListReq struct {
	model.Pagination
	AgentId  string `json:"agent_id"`
	Type     string `json:"type"`
	Duration uint32 `json:"duration"`
}
