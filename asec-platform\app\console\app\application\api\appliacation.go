package api

import (
	"fmt"
	"net"
	"strconv"

	strategydto "asdsec.com/asec/platform/app/console/app/access/dto"
	strategysrv "asdsec.com/asec/platform/app/console/app/access/service"
	"asdsec.com/asec/platform/app/console/app/application/constants"
	"asdsec.com/asec/platform/app/console/app/application/dto"
	"asdsec.com/asec/platform/app/console/app/application/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	SystemService "asdsec.com/asec/platform/app/console/app/system/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/miekg/dns"
	"go.uber.org/zap"
)

type ListAppReq struct {
	Limit   int    `form:"limit" json:"limit" binding:"required,min=1,max=1000"`
	Offset  int    `form:"offset" json:"offset" binding:"min=0"`
	Search  string `form:"search"  json:"search"`
	GroupId uint64 `form:"group_id"  json:"group_id"`
}

type AddGroupReq struct {
	GroupName string `json:"group_name" binding:"required"`
}

// AddApplicationGroup godoc
// @Summary 创建应用分组
// @Schemes
// @Description 创建应用分组
// @Tags        Application
// @Produce     application/json
// @Param       req body AddGroupReq true "应用分组信息"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/application/group [POST]
// @success     200 {object} common.Response{} "ok"
func AddApplicationGroup(c *gin.Context) {
	var req AddGroupReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppGroupResourceType,
			OperationType:  common.OperateCreate,
			Representation: req.GroupName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	group := model.AppGroup{GroupName: req.GroupName}
	id, aeErr := service.GetApplicationService().AddApplicationGroup(c, &group)
	if aeErr != nil {
		common.FailAError(c, aeErr)
		errorLog = aeErr.Error()
		return
	}
	common.OkWithData(c, strconv.FormatUint(id, 10))
}

// GetAppGroup godoc
// @Summary 获取应用分组
// @Schemes
// @Description 获取应用分组
// @Tags        Application
// @Produce     application/json
// @Success     200
// @Router      /v1/application/group [GET]
// @success     200 {object} common.Response{data=[]model.AppGroup} "ok"
func GetAppGroup(c *gin.Context) {
	var req dto.GetApplicationGroupListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	groups, err := service.GetApplicationService().ListApplicationGroup(c, req)
	if err != nil {
		common.Fail(c, common.QueryAppGroupErr)
		return
	}
	common.OkWithData(c, groups)
}

// GetApplication godoc
// @Summary 查询应用详情
// @Schemes
// @Description 查询应用详情
// @Tags        Application
// @Produce     application/json
// @Param       id query uint64 true "应用ID"
// @Success     200
// @Router      /v1/application [GET]
// @success     200 {object} common.Response{data=dto.ApplicationResp} "application "
func GetApplication(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetApplicationService().GetById(c, id)
	if err != nil {
		global.SysLog.Error("query list err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// DeleteApplication godoc
// @Summary 删除应用
// @Schemes
// @Description 删除应用
// @Tags        Application
// @Produce     application/json
// @Param       id query int true "应用ID"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/application [DELETE]
// @success     200 {object} common.Response{} ""
func DeleteApplication(c *gin.Context) {
	var req dto.DeleteApplicationByTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppResourceType,
			OperationType:  common.OperateDelete,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	//批量查询
	vo := strategydto.CheckStrategyQuoteReq{AppIds: req.Ids}
	quoteResp, err := strategysrv.GetStrategyService().CheckStrategyQuote(c, vo)
	if err != nil {
		global.SysLog.Error("CheckStrategyQuote err:", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if len(quoteResp) > 0 {
		stgNames := quoteResp[0].StrategyName
		i := 1
		for ; i < len(quoteResp); i++ {
			stgNames = fmt.Sprintf("%s、%s", stgNames, quoteResp[i].StrategyName)
			if len(stgNames) > constants.MaxDisplayLength {
				break
			}
		}

		if i != (len(quoteResp) - 1) {
			stgNames = fmt.Sprintf("%s%s", stgNames, constants.MoreThanName)
		}
		common.FailFormat(c, common.AppAndPolicyAss, "strategy_names", stgNames)
		return
	}
	var ids []uint64
	for _, v := range req.Ids {
		id, err := strconv.ParseUint(v, 10, 64)
		if err != nil {
			global.SysLog.Error("param err", zap.Error(err))
			common.Fail(c, common.ParamInvalidError)
			return
		}
		ids = append(ids, id)
	}
	err = service.GetApplicationService().DeleteApp(c, ids)
	if err != nil {
		global.SysLog.Error("delete list err", zap.Error(err))
		common.Fail(c, common.AppDelErr)
		return
	}
	//删除前检查应用对应的策略

	common.Ok(c)
}

// DeleteGroup godoc
// @Summary 删除应用分组
// @Schemes
// @Description 删除应用分组
// @Tags        Application
// @Produce     application/json
// @Param       id query int true "应用分组ID"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/application/group [DELETE]
// @success     200 {object} common.Response{} ""
func DeleteGroup(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		common.Fail(c, common.ParamInvalidError)
		//TODO delete this return?
		return
	}

	//检测默认分组,不允许删除
	group, err := service.GetApplicationService().GetGroupById(c, id)
	if err != nil {
		global.SysLog.Error("获取应用分组失败", zap.Error(err))
		common.Fail(c, "")
		return
	}
	if group.IsDefault {
		common.FailWithMessage(c, -1, "默认应用分组不允许删除")
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppGroupResourceType,
			OperationType:  common.OperateDelete,
			Representation: group.GroupName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	//检查标签是否被引用
	vo := strategydto.CheckStrategyQuoteReq{AppGroupId: id}
	quoteResp, err := strategysrv.GetStrategyService().CheckStrategyQuote(c, vo)
	if err != nil {
		global.SysLog.Error("CheckStrategyQuote err:", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if len(quoteResp) > 0 {
		stgNames := quoteResp[0].StrategyName
		i := 1
		for ; i < len(quoteResp); i++ {
			stgNames = fmt.Sprintf("%s、%s", stgNames, quoteResp[i].StrategyName)
			if len(stgNames) > constants.MaxDisplayLength {
				break
			}
		}

		if i != (len(quoteResp) - 1) {
			stgNames = fmt.Sprintf("%s%s", stgNames, constants.MoreThanName)
		}
		common.FailFormat(c, common.AppAndPolicyAss, "strategy_names", stgNames)
		return
	}

	err = service.GetApplicationService().DeleteAppGroup(c, id)
	if err != nil {
		global.SysLog.Error("query list err", zap.Error(err))
		common.Fail(c, "")
		return
	}
	common.Ok(c)
}

type UpdateGroupReq struct {
	ID        uint64 `json:"id,string"  binding:"required"`
	GroupName string `json:"group_name"  binding:"required"`
}

// UpdateAppGroup godoc
// @Summary 更新应用分组
// @Schemes
// @Description 更新应用分组
// @Tags        Application
// @Produce     application/json
// @Param       req body UpdateGroupReq true "应用分组信息"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/application/group [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateAppGroup(c *gin.Context) {
	var req UpdateGroupReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppGroupResourceType,
			OperationType:  common.OperateUpdate,
			Representation: req.GroupName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	var group model.AppGroup
	copier.Copy(&group, &req)
	aeErr := service.GetApplicationService().UpdateAppGroup(c, &group)
	if aeErr != nil {
		common.FailAError(c, aeErr)
		return
	}
	common.Ok(c)
}

// MyApplication godoc
// @Summary 我的应用
// @Schemes
// @Description 获取个人应用信息
// @Tags        Application
// @Produce     application/json
// @Success     200
// @Router      /v1/application/current_user [GET]
// @Security    ApiKeyAuth
// @success     200 {object} common.Response{data=[]model.Application} "application "
func MyApplication(c *gin.Context) {
	curUserId := web.GetCurrentUserId(c)
	if curUserId == "" {
		common.Fail(c, common.AuthFailedError)
		return
	}

	data, err := service.GetApplicationService().GetMyApp(c, curUserId)
	if err != nil {
		common.FailWithMessage(c, -1, "获取用户应用失败")
		return
	}
	common.OkWithData(c, data)
}

func IsIPConflact(gin_ctx *gin.Context, req *dto.ApplicationReq) bool {
	fakeip, _ := SystemService.GetSystemService().GetFakeIpPool(gin_ctx)

	host_ip := web.GetServerHost(gin_ctx)
	if _, ok := dns.IsDomainName(host_ip); ok {
		hostIP, err := net.LookupHost(host_ip)
		if err != nil {
			return false
		}
		host_ip = hostIP[0]
	}
	for _, elem := range req.AppSites {
		if IsIPConflactBase(fakeip.FakeIpPool, elem.Address) {
			return true
		}
		if IsIPConflactBase(host_ip, elem.Address) {
			return true
		}
	}

	return false
}

func IsIPConflactBase(attackedIP string, spoofedIP string) bool {
	if len(attackedIP) == 0 || len(spoofedIP) == 0 {
		return false
	}
	attackedNetIP, err := ParseToIPNet(attackedIP)
	if err != nil {
		return false
	}
	spoofedNetIP, err := ParseToIPNet(spoofedIP)
	if err != nil {
		return false
	}

	attackedNetIPOnes, _ := attackedNetIP.Mask.Size()
	spoofedNetIPOnes, _ := spoofedNetIP.Mask.Size()
	//如果attackedIP的网段大于spoofedIP，则返回false
	if attackedNetIPOnes >= spoofedNetIPOnes && spoofedNetIP.Contains(attackedNetIP.IP) {
		return true
	}

	return false
}

func Min(x, y int) int {
	if x > y {
		return x
	}
	return y
}

func ParseToIPNet(desc string) (*net.IPNet, error) {
	ip := net.ParseIP(desc)
	if ip != nil {
		desc += "/32"
	}

	_, ipnet, err := net.ParseCIDR(desc)
	return ipnet, err
}

// UpdateApplicationByType godoc
// @Summary 更新应用
// @Schemes
// @Description 更新应用
// @Tags        Application
// @accept      application/json
// @Param       req body dto.UpdateApplicationByTypeReq true "application data"
// @Produce     application/json
// @Security    ApiKeyAuth
// @Success     200
// @Router      /v1/application [put]
func UpdateApplicationByType(c *gin.Context) {
	var req dto.UpdateApplicationByTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppResourceType,
			OperationType:  common.OperateUpdate,
			Representation: req.AppName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	// 查询应用名称是否存在
	count, err := service.GetApplicationService().AppCountByName(c, req.AppName, req.ID)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("find strategy by name err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if count > 0 {
		common.Fail(c, common.AppNameRepeat)
		return
	}

	// 未选中标签时，指定为默认分类标签
	if len(req.GroupIds) == 0 {
		req.GroupIds = append(req.GroupIds, strconv.Itoa(constants.DefaultGroupId))
	}

	if req.AppType == constants.WebAppType {
		var webAppReq dto.AddWebAppReq
		serverHost := web.GetServerHost(c)
		webAppReq, err = structWebAppReq(req.AddApplicationByTypeReq, serverHost)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if !checkIpOrDomainAndPort(req.ServerAddress) || !checkIpOrDomainAndPort(req.PublishAddress) {
			common.FailAError(c, aerrors.New("ip or domain invalid", constants.AppDomainInvalidErr))
			return
		}
		var aeErr aerrors.AError
		_, aeErr = service.GetApplicationService().UpdateWebApplication(c, webAppReq, req.ID)
		if aeErr != nil {
			errorLog = aeErr.Error()
			common.FailAError(c, aeErr)
			return
		}
	} else if req.AppType == constants.PortalAppType {
		var res dto.PortalAppReq
		err := copier.Copy(&res, req)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		var groupIds []int64
		for _, groupId := range req.GroupIds {
			idStr, err := strconv.ParseInt(groupId, 10, 64)
			if err != nil {
				common.Fail(c, common.ParamInvalidError)
				return
			}
			groupIds = append(groupIds, idStr)
		}
		res.GroupIds = groupIds
		var aeErr aerrors.AError
		_, aeErr = service.GetApplicationService().UpdatePortalApplication(c, res, req.ID)
		if aeErr != nil {
			errorLog = aeErr.Error()
			common.FailAError(c, aeErr)
			return
		}
	} else {
		var tunAppReq dto.ApplicationReq
		tunAppReq, err = structTunAppReq(req.AddApplicationByTypeReq)
		if err != nil {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		if !CheckTunReq(tunAppReq) {
			common.Fail(c, common.ParamInvalidError)
			return
		}
		//判断设置的IP是否与域名IP冲突
		var sites []string
		for _, v := range tunAppReq.AppSites {
			sites = append(sites, v.Address)
		}
		_, err = service.GetApplicationService().UpdateApplication(c, &dto.UpdateApplicationReq{
			ID:             req.ID,
			ApplicationReq: tunAppReq,
		})
		if err != nil {
			errorLog = err.Error()
			common.Fail(c, common.UpdateAppErr)
			return
		}
	}
	common.Ok(c)
}

// UpdateGroupSortReq 更新分组排序请求
type UpdateGroupSortReq struct {
	Groups []dto.GroupSort `json:"groups" binding:"required"`
}

// UpdateGroupSort godoc
// @Summary 更新应用分组排序
// @Schemes
// @Description 更新应用分组排序
// @Tags        Application
// @Produce     application/json
// @Param       req body UpdateGroupSortReq true "分组排序信息"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/application/group/sort [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateGroupSort(c *gin.Context) {
	var req dto.UpdateGroupSortReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppGroupResourceType,
			OperationType:  common.OperateUpdate,
			Representation: "更新应用分组排序",
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	// 调用 service 层更新排序
	err := service.GetApplicationService().UpdateGroupSort(c, req.Groups)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("update group sort failed", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	common.Ok(c)
}

// MoveGroup godoc
// @Summary 移动应用分组位置
// @Schemes
// @Description 移动应用分组位置
// @Tags        Application
// @Produce     application/json
// @Param       req body dto.GroupMoveReq true "分组移动信息"
// @Success     200
// @Security    ApiKeyAuth
// @Router      /v1/application/group/sort [PUT]
// @success     200 {object} common.Response{} "ok"
func MoveGroup(c *gin.Context) {
	var req dto.GroupMoveReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AppGroupResourceType,
			OperationType:  common.OperateUpdate,
			Representation: fmt.Sprintf("移动分组 %d 到位置 %s", req.GroupID, req.Position),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err := service.GetApplicationService().MoveGroup(c, req)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("move group failed", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}

	common.Ok(c)
}

// GetApplicationByCategory godoc
// @Summary 获取分类应用列表
// @Schemes
// @Description 获取分类应用列表
// @Tags        Application
// @Produce     application/json
// @Success     200
// @Router      /v1/application/category [GET]
// @Security    ApiKeyAuth
// @success     200 {object} common.Response{data=dto.GetApplicationByCategoryRsp} "application"
func GetApplicationByCategory(c *gin.Context) {
	curUserId := web.GetCurrentUserId(c)
	if curUserId == "" {
		common.Fail(c, common.AuthFailedError)
		return
	}

	data, err := service.GetApplicationService().GetApplicationByCategory(c, curUserId)
	if err != nil {
		common.FailWithMessage(c, -1, "获取分类应用列表失败")
		return
	}
	common.OkWithData(c, data)
}
