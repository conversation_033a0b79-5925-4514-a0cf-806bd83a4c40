package dto

type DynamicRule struct {
	Operator string `json:"operator"`
	Rule     []Rule `json:"rule"`
}

type Rule struct {
	Condition  []map[string]interface{} `json:"condition"`
	FactorOp   string                   `json:"factor_op"`
	ValueName  string                   `json:"value_name"`
	FactorAttr string                   `json:"factor_attr"`
	FactorIcon string                   `json:"factor_icon"`
	FactorType string                   `json:"factor_type"`
}

type BrowserCondition struct {
	Browser string `json:"browser"`
}

// map key
const (
	BrowserKey       = Browser
	ProcessNameKey   = "process_name"
	OsKey            = Os
	ConditionNameKey = "condition_name"
)

// rego op
const (
	In    = "in"
	NotIn = "not_in"
	Not   = "not"
	Empty = ""
)

// rego rule type
const (
	Browser     = "browser"
	Os          = "os"
	Region      = "region"
	Process     = "process"
	Ip          = "ip"
	NetLocation = "net_location"
)

// rego rule allow
const (
	BrowserAllow     = "browser_%s"
	OsAllow          = "%sos_in"
	RegionAllow      = "region_%s"
	ProcessAllow     = "process_%s"
	IpAllow          = "ip_%s"
	NetLocationAllow = "net_location_%s"
)

// rego rule template
var (
	TemplateRego = `package asd
import future.keywords.in

default allow := false

%s

allow {
	%s
}`

	BrowserRuleTemplate = `browser_%s {
	%s input.%s in %s
}`
	OsRuleTemplate = `os_in {
	some i
	contains(input.%s, %s[i])
}`
	RegionTemplate = `region_%s{
    region_contains(input.%s, %s, %s)
}`

	ProcessTemplate = `process_%s{
	%s process_contains(input.%s, %s)
}`
	IpTemplate = `ip_%s{
	%s ip_contains(input.%s, %s)
}`

	NetLocationTemplate = `net_location_%s{
	%s net_location_contains(input.%s, %s)
}`
)
