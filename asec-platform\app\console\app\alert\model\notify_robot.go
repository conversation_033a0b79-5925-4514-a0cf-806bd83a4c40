package model

import (
	"asdsec.com/asec/platform/app/console/common/utils"
	"time"
)

type NotifySetting struct {
	ID          uint64    `gorm:"column:id" json:"id"`
	TenantId    uint64    `gorm:"column:tenant_id" json:"tenant_id"`
	Robot<PERSON><PERSON>   string    `gorm:"column:robot_name" json:"robot_name"`
	PlatName    string    `gorm:"column:plat_name" json:"plat_name"`
	WebhookAddr string    `gorm:"column:webhook_addr" json:"webhook_addr"`
	ApiSecret   string    `gorm:"column:api_secret" json:"api_secret"`
	CreateTime  time.Time `gorm:"column:create_time;default:null" json:"create_time"`
	UpdateTime  time.Time `gorm:"column:update_time;default:null" json:"update_time"`
}

type NotifyResp struct {
	ID          uint64          `gorm:"column:id" json:"id"`
	TenantId    uint64          `gorm:"column:tenant_id" json:"tenant_id"`
	RobotName   string          `gorm:"column:robot_name" json:"robot_name"`
	Plat<PERSON><PERSON>    string          `gorm:"column:plat_name" json:"plat_name"`
	WebhookAddr string          `gorm:"column:webhook_addr" json:"webhook_addr"`
	ApiSecret   string          `gorm:"column:api_secret" json:"api_secret"`
	CreateTime  utils.FrontTime `gorm:"column:create_time;default:null" json:"create_time"`
	UpdateTime  utils.FrontTime `gorm:"column:update_time;default:null" json:"update_time"`
}

func (NotifySetting) TableName() string {
	return "tb_notify_setting"
}
