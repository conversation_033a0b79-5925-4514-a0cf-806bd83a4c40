package data

import (
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	pb "asdsec.com/asec/platform/api/application/v1"
	sysMode "asdsec.com/asec/platform/app/console/app/system/model"
	virtualIPModel "asdsec.com/asec/platform/app/console/app/virtual_ip/model"
	"asdsec.com/asec/platform/pkg/biz"
	"asdsec.com/asec/platform/pkg/biz/dto"
	"asdsec.com/asec/platform/pkg/model"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

type appRepo struct {
	data *Data
	log  *log.Helper
}

const (
	webWatermarkConf      = "web_watermark_conf"
	webClearWatermarkType = "web_clear_watermark"
)

func (a appRepo) WebGatewayHosts(ctx context.Context, req *pb.WebGatewayHostsReq) ([]sysMode.Hosts, error) {
	db := a.data.GetDb(ctx)
	var data []sysMode.Hosts
	err := db.Table("tb_hosts").Find(&data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (a appRepo) WebGatewayWatermark(ctx context.Context, req *pb.WebGatewayWatermarkReq) (dto.WebWatermarkDto, error) {
	db := a.data.GetDb(ctx)
	var data []dto.WaterConf
	err := db.Table("tb_watermark_config").Where("key = ?", webWatermarkConf).Find(&data).Error
	if err != nil {
		return dto.WebWatermarkDto{}, err
	}
	var wmConf dto.WebWatermarkDto
	var wmEnable bool
	for _, v := range data {
		var tmpWatermark dto.WebWatermarkDto
		err = json.Unmarshal([]byte(v.Value), &tmpWatermark)
		if err != nil {
			return dto.WebWatermarkDto{}, err
		}
		if tmpWatermark.Enable {
			wmConf = tmpWatermark
			wmEnable = true
			if v.Type == webClearWatermarkType {
				break
			}
			wmConf.Secret = true
		}
	}
	if wmEnable {
		if len(wmConf.AppGroupIds) > 0 {
			appIds := wmConf.AppIds
			var tmpAppIds []string
			err = db.Table("tb_application").Select("id").
				Where("group_ids && ? AND app_type = 'web'", pq.Array(wmConf.AppGroupIds)).Find(&tmpAppIds).Error
			if err != nil {
				return dto.WebWatermarkDto{}, err
			}
			appIds = append(appIds, tmpAppIds...)
			wmConf.AppIds = appIds
		}
		return wmConf, nil
	}
	return dto.WebWatermarkDto{Enable: false}, nil
}

func (a appRepo) UciUserInfo(ctx context.Context) ([]dto.UciUserInfo, error) {
	db := a.data.GetDb(ctx)
	var res []dto.UciUserInfo
	err := db.Table("tb_ueba_user_risk").Select("user_id,score,risk_level").
		Where("score_date = ? ", time.Now().Format("2006-01-02")).Find(&res).Error
	if err != nil {
		a.log.Errorf("getUciUserInfo err:%v", err)
		return nil, err
	}
	return res, nil
}

func (a appRepo) GetWebStrategyAppIds(ctx context.Context, appGroupIds pq.Int64Array) ([]int64, error) {
	db := a.data.GetDb(ctx)
	var appIds []int64
	err := db.Model(&model.Application{}).Select("tb_application.id").
		Where("tb_application.group_ids && ? ", pq.Array(appGroupIds)).Find(&appIds).Error
	if err != nil {
		a.log.Errorf("getStrategyAppIds err:%v", err)
		return nil, err
	}
	return appIds, nil
}

var webAccessInfoSelectSql = `t1.id as strategy_id,t1.user_ids,t1.user_group_ids,t1.role_ids,
       t1.exclude_user_ids,t1.exclude_user_group_ids,t1.exclude_user_role_ids,
       t1.app_ids,t1.app_group_ids,
       t1.start_time,t1.end_time,
       t1.enable_all_app,t1.enable_all_user,t1.enable_log,
       t1.strategy_name,t1.action,t1.priority,t1.rego_file,t1.time_id,t1.user_risk_rule,t2.*`

func (a appRepo) WebAccessInfo(ctx context.Context, req *pb.WebAccessInfoReq) ([]dto.WebAccessInfoDto, error) {
	db := a.data.GetDb(ctx)
	var _l []dto.WebAccessInfoDto
	err := db.Table("tb_access_strategy t1").Select(webAccessInfoSelectSql).
		Joins("left outer join tb_factor_time t2 on t1.time_id = t2.id").
		Where("strategy_status=1").Order("priority").Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (a appRepo) WebAppInfo(ctx context.Context, req *pb.WebAppInfoReq) ([]dto.WebAppInfoDto, error) {
	db := a.data.GetDb(ctx)
	var _l []dto.WebAppInfoDto
	err := db.Model(&model.Application{}).Select("id,app_name,publish_address,publish_schema,uri,server_address,idp_id,app_status,app_type,health_config").Where("app_type in ('web','tun')").Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (a appRepo) GetUserIdByRoles(ctx context.Context, roleIds pq.StringArray) ([]string, error) {
	db := a.data.GetDb(ctx)
	sql := `
	select distinct user_id from tb_user_role where role_id in (?);
	`
	var userIds []string
	err := db.Raw(sql, ([]string)(roleIds)).Find(&userIds).Error
	if err != nil {
		a.log.Errorf("GetUserIdByRoles err:%v", err)
		return nil, err
	}
	return userIds, nil
}

func (a appRepo) GetAllAppStrategy(ctx context.Context) ([]dto.SeStrategy, error) {
	db := a.data.GetDb(ctx)
	var respList []dto.SeStrategy
	err := db.Model(&model.AccessStrategy{}).Where("enable_all_app = 1 and strategy_status = 1").Find(&respList).Error
	if err != nil {
		return nil, err
	}
	return respList, nil
}

func (a appRepo) GetStrategyAppIds(ctx context.Context, appGroupIds pq.Int64Array) ([]int64, error) {
	db := a.data.GetDb(ctx)
	var appIds []int64
	err := db.Model(&model.Application{}).Select("tb_application.id").
		Joins("inner join tb_app_address on tb_application.id = tb_app_address.app_id").
		Where("tb_application.group_ids && ?", pq.Array(appGroupIds)).Find(&appIds).Error
	if err != nil {
		a.log.Errorf("getStrategyAppIds err:%v", err)
		return nil, err
	}
	return appIds, nil
}

func (a appRepo) UserIdExists(ctx context.Context, userID string) (bool, error) {
	db := a.data.GetDb(ctx)
	var count int64
	err := db.Table("tb_user_entity").Where("id = ?", userID).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// RemoveInvalidDedicatedUser 从 tb_virtual_ip_pools 表中移除无效用户的独享IP配置
func (a appRepo) RemoveInvalidDedicatedUser(ctx context.Context, poolID int, userID string) error {
	db := a.data.GetDb(ctx)

	query := `
	UPDATE tb_virtual_ip_pools 
	SET directory_config = jsonb_set(
		directory_config, 
		'{dedicated_users}', 
		COALESCE(
			(
				SELECT jsonb_agg(elem)
				FROM jsonb_array_elements(directory_config->'dedicated_users') AS elem
				WHERE elem->>'user_id' != ?
			),
			'[]'::jsonb
		)
	)
	WHERE id = ? 
	AND deleted_at IS NULL
	AND directory_config ? 'dedicated_users'
	`

	result := db.Exec(query, userID, poolID)
	if result.Error != nil {
		a.log.Errorf("移除无效用户 %s 的独享IP配置失败: %v", userID, result.Error)
		return result.Error
	}

	if result.RowsAffected > 0 {
		a.log.Debugf("已从池 %d 中移除无效用户 %s 的独享IP配置", poolID, userID)
	} else {
		a.log.Warnf("池 %d 中未找到用户 %s 的配置，可能已被移除", poolID, userID)
	}

	return nil
}

func (a appRepo) GetUserIdByGroups(ctx context.Context, userGroupIds pq.StringArray) ([]string, error) {
	db := a.data.GetDb(ctx)
	sql := `
	SELECT DISTINCT id
	FROM tb_user_entity
	WHERE group_id IN (with RECURSIVE k_group AS
		(SELECT *
		FROM tb_user_group
		WHERE tb_user_group.id IN
			(?)
			UNION all
			SELECT tb_user_group.*
			FROM tb_user_group, k_group
			WHERE tb_user_group.parent_group_id = k_group.id)
			SELECT id
			FROM k_group);
	`
	var userIds []string
	err := db.Raw(sql, ([]string)(userGroupIds)).Find(&userIds).Error
	if err != nil {
		a.log.Errorf("getUserIdByGroups err:%v", err)
		return nil, err
	}
	return userIds, nil
}

// SeStrategy 根据网关id 获取策略信息
func (a appRepo) SeStrategy(ctx context.Context, applianceId uint64) ([]dto.SeStrategy, error) {
	db := a.data.GetDb(ctx)
	var respList []dto.SeStrategy
	sql := `
	with tmp_app as (select t1.id as appId, t1.group_ids as appGroupId
					 from tb_application t1
							  inner join tb_se_app_relation t2 on t1.id = t2.app_id
					 where se_id = ?)
	select distinct t1.id, t1.app_ids, t1.app_group_ids, t1.user_ids, t1.user_group_ids, t1.start_time, t1.end_time,t1.enable_log,t1.enable_all_user,t1.enable_all_app,t1.strategy_name,t1.role_ids
	from tb_access_strategy t1
			 inner join tmp_app on tmp_app.appId = any (t1.app_ids) or tmp_app.appGroupId && (t1.app_group_ids)
	where t1.strategy_status = 1
	`
	err := db.Raw(sql, applianceId).Find(&respList).Error
	if err != nil {
		a.log.Errorf("seStrategyAppGroup err:%v", err)
		return nil, err
	}
	return respList, nil
}

func (a appRepo) SeGetApp(ctx context.Context, applianceId uint64) ([]dto.AppInfoDto, error) {
	db := a.data.GetDb(ctx)
	var res []dto.AppInfoDto

	find := db.Model(model.SeAppRelation{}).
		Select("tb_application.app_name,tb_application.id as app_id,tb_app_address.address,tb_app_address.port,tb_app_address.protocol").
		Joins("inner join tb_application on tb_se_app_relation.app_id = tb_application.id").
		Joins("inner join tb_app_address on tb_se_app_relation.app_id = tb_app_address.app_id").
		Where("tb_se_app_relation.se_id = ?", applianceId).
		Where("tb_application.app_status in (1,2)").Find(&res)

	if !errors.Is(find.Error, gorm.ErrRecordNotFound) && find.Error != nil {
		return nil, find.Error
	}
	return res, nil
}

func (a appRepo) WebGatewayRsApp(ctx context.Context, applianceId uint64) ([]dto.ApplicationGatewayDto, error) {
	db := a.data.GetDb(ctx)
	var res []dto.ApplicationGatewayDto

	find := db.Model(model.SeAppRelation{}).
		Select("tb_application.app_name,tb_application.app_status,tb_application.app_type,tb_application.id,tb_application.server_address,tb_application.server_schema,"+
			"tb_application.publish_address,tb_application.publish_schema,tb_application.web_compatible_config,tb_application.health_config,tb_application.uri").
		Joins("inner join tb_application on tb_se_app_relation.app_id = tb_application.id").
		Where("tb_application.app_status in (1,2) AND tb_application.app_type in (?,?)", "web", "tun").Find(&res)

	if !errors.Is(find.Error, gorm.ErrRecordNotFound) && find.Error != nil {
		return nil, find.Error
	}
	return res, nil
}

func (a appRepo) WebGatewayRsCrt(ctx context.Context) ([]dto.CrtGatewayDto, error) {
	db := a.data.GetDb(ctx)
	var res []dto.CrtGatewayDto
	find := db.Table("tb_certificate").
		Select("id,certificate,private_key,domain").Find(&res)
	if !errors.Is(find.Error, gorm.ErrRecordNotFound) && find.Error != nil {
		return nil, find.Error
	}
	return res, nil
}

func NewAppRepo(data *Data, logger log.Logger) biz.AppRepo {
	return &appRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// 新增虚拟IP相关方法
func (a appRepo) WebGatewayVirtualIPPools(ctx context.Context, applianceId uint64) ([]dto.VirtualIPPoolDto, error) {
	db := a.data.GetDb(ctx)

	// 查询与指定网关关联的虚拟IP池
	query := `
        SELECT 
            vip.id,
            vip.name,
            vip.ip_range,
            vip.pool_type::text as pool_type,
            vip.allocation_policy,
            vip.release_policy,
            vip.directory_config,
            vip.is_enabled
        FROM tb_virtual_ip_pools vip
        WHERE vip.se_id = ? AND vip.is_enabled = true AND vip.deleted_at IS NULL
        ORDER BY vip.created_at ASC
    `

	type poolResult struct {
		ID               int    `gorm:"column:id"`
		Name             string `gorm:"column:name"`
		IPRange          string `gorm:"column:ip_range"`
		PoolType         string `gorm:"column:pool_type"`
		AllocationPolicy string `gorm:"column:allocation_policy"`
		ReleasePolicy    string `gorm:"column:release_policy"`
		DirectoryConfig  string `gorm:"column:directory_config"`
		IsEnabled        bool   `gorm:"column:is_enabled"`
	}

	var results []poolResult
	err := db.Raw(query, applianceId).Scan(&results).Error
	if err != nil {
		a.log.Errorf("WebGatewayVirtualIPPools query err: %v", err)
		return nil, err
	}

	var pools []dto.VirtualIPPoolDto
	for _, result := range results {
		pool := dto.VirtualIPPoolDto{
			ID:       strconv.Itoa(result.ID),
			Name:     result.Name,
			IPRange:  result.IPRange,
			PoolType: result.PoolType,
		}

		// 解析分配策略JSON
		if result.AllocationPolicy != "" {
			var allocPolicy dto.AllocationPolicyDto
			if err := json.Unmarshal([]byte(result.AllocationPolicy), &allocPolicy); err == nil {
				pool.AllocationPolicy = &allocPolicy
				// 从分配策略中提取时间配置
				pool.IPExpiryDuration = allocPolicy.IPExpiryDuration
				pool.CleanupInterval = allocPolicy.CleanupInterval
			}
		}

		// 解析释放策略JSON
		if result.ReleasePolicy != "" {
			var releasePolicy dto.ReleasePolicyDto
			if err := json.Unmarshal([]byte(result.ReleasePolicy), &releasePolicy); err == nil {
				pool.ReleasePolicy = &releasePolicy
			}
		}

		// 根据池类型解析directory_config
		if result.DirectoryConfig != "" {
			// 创建包含原始JSON的DirectoryConfig
			dirConfig := dto.DirectoryConfigDto{
				DirectoryID:   pool.ID,
				DirectoryName: pool.PoolType + "_config",
				UserGroups:    []string{},
				Config:        result.DirectoryConfig, // 保存原始JSON
			}
			pool.DirectoryConfigs = append(pool.DirectoryConfigs, dirConfig)

			// 同时解析独享配置
			if result.PoolType == "dedicated" {
				var directoryConfigData struct {
					DedicatedUsers []dto.DedicatedIPConfigDto `json:"dedicated_users"`
				}
				if err := json.Unmarshal([]byte(result.DirectoryConfig), &directoryConfigData); err == nil {
					pool.DedicatedConfigs = directoryConfigData.DedicatedUsers
				} else {
					a.log.Errorf("解析独享配置失败: %v, JSON: %s", err, result.DirectoryConfig)
				}
			}
		}

		pools = append(pools, pool)
	}

	return pools, nil
}

func (a appRepo) UpdateAppHealthStatus(ctx context.Context, req *pb.ReportHealthCheckResultReq) error {
	appStatus := req.HealthResults

	db := a.data.GetDb(ctx)

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		a.log.Errorf("Failed to begin transaction: %v", tx.Error)
		return tx.Error
	}

	// 使用defer确保在函数退出时正确处理事务
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			a.log.Errorf("Recovered from panic in UpdateAppHealthStatus, transaction rolled back: %v", r)
		}
	}()

	// 遍历appStatus映射，更新每个应用的health_status字段
	for appIDStr, healthStatus := range appStatus {
		// 将字符串形式的appID转换为uint64
		appID, err := strconv.ParseUint(appIDStr, 10, 64)
		if err != nil {
			a.log.Errorf("Failed to parse appID %s: %v", appIDStr, err)
			tx.Rollback()
			return err
		}

		// 根据appID查询tb_application表中的health_status字段
		// 定义一个结构体来接收查询结果
		var appInfo struct {
			HealthStatus int32  `gorm:"column:health_status"`
			AppName      string `gorm:"column:app_name"`
			HealthConfig string `gorm:"column:health_config"`
		}

		err = tx.Table("tb_application").
			Select("health_status,app_name,health_config").
			Where("id = ?", appID).
			Scan(&appInfo).Error

		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				a.log.Warnf("No application found with id %d for health status update", appID)
				continue
			} else {
				a.log.Errorf("Failed to query health_status for appID %d: %v", appID, err)
				tx.Rollback()
				return err
			}
		}

		// 只有当表中的health_status不等于healthStatus时才更新
		if appInfo.HealthStatus != healthStatus {
			// 使用单个SQL语句同时更新应用状态和插入日志记录
			updateResult := tx.Table("tb_application").
				Where("id = ?", appID).
				Update("health_status", healthStatus)

			if updateResult.Error != nil {
				a.log.Errorf("Failed to update health_status for appID %d: %v", appID, updateResult.Error)
				tx.Rollback()
				return updateResult.Error
			}

			if updateResult.RowsAffected > 0 {
				a.log.Debugf("Successfully updated health_status for appID %d from %d to %d, rows affected: %d",
					appID, appInfo.HealthStatus, healthStatus, updateResult.RowsAffected)

				// 插入日志，使用事务确保数据一致性
				id, err := snowflake.Sf.GetId()
				if err != nil {
					a.log.Errorf("Failed to generate ID for health log: %v", err)
					tx.Rollback()
					return err
				}

				healthLog := map[string]interface{}{
					"id":            id,
					"app_id":        appID,
					"health_status": healthStatus,
					"health_config": appInfo.HealthConfig,
					"app_name":      appInfo.AppName,
					"created_at":    time.Now(),
				}

				logResult := tx.Table("tb_app_health_log").Create(healthLog)
				if logResult.Error != nil {
					a.log.Errorf("Failed to insert health log for appID %d: %v", appID, logResult.Error)
					tx.Rollback()
					return logResult.Error
				}
				a.log.Debugf("Successfully inserted health log for appID %d", appID)
			}
		} else {
			a.log.Debugf("health_status for appID %d is already %d, no update needed", appID, healthStatus)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		a.log.Errorf("Failed to commit transaction: %v", err)
		return err
	}

	a.log.Debugf("Successfully updated health status for %d applications", len(appStatus))

	return nil
}

func (a appRepo) SaveVirtualIPAllocations(ctx context.Context, applianceId string, allocations []dto.IPAllocationDto, poolStats map[string]dto.PoolStatsDto) error {
	db := a.data.GetDb(ctx)

	// 开始事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 只保存IP分配记录到 tb_virtual_ip_allocations
	for _, alloc := range allocations {
		// 先查询 pool_id
		var poolID int
		err := tx.Table("tb_virtual_ip_pools").
			Select("id").
			Where("name = ? AND se_id = ? AND deleted_at IS NULL", alloc.PoolName, applianceId).
			Scan(&poolID).Error

		if err != nil {
			tx.Rollback()
			a.log.Errorf("Failed to find pool %s for appliance %s: %v", alloc.PoolName, applianceId, err)
			return err
		}

		// 查询用户真实姓名
		userName := a.getUserDisplayName(ctx, alloc.UserID)
		if userName == "" {
			userName = alloc.UserID
		}

		if alloc.DeviceID != "0" && alloc.DeviceID != "" && alloc.Status == "active" {
			err = a.updateDeviceIdIfNeeded(tx, alloc, applianceId)
			if err != nil {
				a.log.Warnf("更新设备ID失败但继续处理: %v", err)
			}
		}
		// 根据事件类型处理不同的逻辑
		switch alloc.Status {
		case "active":
			if alloc.LastUsedAt == 0 {
				// 新分配事件：总是创建新记录
				err = a.createNewAllocationRecord(tx, poolID, userName, alloc, applianceId)
			} else {
				// 使用更新事件：更新现有活跃记录的使用时间
				err = a.updateActiveAllocationUsage(tx, alloc, applianceId)
				if err != nil {
					a.log.Warnf("更新活跃记录失败: user=%s, ip=%s, err=%v", alloc.UserID, alloc.IPAddress, err)
					// 如果更新失败，不再尝试创建新记录，因为这不是分配事件
					return err
				}
			}
		case "released", "expired":
			// 释放或过期事件：更新现有活跃记录的状态
			err = a.updateAllocationStatus(tx, alloc, applianceId)
		case "init":
			// 服务初始化事件：释放所有活跃IP（在事务外处理以避免冲突）
			a.log.Debugf("收到虚拟IP服务初始化事件，网关: %s", applianceId)
			continue
		default:
			// 其他状态：创建新记录
			err = a.createNewAllocationRecord(tx, poolID, userName, alloc, applianceId)
		}

		if err != nil {
			tx.Rollback()
			a.log.Errorf("SaveVirtualIPAllocations operation err: %v", err)
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		a.log.Errorf("SaveVirtualIPAllocations commit err: %v", err)
		return err
	}

	a.log.Debugf("Saved %d IP allocation events for appliance %s", len(allocations), applianceId)
	return nil
}

// createNewAllocationRecord 创建新的分配记录
func (a appRepo) createNewAllocationRecord(tx *gorm.DB, poolID int, userName string, alloc dto.IPAllocationDto, applianceId string) error {
	allocRecord := map[string]interface{}{
		"pool_id":                poolID,
		"user_id":                alloc.UserID,
		"user_name":              userName,
		"device_id":              alloc.DeviceID,
		"virtual_ip":             alloc.IPAddress,
		"se_id":                  applianceId,
		"allocated_at":           time.Unix(alloc.AllocatedAt, 0),
		"expires_at":             time.Unix(alloc.ExpiresAt, 0),
		"status":                 alloc.Status,
		"total_upstream_bytes":   alloc.TotalUpstreamBytes,
		"total_downstream_bytes": alloc.TotalDownstreamBytes,
	}

	// 新分配记录不设置 last_used_at（保持为 NULL）
	if alloc.LastUsedAt == 0 {
		allocRecord["last_used_at"] = time.Unix(alloc.AllocatedAt, 0)
	} else {
		allocRecord["last_used_at"] = time.Unix(alloc.LastUsedAt, 0)
	}

	return tx.Table("tb_virtual_ip_allocations").Create(allocRecord).Error
}

func (a appRepo) updateDeviceIdIfNeeded(tx *gorm.DB, alloc dto.IPAllocationDto, applianceId string) error {
	// 查找该用户IP的活跃记录中设备ID为0的记录
	var count int64
	err := tx.Table("tb_virtual_ip_allocations").
		Where("user_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active' AND (device_id = '0' OR device_id = '')",
			alloc.UserID, alloc.IPAddress, applianceId).
		Count(&count).Error

	if err != nil {
		return err
	}

	if count > 0 {
		// 找到设备ID为0的记录，更新为真实设备ID
		result := tx.Table("tb_virtual_ip_allocations").
			Where("user_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active' AND (device_id = '0' OR device_id = '')",
				alloc.UserID, alloc.IPAddress, applianceId).
			Update("device_id", alloc.DeviceID)

		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected > 0 {
			a.log.Debugf("设备ID升级成功: user=%s, ip=%s, 从web代理(0)更新为真实设备(%s), 影响行数=%d",
				alloc.UserID, alloc.IPAddress, alloc.DeviceID, result.RowsAffected)
		}
	}

	return nil
}

// updateActiveAllocationUsage 更新活跃记录的使用时间
func (a appRepo) updateActiveAllocationUsage(tx *gorm.DB, alloc dto.IPAllocationDto, applianceId string) error {
	// 查找该用户在该网关上的活跃记录
	updateFields := map[string]interface{}{
		"last_used_at":           time.Unix(alloc.LastUsedAt, 0),
		"total_upstream_bytes":   alloc.TotalUpstreamBytes,
		"total_downstream_bytes": alloc.TotalDownstreamBytes,
	}

	// 更新过期时间（如果提供）
	if alloc.ExpiresAt > 0 {
		updateFields["expires_at"] = time.Unix(alloc.ExpiresAt, 0)
	}

	if alloc.DeviceID != "0" && alloc.DeviceID != "" {
		// 先查询现有记录的设备ID
		var existingDeviceID string
		err := tx.Table("tb_virtual_ip_allocations").
			Select("device_id").
			Where("user_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active'",
				alloc.UserID, alloc.IPAddress, applianceId).
			Scan(&existingDeviceID).Error

		if err == nil && (existingDeviceID == "0" || existingDeviceID == "") {
			// 如果现有设备ID是0或空，则更新为真实设备ID
			updateFields["device_id"] = alloc.DeviceID
			a.log.Debugf("更新设备ID: user=%s, ip=%s, 从 %s 更新为 %s",
				alloc.UserID, alloc.IPAddress, existingDeviceID, alloc.DeviceID)
		}
	}

	result := tx.Table("tb_virtual_ip_allocations").
		Where("user_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active'",
			alloc.UserID, alloc.IPAddress, applianceId).
		Updates(updateFields)

	if result.Error != nil {
		return result.Error
	}
	// 如果没有找到活跃记录，返回特定错误以便调用方创建新记录
	if result.RowsAffected == 0 {
		a.log.Debugf("No active allocation found for usage update: user=%s, device=%s, ip=%s",
			alloc.UserID, alloc.DeviceID, alloc.IPAddress)
		return fmt.Errorf("no active allocation found for user=%s, ip=%s", alloc.UserID, alloc.IPAddress)
	} else {
		a.log.Debugf("Updated usage for user=%s, device=%s, ip=%s, rows_affected=%d",
			alloc.UserID, alloc.DeviceID, alloc.IPAddress, result.RowsAffected)
	}

	return nil
}

// updateAllocationStatus 更新分配记录的状态
func (a appRepo) updateAllocationStatus(tx *gorm.DB, alloc dto.IPAllocationDto, applianceId string) error {
	updateFields := map[string]interface{}{
		"status":                 alloc.Status,
		"total_upstream_bytes":   alloc.TotalUpstreamBytes,
		"total_downstream_bytes": alloc.TotalDownstreamBytes,
	}

	// 设置最后使用时间（释放时刻）
	if alloc.LastUsedAt > 0 {
		updateFields["last_used_at"] = time.Unix(alloc.LastUsedAt, 0)
	}
	var result *gorm.DB

	// 先尝试精确匹配（用户+设备+IP+状态）
	if alloc.DeviceID != "0" && alloc.DeviceID != "" {
		result = tx.Table("tb_virtual_ip_allocations").
			Where("user_id = ? AND device_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active'",
				alloc.UserID, alloc.DeviceID, alloc.IPAddress, applianceId).
			Updates(updateFields)
	} else {
		// 如果设备ID是0，直接按用户+IP匹配
		result = tx.Table("tb_virtual_ip_allocations").
			Where("user_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active'",
				alloc.UserID, alloc.IPAddress, applianceId).
			Updates(updateFields)
	}

	if result.Error != nil {
		return result.Error
	}

	// 🔥 如果精确匹配没找到记录，且当前是真实设备ID，尝试宽松匹配
	if result.RowsAffected == 0 && alloc.DeviceID != "0" && alloc.DeviceID != "" {
		// 先检查是否存在设备ID为0的活跃记录
		var existingDeviceID string
		err := tx.Table("tb_virtual_ip_allocations").
			Select("device_id").
			Where("user_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active'",
				alloc.UserID, alloc.IPAddress, applianceId).
			Scan(&existingDeviceID).Error

		if err == nil {
			// 如果找到记录且设备ID是0，同时更新设备ID和状态
			if existingDeviceID == "0" || existingDeviceID == "" {
				updateFields["device_id"] = alloc.DeviceID
				a.log.Infof("释放时更新设备ID: user=%s, ip=%s, 从 %s 更新为 %s",
					alloc.UserID, alloc.IPAddress, existingDeviceID, alloc.DeviceID)
			}

			// 按用户+IP匹配更新
			result = tx.Table("tb_virtual_ip_allocations").
				Where("user_id = ? AND virtual_ip = ? AND se_id = ? AND status = 'active'",
					alloc.UserID, alloc.IPAddress, applianceId).
				Updates(updateFields)

			if result.Error != nil {
				return result.Error
			}
		}
	}

	// 如果仍然没有找到活跃记录，记录警告
	if result.RowsAffected == 0 {
		a.log.Warnf("No active allocation found for status update: user=%s, device=%s, ip=%s, status=%s",
			alloc.UserID, alloc.DeviceID, alloc.IPAddress, alloc.Status)
	} else {
		a.log.Debugf("Successfully updated allocation status: user=%s, device=%s, ip=%s, status=%s, rows_affected=%d",
			alloc.UserID, alloc.DeviceID, alloc.IPAddress, alloc.Status, result.RowsAffected)
	}

	return nil
}

// GetVirtualIPGlobalSettings 获取虚拟IP全局设置
func (a appRepo) GetVirtualIPGlobalSettings(ctx context.Context) (*dto.VirtualIPGlobalSettingsDto, error) {
	db := a.data.GetDb(ctx)

	var result struct {
		ID                int    `gorm:"column:id"`
		Enabled           bool   `gorm:"column:enabled"`
		GlobalMaxDuration int    `gorm:"column:global_max_duration"`
		Description       string `gorm:"column:description"`
		CreatedAt         string `gorm:"column:created_at"`
		UpdatedAt         string `gorm:"column:updated_at"`
	}

	// 查询最新的全局设置（通常只有一条记录）
	err := db.Table("tb_virtual_ip_settings").
		Select("id, enabled, global_max_duration, description, created_at, updated_at").
		Order("id DESC").
		Limit(1).
		Scan(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果没有找到记录，返回默认设置
			a.log.Warnf("No virtual IP global settings found, returning default settings")
			return &dto.VirtualIPGlobalSettingsDto{
				ID:                0,
				Enabled:           false, // 默认禁用
				GlobalMaxDuration: 24,    // 默认24小时
				Description:       "Default virtual IP settings",
				CreatedAt:         "",
				UpdatedAt:         "",
			}, nil
		}
		a.log.Errorf("GetVirtualIPGlobalSettings query err: %v", err)
		return nil, err
	}

	// 转换为 DTO
	globalSettings := &dto.VirtualIPGlobalSettingsDto{
		ID:                result.ID,
		Enabled:           result.Enabled,
		GlobalMaxDuration: result.GlobalMaxDuration,
		Description:       result.Description,
		CreatedAt:         result.CreatedAt,
		UpdatedAt:         result.UpdatedAt,
	}

	a.log.Debugf("Retrieved virtual IP global settings: enabled=%v, max_duration=%d",
		globalSettings.Enabled, globalSettings.GlobalMaxDuration)

	return globalSettings, nil
}

// 添加获取用户显示名称的方法
func (a appRepo) getUserDisplayName(ctx context.Context, userID string) string {
	db := a.data.GetDb(ctx)

	var userName string
	err := db.Table("tb_user_entity").
		Select("name").
		Where("id = ?", userID).
		Scan(&userName).Error

	if err != nil {
		a.log.Debugf("Failed to get user name for %s: %v", userID, err)
		return ""
	}

	return userName
}

// 添加获取池统计信息的方法
func (a appRepo) GetVirtualIPPoolStats(ctx context.Context, applianceId string) ([]dto.PoolStatsDto, error) {
	db := a.data.GetDb(ctx)

	var results []dto.PoolStatsDto

	// 从主表实时计算统计信息
	err := db.Raw(`
        SELECT 
            p.name as pool_name,
            -- 计算总IP数
            32 as total_ips,
            COUNT(CASE WHEN a.status = 'active' THEN 1 END) as allocated_ips,
            32 - COUNT(CASE WHEN a.status = 'active' THEN 1 END) as available_ips,
            EXTRACT(epoch FROM NOW()) as last_updated
        FROM tb_virtual_ip_pools p
        LEFT JOIN tb_virtual_ip_allocations a ON p.id = a.pool_id
        WHERE p.se_id = ? AND p.deleted_at IS NULL
        GROUP BY p.id, p.name
    `, applianceId).Scan(&results).Error

	return results, err
}

func (a appRepo) SaveTrafficStats(ctx context.Context, applianceId string, trafficBuckets []dto.TrafficBucketDto) error {
	db := a.data.GetDb(ctx)

	// 批量插入流量统计数据
	if len(trafficBuckets) == 0 {
		return nil
	}

	// 准备批量插入数据
	var records []map[string]interface{}
	for _, bucket := range trafficBuckets {
		record := map[string]interface{}{
			"appliance_id":      applianceId,
			"ip_address":        bucket.IPAddress,
			"user_id":           bucket.UserID,
			"bucket_start_time": time.Unix(bucket.BucketStartTime, 0),
			"bucket_end_time":   time.Unix(bucket.BucketEndTime, 0),
			"upstream_bytes":    bucket.UpstreamBytes,
			"downstream_bytes":  bucket.DownstreamBytes,
			"session_count":     bucket.SessionCount,
			"created_at":        time.Now(),
		}
		records = append(records, record)
	}

	// 批量插入，使用分批处理避免单次插入过多数据
	batchSize := 1000
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}

		batch := records[i:end]
		if err := db.Table("tb_virtual_ip_traffic_stats").Create(batch).Error; err != nil {
			a.log.Errorf("SaveTrafficStats batch insert err: %v", err)
			return err
		}
	}

	a.log.Debugf("Saved %d traffic buckets for appliance %s", len(trafficBuckets), applianceId)

	return nil
}

// GetPendingGatewayCommands 获取待处理的网关命令
func (a appRepo) GetPendingGatewayCommands(ctx context.Context, applianceID int64) ([]virtualIPModel.GatewayCommand, error) {
	db := a.data.GetDb(ctx)

	var commands []virtualIPModel.GatewayCommand
	err := db.Where("se_id = ? AND status = ? AND (expires_at IS NULL OR expires_at > NOW())",
		applianceID, virtualIPModel.CommandStatusPending).
		Order("created_at ASC").
		Find(&commands).Error

	if err != nil {
		a.log.Errorf("GetPendingGatewayCommands err: %v", err)
		return nil, err
	}

	return commands, nil
}

// UpdateGatewayCommandStatus 更新网关命令状态
func (a appRepo) UpdateGatewayCommandStatus(ctx context.Context, commandID string, status string, errorMessage string) error {
	db := a.data.GetDb(ctx)

	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if status == virtualIPModel.CommandStatusProcessing {
		updates["processed_at"] = time.Now()
	} else if status == virtualIPModel.CommandStatusCompleted || status == virtualIPModel.CommandStatusFailed {
		updates["completed_at"] = time.Now()
	}

	if errorMessage != "" {
		updates["error_message"] = errorMessage
	}

	err := db.Model(&virtualIPModel.GatewayCommand{}).
		Where("command_id = ?", commandID).
		Updates(updates).Error

	if err != nil {
		a.log.Errorf("UpdateGatewayCommandStatus err: %v", err)
		return err
	}

	return nil
}

// ReleaseAllActiveVirtualIPs 释放指定网关的所有活跃虚拟IP分配
// 用于虚拟IP服务初始化时清理数据库中的活跃状态
func (a appRepo) ReleaseAllActiveVirtualIPs(ctx context.Context, applianceId string) error {
	db := a.data.GetDb(ctx)
	// 查询该网关的所有活跃IP分配记录
	var activeAllocations []struct {
		ID        int    `gorm:"column:id"`
		UserID    string `gorm:"column:user_id"`
		VirtualIP string `gorm:"column:virtual_ip"`
		PoolName  string `gorm:"column:pool_name"`
		DeviceID  string `gorm:"column:device_id"`
	}

	query := `
		SELECT 
			via.id,
			via.user_id,
			via.virtual_ip,
			vip.name as pool_name,
			via.device_id
		FROM tb_virtual_ip_allocations via
		INNER JOIN tb_virtual_ip_pools vip ON via.pool_id = vip.id
		WHERE via.se_id = ? AND via.status = 'active' AND vip.deleted_at IS NULL
	`

	err := db.Raw(query, applianceId).Scan(&activeAllocations).Error
	if err != nil {
		a.log.Errorf("查询网关 %s 的活跃虚拟IP分配失败: %v", applianceId, err)
		return err
	}
	if len(activeAllocations) == 0 {
		a.log.Infof("网关 %s 没有活跃的虚拟IP分配记录", applianceId)
		return nil
	}

	// 批量更新状态为已释放
	now := time.Now()
	updateResult := db.Table("tb_virtual_ip_allocations").
		Where("se_id = ? AND status = 'active'", applianceId).
		Updates(map[string]interface{}{
			"status":       "released",
			"last_used_at": now,
		})

	if updateResult.Error != nil {
		a.log.Errorf("批量更新网关 %s 的虚拟IP状态失败: %v", applianceId, updateResult.Error)
		return updateResult.Error
	}

	a.log.Debugf("网关 %s 虚拟IP服务初始化: 成功释放 %d 个活跃IP分配记录",
		applianceId, updateResult.RowsAffected)

	// 记录详细信息用于调试
	for _, alloc := range activeAllocations {
		a.log.Debugf("释放活跃IP: user=%s, ip=%s, pool=%s, device=%s",
			alloc.UserID, alloc.VirtualIP, alloc.PoolName, alloc.DeviceID)
	}

	return nil
}

// WebGatewayRsSSOIDP 网关拉取SSO IDP配置
func (a appRepo) WebGatewayRsSSOIDP(ctx context.Context, idpTypes []string) ([]dto.SSOIDPDto, error) {
	db := a.data.GetDb(ctx)

	corpId := "2efd2601-d800-48b3-9035-9ed23694ba0f"
	a.log.Debugf("查询SSO IDP配置: corp_id=%s, types=%v", corpId, idpTypes)

	// 查询IDP基本信息，按类型分组，每个类型只取创建时间最新的一个
	var idps []struct {
		ID        string    `json:"id"`
		Name      string    `json:"name"`
		Type      string    `json:"type"`
		CorpID    string    `json:"corp_id"`
		Enable    bool      `json:"enable"`
		CreatedAt time.Time `json:"created_at"`
		UpdatedAt time.Time `json:"updated_at"`
	}

	// 使用窗口函数获取每个类型最新的IDP
	subQuery := db.Table("tb_identity_provider").
		Select("*, ROW_NUMBER() OVER (PARTITION BY type ORDER BY created_at DESC) as rn").
		Where("corp_id = ? AND enable = true", corpId)

	if len(idpTypes) > 0 {
		subQuery = subQuery.Where("type IN ?", idpTypes)
	}

	err := db.Table("(?) as ranked_idp", subQuery).
		Select("id, name, type, corp_id, enable, created_at, updated_at").
		Where("rn = 1").
		Find(&idps).Error

	if err != nil {
		a.log.Errorf("查询IDP基本信息失败: %v", err)
		return nil, err
	}

	if len(idps) == 0 {
		a.log.Debugf("未找到匹配的IDP配置: corp_id=%s, types=%v", corpId, idpTypes)
		return []dto.SSOIDPDto{}, nil
	}

	// 获取IDP的属性信息
	var idpIds []string
	for _, idp := range idps {
		idpIds = append(idpIds, idp.ID)
	}

	var attributes []struct {
		ProviderID string `json:"provider_id"`
		Key        string `json:"key"`
		Value      string `json:"value"`
	}

	err = db.Table("tb_identity_provider_attribute").
		Select("provider_id, key, value").
		Where("provider_id IN ?", idpIds).
		Find(&attributes).Error

	if err != nil {
		a.log.Errorf("查询IDP属性信息失败: %v", err)
		return nil, err
	}

	// 将属性按provider_id分组
	attrMap := make(map[string]map[string]string)
	for _, attr := range attributes {
		if attrMap[attr.ProviderID] == nil {
			attrMap[attr.ProviderID] = make(map[string]string)
		}
		attrMap[attr.ProviderID][attr.Key] = attr.Value
	}

	// 转换为SSOIDPDto
	var result []dto.SSOIDPDto
	for _, idp := range idps {
		idpDto := dto.SSOIDPDto{
			ID:         idp.ID,
			Name:       idp.Name,
			Type:       idp.Type,
			CorpId:     idp.CorpID,
			Enable:     idp.Enable,
			CreatedAt:  idp.CreatedAt,
			UpdatedAt:  idp.UpdatedAt,
			Attributes: attrMap[idp.ID],
		}
		// 根据IDP类型适配属性到标准字段
		attrs := attrMap[idp.ID]
		if attrs != nil {
			switch idp.Type {
			case "feishu":
				// 飞书属性适配
				if appId := attrs["fs_app_id"]; appId != "" {
					idpDto.AppId = appId
				}
				if appSecret := attrs["fs_app_secret"]; appSecret != "" {
					idpDto.AppSecret = appSecret
				}
				// 飞书没有单独的corpId，通常使用app_id
				if idpDto.AppId != "" {
					idpDto.CorpId = idpDto.AppId
				}
			case "dingtalk":
				// 钉钉属性适配
				if appKey := attrs["dingtalk_app_key"]; appKey != "" {
					idpDto.AppKey = appKey
				}
				if appSecret := attrs["dingtalk_app_secret"]; appSecret != "" {
					idpDto.AppSecret = appSecret
				}
				if appcorpId := attrs["dingtalk_dd_corp_id"]; appcorpId != "" {
					idpDto.CorpId = appcorpId
				}
				// 钉钉通常使用app_key作为主要标识
				if idpDto.AppKey != "" {
					idpDto.AppId = idpDto.AppKey
				}
			case "qiyewx":
				// 企业微信属性适配
				if appcorpId := attrs["wx_corp_id"]; appcorpId != "" {
					idpDto.CorpId = appcorpId
				}
				if agentId := attrs["wx_agent_id"]; agentId != "" {
					idpDto.AppId = agentId // 企业微信用 agent_id 作为 app_id
				}
				if agentSecret := attrs["wx_agent_secret"]; agentSecret != "" {
					idpDto.AppSecret = agentSecret
				}
			}

			// 通用属性适配
			if redirectUri := attrs["redirect_uri"]; redirectUri != "" {
				idpDto.RedirectUri = redirectUri
			}
		}

		result = append(result, idpDto)
	}

	a.log.Debugf("成功获取SSO IDP配置: corp_id=%s, 共%d个配置", corpId, len(result))
	return result, nil
}

func (a appRepo) GetPlatformDomainByApplianceID(ctx context.Context, applianceId uint64) (string, error) {
	db := a.data.GetDb(ctx)

	var installCmd model.ApplianceInstall
	err := db.Model(&installCmd).Where("appliance_id = ?", applianceId).First(&installCmd).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", fmt.Errorf("appliance install record not found for id: %d", applianceId)
		}
		return "", fmt.Errorf("failed to query appliance install: %w", err)
	}

	return installCmd.PlatformDomain, nil
}

// WebGatewayWechatVerify 网关拉取微信验证文件
func (a appRepo) WebGatewayWechatVerify(ctx context.Context, applianceId string) ([]dto.WechatVerifyFileDto, bool, error) {
	// 通过HTTP API调用sys-panel服务获取微信验证文件
	a.log.Debugf("通过API获取微信验证文件: appliance_id=%s", applianceId)

	// sys-panel服务的API端点
	apiURL := "http://localhost:18080/system/v1/wechat-verify"

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		a.log.Errorf("创建HTTP请求失败: %v", err)
		return []dto.WechatVerifyFileDto{}, true, nil
	}

	// 添加API Key头部用于认证
	req.Header.Set("X-API-KEY", "c34ff517ed828d279f91c884caac8f1be1efb999")
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		a.log.Errorf("调用sys-panel API失败: %v", err)
		// 返回空列表而不是错误，避免影响网关运行
		a.log.Warnf("由于API调用失败，返回空的微信验证文件列表")
		return []dto.WechatVerifyFileDto{}, true, nil
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		a.log.Errorf("sys-panel API返回非200状态码: %d", resp.StatusCode)
		return []dto.WechatVerifyFileDto{}, true, nil
	}

	// 定义API响应结构 - 匹配sys-panel handler的返回格式
	type APIResponse struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Total int64 `json:"total"`
			List  []struct {
				ID          uint   `json:"id"`
				FileName    string `json:"fileName"`
				Content     string `json:"content"`
				IsEnabled   bool   `json:"isEnabled"`
				Description string `json:"description"`
				CreatedAt   string `json:"createdAt"`
				UpdatedAt   string `json:"updatedAt"`
			} `json:"list"`
		} `json:"data"`
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		a.log.Errorf("解析sys-panel API响应失败: %v", err)
		return []dto.WechatVerifyFileDto{}, true, nil
	}

	// 检查API响应码
	if apiResp.Code != 200 {
		a.log.Errorf("sys-panel API业务错误: code=%d, message=%s", apiResp.Code, apiResp.Message)
		return []dto.WechatVerifyFileDto{}, true, nil
	}

	// 转换API响应数据为DTO格式
	var result []dto.WechatVerifyFileDto
	for _, item := range apiResp.Data.List {
		// 解析时间字符串
		var createdAt, updatedAt time.Time
		if item.CreatedAt != "" {
			if t, err := time.Parse(time.RFC3339, item.CreatedAt); err == nil {
				createdAt = t
			}
		}
		if item.UpdatedAt != "" {
			if t, err := time.Parse(time.RFC3339, item.UpdatedAt); err == nil {
				updatedAt = t
			}
		}

		result = append(result, dto.WechatVerifyFileDto{
			ID:          fmt.Sprintf("%d", item.ID),
			FileName:    item.FileName,
			Content:     item.Content,
			IsEnabled:   item.IsEnabled,
			Description: item.Description,
			CreatedAt:   createdAt,
			UpdatedAt:   updatedAt,
		})
	}

	a.log.Debugf("成功从API获取微信验证文件: appliance_id=%s, count=%d", applianceId, len(result))
	return result, true, nil
}
