package common

import (
	"path/filepath"
	"sync"
	"time"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/pkg/utils"
	"github.com/fsnotify/fsnotify"
	"github.com/go-ini/ini"
)

var (
	ServiceIniPath    = filepath.Join(utils.GetConfigDir(), "config", "service_config.ini")
	writeOrCreateMask = fsnotify.Write | fsnotify.Create
)

var (
	DdrSwitch = false
)

func closeWatcher(watcher *fsnotify.Watcher) {
	if watcher != nil {
		_ = watcher.Close()
	}
}

func StartWatchServiceIni() {
	// 初始加载下
	ddrSwitch, err := getBoolSwitch("ddr_switch")
	if err == nil {
		DdrSwitch = ddrSwitch
		global.Logger.Sugar().Infof("init ddr_switch is %v", ddrSwitch)
	}
	// 无限循环避免报错后中断监听
	for {
		// 注册一个watcher 监听配置文件变化
		watcher, err := fsnotify.NewWatcher()
		if err != nil {
			global.Logger.Sugar().Errorf("new service_ini watcher err: %v", err)
			closeWatcher(watcher)
			continue
		}
		err = watcher.Add(ServiceIniPath)
		if err != nil {
			global.Logger.Sugar().Errorf("add water path err: %v ,path: %v", err, ServiceIniPath)
			closeWatcher(watcher)
			continue
		}

		eventsWG := sync.WaitGroup{}
		eventsWG.Add(1)
		// 开个线程监听回传信号量
		go func() {
			defer eventsWG.Done()
			for {
				select {
				case event, ok := <-watcher.Events:
					if !ok {
						return
					}
					// 处理变更事件
					doChange(event)
				case err, ok := <-watcher.Errors:
					if ok {
						global.Logger.Sugar().Errorf("watchering service_ini err: %v", err)
					}
					return
				}
			}
		}()
		eventsWG.Wait()
		closeWatcher(watcher)
		time.Sleep(time.Minute * 2)
	}

}

func PollSwitch() {
	for {
		ddrSwitch, err := getBoolSwitch("ddr_switch")
		if err == nil {
			DdrSwitch = ddrSwitch
		}
		time.Sleep(time.Minute * 30)
	}
}

func doChange(event fsnotify.Event) {
	if event.Name != ServiceIniPath {
		return
	}
	if event.Op&writeOrCreateMask == 0 {
		return
	}

	ddrSwitch, err := getBoolSwitch("ddr_switch")
	if err != nil {
		return
	}
	// 存在变更输出日志
	if ddrSwitch != DdrSwitch {
		global.Logger.Sugar().Infof("ddr_switch is %v now,old is: %v", ddrSwitch, DdrSwitch)
	}
	DdrSwitch = ddrSwitch
}

func getBoolSwitch(key string) (bool, error) {
	// 防止文件还未完全写入读取不到key的情况
	time.Sleep(time.Millisecond * 500)
	serviceCfgIni, err := ini.Load(ServiceIniPath)
	if err != nil {
		global.Logger.Sugar().Errorf("read service_config.ini err: %v", err)
		return false, err
	}

	// 获取配置项的值
	keyObj := serviceCfgIni.Section("service").Key(key)
	keyValue := keyObj.String()

	// 如果配置项值为空字符串，设置默认值为 false
	if keyValue == "" {
		global.Logger.Sugar().Warnf("config key %s is empty, using default value: false", key)
		return false, nil
	}

	boolSwitch, err := keyObj.Bool()
	if err != nil {
		global.Logger.Sugar().Errorf("get %s err: %v, value: '%s'", key, err, keyValue)
		return false, err
	}
	return boolSwitch, nil
}
