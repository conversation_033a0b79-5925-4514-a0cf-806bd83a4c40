package repository

import (
	alertComm "asdsec.com/asec/platform/app/console/app/alert/common"
	"asdsec.com/asec/platform/app/console/app/alert/model"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
)

type notifyRepository struct {
}

func (n *notifyRepository) GetRobot(ctx context.Context, tenantId, robotId uint64) (*model.NotifySetting, error) {
	robot := new(model.NotifySetting)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return &model.NotifySetting{}, err
	}
	conn := db.Table(alertComm.NotifyTable).Where("tenant_id = ?", tenantId)
	err = conn.Where("id = ?", robotId).First(&robot).Error
	return robot, err
}

func (n *notifyRepository) AddRobot(ctx context.Context, tenantId uint64, setting *model.NotifySetting) error {
	var robot []model.NotifySetting
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	conn := db.Table(alertComm.NotifyTable)
	//插入之前先查询name是否存在
	err = conn.Where("robot_name = ? and tenant_id = ?", setting.RobotName, tenantId).Find(&robot).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}
	if len(robot) > 0 {
		return errors.New("robot name exit")
	}
	return conn.Create(setting).Error
}

func (n *notifyRepository) DelRobot(ctx context.Context, tenantId uint64, robotId []uint64) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	conn := db.Table(alertComm.NotifyTable)
	return conn.Where("tenant_id = ? and id in (?)", tenantId, robotId).Delete(model.NotifySetting{}).Error
}

func (n *notifyRepository) UpdateRobot(ctx context.Context, tenantId, robotId uint64, robot *model.NotifySetting) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	conn := db.Table(alertComm.NotifyTable)
	return conn.Where("tenant_id = ? and id = ?", tenantId, robotId).Updates(robot).Error
}

func (n *notifyRepository) QueryAllRobot(ctx context.Context, tenantId uint64, robotName, robotPlat string, limit, offset int) (map[string]interface{}, error) {
	var robots []model.NotifyResp
	var count int64
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	sql := db.Table(alertComm.NotifyTable).Where("tenant_id = ?", tenantId)
	if robotName != "" {
		sql = sql.Where("robot_name ilike ?",
			fmt.Sprintf("%%%s%%", dbutil.EscapeForLike(robotName)))
	}
	if robotPlat != "" {
		sql = sql.Where("plat_name = ?", robotPlat)
	}
	err = sql.Count(&count).Error
	if err != nil {
		return nil, err
	}
	err = sql.Limit(limit).Offset(offset).Order("id").Find(&robots).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	res := make(map[string]interface{}, 0)
	res["data"] = robots
	res["count"] = count
	return res, nil
}

func (n *notifyRepository) GetRobots(ctx context.Context, tenantId uint64) ([]model.NotifySetting, error) {
	var robots []model.NotifySetting
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return robots, err
	}
	conn := db.Table(alertComm.NotifyTable).Where("tenant_id = ?", tenantId)
	err = conn.Find(&robots).Error
	if err != nil {
		return nil, err
	}
	return robots, nil
}

func (n *notifyRepository) GetRobotByPlat(ctx context.Context, tenantId uint64, plat string) ([]model.NotifySetting, error) {
	var robots []model.NotifySetting
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return robots, err
	}
	conn := db.Table(alertComm.NotifyTable).Where("tenant_id = ?", tenantId)
	if plat != "" {
		conn = conn.Where("plat_name = ?", plat)
	}
	err = conn.Find(&robots).Error
	if err != nil {
		return nil, err
	}
	return robots, nil
}

func NewNotifyRepository() NotifyRepository {
	return &notifyRepository{}
}

type NotifyRepository interface {
	GetRobot(ctx context.Context, tenantId, robotId uint64) (*model.NotifySetting, error)
	QueryAllRobot(ctx context.Context, tenantId uint64, robotName, robotPlat string, limit, offset int) (map[string]interface{}, error)
	AddRobot(ctx context.Context, tenantId uint64, setting *model.NotifySetting) error
	DelRobot(ctx context.Context, tenantId uint64, robotId []uint64) error
	UpdateRobot(ctx context.Context, tenantId, robotId uint64, robot *model.NotifySetting) error
	GetRobots(ctx context.Context, tenantId uint64) ([]model.NotifySetting, error)
	GetRobotByPlat(ctx context.Context, tenantId uint64, plat string) ([]model.NotifySetting, error)
}
