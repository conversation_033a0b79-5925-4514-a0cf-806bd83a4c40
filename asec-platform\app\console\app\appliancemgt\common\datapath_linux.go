package common

import "path/filepath"

var BaseDir = "/data/asec"
var AgentDir = filepath.Join(BaseDir, "agents")
var GatewayDir = "/data/asec/gateway"  
var AgentWinFileName = "ASec_Client_Setup_Win32.exe"
var AgentWinFileDownloadTemp = "ASec_Client_Setup_%s[%s].exe"
var AgentDarwinFileName = "ASec_Client_Installer.pkg"
var AgentDarwinFileDownloadTemp = "ASec_Client_Installer_%s[%s].pkg"
var AgentAndroidFileName = "ASec_Client.apk"
var AgentAndroidFileDownloadTemp = "ASec_Client_%s[%s].apk"
var AuthDir = "/data/auth"
var FreeOptFileName = "FreeOTP.apk"
var GoogleAuthFileName = "Google_Authenticator.apk"
var ConfigPath = "/etc/asec/config.ini"