package handler

import (
	"net/http"
	"strconv"

	"asdsec.com/asec/platform/app/sys-panel/dto"
	"asdsec.com/asec/platform/app/sys-panel/service"
	"github.com/gin-gonic/gin"
)

type WechatVerifyHandler struct {
	service *service.WechatVerifyService
}

func NewWechatVerifyHandler(service *service.WechatVerifyService) *WechatVerifyHandler {
	return &WechatVerifyHandler{
		service: service,
	}
}

// GetWechatVerifyList 获取企业微信验证文件列表
// @Summary 获取企业微信验证文件列表
// @Description 获取所有企业微信域名验证文件，支持X-API-KEY头部认证
// @Tags 企业微信验证
// @Accept json
// @Produce json
// @Success 200 {object} dto.WechatVerifyListResponse
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /system/v1/wechat-verify [get]
func (h *WechatVerifyHandler) GetWechatVerifyList(c *gin.Context) {
	// 检查是否有X-API-KEY头部，如果有则验证API Key
	apiKey := c.GetHeader("X-API-KEY")
	if apiKey != "" {
		// 固定的API密钥 asec-web-gateway HmacSHA1 infogo
		const validAPIKey = "c34ff517ed828d279f91c884caac8f1be1efb999"
		if apiKey != validAPIKey {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "无效的API Key",
			})
			return
		}
		// API Key验证通过，跳过JWT验证，直接处理请求
	}
	// 如果没有API Key，则走正常的JWT验证流程（由中间件处理）

	result, err := h.service.GetWechatVerifyList()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": result,
	})
}

// CreateWechatVerify 创建企业微信验证文件
// @Summary 创建企业微信验证文件
// @Description 创建新的企业微信域名验证文件
// @Tags 企业微信验证
// @Accept json
// @Produce json
// @Param request body dto.CreateWechatVerifyRequest true "创建请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /system/v1/wechat-verify [post]
func (h *WechatVerifyHandler) CreateWechatVerify(c *gin.Context) {
	var req dto.CreateWechatVerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := h.service.CreateWechatVerify(&req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
	})
}

// UpdateWechatVerify 更新企业微信验证文件
// @Summary 更新企业微信验证文件
// @Description 更新指定的企业微信域名验证文件
// @Tags 企业微信验证
// @Accept json
// @Produce json
// @Param id path int true "验证文件ID"
// @Param request body dto.UpdateWechatVerifyRequest true "更新请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /system/v1/wechat-verify/{id} [put]
func (h *WechatVerifyHandler) UpdateWechatVerify(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
		})
		return
	}

	var req dto.UpdateWechatVerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := h.service.UpdateWechatVerify(uint(id), &req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// DeleteWechatVerify 删除企业微信验证文件
// @Summary 删除企业微信验证文件
// @Description 删除指定的企业微信域名验证文件
// @Tags 企业微信验证
// @Accept json
// @Produce json
// @Param id path int true "验证文件ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /system/v1/wechat-verify/{id} [delete]
func (h *WechatVerifyHandler) DeleteWechatVerify(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
		})
		return
	}

	if err := h.service.DeleteWechatVerify(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetWechatVerify 获取单个企业微信验证文件
// @Summary 获取企业微信验证文件详情
// @Description 获取指定的企业微信域名验证文件详情
// @Tags 企业微信验证
// @Accept json
// @Produce json
// @Param id path int true "验证文件ID"
// @Success 200 {object} dto.WechatVerifyResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /system/v1/wechat-verify/{id} [get]
func (h *WechatVerifyHandler) GetWechatVerify(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
		})
		return
	}

	result, err := h.service.GetWechatVerify(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": result,
	})
}
