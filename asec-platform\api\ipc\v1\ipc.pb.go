// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: ipc/v1/ipc.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpgradeMessage_UpMsgType int32

const (
	UpgradeMessage_UT_CHECK_UPGRADE          UpgradeMessage_UpMsgType = 0
	UpgradeMessage_UT_START_DOWNLOAD_PACKAGE UpgradeMessage_UpMsgType = 1
	UpgradeMessage_UT_DOWNLOAD_PROGRESS      UpgradeMessage_UpMsgType = 2
	UpgradeMessage_UT_DOWNLOAD_FINISHED      UpgradeMessage_UpMsgType = 3
	UpgradeMessage_UT_START_TIMING_CHECK     UpgradeMessage_UpMsgType = 4
)

// Enum value maps for UpgradeMessage_UpMsgType.
var (
	UpgradeMessage_UpMsgType_name = map[int32]string{
		0: "UT_CHECK_UPGRADE",
		1: "UT_START_DOWNLOAD_PACKAGE",
		2: "UT_DOWNLOAD_PROGRESS",
		3: "UT_DOWNLOAD_FINISHED",
		4: "UT_START_TIMING_CHECK",
	}
	UpgradeMessage_UpMsgType_value = map[string]int32{
		"UT_CHECK_UPGRADE":          0,
		"UT_START_DOWNLOAD_PACKAGE": 1,
		"UT_DOWNLOAD_PROGRESS":      2,
		"UT_DOWNLOAD_FINISHED":      3,
		"UT_START_TIMING_CHECK":     4,
	}
)

func (x UpgradeMessage_UpMsgType) Enum() *UpgradeMessage_UpMsgType {
	p := new(UpgradeMessage_UpMsgType)
	*p = x
	return p
}

func (x UpgradeMessage_UpMsgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpgradeMessage_UpMsgType) Descriptor() protoreflect.EnumDescriptor {
	return file_ipc_v1_ipc_proto_enumTypes[0].Descriptor()
}

func (UpgradeMessage_UpMsgType) Type() protoreflect.EnumType {
	return &file_ipc_v1_ipc_proto_enumTypes[0]
}

func (x UpgradeMessage_UpMsgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpgradeMessage_UpMsgType.Descriptor instead.
func (UpgradeMessage_UpMsgType) EnumDescriptor() ([]byte, []int) {
	return file_ipc_v1_ipc_proto_rawDescGZIP(), []int{0, 0}
}

type HostMessage_HostType int32

const (
	HostMessage_HT_UNKNOWN          HostMessage_HostType = 0
	HostMessage_HT_CLIENTANDTUNNEL  HostMessage_HostType = 1001
	HostMessage_HT_CLIENTANDSIDECAR HostMessage_HostType = 2001
	HostMessage_HT_CLIENTANDUPGRADE HostMessage_HostType = 3001
	HostMessage_HT_CLIENTANDUI      HostMessage_HostType = 4001
	HostMessage_HT_CLIENT_DDR       HostMessage_HostType = 5001
	HostMessage_HT_CLIENT_SETUP     HostMessage_HostType = 6001
)

// Enum value maps for HostMessage_HostType.
var (
	HostMessage_HostType_name = map[int32]string{
		0:    "HT_UNKNOWN",
		1001: "HT_CLIENTANDTUNNEL",
		2001: "HT_CLIENTANDSIDECAR",
		3001: "HT_CLIENTANDUPGRADE",
		4001: "HT_CLIENTANDUI",
		5001: "HT_CLIENT_DDR",
		6001: "HT_CLIENT_SETUP",
	}
	HostMessage_HostType_value = map[string]int32{
		"HT_UNKNOWN":          0,
		"HT_CLIENTANDTUNNEL":  1001,
		"HT_CLIENTANDSIDECAR": 2001,
		"HT_CLIENTANDUPGRADE": 3001,
		"HT_CLIENTANDUI":      4001,
		"HT_CLIENT_DDR":       5001,
		"HT_CLIENT_SETUP":     6001,
	}
)

func (x HostMessage_HostType) Enum() *HostMessage_HostType {
	p := new(HostMessage_HostType)
	*p = x
	return p
}

func (x HostMessage_HostType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostMessage_HostType) Descriptor() protoreflect.EnumDescriptor {
	return file_ipc_v1_ipc_proto_enumTypes[1].Descriptor()
}

func (HostMessage_HostType) Type() protoreflect.EnumType {
	return &file_ipc_v1_ipc_proto_enumTypes[1]
}

func (x HostMessage_HostType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostMessage_HostType.Descriptor instead.
func (HostMessage_HostType) EnumDescriptor() ([]byte, []int) {
	return file_ipc_v1_ipc_proto_rawDescGZIP(), []int{1, 0}
}

type HostMessage_Actions int32

const (
	HostMessage_ACT_NO_ACT           HostMessage_Actions = 0
	HostMessage_ACT_DATA_READY       HostMessage_Actions = 1
	HostMessage_ACT_DATA_FINISHED    HostMessage_Actions = 2
	HostMessage_ACT_SHUTDOWN_TUN     HostMessage_Actions = 3
	HostMessage_ACT_SHUTDOWN_SIDECAR HostMessage_Actions = 4
	HostMessage_ACT_START_SIDECAR    HostMessage_Actions = 5
	HostMessage_ACT_START_TUN        HostMessage_Actions = 6
	HostMessage_ACT_SHUTDOWN_UI      HostMessage_Actions = 7
	HostMessage_ACT_PLATADDR_CHANGE  HostMessage_Actions = 8
	// ddr act          20-40
	HostMessage_ACT_SHUTDOWN_DDR HostMessage_Actions = 20
	HostMessage_ACT_START_DDR    HostMessage_Actions = 21
	// Service服务
	HostMessage_ACT_SET_SERVICE_CAN_STOP     HostMessage_Actions = 30
	HostMessage_ACT_SET_SERVICE_CAN_NOT_STOP HostMessage_Actions = 31
)

// Enum value maps for HostMessage_Actions.
var (
	HostMessage_Actions_name = map[int32]string{
		0:  "ACT_NO_ACT",
		1:  "ACT_DATA_READY",
		2:  "ACT_DATA_FINISHED",
		3:  "ACT_SHUTDOWN_TUN",
		4:  "ACT_SHUTDOWN_SIDECAR",
		5:  "ACT_START_SIDECAR",
		6:  "ACT_START_TUN",
		7:  "ACT_SHUTDOWN_UI",
		8:  "ACT_PLATADDR_CHANGE",
		20: "ACT_SHUTDOWN_DDR",
		21: "ACT_START_DDR",
		30: "ACT_SET_SERVICE_CAN_STOP",
		31: "ACT_SET_SERVICE_CAN_NOT_STOP",
	}
	HostMessage_Actions_value = map[string]int32{
		"ACT_NO_ACT":                   0,
		"ACT_DATA_READY":               1,
		"ACT_DATA_FINISHED":            2,
		"ACT_SHUTDOWN_TUN":             3,
		"ACT_SHUTDOWN_SIDECAR":         4,
		"ACT_START_SIDECAR":            5,
		"ACT_START_TUN":                6,
		"ACT_SHUTDOWN_UI":              7,
		"ACT_PLATADDR_CHANGE":          8,
		"ACT_SHUTDOWN_DDR":             20,
		"ACT_START_DDR":                21,
		"ACT_SET_SERVICE_CAN_STOP":     30,
		"ACT_SET_SERVICE_CAN_NOT_STOP": 31,
	}
)

func (x HostMessage_Actions) Enum() *HostMessage_Actions {
	p := new(HostMessage_Actions)
	*p = x
	return p
}

func (x HostMessage_Actions) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostMessage_Actions) Descriptor() protoreflect.EnumDescriptor {
	return file_ipc_v1_ipc_proto_enumTypes[2].Descriptor()
}

func (HostMessage_Actions) Type() protoreflect.EnumType {
	return &file_ipc_v1_ipc_proto_enumTypes[2]
}

func (x HostMessage_Actions) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostMessage_Actions.Descriptor instead.
func (HostMessage_Actions) EnumDescriptor() ([]byte, []int) {
	return file_ipc_v1_ipc_proto_rawDescGZIP(), []int{1, 1}
}

type HostMessage_State int32

const (
	HostMessage_UNKNOWN_STATE HostMessage_State = 0
	// UI消息
	HostMessage_UI_ONLINE  HostMessage_State = 1
	HostMessage_UI_OFFLINE HostMessage_State = 2
	// TUN消息
	HostMessage_TUN_ONLINE     HostMessage_State = 100
	HostMessage_TUN_CONNECTING HostMessage_State = 101
	HostMessage_TUN_OFFLINE    HostMessage_State = 102
	// SIDECAR消息
	HostMessage_SIDECAR_ONLINE  HostMessage_State = 200
	HostMessage_SIDECAR_OFFLINE HostMessage_State = 201
	// DDR消息
	HostMessage_DDR_ONLINE  HostMessage_State = 300
	HostMessage_DDR_OFFLINE HostMessage_State = 301
	// SETUP消息
	HostMessage_SETUP_ONLINE  HostMessage_State = 400
	HostMessage_SETUP_OFFLINE HostMessage_State = 401
)

// Enum value maps for HostMessage_State.
var (
	HostMessage_State_name = map[int32]string{
		0:   "UNKNOWN_STATE",
		1:   "UI_ONLINE",
		2:   "UI_OFFLINE",
		100: "TUN_ONLINE",
		101: "TUN_CONNECTING",
		102: "TUN_OFFLINE",
		200: "SIDECAR_ONLINE",
		201: "SIDECAR_OFFLINE",
		300: "DDR_ONLINE",
		301: "DDR_OFFLINE",
		400: "SETUP_ONLINE",
		401: "SETUP_OFFLINE",
	}
	HostMessage_State_value = map[string]int32{
		"UNKNOWN_STATE":   0,
		"UI_ONLINE":       1,
		"UI_OFFLINE":      2,
		"TUN_ONLINE":      100,
		"TUN_CONNECTING":  101,
		"TUN_OFFLINE":     102,
		"SIDECAR_ONLINE":  200,
		"SIDECAR_OFFLINE": 201,
		"DDR_ONLINE":      300,
		"DDR_OFFLINE":     301,
		"SETUP_ONLINE":    400,
		"SETUP_OFFLINE":   401,
	}
)

func (x HostMessage_State) Enum() *HostMessage_State {
	p := new(HostMessage_State)
	*p = x
	return p
}

func (x HostMessage_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostMessage_State) Descriptor() protoreflect.EnumDescriptor {
	return file_ipc_v1_ipc_proto_enumTypes[3].Descriptor()
}

func (HostMessage_State) Type() protoreflect.EnumType {
	return &file_ipc_v1_ipc_proto_enumTypes[3]
}

func (x HostMessage_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostMessage_State.Descriptor instead.
func (HostMessage_State) EnumDescriptor() ([]byte, []int) {
	return file_ipc_v1_ipc_proto_rawDescGZIP(), []int{1, 2}
}

// 发送消息的原因
type HostMessage_Reason int32

const (
	HostMessage_DEFAULT_REASON          HostMessage_Reason = 0
	HostMessage_CREATE_TUN_FAILED       HostMessage_Reason = 1
	HostMessage_CHK_TUN_MGRPORT_FAILED  HostMessage_Reason = 2
	HostMessage_GET_TUN_ROUTE_FAILED    HostMessage_Reason = 3
	HostMessage_CHK_TUN_DATAPORT_FAILED HostMessage_Reason = 4
	HostMessage_UNKNOW_REASON           HostMessage_Reason = 65535
)

// Enum value maps for HostMessage_Reason.
var (
	HostMessage_Reason_name = map[int32]string{
		0:     "DEFAULT_REASON",
		1:     "CREATE_TUN_FAILED",
		2:     "CHK_TUN_MGRPORT_FAILED",
		3:     "GET_TUN_ROUTE_FAILED",
		4:     "CHK_TUN_DATAPORT_FAILED",
		65535: "UNKNOW_REASON",
	}
	HostMessage_Reason_value = map[string]int32{
		"DEFAULT_REASON":          0,
		"CREATE_TUN_FAILED":       1,
		"CHK_TUN_MGRPORT_FAILED":  2,
		"GET_TUN_ROUTE_FAILED":    3,
		"CHK_TUN_DATAPORT_FAILED": 4,
		"UNKNOW_REASON":           65535,
	}
)

func (x HostMessage_Reason) Enum() *HostMessage_Reason {
	p := new(HostMessage_Reason)
	*p = x
	return p
}

func (x HostMessage_Reason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostMessage_Reason) Descriptor() protoreflect.EnumDescriptor {
	return file_ipc_v1_ipc_proto_enumTypes[4].Descriptor()
}

func (HostMessage_Reason) Type() protoreflect.EnumType {
	return &file_ipc_v1_ipc_proto_enumTypes[4]
}

func (x HostMessage_Reason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostMessage_Reason.Descriptor instead.
func (HostMessage_Reason) EnumDescriptor() ([]byte, []int) {
	return file_ipc_v1_ipc_proto_rawDescGZIP(), []int{1, 3}
}

type UpgradeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientVersion      string                   `protobuf:"bytes,1,opt,name=clientVersion,proto3" json:"clientVersion,omitempty"`
	SidecarVersion     string                   `protobuf:"bytes,2,opt,name=sidecarVersion,proto3" json:"sidecarVersion,omitempty"`
	TuncliVersion      string                   `protobuf:"bytes,3,opt,name=tuncliVersion,proto3" json:"tuncliVersion,omitempty"`
	UpgradeVersion     string                   `protobuf:"bytes,4,opt,name=upgradeVersion,proto3" json:"upgradeVersion,omitempty"`
	UpgradeVersionDes  string                   `protobuf:"bytes,5,opt,name=upgradeVersionDes,proto3" json:"upgradeVersionDes,omitempty"`
	UpgradePackagePath string                   `protobuf:"bytes,6,opt,name=upgradePackagePath,proto3" json:"upgradePackagePath,omitempty"`
	DownloadProgress   int32                    `protobuf:"varint,7,opt,name=downloadProgress,proto3" json:"downloadProgress,omitempty"`
	ForceUpgrade       bool                     `protobuf:"varint,8,opt,name=forceUpgrade,proto3" json:"forceUpgrade,omitempty"`
	UpMsgType          UpgradeMessage_UpMsgType `protobuf:"varint,9,opt,name=upMsgType,proto3,enum=api.ipc.UpgradeMessage_UpMsgType" json:"upMsgType,omitempty"`
}

func (x *UpgradeMessage) Reset() {
	*x = UpgradeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ipc_v1_ipc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeMessage) ProtoMessage() {}

func (x *UpgradeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ipc_v1_ipc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeMessage.ProtoReflect.Descriptor instead.
func (*UpgradeMessage) Descriptor() ([]byte, []int) {
	return file_ipc_v1_ipc_proto_rawDescGZIP(), []int{0}
}

func (x *UpgradeMessage) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *UpgradeMessage) GetSidecarVersion() string {
	if x != nil {
		return x.SidecarVersion
	}
	return ""
}

func (x *UpgradeMessage) GetTuncliVersion() string {
	if x != nil {
		return x.TuncliVersion
	}
	return ""
}

func (x *UpgradeMessage) GetUpgradeVersion() string {
	if x != nil {
		return x.UpgradeVersion
	}
	return ""
}

func (x *UpgradeMessage) GetUpgradeVersionDes() string {
	if x != nil {
		return x.UpgradeVersionDes
	}
	return ""
}

func (x *UpgradeMessage) GetUpgradePackagePath() string {
	if x != nil {
		return x.UpgradePackagePath
	}
	return ""
}

func (x *UpgradeMessage) GetDownloadProgress() int32 {
	if x != nil {
		return x.DownloadProgress
	}
	return 0
}

func (x *UpgradeMessage) GetForceUpgrade() bool {
	if x != nil {
		return x.ForceUpgrade
	}
	return false
}

func (x *UpgradeMessage) GetUpMsgType() UpgradeMessage_UpMsgType {
	if x != nil {
		return x.UpMsgType
	}
	return UpgradeMessage_UT_CHECK_UPGRADE
}

type HostMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostType     HostMessage_HostType `protobuf:"varint,1,opt,name=hostType,proto3,enum=api.ipc.HostMessage_HostType" json:"hostType,omitempty"`
	ProcessState HostMessage_State    `protobuf:"varint,2,opt,name=processState,proto3,enum=api.ipc.HostMessage_State" json:"processState,omitempty"`
	Action       HostMessage_Actions  `protobuf:"varint,3,opt,name=action,proto3,enum=api.ipc.HostMessage_Actions" json:"action,omitempty"`
	UpMsg        *UpgradeMessage      `protobuf:"bytes,4,opt,name=upMsg,proto3" json:"upMsg,omitempty"`
	Reason       HostMessage_Reason   `protobuf:"varint,5,opt,name=reason,proto3,enum=api.ipc.HostMessage_Reason" json:"reason,omitempty"`
}

func (x *HostMessage) Reset() {
	*x = HostMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ipc_v1_ipc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostMessage) ProtoMessage() {}

func (x *HostMessage) ProtoReflect() protoreflect.Message {
	mi := &file_ipc_v1_ipc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostMessage.ProtoReflect.Descriptor instead.
func (*HostMessage) Descriptor() ([]byte, []int) {
	return file_ipc_v1_ipc_proto_rawDescGZIP(), []int{1}
}

func (x *HostMessage) GetHostType() HostMessage_HostType {
	if x != nil {
		return x.HostType
	}
	return HostMessage_HT_UNKNOWN
}

func (x *HostMessage) GetProcessState() HostMessage_State {
	if x != nil {
		return x.ProcessState
	}
	return HostMessage_UNKNOWN_STATE
}

func (x *HostMessage) GetAction() HostMessage_Actions {
	if x != nil {
		return x.Action
	}
	return HostMessage_ACT_NO_ACT
}

func (x *HostMessage) GetUpMsg() *UpgradeMessage {
	if x != nil {
		return x.UpMsg
	}
	return nil
}

func (x *HostMessage) GetReason() HostMessage_Reason {
	if x != nil {
		return x.Reason
	}
	return HostMessage_DEFAULT_REASON
}

var File_ipc_v1_ipc_proto protoreflect.FileDescriptor

var file_ipc_v1_ipc_proto_rawDesc = []byte{
	0x0a, 0x10, 0x69, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x07, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x70, 0x63, 0x22, 0xad, 0x04, 0x0a, 0x0e,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x69, 0x64, 0x65, 0x63, 0x61, 0x72, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x69,
	0x64, 0x65, 0x63, 0x61, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d,
	0x74, 0x75, 0x6e, 0x63, 0x6c, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x75, 0x6e, 0x63, 0x6c, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x75, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x75, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x75, 0x70, 0x4d, 0x73,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x55, 0x70, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x75, 0x70, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x09, 0x55, 0x70,
	0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x54, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x55, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14,
	0x55, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x54, 0x5f, 0x44, 0x4f, 0x57,
	0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x03,
	0x12, 0x19, 0x0a, 0x15, 0x55, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x49, 0x4d,
	0x49, 0x4e, 0x47, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x04, 0x22, 0x87, 0x09, 0x0a, 0x0b,
	0x48, 0x6f, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x68,
	0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x70, 0x63, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x68, 0x6f,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x70, 0x63, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x70, 0x63,
	0x2e, 0x48, 0x6f, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x05,
	0x75, 0x70, 0x4d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x75, 0x70, 0x4d, 0x73, 0x67, 0x12, 0x33, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x70, 0x63, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0xa6, 0x01, 0x0a, 0x08, 0x48, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a,
	0x0a, 0x48, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x12, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x41, 0x4e, 0x44, 0x54, 0x55, 0x4e,
	0x4e, 0x45, 0x4c, 0x10, 0xe9, 0x07, 0x12, 0x18, 0x0a, 0x13, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x41, 0x4e, 0x44, 0x53, 0x49, 0x44, 0x45, 0x43, 0x41, 0x52, 0x10, 0xd1, 0x0f,
	0x12, 0x18, 0x0a, 0x13, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x41, 0x4e, 0x44,
	0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0xb9, 0x17, 0x12, 0x13, 0x0a, 0x0e, 0x48, 0x54,
	0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x41, 0x4e, 0x44, 0x55, 0x49, 0x10, 0xa1, 0x1f, 0x12,
	0x12, 0x0a, 0x0d, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x44, 0x52,
	0x10, 0x89, 0x27, 0x12, 0x14, 0x0a, 0x0f, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x10, 0xf1, 0x2e, 0x22, 0xb5, 0x02, 0x0a, 0x07, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x43, 0x54, 0x5f, 0x4e, 0x4f, 0x5f,
	0x41, 0x43, 0x54, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x43, 0x54, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x54,
	0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x48, 0x55, 0x54, 0x44, 0x4f, 0x57, 0x4e,
	0x5f, 0x54, 0x55, 0x4e, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x48,
	0x55, 0x54, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x53, 0x49, 0x44, 0x45, 0x43, 0x41, 0x52, 0x10, 0x04,
	0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x53, 0x49,
	0x44, 0x45, 0x43, 0x41, 0x52, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x43, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x55, 0x4e, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x43,
	0x54, 0x5f, 0x53, 0x48, 0x55, 0x54, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x55, 0x49, 0x10, 0x07, 0x12,
	0x17, 0x0a, 0x13, 0x41, 0x43, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x41, 0x44, 0x44, 0x52, 0x5f,
	0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x08, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x54, 0x5f,
	0x53, 0x48, 0x55, 0x54, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x44, 0x44, 0x52, 0x10, 0x14, 0x12, 0x11,
	0x0a, 0x0d, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x44, 0x52, 0x10,
	0x15, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x10, 0x1e, 0x12,
	0x20, 0x0a, 0x1c, 0x41, 0x43, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x10,
	0x1f, 0x22, 0xe3, 0x01, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x55, 0x49, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x55, 0x49, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x02, 0x12, 0x0e, 0x0a,
	0x0a, 0x54, 0x55, 0x4e, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x64, 0x12, 0x12, 0x0a,
	0x0e, 0x54, 0x55, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x55, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0x66, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x49, 0x44, 0x45, 0x43, 0x41, 0x52, 0x5f, 0x4f, 0x4e,
	0x4c, 0x49, 0x4e, 0x45, 0x10, 0xc8, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x53, 0x49, 0x44, 0x45, 0x43,
	0x41, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0xc9, 0x01, 0x12, 0x0f, 0x0a,
	0x0a, 0x44, 0x44, 0x52, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0xac, 0x02, 0x12, 0x10,
	0x0a, 0x0b, 0x44, 0x44, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0xad, 0x02,
	0x12, 0x11, 0x0a, 0x0c, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0x90, 0x03, 0x12, 0x12, 0x0a, 0x0d, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x4f, 0x46, 0x46,
	0x4c, 0x49, 0x4e, 0x45, 0x10, 0x91, 0x03, 0x22, 0x9b, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x5f, 0x54, 0x55, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a,
	0x16, 0x43, 0x48, 0x4b, 0x5f, 0x54, 0x55, 0x4e, 0x5f, 0x4d, 0x47, 0x52, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x47, 0x45, 0x54,
	0x5f, 0x54, 0x55, 0x4e, 0x5f, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x48, 0x4b, 0x5f, 0x54, 0x55, 0x4e, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x13, 0x0a, 0x0d, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x10, 0xff, 0xff, 0x03, 0x42, 0x28, 0x5a, 0x26, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ipc_v1_ipc_proto_rawDescOnce sync.Once
	file_ipc_v1_ipc_proto_rawDescData = file_ipc_v1_ipc_proto_rawDesc
)

func file_ipc_v1_ipc_proto_rawDescGZIP() []byte {
	file_ipc_v1_ipc_proto_rawDescOnce.Do(func() {
		file_ipc_v1_ipc_proto_rawDescData = protoimpl.X.CompressGZIP(file_ipc_v1_ipc_proto_rawDescData)
	})
	return file_ipc_v1_ipc_proto_rawDescData
}

var file_ipc_v1_ipc_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_ipc_v1_ipc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_ipc_v1_ipc_proto_goTypes = []interface{}{
	(UpgradeMessage_UpMsgType)(0), // 0: api.ipc.UpgradeMessage.UpMsgType
	(HostMessage_HostType)(0),     // 1: api.ipc.HostMessage.HostType
	(HostMessage_Actions)(0),      // 2: api.ipc.HostMessage.Actions
	(HostMessage_State)(0),        // 3: api.ipc.HostMessage.State
	(HostMessage_Reason)(0),       // 4: api.ipc.HostMessage.Reason
	(*UpgradeMessage)(nil),        // 5: api.ipc.UpgradeMessage
	(*HostMessage)(nil),           // 6: api.ipc.HostMessage
}
var file_ipc_v1_ipc_proto_depIdxs = []int32{
	0, // 0: api.ipc.UpgradeMessage.upMsgType:type_name -> api.ipc.UpgradeMessage.UpMsgType
	1, // 1: api.ipc.HostMessage.hostType:type_name -> api.ipc.HostMessage.HostType
	3, // 2: api.ipc.HostMessage.processState:type_name -> api.ipc.HostMessage.State
	2, // 3: api.ipc.HostMessage.action:type_name -> api.ipc.HostMessage.Actions
	5, // 4: api.ipc.HostMessage.upMsg:type_name -> api.ipc.UpgradeMessage
	4, // 5: api.ipc.HostMessage.reason:type_name -> api.ipc.HostMessage.Reason
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_ipc_v1_ipc_proto_init() }
func file_ipc_v1_ipc_proto_init() {
	if File_ipc_v1_ipc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ipc_v1_ipc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ipc_v1_ipc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ipc_v1_ipc_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ipc_v1_ipc_proto_goTypes,
		DependencyIndexes: file_ipc_v1_ipc_proto_depIdxs,
		EnumInfos:         file_ipc_v1_ipc_proto_enumTypes,
		MessageInfos:      file_ipc_v1_ipc_proto_msgTypes,
	}.Build()
	File_ipc_v1_ipc_proto = out.File
	file_ipc_v1_ipc_proto_rawDesc = nil
	file_ipc_v1_ipc_proto_goTypes = nil
	file_ipc_v1_ipc_proto_depIdxs = nil
}
