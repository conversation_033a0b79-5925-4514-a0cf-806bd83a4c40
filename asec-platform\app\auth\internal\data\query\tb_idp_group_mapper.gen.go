// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbIdpGroupMapper(db *gorm.DB, opts ...gen.DOOption) tbIdpGroupMapper {
	_tbIdpGroupMapper := tbIdpGroupMapper{}

	_tbIdpGroupMapper.tbIdpGroupMapperDo.UseDB(db, opts...)
	_tbIdpGroupMapper.tbIdpGroupMapperDo.UseModel(&model.TbIdpGroupMapper{})

	tableName := _tbIdpGroupMapper.tbIdpGroupMapperDo.TableName()
	_tbIdpGroupMapper.ALL = field.NewAsterisk(tableName)
	_tbIdpGroupMapper.ProviderID = field.NewString(tableName, "provider_id")
	_tbIdpGroupMapper.GroupID = field.NewString(tableName, "group_id")
	_tbIdpGroupMapper.CorpID = field.NewString(tableName, "corp_id")
	_tbIdpGroupMapper.CreatedAt = field.NewTime(tableName, "created_at")
	_tbIdpGroupMapper.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbIdpGroupMapper.fillFieldMap()

	return _tbIdpGroupMapper
}

type tbIdpGroupMapper struct {
	tbIdpGroupMapperDo tbIdpGroupMapperDo

	ALL        field.Asterisk
	ProviderID field.String // 认证服务器 id
	GroupID    field.String // 顶层目录id
	CorpID     field.String
	CreatedAt  field.Time
	UpdatedAt  field.Time

	fieldMap map[string]field.Expr
}

func (t tbIdpGroupMapper) Table(newTableName string) *tbIdpGroupMapper {
	t.tbIdpGroupMapperDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbIdpGroupMapper) As(alias string) *tbIdpGroupMapper {
	t.tbIdpGroupMapperDo.DO = *(t.tbIdpGroupMapperDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbIdpGroupMapper) updateTableName(table string) *tbIdpGroupMapper {
	t.ALL = field.NewAsterisk(table)
	t.ProviderID = field.NewString(table, "provider_id")
	t.GroupID = field.NewString(table, "group_id")
	t.CorpID = field.NewString(table, "corp_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbIdpGroupMapper) WithContext(ctx context.Context) *tbIdpGroupMapperDo {
	return t.tbIdpGroupMapperDo.WithContext(ctx)
}

func (t tbIdpGroupMapper) TableName() string { return t.tbIdpGroupMapperDo.TableName() }

func (t tbIdpGroupMapper) Alias() string { return t.tbIdpGroupMapperDo.Alias() }

func (t *tbIdpGroupMapper) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbIdpGroupMapper) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["provider_id"] = t.ProviderID
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbIdpGroupMapper) clone(db *gorm.DB) tbIdpGroupMapper {
	t.tbIdpGroupMapperDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbIdpGroupMapper) replaceDB(db *gorm.DB) tbIdpGroupMapper {
	t.tbIdpGroupMapperDo.ReplaceDB(db)
	return t
}

type tbIdpGroupMapperDo struct{ gen.DO }

func (t tbIdpGroupMapperDo) Debug() *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Debug())
}

func (t tbIdpGroupMapperDo) WithContext(ctx context.Context) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbIdpGroupMapperDo) ReadDB() *tbIdpGroupMapperDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbIdpGroupMapperDo) WriteDB() *tbIdpGroupMapperDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbIdpGroupMapperDo) Session(config *gorm.Session) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbIdpGroupMapperDo) Clauses(conds ...clause.Expression) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbIdpGroupMapperDo) Returning(value interface{}, columns ...string) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbIdpGroupMapperDo) Not(conds ...gen.Condition) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbIdpGroupMapperDo) Or(conds ...gen.Condition) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbIdpGroupMapperDo) Select(conds ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbIdpGroupMapperDo) Where(conds ...gen.Condition) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbIdpGroupMapperDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbIdpGroupMapperDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbIdpGroupMapperDo) Order(conds ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbIdpGroupMapperDo) Distinct(cols ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbIdpGroupMapperDo) Omit(cols ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbIdpGroupMapperDo) Join(table schema.Tabler, on ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbIdpGroupMapperDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbIdpGroupMapperDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbIdpGroupMapperDo) Group(cols ...field.Expr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbIdpGroupMapperDo) Having(conds ...gen.Condition) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbIdpGroupMapperDo) Limit(limit int) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbIdpGroupMapperDo) Offset(offset int) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbIdpGroupMapperDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbIdpGroupMapperDo) Unscoped() *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbIdpGroupMapperDo) Create(values ...*model.TbIdpGroupMapper) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbIdpGroupMapperDo) CreateInBatches(values []*model.TbIdpGroupMapper, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbIdpGroupMapperDo) Save(values ...*model.TbIdpGroupMapper) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbIdpGroupMapperDo) First() (*model.TbIdpGroupMapper, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpGroupMapper), nil
	}
}

func (t tbIdpGroupMapperDo) Take() (*model.TbIdpGroupMapper, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpGroupMapper), nil
	}
}

func (t tbIdpGroupMapperDo) Last() (*model.TbIdpGroupMapper, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpGroupMapper), nil
	}
}

func (t tbIdpGroupMapperDo) Find() ([]*model.TbIdpGroupMapper, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbIdpGroupMapper), err
}

func (t tbIdpGroupMapperDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbIdpGroupMapper, err error) {
	buf := make([]*model.TbIdpGroupMapper, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbIdpGroupMapperDo) FindInBatches(result *[]*model.TbIdpGroupMapper, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbIdpGroupMapperDo) Attrs(attrs ...field.AssignExpr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbIdpGroupMapperDo) Assign(attrs ...field.AssignExpr) *tbIdpGroupMapperDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbIdpGroupMapperDo) Joins(fields ...field.RelationField) *tbIdpGroupMapperDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbIdpGroupMapperDo) Preload(fields ...field.RelationField) *tbIdpGroupMapperDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbIdpGroupMapperDo) FirstOrInit() (*model.TbIdpGroupMapper, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpGroupMapper), nil
	}
}

func (t tbIdpGroupMapperDo) FirstOrCreate() (*model.TbIdpGroupMapper, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpGroupMapper), nil
	}
}

func (t tbIdpGroupMapperDo) FindByPage(offset int, limit int) (result []*model.TbIdpGroupMapper, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbIdpGroupMapperDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbIdpGroupMapperDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbIdpGroupMapperDo) Delete(models ...*model.TbIdpGroupMapper) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbIdpGroupMapperDo) withDO(do gen.Dao) *tbIdpGroupMapperDo {
	t.DO = *do.(*gen.DO)
	return t
}
