#!/bin/bash
# etcd 启动脚本（带碎片整理功能）
# 负责启动 etcd 服务并配置定时碎片整理任务
set -e

echo "正在启动带碎片整理功能的 etcd 服务..."

if [ "$(id -u)" = "0" ]; then
    echo "当前以 root 用户运行，正在切换到 etcd 用户 (1001)..."
    echo "正在修复 etcd 数据目录权限..."
    mkdir -p /bitnami/etcd/data
    chown -R 1001:1001 /bitnami/etcd
    chmod -R 700 /bitnami/etcd/data
    exec gosu 1001 "$0" "$@"
fi

echo "当前运行用户: $(whoami) (用户ID: $(id -u))"

# 配置定时碎片整理任务
echo "正在设置碎片整理定时任务..."
echo "0 3 * * * /defrag.sh" | crontab -
echo "已设置每日凌晨 3 点执行碎片整理"

# 启动 cron 守护进程
echo "正在启动 cron 服务..."
cron &

# 启动 etcd 服务
echo "正在启动 etcd 服务..."
exec /opt/bitnami/scripts/etcd/entrypoint.sh /opt/bitnami/scripts/etcd/run.sh
