package model

import (
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
)

type GetAlertRuleListResp struct {
	Id                string         `gorm:"column:id" json:"id"`
	Name              string         `gorm:"column:name" json:"name"`
	Description       string         `gorm:"column:description" json:"description"`
	Enable            int            `gorm:"column:enable" json:"enable"`
	EnableAnalysis    int            `gorm:"column:enable_analysis" json:"enable_analysis"`
	BuiltIn           int            `gorm:"column:built_in" json:"built_in"`
	SeverityIds       pq.Int64Array  `gorm:"column:severity_ids;type:int" json:"severity_ids"`
	UserIds           pq.StringArray `gorm:"column:user_ids;type:string" json:"user_ids"`
	UserGroupIds      pq.StringArray `gorm:"column:user_group_ids;type:string" json:"user_group_ids"`
	ChannelTypes      pq.StringArray `gorm:"column:channel_types;type:string" json:"channel_types"`
	SensitiveIds      pq.StringArray `gorm:"column:sensitive_ids;type:string" json:"sensitive_ids"`
	SensitiveLevel    pq.Int64Array  `gorm:"column:sensitive_level;type:int" json:"sensitive_level"`
	SensitiveCategory pq.StringArray `gorm:"column:sensitive_category;type:string" json:"sensitive_category"`
	Time              pgtype.JSONB   `gorm:"column:time" json:"time"`

	Sensitive             pgtype.JSONBArray `gorm:"column:sensitive;type:[]jsonb" json:"sensitive"`
	UserNameList          pgtype.JSONBArray `gorm:"column:user_name_list;type:[]jsonb" json:"user_name_list"`
	GroupNameList         pgtype.JSONBArray `gorm:"column:group_name_list;type:[]jsonb" json:"group_name_list"`
	ChannelTypeNameList   pq.StringArray    `gorm:"column:channel_type_list;type:string" json:"channel_type_list"`
	SensitiveCategoryList pq.StringArray    `gorm:"column:sensitive_category_list;type:string" json:"sensitive_category_list"`

	AlertType        int            `gorm:"column:alert_type;comment:告警类型(1代码/2数据)" json:"alert_type"`
	GitContainOption int            `gorm:"column:git_contain_option;comment:git包含逻辑(1包含/2不包含)" json:"git_contain_option"`
	SvnContainOption int            `gorm:"column:svn_contain_option;comment:svn包含逻辑(1包含/2不包含)" json:"svn_contain_option"`
	GitPath          pq.StringArray `gorm:"column:git_path;comment:git_path;type:string" json:"git_path"`
	SvnPath          pq.StringArray `gorm:"column:svn_path;comment:svn_path;type:string" json:"svn_path"`

	// 处置动作 1审批/2阻断/3放通
	DisposeAction int `gorm:"column:dispose_action" json:"dispose_action"`

	EnableNotification bool   `gorm:"column:enable_notification;" json:"enable_notification"`
	NotificationId     string `gorm:"column:notification_id;type:string" json:"notification_id"`
}
