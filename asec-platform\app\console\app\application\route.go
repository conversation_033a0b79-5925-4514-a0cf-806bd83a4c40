package application

import (
	"asdsec.com/asec/platform/app/console/app/application/api"
	"github.com/gin-gonic/gin"
)

func ApplicationApi(r *gin.RouterGroup) {
	v := r.Group("/v1/application")
	{
		v.POST("/list", api.GetApplicationList)
		v.POST("/userAppList", api.GetUserAppList)
		v.POST("", api.AddApplicationByType)
		v.POST("/import", api.ImportApp)
		v.POST("/export", api.ApplicationExport)
		v.POST("/template", api.ApplicationTemplate)

		v.GET("", api.GetApplication)
		v.DELETE("", api.DeleteApplication)
		v.PUT("", api.UpdateApplicationByType)
		v.POST("/group", api.AddApplicationGroup)
		v.GET("/group", api.GetAppGroup)
		v.DELETE("/group", api.DeleteGroup)
		v.PUT("/group", api.UpdateAppGroup)
		v.POST("/upload-icon", api.UploadIcon)
		v.PUT("/group/sort", api.UpdateGroupSort)
		v.POST("/group/move", api.MoveGroup)
		
		// 证书相关接口
		v.GET("/certificates", api.GetCertificateList)
	}
}
