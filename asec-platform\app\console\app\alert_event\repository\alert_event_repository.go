package repository

import (
	"asdsec.com/asec/platform/app/console/app/alert_event/model"
	comm "asdsec.com/asec/platform/app/console/app/appliancemgt/common"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	normErrors "errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/go-kratos/kratos/v2/log"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	Int2Max = 3
)

type AlertEventRepository interface {
	GetAlertEventTotal(ctx *gin.Context, req model.GetAlertEventReq) ([]model.GetAlertEventRsp, error)
	GetAlertEventDetail(ctx *gin.Context, id string) (model.GetAlertEventDetailResp, error)
	GetAlertEventList(ctx *gin.Context, req model.GetAlertEventListReq) (modelTable.Pagination, error)
	UpdateScoreConfig(ctx *gin.Context, req model.UpdateScoreConfig) error
}

// NewAppRepository 创建接口实现接口实现
func NewAppRepository() AlertEventRepository {
	return &alertEventRepository{}
}

type alertEventRepository struct {
}

type AlertEventArray []model.GetAlertEventRsp

func (arr AlertEventArray) Len() int {
	return len(arr)
}

func (arr AlertEventArray) Less(i, j int) bool {
	return arr[i].SeverityId > arr[j].SeverityId
}

func (arr AlertEventArray) Swap(i, j int) {
	arr[i], arr[j] = arr[j], arr[i]
}

func (a *alertEventRepository) UpdateScoreConfig(ctx *gin.Context, req model.UpdateScoreConfig) error {
	pgDb, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	id, err := strconv.Atoi(req.Id)
	if err != nil {
		log.Errorf("parse id failed. id=%s, err=%v", req.Id, err)
		return err
	}
	item := modelTable.RiskLevelConfig{Id: id, MinScore: req.MinScore, MaxScore: req.MaxScore}
	return pgDb.Model(modelTable.RiskLevelConfig{}).Where("id = ?", req.Id).Updates(&item).Error
}

func (a *alertEventRepository) GetAlertEventTotal(ctx *gin.Context, req model.GetAlertEventReq) ([]model.GetAlertEventRsp, error) {
	local, _ := time.LoadLocation("Local")
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, local)
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, local)

	db, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}

	var res AlertEventArray
	err = db.Model(&modelTable.AlertEvent{}).
		Select("severity_id,severity,count(uuid) as event_count").
		Where("occur_time >= ? and occur_time <= ?", startTime.Unix(), endTime.Unix()).
		Group("severity,severity_id").
		Order("severity_id desc").
		Find(&res).Error

	riskLevelMap := make(map[int]string)
	for _, v := range res {
		riskLevelMap[v.SeverityId] = v.Severity
	}

	riskLevelList, err := GetRiskLevelConfigList(ctx)

	severityMap := make(map[int]model.GetAlertEventRsp)
	for _, v := range riskLevelList {
		if _, ok := riskLevelMap[v.RiskLevel]; !ok {
			res = append(res, model.GetAlertEventRsp{SeverityId: v.RiskLevel, Severity: v.RiskLevelName})
		}
		severityMap[v.RiskLevel] = model.GetAlertEventRsp{Severity: v.RiskLevelName, MinScore: v.MinScore, MaxScore: v.MaxScore}
	}
	for key, v := range res {
		tmpModel := severityMap[v.SeverityId]
		res[key].Severity = tmpModel.Severity
		res[key].MinScore = tmpModel.MinScore
		res[key].MaxScore = tmpModel.MaxScore
	}
	sort.Sort(res)
	return res, err
}

func (a *alertEventRepository) GetAlertEventDetail(ctx *gin.Context, uuid string) (ret model.GetAlertEventDetailResp, err error) {
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return
	}
	err = db.Model(&modelTable.AlertEvent{}).
		Select("user_name,agent_name,policy_name,severity_id,severity,alert_type,toDateTime(occur_time, 'Asia/Shanghai') AS occur_time,file_name,file_type,file_size,score,"+
			"data_category,sensitive_level,sensitive_rule_name,md5,sha256,channel,channel_type,user_tags,extension_name,real_extension_name,"+
			"visitParamExtractInt(score_reason,'tag_score') as tag_score,visitParamExtractString(score_reason,'tag_name') as tag_name,"+
			"file_path,original_file_name,original_file_path,owner,name_match_info,content_match_info,file_category_id,activity,dispose_action,"+
			"visitParamExtractInt(score_reason,'channel_score') as channel_score,"+
			"visitParamExtractInt(score_reason,'sensitive_data_score') as sensitive_data_score,"+
			"visitParamExtractInt(score_reason,'hide_suffix_score') as hide_suffix_score,"+
			"visitParamExtractInt(score_reason,'file_compression_score') as file_compression_score,"+
			"visitParamExtractInt(score_reason,'rename_score') as rename_score,visitParamExtractInt(score_reason,'copy_score') as copy_score,"+
			"file_event_id,plat_type").
		Where("tb_ddr_alert.uuid = ?", uuid).Find(&ret).Error
	if err != nil {
		return model.GetAlertEventDetailResp{}, err
	}
	var fileEvent modelTable.FileEvents
	err = db.Model(&modelTable.FileEvents{}).
		Select("source_name,source_type,src_path,dst_path").
		Where("uuid = ?", ret.FileEventId).Find(&fileEvent).Error
	if err != nil {
		return model.GetAlertEventDetailResp{}, err
	}
	fileCategory, err := commonApi.GetFileTypeAndCode(ctx, strconv.FormatInt(int64(ret.FileCategoryId), 10))
	// 这里查询失败不返回空，这里存在识别不出来的文件类型
	if err != nil && !normErrors.Is(err, comm.ErrUnSupportFileType) {
		global.SysLog.Error(err.Error())
	}
	ret.FileCategory = fileCategory
	ret.SrcPath = fileEvent.SrcPath
	ret.SourceType = fileEvent.SourceType
	ret.SourceName = fileEvent.SourceName
	ret.DstPath = fileEvent.DstPath
	return ret, nil
}

func GetRiskLevelConfigList(ctx *gin.Context) ([]modelTable.RiskLevelConfig, error) {
	pgDB, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	var riskLevelList []modelTable.RiskLevelConfig
	err = pgDB.Model(&modelTable.RiskLevelConfig{}).Select("id,risk_level,risk_level_name,min_score,max_score").Order("risk_level desc").Find(&riskLevelList).Error
	if err != nil {
		return nil, err
	}
	return riskLevelList, err
}

func (a *alertEventRepository) GetAlertEventList(ctx *gin.Context, req model.GetAlertEventListReq) (modelTable.Pagination, error) {
	var res = req.Pagination
	if req.Search != "" {
		res.SearchColumns = []string{"user_name", "file_name"}
	}
	local, _ := time.LoadLocation("Local")
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, local)
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, local)
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return res, err
	}

	if endTime.Before(startTime) {
		global.SysLog.Error(err.Error())
		return res, err
	}
	db = db.Model(&modelTable.AlertEvent{}).
		Select("uuid,severity_id,severity,alert_type,policy_name,toDateTime(occur_time, 'Asia/Shanghai') AS occur_time,score,"+
			"alert_summary,user_name,file_name,file_type,file_size,real_extension_name,extension_name,channel,channel_type,activity,dispose_action,"+
			"visitParamExtractInt(score_reason,'tag_score') as tag_score,visitParamExtractString(score_reason,'tag_name') as tag_name,"+
			"visitParamExtractInt(score_reason,'channel_score') as channel_score,"+
			"visitParamExtractInt(score_reason,'sensitive_data_score') as sensitive_data_score,sensitive_rule_name,sensitive_level,"+
			"visitParamExtractInt(score_reason,'hide_suffix_score') as hide_suffix_score,"+
			"visitParamExtractInt(score_reason,'file_compression_score') as file_compression_score,"+
			"visitParamExtractInt(score_reason,'rename_score') as rename_score,visitParamExtractInt(score_reason,'copy_score') as copy_score").
		Where("occur_time >= ? AND occur_time <= ?", startTime.Unix(), endTime.Unix()).
		Order("occur_time desc")

	// optional condition
	if req.SeverityId != 0 {
		db = db.Where("severity_id = ?", req.SeverityId)
	}
	if len(req.Channel) != 0 {
		db = db.Where("channel in ? ", req.Channel)
	}
	if len(req.SensitiveIds) != 0 {
		db = db.Where("sensitive_rule_id in ? ", req.SensitiveIds)
	}
	if req.Action != "" {
		db = db.Where("dispose_action = ?", req.Action)
	}

	if req.Search != "" {
		searchTerm := fmt.Sprintf("%%%s%%", strings.ToLower(req.Search))
		for _, column := range res.SearchColumns {
			db = db.Where(fmt.Sprintf("LOWER(%s) LIKE ?", column), searchTerm)
		}
	}

	var rsp []model.GetAlertEventListResp
	return modelTable.Paginate(&rsp, &res, db)
}
