package biz

import (
	"asdsec.com/asec/platform/pkg/apisix"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	pb "asdsec.com/asec/platform/api/application/v1"
	sysMode "asdsec.com/asec/platform/app/console/app/system/model"
	virtualIPModel "asdsec.com/asec/platform/app/console/app/virtual_ip/model"
	"asdsec.com/asec/platform/pkg/biz/dto"
	"asdsec.com/asec/platform/pkg/utils"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/lib/pq"
)

type AppRepo interface {
	SeGetApp(ctx context.Context, applianceId uint64) ([]dto.AppInfoDto, error)
	SeStrategy(ctx context.Context, applianceId uint64) ([]dto.SeStrategy, error)
	UserIdExists(ctx context.Context, userID string) (bool, error)
	RemoveInvalidDedicatedUser(ctx context.Context, poolID int, userID string) error
	GetUserIdByGroups(ctx context.Context, userGroupIds pq.StringArray) ([]string, error)
	GetUserIdByRoles(ctx context.Context, roleIds pq.StringArray) ([]string, error)
	GetStrategyAppIds(ctx context.Context, appGroupIds pq.Int64Array) ([]int64, error)
	GetAllAppStrategy(ctx context.Context) ([]dto.SeStrategy, error)
	WebAppInfo(ctx context.Context, req *pb.WebAppInfoReq) ([]dto.WebAppInfoDto, error)
	WebAccessInfo(ctx context.Context, req *pb.WebAccessInfoReq) ([]dto.WebAccessInfoDto, error)
	GetWebStrategyAppIds(ctx context.Context, appGroupIds pq.Int64Array) ([]int64, error)
	WebGatewayRsApp(ctx context.Context, applianceId uint64) ([]dto.ApplicationGatewayDto, error)
	WebGatewayRsCrt(ctx context.Context) ([]dto.CrtGatewayDto, error)
	UciUserInfo(ctx context.Context) ([]dto.UciUserInfo, error)
	WebGatewayWatermark(ctx context.Context, req *pb.WebGatewayWatermarkReq) (dto.WebWatermarkDto, error)
	WebGatewayHosts(ctx context.Context, req *pb.WebGatewayHostsReq) ([]sysMode.Hosts, error)
	WebGatewayVirtualIPPools(ctx context.Context, applianceId uint64) ([]dto.VirtualIPPoolDto, error)
	SaveVirtualIPAllocations(ctx context.Context, applianceId string, allocations []dto.IPAllocationDto, poolStats map[string]dto.PoolStatsDto) error
	UpdateAppHealthStatus(ctx context.Context, req *pb.ReportHealthCheckResultReq) error
	SaveTrafficStats(ctx context.Context, applianceId string, trafficBuckets []dto.TrafficBucketDto) error
	GetVirtualIPGlobalSettings(ctx context.Context) (*dto.VirtualIPGlobalSettingsDto, error)
	ReleaseAllActiveVirtualIPs(ctx context.Context, applianceId string) error
	// 网关命令相关方法
	GetPendingGatewayCommands(ctx context.Context, applianceID int64) ([]virtualIPModel.GatewayCommand, error)
	UpdateGatewayCommandStatus(ctx context.Context, commandID string, status string, errorMessage string) error
	// SSO IDP配置相关方法
	WebGatewayRsSSOIDP(ctx context.Context, idpTypes []string) ([]dto.SSOIDPDto, error)
	GetPlatformDomainByApplianceID(ctx context.Context, applianceId uint64) (string, error)
	// 微信验证文件相关方法
	WebGatewayWechatVerify(ctx context.Context, applianceId string) ([]dto.WechatVerifyFileDto, bool, error)
}

type AppUsecase struct {
	repo AppRepo
	log  *log.Helper
}

// NewAppUsecase new a AppUsecase
func NewAppUsecase(repo AppRepo, logger log.Logger) *AppUsecase {
	return &AppUsecase{repo: repo, log: log.NewHelper(log.With(logger, "module", "biz/app-biz"))}
}

func (uc AppUsecase) UciUserInfo(ctx context.Context) (*pb.UciUserInfoResp, error) {

	var ret []*pb.UciUserInfo
	data, err := uc.repo.UciUserInfo(ctx)
	if err != nil {
		uc.log.Errorf("GetUciInfo err: %v", err)
		return &pb.UciUserInfoResp{}, err
	}
	for _, v := range data {
		var item pb.UciUserInfo
		err = copier.Copy(&item, &v)
		if err != nil {
			uc.log.Errorf("GetUciInfo Copier err: %v", err)
			return &pb.UciUserInfoResp{}, err
		}
		ret = append(ret, &item)
	}
	return &pb.UciUserInfoResp{UciUserInfo: ret}, nil
}

func (uc AppUsecase) GetAppBySe(ctx context.Context, applianceId uint64) ([]dto.AppInfoDto, error) {
	app, err := uc.repo.SeGetApp(ctx, applianceId)

	if err != nil {
		uc.log.Errorf("se: %v get app err: %v", applianceId, err)
		return nil, err
	}

	var resNew []dto.AppInfoDto
	for _, appInfoDto := range app {
		if !utils.NeedIpRangeToCIDR(appInfoDto.Address) {
			resNew = append(resNew, appInfoDto)
			continue
		}
		splitIp := strings.Split(appInfoDto.Address, "-")
		cidrs, err := utils.IpRangeToCIDR(nil, splitIp[0], splitIp[1])
		if err != nil {
			uc.log.Errorf("IpRangeToCIDR err: %v, range: %v", err, appInfoDto.Address)
			continue
		}
		if len(cidrs) <= 0 {
			uc.log.Errorf("IpRangeToCIDR get nil cidrs, range: %v", appInfoDto.Address)
			continue
		}
		for _, cidr := range cidrs {
			resNew = append(resNew, dto.AppInfoDto{
				AppId:    appInfoDto.AppId,
				AppName:  appInfoDto.AppName,
				Port:     appInfoDto.Port,
				Address:  cidr,
				Protocol: appInfoDto.Protocol,
			})
		}
	}
	return resNew, nil
}

func (uc AppUsecase) SeGetStrategy(ctx context.Context, applianceId uint64) (*pb.SeGetStrategyResp, error) {
	resp := pb.SeGetStrategyResp{}
	var strategiesList []*pb.SeStrategy
	// 先根据网关和应用的关系获取这个网关有多少条策略
	seStrategies, err := uc.repo.SeStrategy(ctx, applianceId)
	if err != nil {
		uc.log.Errorf("se: %v SeStrategyAppGroup err: %v", applianceId, err)
		return &resp, err
	}
	allAppStrategy, err := uc.repo.GetAllAppStrategy(ctx)
	if err != nil {
		uc.log.Errorf("se: %v GetAllAppStrategy err: %v", applianceId, err)
		return &resp, err
	}
	seStrategies = append(seStrategies, allAppStrategy...)
	for _, strategy := range seStrategies {
		// 根据策略生成json数据
		strategy, err = GenerateStrategyDetail(strategy, uc.repo, ctx)
		if err != nil {
			uc.log.Errorf("generateStrategyJson err:%v", err)
			return nil, err
		}
		// to json
		appIdsUint := utils.SliceMap(strategy.AppIds, func(t1 int64) uint64 {
			return uint64(t1)
		})

		strategiesList = append(strategiesList, &pb.SeStrategy{
			StrategyId:    strategy.Id,
			StartTime:     strategy.StartTime,
			EndTime:       strategy.EndTime,
			AppIds:        appIdsUint,
			UserIds:       strategy.UserIds,
			EnableAllUser: uint32(strategy.EnableAllUser),
			EnableAllApp:  uint32(strategy.EnableAllApp),
			EnableLog:     uint32(strategy.EnableLog),
			StrategyName:  strategy.StrategyName,
		})
	}
	resp.SeStrategy = strategiesList
	return &resp, nil
}

func (uc AppUsecase) WebAppInfo(ctx context.Context, req *pb.WebAppInfoReq) (*pb.WebAppInfoResp, error) {
	info, err := uc.repo.WebAppInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	var infos []*pb.WebAppInfo
	for _, d := range info {
		appInfo := pb.WebAppInfo{
			AppId:         d.Id,
			AppName:       d.AppName,
			Protocol:      d.PublishSchema,
			Uri:           strings.TrimSuffix(d.Uri, "*"),
			ServerAddress: d.ServerAddress,
			IdpId:         d.IdpId,
			AppStatus:     d.AppStatus,
		}
		if d.AppType == "tun" {
			var healthConfig apisix.HealthConfig
			healthConfigBytes := d.HealthConfig.Bytes
			err := json.Unmarshal(healthConfigBytes, &healthConfig)
			if err != nil {
				continue //继续下一个循环
			}
			appInfo.Protocol = healthConfig.Config.Protocol
			d.PublishAddress = healthConfig.Config.Address
			appInfo.ServerAddress = strconv.FormatUint(d.Id, 10) + ".asdsec.com"
		}
		split := strings.Split(d.PublishAddress, ":")
		appInfo.Address = split[0]
		if len(split) == 1 {
			if d.PublishSchema == "http" {
				appInfo.Port = 80
			} else {
				appInfo.Port = 443
			}
		} else {
			port, err := strconv.ParseInt(split[1], 10, 64)
			if err != nil {
				return nil, err
			}
			appInfo.Port = int32(port)
		}
		infos = append(infos, &appInfo)
	}
	resp := pb.WebAppInfoResp{
		WebAppInfo: infos,
	}
	return &resp, nil
}

func (uc AppUsecase) WebAccessInfo(ctx context.Context, req *pb.WebAccessInfoReq) (*pb.WebAccessInfoResp, error) {
	accessInfo, err := uc.repo.WebAccessInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	var resList []*pb.StrategyInfo
	for _, strategy := range accessInfo {
		gap := pb.TimeGap{}
		err = copier.Copy(&gap, &strategy)
		if err != nil {
			return nil, err
		}
		info := pb.StrategyInfo{
			StrategyId:    strategy.StrategyId,
			StrategyName:  strategy.StrategyName,
			StartTime:     strategy.StartTime,
			EndTime:       strategy.EndTime,
			EnableAllUser: uint32(strategy.EnableAllUser),
			EnableAllApp:  uint32(strategy.EnableAllApp),
			EnableLog:     uint32(strategy.EnableLog),
			Action:        strategy.Action,
			Priority:      int32(strategy.Priority),
			RegoFile:      []byte(strategy.RegoFile),
			TimeGap:       &gap,
			UserRiskRule:  string(strategy.UserRiskRule.Bytes),
		}
		appIdsByStrategy, err := uc.GetAppIdsByStrategy(ctx, strategy)
		if err != nil {
			return nil, err
		}
		userIdsByStrategy, err := uc.GetUserIdsByStrategy(ctx, strategy)
		if err != nil {
			return nil, err
		}

		appIdsUint := utils.SliceMap(appIdsByStrategy, func(t1 int64) uint64 {
			return uint64(t1)
		})

		info.AppIds = appIdsUint
		info.UserIds = userIdsByStrategy
		resList = append(resList, &info)
	}
	resp := pb.WebAccessInfoResp{
		StrategyInfo: resList,
	}
	return &resp, nil
}

func (uc AppUsecase) WebGatewayRsApp(ctx context.Context, req *pb.WebGatewayRsAppReq) (*pb.WebGatewayRsAppResp, error) {
	applianceId, err := strconv.ParseUint(req.ApplianceId, 10, 64)
	if err != nil {
		return &pb.WebGatewayRsAppResp{}, err
	}
	apps, err := uc.repo.WebGatewayRsApp(ctx, applianceId)
	if err != nil {
		return &pb.WebGatewayRsAppResp{}, err
	}
	var appList []*pb.ApplicationGatewayInfo
	for _, v := range apps {
		var tmpItem pb.ApplicationGatewayInfo
		err = copier.Copy(&tmpItem, &v)
		if err != nil {
			return &pb.WebGatewayRsAppResp{}, err
		}
		tmpItem.WebCompatibleConfig = v.WebCompatibleConfig.Bytes
		appList = append(appList, &tmpItem)
	}
	resp := pb.WebGatewayRsAppResp{
		Apps: appList,
	}
	return &resp, nil
}

func (uc AppUsecase) WebGatewayRsCrt(ctx context.Context, req *pb.WebGatewayRsCrtReq) (*pb.WebGatewayRsCrtResp, error) {
	apps, err := uc.repo.WebGatewayRsCrt(ctx)
	if err != nil {
		return &pb.WebGatewayRsCrtResp{}, err
	}
	var crtList []*pb.Crt
	for _, v := range apps {
		tmpItem := pb.Crt{
			Id:     v.Id,
			Cert:   v.Certificate,
			Key:    v.PrivateKey,
			Domain: v.Domain,
		}
		crtList = append(crtList, &tmpItem)
	}
	resp := pb.WebGatewayRsCrtResp{
		Crts: crtList,
	}
	return &resp, nil
}

func (uc AppUsecase) GetAppIdsByStrategy(ctx context.Context, strategy dto.WebAccessInfoDto) ([]int64, error) {
	if strategy.EnableAllApp == 1 {
		return nil, nil
	}
	// 查询应用分组ids
	appIds, err := uc.repo.GetWebStrategyAppIds(ctx, strategy.AppGroupIds)
	if err != nil {
		return nil, err
	}
	appIds = append(appIds, strategy.AppIds...)
	appIds = utils.SliceUniqueWithMap(appIds)
	return appIds, nil
}

func (uc AppUsecase) GetUserIdsByStrategy(ctx context.Context, strategy dto.WebAccessInfoDto) ([]string, error) {
	if strategy.EnableAllUser == 1 {
		return nil, nil
	}

	// 1. 先展开所有用户组的用户（不在这里过滤排除的组，因为即使组被排除，组内的其他用户仍可能通过其他方式被包含）
	userIdByGroups, err := uc.repo.GetUserIdByGroups(ctx, strategy.UserGroupIds)
	if err != nil {
		return nil, err
	}

	// 2. 展开所有角色的用户（同样不在这里过滤排除的角色）
	userIdByRole, err := uc.repo.GetUserIdByRoles(ctx, strategy.RoleIds)
	if err != nil {
		return nil, err
	}

	// 3. 合并所有用户：直接指定的用户 + 用户组的用户 + 角色的用户
	allUsers := append(userIdByGroups, userIdByRole...)
	allUsers = append(allUsers, strategy.UserIds...)
	allUsers = utils.SliceUniqueWithMap(allUsers)

	// 4. 从最终用户列表中移除所有排除的用户
	// 首先展开排除的用户组和角色中的用户
	excludeUsersByGroups, err := uc.repo.GetUserIdByGroups(ctx, strategy.ExcludeUserGroupIds)
	if err != nil {
		return nil, err
	}

	excludeUsersByRoles, err := uc.repo.GetUserIdByRoles(ctx, strategy.ExcludeUserRoleIds)
	if err != nil {
		return nil, err
	}

	// 合并所有需要排除的用户
	allExcludeUsers := append(strategy.ExcludeUserIds, excludeUsersByGroups...)
	allExcludeUsers = append(allExcludeUsers, excludeUsersByRoles...)
	allExcludeUsers = utils.SliceUniqueWithMap(allExcludeUsers)

	// 5. 从最终用户列表中移除所有排除的用户
	return utils.RemoveSlice(allUsers, allExcludeUsers), nil
}

func GenerateStrategyDetail(strategy dto.SeStrategy, repo AppRepo, ctx context.Context) (dto.SeStrategy, error) {
	// 查询用户分组ids
	userIdByGroups, err := repo.GetUserIdByGroups(ctx, strategy.UserGroupIds)
	if err != nil {
		return dto.SeStrategy{}, err
	}
	// 查询应用分组ids
	appIds, err := repo.GetStrategyAppIds(ctx, strategy.AppGroupIds)
	if err != nil {
		return dto.SeStrategy{}, err
	}
	// 基于角色查询用户ids
	userIdByRole, err := repo.GetUserIdByRoles(ctx, strategy.RoleIds)
	if err != nil {
		return dto.SeStrategy{}, err
	}
	userIdByGroups = append(userIdByGroups, userIdByRole...)

	// 加上单独的ids
	userIdByGroups = append(userIdByGroups, strategy.UserIds...)
	appIds = append(appIds, strategy.AppIds...)

	// 去重
	userIdByGroups = utils.SliceUniqueWithMap(userIdByGroups)
	appIds = utils.SliceUniqueWithMap(appIds)

	strategy.AppIds = appIds
	strategy.UserIds = userIdByGroups

	return strategy, nil
}

func (uc AppUsecase) WebGatewayWatermark(ctx context.Context, req *pb.WebGatewayWatermarkReq) (*pb.WebGatewayWatermarkResp, error) {
	wmConf, err := uc.repo.WebGatewayWatermark(ctx, req)
	if err != nil {
		return &pb.WebGatewayWatermarkResp{}, err
	}
	var wm pb.Watermark
	err = copier.Copy(&wm, &wmConf)
	if err != nil {
		return &pb.WebGatewayWatermarkResp{}, err
	}
	return &pb.WebGatewayWatermarkResp{WatermarkConf: &wm}, nil
}

func (uc AppUsecase) WebGatewayHosts(ctx context.Context, req *pb.WebGatewayHostsReq) (*pb.WebGatewayHostsResp, error) {
	hosts, err := uc.repo.WebGatewayHosts(ctx, req)
	if err != nil {
		return &pb.WebGatewayHostsResp{}, err
	}

	resp := &pb.WebGatewayHostsResp{}
	if err := copier.Copy(&resp.Hosts, &hosts); err != nil {
		return &pb.WebGatewayHostsResp{}, err
	}

	return resp, nil
}

func (uc AppUsecase) WebGatewayVirtualIPPools(ctx context.Context, req *pb.WebGatewayVirtualIPPoolsReq) (*pb.WebGatewayVirtualIPPoolsResp, error) {
	applianceId, err := strconv.ParseUint(req.ApplianceId, 10, 64)
	if err != nil {
		uc.log.Errorf("parse appliance_id err: %v", err)
		return &pb.WebGatewayVirtualIPPoolsResp{}, err
	}

	globalSettings, err := uc.repo.GetVirtualIPGlobalSettings(ctx)
	if err != nil {
		uc.log.Errorf("GetVirtualIPGlobalSettings err: %v", err)
		return &pb.WebGatewayVirtualIPPoolsResp{}, err
	}

	pools, err := uc.repo.WebGatewayVirtualIPPools(ctx, applianceId)
	if err != nil {
		uc.log.Errorf("WebGatewayVirtualIPPools err: %v", err)
		return &pb.WebGatewayVirtualIPPoolsResp{}, err
	}

	var poolConfigs []*pb.VirtualIPPoolConfig
	for _, pool := range pools {
		config := &pb.VirtualIPPoolConfig{
			Id:               pool.ID,
			Name:             pool.Name,
			IpRange:          pool.IPRange,
			PoolType:         pool.PoolType,
			IpExpiryDuration: int32(pool.IPExpiryDuration),
			CleanupInterval:  int32(pool.CleanupInterval),
		}

		// 分配策略
		if pool.AllocationPolicy != nil {
			config.AllocationPolicy = &pb.AllocationPolicy{
				Strategy:      pool.AllocationPolicy.Strategy,
				MaxIpsPerUser: int32(pool.AllocationPolicy.MaxIPsPerUser),
			}
		}

		// 释放策略
		if pool.ReleasePolicy != nil {
			config.ReleasePolicy = &pb.ReleasePolicy{
				ReleaseOnLogout:     pool.ReleasePolicy.ReleaseOnLogout,
				IdleTimeoutHours:    int32(pool.ReleasePolicy.IdleTimeoutHours),
				ForceReleaseOnLimit: pool.ReleasePolicy.ForceReleaseOnLimit,
			}
		}

		// 目录配置
		for _, dirConfig := range pool.DirectoryConfigs {
			if dirConfig.Config != "" {
				// 解析原始配置
				var rawConfig map[string]interface{}
				if err := json.Unmarshal([]byte(dirConfig.Config), &rawConfig); err != nil {
					uc.log.Errorf("解析池 %s 的DirectoryConfig失败: %v", pool.Name, err)
					continue
				}

				// 预展开用户组信息
				optimizedConfig, err := uc.expandDirectoryConfigUsers(ctx, rawConfig)
				if err != nil {
					uc.log.Errorf("展开池 %s 的用户组信息失败: %v", pool.Name, err)
					// 如果展开失败，使用原始配置
					optimizedConfig = rawConfig
				}

				// 序列化优化后的配置
				optimizedConfigBytes, err := json.Marshal(optimizedConfig)
				if err != nil {
					uc.log.Errorf("序列化优化配置失败: %v", err)
					optimizedConfigBytes = []byte(dirConfig.Config)
				}

				pbDirConfig := &pb.DirectoryConfig{
					DirectoryId:   dirConfig.DirectoryID,
					DirectoryName: dirConfig.DirectoryName,
					UserGroups:    dirConfig.UserGroups,
					Config:        string(optimizedConfigBytes),
				}
				config.DirectoryConfigs = append(config.DirectoryConfigs, pbDirConfig)
			}
		}

		// 独享IP配置 - 同时设置DirectoryConfig的Config字段
		for _, dedicatedConfig := range pool.DedicatedConfigs {
			exists, err := uc.repo.UserIdExists(ctx, dedicatedConfig.UserID)
			if err != nil {
				uc.log.Errorf("校验独享IP用户 %s 是否存在出错: %v", dedicatedConfig.UserID, err)
				continue
			}
			if !exists {
				uc.log.Warnf("独享IP配置中发现无效用户: %s，已移除", dedicatedConfig.UserID)
				// 从数据库中移除无效用户的配置
				poolIDInt, parseErr := strconv.Atoi(pool.ID)
				if parseErr != nil {
					uc.log.Errorf("转换池ID失败: %v", parseErr)
				} else {
					removeErr := uc.repo.RemoveInvalidDedicatedUser(ctx, poolIDInt, dedicatedConfig.UserID)
					if removeErr != nil {
						uc.log.Errorf("移除无效用户 %s 配置失败: %v", dedicatedConfig.UserID, removeErr)
					}
				}
				continue
			}
			config.DedicatedConfigs = append(config.DedicatedConfigs, &pb.DedicatedIPConfig{
				UserId:     dedicatedConfig.UserID,
				UserName:   dedicatedConfig.UserName,
				VirtualIps: dedicatedConfig.VirtualIPs,
				Priority:   int32(dedicatedConfig.Priority),
			})
		}

		poolConfigs = append(poolConfigs, config)
	}

	return &pb.WebGatewayVirtualIPPoolsResp{
		Pools: poolConfigs,
		GlobalSettings: &pb.VirtualIPGlobalSettings{
			Enabled:           globalSettings.Enabled,
			GlobalMaxDuration: int32(globalSettings.GlobalMaxDuration),
			Description:       globalSettings.Description,
		},
	}, nil
}

func (uc AppUsecase) ReportHealthCheckResult(ctx context.Context, req *pb.ReportHealthCheckResultReq) (*pb.ReportHealthCheckResultResp, error) {
	err := uc.repo.UpdateAppHealthStatus(ctx, req)
	if err != nil {
		return &pb.ReportHealthCheckResultResp{
			Success: false,
			Message: "上报健康检查结果失败",
		}, err
	}
	return &pb.ReportHealthCheckResultResp{
		Success: true,
		Message: "上报健康检查结果成功",
	}, err
}

func (uc AppUsecase) ReportVirtualIPAllocations(ctx context.Context, req *pb.ReportVirtualIPAllocationsReq) (*pb.ReportVirtualIPAllocationsResp, error) {
	// 检查是否有init事件，如果有则先处理服务初始化
	hasInitEvent := false
	for _, alloc := range req.Allocations {
		if alloc.Status == "init" {
			hasInitEvent = true
			break
		}
	}
	// 如果有init事件，先释放所有活跃IP
	if hasInitEvent {
		err := uc.HandleVirtualIPServiceInit(ctx, req.ApplianceId)
		if err != nil {
			return &pb.ReportVirtualIPAllocationsResp{
				Success:   false,
				Message:   fmt.Sprintf("初始化: 释放历史活跃虚拟IP失败: %v", err),
				ErrorCode: 500,
			}, nil
		}
	}

	// 转换IP分配数据（过滤掉init事件，因为它们只用于触发初始化）
	var allocations []dto.IPAllocationDto
	for _, alloc := range req.Allocations {
		if alloc.Status == "init" {
			continue // 跳过init事件，不保存到数据库
		}
		allocations = append(allocations, dto.IPAllocationDto{
			IPAddress:             alloc.IpAddress,
			UserID:                alloc.UserId,
			DeviceID:              alloc.DeviceId,
			PoolName:              alloc.PoolName,
			AllocatedAt:           alloc.AllocatedAt,
			ExpiresAt:             alloc.ExpiresAt,
			LastUsedAt:            alloc.LastUsedAt,
			Status:                alloc.Status,
			PeriodUpstreamBytes:   alloc.PeriodUpstreamBytes,
			PeriodDownstreamBytes: alloc.PeriodDownstreamBytes,
			TotalUpstreamBytes:    alloc.TotalUpstreamBytes,
			TotalDownstreamBytes:  alloc.TotalDownstreamBytes,
			SessionCount:          alloc.SessionCount,
			LastTrafficTime:       alloc.LastTrafficTime,
		})
	}

	poolStats := make(map[string]dto.PoolStatsDto)
	for poolName, stats := range req.PoolStats {
		poolStats[poolName] = dto.PoolStatsDto{
			TotalIPs:     int(stats.TotalIps),
			AllocatedIPs: int(stats.AllocatedIps),
			AvailableIPs: int(stats.AvailableIps),
			LastUpdated:  stats.LastUpdated,
		}
	}

	// 保存数据
	err := uc.repo.SaveVirtualIPAllocations(ctx, req.ApplianceId, allocations, poolStats)
	if err != nil {
		uc.log.Errorf("SaveVirtualIPAllocations err: %v", err)
		return &pb.ReportVirtualIPAllocationsResp{
			Success:   false,
			Message:   err.Error(),
			ErrorCode: 500,
		}, nil
	}

	return &pb.ReportVirtualIPAllocationsResp{
		Success: true,
		Message: "IP allocations reported successfully",
	}, nil
}

func (uc AppUsecase) ReportTrafficStats(ctx context.Context, req *pb.TrafficStatsReq) (*pb.TrafficStatsResp, error) {
	// 转换流量统计数据
	var trafficBuckets []dto.TrafficBucketDto
	for _, bucket := range req.TrafficBuckets {
		trafficBuckets = append(trafficBuckets, dto.TrafficBucketDto{
			IPAddress:       bucket.IpAddress,
			UserID:          bucket.UserId,
			BucketStartTime: bucket.BucketStartTime,
			BucketEndTime:   bucket.BucketEndTime,
			UpstreamBytes:   bucket.UpstreamBytes,
			DownstreamBytes: bucket.DownstreamBytes,
			SessionCount:    int(bucket.SessionCount),
		})
	}

	// 保存流量统计数据
	err := uc.repo.SaveTrafficStats(ctx, req.ApplianceId, trafficBuckets)
	if err != nil {
		uc.log.Errorf("SaveTrafficStats err: %v", err)
		return &pb.TrafficStatsResp{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	uc.log.Infof("Traffic stats reported: appliance=%s, buckets=%d, processed=%d, failed=%d",
		req.ApplianceId, len(req.TrafficBuckets), req.ProcessedBuckets, req.FailedBuckets)

	return &pb.TrafficStatsResp{
		Success: true,
		Message: "Traffic stats reported successfully",
	}, nil
}

// 新增：展开目录配置中的用户组信息
func (uc AppUsecase) expandDirectoryConfigUsers(ctx context.Context, rawConfig map[string]interface{}) (map[string]interface{}, error) {
	// 复制原始配置
	optimizedConfig := make(map[string]interface{})
	for k, v := range rawConfig {
		optimizedConfig[k] = v
	}

	// 检查是否有departments字段需要展开
	if departments, exists := rawConfig["departments"]; exists {
		if deptArray, ok := departments.([]interface{}); ok {
			// 提取所有部门ID
			var departmentIds []string
			for _, dept := range deptArray {
				if deptMap, ok := dept.(map[string]interface{}); ok {
					if id, exists := deptMap["id"]; exists {
						if idStr, ok := id.(string); ok {
							departmentIds = append(departmentIds, idStr)
						}
					}
				}
			}

			// 如果有部门ID，查询对应的用户
			if len(departmentIds) > 0 {
				userIds, err := uc.repo.GetUserIdByGroups(ctx, departmentIds)
				if err != nil {
					uc.log.Errorf("GetUserIdByGroups err: %v", err)
					return optimizedConfig, err
				}

				// 去重用户ID
				uniqueUserIds := utils.SliceUniqueWithMap(userIds)

				// 添加展开的用户ID列表到配置中
				optimizedConfig["expanded_user_ids"] = uniqueUserIds

				// uc.log.Debugf("部门展开完成: %d个部门 -> %d个用户",
				// 	len(departmentIds), len(uniqueUserIds))
			}
		}
	}

	return optimizedConfig, nil
}

// GatewayCommand 处理网关命令拉取请求
func (uc AppUsecase) GatewayCommand(ctx context.Context, req *pb.GatewayCommandReq) (*pb.GatewayCommandResp, error) {
	applianceID, err := strconv.ParseInt(req.ApplianceId, 10, 64)
	if err != nil {
		uc.log.Errorf("parse appliance_id err: %v", err)
		return &pb.GatewayCommandResp{}, err
	}

	// 获取待处理的网关命令
	commands, err := uc.repo.GetPendingGatewayCommands(ctx, applianceID)
	if err != nil {
		uc.log.Errorf("GetPendingGatewayCommands err: %v", err)
		return &pb.GatewayCommandResp{}, err
	}
	// 转换为proto格式
	var pbCommands []*pb.GatewayCommand
	for _, cmd := range commands {
		pbCmd := &pb.GatewayCommand{
			CommandId:      cmd.CommandID,
			CommandType:    cmd.CommandType,
			Action:         cmd.Action,
			TargetResource: cmd.TargetResource,
			Parameters:     make(map[string]string),
			Reason:         cmd.Reason,
			CreatedAt:      cmd.CreatedAt.Unix(),
			Status:         "processing", // 设置为processing状态
			CreatedBy:      cmd.CreatedBy,
		}

		if cmd.ExpiresAt != nil {
			pbCmd.ExpiresAt = cmd.ExpiresAt.Unix()
		}

		// 解析JSON参数到map
		if cmd.Parameters != nil {
			var params map[string]interface{}
			if err := json.Unmarshal(cmd.Parameters, &params); err == nil {
				for k, v := range params {
					if str, ok := v.(string); ok {
						pbCmd.Parameters[k] = str
					} else {
						// 将非字符串类型转换为字符串
						pbCmd.Parameters[k] = fmt.Sprintf("%v", v)
					}
				}
			}
		}

		pbCommands = append(pbCommands, pbCmd)
	}

	// 标记命令为已处理
	for _, cmd := range commands {
		_ = uc.repo.UpdateGatewayCommandStatus(ctx, cmd.CommandID, "processing", "")
	}

	// uc.log.Debugf("网关 %s 拉取了 %d 个待处理命令", req.ApplianceId, len(pbCommands))

	return &pb.GatewayCommandResp{
		Commands: pbCommands,
	}, nil
}

// ReportGatewayCommandResult 处理网关命令执行结果报告
func (uc AppUsecase) ReportGatewayCommandResult(ctx context.Context, req *pb.GatewayCommandResultReq) (*pb.GatewayCommandResultResp, error) {
	for _, result := range req.Results {
		err := uc.repo.UpdateGatewayCommandStatus(ctx, result.CommandId, result.Status, result.Message)
		if err != nil {
			uc.log.Errorf("UpdateGatewayCommandStatus err: %v", err)
			continue
		}

		uc.log.Debugf("网关 %s 报告命令 %s 执行结果: %s - %s",
			req.ApplianceId, result.CommandId, result.Status, result.Message)
	}

	return &pb.GatewayCommandResultResp{
		Success: true,
		Message: "命令结果处理成功",
	}, nil
}

// HandleVirtualIPServiceInit 处理虚拟IP服务初始化事件
// 当接收到init动作的事件时，将指定网关的所有活跃IP分配标记为已释放
func (uc AppUsecase) HandleVirtualIPServiceInit(ctx context.Context, applianceId string) error {
	uc.log.Debugf("处理虚拟IP服务初始化事件: 网关=%s", applianceId)

	// 调用repository方法释放所有活跃IP
	err := uc.repo.ReleaseAllActiveVirtualIPs(ctx, applianceId)
	if err != nil {
		uc.log.Errorf("释放网关 %s 的历史活跃虚拟IP失败: %v", applianceId, err)
		return err
	}

	uc.log.Debugf("成功处理虚拟IP服务初始化: 网关=%s", applianceId)
	return nil
}

// WebGatewayRsSSOIDP 网关拉取SSO IDP配置
func (uc AppUsecase) WebGatewayRsSSOIDP(ctx context.Context, req *pb.WebGatewayRsSSOIDPReq) (*pb.WebGatewayRsSSOIDPResp, error) {
	uc.log.Debugf("网关拉取SSO IDP配置: corp_id=%s, type=%s", req.CorpId, req.Type)

	// 定义支持的IDP类型
	supportedTypes := []string{"dingtalk", "feishu", "qiyewx"}

	// 如果指定了类型，只查询指定类型；否则查询所有支持的类型
	var queryTypes []string
	if req.Type != "" {
		// 验证类型是否支持
		typeSupported := false
		for _, supportedType := range supportedTypes {
			if req.Type == supportedType {
				typeSupported = true
				break
			}
		}
		if !typeSupported {
			return nil, fmt.Errorf("不支持的IDP类型: %s", req.Type)
		}
		queryTypes = []string{req.Type}
	} else {
		queryTypes = supportedTypes
	} // 从repository获取IDP配置
	idpDtos, err := uc.repo.WebGatewayRsSSOIDP(ctx, queryTypes)
	if err != nil {
		uc.log.Errorf("获取SSO IDP配置失败: %v", err)
		return nil, err
	}

	// 转换为protobuf格式
	var pbIDPs []*pb.SSOIDPInfo
	for _, idpDto := range idpDtos {
		pbIDP := &pb.SSOIDPInfo{
			Id:          idpDto.ID,
			Name:        idpDto.Name,
			Type:        idpDto.Type,
			CorpId:      idpDto.CorpId,
			AppId:       idpDto.AppId,
			AppKey:      idpDto.AppKey,
			AppSecret:   idpDto.AppSecret,
			RedirectUri: idpDto.RedirectUri,
			Enable:      idpDto.Enable,
			CreatedAt:   idpDto.CreatedAt.Unix(),
			UpdatedAt:   idpDto.UpdatedAt.Unix(),
		}
		pbIDPs = append(pbIDPs, pbIDP)
	}

	uc.log.Debugf("成功获取SSO IDP配置: 共%d个配置", len(pbIDPs))
	return &pb.WebGatewayRsSSOIDPResp{
		Idps: pbIDPs,
	}, nil
}

// WebGatewayPlatformDomain 网关获取平台域名
func (uc AppUsecase) WebGatewayPlatformDomain(ctx context.Context, req *pb.WebGatewayPlatformDomainReq) (*pb.WebGatewayPlatformDomainResp, error) {
	uc.log.Debugf("网关获取平台域名: appliance_id=%d", req.ApplianceId)

	platformDomain, err := uc.repo.GetPlatformDomainByApplianceID(ctx, req.ApplianceId)
	if err != nil {
		uc.log.Errorf("获取平台域名失败: appliance_id=%d, err=%v", req.ApplianceId, err)
		return &pb.WebGatewayPlatformDomainResp{
			PlatformDomain: "",
			Success:        false,
			Message:        fmt.Sprintf("获取平台域名失败: %v", err),
		}, nil
	}

	uc.log.Debugf("成功获取平台域名: appliance_id=%d, domain=%s", req.ApplianceId, platformDomain)
	return &pb.WebGatewayPlatformDomainResp{
		PlatformDomain: platformDomain,
		Success:        true,
		Message:        "获取平台域名成功",
	}, nil
}

// WebGatewayWechatVerify 网关拉取微信验证文件
func (uc AppUsecase) WebGatewayWechatVerify(ctx context.Context, req *pb.WebGatewayWechatVerifyReq) (*pb.WebGatewayWechatVerifyResp, error) {
	uc.log.Debugf("网关拉取微信验证文件: appliance_id=%s", req.ApplianceId)

	// 从数据库获取微信验证文件列表
	files, globalEnabled, err := uc.repo.WebGatewayWechatVerify(ctx, req.ApplianceId)
	if err != nil {
		uc.log.Errorf("获取微信验证文件失败: appliance_id=%s, err=%v", req.ApplianceId, err)
		return &pb.WebGatewayWechatVerifyResp{
			Files:         []*pb.WechatVerifyFile{},
			GlobalEnabled: false,
		}, nil
	}

	// 转换为proto格式
	var protoFiles []*pb.WechatVerifyFile
	for _, file := range files {
		protoFile := &pb.WechatVerifyFile{
			Id:          file.ID,
			FileName:    file.FileName,
			Content:     file.Content,
			IsEnabled:   file.IsEnabled,
			Description: file.Description,
			CreatedAt:   file.CreatedAt.Unix(),
			UpdatedAt:   file.UpdatedAt.Unix(),
		}
		protoFiles = append(protoFiles, protoFile)
	}

	uc.log.Debugf("成功获取微信验证文件: appliance_id=%s, files_count=%d, global_enabled=%t",
		req.ApplianceId, len(protoFiles), globalEnabled)

	return &pb.WebGatewayWechatVerifyResp{
		Files:         protoFiles,
		GlobalEnabled: globalEnabled,
	}, nil
}
