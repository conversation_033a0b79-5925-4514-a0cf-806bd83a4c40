package test

import (
	alertComm "asdsec.com/asec/platform/app/console/app/alert/common"
	"asdsec.com/asec/platform/app/console/app/alert/model"
	"asdsec.com/asec/platform/app/console/app/alert/service"
	"context"
	"testing"
	"time"
)

var Ctx = context.Background()

func TestAdd(t *testing.T) {
	robot := model.NotifySetting{
		TenantId:    1200,
		RobotName:   "告警机器人",
		PlatName:    "wechat",
		WebhookAddr: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b1a4cc6-e67a-49b6-9ad0-0ad855ae84e5",
		ApiSecret:   "",
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}

	err := service.GetNotifyService().AddRobot(Ctx, 1200, &robot)
	if err != nil && err.Error() == "robot name exit" {
		t.Log("1111111111")
	}
}

func TestEdit(t *testing.T) {
	robotId := uint64(5)
	_, err := service.GetNotifyService().GetRobotById(Ctx, 1200, robotId)
	if err != nil {
		if err.Error() == alertComm.ErrRecordNotFound {
			t.Log("记录不存在")
			return
		}
		t.Log(err.Error())
		return
	}
	robot := model.NotifySetting{
		RobotName:   "1234",
		PlatName:    "2314123",
		WebhookAddr: "321321",
		ApiSecret:   "12312",
		UpdateTime:  time.Now(),
	}
	err = service.GetNotifyService().UpdateRobot(Ctx, 1200, robotId, &robot)
	if err != nil {
		t.Log(err.Error())
	}
}

func TestDel(t *testing.T) {
	err := service.GetNotifyService().DelRobot(Ctx, 1200, []uint64{4, 5, 6})
	if err != nil {
		t.Log(err.Error())
	}
}

func TestGetAll(t *testing.T) {
	res, err := service.GetNotifyService().GetAllRobot(Ctx, 1200, "", "", 0, 0)
	if err != nil {
		t.Log(err.Error())
	}
	t.Log(res)
}
