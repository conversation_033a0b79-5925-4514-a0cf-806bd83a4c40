/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import{_ as e,u as o,r,c as a,o as s,a as t,b as l,d as n,n as c,t as i,e as g}from"./index.a794166c.js";const d={class:"container"},w=e(Object.assign({name:"Status"},{setup(e){const w=o(),u=r(null),p=r(null),f=r("正在处理飞书认证..."),y=r("normal"),m=r(!0),_=a((()=>["message",y.value].filter(Boolean).join(" "))),h=(e,o="normal")=>{f.value=e,y.value=o,"normal"!==o&&(m.value=!1)},v=e=>{try{logger.log("向父窗口发送消息:",e),window.parent&&window.parent!==window&&(window.parent.postMessage(e,"*"),logger.log("已向parent发送消息")),window.opener&&(window.opener.postMessage(e,"*"),logger.log("已向opener发送消息")),window.top&&window.top!==window&&(window.top.postMessage(e,"*"),logger.log("已向top发送消息"))}catch(o){console.error("发送消息失败:",o)}},b=()=>{try{const e=(()=>{if(w.query&&Object.keys(w.query).length>0)return w.query;const e=new URLSearchParams(window.location.search),o={};for(const[r,a]of e)o[r]=a;return o})();logger.log("URL参数:",e);const{code:o,state:r,error:a,error_description:s}=e,t="feishu";if(logger.log("检测到的认证类型:",t),a)return console.error("认证失败:",a,s),h("认证失败: "+(s||a),"error"),void v({type:t+"_auth_callback",error:s||a});if(o&&r){logger.log("认证成功，code:",o,"state:",r);let e="认证成功，正在跳转...";"feishu"===t&&(e="飞书认证成功，正在跳转..."),h(e,"success"),v({type:t+"_auth_callback",code:o,state:r}),setTimeout((()=>{try{if(window.parent!==window){const e=t+"-callback-iframe",o=window.parent.document.getElementById(e);o&&(o.style.display="none")}window.opener&&window.close()}catch(e){logger.log("无法自动关闭/隐藏窗口:",e.message)}}),1e3)}else console.error("缺少必要的认证参数"),h("认证参数不完整","error"),v({type:t+"_auth_callback",error:"认证参数不完整"})}catch(e){console.error("处理回调失败:",e),h("处理认证结果时出错","error"),v({type:"auth_callback",error:"处理认证结果时出错: "+e.message})}};return s((()=>{logger.log("飞书认证状态页面挂载"),setTimeout((()=>{b()}),100)})),(e,o)=>(t(),l("div",d,[n("div",{ref_key:"loadingRef",ref:u,class:"loading",style:c({display:m.value?"block":"none"})},null,4),n("div",{ref_key:"messageRef",ref:p,class:g(_.value)},i(f.value),3)]))}}),[["__scopeId","data-v-0941919a"]]);export{w as default};
