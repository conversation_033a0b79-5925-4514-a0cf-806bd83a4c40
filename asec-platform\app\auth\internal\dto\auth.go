package dto

import (
	"time"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

type TokenType string
type RedisPrefixKey string

const (
	AuthorizationKey                       = "Authorization"
	TokenTypeBear                          = "Bearer"
	AccessTokenDuration                    = "access_token_duration"
	RefreshTokenDuration                   = "refresh_token_duration"
	AccessTokenTyp          TokenType      = "access_token"
	RefreshTokenTyp         TokenType      = "refresh_token"
	ActiveCorp              RedisPrefixKey = "active"
	ActiveUserToken         RedisPrefixKey = "token"
	ActiveUser              RedisPrefixKey = "user"
	ActiveUserSessionMaxNum int            = 10

	TokenBlackList         RedisPrefixKey = "logout"
	TokenBlackDefaultValue string         = "1"
	SmsIdpPrefix           string         = "secondary_idp"

	UserAuthType  string = "user"
	AdminAuthType string = "admin"

	EmailIdpPrefix         string = "email_idp"  // 邮箱验证码缓存前缀
	VerificationCodePrefix string = "verif_code" // 通用验证码前缀
)

const (
	LoginLogType      = "LOGIN"
	LoginOutLogType   = "LOGOUT"
	LoginErrorLogType = "LOGIN_ERROR"
	AuthLoginLog      = "User"
)

type GrantType string

const (
	GrantTypeImplicit  GrantType = "implicit"
	GrantTypeAuthzCode GrantType = "authorization_code"
)

const (
	HeaderCorpKey    = "X-Corp-ID"
	HeaderClientHost = "RemoteAddr"
)

type CacheType string

const (
	CacheTypeWxIdp       = CacheType(IDPTypeQiYeWx)
	CacheTypeFsIdp       = CacheType(IDPTypeFeiShu)
	CacheTypeDingtalkIdp = CacheType(IDPTypeDingtalk)
	CacheTypeLdapIdp     = CacheType(IDPTypeLdap)
	CacheTypeMsadkIdp    = CacheType(IDPTypeMsad)
	CacheTypeSmsIdp      = CacheType(IDPTypeSMS)
	CacheTypeInfogoIdp   = CacheType(IDPTypeInfogo)
	CacheTypeOAuth2Idp   = CacheType(IDPTypeOAuth2) // 新增OAuth2支持
	CacheTypeOAuth2Test  = CacheType("oauth2_test")
	CacheTypeCasIdp      = CacheType(IDPTypeCas) // 新增OAuth2支持
	CacheTypeWebIdp      = CacheType(IDPTypeWeb) // 新增OAuth2支持
	CacheTypeEmailIdp    = CacheType(IDPTypeEmail)
)

const (
	WxWebCacheTTL  = time.Minute
	FsWebCacheTTL  = time.Minute
	DtWebCacheTTL  = time.Minute
	SmsWebCacheTTL = time.Minute
)
const (
	InRegionOpt           = "in"
	NotInRegionOpt        = "not_in"
	AbnormalLogin         = 0
	RegionEnhancement     = "登录地(%s)异常"
	TimeEnhancement       = "登录时间(%s)异常"
	Enhancement           = "触发增强认证"
	AuthEnhancementPrefix = "auth_enhancement"
)

type LoginForm struct {
	AuthBasicForm
	LocalLoginForm
}

type AuthBasicForm struct {
	CorpId         string
	IdpId          string
	RedirectUri    string
	GrantType      GrantType
	ClientId       string
	Scope          string
	UniqKey        string
	AuthCode       string
	AdUsername     string
	AdPwd          string
	ActivationCode string
	TotpKey        string
	Encryption     string
	ClientType     string `json:"client_type,omitempty"` // 新增：客户端类型 pc/mobile
	DeviceID       string `json:"device_id,omitempty"`   // 新增：设备ID
	DeviceInfo     string `json:"device_info,omitempty"` // 新增：设备信息
}

type LocalLoginForm struct {
	UserName string
	Password string
	UserId   string
}

type ThirdLoginForm struct {
	AuthBasicForm
	AuthWeb     AuthWebLoginForm
	RedirectUri string
}

type AuthWebLoginForm struct {
	AuthWebToken string
	AuthWebCode  string
	AuthWebState string
	AuthParams   map[string]string
}

// AuthzLoginResp auth2.0认证码授权返回结果
type AuthzLoginResp struct {
	Code        string
	GrantType   GrantType
	RedirectUri string
}

// ImplicitLoginResp auth2.0隐式授权直接返回token
type ImplicitLoginResp struct {
	Token
}

type IssuerParams struct {
	PrivateKey                any
	AccessTokenDuration       time.Duration
	RefreshTokenDuration      time.Duration
	RefreshTokenLeastDuration time.Duration
}

type IssueUserInfo struct {
	CorpId        string
	UserId        string
	UserName      string
	PreferredName string
	Identifier    string
	Phone         string
	Email         string
	GroupId       string
	SecurityCode  string
	SessionID     string
}

type Token struct {
	AccessToken     string `json:"accessToken"`
	ExpireIn        int64  `json:"expireIn"`
	RefreshToken    string `json:"refreshToken"`
	RefreshExpireIn int64  `json:"refreshExpireIn"`
	TokenType       string `json:"tokenType"`
}
type AuthEnhancement struct {
	IdpId  string   `json:"idp_id"`
	Factor []Factor `json:"factor"`
}
type Factor struct {
	Type       string   `json:"type"`
	Ids        []string `json:"ids"`
	Operator   string   `json:"operator"`
	RegionType []string `json:"region_type"`
}

type AuthBasic struct {
	Idp    *model.TbIdentityProvider
	User   *model.TbUserEntity
	Policy []*model.TbAuthPolicy
}

type PublicKey struct {
	CorpId    string
	PublicKey string
}

type WxWebCache struct {
	IdpId string `json:"idpId"`
}

type UserCount struct {
	TotalCount   uint32
	OnlineCount  uint32
	OfflineCount uint32
}

type CreateLoginLogParam struct {
	Id          string
	CorpId      string
	ClientId    string
	Error       string
	IpAddress   string
	EventTime   int64
	Type        string
	UserName    string
	SourceId    string
	Description string
}

type LoginAuthInfo struct {
	UserId              string              `json:"userId"`
	Name                string              `json:"name"`
	NeedSecondary       bool                `json:"needSecondary"`
	Phone               string              `json:"phone,omitempty"`
	CurrentSecret       string              `json:"current_secret"`
	Email               string              `json:"email,omitempty"`
	SecondaryCategory   string              `json:"secondaryCategory,omitempty"` //二次认证类型
	SecondaryType       string              `json:"secondaryType,omitempty"`     //二次认证发送方式
	AuthEnhancementInfo AuthEnhancementInfo `json:"authEnhancementInfo"`
	IdpAttr             IdpAttr
	Account             string `json:"account"`
}

type AuthEnhancementInfo struct {
	IsRegionEnhancement bool   `json:"is_region_enhancement"`
	IsTimeEnhancement   bool   `json:"is_time_enhancement"`
	Region              string `json:"region"`
	Time                string `json:"time"`
}

type UserBindInfo struct {
	Success       bool   `json:"success"`
	LocalUserId   string `json:"local_user_id"`
	LocalUserName string `json:"local_user_name"`
	Message       string `json:"message"`
}

// MicroAppAuthParam 微应用认证参数
type MicroAppAuthParam struct {
	AuthProvider string `json:"auth_provider"` // 认证提供商：dingtalk, wechat_work, feishu
	AuthCode     string `json:"auth_code"`     // 认证码
	CorpId       string `json:"corp_id"`       // 企业ID
	IdpId        string `json:"idp_id"`        // IDP ID
	RedirectUri  string `json:"redirect_uri"`  // 重定向地址
}

// MicroAppAuthResult 微应用认证结果
type MicroAppAuthResult struct {
	AccessToken  string `json:"access_token"`  // 访问令牌
	RefreshToken string `json:"refresh_token"` // 刷新令牌
	UserId       string `json:"user_id"`       // 用户ID
	UserName     string `json:"user_name"`     // 用户名
	AuthProvider string `json:"auth_provider"` // 认证提供商
	AuthType     string `json:"auth_type"`     // 认证类型
}
