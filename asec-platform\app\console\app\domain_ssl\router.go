package ddr_source

import (
	"asdsec.com/asec/platform/app/console/app/domain_ssl/api"
	"github.com/gin-gonic/gin"
)

func DomainSslApi(r *gin.RouterGroup) {
	v := r.Group("/v1/domain_ssl")
	{
		//列表接口
		v.GET("/list", api.GetDomainCertificateList)
		//证书合法性
		//v.POST("/verify", api.VerifyDomainCertificate)
		//证书详情
		v.GET("/detail", api.GetDomainCertificateDetail)
		//新增证书
		v.POST("", api.AddDomainCertificate)
		//删除证书
		v.DELETE("", api.DelDomainCertificate)
		//修改证书
		v.PUT("", api.UpdateDomainCertificate)
	}
}
