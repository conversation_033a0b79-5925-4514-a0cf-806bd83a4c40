package dto

import (
	"time"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

const FakeRootUserGroupId = "0"

type UserGroupNode struct {
	Id            string // 分组id
	Name          string // 分组名，在同级目录下唯一
	ParentGroupId string // 父级分组id，用于维护分组的树形结构，为0时表示最顶层
	CorpId        string // 租户id
	SourceId      string // 来源id
	IsDefault     bool
	SourceType    string
	TemplateType  string // 模版类型
	SourceName    string
	Description   string
	CreatedAt     time.Time // 创建时间
}

type UserGroupTree struct {
	UserGroupNode
	Children    []*UserGroupTree // 子节点
	BindIdpList []BindIDPInfo    // 绑定idp
}

func (u UserGroupTree) CountChildren() int {
	return len(u.Children)
}

func (u UserGroupTree) PageChildren(offset, limit int) []*UserGroupTree {
	if offset < 0 || offset >= len(u.Children) {
		return []*UserGroupTree{}
	}
	if limit < 0 {
		return []*UserGroupTree{}
	}
	bound := offset + limit
	if bound > len(u.Children) {
		bound = len(u.Children)
	}
	return u.Children[offset:bound]
}

type ListUserGroupResp struct {
	Root  *UserGroupTree
	Count uint32
}

type UserGroupWithIDP struct {
	model.TbUserGroup
	IDPId   string
	IDPName string
}

type UserGroupWithIDPs []UserGroupWithIDP

func (u UserGroupWithIDPs) BuildGroupToIdp() map[string][]BindIDPInfo {
	groupToIdp := make(map[string][]BindIDPInfo)
	for _, e := range u {
		groupToIdp[e.ID] = append(groupToIdp[e.ID], BindIDPInfo{
			Id:   e.IDPId,
			Name: e.IDPName,
		})
	}
	return groupToIdp
}

type SyncStatus string

const (
	SyncSuccess SyncStatus = "success"
	SyncFailed  SyncStatus = "failed"
	SyncRunning SyncStatus = "running"
)

type SyncUnit string

const (
	SyncUnitMin  SyncUnit = "minute"
	SyncUnitHour SyncUnit = "hour"
	SyncUnitDay  SyncUnit = "day"
)

const (
	DefaultSyncUnit  = SyncUnitHour
	DefaultSyncCycle = 24
)

type RootGroup struct {
	ID           string // 分组id
	Name         string // 分组名，在同级目录下唯一
	SourceId     string // 来源id
	SourceType   string // 来源类型
	TemplateType string // 模版类型
	SourceName   string // 来源名称
	IsDefault    bool
	SyncStatus   SyncStatus
	SyncTime     time.Time
	AutoSync     bool
	CreatedAt    time.Time
}

type ListRootGroupResult struct {
	RootGroups []RootGroup
	Count      uint32
}

type GroupBasicInfo struct {
	Id           string // 分组id
	Name         string // 分组名，在同级目录下唯一
	SourceId     string // 来源id
	SourceType   string // 来源类型
	TemplateType string // 模版类型
	SourceName   string // 来源名称
	IsDefault    bool
	CreatedAt    time.Time
	Description  string
}

type SyncGroupInfo struct {
	SyncStatus SyncStatus
	SyncTime   time.Time
	AutoSync   bool
	SyncCycle  int32
	SyncUnit   SyncUnit
	SyncId     string
	WxConfig
	FeishuConfig
	DingtalkConfig
	IdpConfig
	InfogoConfig
	OAuth2Config
	FieldMap []KV
}
type GetRootGroupResult struct {
	GroupBasicInfo
	SyncGroupInfo
}

type RootGroupAttrs struct {
	ID         string // 分组id
	SyncStatus SyncStatus
	SyncTime   string
	AutoSync   bool
}

type CreateGroupParam struct {
	CorpId        string
	Name          string
	ParentGroupId string
	SourceId      string
	Description   string
	GroupId       string
}

type UpdateGroupParam struct {
	GroupId       string
	ParentGroupId string
	CreateRootGroupParam
}

type CreateRootGroupParam struct {
	CorpId         string
	Name           string
	SourceId       string
	Description    string
	AutoSync       bool
	SyncCycle      int32
	SyncUnit       SyncUnit
	WxConfig       WxConfig
	FeishuConfig   FeishuConfig
	DingtalkConfig DingtalkConfig
	LdapConfig     LdapConfig
	InfogoConfig   InfogoConfig
	OAuth2Config   OAuth2Config
	FieldMap       []KV
}

type CreateRootGroupDaoParam struct {
	ID          string
	CorpId      string
	Name        string
	SourceId    string
	Description string
	AutoSync    bool
	SyncCycle   int32
	SyncUnit    SyncUnit
	Configs     []KV
}

type CreateGroupDaoParam struct {
	CorpId        string
	Id            string
	Name          string
	Description   string
	ParentGroupId string
	Path          string
	SourceId      string
	RootGroupID   string
}

type RootGroupSync struct {
	CorpID       string
	GroupID      string
	AutoSync     bool
	LastSyncTime time.Time
	NextSyncTime time.Time
	SyncStatus   string
	SyncCycle    int32
	SyncUnit     string
}

type CreateSyncLogParam struct {
	CorpID     string
	GroupID    string
	Type       SyncType
	SyncStatus SyncStatus
	SyncInfo   string
	Operator   string
}

type SyncLogResp struct {
	SyncLogs []*model.TbUserGroupSyncLog
	Count    uint32
}

type SyncType string

const (
	SyncTypeAuto   SyncType = "auto"
	SyncTypeCustom SyncType = "custom"
)

const (
	SyncSuccessInfo = "同步成功"
	SyncFailedInfo  = "同步失败"

	SyncOperatorSysDefault   = "系统(自动)"
	SyncOperatorAdminDefault = "管理员"
)
