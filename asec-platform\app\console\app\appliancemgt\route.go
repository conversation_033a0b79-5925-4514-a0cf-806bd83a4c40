package appliancemgt

import (
	"asdsec.com/asec/platform/app/console/app/appliancemgt/api"
	switchApi "asdsec.com/asec/platform/app/console/app/module_switch/api"
	"github.com/gin-gonic/gin"
)

func AppMgtApi(r *gin.RouterGroup) {
	v := r.Group("/v1/agents")
	{
		v.POST("", api.GetAgentList)
		v.GET("/upgrade_policy", api.GetUpgradePolicyDetail)
		v.POST("/upgrade_policy", api.SetUpgradePolicy)
		v.GET("/upgrade_overview", api.GetUpgradeOverview)
		v.GET("/tree", switchApi.GetAllModuleSwitchAgents)
		v.GET("/pas", api.GetAgentPas)
		v.POST("/upload_log", api.UploadLog)
		v.POST("/delete", api.DeleteAgentsRecord)
		v.POST("/uninstall", api.UninstallAgents)
		v.POST("/bind", api.BindAgentUser)
	}
	v = r.Group("v1/appliances")
	{
		v.POST("/install_cmd", api.GetInstallCMD)
		v.POST("/list", api.GetApplianceList)
		v.PUT("", api.UpdateAppliance)
		v.DELETE("", api.DeleteAppliance)
		v.GET("check_application_quote", api.CheckAppQuote)
	}
	v = r.Group("/v1/auth")
	{
		v.GET("/file", api.DownloadAuthFile)
	}
	v = r.Group("")
	{
		v.GET("/archives/latest/:file", api.DownloadArchives)

	}
}
