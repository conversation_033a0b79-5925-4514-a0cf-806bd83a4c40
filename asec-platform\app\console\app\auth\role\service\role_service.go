package service

import (
	"asdsec.com/asec/platform/app/console/app/auth/role/model"
	"asdsec.com/asec/platform/app/console/app/auth/role/repository"
	globalModel "asdsec.com/asec/platform/pkg/model"
	"context"
	"sync"
)

var AdminRoleServiceImpl AdminRoleService

// AdminRoleServiceInit 单例对象
var AdminRoleServiceInit sync.Once

type AdminRoleService interface {
	CreateAdminRole(ctx context.Context, req model.CreateAdminRoleReq) error
	DeleteAdminRole(ctx context.Context, roleId string, corpId string) error
	GetAdminRoleList(ctx context.Context, req model.GetAdminRoleListReq) (globalModel.Pagination, error)
}

type adminRoleService struct {
	db repository.AdminRoleRepository
}

func (a adminRoleService) CreateAdminRole(ctx context.Context, req model.CreateAdminRoleReq) error {
	return a.db.CreateAdminRole(ctx, req)
}

func (a adminRoleService) DeleteAdminRole(ctx context.Context, roleId string, corpId string) error {
	return a.db.DeleteAdminRole(ctx, roleId, corpId)
}

func (a adminRoleService) GetAdminRoleList(ctx context.Context, req model.GetAdminRoleListReq) (globalModel.Pagination, error) {
	return a.db.GetAdminRoleList(ctx, req)
}

func GetAdminRoleService() AdminRoleService {
	AdminRoleServiceInit.Do(func() {
		AdminRoleServiceImpl = &adminRoleService{db: repository.NewAppRepository()}
	})
	return AdminRoleServiceImpl
}
