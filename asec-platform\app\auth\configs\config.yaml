server:
  http:
    addr: 0.0.0.0:8000
    timeout: 300s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
  primary: true
data:
  database:
    driver: postgres
    dsn: "host=************* port=5432 user=asec dbname=asec_platform password=pg@asd@1234! sslmode=disable"
  redis:
    addr: *************:6379
    password: redis@asd@1234!
    dial_timeout: 1s
    read_timeout: 0.4s
    write_timeout: 0.6s
  ip2region_type: aiwencity
  aiwen_key: PmUVVY1jHaEpk4E5pEhMmnfFz8U5iRS3tc7PKwuYyaeNvaDiIThqTasyQGBN6p0x