// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserGroupSyncLog(db *gorm.DB, opts ...gen.DOOption) tbUserGroupSyncLog {
	_tbUserGroupSyncLog := tbUserGroupSyncLog{}

	_tbUserGroupSyncLog.tbUserGroupSyncLogDo.UseDB(db, opts...)
	_tbUserGroupSyncLog.tbUserGroupSyncLogDo.UseModel(&model.TbUserGroupSyncLog{})

	tableName := _tbUserGroupSyncLog.tbUserGroupSyncLogDo.TableName()
	_tbUserGroupSyncLog.ALL = field.NewAsterisk(tableName)
	_tbUserGroupSyncLog.CorpID = field.NewString(tableName, "corp_id")
	_tbUserGroupSyncLog.GroupID = field.NewString(tableName, "group_id")
	_tbUserGroupSyncLog.Type = field.NewString(tableName, "type")
	_tbUserGroupSyncLog.SyncStatus = field.NewString(tableName, "sync_status")
	_tbUserGroupSyncLog.SyncInfo = field.NewString(tableName, "sync_info")
	_tbUserGroupSyncLog.Operator = field.NewString(tableName, "operator")
	_tbUserGroupSyncLog.CreatedAt = field.NewTime(tableName, "created_at")

	_tbUserGroupSyncLog.fillFieldMap()

	return _tbUserGroupSyncLog
}

type tbUserGroupSyncLog struct {
	tbUserGroupSyncLogDo tbUserGroupSyncLogDo

	ALL        field.Asterisk
	CorpID     field.String
	GroupID    field.String
	Type       field.String
	SyncStatus field.String
	SyncInfo   field.String
	Operator   field.String
	CreatedAt  field.Time

	fieldMap map[string]field.Expr
}

func (t tbUserGroupSyncLog) Table(newTableName string) *tbUserGroupSyncLog {
	t.tbUserGroupSyncLogDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserGroupSyncLog) As(alias string) *tbUserGroupSyncLog {
	t.tbUserGroupSyncLogDo.DO = *(t.tbUserGroupSyncLogDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserGroupSyncLog) updateTableName(table string) *tbUserGroupSyncLog {
	t.ALL = field.NewAsterisk(table)
	t.CorpID = field.NewString(table, "corp_id")
	t.GroupID = field.NewString(table, "group_id")
	t.Type = field.NewString(table, "type")
	t.SyncStatus = field.NewString(table, "sync_status")
	t.SyncInfo = field.NewString(table, "sync_info")
	t.Operator = field.NewString(table, "operator")
	t.CreatedAt = field.NewTime(table, "created_at")

	t.fillFieldMap()

	return t
}

func (t *tbUserGroupSyncLog) WithContext(ctx context.Context) *tbUserGroupSyncLogDo {
	return t.tbUserGroupSyncLogDo.WithContext(ctx)
}

func (t tbUserGroupSyncLog) TableName() string { return t.tbUserGroupSyncLogDo.TableName() }

func (t tbUserGroupSyncLog) Alias() string { return t.tbUserGroupSyncLogDo.Alias() }

func (t *tbUserGroupSyncLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserGroupSyncLog) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 7)
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["type"] = t.Type
	t.fieldMap["sync_status"] = t.SyncStatus
	t.fieldMap["sync_info"] = t.SyncInfo
	t.fieldMap["operator"] = t.Operator
	t.fieldMap["created_at"] = t.CreatedAt
}

func (t tbUserGroupSyncLog) clone(db *gorm.DB) tbUserGroupSyncLog {
	t.tbUserGroupSyncLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserGroupSyncLog) replaceDB(db *gorm.DB) tbUserGroupSyncLog {
	t.tbUserGroupSyncLogDo.ReplaceDB(db)
	return t
}

type tbUserGroupSyncLogDo struct{ gen.DO }

func (t tbUserGroupSyncLogDo) Debug() *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserGroupSyncLogDo) WithContext(ctx context.Context) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserGroupSyncLogDo) ReadDB() *tbUserGroupSyncLogDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserGroupSyncLogDo) WriteDB() *tbUserGroupSyncLogDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserGroupSyncLogDo) Session(config *gorm.Session) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserGroupSyncLogDo) Clauses(conds ...clause.Expression) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserGroupSyncLogDo) Returning(value interface{}, columns ...string) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserGroupSyncLogDo) Not(conds ...gen.Condition) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserGroupSyncLogDo) Or(conds ...gen.Condition) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserGroupSyncLogDo) Select(conds ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserGroupSyncLogDo) Where(conds ...gen.Condition) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserGroupSyncLogDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserGroupSyncLogDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserGroupSyncLogDo) Order(conds ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserGroupSyncLogDo) Distinct(cols ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserGroupSyncLogDo) Omit(cols ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserGroupSyncLogDo) Join(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserGroupSyncLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserGroupSyncLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserGroupSyncLogDo) Group(cols ...field.Expr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserGroupSyncLogDo) Having(conds ...gen.Condition) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserGroupSyncLogDo) Limit(limit int) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserGroupSyncLogDo) Offset(offset int) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserGroupSyncLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserGroupSyncLogDo) Unscoped() *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserGroupSyncLogDo) Create(values ...*model.TbUserGroupSyncLog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserGroupSyncLogDo) CreateInBatches(values []*model.TbUserGroupSyncLog, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserGroupSyncLogDo) Save(values ...*model.TbUserGroupSyncLog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserGroupSyncLogDo) First() (*model.TbUserGroupSyncLog, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncLog), nil
	}
}

func (t tbUserGroupSyncLogDo) Take() (*model.TbUserGroupSyncLog, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncLog), nil
	}
}

func (t tbUserGroupSyncLogDo) Last() (*model.TbUserGroupSyncLog, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncLog), nil
	}
}

func (t tbUserGroupSyncLogDo) Find() ([]*model.TbUserGroupSyncLog, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserGroupSyncLog), err
}

func (t tbUserGroupSyncLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserGroupSyncLog, err error) {
	buf := make([]*model.TbUserGroupSyncLog, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserGroupSyncLogDo) FindInBatches(result *[]*model.TbUserGroupSyncLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserGroupSyncLogDo) Attrs(attrs ...field.AssignExpr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserGroupSyncLogDo) Assign(attrs ...field.AssignExpr) *tbUserGroupSyncLogDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserGroupSyncLogDo) Joins(fields ...field.RelationField) *tbUserGroupSyncLogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserGroupSyncLogDo) Preload(fields ...field.RelationField) *tbUserGroupSyncLogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserGroupSyncLogDo) FirstOrInit() (*model.TbUserGroupSyncLog, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncLog), nil
	}
}

func (t tbUserGroupSyncLogDo) FirstOrCreate() (*model.TbUserGroupSyncLog, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSyncLog), nil
	}
}

func (t tbUserGroupSyncLogDo) FindByPage(offset int, limit int) (result []*model.TbUserGroupSyncLog, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserGroupSyncLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserGroupSyncLogDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserGroupSyncLogDo) Delete(models ...*model.TbUserGroupSyncLog) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserGroupSyncLogDo) withDO(do gen.Dao) *tbUserGroupSyncLogDo {
	t.DO = *do.(*gen.DO)
	return t
}
