package alert_rule

import (
	"asdsec.com/asec/platform/app/console/app/alert_rule/api"
	"github.com/gin-gonic/gin"
)

func AlertRuleApi(r *gin.RouterGroup) {
	v := r.Group("/v1/alert/rule")
	{
		v.POST("/list", api.GetAlertRuleList)
		v.POST("", api.Create)
		v.PUT("", api.Update)
		v.DELETE("", api.Delete)
		v.POST("template", api.CreateTemplate)
		v.GET("template/list", api.GetAlertRuleTemplateList)
	}
}
