package dto

import (
	"time"
)

// OAuth2Client OAuth2客户端信息
type OAuth2Client struct {
	ID           int64     `json:"id" gorm:"primaryKey"`
	CorpID       int64     `json:"corp_id" gorm:"not null"`
	AppID        *int64    `json:"app_id" gorm:"index"`
	ClientID     string    `json:"client_id" gorm:"unique;not null;size:64"`
	ClientSecret string    `json:"client_secret" gorm:"not null;size:128"`
	Name         string    `json:"name" gorm:"not null;size:128"`
	RedirectURI  string    `json:"redirect_uri" gorm:"not null;size:255"`
	Scope        string    `json:"scope" gorm:"size:255"`
	Status       int16     `json:"status" gorm:"default:1"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (OAuth2Client) TableName() string {
	return "tb_oauth2_clients"
}

// OIDCAuthorizationCode OIDC授权码
type OIDCAuthorizationCode struct {
	ID                  int64     `json:"id" gorm:"primaryKey"`
	Code                string    `json:"code" gorm:"unique;not null;size:128"`
	ClientID            string    `json:"client_id" gorm:"not null;size:255"`
	UserID              string    `json:"user_id" gorm:"not null;size:255"`
	RedirectURI         string    `json:"redirect_uri" gorm:"not null;size:2048"`
	Scope               string    `json:"scope" gorm:"size:1024"`
	State               string    `json:"state" gorm:"size:1024"`
	Nonce               string    `json:"nonce" gorm:"size:255"`
	CodeChallenge       string    `json:"code_challenge" gorm:"size:255"`
	CodeChallengeMethod string    `json:"code_challenge_method" gorm:"size:10"`
	ExpiresIn           int       `json:"expires_in" gorm:"default:600"`
	ExpiresAt           time.Time `json:"expires_at" gorm:"not null"`
	CreatedAt           time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt           time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (OIDCAuthorizationCode) TableName() string {
	return "tb_oidc_authorization_codes"
}

// IsExpired 检查授权码是否过期
func (code *OIDCAuthorizationCode) IsExpired() bool {
	return time.Now().After(code.ExpiresAt)
}

// OIDCAccessToken OIDC访问令牌
type OIDCAccessToken struct {
	ID        int64     `json:"id" gorm:"primaryKey"`
	TokenID   string    `json:"token_id" gorm:"unique;not null;size:128"`
	ClientID  string    `json:"client_id" gorm:"not null;size:255"`
	UserID    string    `json:"user_id" gorm:"not null;size:255"`
	Token     string    `json:"token" gorm:"not null"`
	TokenType string    `json:"token_type" gorm:"default:'Bearer';size:50"`
	Scope     string    `json:"scope" gorm:"size:1024"`
	ExpiresIn int       `json:"expires_in" gorm:"default:3600"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (OIDCAccessToken) TableName() string {
	return "tb_oidc_access_tokens"
}

// IsExpired 检查访问令牌是否过期
func (token *OIDCAccessToken) IsExpired() bool {
	return time.Now().After(token.ExpiresAt)
}

// OIDCRefreshToken OIDC刷新令牌
type OIDCRefreshToken struct {
	ID            int64     `json:"id" gorm:"primaryKey"`
	TokenID       string    `json:"token_id" gorm:"unique;not null;size:128"`
	ClientID      string    `json:"client_id" gorm:"not null;size:255"`
	UserID        string    `json:"user_id" gorm:"not null;size:255"`
	Token         string    `json:"token" gorm:"not null"`
	AccessTokenID string    `json:"access_token_id" gorm:"size:128"`
	ExpiresIn     int       `json:"expires_in" gorm:"default:2592000"` // 30天
	ExpiresAt     time.Time `json:"expires_at" gorm:"not null"`
	CreatedAt     time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (OIDCRefreshToken) TableName() string {
	return "tb_oidc_refresh_tokens"
}

// IsExpired 检查刷新令牌是否过期
func (token *OIDCRefreshToken) IsExpired() bool {
	return time.Now().After(token.ExpiresAt)
}

// OIDCClaims OIDC ID令牌的标准声明
type OIDCClaims struct {
	Sub                 string                 `json:"sub"`                             // 用户唯一标识
	Aud                 string                 `json:"aud"`                             // 客户端ID
	Iss                 string                 `json:"iss"`                             // 发行者
	Exp                 int64                  `json:"exp"`                             // 过期时间
	Iat                 int64                  `json:"iat"`                             // 签发时间
	AuthTime            int64                  `json:"auth_time"`                       // 认证时间
	Nonce               string                 `json:"nonce,omitempty"`                 // 随机数
	Name                string                 `json:"name,omitempty"`                  // 用户姓名
	Nickname            string                 `json:"nickname,omitempty"`              // 昵称
	PreferredUsername   string                 `json:"preferred_username,omitempty"`    // 首选用户名
	Picture             string                 `json:"picture,omitempty"`               // 头像URL
	Email               string                 `json:"email,omitempty"`                 // 邮箱
	EmailVerified       bool                   `json:"email_verified,omitempty"`        // 邮箱验证状态
	PhoneNumber         string                 `json:"phone_number,omitempty"`          // 电话号码
	PhoneNumberVerified bool                   `json:"phone_number_verified,omitempty"` // 电话验证状态
	Address             map[string]interface{} `json:"address,omitempty"`               // 地址信息（存放扩展字段）
}

// OIDCDiscoveryResponse OIDC发现端点响应
type OIDCDiscoveryResponse struct {
	Issuer                           string   `json:"issuer"`
	AuthorizationEndpoint            string   `json:"authorization_endpoint"`
	TokenEndpoint                    string   `json:"token_endpoint"`
	UserinfoEndpoint                 string   `json:"userinfo_endpoint"`
	JwksURI                          string   `json:"jwks_uri"`
	RevocationEndpoint               string   `json:"revocation_endpoint"`
	EndSessionEndpoint               string   `json:"end_session_endpoint"`
	ScopesSupported                  []string `json:"scopes_supported"`
	ResponseTypesSupported           []string `json:"response_types_supported"`
	GrantTypesSupported              []string `json:"grant_types_supported"`
	SubjectTypesSupported            []string `json:"subject_types_supported"`
	IDTokenSigningAlgValuesSupported []string `json:"id_token_signing_alg_values_supported"`
	CodeChallengeMethodsSupported    []string `json:"code_challenge_methods_supported"`
}

// JWK JSON Web Key
type JWK struct {
	Kty string `json:"kty"` // 密钥类型
	Use string `json:"use"` // 密钥用途
	Kid string `json:"kid"` // 密钥ID
	Alg string `json:"alg"` // 算法
	N   string `json:"n"`   // RSA模数
	E   string `json:"e"`   // RSA公钥指数
}

// JWKSet JSON Web Key Set
type JWKSet struct {
	Keys []JWK `json:"keys"`
}

// OIDCErrorResponse OIDC错误响应
type OIDCErrorResponse struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description,omitempty"`
	ErrorURI         string `json:"error_uri,omitempty"`
}

// OIDC标准错误类型
const (
	// 授权端点错误
	ErrInvalidRequest          = "invalid_request"
	ErrUnauthorizedClient      = "unauthorized_client"
	ErrAccessDenied            = "access_denied"
	ErrUnsupportedResponseType = "unsupported_response_type"
	ErrInvalidScope            = "invalid_scope"
	ErrServerError             = "server_error"
	ErrTemporarilyUnavailable  = "temporarily_unavailable"

	// 令牌端点错误
	ErrInvalidClient        = "invalid_client"
	ErrInvalidGrant         = "invalid_grant"
	ErrUnsupportedGrantType = "unsupported_grant_type"
)

// OIDC标准scope
const (
	ScopeOpenID  = "openid"
	ScopeProfile = "profile"
	ScopeEmail   = "email"
	ScopePhone   = "phone"
	ScopeAddress = "address"
)

// OIDC授权码流程常量
const (
	ResponseTypeCode    = "code"
	GrantTypeAuthCode   = "authorization_code"
	GrantTypeRefresh    = "refresh_token"
	TokenTypeBearer     = "Bearer"
	CodeChallengeMethod = "S256"
)
