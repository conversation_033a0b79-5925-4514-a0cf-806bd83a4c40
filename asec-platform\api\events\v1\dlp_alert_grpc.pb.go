// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: events/v1/dlp_alert.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DlpAlertReport_CreateDlpLog_FullMethodName = "/api.asdsec.file_event.DlpAlertReport/CreateDlpLog"
)

// DlpAlertReportClient is the client API for DlpAlertReport service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DlpAlertReportClient interface {
	CreateDlpLog(ctx context.Context, opts ...grpc.CallOption) (DlpAlertReport_CreateDlpLogClient, error)
}

type dlpAlertReportClient struct {
	cc grpc.ClientConnInterface
}

func NewDlpAlertReportClient(cc grpc.ClientConnInterface) DlpAlertReportClient {
	return &dlpAlertReportClient{cc}
}

func (c *dlpAlertReportClient) CreateDlpLog(ctx context.Context, opts ...grpc.CallOption) (DlpAlertReport_CreateDlpLogClient, error) {
	stream, err := c.cc.NewStream(ctx, &DlpAlertReport_ServiceDesc.Streams[0], DlpAlertReport_CreateDlpLog_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &dlpAlertReportCreateDlpLogClient{stream}
	return x, nil
}

type DlpAlertReport_CreateDlpLogClient interface {
	Send(*DlpAlertReq) error
	CloseAndRecv() (*Reply, error)
	grpc.ClientStream
}

type dlpAlertReportCreateDlpLogClient struct {
	grpc.ClientStream
}

func (x *dlpAlertReportCreateDlpLogClient) Send(m *DlpAlertReq) error {
	return x.ClientStream.SendMsg(m)
}

func (x *dlpAlertReportCreateDlpLogClient) CloseAndRecv() (*Reply, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(Reply)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// DlpAlertReportServer is the server API for DlpAlertReport service.
// All implementations must embed UnimplementedDlpAlertReportServer
// for forward compatibility
type DlpAlertReportServer interface {
	CreateDlpLog(DlpAlertReport_CreateDlpLogServer) error
	mustEmbedUnimplementedDlpAlertReportServer()
}

// UnimplementedDlpAlertReportServer must be embedded to have forward compatible implementations.
type UnimplementedDlpAlertReportServer struct {
}

func (UnimplementedDlpAlertReportServer) CreateDlpLog(DlpAlertReport_CreateDlpLogServer) error {
	return status.Errorf(codes.Unimplemented, "method CreateDlpLog not implemented")
}
func (UnimplementedDlpAlertReportServer) mustEmbedUnimplementedDlpAlertReportServer() {}

// UnsafeDlpAlertReportServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DlpAlertReportServer will
// result in compilation errors.
type UnsafeDlpAlertReportServer interface {
	mustEmbedUnimplementedDlpAlertReportServer()
}

func RegisterDlpAlertReportServer(s grpc.ServiceRegistrar, srv DlpAlertReportServer) {
	s.RegisterService(&DlpAlertReport_ServiceDesc, srv)
}

func _DlpAlertReport_CreateDlpLog_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(DlpAlertReportServer).CreateDlpLog(&dlpAlertReportCreateDlpLogServer{stream})
}

type DlpAlertReport_CreateDlpLogServer interface {
	SendAndClose(*Reply) error
	Recv() (*DlpAlertReq, error)
	grpc.ServerStream
}

type dlpAlertReportCreateDlpLogServer struct {
	grpc.ServerStream
}

func (x *dlpAlertReportCreateDlpLogServer) SendAndClose(m *Reply) error {
	return x.ServerStream.SendMsg(m)
}

func (x *dlpAlertReportCreateDlpLogServer) Recv() (*DlpAlertReq, error) {
	m := new(DlpAlertReq)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// DlpAlertReport_ServiceDesc is the grpc.ServiceDesc for DlpAlertReport service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DlpAlertReport_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.asdsec.file_event.DlpAlertReport",
	HandlerType: (*DlpAlertReportServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CreateDlpLog",
			Handler:       _DlpAlertReport_CreateDlpLog_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "events/v1/dlp_alert.proto",
}
