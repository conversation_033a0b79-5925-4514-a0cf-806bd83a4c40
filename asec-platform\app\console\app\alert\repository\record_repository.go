package repository

import (
	alertComm "asdsec.com/asec/platform/app/console/app/alert/common"
	"asdsec.com/asec/platform/app/console/app/alert/model"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"time"
)

type recordRepository struct {
}

func (r recordRepository) AddRecord(ctx context.Context, record *model.AlertRecord) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	conn := db.Table(alertComm.AlertRecordTable)
	return conn.Create(record).Error
}

func (r recordRepository) DeleteRecord(ctx context.Context, beforeTime time.Time) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	conn := db.Table(alertComm.AlertRecordTable)
	err = conn.Where("create_time < ?", beforeTime).Delete(model.AlertRecord{}).Error
	return err
}

func NewRecordRepository() RecordRepository {
	return &recordRepository{}
}

type RecordRepository interface {
	AddRecord(ctx context.Context, record *model.AlertRecord) error
	DeleteRecord(ctx context.Context, beforeTime time.Time) error
}
