// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbExternalUser = "tb_external_user"

// TbExternalUser mapped from table <tb_external_user>
type TbExternalUser struct {
	LocalRootGroupID string    `gorm:"column:local_root_group_id;not null" json:"local_root_group_id"`
	LocalUserID      string    `gorm:"column:local_user_id;not null" json:"local_user_id"`
	MainDepartment   string    `gorm:"column:main_department;not null" json:"main_department"`
	Userid           string    `gorm:"column:userid;not null" json:"userid"`
	Name             string    `gorm:"column:name;not null" json:"name"`
	NickName         string    `gorm:"column:nick_name" json:"nick_name"`
	Email            string    `gorm:"column:email" json:"email"`
	Mobile           string    `gorm:"column:mobile" json:"mobile"`
	Status           bool      `gorm:"column:status" json:"status"`
	CreatedAt        time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	Type             string    `gorm:"column:type" json:"type"`
	UniqKey          string    `gorm:"column:uniq_key" json:"uniq_key"`
	DisplayName      string    `gorm:"column:display_name" json:"display_name"`
	TrueName         string    `gorm:"column:true_name" json:"true_name"`
}

// TableName TbExternalUser's table name
func (*TbExternalUser) TableName() string {
	return TableNameTbExternalUser
}
