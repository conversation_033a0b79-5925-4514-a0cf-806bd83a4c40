package vo

type WebInfo struct {
	// web 来源地址
	UrlAddr string `json:"url_addr"`
	// web 来源端口
	UrlPort string `json:"url_port"`
	// web 来源路径
	UrlRoute string `json:"url_route"`
}

type SoftwareInfo struct {
	// 软件 名称
	SoftwareName string `json:"software_name"`
	// 软件 包含进程
	ProcessName string `json:"process_name"`
}

type CreateSourceReq struct {
	// 来源名称
	SourceName string `json:"source_name" binding:"required"`
	// 来源类型(枚举web/git/software)
	SourceType string `json:"source_type" binding:"required"`
	// git 来源url
	GitUrl string `json:"git_url"`
	// web 来源信息
	WebInfos []WebInfo `json:"web_infos"`
	// 软件 来源信息
	SoftwareInfos []SoftwareInfo `json:"software_infos"`
	// 软件 包含目录
	IncludeFilePath []string `json:"include_file_path"`
	// 软件 排除目录
	ExcludeFilePath []string `json:"exclude_file_path"`
	// 状态(1-启用/2-禁用)
	Status int `json:"status"`
}

type UpdateSourceReq struct {
	// id
	Id string `json:"id" binding:"required"`
	CreateSourceReq
}

type DelSourceReq struct {
	// ids
	Id   []string `json:"id" binding:"required"`
	Name []string `json:"name"`
}

type SourceListReq struct {
	Limit  int    `json:"limit" binding:"required,min=1,max=1000"`
	Offset int    `json:"offset" binding:"min=0"`
	Search string `json:"search"`
}

type SourceListResp struct {
	CurrentPage    int              `json:"current_page"`
	PageSize       int              `json:"page_size"`
	TotalNum       int              `json:"total_num"`
	SourceListData []SourceListData `json:"source_list_data"`
}

type SourceListData struct {
	// id
	Id string `json:"id"`
	// 来源名称
	SourceName string `json:"source_name"`
	// 来源类型(枚举web/git/software)
	SourceType string `json:"source_type"`
	// 来源信息
	SourceInfo []string `json:"source_info"`
	// 状态(1-启用/2-禁用)
	Status int `json:"status"`
}

type DetailSourceResp struct {
	// id
	Id string `json:"id"`
	CreateSourceReq
}

type SourceListQuote struct {
	// id
	Id string `json:"id"`
	// 来源名称
	SourceName string `json:"source_name"`
	// 来源类型(枚举web/git/software)
	SourceType string `json:"source_type"`
}
