syntax = "proto3";

package api.appliance;
import public "google/protobuf/timestamp.proto";
import "google/api/annotations.proto";
option go_package = "asdsec.com/asec/platform/api/appliance/v1;v1";

service ApplianceMgt {
  // appliance首次安装注册，返回applianceID
  rpc Enroll(ApplianceEnrollReq) returns (ApplianceEnrollRes);
  rpc Heartbeat(stream HeartbeatReq) returns (HeartbeatRes);
  rpc GetConfig(GetConfigReq)returns(Configs);

  // http接口
  rpc SetUpgradeStatus(SetUpgradeStatusReq) returns (SetUpgradeStatusRes){// todo 后续将console中的 SetAgentUpgradeStatus 接口迁移过来
    option (google.api.http) = {
      post: "/appliance/v1/agent/upgrade_status",
      body: "*"
    };
  }
  // 任务汇报接口
  rpc TaskReport(TaskReportReq) returns (TaskReportResp){
    option (google.api.http) = {
      post: "/appliance/v1/task/report",
      body: "*"
    };
  }

  // 任务汇报接口
  rpc HeartbeatReport(HeartbeatReportReq) returns (HeartbeatReportRes){
    option (google.api.http) = {
      post: "/appliance/v1/heartbeat",
      body: "*"
    };
  }
}

enum ApplianceType
{
  //客户端
  AGENT = 0;
  //安全边缘代理
  SECURITY_EDGE = 1;
  //连接器
  CONNECTOR = 2;
  //网关
  GATEWAY = 3;
  // 日志服务器
  LOGSERVER = 4;
  // 认证服务器
  AUTHSERVER = 5;
}

message ApplianceEnrollReq {
  ApplianceType type = 1;
  // agent版本
  string version = 2;
  // agent操作系统平台类型
  string plat = 3;
  // agent mac地址，考虑多网卡情况
  repeated string mac = 4;
  string uuid = 5;
  string cpuid = 6;
  repeated string agent_ip = 7; //把ip信息也带上来
  string login_user = 8;
  string appliance_name = 9;
  string first_mac = 10;
  string se_ip = 11;
  int32 se_port = 12;
  uint64 appliance_id = 13; //connector等使用命令安装的设备，会从安装命令获取平台预先生成的ID
  string private_ip = 14; //内网地址
  string public_ip = 15; //公网地址
}

message ApplianceEnrollRes{
  ApplianceType type = 1;
  uint64 appliance_id = 2;
  string public_key = 3;
  EnrollErrorCode error_code = 4;
  string error_msg = 5;
}

enum EnrollErrorCode{
  SUCCEED = 0;
  //新客户端接入,授权不足
  LicenseReachLimit = -1;
  LicenseQueryFailed = -2;
  //授权不足并且客户端存在ID但是平台已经删除,这个情况下通知客户端清除ID缓存
  AgentDeletedAndLicenseReachLimit = -3;
  DBOperateErr = -4;
}

message HeartbeatReq{
  ApplianceType type = 1;
  uint64 appliance_id = 2;
  //进程的状态监控，因为agent可能有多个进程，这里使用数组
  repeated ProcStat procsStat = 3;
  string sys_cpu = 4;
  string sys_mem = 5;
  string kernel_version = 6;
  string arch = 7;
  string platform = 8;
  string platform_family = 9;
  string platform_version = 10;
  string version = 11; //客户端版本
  string upgrade_time = 12; //客户端更新时间
  string user_id = 13;
}

//进程状态监控，用于定位运行状态，性能等问题
message ProcStat{
  int32 pid = 1;
  string cpu = 2;
  string rss = 3;
  string read_speed = 4;
  string write_speed = 5;
  int32 fd_cnt = 6;
  int64 start_at = 7;
  string tx_tps = 8;
  string rx_tps = 9;
  //进程目录占用空间
  string du = 10;
  //进程名称
  string proc_name = 11;
}

enum CommandType
{
  // none
  NONE = 0;
  // 执行命令
  CMD = 1;
  UNINSTALL = 2;
  ENROLL = 3;
}

message HeartbeatReportReq{
  ApplianceType type = 1;
  uint64 appliance_id = 2;
  string version = 3; //客户端版本
  string upgrade_time = 4; //客户端更新时间
  string user_id = 5;
  // 新增字段
  uint32 service_status = 6; //服务状态 0x1 - 服务异常
  // 终端类型
  string platform = 7;
  string user_name = 8;
  int32 user_type = 9;
  string source_process = 10;
  bool flush = 11; //是否立即刷新数据库信息
}
// 逃生指令通过心跳信息返回
message HeartbeatReportRes{
  // 下发给设备的指令，比如重启，升级，停止等
  int32 command = 1;
  // 命令执行参数
  string args = 2;
  // 新增字段
  CommandType command_type = 3; //心跳返回命令类型，见枚举
  TaskInfo task_info = 4;
}
message TaskInfo {
  string task_id = 1; // 任务id
  string task_cmd = 2; // 任务执行命令
  uint32 time_out = 3; // 任务执行超时时间，单位/秒， 如5分钟超时则是300
}

// 逃生指令通过心跳信息返回
message HeartbeatRes{
  // 下发给设备的指令，比如重启，升级，停止等
  int32 command = 1;
  // 命令执行参数
  string args = 2;
}

message GetConfigReq{
  ApplianceType type = 1;
  uint64 appliance_id = 2;
  uint64 corp_id = 3;
  uint64 os_type = 4;
  uint64 agent_type = 5; //网关,终端
  repeated CfgSurvey cfgItems = 6;
}

message CfgSurvey {
  string config_type = 3;
  string config_version = 6;
}

message Configs{
  ApplianceType type = 1;
  uint64 appliance_id = 2;
  uint64 corp_id = 3;
  uint64 os_type = 4;
  uint64 agent_type = 5;
  repeated CfgResult dataItems = 6;
}

message CfgResult{
  string config_name = 1;
  string config_type = 2;
  bytes config_data = 3;
  string config_version = 4;
}

message SetUpgradeStatusReq{
  string last_version = 1;
  string next_version = 2;
  string appliance_id = 3;
  string platform = 4;
  string status = 5;
  string failed_reason = 6;
}

message SetUpgradeStatusRes{
  enum StatusCode {
    SUCCESS = 0;
  }
  StatusCode status = 1;
}
enum StatusCode {
  SUCCESS = 0;
  FAIL = -1;
}
message TaskReportReq {
  string task_id = 1; // 任务id
  uint64 task_start_time = 2; // 任务开始时间，客户端可能存在任务排队情况
  uint64 task_end_time = 3; // 任务结束时间，客户端可能存在任务排队情况
  string task_result = 4; // 任务返回结果，成功返回输出，失败返回异常，超时可不返回
  string task_status = 5; // 任务状态 success-成功，fail-失败，timeout-超时
}
message TaskReportResp {
  int32 code = 1; // 汇报结果 0 - 汇报成功 -1 - 汇报失败
  string msg = 2; // 成功 - 无内容，失败 - 失败原因
}
