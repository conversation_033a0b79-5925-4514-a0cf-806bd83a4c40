package repository

import (
	"asdsec.com/asec/platform/app/console/app/alarm_rule/common"
	"asdsec.com/asec/platform/app/console/app/alarm_rule/model"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"strconv"
	"time"
)

type channelRepositoryPriv struct {
}

func (c *channelRepositoryPriv) GetChannelName(ctx context.Context) (map[string]string, error) {
	var data []model.ChannelTypeDB
	res := make(map[string]string, 0)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return res, err
	}
	err = db.Model(&model.ChannelTypeDB{}).Find(&data).Error
	if err != nil {
		return res, err
	}
	for _, v := range data {
		res[v.Channel] = v.ChannelName
	}
	return res, nil
}

type ChannelRepositoryImpl interface {
	GetChannelType(ctx context.Context, startT time.Time, endTime time.Time) ([]model.ChannelTypeDB, error)
	GetChannelName(ctx context.Context) (map[string]string, error)
	GetSubActivityType(ctx context.Context, pid uint64, startT time.Time, endT time.Time) []model.ChannelTypeDB
}

func NewChannelRepository() ChannelRepositoryImpl {
	return &channelRepositoryPriv{}
}

type ChannelCountStrut struct {
	Channel string `gorm:"column:channel" json:"channel"`
	Count   int64  `json:"count"`
}

func (c *channelRepositoryPriv) GetChannelCount(ctx context.Context, startT time.Time, endT time.Time) (map[string]int64, error) {
	channelMap := make(map[string]int64, 0)
	var channelCount []ChannelCountStrut
	ckDB, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}
	var alertEvent []model.AlertEventDB
	err = common.GetFeildCountByDB(ckDB, &alertEvent, "channel", &channelCount, startT, endT)
	if err != nil {
		global.SysLog.Error(err.Error())
		return nil, err
	}
	for _, v := range channelCount {
		channelMap[v.Channel] = v.Count
	}
	return channelMap, nil
}

func (c *channelRepositoryPriv) GetChannelType(ctx context.Context, startT time.Time, endTime time.Time) ([]model.ChannelTypeDB, error) {
	var data []model.ChannelTypeDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return data, err
	}
	err = db.Model(&model.ChannelTypeDB{}).Where("pid = '0'").Find(&data).Error
	if err != nil {
		global.SysLog.Error(err.Error())
		return data, err
	}
	channelMap, err := c.GetChannelCount(ctx, startT, endTime)
	if err != nil {
		return data, err
	}
	var res []model.ChannelTypeDB
	for k, v := range data {
		children := c.GetSubActivityType(ctx, v.Id, startT, endTime)
		data[k].Children = children
		data[k].Count = channelMap[v.Channel]
		for _, cc := range children {
			data[k].Count += cc.Count
		}
		if data[k].Count > 0 {
			res = append(res, data[k])
		}
	}
	return res, nil
}

func (c *channelRepositoryPriv) GetSubActivityType(ctx context.Context, pid uint64, startT, endT time.Time) []model.ChannelTypeDB {
	var tmp []model.ChannelTypeDB
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error(err.Error())
		return tmp
	}
	err = db.Model(&model.ChannelTypeDB{}).Where("pid = ?", strconv.FormatUint(pid, 10)).Find(&tmp).Error
	if err != nil {
		global.SysLog.Error(err.Error())
		return tmp
	}
	var res []model.ChannelTypeDB
	if len(tmp) > 0 {
		channelMap, err := c.GetChannelCount(ctx, startT, endT)
		if err != nil {
			return tmp
		}
		for k, v := range tmp {
			tmp[k].Children = c.GetSubActivityType(ctx, v.Id, startT, endT)
			tmp[k].Count = channelMap[v.Channel]
			if tmp[k].Count > 0 {
				res = append(res, tmp[k])
			}
		}
	}
	return res
}
