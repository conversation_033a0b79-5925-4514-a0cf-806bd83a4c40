package common

import "testing"

// Test GetAllSubNode

func TestGetAllSubNodeWithTreeOfDepthTwo(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
		"D": {},
		"E": {},
		"F": {},
		"G": {},
	}
	root := "A"

	expected := []string{"A", "B", "D", "E", "C", "F", "G"}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetAllSubNode(root)

	assertSliceEqual(t, expected, actual)
}

func TestGetAllSubNodeWithSubTree(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
		"D": {},
		"E": {},
		"F": {},
		"G": {},
	}
	root := "B"

	expected := []string{"B", "D", "E"}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetAllSubNode(root)

	assertSliceEqual(t, expected, actual)
}

func TestGetAllSubNodeWithSingleRootNode(t *testing.T) {
	tree := map[string][]string{
		"A": {},
	}
	root := "A"

	expected := []string{"A"}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetAllSubNode(root)

	assertSliceEqual(t, expected, actual)
}

func TestGetAllSubNodeWithNonexistentRoot(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
		"D": {},
		"E": {},
		"F": {},
		"G": {},
	}
	root := "Z"

	expected := []string{}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetAllSubNode(root)

	assertSliceEqual(t, expected, actual)
}

func TestGetAllSubNodeWithNodeHavingNoChildren(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {},
		"C": {"D"},
		"D": {},
	}
	root := "A"

	expected := []string{"A", "B", "C", "D"}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetAllSubNode(root)

	assertSliceEqual(t, expected, actual)
}

func TestGetAllSubNodeWithUncoveredNode(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
		"D": {},
		"E": {},
		"F": {},
		"G": {},
	}
	root := "X"

	expected := []string{}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetAllSubNode(root)

	assertSliceEqual(t, expected, actual)
}

// Test GetRootToNodePath

func TestGetRootToNodeWithTreeOfDepthTwo(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
	}
	root := "A"
	node := "G"

	expected := []string{"A", "C", "G"}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetRootToNodePath(root, node)

	assertSliceEqual(t, expected, actual)
}

func TestGetRootToNodeWithSingleNode(t *testing.T) {
	tree := map[string][]string{
		"A": {},
	}
	root := "A"
	node := "A"

	expected := []string{"A"}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetRootToNodePath(root, node)

	assertSliceEqual(t, expected, actual)
}

func TestGetRootToNodeWithNonexistentNode(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
	}
	root := "A"
	node := "Z"

	expected := []string{}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetRootToNodePath(root, node)

	assertSliceEqual(t, expected, actual)
}

func TestGetRootToNodeWithNodeNotInSubtree(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
	}
	root := "B"
	node := "G"

	expected := []string{}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetRootToNodePath(root, node)

	assertSliceEqual(t, expected, actual)
}

func TestGetRootToNodeWithDisjointTree(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"D": {"E", "F"},
		"G": {"H", "I", "J"},
	}
	root := "A"
	node := "I"

	expected := []string{}
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.GetRootToNodePath(root, node)

	assertSliceEqual(t, expected, actual)
}

func assertSliceEqual(t *testing.T, expected, actual []string) {
	t.Helper()

	if len(actual) != len(expected) {
		t.Errorf("Expected length %d but got %d", len(expected), len(actual))
	}

	for i, val := range expected {
		if val != actual[i] {
			t.Errorf("Expected %v but got %v at index %d", expected, actual, i)
		}
	}
}

func TestCircleCheckWithCircularTree(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E", "A"},
		"C": {"F", "G"},
	}
	root := "A"

	expected := true
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.CircleCheck(root)

	assertBoolEqual(t, expected, actual)
}

func TestCircleCheckWithTreeWithoutCycles(t *testing.T) {
	tree := map[string][]string{
		"A": {"B", "C"},
		"B": {"D", "E"},
		"C": {"F", "G"},
	}
	root := "A"

	expected := false
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.CircleCheck(root)

	assertBoolEqual(t, expected, actual)
}

func TestCircleCheckWithSingleNode(t *testing.T) {
	tree := map[string][]string{
		"A": {},
	}
	root := "A"

	expected := false
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.CircleCheck(root)

	assertBoolEqual(t, expected, actual)
}

func TestCircleCheckWithEmptyTree(t *testing.T) {
	tree := map[string][]string{}
	root := ""

	expected := false
	treeHandler := RootToNodeSimpleTree{
		tree: tree,
	}
	actual := treeHandler.CircleCheck(root)

	assertBoolEqual(t, expected, actual)
}

func assertBoolEqual(t *testing.T, expected, actual bool) {
	t.Helper()

	if expected != actual {
		t.Errorf("Expected %t but got %t", expected, actual)
	}
}
