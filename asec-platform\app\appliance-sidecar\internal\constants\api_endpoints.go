package constants

import (
	"path/filepath"

	"asdsec.com/asec/platform/pkg/utils"
)

// API 端点常量定义，与 AsecPlatformInterface.hpp 保持一致
// 统一管理所有平台接口的 API 路径，方便维护和查看

const (
	// 模块开关接口
	ModuleSwitchEndpoint = "/appliance/v1/module_switch"

	// 升级检查接口
	CheckUpgradeEndpoint = "/console/v1/agents/version_check"

	// 升级状态接口
	UpgradeStatusEndpoint = "/console/v1/agent/upgrade_status"

	// 用户信息接口
	LoginUserInfoEndpoint = "/auth/user/v1/login_user"

	// 心跳接口
	HeartbeatEndpoint = "/appliance/v1/heartbeat"

	// 自动登录接口
	AutoLoginEndpoint = "/auth/login/v1/adms"

	// 用户绑定接口
	UserBindEndpoint = "/auth/login/v1/userbind"

	// 终端动作上报接口
	ActionEndpoint = "/console/v1/agents/action"
)

// InterfaceType 接口类型枚举，与 C++ 端保持一致
type InterfaceType int

const (
	ModuleSwitch InterfaceType = iota
	CheckUpgrade
	UpgradeStatus
	LoginUserface
	HeartBeat
	AutoLogin
	UserBind
	Action
	Invalid
)

// GetEndpointByType 根据接口类型获取对应的端点路径
func GetEndpointByType(interfaceType InterfaceType) string {
	switch interfaceType {
	case ModuleSwitch:
		return ModuleSwitchEndpoint
	case CheckUpgrade:
		return CheckUpgradeEndpoint
	case UpgradeStatus:
		return UpgradeStatusEndpoint
	case LoginUserface:
		return LoginUserInfoEndpoint
	case HeartBeat:
		return HeartbeatEndpoint
	case AutoLogin:
		return AutoLoginEndpoint
	case UserBind:
		return UserBindEndpoint
	case Action:
		return ActionEndpoint
	default:
		return ""
	}
}

// GetInterfaceTypeName 获取接口类型的名称，用于日志记录
func GetInterfaceTypeName(interfaceType InterfaceType) string {
	switch interfaceType {
	case ModuleSwitch:
		return "ModuleSwitch"
	case CheckUpgrade:
		return "CheckUpgrade"
	case UpgradeStatus:
		return "UpgradeStatus"
	case LoginUserface:
		return "LoginUserface"
	case HeartBeat:
		return "HeartBeat"
	case AutoLogin:
		return "AutoLogin"
	case UserBind:
		return "UserBind"
	case Action:
		return "Action"
	default:
		return "Invalid"
	}
}

// 配置文件路径常量定义
// 统一管理 Sidecar 拉取的配置文件路径，方便维护和查看
// 只定义当前需要文件缓存的配置

const (
	// 模块开关配置文件名
	ModuleSwitchConfigFile = "module_switch.json"

	// 应用配置文件名（参考 app_config_sync）
	AppConfigFile = "app_config.json"

	// 升级查询结果文件名
	UpgradeCheckConfigFile = "upgrade_check.json"

	// 升级下载请求文件名
	UpgradeDownloadRequestFile = "upgrade_download_request.json"

	// 升级下载状态文件名
	UpgradeDownloadStatusFile = "upgrade_download_status.json"

	// 下载请求文件名
	DownloadRequestFile = "download_request.json"
	// 下载响应文件名
	DownloadResponseFile = "download_response.json"

	// 升级锁文件名 - 确保升级流程串行化
	UpgradeLockFile = "upgrade_lock.json"
)

// GetConfigFilePath 获取指定配置文件的完整路径
// 使用config子目录，与LightSentinel监控路径保持一致：C:\ProgramData\Asec\config\
func GetConfigFilePath(filename string) string {
	return filepath.Join(utils.GetConfigDir(), "config", filename)
}

// GetModuleSwitchConfigPath 获取模块开关配置文件路径
func GetModuleSwitchConfigPath() string {
	return GetConfigFilePath(ModuleSwitchConfigFile)
}

// GetAppConfigPath 获取应用配置文件路径
func GetAppConfigPath() string {
	return GetConfigFilePath(AppConfigFile)
}

// GetUpgradeCheckConfigPath 获取升级查询结果文件路径
func GetUpgradeCheckConfigPath() string {
	return GetConfigFilePath(UpgradeCheckConfigFile)
}

// GetUpgradeDownloadRequestPath 获取升级下载请求文件路径
func GetUpgradeDownloadRequestPath() string {
	return GetConfigFilePath(UpgradeDownloadRequestFile)
}

// GetUpgradeDownloadStatusPath 获取升级下载状态文件路径
func GetUpgradeDownloadStatusPath() string {
	return GetConfigFilePath(UpgradeDownloadStatusFile)
}

// GetDownloadRequestPath 获取下载请求文件路径
func GetDownloadRequestPath() string {
	return GetConfigFilePath(DownloadRequestFile)
}

// GetDownloadResponsePath 获取下载响应文件路径
func GetDownloadResponsePath() string {
	return GetConfigFilePath(DownloadResponseFile)
}

// GetUpgradeLockPath 获取升级锁文件路径
func GetUpgradeLockPath() string {
	return GetConfigFilePath(UpgradeLockFile)
}
