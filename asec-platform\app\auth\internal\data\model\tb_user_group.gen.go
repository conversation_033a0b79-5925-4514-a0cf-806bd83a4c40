// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserGroup = "tb_user_group"

// TbUserGroup mapped from table <tb_user_group>
type TbUserGroup struct {
	ID            string    `gorm:"column:id;primaryKey" json:"id"`   // 分组id
	Name          string    `gorm:"column:name;not null" json:"name"` // 分组名，在同级目录下唯一
	Description   string    `gorm:"column:description" json:"description"`
	ParentGroupID string    `gorm:"column:parent_group_id;default:'0'::character varying" json:"parent_group_id"` // 父级分组id，用于维护分组的树形结构，为0时表示最顶层
	CorpID        string    `gorm:"column:corp_id" json:"corp_id"`                                                // 租户id
	SourceID      string    `gorm:"column:source_id" json:"source_id"`                                            // 来源id
	IsDefault     bool      `gorm:"column:is_default" json:"is_default"`
	RootGroupID   string    `gorm:"column:root_group_id;not null" json:"root_group_id"`
	CreatedAt     time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	Path          string    `gorm:"column:path;default:'/'::character varying" json:"path"`
}

// TableName TbUserGroup's table name
func (*TbUserGroup) TableName() string {
	return TableNameTbUserGroup
}
