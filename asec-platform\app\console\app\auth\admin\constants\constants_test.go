package constants

import (
	"fmt"
	"github.com/dlclark/regexp2"
	"testing"
)

func Test(t *testing.T) {
	// test password match cases
	regex := regexp2.MustCompile(PasswordRegex, 0)

	testCases := []struct {
		password string
		expected bool
	}{
		{"Abc123!@#", true},
		{"123456As", true},
		{"abcdefgh", false}, // 不包含大写字母和特殊字符
		{"ABCD1234", false}, // 不包含小写字母和特殊字符
		{"Abc123!@#abcde", true},
		{"Ab1!de", false}, //长度小于8
		{"Abc123!@#abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcd", false}, // 超过 63 个字符
		{"密码123456", false},       // 包含中文字符
		{"password 123", false},   // 包含空格
		{"password＠（）123", false}, // 包含全角字符
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Testing password: %s", tc.password), func(t *testing.T) {
			result, err := regex.MatchString(tc.password)
			if result != tc.expected || err != nil {
				t.Errorf("Expected %t, but got %t for password: %s", tc.expected, result, tc.password)
			}
		})
	}
}
