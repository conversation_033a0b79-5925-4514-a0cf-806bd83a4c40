package jwt_util

import "github.com/golang-jwt/jwt/v4"

type TokenClaims struct {
	AuthTime          int    `json:"auth_time,omitempty"`
	Typ               string `json:"typ"`
	Azp               string `json:"azp,omitempty"`
	Nonce             string `json:"nonce,omitempty"`
	SessionState      string `json:"session_state,omitempty"`
	AtHash            string `json:"at_hash,omitempty"`
	Acr               string `json:"acr,omitempty"`
	Sid               string `json:"sid,omitempty"`
	SessionID         string `json:"session_id,omitempty"`
	EmailVerified     bool   `json:"email_verified,omitempty"`
	Email             string `json:"email"`
	Phone             string `json:"phone"`
	Name              string `json:"name"`
	GroupId           string `json:"group_id"`
	PreferredUsername string `json:"preferred_username"`
	Identifier        string `json:"identifier"`
	GivenName         string `json:"given_name,omitempty"`
	FamilyName        string `json:"family_name,omitempty"`
	SecurityCode      string `json:"security_code,omitempty"`
	jwt.RegisteredClaims
}
