// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: events/v1/dlp_alert.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DlpAlertReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alert []*DlpAlert `protobuf:"bytes,1,rep,name=alert,proto3" json:"alert,omitempty"`
}

func (x *DlpAlertReq) Reset() {
	*x = DlpAlertReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_events_v1_dlp_alert_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DlpAlertReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DlpAlertReq) ProtoMessage() {}

func (x *DlpAlertReq) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_dlp_alert_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DlpAlertReq.ProtoReflect.Descriptor instead.
func (*DlpAlertReq) Descriptor() ([]byte, []int) {
	return file_events_v1_dlp_alert_proto_rawDescGZIP(), []int{0}
}

func (x *DlpAlertReq) GetAlert() []*DlpAlert {
	if x != nil {
		return x.Alert
	}
	return nil
}

type DlpAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid              string   `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	CorpId            string   `protobuf:"bytes,2,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	EventType         string   `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	EventSubType      string   `protobuf:"bytes,4,opt,name=event_sub_type,json=eventSubType,proto3" json:"event_sub_type,omitempty"`
	EventSource       string   `protobuf:"bytes,5,opt,name=event_source,json=eventSource,proto3" json:"event_source,omitempty"`
	UserId            string   `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName          string   `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	AgentId           uint64   `protobuf:"varint,8,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName         string   `protobuf:"bytes,9,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	AgentIp           []string `protobuf:"bytes,10,rep,name=agent_ip,json=agentIp,proto3" json:"agent_ip,omitempty"`
	AgentMac          []string `protobuf:"bytes,11,rep,name=agent_mac,json=agentMac,proto3" json:"agent_mac,omitempty"`
	FileName          string   `protobuf:"bytes,12,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType          string   `protobuf:"bytes,13,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FilePath          string   `protobuf:"bytes,14,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	OriginalFileName  string   `protobuf:"bytes,15,opt,name=original_file_name,json=originalFileName,proto3" json:"original_file_name,omitempty"`
	OriginalFilePath  string   `protobuf:"bytes,16,opt,name=original_file_path,json=originalFilePath,proto3" json:"original_file_path,omitempty"`
	FileSize          int64    `protobuf:"varint,17,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	Owner             string   `protobuf:"bytes,18,opt,name=owner,proto3" json:"owner,omitempty"`
	FileCreateTime    int64    `protobuf:"varint,19,opt,name=file_create_time,json=fileCreateTime,proto3" json:"file_create_time,omitempty"`
	LastChangeTime    int64    `protobuf:"varint,20,opt,name=last_change_time,json=lastChangeTime,proto3" json:"last_change_time,omitempty"`
	ExtensionName     string   `protobuf:"bytes,21,opt,name=extension_name,json=extensionName,proto3" json:"extension_name,omitempty"`
	FileCategoryId    int64    `protobuf:"varint,22,opt,name=file_category_id,json=fileCategoryId,proto3" json:"file_category_id,omitempty"`
	RealExtensionName string   `protobuf:"bytes,23,opt,name=real_extension_name,json=realExtensionName,proto3" json:"real_extension_name,omitempty"`
	NameMatchInfo     string   `protobuf:"bytes,24,opt,name=name_match_info,json=nameMatchInfo,proto3" json:"name_match_info,omitempty"`
	ContentMatchInfo  string   `protobuf:"bytes,25,opt,name=content_match_info,json=contentMatchInfo,proto3" json:"content_match_info,omitempty"`
	Md5               string   `protobuf:"bytes,26,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256            string   `protobuf:"bytes,27,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Activity          string   `protobuf:"bytes,28,opt,name=activity,proto3" json:"activity,omitempty"`
	OccurTime         int64    `protobuf:"varint,29,opt,name=occur_time,json=occurTime,proto3" json:"occur_time,omitempty"`
	Channel           string   `protobuf:"bytes,30,opt,name=channel,proto3" json:"channel,omitempty"`
	ChannelType       string   `protobuf:"bytes,31,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	SoftwarePath      string   `protobuf:"bytes,32,opt,name=software_path,json=softwarePath,proto3" json:"software_path,omitempty"`
	DstPath           string   `protobuf:"bytes,33,opt,name=dst_path,json=dstPath,proto3" json:"dst_path,omitempty"`
	Score             int32    `protobuf:"varint,34,opt,name=score,proto3" json:"score,omitempty"`
	SensitiveRuleId   string   `protobuf:"bytes,35,opt,name=sensitive_rule_id,json=sensitiveRuleId,proto3" json:"sensitive_rule_id,omitempty"`
	SensitiveRuleName string   `protobuf:"bytes,36,opt,name=sensitive_rule_name,json=sensitiveRuleName,proto3" json:"sensitive_rule_name,omitempty"`
	SensitiveLevel    int32    `protobuf:"varint,37,opt,name=sensitive_level,json=sensitiveLevel,proto3" json:"sensitive_level,omitempty"`
	DataCategory      string   `protobuf:"bytes,38,opt,name=data_category,json=dataCategory,proto3" json:"data_category,omitempty"`
	Severity          Severity `protobuf:"varint,39,opt,name=severity,proto3,enum=api.asdsec.file_event.Severity" json:"severity,omitempty"`
	SeverityId        int32    `protobuf:"varint,40,opt,name=severity_id,json=severityId,proto3" json:"severity_id,omitempty"`
	PlatType          string   `protobuf:"bytes,41,opt,name=plat_type,json=platType,proto3" json:"plat_type,omitempty"`
	TraceId           []string `protobuf:"bytes,42,rep,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	CompressEncrypt   int32    `protobuf:"varint,43,opt,name=compress_encrypt,json=compressEncrypt,proto3" json:"compress_encrypt,omitempty"`
	UserTags          []string `protobuf:"bytes,44,rep,name=user_tags,json=userTags,proto3" json:"user_tags,omitempty"`
	IngestionTime     int64    `protobuf:"varint,45,opt,name=ingestion_time,json=ingestionTime,proto3" json:"ingestion_time,omitempty"`
	CreateTime        int64    `protobuf:"varint,46,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	FileProperties    []string `protobuf:"bytes,47,rep,name=file_properties,json=fileProperties,proto3" json:"file_properties,omitempty"`
	SubSrcTraceId     []string `protobuf:"bytes,48,rep,name=sub_src_trace_id,json=subSrcTraceId,proto3" json:"sub_src_trace_id,omitempty"`
	PublicIp          string   `protobuf:"bytes,49,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	ScoreReason       string   `protobuf:"bytes,50,opt,name=score_reason,json=scoreReason,proto3" json:"score_reason,omitempty"`
	SrcPath           string   `protobuf:"bytes,51,opt,name=src_path,json=srcPath,proto3" json:"src_path,omitempty"`
	SourceId          string   `protobuf:"bytes,52,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceName        string   `protobuf:"bytes,53,opt,name=source_name,json=sourceName,proto3" json:"source_name,omitempty"`
	SourceType        string   `protobuf:"bytes,54,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	SensitiveInfo     string   `protobuf:"bytes,55,opt,name=sensitive_info,json=sensitiveInfo,proto3" json:"sensitive_info,omitempty"`
	FileHideSuffix    uint32   `protobuf:"varint,56,opt,name=file_hide_suffix,json=fileHideSuffix,proto3" json:"file_hide_suffix,omitempty"`
	FileEventId       string   `protobuf:"bytes,57,opt,name=file_event_id,json=fileEventId,proto3" json:"file_event_id,omitempty"`
	PolicyId          string   `protobuf:"bytes,58,opt,name=policy_id,json=policyId,proto3" json:"policy_id,omitempty"`
	PolicyName        string   `protobuf:"bytes,59,opt,name=policy_name,json=policyName,proto3" json:"policy_name,omitempty"`
	AlertType         string   `protobuf:"bytes,60,opt,name=alert_type,json=alertType,proto3" json:"alert_type,omitempty"`
	EngineName        string   `protobuf:"bytes,61,opt,name=engine_name,json=engineName,proto3" json:"engine_name,omitempty"`
	EnableAnalysis    uint32   `protobuf:"varint,62,opt,name=enable_analysis,json=enableAnalysis,proto3" json:"enable_analysis,omitempty"`
	DisposeAction     uint32   `protobuf:"varint,63,opt,name=dispose_action,json=disposeAction,proto3" json:"dispose_action,omitempty"`
	DataCategoryName  string   `protobuf:"bytes,64,opt,name=data_category_name,json=dataCategoryName,proto3" json:"data_category_name,omitempty"`
}

func (x *DlpAlert) Reset() {
	*x = DlpAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_events_v1_dlp_alert_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DlpAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DlpAlert) ProtoMessage() {}

func (x *DlpAlert) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_dlp_alert_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DlpAlert.ProtoReflect.Descriptor instead.
func (*DlpAlert) Descriptor() ([]byte, []int) {
	return file_events_v1_dlp_alert_proto_rawDescGZIP(), []int{1}
}

func (x *DlpAlert) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *DlpAlert) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *DlpAlert) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *DlpAlert) GetEventSubType() string {
	if x != nil {
		return x.EventSubType
	}
	return ""
}

func (x *DlpAlert) GetEventSource() string {
	if x != nil {
		return x.EventSource
	}
	return ""
}

func (x *DlpAlert) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DlpAlert) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *DlpAlert) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *DlpAlert) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *DlpAlert) GetAgentIp() []string {
	if x != nil {
		return x.AgentIp
	}
	return nil
}

func (x *DlpAlert) GetAgentMac() []string {
	if x != nil {
		return x.AgentMac
	}
	return nil
}

func (x *DlpAlert) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DlpAlert) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *DlpAlert) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *DlpAlert) GetOriginalFileName() string {
	if x != nil {
		return x.OriginalFileName
	}
	return ""
}

func (x *DlpAlert) GetOriginalFilePath() string {
	if x != nil {
		return x.OriginalFilePath
	}
	return ""
}

func (x *DlpAlert) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *DlpAlert) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *DlpAlert) GetFileCreateTime() int64 {
	if x != nil {
		return x.FileCreateTime
	}
	return 0
}

func (x *DlpAlert) GetLastChangeTime() int64 {
	if x != nil {
		return x.LastChangeTime
	}
	return 0
}

func (x *DlpAlert) GetExtensionName() string {
	if x != nil {
		return x.ExtensionName
	}
	return ""
}

func (x *DlpAlert) GetFileCategoryId() int64 {
	if x != nil {
		return x.FileCategoryId
	}
	return 0
}

func (x *DlpAlert) GetRealExtensionName() string {
	if x != nil {
		return x.RealExtensionName
	}
	return ""
}

func (x *DlpAlert) GetNameMatchInfo() string {
	if x != nil {
		return x.NameMatchInfo
	}
	return ""
}

func (x *DlpAlert) GetContentMatchInfo() string {
	if x != nil {
		return x.ContentMatchInfo
	}
	return ""
}

func (x *DlpAlert) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *DlpAlert) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *DlpAlert) GetActivity() string {
	if x != nil {
		return x.Activity
	}
	return ""
}

func (x *DlpAlert) GetOccurTime() int64 {
	if x != nil {
		return x.OccurTime
	}
	return 0
}

func (x *DlpAlert) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *DlpAlert) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *DlpAlert) GetSoftwarePath() string {
	if x != nil {
		return x.SoftwarePath
	}
	return ""
}

func (x *DlpAlert) GetDstPath() string {
	if x != nil {
		return x.DstPath
	}
	return ""
}

func (x *DlpAlert) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DlpAlert) GetSensitiveRuleId() string {
	if x != nil {
		return x.SensitiveRuleId
	}
	return ""
}

func (x *DlpAlert) GetSensitiveRuleName() string {
	if x != nil {
		return x.SensitiveRuleName
	}
	return ""
}

func (x *DlpAlert) GetSensitiveLevel() int32 {
	if x != nil {
		return x.SensitiveLevel
	}
	return 0
}

func (x *DlpAlert) GetDataCategory() string {
	if x != nil {
		return x.DataCategory
	}
	return ""
}

func (x *DlpAlert) GetSeverity() Severity {
	if x != nil {
		return x.Severity
	}
	return Severity_UNUSED
}

func (x *DlpAlert) GetSeverityId() int32 {
	if x != nil {
		return x.SeverityId
	}
	return 0
}

func (x *DlpAlert) GetPlatType() string {
	if x != nil {
		return x.PlatType
	}
	return ""
}

func (x *DlpAlert) GetTraceId() []string {
	if x != nil {
		return x.TraceId
	}
	return nil
}

func (x *DlpAlert) GetCompressEncrypt() int32 {
	if x != nil {
		return x.CompressEncrypt
	}
	return 0
}

func (x *DlpAlert) GetUserTags() []string {
	if x != nil {
		return x.UserTags
	}
	return nil
}

func (x *DlpAlert) GetIngestionTime() int64 {
	if x != nil {
		return x.IngestionTime
	}
	return 0
}

func (x *DlpAlert) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DlpAlert) GetFileProperties() []string {
	if x != nil {
		return x.FileProperties
	}
	return nil
}

func (x *DlpAlert) GetSubSrcTraceId() []string {
	if x != nil {
		return x.SubSrcTraceId
	}
	return nil
}

func (x *DlpAlert) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *DlpAlert) GetScoreReason() string {
	if x != nil {
		return x.ScoreReason
	}
	return ""
}

func (x *DlpAlert) GetSrcPath() string {
	if x != nil {
		return x.SrcPath
	}
	return ""
}

func (x *DlpAlert) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *DlpAlert) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

func (x *DlpAlert) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

func (x *DlpAlert) GetSensitiveInfo() string {
	if x != nil {
		return x.SensitiveInfo
	}
	return ""
}

func (x *DlpAlert) GetFileHideSuffix() uint32 {
	if x != nil {
		return x.FileHideSuffix
	}
	return 0
}

func (x *DlpAlert) GetFileEventId() string {
	if x != nil {
		return x.FileEventId
	}
	return ""
}

func (x *DlpAlert) GetPolicyId() string {
	if x != nil {
		return x.PolicyId
	}
	return ""
}

func (x *DlpAlert) GetPolicyName() string {
	if x != nil {
		return x.PolicyName
	}
	return ""
}

func (x *DlpAlert) GetAlertType() string {
	if x != nil {
		return x.AlertType
	}
	return ""
}

func (x *DlpAlert) GetEngineName() string {
	if x != nil {
		return x.EngineName
	}
	return ""
}

func (x *DlpAlert) GetEnableAnalysis() uint32 {
	if x != nil {
		return x.EnableAnalysis
	}
	return 0
}

func (x *DlpAlert) GetDisposeAction() uint32 {
	if x != nil {
		return x.DisposeAction
	}
	return 0
}

func (x *DlpAlert) GetDataCategoryName() string {
	if x != nil {
		return x.DataCategoryName
	}
	return ""
}

var File_events_v1_dlp_alert_proto protoreflect.FileDescriptor

var file_events_v1_dlp_alert_proto_rawDesc = []byte{
	0x0a, 0x19, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x6c, 0x70, 0x5f,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x1a, 0x1a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x44,
	0x0a, 0x0b, 0x44, 0x6c, 0x70, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a,
	0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6c, 0x70, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x05, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x22, 0x9d, 0x11, 0x0a, 0x08, 0x44, 0x6c, 0x70, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x70, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x70, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x63, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x61,
	0x6c, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64,
	0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x63, 0x63, 0x75, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x25, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x26, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x27, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x76, 0x65,
	0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x28, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x18, 0x2b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x2c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x69, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x2f, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x27, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x72, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x30, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x53,
	0x72, 0x63, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x49, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x72, 0x63,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x68, 0x69, 0x64, 0x65, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x38,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x69, 0x64, 0x65, 0x53, 0x75,
	0x66, 0x66, 0x69, 0x78, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x40, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x64, 0x61, 0x74, 0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x32, 0x64, 0x0a, 0x0e, 0x44, 0x6c, 0x70, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x52, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x6c, 0x70, 0x4c, 0x6f, 0x67, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x44,
	0x6c, 0x70, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x28, 0x01, 0x42, 0x40, 0x0a, 0x13, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x5a, 0x29, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73,
	0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_events_v1_dlp_alert_proto_rawDescOnce sync.Once
	file_events_v1_dlp_alert_proto_rawDescData = file_events_v1_dlp_alert_proto_rawDesc
)

func file_events_v1_dlp_alert_proto_rawDescGZIP() []byte {
	file_events_v1_dlp_alert_proto_rawDescOnce.Do(func() {
		file_events_v1_dlp_alert_proto_rawDescData = protoimpl.X.CompressGZIP(file_events_v1_dlp_alert_proto_rawDescData)
	})
	return file_events_v1_dlp_alert_proto_rawDescData
}

var file_events_v1_dlp_alert_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_events_v1_dlp_alert_proto_goTypes = []interface{}{
	(*DlpAlertReq)(nil), // 0: api.asdsec.file_event.DlpAlertReq
	(*DlpAlert)(nil),    // 1: api.asdsec.file_event.DlpAlert
	(Severity)(0),       // 2: api.asdsec.file_event.Severity
	(*Reply)(nil),       // 3: api.asdsec.file_event.Reply
}
var file_events_v1_dlp_alert_proto_depIdxs = []int32{
	1, // 0: api.asdsec.file_event.DlpAlertReq.alert:type_name -> api.asdsec.file_event.DlpAlert
	2, // 1: api.asdsec.file_event.DlpAlert.severity:type_name -> api.asdsec.file_event.Severity
	0, // 2: api.asdsec.file_event.DlpAlertReport.CreateDlpLog:input_type -> api.asdsec.file_event.DlpAlertReq
	3, // 3: api.asdsec.file_event.DlpAlertReport.CreateDlpLog:output_type -> api.asdsec.file_event.Reply
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_events_v1_dlp_alert_proto_init() }
func file_events_v1_dlp_alert_proto_init() {
	if File_events_v1_dlp_alert_proto != nil {
		return
	}
	file_events_v1_file_event_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_events_v1_dlp_alert_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DlpAlertReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_events_v1_dlp_alert_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DlpAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_events_v1_dlp_alert_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_events_v1_dlp_alert_proto_goTypes,
		DependencyIndexes: file_events_v1_dlp_alert_proto_depIdxs,
		MessageInfos:      file_events_v1_dlp_alert_proto_msgTypes,
	}.Build()
	File_events_v1_dlp_alert_proto = out.File
	file_events_v1_dlp_alert_proto_rawDesc = nil
	file_events_v1_dlp_alert_proto_goTypes = nil
	file_events_v1_dlp_alert_proto_depIdxs = nil
}
