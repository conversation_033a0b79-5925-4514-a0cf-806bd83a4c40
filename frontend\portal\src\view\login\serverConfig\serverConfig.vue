<template>
  <div class="server-config">
    <div class="config-header">
      <span class="title">平台地址</span>
    </div>

    <div class="config-form">
      <base-form
        ref="serverForm"
        :model="formData"
        :rules="rules"
        @keyup.enter="handleSubmit"
      >
        <base-form-item prop="serverUrl">
          <div class="input-wrapper">
            <base-input
              v-model="formData.serverUrl"
              placeholder="输入您连接的平台服务器地址"
              suffix-icon="link"
              class="setting-input"
            />
            <span class="spa-label" @click="handleSpaEnabled">
              <svg
                class="icon"
                aria-hidden="true"
              >
                <use xlink:href="#icon-spa" />
              </svg>
            </span>
          </div>
          <div v-if="spaEnabled" style="margin-top: 12px; width: 288px;">
            <base-input
              v-model="spaCode"
              maxlength="6"
              placeholder="请输入6位数字访问码"
              style="width: 100%;"
              @input="handleSpaCodeInput"
            />
          </div>
          <div class="input-tip">
            请输入平台地址，如：https://*************
          </div>
        </base-form-item>

        <base-form-item>
          <base-button
            type="primary"
            class="submit-button"
            :loading="loading"
            @click="handleSubmit"
          >
            连接服务器
          </base-button>
        </base-form-item>
      </base-form>
    </div>

    <div class="config-tips">
      <div v-if="tipStatus.message1" class="tip-item error-tip">
        <base-icon
          :name="tipStatus.icon1"
          :style="{
            color: tipStatus.color1,
            marginRight: '6px',
            fontSize: '14px',
            flexShrink: 0,
            marginTop: '1px'
          }"
        />
        <span class="error-message">{{ tipStatus.message1 }}</span>
      </div>
      <div v-if="tipStatus.message2" class="tip-item error-reasons">
        <span class="reasons-text">{{ tipStatus.message2 }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServerConfig'
}
</script>

<script setup>
import { ref, reactive } from 'vue'
import { updateServerHost, getSidecarProxyPort } from '@/utils/request'
import { Message } from '@/components/base'
import agentApi from '@/api/agentApi'

const emit = defineEmits(['server-configured'])

const serverForm = ref(null)
const loading = ref(false)

const formData = reactive({
  serverUrl: ''
})

const spaEnabled = ref(false)
const spaCode = ref('')

// 提示状态管理
const tipStatus = reactive({
  icon1: 'info-circle',
  color1: '#e6a23c',
  message1: '',
  icon2: 'check',
  color2: '#67c23a',
  message2: ''
})

// 重置提示状态
const resetTipStatus = () => {
  tipStatus.icon1 = 'loading'
  tipStatus.color1 = '#409eff'
  tipStatus.message1 = '正在测试服务器连接...'
  tipStatus.icon2 = 'warning'
  tipStatus.color2 = '#e6a23c'
  tipStatus.message2 = '请稍候，正在验证服务器配置'
}

const rules = {
  serverUrl: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    {
      pattern: /^https?:\/\/.+/,
      message: '请输入有效的服务器地址（需包含 http:// 或 https://）',
      trigger: 'blur'
    }
  ]
}

// 点击访问码开关
const handleSpaEnabled = async() => {
  spaEnabled.value = !spaEnabled.value
}

// 处理访问码输入事件，限制只能输入数字
const handleSpaCodeInput = (value) => {
  // 只保留数字字符
  const numericValue = value.replace(/[^0-9]/g, '')
  if (numericValue !== value) {
    spaCode.value = numericValue
  }
}

// 保存配置到后台
const saveConfigToBackend = async(configData) => {
  try {
    if (agentApi.isClient()) {
      // 客户端环境：保存到客户端配置
      const currentSettings = await agentApi.getClientConfig() || {}
      const settings = {
        ...currentSettings,
        ServerUrl: configData.serverUrl,
        SpaEnabled: configData.spaEnabled,
        activation_code: configData.spaEnabled ? configData.spaCode : ''
      }
      console.log('SPA码将保存为activation_code:', settings.activation_code)

      await agentApi.setClientConfig(settings)
      // 更新全局URL参数
      globalUrlHashParams.set('WebUrl', configData.serverUrl)
      console.log('配置已保存到客户端:', settings)
    } else {
      // 浏览器环境：保存到本地存储
      const settings = {
        serverUrl: configData.serverUrl,
        spaEnabled: configData.spaEnabled,
        spaCode: configData.spaCode
      }
      localStorage.setItem('server_config', JSON.stringify(settings))
      console.log('配置已保存到本地存储:', settings)
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    throw new Error('保存配置失败')
  }
}

// 清除已保存的配置
// const clearSavedConfig = async() => {
//   try {
//     if (agentApi.isClient()) {
//       // 客户端环境：尝试获取当前配置并只清除相关字段
//       try {
//         const currentSettings = await agentApi.getClientConfig() || {}
//         // 只清除服务器相关配置，保留其他配置
//         const updatedSettings = {
//           ...currentSettings,
//           ServerUrl: '',
//           SpaEnabled: false,
//           activation_code: '' // 清除activation_code
//         }
//         await agentApi.setClientConfig(updatedSettings)
//         console.log('已清除客户端服务器配置')
//       } catch (clientError) {
//         console.warn('无法通过客户端API清除配置，尝试清除URL参数:', clientError)
//         // 如果客户端API失败，至少清除URL参数
//         globalUrlHashParams.delete('WebUrl')
//       }
//       // 清除全局URL参数
//       globalUrlHashParams.delete('WebUrl')
//     } else {
//       // 浏览器环境：清除本地存储
//       localStorage.removeItem('server_config')
//       console.log('已清除本地存储配置')
//     }
//   } catch (error) {
//     console.error('清除配置失败:', error)
//     // 不抛出错误，避免阻塞用户操作
//     console.warn('配置清除失败，但不影响后续操作')
//   }
// }

const handleSubmit = async() => {
  if (!serverForm.value) return

  try {
    const valid = await serverForm.value.validate()
    if (!valid) return
    // 验证SPA码
    if (spaEnabled.value) {
      if (!spaCode.value) {
        Message.error('请输入访问码')
        return
      }
      if (spaCode.value.length !== 6) {
        Message.error('访问码必须是6位数字')
        return
      }
      if (!/^\d{6}$/.test(spaCode.value)) {
        Message.error('访问码只能包含数字')
        return
      }
    }
    loading.value = true
    // 验证服务器地址格式
    const success = await updateServerHost(formData.serverUrl)
    if (!success) {
      Message.error('服务器地址格式错误')
      return
    }
    // 解析并标准化服务器地址
    const url = new URL(formData.serverUrl)
    const host = `${url.protocol}//${url.host}`
    formData.serverUrl = host
    // 先保存配置（包括SPA码），这样后台SPA服务才能工作
    const configData = {
      serverUrl: formData.serverUrl,
      spaEnabled: spaEnabled.value,
      spaCode: spaEnabled.value ? spaCode.value : ''
    }

    try {
      // 重置提示状态，显示正在连接
      resetTipStatus()

      // 先保存配置到后台，这样SPA服务才能工作
      await saveConfigToBackend(configData)
      console.info('配置已保存，正在测试连接...')

      // SP等待2秒让后台服务启动
      tipStatus.message1 = '正在启动服务，请稍候...'
      tipStatus.color1 = '#409eff'
      tipStatus.icon1 = 'loading'
      await new Promise(resolve => setTimeout(resolve, 5000))

      // 通过本地代理测试服务器连接（SPA和非SPA模式都使用代理）
      let proxyPort = 28080 // 默认端口
      try {
        // 获取动态端口（getSidecarProxyPort已经处理了对象解析，直接返回int）
        proxyPort = await getSidecarProxyPort()
        console.log('获取到的代理端口:', proxyPort)
      } catch (error) {
        console.warn('获取动态端口失败，使用默认端口28080:', error)
        proxyPort = 28080
      }

      const testUrl = `http://127.0.0.1:${proxyPort}/auth/login/v1/user/main_idp/list`
      console.log('测试连接地址:', testUrl)

      // 重试机制：最多重试5次，每次等待1秒
      const maxRetries = 5
      const retryDelay = 2000 // 1秒
      let lastError = null

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`连接测试第${attempt}次尝试...`)

          // 更新提示信息显示当前尝试次数
          if (attempt > 1) {
            tipStatus.message1 = `正在重试连接... (${attempt}/${maxRetries})`
          }

          // 创建AbortController来实现超时控制
          const controller = new AbortController()
          const timeoutId = setTimeout(() => {
            controller.abort()
          }, 1000) // 1秒超时

          const response = await fetch(testUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            },
            signal: controller.signal
          })

          // 清除超时定时器
          clearTimeout(timeoutId)

          // 如果请求成功，跳出重试循环
          if (response.ok && response.status === 200) {
            console.log(`连接测试第${attempt}次尝试成功`)
            break
          } else {
            throw new Error(`服务器响应错误: ${response.status}`)
          }
        } catch (error) {
          lastError = error
          console.warn(`连接测试第${attempt}次尝试失败:`, error.message)

          // 如果不是最后一次尝试，等待后重试
          if (attempt < maxRetries) {
            console.log(`等待${retryDelay}ms后进行第${attempt + 1}次重试...`)
            await new Promise(resolve => setTimeout(resolve, retryDelay))
          } else {
            // 最后一次尝试也失败了，抛出错误
            console.error(`连接测试失败，已重试${maxRetries}次`)
            throw lastError
          }
        }
      }

      const response = { ok: true, status: 200 } // 如果到这里说明连接成功
      if (response.ok && response.status === 200) {
        // 更新提示标签显示成功状态
        tipStatus.icon1 = 'check'
        tipStatus.color1 = '#67c23a'
        tipStatus.message1 = '服务器连接成功！'
        tipStatus.icon2 = 'check'
        tipStatus.color2 = '#67c23a'
        tipStatus.message2 = '配置成功后将自动跳转到登录页面'

        Message.success('服务器连接成功！')
        // 连接成功，通知父组件
        emit('server-configured', configData)
      } else {
        throw new Error(`服务器响应错误: ${response.status}`)
      }
    } catch (error) {
      console.warn('服务器连接测试失败，清除已保存的配置:', error)
      // 连接失败，尝试清除已保存的配置
      // await clearSavedConfig()

      // 更新提示标签显示错误信息
      tipStatus.icon1 = 'close'
      tipStatus.color1 = '#f56c6c'
      tipStatus.icon2 = 'warning'
      tipStatus.color2 = '#e6a23c'

      // 统一的服务器连接错误处理
      tipStatus.message1 = '连接失败，可能存在以下原因：'
      if (spaEnabled.value) {
        tipStatus.message2 = `1.网络异常或服务器地址不可达
2.已启用SPA安全防护，未输入SPA访问码
3.SPA访问码无效（无效原因可能为：电脑系统时间存在偏差、未区分字母大小写、访问码已过期），请重新输入`
      } else {
        tipStatus.message2 = `1.网络异常或服务器地址不可达
2.服务器服务未启动或配置异常`
      }
    }
  } catch (error) {
    console.error('配置服务器失败:', error)

    // 更新提示标签显示配置失败信息
    tipStatus.icon1 = 'close'
    tipStatus.color1 = '#f56c6c'
    tipStatus.message1 = '配置失败，请检查输入格式'

    if (spaEnabled.value) {
      tipStatus.icon2 = 'warning'
      tipStatus.color2 = '#e6a23c'
      tipStatus.message2 = '请检查服务器地址和访问码格式'
    } else {
      tipStatus.icon2 = 'warning'
      tipStatus.color2 = '#e6a23c'
      tipStatus.message2 = '请检查服务器地址格式是否正确'
    }
  } finally {
    loading.value = false
  }
}

</script>

<style lang="scss" scoped>
.server-config {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;

  .config-header {
    display: flex;
    align-items: center;
    justify-content: center;

    .title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";
    }
  }

  .config-form {
    margin-bottom: 30px;

    .label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
      font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";
    }

    .input-tip {
      margin-top: 6px;
      font-size: 12px;
      color: #999;
      line-height: 1.4;
      font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
    }

    .submit-button {
      width: 100%;
      height: 40px;
      font-size: 16px;
      font-weight: 500;
      padding: 0px;
      font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";

      :deep(.loading) {
        width: 16px !important;
        height: 16px !important;
        border-width: 2px !important;
        margin-right: 8px !important;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .input-wrapper {
    display: flex;
  }

  .setting-input {
    width: 288px;

    :deep(.el-input__inner) {
      height: 40px;
      border-radius: 6px;

      &:focus {
        border-color: #536ce6;
      }
    }
  }

  .spa-label {
    margin-left: 10px;
    width: 34px;
    height: 34px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .spa-label .icon {
    width: 16px;
    height: 16px;
  }

  .config-tips {
    .tip-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      font-size: 14px;
      color: #666;
      font-family: "PingFang SC", "PingFang SC-Regular", "Microsoft YaHei", "微软雅黑";

      &:last-child {
        margin-bottom: 0;
      }

      &.error-tip {
        align-items: center;

        .error-message {
          color: #e6a23c;
          font-weight: 500;
          font-size: 14px;
          line-height: 1.4;
        }
      }

      &.error-reasons {
        margin-left: 20px; // 与图标对齐
        margin-top: 6px;

        .reasons-text {
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
          white-space: pre-line;
          word-break: break-word;
        }
      }
    }
  }
}
</style>
