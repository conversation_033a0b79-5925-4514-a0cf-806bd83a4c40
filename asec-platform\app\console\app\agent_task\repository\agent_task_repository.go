package repository

import (
	"asdsec.com/asec/platform/app/console/app/agent_task/constants"
	"asdsec.com/asec/platform/app/console/app/agent_task/dto"
	appliance_dto "asdsec.com/asec/platform/app/console/app/appliancemgt/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model/agent_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
	"time"
)

type agentTaskRepository struct {
}

func (a agentTaskRepository) UpdateAgentTaskStatus(ctx context.Context, req appliance_dto.SatusReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	update := agent_model.AgentEscapeTask{}
	update.Id = req.TaskId
	update.ApplianceId = req.Id
	update.TaskDesc = req.Desc
	update.TaskStatus = req.Status
	update.Cmd = req.Action

	//终端上报的新事件
	if update.Id == "" {
		id, _ := snowflake.Sf.GetId()
		update.Id = strconv.FormatUint(id, 10)
		update.CreateTime = utils.GetFormattedNowTime()
		update.Timeout = constants.DefaultTimeout
	}

	//如果任务不存在则创建，否则更新状态
	return db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"task_desc", "task_status", "end_time"}),
	}).Create(&update).Error
}

func (a agentTaskRepository) GetAgentTaskList(ctx context.Context, agentId string, status string) ([]agent_model.AgentEscapeTask, error) {
	res := make([]agent_model.AgentEscapeTask, 0)
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return res, err
	}
	db = db.Model(agent_model.AgentEscapeTask{}).Order("create_time asc")
	if agentId != "" {
		db = db.Where("appliance_id = ?", agentId)
	}
	if status != "" {
		db = db.Where("task_status = ?", status)
	}
	err = db.Find(&res).Error
	if err != nil {
		return res, err
	}
	return res, nil
}

func (a agentTaskRepository) CreateAgentTask(ctx context.Context, req dto.CreateAgentTaskReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var taskList []agent_model.AgentEscapeTask
	redisKeyList := make(map[string]time.Duration)
	for _, v := range req.TaskList {
		id := v.TaskId
		if v.TaskId == "" {
			idUInt64, err := snowflake.Sf.GetId()
			if err != nil {
				global.SysLog.Error("get snowflake err", zap.Error(err))
				return err
			}
			id = strconv.FormatUint(idUInt64, 10)
		}

		taskList = append(taskList, agent_model.AgentEscapeTask{
			Id:          id,
			CreateTime:  time.Now(),
			Timeout:     v.Timeout,
			Cmd:         v.Cmd,
			ApplianceId: v.AgentId,
			TaskStatus:  constants.WaitTaskStatus,
			TaskDesc:    v.TaskDesc,
		})
		redisKeyList[fmt.Sprintf("%s_%s", id, v.AgentId)] = time.Duration(v.Timeout+constants.DefaultDuration) * time.Second
	}
	redisCli, err := global.GetRedisClient(ctx)
	if err != nil {
		global.SysLog.Error("get redis cli err", zap.Error(err))
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		for key, duration := range redisKeyList {
			err = redisCli.LPush(ctx, key, constants.RedisDefaultValue).Err()
			if err != nil {
				return err
			}
			err = redisCli.Expire(ctx, key, duration).Err()
			if err != nil {
				return err
			}
		}
		return db.Create(&taskList).Error
	})
}

func (a agentTaskRepository) GetAgentTaskDetail(ctx context.Context, id string) (agent_model.AgentEscapeTask, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return agent_model.AgentEscapeTask{}, err
	}
	var res agent_model.AgentEscapeTask
	err = db.Model(agent_model.AgentEscapeTask{}).Where("id = ?", id).Find(&res).Error
	if err != nil {
		return agent_model.AgentEscapeTask{}, err
	}
	return res, nil
}

type AgentTaskRepository interface {
	CreateAgentTask(ctx context.Context, req dto.CreateAgentTaskReq) error
	GetAgentTaskDetail(ctx context.Context, id string) (agent_model.AgentEscapeTask, error)
	GetAgentTaskList(ctx context.Context, agentId string, status string) ([]agent_model.AgentEscapeTask, error)
	UpdateAgentTaskStatus(ctx context.Context, req appliance_dto.SatusReq) error
}

func NewAgentTaskRepository() AgentTaskRepository {
	return agentTaskRepository{}
}
