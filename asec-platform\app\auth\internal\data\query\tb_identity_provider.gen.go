// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbIdentityProvider(db *gorm.DB, opts ...gen.DOOption) tbIdentityProvider {
	_tbIdentityProvider := tbIdentityProvider{}

	_tbIdentityProvider.tbIdentityProviderDo.UseDB(db, opts...)
	_tbIdentityProvider.tbIdentityProviderDo.UseModel(&model.TbIdentityProvider{})

	tableName := _tbIdentityProvider.tbIdentityProviderDo.TableName()
	_tbIdentityProvider.ALL = field.NewAsterisk(tableName)
	_tbIdentityProvider.ID = field.NewString(tableName, "id")
	_tbIdentityProvider.Name = field.NewString(tableName, "name")
	_tbIdentityProvider.Type = field.NewString(tableName, "type")
	_tbIdentityProvider.SourceID = field.NewString(tableName, "source_id")
	_tbIdentityProvider.CorpID = field.NewString(tableName, "corp_id")
	_tbIdentityProvider.Avatar = field.NewString(tableName, "avatar")
	_tbIdentityProvider.CreatedAt = field.NewTime(tableName, "created_at")
	_tbIdentityProvider.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbIdentityProvider.Enable = field.NewBool(tableName, "enable")
	_tbIdentityProvider.Description = field.NewString(tableName, "description")
	_tbIdentityProvider.IsDefault = field.NewBool(tableName, "is_default")
	_tbIdentityProvider.TemplateType = field.NewString(tableName, "template_type")

	_tbIdentityProvider.fillFieldMap()

	return _tbIdentityProvider
}

type tbIdentityProvider struct {
	tbIdentityProviderDo tbIdentityProviderDo

	ALL         field.Asterisk
	ID          field.String // 认证服务器id
	Name        field.String // 认证服务器名称，租户下名称唯一
	Type        field.String // 认证服务器类型
	SourceID    field.String // 认证服务器对应的用户来源，为0表示可应用于所有用户
	CorpID      field.String // 租户id
	Avatar      field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Enable      field.Bool
	Description field.String
	IsDefault   field.Bool
	TemplateType field.String
	fieldMap map[string]field.Expr
}

func (t tbIdentityProvider) Table(newTableName string) *tbIdentityProvider {
	t.tbIdentityProviderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbIdentityProvider) As(alias string) *tbIdentityProvider {
	t.tbIdentityProviderDo.DO = *(t.tbIdentityProviderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbIdentityProvider) updateTableName(table string) *tbIdentityProvider {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.Type = field.NewString(table, "type")
	t.SourceID = field.NewString(table, "source_id")
	t.CorpID = field.NewString(table, "corp_id")
	t.Avatar = field.NewString(table, "avatar")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.Enable = field.NewBool(table, "enable")
	t.Description = field.NewString(table, "description")
	t.IsDefault = field.NewBool(table, "is_default")
	t.TemplateType = field.NewString(table, "template_type")

	t.fillFieldMap()

	return t
}

func (t *tbIdentityProvider) WithContext(ctx context.Context) *tbIdentityProviderDo {
	return t.tbIdentityProviderDo.WithContext(ctx)
}

func (t tbIdentityProvider) TableName() string { return t.tbIdentityProviderDo.TableName() }

func (t tbIdentityProvider) Alias() string { return t.tbIdentityProviderDo.Alias() }

func (t *tbIdentityProvider) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbIdentityProvider) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 11)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["type"] = t.Type
	t.fieldMap["source_id"] = t.SourceID
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["avatar"] = t.Avatar
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["enable"] = t.Enable
	t.fieldMap["description"] = t.Description
	t.fieldMap["is_default"] = t.IsDefault
	t.fieldMap["template_type"] = t.TemplateType
}

func (t tbIdentityProvider) clone(db *gorm.DB) tbIdentityProvider {
	t.tbIdentityProviderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbIdentityProvider) replaceDB(db *gorm.DB) tbIdentityProvider {
	t.tbIdentityProviderDo.ReplaceDB(db)
	return t
}

type tbIdentityProviderDo struct{ gen.DO }

func (t tbIdentityProviderDo) Debug() *tbIdentityProviderDo {
	return t.withDO(t.DO.Debug())
}

func (t tbIdentityProviderDo) WithContext(ctx context.Context) *tbIdentityProviderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbIdentityProviderDo) ReadDB() *tbIdentityProviderDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbIdentityProviderDo) WriteDB() *tbIdentityProviderDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbIdentityProviderDo) Session(config *gorm.Session) *tbIdentityProviderDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbIdentityProviderDo) Clauses(conds ...clause.Expression) *tbIdentityProviderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbIdentityProviderDo) Returning(value interface{}, columns ...string) *tbIdentityProviderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbIdentityProviderDo) Not(conds ...gen.Condition) *tbIdentityProviderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbIdentityProviderDo) Or(conds ...gen.Condition) *tbIdentityProviderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbIdentityProviderDo) Select(conds ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbIdentityProviderDo) Where(conds ...gen.Condition) *tbIdentityProviderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbIdentityProviderDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbIdentityProviderDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbIdentityProviderDo) Order(conds ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbIdentityProviderDo) Distinct(cols ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbIdentityProviderDo) Omit(cols ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbIdentityProviderDo) Join(table schema.Tabler, on ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbIdentityProviderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbIdentityProviderDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbIdentityProviderDo) Group(cols ...field.Expr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbIdentityProviderDo) Having(conds ...gen.Condition) *tbIdentityProviderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbIdentityProviderDo) Limit(limit int) *tbIdentityProviderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbIdentityProviderDo) Offset(offset int) *tbIdentityProviderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbIdentityProviderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbIdentityProviderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbIdentityProviderDo) Unscoped() *tbIdentityProviderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbIdentityProviderDo) Create(values ...*model.TbIdentityProvider) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbIdentityProviderDo) CreateInBatches(values []*model.TbIdentityProvider, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbIdentityProviderDo) Save(values ...*model.TbIdentityProvider) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbIdentityProviderDo) First() (*model.TbIdentityProvider, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProvider), nil
	}
}

func (t tbIdentityProviderDo) Take() (*model.TbIdentityProvider, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProvider), nil
	}
}

func (t tbIdentityProviderDo) Last() (*model.TbIdentityProvider, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProvider), nil
	}
}

func (t tbIdentityProviderDo) Find() ([]*model.TbIdentityProvider, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbIdentityProvider), err
}

func (t tbIdentityProviderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbIdentityProvider, err error) {
	buf := make([]*model.TbIdentityProvider, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbIdentityProviderDo) FindInBatches(result *[]*model.TbIdentityProvider, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbIdentityProviderDo) Attrs(attrs ...field.AssignExpr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbIdentityProviderDo) Assign(attrs ...field.AssignExpr) *tbIdentityProviderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbIdentityProviderDo) Joins(fields ...field.RelationField) *tbIdentityProviderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbIdentityProviderDo) Preload(fields ...field.RelationField) *tbIdentityProviderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbIdentityProviderDo) FirstOrInit() (*model.TbIdentityProvider, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProvider), nil
	}
}

func (t tbIdentityProviderDo) FirstOrCreate() (*model.TbIdentityProvider, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProvider), nil
	}
}

func (t tbIdentityProviderDo) FindByPage(offset int, limit int) (result []*model.TbIdentityProvider, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbIdentityProviderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbIdentityProviderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbIdentityProviderDo) Delete(models ...*model.TbIdentityProvider) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbIdentityProviderDo) withDO(do gen.Dao) *tbIdentityProviderDo {
	t.DO = *do.(*gen.DO)
	return t
}
