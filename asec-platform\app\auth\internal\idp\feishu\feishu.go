package feishu

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	pb "asdsec.com/asec/platform/api/auth/v1"
)

// 错误码 https://developer.work.weixin.qq.com/document/path/90313
const (
	errorCodeSuccess      = 0
	errorCodeNotTrustedIp = 60020 // 可信ip未配置
	errorCodeCorpIdWrong  = 40013 // corp id 不正确
	errorCodeSecretWrong  = 40091 // secret 不正确
	errorAgentIdWrong     = 40056 // secret 不正确
)

// https://developer.work.weixin.qq.com/document/path/91023
// https://developer.work.weixin.qq.com/document/path/90196

type FsIDProvider struct {
	client *http.Client

	providerId   string
	fsAppId      string
	secret       string
	cacheHandler CacheHandler
}

type CacheHandler interface {
	GetFsWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error)
	SetFsWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error
}

func NewFsIDProvider(providerId, fsAppId, secret string, handler CacheHandler) FsIDProvider {
	return FsIDProvider{
		client: &http.Client{Timeout: time.Second * 30},

		providerId:   providerId,
		fsAppId:      fsAppId,
		secret:       secret,
		cacheHandler: handler,
	}
}

type GetAccessTokenResp struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
	AccessToken      string `json:"access_token"`
	ExpireIn         int    `json:"expires_in"`
}

type GetUserIDResp struct {
	Errcode        int    `json:"errcode"`
	Errmsg         string `json:"errmsg"`
	Userid         string `json:"userid"`
	UserTicket     string `json:"user_ticket"`
	Openid         string `json:"openid"`
	ExternalUserid string `json:"external_userid"`
}

type GetUserInfo struct {
	Code       int    `json:"code"`
	Message    string `json:"message"`
	Sub        string `json:"sub"`
	Name       string `json:"name"`
	Picture    string `json:"picture"`
	OpenId     string `json:"open_id"`
	UnionId    string `json:"union_id"`
	EnName     string `json:"en_name"`
	TenantKey  string `json:"tenant_key"`
	AvatarUrl  string `json:"avatar_url"`
	UserId     string `json:"user_id"`
	EmployeeNo string `json:"employee_no"`
	Email      string `json:"email"`
	Mobile     string `json:"mobile"`
}

type Response struct {
	Message string `json:"message"`
}

func (fs FsIDProvider) getAccessToken(code string, redirectUri string) (string, error) {
	query := fmt.Sprintf("grant_type=authorization_code&client_id=%v&client_secret=%v&code=%v&redirect_uri=%v", fs.fsAppId, fs.secret, code, redirectUri)
	payload := strings.NewReader(query)
	getAccessTokenUrl := fmt.Sprintf("https://passport.feishu.cn/suite/passport/oauth/token")
	req, _ := http.NewRequest("POST", getAccessTokenUrl, payload)
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	resp, _ := http.DefaultClient.Do(req)

	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)

	var getAccessTokenResp GetAccessTokenResp
	err := json.Unmarshal(body, &getAccessTokenResp)
	if err != nil {
		return "", err
	}

	if getAccessTokenResp.Error != "" {
		return "", pb.ErrorRecordNotFound("token not found. getAccessTokenResp: %+v", getAccessTokenResp)
	}

	return getAccessTokenResp.AccessToken, nil
}
func (fs FsIDProvider) errorHandler(errorCode int, errorMsg string) error {
	if errorCode == errorCodeSuccess {
		return nil
	} else if errorCode == errorCodeNotTrustedIp {
		return pb.ErrorQiyewxTrustedIpError("errorCode=%v, errorMsg=%v", errorCode, errorMsg)
	} else if errorCode == errorAgentIdWrong || errorCode == errorCodeSecretWrong || errorCode == errorCodeCorpIdWrong {
		return pb.ErrorQiyewxConfigError("errorCode=%v, errorMsg=%v", errorCode, errorMsg)
	} else {
		return pb.ErrorParamError("errorCode=%v, errorMsg=%v", errorCode, errorMsg)
	}
}

func (fs FsIDProvider) GetUserInfo(code string, redirectUri string) (GetUserInfo, error) {
	accessToken, err := fs.getAccessToken(code, redirectUri)
	if err != nil {
		return GetUserInfo{}, err
	}

	getUserIdUrl := fmt.Sprintf("https://passport.feishu.cn/suite/passport/oauth/userinfo")
	req, err := http.NewRequest("GET", getUserIdUrl, nil)
	if err != nil {
		return GetUserInfo{}, nil
	}
	// 设置请求头
	//authorization := fmt.Sprintf("Bearer %v", accessToken)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := fs.client.Do(req)
	if err != nil {
		return GetUserInfo{}, err
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			return
		}
	}(resp.Body)

	buf := new(bytes.Buffer)
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return GetUserInfo{}, err
	}

	var userInfoResp GetUserInfo
	if err = json.Unmarshal(buf.Bytes(), &userInfoResp); err != nil {
		return GetUserInfo{}, err
	}

	err = fs.errorHandler(userInfoResp.Code, userInfoResp.Message)
	if err != nil {
		return GetUserInfo{}, err
	}

	return userInfoResp, nil
}

// MicroAppTokenResponse 微应用获取token的响应结构
type MicroAppTokenResponse struct {
	Code                  int    `json:"code"`
	Msg                   string `json:"msg"`
	AccessToken           string `json:"access_token"`
	ExpiresIn             int    `json:"expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	Scope                 string `json:"scope"`
	TokenType             string `json:"token_type"`
}

// MicroAppUserInfo 微应用用户信息响应结构
type MicroAppUserInfo struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Name            string `json:"name"`
		EnName          string `json:"en_name"`
		Email           string `json:"email"`
		EnterpriseEmail string `json:"enterprise_email"`
		Mobile          string `json:"mobile"`
		UserId          string `json:"user_id"`
		OpenId          string `json:"open_id"`
		UnionId         string `json:"union_id"`
		TenantKey       string `json:"tenant_key"`
		EmployeeNo      string `json:"employee_no"`
		AvatarUrl       string `json:"avatar_url"`
		AvatarThumb     string `json:"avatar_thumb"`
		AvatarMiddle    string `json:"avatar_middle"`
		AvatarBig       string `json:"avatar_big"`
	} `json:"data"`
}

// GetMicroAppUserAccessToken 微应用：用授权码换取user_access_token
func (fs FsIDProvider) GetMicroAppUserAccessToken(authCode string) (*MicroAppTokenResponse, error) {
	tokenUrl := "https://open.feishu.cn/open-apis/authen/v2/oauth/token"

	// 构建请求体
	requestBody := map[string]string{
		"grant_type":    "authorization_code",
		"client_id":     fs.fsAppId,
		"client_secret": fs.secret,
		"code":          authCode,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %v", err)
	}

	// 创建POST请求
	req, err := http.NewRequest("POST", tokenUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	// 发送请求
	resp, err := fs.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求发送失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var tokenResp MicroAppTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return nil, fmt.Errorf("解析token响应失败: %v", err)
	}

	// 检查响应状态
	if tokenResp.Code != 0 {
		return nil, fmt.Errorf("获取token失败，错误码: %d, 错误信息: %s", tokenResp.Code, tokenResp.Msg)
	}

	return &tokenResp, nil
}

// GetMicroAppUserInfo 微应用：用user_access_token获取用户信息
func (fs FsIDProvider) GetMicroAppUserInfo(userAccessToken string) (*MicroAppUserInfo, error) {
	// 飞书微应用获取用户信息的正确API端点
	userInfoUrl := "https://open.feishu.cn/open-apis/authen/v1/user_info"

	// 添加调试日志
	// fmt.Printf("[飞书微应用] 获取用户信息，URL: %s, Token: %s\n", userInfoUrl, userAccessToken)

	// 创建GET请求
	req, err := http.NewRequest("GET", userInfoUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置Authorization头部
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", userAccessToken))
	// 发送请求
	resp, err := fs.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求发送失败: %v", err)
	}
	defer resp.Body.Close()

	// 添加响应状态日志
	// fmt.Printf("[飞书微应用] 用户信息API响应状态: %s\n", resp.Status)

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 添加响应内容日志
	// fmt.Printf("[飞书微应用] 用户信息API响应内容: %s\n", string(body))

	// 解析响应
	var userInfo MicroAppUserInfo
	if err := json.Unmarshal(body, &userInfo); err != nil {
		return nil, fmt.Errorf("解析用户信息响应失败: %v", err)
	}

	// 检查响应状态
	if userInfo.Code != 0 {
		return nil, fmt.Errorf("获取用户信息失败，错误码: %d, 错误信息: %s", userInfo.Code, userInfo.Msg)
	}

	return &userInfo, nil
}

// GetMicroAppUserInfoByCode 微应用：一站式方法，用授权码直接获取用户信息
func (fs FsIDProvider) GetMicroAppUserInfoByCode(authCode string) (*MicroAppUserInfo, error) {
	// 1. 用授权码换取user_access_token
	tokenResp, err := fs.GetMicroAppUserAccessToken(authCode)
	if err != nil {
		return nil, fmt.Errorf("获取access_token失败: %v", err)
	}
	// 2. 用user_access_token获取用户信息（优先尝试Contact API）
	userInfo, err := fs.GetMicroAppUserInfo(tokenResp.AccessToken)

	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	return userInfo, nil
}
