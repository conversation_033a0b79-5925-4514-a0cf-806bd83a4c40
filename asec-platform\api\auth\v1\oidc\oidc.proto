syntax = "proto3";

package api.auth.v1.oidc;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "asdsec.com/asec/platform/api/auth/v1/oidc;oidc";
option java_multiple_files = true;
option java_package = ".api.auth.v1.oidc";

// OIDC服务定义
service OIDC {
  // 授权端点 - OIDC授权码流程的起始点
  rpc Authorize (AuthorizeRequest) returns (AuthorizeReply) {
    option (google.api.http) = {
      get: "/auth/login/v1/authorize/oidc/authorize"
    };
  }

  // 令牌端点 - 用授权码交换访问令牌和ID令牌
  rpc Token (TokenRequest) returns (TokenReply) {
    option (google.api.http) = {
      post: "/auth/login/v1/authorize/oidc/token",
      body: "*"
    };
  }

  // 用户信息端点 - 使用访问令牌获取用户信息
  rpc UserInfo (UserInfoRequest) returns (UserInfoReply) {
    option (google.api.http) = {
      get: "/auth/login/v1/authorize/oidc/userinfo"
    };
  }

  // 发现端点 - 提供OIDC提供者的元数据
  rpc Discovery (DiscoveryRequest) returns (DiscoveryReply) {
    option (google.api.http) = {
      get: "/auth/login/v1/authorize/oidc/.well-known/openid_configuration"
    };
  }

  // JWKS端点 - 提供用于验证JWT签名的公钥集
  rpc JWKS (JWKSRequest) returns (JWKSReply) {
    option (google.api.http) = {
      get: "/auth/login/v1/authorize/oidc/jwks"
    };
  }

  // 令牌撤销端点 - 撤销访问令牌或刷新令牌
  rpc Revoke (RevokeRequest) returns (RevokeReply) {
    option (google.api.http) = {
      post: "/auth/login/v1/authorize/oidc/revoke",
      body: "*"
    };
  }

  // 登出端点 - 结束用户会话
  rpc Logout (LogoutRequest) returns (LogoutReply) {
    option (google.api.http) = {
      get: "/auth/login/v1/authorize/oidc/logout"
    };
  }
}

// 授权请求
message AuthorizeRequest {
  string response_type = 1;       // 响应类型，固定为"code"
  string client_id = 2;           // 客户端ID
  string redirect_uri = 3;        // 重定向URI
  string scope = 4;               // 权限范围，如"openid profile email"
  string state = 5;               // 状态参数，防CSRF
  string code_challenge = 6;      // PKCE代码挑战
  string code_challenge_method = 7; // PKCE挑战方法，如"S256"
  string nonce = 8;               // 随机数，用于ID令牌
}

// 授权响应
message AuthorizeReply {
  string redirect_url = 1;        // 重定向URL（包含授权码或错误）
}

// 令牌请求
message TokenRequest {
  string grant_type = 1;          // 授权类型，如"authorization_code"
  string client_id = 2;           // 客户端ID
  string client_secret = 3;       // 客户端密钥
  string code = 4;                // 授权码
  string redirect_uri = 5;        // 重定向URI（必须与授权时一致）
  string code_verifier = 6;       // PKCE代码验证器
}

// 令牌响应
message TokenReply {
  string access_token = 1;        // 访问令牌
  string token_type = 2;          // 令牌类型，通常为"Bearer"
  int64 expires_in = 3;           // 过期时间（秒）
  string refresh_token = 4;       // 刷新令牌
  string id_token = 5;            // ID令牌（JWT格式）
  string scope = 6;               // 实际授权的权限范围
}

// 用户信息请求
message UserInfoRequest {
  string access_token = 1;        // 访问令牌（从Authorization头获取）
}

// 用户信息响应
message UserInfoReply {
  string sub = 1;                 // 用户唯一标识
  string name = 2;                // 用户姓名
  string nickname = 3;            // 昵称
  string preferred_username = 4;  // 首选用户名
  string picture = 5;             // 头像URL
  string email = 6;              // 邮箱地址
  bool email_verified = 7;       // 邮箱是否已验证
  string phone_number = 8;       // 电话号码
  bool phone_number_verified = 9; // 电话号码是否已验证
  google.protobuf.Struct address = 10; // 企业相关信息
  // 通过address结构传递非标准字段:
  // - group_id: 组织ID (dto.User.GroupID)
  // - group_name: 组织名称 (dto.User.GroupName)
  // - corp_id: 企业ID (dto.User.CorpID)
  // - source_type: 用户来源类型 (dto.User.SourceType)
  // - source_name: 用户来源名称 (dto.User.SourceName)
  // - identifier: 用户标识 (dto.User.Identify)
  // - auth_type: 认证类型 (dto.User.AuthType)
  int64 updated_at = 11;          // 信息更新时间
}

// 发现请求
message DiscoveryRequest {
  // 无需参数
}

// 发现响应
message DiscoveryReply {
  string issuer = 1;              // 发行者标识
  string authorization_endpoint = 2; // 授权端点
  string token_endpoint = 3;      // 令牌端点
  string userinfo_endpoint = 4;   // 用户信息端点
  string jwks_uri = 5;            // JWKS端点
  string revocation_endpoint = 6; // 撤销端点
  string end_session_endpoint = 7; // 登出端点
  repeated string scopes_supported = 8; // 支持的权限范围
  repeated string response_types_supported = 9; // 支持的响应类型
  repeated string grant_types_supported = 10; // 支持的授权类型
  repeated string subject_types_supported = 11; // 支持的主体类型
  repeated string id_token_signing_alg_values_supported = 12; // 支持的ID令牌签名算法
  repeated string code_challenge_methods_supported = 13; // 支持的PKCE挑战方法
}

// JWKS请求
message JWKSRequest {
  // 无需参数
}

// JWKS响应
message JWKSReply {
  repeated JWK keys = 1;          // 公钥集
}

// JSON Web Key
message JWK {
  string kty = 1;                 // 密钥类型
  string use = 2;                 // 密钥用途
  string kid = 3;                 // 密钥ID
  string alg = 4;                 // 算法
  string n = 5;                   // RSA模数
  string e = 6;                   // RSA公钥指数
}

// 撤销请求
message RevokeRequest {
  string client_id = 1;           // 客户端ID
  string client_secret = 2;       // 客户端密钥
  string token = 3;               // 要撤销的令牌
  string token_type_hint = 4;     // 令牌类型提示
}

// 撤销响应
message RevokeReply {
  bool success = 1;               // 是否成功
  string message = 2;             // 响应消息
}

// 登出请求
message LogoutRequest {
  string client_id = 1;           // 客户端ID
  string id_token_hint = 2;       // ID令牌提示
  string post_logout_redirect_uri = 3; // 登出后重定向URI
  string state = 4;               // 状态参数
}

// 登出响应
message LogoutReply {
  string redirect_url = 1;        // 重定向URL
}
