package paila

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt/v4"
)

// PailaClient 派拉客户端实现
type PailaClient struct {
	host         string
	clientID     string
	clientSecret string
	deptURI      string
	userURI      string
	logger       *log.Helper
	client       *http.Client
}

// Config 包含派拉客户端的配置信息
type Config struct {
	AppURL    string
	AppKey    string
	AppSecret string
}

// GlobalConfigItem 全局配置项结构
type GlobalConfigItem struct {
	Key    string `json:"key"`
	Value  string `json:"value"`
	Remark string `json:"remark"`
}

// NewPailaClient 创建一个新的派拉客户端
func NewPailaClient(globalData string) (*PailaClient, error) {
	var configItems []GlobalConfigItem
	if err := json.Unmarshal([]byte(globalData), &configItems); err != nil {
		return nil, fmt.Errorf("解析派拉配置失败: %w", err)
	}

	config := parseConfig(configItems)
	if config.AppURL == "" || config.AppKey == "" || config.AppSecret == "" {
		return nil, fmt.Errorf("派拉配置不完整，需要appUrl、appKey和appSecret")
	}

	// 确保URL格式正确
	if !strings.HasPrefix(config.AppURL, "http") {
		config.AppURL = "https://" + config.AppURL
	}
	// 移除尾部斜杠
	config.AppURL = strings.TrimSuffix(config.AppURL, "/")

	logger := log.With(log.GetLogger(),
		"module", "paila_client",
		"app_url", config.AppURL)

	return &PailaClient{
		host:         config.AppURL,
		clientID:     config.AppKey,
		clientSecret: config.AppSecret,
		deptURI:      "/esc-idm/api/v1/org/list",
		userURI:      "/esc-idm/api/v1/account/list",
		logger:       log.NewHelper(logger),
		client:       &http.Client{Timeout: 10 * time.Second},
	}, nil
}

// parseConfig 从配置项中提取派拉配置
func parseConfig(items []GlobalConfigItem) Config {
	config := Config{}
	for _, item := range items {
		switch item.Key {
		case "appKey", "orgAppKey":
			config.AppKey = item.Value
		case "appSecret", "orgAppSecret":
			config.AppSecret = item.Value
		case "appUrl", "orgAppUrl":
			config.AppURL = item.Value
		}
	}
	return config
}

// generateToken 生成JWT令牌
func (c *PailaClient) generateToken() (string, error) {
	jti := make([]byte, 16)
	if _, err := rand.Read(jti); err != nil {
		return "", err
	}

	claims := jwt.MapClaims{
		"iss": c.clientID,
		"iat": time.Now().Unix(),
		"jti": hex.EncodeToString(jti),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(c.clientSecret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ApiResponse 表示API响应格式
type ApiResponse struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// DepartmentResponse 部门响应结构
type DepartmentResponse struct {
	List    []Department `json:"list"`
	Total   json.Number  `json:"total"`
	HasMore bool         `json:"has_more"`
}

// Department 部门信息结构
type Department struct {
	ID       string `json:"idt_org__id"`
	Name     string `json:"idt_org__name"`
	ParentID string `json:"idt_org__parent_id"`
}

// UserResponse 用户响应结构
type UserResponse struct {
	List    []User      `json:"list"`
	Total   json.Number `json:"total"`
	HasMore bool        `json:"has_more"`
}

// User 用户信息结构
type User struct {
	AccountNo   string `json:"app_account__account_no"`
	AccountName string `json:"app_account__account_name"`
	UserID      string `json:"idt_user__id"`
	AccountID   string `json:"app_account__id"`
	Email       string `json:"idt_user__email"`
	Mobile      string `json:"idt_user__mobile"`
	Orgs        []Org  `json:"orgs"`
}

// Org 组织信息结构
type Org struct {
	OrgID string `json:"idt_org__id"`
}

// GetAllDepts 获取所有部门
func (c *PailaClient) GetAllDepts() ([]*dto.ExternalDepartment, error) {
	page := 1
	size := 1000
	var allDepts []*dto.ExternalDepartment

	for {
		depts, hasMore, err := c.getDepartmentPage(page, size)
		if err != nil {
			return nil, err
		}

		allDepts = append(allDepts, depts...)
		if !hasMore {
			break
		}
		page++
	}

	return allDepts, nil
}

// getDepartmentPage 获取单页部门信息
func (c *PailaClient) getDepartmentPage(page, size int) ([]*dto.ExternalDepartment, bool, error) {
	token, err := c.generateToken()
	if err != nil {
		return nil, false, fmt.Errorf("生成令牌失败: %w", err)
	}

	reqData := map[string]interface{}{
		"page": page,
		"size": size,
	}
	c.logger.Infof("正在请求部门页面: page=%d, size=%d", page, size)

	reqJSON, err := json.Marshal(reqData)
	if err != nil {
		return nil, false, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	req, err := http.NewRequest("POST", c.host+c.deptURI, strings.NewReader(string(reqJSON)))
	if err != nil {
		return nil, false, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Authorization", "Bearer "+token)
	c.logger.Infof("部门请求URL: %s", c.host+c.deptURI)

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, false, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	var apiResp ApiResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, false, fmt.Errorf("解析响应失败: %w", err)
	}

	if apiResp.Code != "0" {
		return nil, false, fmt.Errorf("API错误: %s", apiResp.Message)
	}

	respDataBytes, _ := json.Marshal(apiResp.Data)
	c.logger.Debugf("部门API响应: %s", string(respDataBytes))

	// 解析数据部分
	respData, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, false, fmt.Errorf("序列化数据失败: %w", err)
	}

	var deptResp DepartmentResponse
	if err := json.Unmarshal(respData, &deptResp); err != nil {
		return nil, false, fmt.Errorf("解析部门数据失败: %w", err)
	}

	c.logger.Infof("部门API返回: 总数=%v, 本页数量=%d, 是否有更多=%v",
		deptResp.Total, len(deptResp.List), deptResp.HasMore)

	// 转换为系统使用的格式
	var depts []*dto.ExternalDepartment
	for _, dept := range deptResp.List {
		parentID := dept.ParentID
		// 规范化父ID: 0, -1, 空值或其他非有效ID的情况，统一设置为"0"
		if parentID == "0" || parentID == "" || parentID == "-1" ||
			parentID == "0'::character varying" || strings.Contains(parentID, "::") {
			parentID = "0"
		}

		depts = append(depts, &dto.ExternalDepartment{
			ID:       dept.ID,
			Name:     dept.Name,
			Parentid: parentID,
			UniqKey:  fmt.Sprintf("%s_%s_%s", dept.ID, dept.Name, parentID),
		})

		c.logger.Debugf("处理部门: ID=%s, Name=%s, ParentID=%s -> %s",
			dept.ID, dept.Name, dept.ParentID, parentID)
	}

	return depts, deptResp.HasMore, nil
}

// GetAllUsers 获取所有用户信息
func (c *PailaClient) GetAllUsers(rootGroupId string) ([]*dto.ExternalUser, error) {
	var allUsers []*dto.ExternalUser

	page := 1
	size := 1000
	hasMore := true

	for hasMore {
		users, more, err := c.getUserPage(page, size)
		if err != nil {
			c.logger.Errorf("获取派拉用户页面失败: %v", err)
			return nil, err
		}

		// 在这里处理每个用户的数据
		for i := range users {
			// 设置根目录ID
			users[i].LocalRootGroupID = rootGroupId

			// 处理MainDepartment
			if users[i].MainDepartment == "" {
				// MainDepartment已经在getUserPage中设置，如果仍为空，记录日志
				c.logger.Warnf("用户 %s 没有部门信息", users[i].Name)
			}
		}

		allUsers = append(allUsers, users...)
		hasMore = more
		page++
	}

	c.logger.Infof("已获取派拉用户，总数: %d", len(allUsers))
	return allUsers, nil
}

// getUserPage 获取单页用户信息
func (c *PailaClient) getUserPage(page, size int) ([]*dto.ExternalUser, bool, error) {
	token, err := c.generateToken()
	if err != nil {
		return nil, false, fmt.Errorf("生成令牌失败: %w", err)
	}

	reqData := map[string]interface{}{
		"page": page,
		"size": size,
	}

	reqJSON, err := json.Marshal(reqData)
	if err != nil {
		return nil, false, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	req, err := http.NewRequest("POST", c.host+c.userURI, strings.NewReader(string(reqJSON)))
	if err != nil {
		return nil, false, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Authorization", "Bearer "+token)

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, false, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	var apiResp ApiResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, false, fmt.Errorf("解析响应失败: %w", err)
	}

	if apiResp.Code != "0" {
		return nil, false, fmt.Errorf("API错误: %s", apiResp.Message)
	}

	// 解析数据部分
	respData, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, false, fmt.Errorf("序列化数据失败: %w", err)
	}

	var userResp UserResponse
	if err := json.Unmarshal(respData, &userResp); err != nil {
		return nil, false, fmt.Errorf("解析用户数据失败: %w", err)
	}

	// 转换为系统使用的格式
	var users []*dto.ExternalUser
	for _, user := range userResp.List {
		// 获取主部门ID，如果有
		mainDeptID := ""

		if len(user.Orgs) > 0 {
			mainDeptID = user.Orgs[0].OrgID
			// 移除未使用的depIDs收集代码
		}

		// 创建唯一键
		uniqKey := fmt.Sprintf("%s_%s_%s", user.UserID, user.AccountNo, user.AccountName)

		users = append(users, &dto.ExternalUser{
			Userid:         user.AccountNo,
			Name:           user.AccountName,
			Email:          user.Email,
			Mobile:         user.Mobile,
			MainDepartment: mainDeptID,
			UniqKey:        uniqKey,
		})
	}

	return users, userResp.HasMore, nil
}

// GetDeptsAndUsers 同时获取并关联部门和用户信息
func (c *PailaClient) GetDeptsAndUsers(rootGroupId string) ([]*dto.ExternalDepartment, []*dto.ExternalUser, error) {
	// 先获取所有用户
	users, err := c.GetAllUsers(rootGroupId)
	if err != nil {
		return nil, nil, fmt.Errorf("获取用户失败: %w", err)
	}

	// 收集所有用户引用的部门ID
	userDeptIds := make(map[string]bool)
	for _, user := range users {
		if user.MainDepartment != "" {
			userDeptIds[user.MainDepartment] = true
		}
	}
	c.logger.Infof("从用户数据中收集到 %d 个不同部门ID", len(userDeptIds))

	// 获取API返回的部门
	apiDepts, err := c.GetAllDepts()
	if err != nil {
		return nil, nil, fmt.Errorf("获取部门失败: %w", err)
	}
	c.logger.Infof("API返回部门数量: %d", len(apiDepts))

	// 将API返回的部门转换为映射
	deptsMap := make(map[string]*dto.ExternalDepartment)
	for _, dept := range apiDepts {
		// 规范化父ID
		if dept.Parentid == "0" || dept.Parentid == "" ||
			dept.Parentid == "-1" || strings.Contains(dept.Parentid, "::") {
			dept.Parentid = rootGroupId
		}

		dept.LocalRootGroupID = rootGroupId
		deptsMap[dept.ID] = dept
	}

	// 将没有匹配到的部门ID记录下来，但不创建虚拟部门
	unmatchedDeptIds := 0
	for deptId := range userDeptIds {
		if _, exists := deptsMap[deptId]; !exists {
			unmatchedDeptIds++
		}
	}

	// 转换为部门列表
	finalDepts := make([]*dto.ExternalDepartment, 0, len(deptsMap))
	for _, dept := range deptsMap {
		finalDepts = append(finalDepts, dept)
	}

	c.logger.Infof("最终部门总数: %d (API返回部门数量: %d, 未匹配部门ID数量: %d)",
		len(finalDepts), len(apiDepts), unmatchedDeptIds)

	// 修改用户的部门关联，将未匹配的部门ID改为rootGroupId
	modifiedUsers := 0
	for i, user := range users {
		if user.MainDepartment != "" {
			if _, exists := deptsMap[user.MainDepartment]; !exists {
				// 部门ID未匹配，修改为同步目录ID
				// c.logger.Debugf("用户 %s 的部门ID %s 未在API返回中找到，关联到同步目录",
				// 	user.Name, user.MainDepartment)
				users[i].MainDepartment = rootGroupId
				modifiedUsers++
			}
		}
	}

	c.logger.Infof("已修改 %d 个用户的部门关联（关联到同步目录）", modifiedUsers)

	return finalDepts, users, nil
}
