package biz

import (
	"context"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
)

type CorpRepo interface {
	CreateCorp(ctx context.Context, id, name string) error
	DeleteCorp(ctx context.Context, id string) (*model.TbCorp, error)
	UpdateCorp(ctx context.Context, id, name string) error
	GetCorpByID(ctx context.Context, id string) (*model.TbCorp, error)
	GetCorpByName(ctx context.Context, name string) (*model.TbCorp, error)
	ListCorp(ctx context.Context) ([]*model.TbCorp, error)
}

type CorpUsecase struct {
	repo CorpRepo
	log  *log.Helper
}

func NewCorpUsecase(repo CorpRepo, logger log.Logger) *CorpUsecase {
	return &CorpUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (t CorpUsecase) CreateCorp(ctx context.Context, name string) error {
	_, err := t.repo.GetCorpByName(ctx, name)
	if err != nil && !pb.IsRecordNotFound(err) {
		t.log.Errorf("GetCorpByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", name)
	}

	id := uuid.New()
	if err = t.repo.CreateCorp(ctx, id.String(), name); err != nil {
		t.log.Errorf("CreateCorp failed. err=%v", err)
		return err
	}
	return nil
}

func (t CorpUsecase) UpdateCorp(ctx context.Context, id, name string) error {
	_, err := t.repo.GetCorpByName(ctx, name)
	if err != nil && !pb.IsRecordNotFound(err) {
		t.log.Errorf("GetCorpByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", name)
	}

	err = t.repo.UpdateCorp(ctx, id, name)
	if err != nil {
		t.log.Errorf("UpdateCorp failed. err=%v", err)
		return err
	}
	return nil
}

func (t CorpUsecase) DeleteCorp(ctx context.Context, id string) error {
	_, err := t.repo.DeleteCorp(ctx, id)
	if err != nil {
		t.log.Errorf("DeleteCorp failed. err=%v", err)
		return err
	}
	return nil
}

func (t CorpUsecase) GetCorpById(ctx context.Context, id string) (dto.Corp, error) {
	corp, err := t.repo.GetCorpByID(ctx, id)
	if err != nil {
		t.log.Errorf("GetCorpByID failed. err=%v", err)
		return dto.Corp{}, err
	}
	var result dto.Corp
	err = copier.Copy(&result, corp)
	if err != nil {
		t.log.Errorf("Copy failed. err=%v", err)
		return dto.Corp{}, err
	}
	return result, nil
}

func (t CorpUsecase) GetCorpByName(ctx context.Context, name string) (dto.Corp, error) {
	corp, err := t.repo.GetCorpByName(ctx, name)
	if err != nil {
		t.log.Errorf("GetCorpByName failed. err=%v", err)
		return dto.Corp{}, err
	}
	var result dto.Corp
	err = copier.Copy(&result, corp)
	if err != nil {
		t.log.Errorf("Copy failed. err=%v", err)
		return dto.Corp{}, err
	}
	return result, nil
}

func (t CorpUsecase) ListCorp(ctx context.Context) ([]dto.Corp, error) {
	corps, err := t.repo.ListCorp(ctx)
	if err != nil {
		t.log.Errorf("ListCorp failed. err=%v", err)
		return []dto.Corp{}, err
	}
	var results []dto.Corp
	err = copier.Copy(&results, &corps)
	if err != nil {
		t.log.Errorf("copy failed. err=%v", err)
		return []dto.Corp{}, err
	}
	return results, nil
}
