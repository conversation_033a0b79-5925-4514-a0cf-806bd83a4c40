package repository

import (
	"asdsec.com/asec/platform/app/console/app/dashboard/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"time"
)

type TimeParam struct {
	StartTime time.Time
	EndTime   time.Time
}

type TimeAggData struct {
	Channel string    `gorm:"column:channel;type:varchar;comment:外发通道名称"`
	Day     time.Time `gorm:"column:day;type:timestamptz;comment:日期"`
	Count   int       `gorm:"column:count;type:bigint;comment:外发次数"`
}

type ChannelInfo struct {
	Channel     string `gorm:"column:channel;type:varchar;comment:外发通道名称"`
	Count       int    `gorm:"column:count;type:int;comment:外发次数"`
	FileSizeSum int    `gorm:"column:sum;type:int;comment:外发的总文件大小byte为单位"`
}

type SensitiveInfo struct {
	SensitiveLevel    int      `gorm:"column:sensitive_level;type:int;comment:敏感等级"`
	SensitiveRuleId   string   `gorm:"column:sensitive_rule_id;type:varchar;comment:敏感类型ID"`
	SensitiveRuleName string   `gorm:"column:sensitive_rule_name;type:varchar;comment:敏感类型名称"`
	UserName          []string `gorm:"column:user_name;type:varchar;comment:用户名"`
	Count             int      `gorm:"column:count;type:int;comment:外发次数"`
	FileSizeSum       int      `gorm:"column:sum;type:int;comment:外发的总文件大小byte为单位"`
}

type DataAccessInfo struct {
	SourceValue string   `gorm:"column:source_value;type:varchar;comment:源ID"`
	SourceName  string   `gorm:"column:source_name;type:varchar;comment:源名称"`
	SourceType  string   `gorm:"column:source_type;type:varchar;comment:源类型"`
	UserIds     []string `gorm:"column:user_ids;type:varchar;comment:用户id列表"`
	Count       int      `gorm:"column:count;type:int;comment:下载次数"`
	FileSizeSum int      `gorm:"column:sum;type:int;comment:下载的总文件大小byte为单位"`
}

type TimeAggSensitiveData struct {
	SensitiveRuleId string    `gorm:"column:sensitive_rule_id;type:varchar;comment:敏感类型ID"`
	Day             time.Time `gorm:"column:day;type:timestamptz;comment:日期"`
	Count           int       `gorm:"column:count;type:bigint;comment:外发次数"`
}

type TimeAggDataAccessData struct {
	SourceId string    `gorm:"column:source_id;type:varchar;comment:源ID"`
	Day      time.Time `gorm:"column:day;type:timestamptz;comment:日期"`
	Count    int       `gorm:"column:count;type:bigint;comment:下载次数"`
}

type DataAccessSensitiveData struct {
	SourceValue       string `gorm:"column:source_value;type:varchar;comment:源ID"`
	SensitiveLevel    int    `gorm:"column:sensitive_level;type:int;comment:敏感等级"`
	SensitiveRuleId   string `gorm:"column:sensitive_rule_id;type:varchar;comment:敏感类型ID"`
	SensitiveRuleName string `gorm:"column:sensitive_rule_name;type:varchar;comment:敏感类型名称"`
}

type FileEventsRepositoryImpl interface {
	QueryChannelInfo(ctx context.Context, param TimeParam) ([]ChannelInfo, error)
	QueryTimeAggData(ctx context.Context, param TimeParam) ([]TimeAggData, error)
	GetSensitiveDownloadData(ctx context.Context, startT, endT time.Time) ([]common.SensitiveDownloadRsp, error)
	GetSensitiveSendData(ctx context.Context, startT, endT time.Time) ([]common.SensitiveSendRsp, error)
}

func NewFileEventsRepository() FileEventsRepositoryImpl {
	return &fileEventsReposPriv{}
}

// private
type fileEventsReposPriv struct {
}

func (self *fileEventsReposPriv) GetSensitiveSendData(ctx context.Context, startT, endT time.Time) ([]common.SensitiveSendRsp, error) {
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}
	var res []common.SensitiveSendRsp
	err = db.Model(&model.FileEvents{}).
		Select("user_id,sensitive_rule_id,sensitive_rule_name,sensitive_level,file_size,toDateTime(occur_time, 'Asia/Shanghai') as occur_time").
		Where("activity = 'send' and empty(sensitive_rule_id) = 0 and toDateTime(occur_time, 'Asia/Shanghai') > ? and toDateTime(occur_time, 'Asia/Shanghai') < ? and empty(user_id) = 0 ", startT, endT).
		Find(&res).Error
	return res, err
}

func (self *fileEventsReposPriv) GetSensitiveDownloadData(ctx context.Context, startT, endT time.Time) ([]common.SensitiveDownloadRsp, error) {
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}
	var res []common.SensitiveDownloadRsp
	err = db.Model(&model.FileEvents{}).
		Select("source_id,source_name,source_type,user_id,file_size,domainWithoutWWW(src_path) as src_host,src_path,sensitive_rule_id,sensitive_rule_name,sensitive_level,toDateTime(occur_time, 'Asia/Shanghai') as occur_time").
		Where("activity = 'download' and empty(sensitive_rule_id) = 0 and toDateTime(occur_time, 'Asia/Shanghai') > ? and toDateTime(occur_time, 'Asia/Shanghai') < ? and empty(user_id) = 0 ", startT, endT).
		Find(&res).Error
	return res, err
}

func (self *fileEventsReposPriv) QueryChannelInfo(ctx context.Context, param TimeParam) ([]ChannelInfo, error) {
	var result []ChannelInfo
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&model.FileEvents{}).
		Select("channel,count(*) as count,sum(file_size) as sum").
		Where("activity = 'send' and occur_time > ? and occur_time < ?", param.StartTime, param.EndTime).
		Group("channel").
		Find(&result).Error
	return result, err
}
func (self *fileEventsReposPriv) QueryTimeAggData(ctx context.Context, param TimeParam) ([]TimeAggData, error) {
	var result []TimeAggData
	db, err := global.GetCkClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Model(&model.FileEvents{}).
		Select("channel,DATE_TRUNC('day', occur_time) as day,count(*) as count").
		Where("activity = 'send' and occur_time > ? and occur_time < ?", param.StartTime, param.EndTime).
		Group("channel,day").Order("day").Find(&result).Error
	return result, err
}
