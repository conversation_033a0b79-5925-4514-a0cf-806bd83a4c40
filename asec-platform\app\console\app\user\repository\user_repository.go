package repository

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"

	"asdsec.com/asec/platform/app/console/app/user/constants"
	"asdsec.com/asec/platform/app/console/app/user/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/auth_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserRepository 接口定义
type UserRepository interface {
	ListAuthLog(ctx context.Context, req dto.ListAuthLogReq) (model.Pagination, error)
	ListUserLoginLog(ctx context.Context, req dto.ListAuthLogReq) (model.Pagination, error)
	CreateAdminLoginLog(ctx context.Context, req dto.AdminLoginLogReq) error
	GetGroupAndUserList(ctx context.Context, corpId, keyword, groupId string) (dto.GetUserAndGroupRsp, error)
	UserEcho(c context.Context, userIds []string) ([]model.UserEcho, error)
	GroupEcho(c context.Context, groupIds []string) ([]model.GroupEcho, error)
	RoleEcho(c context.Context, roleIds []string) ([]model.RoleEcho, error)
	GetAllUserId(ctx context.Context) ([]string, error)
}

// NewUserRepository 创建接口实现接口实现
func NewUserRepository() UserRepository {
	return &userRepository{}
}

type userRepository struct {
}

func (u userRepository) GetAllUserId(ctx context.Context) ([]string, error) {
	var res []string
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}
	err = db.Table(auth_model.TableNameTbUserEntity).Select("DISTINCT id").Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (u userRepository) GroupEcho(c context.Context, groupIds []string) ([]model.GroupEcho, error) {
	_l := make([]model.GroupEcho, 0, 0)
	if len(groupIds) <= 0 {
		return _l, nil
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	err = db.Model(&auth_model.TbUserGroup{}).Select("id as group_id,name as group_name,path as group_path,'group' as type").Where("id in ?", groupIds).Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (u userRepository) RoleEcho(c context.Context, roleIds []string) ([]model.RoleEcho, error) {
	_l := make([]model.RoleEcho, 0, 0)
	if len(roleIds) <= 0 {
		return _l, nil
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	err = db.Model(&auth_model.TbRole{}).Select("id as role_id,name as role_name,'role' as type,'/' as role_path").Where("id in ?", roleIds).Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

var userEchoSql = `ue.id as user_id,
       CASE
           WHEN (ue.display_name is null or ue.display_name = '')
               THEN ue.name
           ELSE ue.display_name END as user_name,
       CASE
           WHEN ug.PATH = '/' THEN concat(ug.PATH, ug."name")
           ELSE concat(ug.PATH, '/', ug."name")
           END                      AS user_path,'user' as type`

func (u userRepository) UserEcho(c context.Context, userIds []string) ([]model.UserEcho, error) {
	_l := make([]model.UserEcho, 0, 0)
	if len(userIds) <= 0 {
		return _l, nil
	}
	db, err := global.GetDBClient(c)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	err = db.Table("tb_user_entity ue").Select(userEchoSql).Joins("INNER JOIN tb_user_group ug on ue.group_id = ug.id").Where("ue.id in ?", userIds).Find(&_l).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return _l, nil
}

func (u userRepository) GetGroupAndUserList(ctx context.Context, corpId, keyword, groupId string) (dto.GetUserAndGroupRsp, error) {
	var res dto.GetUserAndGroupRsp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	pathQuerySql := `WITH RECURSIVE group_path_tree(id, name,parent_group_id,full_path,depth) AS (
    SELECT id, name,parent_group_id,name AS full_path, 0 AS depth
    FROM tb_user_group
    UNION
    SELECT gpt.id,gpt.name,tug.parent_group_id,CASE WHEN gpt.depth = 0 THEN tug.name ELSE tug.name || '/' || gpt.full_path end,gpt.depth +1
    FROM tb_user_group tug, group_path_tree gpt
    WHERE tug.id  = gpt.parent_group_id)
 	SELECT id, name,parent_group_id,full_path,depth
	FROM group_path_tree
	where parent_group_id ='0' and depth>0`
	pathT := "group_path_tree"
	groupT := (&auth_model.TbUserGroup{}).TableName()
	sourceT := (&auth_model.TbUserSource{}).TableName()
	//Group
	var groupList []dto.UserAndGroupItem
	db = db.Model(auth_model.TbUserGroup{}).
		Select(fmt.Sprintf("%s.id,%s.name,source_type,( CASE WHEN full_path = '' OR full_path IS NULL THEN '/' ELSE '/' || %s.full_path END) AS path,'%s' AS type", groupT, groupT, pathT, constants.GroupType)).
		Joins(fmt.Sprintf("LEFT JOIN %s on %s.source_id = %s.id", sourceT, groupT, sourceT)).
		Joins(fmt.Sprintf("LEFT JOIN (%s) AS %s on %s.id = %s.id", pathQuerySql, pathT, pathT, groupT)).
		Where(fmt.Sprintf("%s.corp_id = ?", groupT), corpId)
	if keyword == "" && groupId == "" {
		db = db.Where(fmt.Sprintf("%s.parent_group_id = '0'", groupT))
	}
	if keyword != "" {
		searchStr := "%" + dbutil.EscapeForLike(keyword) + "%"
		db = db.Where(fmt.Sprintf("%s.name like ?", groupT), searchStr)
	}
	if groupId != "" {
		db = db.Where(fmt.Sprintf("%s.parent_group_id = ?", groupT), groupId)
	}
	err = db.Find(&groupList).Error
	if err != nil {
		global.SysLog.Error("get group list err", zap.Error(err))
		return nil, err
	}
	res = append(res, groupList...)
	if keyword == "" && groupId == "" {
		return res, nil
	}
	//User
	userDb, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	userT := (&auth_model.TbUserEntity{}).TableName()
	userDb = userDb.Model(auth_model.TbUserEntity{}).
		Select(fmt.Sprintf("('/' || CASE WHEN full_path = '' OR full_path IS NULL THEN %s.name ELSE %s.full_path || '/' || %s.name End) AS path,", groupT, pathT, pathT)+
			fmt.Sprintf("%s.id,%s.name,%s.display_name,'%s' AS type", userT, userT, userT, constants.UserType)).
		Joins(fmt.Sprintf("LEFT JOIN (%s) AS %s on %s.id = %s.group_id", pathQuerySql, pathT, pathT, userT)).
		Joins(fmt.Sprintf("LEFT JOIN %s on %s.id = %s.group_id", groupT, groupT, userT)).
		Where(fmt.Sprintf("%s.corp_id = ?", userT), corpId)
	if keyword != "" {
		searchStr := "%" + dbutil.EscapeForLike(keyword) + "%"
		userDb = userDb.Where(fmt.Sprintf("%s.name like ? OR %s.display_name like ?", userT, userT), searchStr, searchStr)
	}
	if groupId != "" {
		userDb = userDb.Where(fmt.Sprintf("%s.group_id = ?", userT), groupId)
	}
	var userList []dto.UserAndGroupItem
	err = userDb.Find(&userList).Error
	if err != nil {
		global.SysLog.Error("get user list err", zap.Error(err))
		return nil, err
	}
	res = append(res, userList...)
	//排序
	sort.Sort(res)
	return res, nil
}

func (u userRepository) ListUserLoginLog(ctx context.Context, req dto.ListAuthLogReq) (model.Pagination, error) {
	var res = req.Pagination
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return res, err
	}
	var logs []dto.GetLoginLogListRsp
	authLog := dto.UserLoginLog{}
	db = db.Model(&authLog)
	if req.StartTimestamp > 0 {
		db = db.Where("event_time >= ? ", req.StartTimestamp)
	}
	if req.EndTimestamp > 0 {
		db = db.Where("event_time <= ? ", req.EndTimestamp)
	}
	if req.Search != "" {
		res.SearchColumns = []string{"ip_address", "ue.name", "ue.display_name", fmt.Sprintf("%s.user_id", authLog.TableName())}
	}
	if req.Type != "" {
		db = db.Where("type=?", req.Type)
	}
	res.Sort = "event_time DESC"

	// 过滤掉user_id为空的记录，现在所有记录都应该有user_id（已知用户的ID或未知用户的用户名）
	db = db.Where("user_id is NOT NULL AND user_id != ''")

	AuthLogTypes := []string{"LOGIN", "LOGIN_ERROR", "LOGOUT"}
	db = db.Select(fmt.Sprintf("%s.*,", authLog.TableName())+
		"CASE "+
		"  WHEN ue.id IS NOT NULL THEN "+
		"    CASE WHEN (ue.display_name is null or ue.display_name ='') THEN ue.name ELSE ue.display_name END "+
		"  ELSE "+
		fmt.Sprintf("    %s.user_id ", authLog.TableName())+
		"END AS user_name,"+
		"to_timestamp(event_time/1000) as operate_time,us.name as auth_type").
		Where("type in ?", AuthLogTypes).
		Joins(fmt.Sprintf("left join tb_user_source us on us.id = %s.source_id", authLog.TableName())).
		Joins(fmt.Sprintf("left join tb_user_entity ue on ue.id = %s.user_id", authLog.TableName()))
	return model.Paginate(&logs, &res, db)
}

func (u userRepository) CreateAdminLoginLog(ctx context.Context, req dto.AdminLoginLogReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return err
	}
	id, err := snowflake.NewSnowFlake().GetId()
	if err != nil {
		global.SysLog.Error("get snow id err", zap.Error(err))
		return err
	}
	var data dto.AdminLoginLog
	copier.Copy(&data, &req)
	data.Id = strconv.FormatUint(id, 10)
	return db.Create(&data).Error

}

func (u userRepository) ListAuthLog(ctx context.Context, req dto.ListAuthLogReq) (model.Pagination, error) {
	var res = req.Pagination
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return res, err
	}
	var logs []dto.GetLoginLogListRsp
	authLog := dto.AdminLoginLog{}
	db = db.Model(&authLog).Where("ue.name is not null")
	if req.StartTimestamp > 0 {
		db = db.Where("event_time >= ? ", req.StartTimestamp)
	}
	if req.EndTimestamp > 0 {
		db = db.Where("event_time <= ? ", req.EndTimestamp)
	}
	if req.Search != "" {
		res.SearchColumns = []string{"ip_address", "ue.name"}
	}
	if req.Type != "" {
		db = db.Where("type=?", req.Type)
	}
	res.Sort = "event_time DESC"

	//过滤掉没有用户ID的数据，但是保留用户名不存在的场景
	db = db.Where("user_id is NOT NULL OR error='user_not_found'")

	AuthLogTypes := []string{"LOGIN", "LOGIN_ERROR", "LOGOUT"}
	db = db.Select(fmt.Sprintf("%s.*,ue.name as user_name,to_timestamp(event_time/1000) as operate_time", authLog.TableName())).
		Where("type in ?", AuthLogTypes).
		Joins(fmt.Sprintf("left join tb_admin_entity ue on ue.id = %s.user_id", authLog.TableName()))
	return model.Paginate(&logs, &res, db)

}
