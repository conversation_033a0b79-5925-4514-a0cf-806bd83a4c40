package bamboocloud

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
)

const contentType = "application/json"

const base = "https://iam.pbwear.com"

func FormatUrl(path string) string {
	return base + path
}

func DoGetToken(ctx context.Context, code string) (*TokenResp, error) {
	tokenResp := &TokenResp{}

	client := resty.New()
	resp, err := client.R().SetQueryParams(map[string]string{"client_id": ClientId,
		"client_secret": ClientSecret,
		"code":          code,
		"grant_type":    GrantType}).SetResult(&TokenResp{}).Post(FormatUrl(GetTokenUrl))
	if err != nil {
		return tokenResp, err
	}
	tokenResp = resp.Result().(*TokenResp)
	if tokenResp.ErrorCode != "" {
		log.Errorf("DoGetToken for code:%s failed,error code:%s msg: %s", code, tokenResp.ErrorCode, tokenResp.Msg)
		return tokenResp, errors.New(tokenResp.Msg)
	}
	return tokenResp, nil
}

func GetUserInfo(accessToken string) (string, error) {
	client := resty.New()
	res := &UserInfoResp{}
	resp, err := client.R().SetQueryParam("client_id", ClientId).
		SetQueryParam("access_token", accessToken).
		SetError(&UserInfoResp{}).
		Get(FormatUrl(GetUserInfoUrl))
	if err != nil {
		log.Errorf("get user info for token:%s failed,error ：%s", accessToken, err)
		return "", err
	}
	//res = resp.Result().(*UserInfoResp)
	err = json.Unmarshal(resp.Body(), res)
	if err != nil {
		return "", err
	}
	if res.ErrorCode != "" {
		log.Errorf("get user info for token:%s failed,error code:%s msg: %s", accessToken, res.ErrorCode, res.Msg)
		return "", errors.New(res.Msg)
	}
	name := ""
	if len(res.SpRoleList) > 0 {
		name = res.SpRoleList[0]
	}
	return name, err
}
