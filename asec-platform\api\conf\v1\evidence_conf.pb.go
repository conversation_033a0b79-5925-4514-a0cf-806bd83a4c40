// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/evidence_conf.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 截图相关配置
type EvidenceScreenshotConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 截图开关
	ScreenshotSwitch bool `protobuf:"varint,1,opt,name=screenshot_switch,json=screenshotSwitch,proto3" json:"screenshot_switch,omitempty"`
	// 取证策略 1(滚动) 2(瞬时)
	EvidenceStrategy uint32 `protobuf:"varint,2,opt,name=evidence_strategy,json=evidenceStrategy,proto3" json:"evidence_strategy,omitempty"`
	// 取证事件发生前时间区段
	PreTime uint32 `protobuf:"varint,3,opt,name=pre_time,json=preTime,proto3" json:"pre_time,omitempty"`
	// 取证事件发生后时间区段
	PostTime uint32 `protobuf:"varint,4,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	// 帧率(frame/second)
	Fps float32 `protobuf:"fixed32,5,opt,name=fps,proto3" json:"fps,omitempty"`
	// 截图留存最大限制 (K)
	EvidenceFileSizeLimit uint64 `protobuf:"varint,6,opt,name=evidence_file_size_limit,json=evidenceFileSizeLimit,proto3" json:"evidence_file_size_limit,omitempty"`
	// 分辨率宽
	ResolutionRatioW uint32 `protobuf:"varint,7,opt,name=resolution_ratio_w,json=resolutionRatioW,proto3" json:"resolution_ratio_w,omitempty"`
	// 分辨率高
	ResolutionRatioH uint32 `protobuf:"varint,8,opt,name=resolution_ratio_h,json=resolutionRatioH,proto3" json:"resolution_ratio_h,omitempty"`
	// 截图格式（png，jpg）
	PicFormat string `protobuf:"bytes,9,opt,name=pic_format,json=picFormat,proto3" json:"pic_format,omitempty"`
}

func (x *EvidenceScreenshotConf) Reset() {
	*x = EvidenceScreenshotConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_evidence_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvidenceScreenshotConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceScreenshotConf) ProtoMessage() {}

func (x *EvidenceScreenshotConf) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_evidence_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceScreenshotConf.ProtoReflect.Descriptor instead.
func (*EvidenceScreenshotConf) Descriptor() ([]byte, []int) {
	return file_conf_v1_evidence_conf_proto_rawDescGZIP(), []int{0}
}

func (x *EvidenceScreenshotConf) GetScreenshotSwitch() bool {
	if x != nil {
		return x.ScreenshotSwitch
	}
	return false
}

func (x *EvidenceScreenshotConf) GetEvidenceStrategy() uint32 {
	if x != nil {
		return x.EvidenceStrategy
	}
	return 0
}

func (x *EvidenceScreenshotConf) GetPreTime() uint32 {
	if x != nil {
		return x.PreTime
	}
	return 0
}

func (x *EvidenceScreenshotConf) GetPostTime() uint32 {
	if x != nil {
		return x.PostTime
	}
	return 0
}

func (x *EvidenceScreenshotConf) GetFps() float32 {
	if x != nil {
		return x.Fps
	}
	return 0
}

func (x *EvidenceScreenshotConf) GetEvidenceFileSizeLimit() uint64 {
	if x != nil {
		return x.EvidenceFileSizeLimit
	}
	return 0
}

func (x *EvidenceScreenshotConf) GetResolutionRatioW() uint32 {
	if x != nil {
		return x.ResolutionRatioW
	}
	return 0
}

func (x *EvidenceScreenshotConf) GetResolutionRatioH() uint32 {
	if x != nil {
		return x.ResolutionRatioH
	}
	return 0
}

func (x *EvidenceScreenshotConf) GetPicFormat() string {
	if x != nil {
		return x.PicFormat
	}
	return ""
}

// 原始事件取证相关配置
type EvidenceRetainFileConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文件存储开关
	FileStoreSwitch bool `protobuf:"varint,1,opt,name=file_store_switch,json=fileStoreSwitch,proto3" json:"file_store_switch,omitempty"`
	// 单个原始原件最大大小限制 (K)
	SingleFileSizeLimit uint64 `protobuf:"varint,2,opt,name=single_file_size_limit,json=singleFileSizeLimit,proto3" json:"single_file_size_limit,omitempty"`
	// 原始原件最大存储限制 (K)
	StoreFileSizeLimit uint64 `protobuf:"varint,3,opt,name=store_file_size_limit,json=storeFileSizeLimit,proto3" json:"store_file_size_limit,omitempty"`
}

func (x *EvidenceRetainFileConf) Reset() {
	*x = EvidenceRetainFileConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_evidence_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvidenceRetainFileConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceRetainFileConf) ProtoMessage() {}

func (x *EvidenceRetainFileConf) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_evidence_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceRetainFileConf.ProtoReflect.Descriptor instead.
func (*EvidenceRetainFileConf) Descriptor() ([]byte, []int) {
	return file_conf_v1_evidence_conf_proto_rawDescGZIP(), []int{1}
}

func (x *EvidenceRetainFileConf) GetFileStoreSwitch() bool {
	if x != nil {
		return x.FileStoreSwitch
	}
	return false
}

func (x *EvidenceRetainFileConf) GetSingleFileSizeLimit() uint64 {
	if x != nil {
		return x.SingleFileSizeLimit
	}
	return 0
}

func (x *EvidenceRetainFileConf) GetStoreFileSizeLimit() uint64 {
	if x != nil {
		return x.StoreFileSizeLimit
	}
	return 0
}

type AdditionConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ak                string `protobuf:"bytes,1,opt,name=ak,proto3" json:"ak,omitempty"`
	Sk                string `protobuf:"bytes,2,opt,name=sk,proto3" json:"sk,omitempty"`
	CorpId            string `protobuf:"bytes,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	OssType           string `protobuf:"bytes,4,opt,name=oss_type,json=ossType,proto3" json:"oss_type,omitempty"`
	Endpoint          string `protobuf:"bytes,5,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	Bucket            string `protobuf:"bytes,6,opt,name=bucket,proto3" json:"bucket,omitempty"`
	CleanMode         uint32 `protobuf:"varint,7,opt,name=clean_mode,json=cleanMode,proto3" json:"clean_mode,omitempty"`
	ExpireDay         uint32 `protobuf:"varint,8,opt,name=expire_day,json=expireDay,proto3" json:"expire_day,omitempty"`
	Enable            uint32 `protobuf:"varint,9,opt,name=enable,proto3" json:"enable,omitempty"`
	Key               string `protobuf:"bytes,10,opt,name=key,proto3" json:"key,omitempty"`
	Random            string `protobuf:"bytes,11,opt,name=random,proto3" json:"random,omitempty"`
	BucketType        string `protobuf:"bytes,12,opt,name=bucket_type,json=bucketType,proto3" json:"bucket_type,omitempty"`
	Region            string `protobuf:"bytes,13,opt,name=region,proto3" json:"region,omitempty"`
	MaxRetainFileSize uint32 `protobuf:"varint,14,opt,name=max_retain_file_size,json=maxRetainFileSize,proto3" json:"max_retain_file_size,omitempty"`
	DeployType        string `protobuf:"bytes,15,opt,name=deploy_type,json=deployType,proto3" json:"deploy_type,omitempty"`
}

func (x *AdditionConf) Reset() {
	*x = AdditionConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_evidence_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionConf) ProtoMessage() {}

func (x *AdditionConf) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_evidence_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionConf.ProtoReflect.Descriptor instead.
func (*AdditionConf) Descriptor() ([]byte, []int) {
	return file_conf_v1_evidence_conf_proto_rawDescGZIP(), []int{2}
}

func (x *AdditionConf) GetAk() string {
	if x != nil {
		return x.Ak
	}
	return ""
}

func (x *AdditionConf) GetSk() string {
	if x != nil {
		return x.Sk
	}
	return ""
}

func (x *AdditionConf) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *AdditionConf) GetOssType() string {
	if x != nil {
		return x.OssType
	}
	return ""
}

func (x *AdditionConf) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *AdditionConf) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *AdditionConf) GetCleanMode() uint32 {
	if x != nil {
		return x.CleanMode
	}
	return 0
}

func (x *AdditionConf) GetExpireDay() uint32 {
	if x != nil {
		return x.ExpireDay
	}
	return 0
}

func (x *AdditionConf) GetEnable() uint32 {
	if x != nil {
		return x.Enable
	}
	return 0
}

func (x *AdditionConf) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AdditionConf) GetRandom() string {
	if x != nil {
		return x.Random
	}
	return ""
}

func (x *AdditionConf) GetBucketType() string {
	if x != nil {
		return x.BucketType
	}
	return ""
}

func (x *AdditionConf) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AdditionConf) GetMaxRetainFileSize() uint32 {
	if x != nil {
		return x.MaxRetainFileSize
	}
	return 0
}

func (x *AdditionConf) GetDeployType() string {
	if x != nil {
		return x.DeployType
	}
	return ""
}

var File_conf_v1_evidence_conf_proto protoreflect.FileDescriptor

var file_conf_v1_evidence_conf_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0xf0, 0x02, 0x0a, 0x16, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x73, 0x68, 0x6f, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x73, 0x68, 0x6f, 0x74,
	0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x73, 0x68, 0x6f, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12,
	0x2b, 0x0a, 0x11, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x65, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x70, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x70, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x03, 0x66, 0x70, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x15, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x2c, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x5f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x72, 0x65, 0x73,
	0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x57, 0x12, 0x2c, 0x0a,
	0x12, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x5f, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x72, 0x65, 0x73, 0x6f, 0x6c,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x48, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x69, 0x63, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x69, 0x63, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x16, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x46, 0x69, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x12, 0x33, 0x0a, 0x16, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x13, 0x73, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xa1, 0x03, 0x0a, 0x0c, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x6b,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x61, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f,
	0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72,
	0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x79,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61,
	0x6e, 0x64, 0x6f, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x64,
	0x6f, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x14, 0x6d,
	0x61, 0x78, 0x5f, 0x72, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x52, 0x65,
	0x74, 0x61, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x29, 0x5a,
	0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63,
	0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_evidence_conf_proto_rawDescOnce sync.Once
	file_conf_v1_evidence_conf_proto_rawDescData = file_conf_v1_evidence_conf_proto_rawDesc
)

func file_conf_v1_evidence_conf_proto_rawDescGZIP() []byte {
	file_conf_v1_evidence_conf_proto_rawDescOnce.Do(func() {
		file_conf_v1_evidence_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_evidence_conf_proto_rawDescData)
	})
	return file_conf_v1_evidence_conf_proto_rawDescData
}

var file_conf_v1_evidence_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_conf_v1_evidence_conf_proto_goTypes = []interface{}{
	(*EvidenceScreenshotConf)(nil), // 0: api.conf.EvidenceScreenshotConf
	(*EvidenceRetainFileConf)(nil), // 1: api.conf.EvidenceRetainFileConf
	(*AdditionConf)(nil),           // 2: api.conf.AdditionConf
}
var file_conf_v1_evidence_conf_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_conf_v1_evidence_conf_proto_init() }
func file_conf_v1_evidence_conf_proto_init() {
	if File_conf_v1_evidence_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_evidence_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvidenceScreenshotConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_evidence_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvidenceRetainFileConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_evidence_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_evidence_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_evidence_conf_proto_goTypes,
		DependencyIndexes: file_conf_v1_evidence_conf_proto_depIdxs,
		MessageInfos:      file_conf_v1_evidence_conf_proto_msgTypes,
	}.Build()
	File_conf_v1_evidence_conf_proto = out.File
	file_conf_v1_evidence_conf_proto_rawDesc = nil
	file_conf_v1_evidence_conf_proto_goTypes = nil
	file_conf_v1_evidence_conf_proto_depIdxs = nil
}
