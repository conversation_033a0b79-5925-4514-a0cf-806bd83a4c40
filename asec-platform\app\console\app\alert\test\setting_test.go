package test

import (
	"asdsec.com/asec/platform/app/console/app/alert/service"
	"context"
	"encoding/json"
	"testing"
	"time"
)

var _ctx = context.Background()

func TestUpdate(t *testing.T) {
	tmp := map[string]int{
		"high":   1,
		"middle": 1,
		"low":    0,
	}
	marshal, err := json.Marshal(tmp)
	if err != nil {
		return
	}
	alertSetting := map[string]interface{}{
		"event_name":   "webshell",
		"threshold":    string(marshal),
		"is_qy_wx":     -1,
		"is_ding_talk": 0,
		"update_time":  time.Now(),
	}
	err = service.GetSettingService().SaveAlertSetting(_ctx, 1, alertSetting)
	if err != nil {
		t.Log(err.<PERSON>r())
	}
}

func TestGetSetting(t *testing.T) {
	res, err := service.GetSettingService().GetAlertSetting(_ctx, 1201)
	if err != nil {
		t.Log(err.<PERSON><PERSON><PERSON>())
	}
	t.Log(res)
}
