#user  nobody;
worker_processes auto;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;
events
{
	worker_connections 20480;
}

http {
    proxy_buffer_size 128k;
    server_tokens off;
    proxy_buffers 32 32k;
    proxy_busy_buffers_size 128k;

    include mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;

    gzip on;
    gzip_disable "MSIE [1-6].(?!.*SV1)";
    gzip_http_version 1.1;
    gzip_vary on;
    gzip_proxied any;
    gzip_min_length 5000;
    gzip_buffers 16 8k;
    gzip_comp_level 6;
    gzip_types text/css text/xml text/plain text/javascript
               application/javascript application/json application/xml
               application/rss+xml application/xhtml+xml;

    # 后端服务定义
    upstream backend_console       { server 127.0.0.1:9000  weight=2; }
    upstream backend_appliance     { server 127.0.0.1:9011  weight=2; }
    upstream backend_auth          { server 127.0.0.1:9009  weight=2; }
    upstream backend_storage       { server 127.0.0.1:9191  weight=2; }
    upstream backend_loki          { server 127.0.0.1:9100  weight=2; }
    upstream backend_panel         { server 127.0.0.1:18080 weight=2; }
    upstream backend_sync          { server 127.0.0.1:8091  weight=2; }
    upstream grpc_appliance        { server 127.0.0.1:9002  weight=2; }
    upstream grpc_log_center       { server 127.0.0.1:9004  weight=2; }

    # Web Console 服务
    server {
        listen 4430 ssl;
        server_name localhost;

        ssl_certificate ssl/server.crt;
        ssl_certificate_key ssl/server.key;
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout 5m;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;

        client_max_body_size 500m;
        autoindex_localtime on;

        location / {
            add_header X-Corp-ID 'default';
            access_log off;
            root /opt/front/asec/web;
            index index.html;
        }

        location /static/icon/ {
            alias /opt/platform/icon/;
        }

        location /panel {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            rewrite ^/panel/(.*)$ /system/v1/$1 break;
            proxy_pass http://backend_panel;
        }

        location /console {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_pass http://backend_console;
        }

        location /auth {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_pass http://backend_auth;
        }

        location /appliance {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_pass http://backend_appliance;
        }

        location /sync/ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_pass http://backend_sync;
        }

        location ^~ /asec-evidence- {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 1000;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_pass https://backend_storage;
        }
    }

    # Web Portal 服务
    server {
        listen 10443 ssl proxy_protocol;
        server_name localhost;

        ssl_certificate ssl/server.crt;
        ssl_certificate_key ssl/server.key;
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout 5m;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;

        ignore_invalid_headers off;
        client_max_body_size 0;

        location / {
            add_header X-Corp-ID 'default';
            root /opt/front/asec/portal;
            access_log off;
            index index.html;
        }

        location /auth_callback {
            alias /opt/front/asec/portal/auth_callback;
            try_files /index.html =404;
        }

        location /wx_oauth_callback {
            alias /opt/front/asec/portal/auth;
            try_files /index.html =404;
        }

        location /wx_oauth_js {
            alias /opt/front/asec/portal/auth;
        }
	location /static/icon/
	{
		alias /opt/platform/icon/;
	}
        include conf.d/wechat-verify-locations.conf;

        location /oauth2_callback {
            alias /opt/front/asec/portal/auth;
            try_files /oauth2_redirect.html =404;
        }

        location /console {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_protocol_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_pass http://backend_console;
        }

        location /auth {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_protocol_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_pass http://backend_auth;
        }

        location /appliance {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_protocol_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_pass http://backend_appliance;
        }

        location /loki {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_protocol_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_pass http://backend_loki;
        }

        location ^~ /asec-evidence- {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_protocol_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 1000;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;
            proxy_pass https://backend_storage;
        }
    }

    # gRPC 服务 (新客户端)
    server {
        listen 19443 ssl http2 proxy_protocol;
        ssl_certificate ssl/server.crt;
        ssl_certificate_key ssl/server.key;

        # 设置客户端请求体大小限制，解决 413 错误
        client_max_body_size 10m;

        # grpc_appliance
        location /api.appliance.ApplianceMgt           { grpc_pass grpc://grpc_appliance; }
        location /asdsec.core.api.app.App              { grpc_pass grpc://grpc_appliance; }
        location /asdsec.core.api.app.GetApp           { grpc_pass grpc://grpc_appliance; }
        location /asdsec.core.api.app.ScanTask         { grpc_pass grpc://grpc_appliance; }
        location /api.strategy.GetSenElem              { grpc_pass grpc://grpc_appliance; }
        location /api.asdsec.agentcontrol.AgentControl { grpc_pass grpc://grpc_appliance; }
        location /api.appliance.ApplianceCfg           { grpc_pass grpc://grpc_appliance; }
        location /api.appliance.ConfCenter             { grpc_pass grpc://grpc_appliance; }
        location /api.specialconfig.SpecialConfig      { grpc_pass grpc://grpc_appliance; }
        location /api.appliance.ReportCollectedInfo    { grpc_pass grpc://grpc_appliance; }

        # grpc_log_center
        location /api.asdsec.file_event.Event          { grpc_pass grpc://grpc_log_center; }
        location /api.accesslog.AccessLogService       { grpc_pass grpc://grpc_log_center; }
        location /api.asdsec.file_event.DlpAlertReport { grpc_pass grpc://grpc_log_center; }
    }


}

stream {
    upstream grpc_backend { server 127.0.0.1:19443; }
    upstream http_backend { server 127.0.0.1:10443; }

    map $ssl_preread_alpn_protocols $backend {
        default http_backend;
        h2      grpc_backend;
    }

    server {
        listen 443;
        ssl_preread on;
        proxy_protocol on;
        proxy_pass $backend;
    }
}
