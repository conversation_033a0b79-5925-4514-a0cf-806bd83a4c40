package dto

import (
	"github.com/jackc/pgtype"
	"github.com/lib/pq"
)

type AppInfoDto struct {
	AppId    uint64
	AppName  string
	Port     string
	Address  string
	Protocol string
}

type WebAppInfoDto struct {
	Id             uint64
	AppName        string
	AppType        string
	PublishAddress string
	PublishSchema  string
	Uri            string
	ServerAddress  string
	IdpId          string
	AppStatus      int32 // 应用状态：1=启用，2=维护中，3=禁用
	HealthConfig   pgtype.JSONB
}

type WebAccessInfoDto struct {
	StrategyId          uint64
	StrategyName        string         `gorm:"column:strategy_name;type:varchar;comment:策略名称"`
	UserIds             pq.StringArray `gorm:"column:user_ids;type:varchar[];comment:用户ids"`
	UserGroupIds        pq.StringArray `gorm:"column:user_group_ids;type:varchar[];comment:用户分组ids"`
	RoleIds             pq.StringArray `gorm:"column:role_ids;type:varchar[];comment:用户角色ids"`
	ExcludeUserIds      pq.StringArray `gorm:"column:exclude_user_ids;comment:'排除用户';type:string" json:"exclude_user_ids,omitempty"`
	ExcludeUserGroupIds pq.StringArray `gorm:"column:exclude_user_group_ids;type:string" json:"exclude_user_group_ids,omitempty"`
	ExcludeUserRoleIds  pq.StringArray `gorm:"column:exclude_user_role_ids;type:string" json:"exclude_user_role_ids,omitempty"`
	AppIds              pq.Int64Array  `gorm:"column:app_ids;type:bigint[];comment:应用ids"`
	AppGroupIds         pq.Int64Array  `gorm:"column:app_group_ids;type:bigint[];comment:应用分组ids"`
	StartTime           string         `gorm:"column:start_time;type:varchar;comment:生效时间起"`
	EndTime             string         `gorm:"column:end_time;type:varchar;comment:生效时间止"`
	EnableLog           int            `gorm:"column:enable_log;type:int4;comment:是否启用日志 1启用 2禁用"`
	Action              string         `gorm:"column:action;comment:'执行动作类型'" json:"action,omitempty"`
	Priority            int            `gorm:"column:priority;comment:'优先级'" json:"priority,omitempty"`
	RegoFile            string         `gorm:"column:rego_file;comment:'rego动态执行文件'" json:"rego_file,omitempty"`
	TimeId              string         `gorm:"column:time_id;comment:'时间选择模板id'" json:"time_id,omitempty"`
	EnableAllUser       int            `gorm:"column:enable_all_user;type:int4;comment:是否启用全部用户 1是 2否"`
	EnableAllApp        int            `gorm:"column:enable_all_app;type:int4;comment:是否启用全部应用 1是 2否"`
	UserRiskRule        pgtype.JSONB   `gorm:"column:user_risk_rule;comment:'用户评分约束';default:'{}'::jsonb" json:"-"`

	IntervalType   int    `gorm:"column:interval_type" json:"interval_type,omitempty"`
	Sunday         bool   `gorm:"column:sunday" json:"sunday,omitempty"`
	Monday         bool   `gorm:"column:monday" json:"monday,omitempty"`
	Tuesday        bool   `gorm:"column:tuesday" json:"tuesday,omitempty"`
	Wednesday      bool   `gorm:"column:wednesday" json:"wednesday,omitempty"`
	Thursday       bool   `gorm:"column:thursday" json:"thursday,omitempty"`
	Friday         bool   `gorm:"column:friday" json:"friday,omitempty"`
	Saturday       bool   `gorm:"column:saturday" json:"saturday,omitempty"`
	DailyStartTime string `gorm:"column:daily_start_time" json:"daily_start_time,omitempty"`
	DailyEndTime   string `gorm:"column:daily_end_time" json:"daily_end_time,omitempty"`
	AllDayEnable   bool   `gorm:"column:all_day_enable" json:"all_day_enable,omitempty"`
	GapName        string `gorm:"column:gap_name" json:"gap_name,omitempty"`
}

type UciUserInfo struct {
	UserId    string `gorm:"column:user_id" json:"user_id"`
	Score     uint32 `gorm:"column:score" json:"score"`
	RiskLevel uint32 `gorm:"column:risk_level" json:"risk_level"`
}

type ApplicationGatewayDto struct {
	Id                  string       `gorm:"column:id" json:"id"`
	AppName             string       `gorm:"column:app_name" json:"app_name"`
	AppStatus           int32        `gorm:"column:app_status" json:"app_status"`
	AppType             string       `gorm:"column:app_type" json:"app_type"`
	ServerAddress       string       `gorm:"column:server_address" json:"server_address"`
	ServerSchema        string       `gorm:"column:server_schema" json:"server_schema"`
	PublishAddress      string       `gorm:"column:publish_address" json:"publish_address"`
	PublishSchema       string       `gorm:"column:publish_schema" json:"publish_schema"`
	PlatformHost        string       `gorm:"column:platform_host" json:"platform_host"`
	WebCompatibleConfig pgtype.JSONB `gorm:"column:web_compatible_config" json:"web_compatible_config"`
	HealthConfig        pgtype.JSONB `gorm:"column:health_config" json:"health_config"`
	Uri                 string       `gorm:"column:uri" json:"uri"`
}

type CrtGatewayDto struct {
	Id          string         `gorm:"column:id" json:"id"`
	Certificate string         `gorm:"column:certificate" json:"certificate"`
	PrivateKey  string         `gorm:"column:private_key" json:"private_key"`
	Domain      pq.StringArray `gorm:"column:domain;type:varchar" json:"domain"`
}

type WebWatermarkDto struct {
	Secret              bool      `json:"secret"`
	Enable              bool      `json:"enable"`
	AppIds              []string  `json:"app_ids"`
	AppGroupIds         []string  `json:"app_group_ids"`
	EnableAllApp        bool      `json:"enable_all_app"`
	EnableAllAppGroup   bool      `json:"enable_all_app_group"`
	UserAll             bool      `json:"user_all"` // 是否针对所有用户
	UserIds             []string  `json:"user_ids"`
	GroupIds            []string  `json:"group_ids"`
	ApplianceIds        []string  `json:"appliance_ids"`
	ExcludeUserAll      bool      `json:"exclude_user_all"` // 是否排除用户 true：排除用户、组、设备 false: 不排除用户、组、设备
	ExcludeUserIds      []string  `json:"exclude_user_ids"`
	ExcludeGroupIds     []string  `json:"exclude_group_ids"`
	ExcludeApplianceIds []string  `json:"exclude_appliance_ids"`
	ContentConfig       []string  `json:"content_config"`
	CustomText          string    `json:"custom_text"`
	FontStyle           FontStyle `json:"font_style"`
}
type FontStyle struct {
	Color          string `json:"color"`           // 字体颜色
	Alpha          int8   `json:"alpha"`           //透明度
	Size           int8   `json:"size"`            // 字体大小
	Rotate         int8   `json:"rotate"`          //旋转角度
	LineSpacing    int32  `json:"line_spacing"`    //行间距
	ColumnsSpacing int32  `json:"columns_spacing"` //列间距
}

type WaterConf struct {
	Type  string `gorm:"column:type" json:"type"`
	Value string `gorm:"column:value" json:"value"`
	Key   string `gorm:"column:key" json:"key"`
}
