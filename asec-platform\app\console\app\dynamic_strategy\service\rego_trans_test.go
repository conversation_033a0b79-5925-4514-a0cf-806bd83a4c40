package service

import (
	"asdsec.com/asec/platform/pkg/model"
	"fmt"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"testing"
)

func TestDynamicRuleToRego(t *testing.T) {
	dsn := "host=************* user=asec password=pg@asd@1234! dbname=asec_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	require.Truef(t, err == nil, "open db err")

	testId := 479236916546371595
	strategy := model.AccessStrategy{}
	err = db.Model(&model.AccessStrategy{}).Where("id = ?", testId).Find(&strategy).Error
	require.Truef(t, err == nil, "db err")

	rego, err := DynamicRuleToRego(strategy.DynamicRule)
	if err != nil {
		fmt.Println(err.Error())
	}
	require.Truef(t, err == nil, "trans err")

	strategy.RegoFile = rego

	err = db.Omit("created_at").Updates(&strategy).Error
	require.Truef(t, err == nil, "save err")

}
