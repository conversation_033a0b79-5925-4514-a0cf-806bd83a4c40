package server

import (
	control "asdsec.com/asec/platform/api/agentcontrol/v1"
	apV1 "asdsec.com/asec/platform/api/appliance/v1"
	app "asdsec.com/asec/platform/api/application/v1"
	specialConfig "asdsec.com/asec/platform/api/specialconfig/v1"
	strategy "asdsec.com/asec/platform/api/strategy/v1"
	system "asdsec.com/asec/platform/api/system/v1"
	"asdsec.com/asec/platform/app/appliance-center/internal/conf"
	"asdsec.com/asec/platform/pkg/service"
	"asdsec.com/asec/platform/pkg/service/cfg_service"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/grpc"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server,
	appliance *service.ApplianceMgtService,
	appService *service.AppService,
	agentAppService *service.AgentAppService,
	strategyService *service.SenElmService,
	controlService *service.ControlService,
	systemService *service.ES256KeyService,
	cfgService *cfg_service.ApplianceCfgService,
	specialConfigService *service.SpecialConfigService,
	scanTaskService *service.ScanTaskService,
	logger log.Logger) *grpc.Server {
	var opts = []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	apV1.RegisterApplianceMgtServer(srv, appliance)
	app.RegisterAppServer(srv, appService)
	app.RegisterGetAppServer(srv, agentAppService)
	strategy.RegisterGetSenElemServer(srv, strategyService)
	app.RegisterScanTaskServer(srv, scanTaskService)
	control.RegisterAgentControlServer(srv, controlService)
	system.RegisterAgentGetES256KeyServer(srv, systemService)
	apV1.RegisterApplianceCfgServer(srv, cfgService)
	specialConfig.RegisterSpecialConfigServer(srv, specialConfigService)
	apV1.RegisterConfCenterServer(srv, cfgService)
	apV1.RegisterReportCollectedInfoServer(srv, appliance)

	return srv
}
