package webauth

import (
	"context"
	"encoding/json"
	"fmt"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
)

// WebAuthProvider 实现多步WebAuth认证
type WebAuthProvider struct {
	providerId string
	name       string
	executor   *StepExecutor
	logger     *log.Helper

	// WebAuth配置
	userConfigs   []Step
	// 测试模式
	isTestMode bool // 是否为测试模式
}

// NewWebAuthProvider 创建WebAuth提供商实例
func NewWebAuthProvider(
	providerId string,
	name string,
	userData string,
	logger log.Logger,
) (*WebAuthProvider, error) {
	// 解析用户信息配置
	var userConfigs []Step
	if err := json.Unmarshal([]byte(userData), &userConfigs); err != nil {
		return nil, fmt.Errorf("解析用户信息配置失败: %v", err)
	}
	// 创建执行器
	executor := NewStepExecutor(logger)

	return &WebAuthProvider{
		providerId:    providerId,
		name:          name,
		executor:      executor,
		logger:        log.NewHelper(logger),
		userConfigs:    userConfigs,
	}, nil
}

// HandleAuthorization 处理WebAuth授权认证
func (p *WebAuthProvider) HandleAuthorization(username, password string, ctx context.Context) (*dto.ExternalUser, error) {
	// 1. 设置执行器的环境变量的用户名和密码
	p.executor.SetEnvVar("UserName", username)
	p.executor.SetEnvVar("Password", password)
	p.executor.SetGlobalVars(make(map[string]string))


	// 2. 执行所有配置的步骤
	for i, step := range p.userConfigs {
		// 处理变量替换
		processedStep := p.executor.PreprocessStep(step)

		// 执行步骤
		if err := p.executor.ExecuteStepOne(ctx, processedStep, i); err != nil {
			return nil, err
		}
	}

	// 3. 设置初始用户变量
	userInfo := p.executor.GetUserInfo()
	if userInfo["{User.Userid}"] == nil { // 兼容旧版本
		p.executor.SetUserInfo("Userid", username)
	}
	p.executor.SetUserInfo("Name", username)

	// 4. 将收集到的用户信息转换为标准格式
	return p.executor.ConvertToExternalUser(), nil
}

func (p *WebAuthProvider) GetExecutor() *StepExecutor {
	return p.executor
}