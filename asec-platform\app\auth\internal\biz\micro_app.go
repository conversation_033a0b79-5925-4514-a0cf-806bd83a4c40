package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp/dingtalk"
	"asdsec.com/asec/platform/app/auth/internal/idp/feishu"
	"asdsec.com/asec/platform/app/auth/internal/idp/qiyewx"
)

// HandleMicroAppAuth 处理微应用认证
func (a *AuthUsecase) HandleMicroAppAuth(ctx context.Context, param dto.MicroAppAuthParam) (*dto.MicroAppAuthResult, error) {
	// 初始化变量用于日志记录
	var systemCorpId string
	var idp *model.TbIdentityProvider
	var user *model.TbUserEntity
	var loginSuccess bool

	// 延迟执行登录日志记录
	defer func() {
		if systemCorpId != "" && idp != nil {
			var userName string
			var errMsg string
			var desc string

			if user != nil {
				userName = user.DisplayName
				if userName == "" {
					userName = user.Name
				}
			} else {
				userName = "未知用户"
			}

			if loginSuccess {
				desc = fmt.Sprintf("微应用认证成功 - %s", param.AuthProvider)
			} else {
				desc = fmt.Sprintf("微应用认证失败 - %s", param.AuthProvider)
				errMsg = desc
			}

			loginLogParam := dto.CreateLoginLogParam{
				Id:          uuid.New().String(),
				CorpId:      systemCorpId,
				ClientId:    dto.AuthLoginLog,
				Error:       errMsg,
				IpAddress:   common.GetClientHost(ctx),
				EventTime:   time.Now().UnixMilli(),
				Type:        "LOGIN",
				UserName:    userName,
				SourceId:    idp.SourceID,
				Description: desc,
			}

			err := a.CreateUserLoginLog(ctx, loginLogParam)
			if err != nil {
				a.log.Errorf("记录微应用登录日志失败: %v", err)
			}
		}
	}()

	// a.log.Infof("处理微应用认证: provider=%s, code=%s, corp_id=%s, idp_id=%s",
	// 	param.AuthProvider, param.AuthCode, param.CorpId, param.IdpId)
	// 注意：这里的 param.CorpId 是第三方微应用的企业ID（如钉钉的corp_id）
	// 我们需要通过当前请求上下文获取我们自己系统的企业ID

	// 1. 获取系统企业ID
	var err error
	systemCorpId, err = common.GetCorpId(ctx)
	if err != nil {
		a.log.Errorf("获取系统企业ID失败: %v", err)
		return nil, fmt.Errorf("获取系统企业ID失败: %v", err)
	}

	// a.log.Infof("系统企业ID: %s, 第三方企业ID: %s", systemCorpId, param.CorpId)

	// 2. 根据认证提供商类型查找 IDP 配置
	idp, err = a.idpRepo.GetIDPByType(ctx, systemCorpId, param.AuthProvider)
	if err != nil {
		a.log.Errorf("根据类型查找IDP失败: provider=%s, system_corp_id=%s, err=%v",
			param.AuthProvider, systemCorpId, err)
		return nil, fmt.Errorf("微应用配置不存在: %v", err)
	}

	// a.log.Infof("找到IDP配置: id=%s, name=%s, type=%s", idp.ID, idp.Name, idp.Type)

	// 3. 获取 IDP 属性
	attrs, err := a.idpRepo.GetIDPAttr(ctx, idp.ID)
	if err != nil {
		a.log.Errorf("获取IDP属性失败: idp_id=%s, err=%v", idp.ID, err)
		return nil, fmt.Errorf("获取微应用配置失败: %v", err)
	}

	// 转换为 map 结构
	attrMap := make(map[string]string)
	for _, attr := range attrs {
		attrMap[attr.Key] = attr.Value
	}
	// 3. 根据提供商获取用户信息
	externalUser, err := a.getMicroAppUserInfo(ctx, param.AuthProvider, param.AuthCode, attrMap, idp.ID)
	if err != nil {
		a.log.Errorf("获取微应用用户信息失败: %v", err)
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 4. 处理用户绑定和创建（使用系统企业ID，不是第三方企业ID）
	user, err = a.handleMicroAppUser(ctx, externalUser, systemCorpId, idp.ID)
	if err != nil {
		a.log.Errorf("处理微应用用户失败: %v", err)
		return nil, fmt.Errorf("用户处理失败: %v", err)
	}

	// 5. 生成访问令牌
	token, err := a.issueToken(ctx, *user, dto.AccessTokenTyp)
	if err != nil {
		a.log.Errorf("生成访问令牌失败: %v", err)
		return nil, fmt.Errorf("令牌生成失败: %v", err)
	}

	// 标记登录成功
	loginSuccess = true

	result := &dto.MicroAppAuthResult{
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		UserId:       user.ID,
		UserName:     user.Name,
		AuthProvider: param.AuthProvider,
		AuthType:     "micro_app",
	}

	a.log.Infof("微应用认证成功: user_id=%s, user_name=%s, provider=%s",
		user.ID, user.Name, param.AuthProvider)

	return result, nil
}

// getMicroAppUserInfo 根据提供商获取微应用用户信息
func (a *AuthUsecase) getMicroAppUserInfo(ctx context.Context, provider, authCode string, attrs map[string]string, idpId string) (*dto.ExternalUser, error) {
	switch provider {
	case "dingtalk":
		return a.getDingTalkUserInfo(ctx, authCode, attrs)
	case "wechat", "qiyewx":
		return a.getWeChatWorkUserInfo(ctx, authCode, attrs, idpId)
	case "feishu":
		return a.getFeishuUserInfo(ctx, authCode, attrs)
	default:
		return nil, fmt.Errorf("不支持的微应用提供商: %s", provider)
	}
}

// getDingTalkUserInfo 获取钉钉用户信息
func (a *AuthUsecase) getDingTalkUserInfo(_ context.Context, authCode string, attrs map[string]string) (*dto.ExternalUser, error) {
	// a.log.Infof("钉钉认证开始，授权码: %s", authCode)
	// 打印所有可用的属性配置
	// a.log.Infof("钉钉IDP配置属性: %+v", attrs)

	// 直接使用钉钉专用的配置键名
	appKey := attrs["dingtalk_app_key"]
	appSecret := attrs["dingtalk_app_secret"]

	// a.log.Infof("钉钉认证参数: appKey=%s, appSecret存在=%t", appKey, appSecret != "")

	if appKey == "" || appSecret == "" {
		a.log.Errorf("钉钉应用配置不完整: appKey=%s, appSecret存在=%t", appKey, appSecret != "")
		return nil, fmt.Errorf("钉钉应用配置不完整，缺少必要的appKey或appSecret")
	}
	// 调用钉钉免登码API获取用户信息
	// 注意：这里使用的是免登码流程，不是OAuth2授权码流程：
	// 1. 先用appKey+appSecret获取应用access_token
	// 2. 再用应用access_token+免登码获取用户信息
	// a.log.Infof("正在调用钉钉免登码API获取用户信息...")

	user, err := dingtalk.GetUserInfoByFreeLoginCode(appKey, appSecret, authCode)
	if err != nil {
		a.log.Errorf("调用钉钉免登码API失败: %v", err)
		return nil, fmt.Errorf("调用钉钉免登码API失败: %v", err)
	}

	// a.log.Infof("钉钉用户信息获取成功: userId=%s, name=%s, email=%s, mobile=%s",
	// 	user.UserId, user.Name, user.Email, user.Mobile)

	return &dto.ExternalUser{
		Userid:      user.UserId,
		Name:        user.Name,
		DisplayName: user.Name,
		Email:       user.Email,
		Mobile:      user.Mobile,
	}, nil
}

// getWeChatWorkUserInfo 获取企业微信用户信息（使用现有的qiyewx包）
func (a *AuthUsecase) getWeChatWorkUserInfo(_ context.Context, authCode string, attrs map[string]string, idpId string) (*dto.ExternalUser, error) {
	// a.log.Infof("企业微信认证开始，授权码: %s", authCode)
	// a.log.Infof("企业微信IDP配置属性: %+v", attrs)

	corpId := attrs["wx_corp_id"]
	corpSecret := attrs["wx_agent_secret"]

	// a.log.Infof("企业微信认证参数: corpId=%s, corpSecret存在=%t, idpId=%s", corpId, corpSecret != "", idpId)

	if corpId == "" || corpSecret == "" {
		a.log.Errorf("企业微信应用配置不完整: corpId=%s, corpSecret存在=%t", corpId, corpSecret != "")
		return nil, fmt.Errorf("企业微信应用配置不完整，缺少必要的corpId或corpSecret")
	}
	// 使用实际的IDP ID而不是硬编码的"microapp"
	wxProvider := qiyewx.NewWxIDProvider(idpId, corpId, corpSecret, &SimpleCacheHandler{})

	// a.log.Infof("正在调用企业微信免登码API获取用户信息...")
	// a.log.Infof("企业微信API调用参数: idpId=%s, corpId=%s, authCode=%s", idpId, corpId, authCode)
	// 先尝试手动测试获取token的URL（调试时可启用）
	// testTokenUrl := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s", corpId, corpSecret)
	// a.log.Infof("企业微信获取token测试URL: %s", testTokenUrl)
	// 注意：这个URL可以在浏览器或Postman中直接访问，用于验证corpId和corpSecret是否正确
	userInfo, err := wxProvider.GetUserInfo(authCode)
	if err != nil {
		a.log.Errorf("调用企业微信免登码API失败: %v", err)
		// a.log.Errorf("错误详情: corpId=%s, secret长度=%d, code=%s", corpId, len(corpSecret), authCode)
		return nil, fmt.Errorf("调用企业微信免登码API失败: %v", err)
	}

	// a.log.Infof("企业微信用户信息获取成功: userId=%s, name=%s, email=%s, mobile=%s",
	// 	userInfo.Userid, userInfo.Name, userInfo.Email, userInfo.Mobile)

	externalUser := &dto.ExternalUser{
		Userid:      userInfo.Userid,
		Name:        userInfo.Name,
		DisplayName: userInfo.Name,
		Email:       userInfo.Email,
		Mobile:      userInfo.Mobile,
	}

	a.log.Infof("构造外部用户对象: userid=%s, name=%s, displayName=%s, email=%s, mobile=%s",
		externalUser.Userid, externalUser.Name, externalUser.DisplayName, externalUser.Email, externalUser.Mobile)

	return externalUser, nil
}

// getFeishuUserInfo 获取飞书微应用用户信息
func (a *AuthUsecase) getFeishuUserInfo(_ context.Context, authCode string, attrs map[string]string) (*dto.ExternalUser, error) {
	// a.log.Infof("飞书微应用认证开始，授权码: %s", authCode)
	// a.log.Infof("飞书IDP配置属性: %+v", attrs)

	appId := attrs["fs_app_id"]
	appSecret := attrs["fs_app_secret"]

	// a.log.Infof("飞书微应用认证参数: appId=%s, appSecret存在=%t", appId, appSecret != "")

	if appId == "" || appSecret == "" {
		a.log.Errorf("飞书应用配置不完整: appId=%s, appSecret存在=%t", appId, appSecret != "")
		return nil, fmt.Errorf("飞书应用配置不完整，缺少必要的appId或appSecret")
	}
	// 创建飞书IDP实例
	fsProvider := feishu.NewFsIDProvider("feishu_provider", appId, appSecret, &FeishuCacheHandler{})

	// a.log.Infof("正在调用飞书微应用API获取用户信息...")

	// 调用飞书微应用专用API获取用户信息
	userInfo, err := fsProvider.GetMicroAppUserInfoByCode(authCode)
	if err != nil {
		a.log.Errorf("调用飞书微应用API失败: %v", err)
		return nil, fmt.Errorf("飞书微应用用户信息获取失败: %v", err)
	}
	// a.log.Infof("飞书微应用用户信息获取成功: userId=%s, name=%s, email=%s, mobile=%s, openId=%s, unionId=%s",
	// 	userInfo.Data.UserId, userInfo.Data.Name, userInfo.Data.Email, userInfo.Data.Mobile, userInfo.Data.OpenId, userInfo.Data.UnionId)

	return &dto.ExternalUser{
		Userid:      userInfo.Data.UserId,
		Name:        userInfo.Data.Name,
		DisplayName: userInfo.Data.Name,
		Email:       userInfo.Data.Email,
		Mobile:      userInfo.Data.Mobile,
	}, nil
}

// handleMicroAppUser 处理微应用用户绑定和创建
func (a *AuthUsecase) handleMicroAppUser(ctx context.Context, externalUser *dto.ExternalUser, corpId, idpId string) (*model.TbUserEntity, error) {
	// a.log.Infof("处理微应用用户: external_user_id=%s, name=%s, corp_id=%s, idp_id=%s",
	// 	externalUser.Userid, externalUser.Name, corpId, idpId)

	// 1. 获取IDP配置
	idp, err := a.idpRepo.GetIDP(ctx, corpId, idpId)
	if err != nil {
		a.log.Errorf("获取IDP配置失败: %v", err)
		return nil, fmt.Errorf("获取IDP配置失败: %v", err)
	}

	// 2. 根据 idpId 查询绑定来源
	allSource, err := a.idpRepo.GetAllSourceOfBind(ctx, corpId, idp.ID)
	if err != nil {
		a.log.Errorf("查询绑定来源失败: %v", err)
		return nil, fmt.Errorf("查询绑定来源失败: %v", err)
	}
	if len(allSource) != 1 {
		a.log.Errorf("IDP绑定来源配置错误: 期望1个，实际%d个", len(allSource))
		return nil, fmt.Errorf("IDP绑定来源配置错误")
	}

	// 3. 根据 idpId 查询绑定根目录
	groups, err := a.idpRepo.GetIDPBindGroup(ctx, corpId, idp.ID)
	if err != nil {
		a.log.Errorf("查询绑定根目录失败: %v", err)
		return nil, fmt.Errorf("查询绑定根目录失败: %v", err)
	}
	if len(groups) != 1 {
		a.log.Errorf("IDP绑定根目录配置错误: 期望1个，实际%d个", len(groups))
		return nil, fmt.Errorf("IDP绑定根目录配置错误")
	}
	rootGroup := groups[0]

	// 4. 查找用户
	localUser, err := a.userRepo.GetExternalUser(ctx, rootGroup.RootGroupID, externalUser.Userid, idp.Type)
	if err != nil {
		a.log.Errorf("GetExternalUser failed. err=%v", err)
		return nil, fmt.Errorf("查找微应用用户失败: %v", err)
	}

	// 5. 查询实际本地用户
	user, err := a.userRepo.QueryUserEntity(ctx, corpId, localUser.LocalUserID)
	if err != nil {
		a.log.Errorf("查询本地用户失败: %v", err)
		return nil, fmt.Errorf("查询本地用户失败: %v", err)
	}

	// 6. 检查用户状态
	if err := a.isUserValidate(user); err != nil {
		a.log.Errorf("用户状态验证失败: %v", err)
		return nil, fmt.Errorf("用户状态验证失败: %v", err)
	}

	// a.log.Infof("微应用用户处理成功: local_user_id=%s, name=%s", user.ID, user.Name)
	return user, nil
}

// SimpleCacheHandler 简单的缓存处理器实现（用于企业微信）
type SimpleCacheHandler struct{}

func (h *SimpleCacheHandler) GetWxWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error) {
	// 返回正确的RecordNotFound错误，让系统重新获取token
	return "", pb.ErrorRecordNotFound("token not found in cache for provider: %s", providerId)
}

func (h *SimpleCacheHandler) SetWxWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error {
	// 简单实现：不做缓存，直接返回成功
	return nil
}

// FeishuCacheHandler 简单的飞书缓存处理器实现
type FeishuCacheHandler struct{}

func (h *FeishuCacheHandler) GetFsWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error) {
	// 返回正确的RecordNotFound错误，让系统重新获取token
	return "", pb.ErrorRecordNotFound("token not found in cache for provider: %s", providerId)
}

func (h *FeishuCacheHandler) SetFsWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error {
	// 简单实现：不做缓存，直接返回成功
	return nil
}
