// @Summary 用户登录 获取动态路由
// @Produce  application/json
// @Param 可以什么都不填 调一下即可
// @Router /menu/getMenu [post]
export const asyncMenu = () => {
  // return service({
  //   url: '/menu/getMenu',
  //   method: 'post'
  // })
  return new Promise(function(resolve, reject) {
    resolve({
      'code': 0, 'data': {
        'menus': [
          {
            'ID': 9,
            'CreatedAt': '2022-09-21T21:35:16.381+08:00',
            'UpdatedAt': '2022-09-21T21:35:16.381+08:00',
            'parentId': '0',
            'path': 'clientLogin',
            'name': 'clientLogin',
            'hidden': true,
            'component': 'view/login/clientLogin.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '客户端登陆',
              'topTitle': '客户端登陆',
              'icon': 'message',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '9',
            'children': null,
            'parameters': [],
            'btns': null,
          },
          {
            'ID': 0,
            'CreatedAt': '2022-07-09T19:02:48.587+08:00',
            'UpdatedAt': '2022-07-09T19:02:48.587+08:00',
            'parentId': '0',
            'path': 'dashboard',
            'name': 'dashboard',
            'hidden': false,
            'component': 'view/layout/main.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '应用门户',
              'topTitle': '',
              'icon': 'icon-yingyongliebiao',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '0',
            'children': null,
            'parameters': [],
            'btns': null,
          },
          {
            'ID': 0,
            'CreatedAt': '2022-07-09T19:02:48.587+08:00',
            'UpdatedAt': '2022-07-09T19:02:48.587+08:00',
            'parentId': '0',
            'path': 'download',
            'name': 'download',
            'hidden': false,
            'component': 'view/client/download.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '客户端下载',
              'topTitle': '客户端下载',
              'icon': 'icon-kehuduanxiazai',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '0',
            'children': null,
            'parameters': [],
            'btns': null,
          },
          {
            'ID': 8,
            'CreatedAt': '2022-09-21T21:35:16.381+08:00',
            'UpdatedAt': '2022-09-21T21:35:16.381+08:00',
            'parentId': '0',
            'path': 'person',
            'name': 'person',
            'hidden': true,
            'component': 'view/person/person.vue',
            'sort': 1,
            'meta': {
              'keepAlive': false,
              'defaultMenu': false,
              'title': '个人信息',
              'topTitle': '个人信息',
              'icon': 'message',
              'closeTab': false,
            },
            'authoritys': null,
            'menuBtn': null,
            'menuId': '8',
            'children': null,
            'parameters': [],
            'btns': null,
          }],
      }, 'msg': '获取成功',
    })
  })
}

