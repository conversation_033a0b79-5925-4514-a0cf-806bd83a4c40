package excel

import (
	core_go "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/global/cons"
	"asdsec.com/asec/platform/app/console/utils/timex"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"net/url"
	"reflect"
	"sort"
	"strconv"
	"time"
)

const XLSX = "xlsx"
const DEFAULT_SHEET_NAME = "Sheet1"

// 加粗加居中
const CENTER_STYLE = `{"alignment":{"horizontal":"center"},"font":{"bold":true}}`

// 加粗
const BOLD_STYLE = `{"font":{"bold":true}}`

// # 文件导出的最大数量
const MAX_EXPORT_EVENTS_NUM = 100000

// # 一次读取到内存然后写入文件的安全事件数量
const MAX_EXPORT_FILES_NUM_PER_WRITE = 10000

// 加粗边框底色
const DATA_COL_STYLE = `{"border":[{"type":"left","color":"000000","style":1},{"type":"top","color":"000000","style":1},{"type":"bottom","color":"000000","style":1},{"type":"right","color":"000000","style":1}],"fill":{"type":"gradient","color":["#FFFFFF","#ccccff"],"shading":1}}`
const TITLE_ROWS = 2

type colTag struct {
	fieldName   string
	columnIndex string
	columnDesc  string
	columnWidth float64
}

type colValue struct {
	columnIndex string
	columnData  interface{}
	columnWidth float64
}

/*
Description---excel文件导出

title---excel标题名称

contentData---导出的结构体数组

c---gin context上下文
*/
func ExportExcelTemplate(title string, contentData interface{}, c *gin.Context, templateMap map[string][]string) {

	// 是否国际化
	inter := true
	excelFile := excelize.NewFile()

	// 获取数据列
	contentDataType := reflect.TypeOf(contentData)
	structType := contentDataType.Elem()

	// 获取最大列切片
	colList := make([]string, 0)
	// 数据列头
	colTagList := make([]colTag, 0)

	// 获取列头
	getTagList(structType, &colList, &colTagList)

	// 表头需要国际化
	err := writeDataColTemplate(excelFile, colTagList, c, inter)
	if err != nil {
		core_go.SysLog.Error("excel title error", zap.Error(err))
		return
	}

	//循环templateMap
	for k, v := range templateMap {
		// 方案1：直接设置下拉选项值（推荐）
		ddValues := v // 选项值
		dv1 := excelize.NewDataValidation(true)
		dv1.SetSqref(k) // 作用于 A 列第 2 行到 100 行
		err = dv1.SetDropList(ddValues)
		if err != nil {
			return
		}
		sheet := "Sheet1"
		err = excelFile.AddDataValidation(sheet, dv1)
		if err != nil {
			return
		}
	}

	// 写入数据
	writeData(excelFile, contentData, TITLE_ROWS, c)

	exportExcelHeaders(c, excelFile, title, XLSX)

}

// 写入数据列头
func writeDataColTemplate(f *excelize.File, colTagList []colTag, c *gin.Context, inter bool) error {
	rowAdd := strconv.Itoa(1)
	// 定义绿色加粗样式的 JSON 字符串
	const GreenBoldStyle = `{
    "font": {
        "bold": true,
        "color": "#008000",
		"size": 14
    }
}`

	// 在你的函数中解析并应用该样式
	greenBoldStyle, err := f.NewStyle(GreenBoldStyle)
	if err != nil {
		// 处理错误
		return err
	}

	//dataColStyle, _ := f.NewStyle(DATA_COL_STYLE)
	for _, colT := range colTagList {
		if inter {
			safeSetCellValue(f, DEFAULT_SHEET_NAME, colT.columnIndex+rowAdd, core_go.GetResource(c, colT.columnDesc))
		} else {
			safeSetCellValue(f, DEFAULT_SHEET_NAME, colT.columnIndex+rowAdd, colT.columnDesc)
		}
		// 设置列宽
		err := f.SetColWidth(DEFAULT_SHEET_NAME, colT.columnIndex, colT.columnIndex, colT.columnWidth)
		if err != nil {
			return err
		}
		// 加格式
		err = f.SetCellStyle(DEFAULT_SHEET_NAME, colT.columnIndex+rowAdd, colT.columnIndex+rowAdd, greenBoldStyle)
		if err != nil {
			return err
		}
	}
	return nil
}

/*
Description---excel文件导出

title---excel标题名称

queryCondition---条件map

contentData---导出的结构体数组

fileName---导出文件名,不需要带上时间戳,会生成

c---gin context上下文
*/
func ExportExcel(title string, queryCondition map[string]string,
	contentData interface{}, fileName string, c *gin.Context, args ...interface{}) {

	// 是否国际化
	var inter bool
	if len(args) > 1 {
		inter = args[1].(bool)
	}

	excelFile := excelize.NewFile()
	// 单元格格式定义
	centerStyle, _ := excelFile.NewStyle(CENTER_STYLE)

	// 获取数据列
	contentDataType := reflect.TypeOf(contentData)
	structType := contentDataType.Elem()

	// 获取最大列切片
	colList := make([]string, 0)
	// 数据列头
	colTagList := make([]colTag, 0)

	// 获取列头
	getTagList(structType, &colList, &colTagList)

	// 获取最大列
	maxColStr := getMaxCol(colList)

	// 写入标题
	writeTitle(excelFile, maxColStr, title, centerStyle, c, inter)

	// 写入查询条件
	if len(queryCondition) > 0 {
		if len(args) == 0 {
			writeQueryCondition(excelFile, queryCondition, maxColStr, c, inter)
		} else {
			queryOrder := args[0].([]string)
			writeQueryCondition(excelFile, queryCondition, maxColStr, c, inter, queryOrder)
		}
	}

	// 表头需要国际化
	writeDataCol(excelFile, colTagList, len(queryCondition), maxColStr, centerStyle, c, inter)

	// 写入数据
	writeData(excelFile, contentData, len(queryCondition)+TITLE_ROWS+3, c)

	exportExcelHeaders(c, excelFile, fileName, XLSX)

}

/*
GenerateExcel 生成Excel,并不导出
Description---excel文件生成
excelFile---excel文件描述符，可以为空nil
title---excel标题名称
sheet---excel sheet页名称
queryCondition---条件map
contentData---导出的结构体数组
c---gin context上下文
*/
func GenerateExcel(excelFile *excelize.File, title string, sheet string, queryCondition map[string]string,
	contentData interface{}, c *gin.Context, args ...interface{}) *excelize.File {

	// 是否国际化
	var inter bool
	if len(args) > 1 {
		inter = args[1].(bool)
	}
	if excelFile == nil {
		excelFile = excelize.NewFile()
	}
	// 单元格格式定义
	centerStyle, _ := excelFile.NewStyle(CENTER_STYLE)
	// 获取数据列
	contentDataType := reflect.TypeOf(contentData)
	structType := contentDataType.Elem()
	// 获取最大列切片
	colList := make([]string, 0)
	// 数据列头
	colTagList := make([]colTag, 0)
	// 获取列头
	getTagList(structType, &colList, &colTagList)
	// 获取最大列
	maxColStr := getMaxCol(colList)
	// 写入标题
	if inter {
		title = core_go.GetResource(c, title)
		sheet = core_go.GetResource(c, sheet)
	}
	writeTitleSheet(excelFile, maxColStr, title, sheet, centerStyle)

	// 写入查询条件
	if len(queryCondition) > 0 {
		writeSearchTitle(excelFile, maxColStr, sheet, centerStyle, c)
		if len(args) == 0 {
			writeQueryConditionInSheet(excelFile, queryCondition, maxColStr, sheet, c, inter)
		} else {
			queryOrder := args[0].([]string)
			writeQueryConditionInSheet(excelFile, queryCondition, maxColStr, sheet, c, inter, queryOrder)
		}
		writeSearchResult(excelFile, len(queryCondition), maxColStr, sheet, centerStyle, c)
	}

	// 表头需要国际化
	writeDataTitle(excelFile, colTagList, len(queryCondition), sheet, c, inter)
	// 写入数据
	writeDataInSheet(excelFile, contentData, len(queryCondition), sheet, c)

	return excelFile
}

// ExportExcelFile 导出Excel文件
func ExportExcelFile(c *gin.Context, f *excelize.File, fileName string) {
	exportExcelHeaders(c, f, fileName, XLSX)
}

// DeleteSheet 删除sheet
func DeleteSheet(f *excelize.File, sheetList []string) {
	for _, sheet := range sheetList {
		f.DeleteSheet(sheet)
	}
}

// 递归获取数据列头
func getTagList(typeOf reflect.Type, colList *[]string, colTagList *[]colTag) {
	for i := 0; i < typeOf.NumField(); i++ {
		field := typeOf.Field(i)
		// 不算自定义的时间类型
		if field.Type.Kind() == reflect.Struct && field.Type.Name() != timex.FrontTimeName {
			getTagList(field.Type, colList, colTagList)
		} else {
			// 获取tag
			structTag := field.Tag
			excelColumn := structTag.Get("excelColumn")
			if excelColumn == "" {
				// 列序为空=没有打签 忽略之
				continue
			}
			excelDesc := structTag.Get("excelDesc")
			excelWidth := structTag.Get("excelWidth")
			// 宽度转换
			w, _ := strconv.ParseFloat(excelWidth, 10)
			cTag := colTag{field.Name, excelColumn, excelDesc, w}
			// 相关数据存到切片中
			*colList = append(*colList, excelColumn)
			*colTagList = append(*colTagList, cTag)
		}
	}
}

// 递归写入数据
const maxInterSliceLen = 5

func writeData(f *excelize.File, contentData interface{}, firstRowNum int, c *gin.Context) {

	// 标题自占一行
	rowAdd := strconv.Itoa(firstRowNum)
	valueOfContent := reflect.ValueOf(contentData)

	for i := 0; i < valueOfContent.Len(); i++ {
		if i > MAX_EXPORT_EVENTS_NUM {
			continue
		}
		value := valueOfContent.Index(i)
		// 定义一个结构体切片,递归获取数据 与 写入excel 逻辑分离
		colValueList := make([]colValue, 0, 100)
		nestedWrite(value, &colValueList)
		for _, colVal := range colValueList {
			// 空字符串填充-
			data, ok := colVal.columnData.(reflect.Value).Interface().(string)
			if ok {
				if data == "" {
					//if firstRowNum != TITLE_ROWS {
					//	safeSetCellValue(f, DEFAULT_SHEET_NAME, colVal.columnIndex+rowAdd, "")
					//}
				} else {
					// 可国际化时国际化 todo 减少国际化，通过tag标识字段是够需要国际化
					safeSetCellValue(f, DEFAULT_SHEET_NAME, colVal.columnIndex+rowAdd, core_go.GetResourceIfCan(c, data, false))
				}
			} else {
				// 判断是不是自定义的时间类型
				timeData, ok := colVal.columnData.(reflect.Value).Interface().(timex.FrontTime)
				if ok {
					safeSetCellValue(f, DEFAULT_SHEET_NAME, colVal.columnIndex+rowAdd, timeData.String())
				} else {
					sliceData, ok := colVal.columnData.(reflect.Value).Interface().([]string)
					// 只对长度小于5的列表进行国际化
					if ok && len(sliceData) <= maxInterSliceLen {
						safeSetCellValue(f, DEFAULT_SHEET_NAME, colVal.columnIndex+rowAdd, core_go.InterStringData(c, sliceData))
					} else {
						safeSetCellValue(f, DEFAULT_SHEET_NAME, colVal.columnIndex+rowAdd, colVal.columnData)
					}
				}
			}
			f.SetColWidth(DEFAULT_SHEET_NAME, colVal.columnIndex, colVal.columnIndex, colVal.columnWidth)
		}
		// 一行写完了
		rowAddInt, _ := strconv.Atoi(rowAdd)
		rowAdd = strconv.Itoa(rowAddInt + 1)
	}
}

// writeDataInSheet 特定sheet写入数据
func writeDataInSheet(f *excelize.File, contentData interface{}, queryConditionCount int, sheet string, c *gin.Context) {
	var rowAdd = strconv.Itoa(TITLE_ROWS + 1)
	// 有搜索条件需要增加前后搜表标题
	if queryConditionCount > 0 {
		rowAddInt, _ := strconv.Atoi(rowAdd)
		rowAdd = strconv.Itoa(rowAddInt + queryConditionCount + TITLE_ROWS)
	}

	valueOfContent := reflect.ValueOf(contentData)
	for i := 0; i < valueOfContent.Len(); i++ {
		if i > MAX_EXPORT_EVENTS_NUM {
			break
		}
		value := valueOfContent.Index(i)
		// 定义一个结构体切片,递归获取数据 与 写入excel 逻辑分离
		colValueList := make([]colValue, 0, 100)
		nestedWrite(value, &colValueList)
		for _, colVal := range colValueList {
			// 空字符串填充-
			data, ok := colVal.columnData.(reflect.Value).Interface().(string)
			if ok {
				if data == "" {
					safeSetCellValue(f, sheet, colVal.columnIndex+rowAdd, cons.ExcelNullString)
				} else {
					// 可国际化时国际化 todo 减少国际化，通过tag标识字段是够需要国际化
					safeSetCellValue(f, sheet, colVal.columnIndex+rowAdd, core_go.GetResourceIfCan(c, data, false))
				}
			} else {
				// 判断是不是自定义的时间类型
				timeData, ok := colVal.columnData.(reflect.Value).Interface().(timex.FrontTime)
				if ok {
					safeSetCellValue(f, sheet, colVal.columnIndex+rowAdd, timeData.String())
				} else {
					sliceData, ok := colVal.columnData.(reflect.Value).Interface().([]string)
					// 只对长度小于5的列表进行国际化
					if ok && len(sliceData) <= maxInterSliceLen {
						safeSetCellValue(f, sheet, colVal.columnIndex+rowAdd, core_go.InterStringData(c, sliceData))
					} else {
						safeSetCellValue(f, sheet, colVal.columnIndex+rowAdd, colVal.columnData)
					}
				}
			}
			f.SetColWidth(sheet, colVal.columnIndex, colVal.columnIndex, colVal.columnWidth)
		}
		// 一行写完了
		rowAddInt, _ := strconv.Atoi(rowAdd)
		rowAdd = strconv.Itoa(rowAddInt + 1)
	}
}

func nestedWrite(valueOf reflect.Value, colValueList *[]colValue) {
	for i := 0; i < valueOf.NumField(); i++ {
		t := valueOf.Type()
		if t.Field(i).Type.Kind() == reflect.Struct && t.Field(i).Type.Name() != timex.FrontTimeName {
			nestedWrite(valueOf.Field(i), colValueList)
		} else {
			// 获取值
			realValue := valueOf.FieldByName(t.Field(i).Name)
			// 获取TAG
			excelColumn := t.Field(i).Tag.Get("excelColumn")
			if excelColumn == "" {
				// 列序为空=没有打签 忽略之
				continue
			}
			excelWidth := t.Field(i).Tag.Get("excelWidth")
			// 宽度转换
			w, _ := strconv.ParseFloat(excelWidth, 10)
			cValue := colValue{excelColumn, realValue, w}
			*colValueList = append(*colValueList, cValue)
		}
	}
}

// 写入数据列头
func writeDataCol(f *excelize.File, colTagList []colTag, queryConditionCount int, maxColStr string, style int, c *gin.Context, inter bool) {
	rowAdd := strconv.Itoa(queryConditionCount + TITLE_ROWS + 1)
	dataColStyle, _ := f.NewStyle(DATA_COL_STYLE)
	// ”查询结果“表头
	safeSetCellValue(f, DEFAULT_SHEET_NAME, "A"+rowAdd, core_go.GetResource(c, cons.ExcelSearchResult))
	f.MergeCell(DEFAULT_SHEET_NAME, "A"+rowAdd, maxColStr+rowAdd)
	f.SetCellStyle(DEFAULT_SHEET_NAME, "A"+rowAdd, "A"+rowAdd, style)
	// 行数为标题三行+查询条件行数
	rowAdd = strconv.Itoa(queryConditionCount + TITLE_ROWS + 2)
	for _, colT := range colTagList {
		if inter {
			safeSetCellValue(f, DEFAULT_SHEET_NAME, colT.columnIndex+rowAdd, core_go.GetResource(c, colT.columnDesc))
		} else {
			safeSetCellValue(f, DEFAULT_SHEET_NAME, colT.columnIndex+rowAdd, colT.columnDesc)
		}
		// 设置列宽
		f.SetColWidth(DEFAULT_SHEET_NAME, colT.columnIndex, colT.columnIndex, colT.columnWidth)
		// 加格式
		f.SetCellStyle(DEFAULT_SHEET_NAME, colT.columnIndex+rowAdd, colT.columnIndex+rowAdd, dataColStyle)
	}
}

// 写入数据列头
func writeDataTitle(f *excelize.File, colTagList []colTag, queryConditionCount int, sheet string, c *gin.Context, inter bool) {
	dataColStyle, _ := f.NewStyle(DATA_COL_STYLE)
	var rowAdd = strconv.Itoa(TITLE_ROWS)
	// 有搜索条件需要增加前后搜表标题
	if queryConditionCount > 0 {
		rowAddInt, _ := strconv.Atoi(rowAdd)
		rowAdd = strconv.Itoa(rowAddInt + queryConditionCount + TITLE_ROWS)
	}

	for _, colT := range colTagList {
		if inter {
			safeSetCellValue(f, sheet, colT.columnIndex+rowAdd, core_go.GetResource(c, colT.columnDesc))
		} else {
			safeSetCellValue(f, sheet, colT.columnIndex+rowAdd, colT.columnDesc)
		}
		// 设置列宽
		f.SetColWidth(sheet, colT.columnIndex, colT.columnIndex, colT.columnWidth)
		// 加格式
		f.SetCellStyle(sheet, colT.columnIndex+rowAdd, colT.columnIndex+rowAdd, dataColStyle)
	}
}

// 写入查询条件
func writeQueryCondition(f *excelize.File, queryCondition map[string]string, maxColStr string, c *gin.Context, inter bool, queryOrder ...[]string) {
	if inter {
		queryCondition = core_go.InterStringData(c, queryCondition).(map[string]string)
	}
	i := 1
	boldStyle, _ := f.NewStyle(BOLD_STYLE)
	if len(queryOrder) == 0 {
		// 调用方没有设置 queryOrder, 则不管排序
		for query, queryItem := range queryCondition {
			// 写入两行标题,行数加1
			rowAdd := strconv.Itoa(i + TITLE_ROWS)
			safeSetCellValue(f, DEFAULT_SHEET_NAME, "A"+rowAdd, query)
			f.SetCellStyle(DEFAULT_SHEET_NAME, "A"+rowAdd, "A"+rowAdd, boldStyle)
			safeSetCellValue(f, DEFAULT_SHEET_NAME, "B"+rowAdd, queryItem)
			f.MergeCell(DEFAULT_SHEET_NAME, "B"+rowAdd, maxColStr+rowAdd)
			i++
		}
	} else {
		// 有设置排序顺序
		queryOrderList := queryOrder[0]
		if inter {
			queryOrderList = core_go.InterStringData(c, queryOrder[0]).([]string)
		}
		for _, key := range queryOrderList {
			// 写入两行标题,行数加1
			rowAdd := strconv.Itoa(i + TITLE_ROWS)
			safeSetCellValue(f, DEFAULT_SHEET_NAME, "A"+rowAdd, key)
			f.SetCellStyle(DEFAULT_SHEET_NAME, "A"+rowAdd, "A"+rowAdd, boldStyle)
			safeSetCellValue(f, DEFAULT_SHEET_NAME, "B"+rowAdd, queryCondition[key])
			f.MergeCell(DEFAULT_SHEET_NAME, "B"+rowAdd, maxColStr+rowAdd)
			i++
		}
	}
}

// writeQueryConditionInSheet 在特定sheet中写入查询条件
func writeQueryConditionInSheet(f *excelize.File, queryCondition map[string]string, maxColStr string, sheet string, c *gin.Context, inter bool, queryOrder ...[]string) {
	if inter {
		queryCondition = core_go.InterStringData(c, queryCondition).(map[string]string)
	}
	i := 1
	boldStyle, _ := f.NewStyle(BOLD_STYLE)
	if len(queryOrder) == 0 {
		// 调用方没有设置 queryOrder, 则不管排序
		for query, queryItem := range queryCondition {
			// 写入两行标题,行数加1
			rowAdd := strconv.Itoa(i + TITLE_ROWS)
			safeSetCellValue(f, sheet, "A"+rowAdd, query)
			f.SetCellStyle(sheet, "A"+rowAdd, "A"+rowAdd, boldStyle)
			safeSetCellValue(f, sheet, "B"+rowAdd, queryItem)
			f.MergeCell(sheet, "B"+rowAdd, maxColStr+rowAdd)
			i++
		}
	} else {
		// 有设置排序顺序
		queryOrderList := queryOrder[0]
		if inter {
			queryOrderList = core_go.InterStringData(c, queryOrder[0]).([]string)
		}
		for _, key := range queryOrderList {
			// 写入两行标题,行数加1
			rowAdd := strconv.Itoa(i + TITLE_ROWS)
			safeSetCellValue(f, sheet, "A"+rowAdd, key)
			f.SetCellStyle(sheet, "A"+rowAdd, "A"+rowAdd, boldStyle)
			safeSetCellValue(f, sheet, "B"+rowAdd, queryCondition[key])
			f.MergeCell(sheet, "B"+rowAdd, maxColStr+rowAdd)
			i++
		}
	}
}

// 获取最大列,当前算法只能支持A~Z
func getMaxCol(list []string) string {
	sort.Strings(list)
	maxCol := list[len(list)-1]
	// 数据列小于B时,最大列为B
	if maxCol == "A" {
		maxCol = "B"
	}
	return maxCol
}

func writeTitle(excelFile *excelize.File, maxColStr string, title string, style int, c *gin.Context, inter bool) {
	if inter {
		safeSetCellValue(excelFile, DEFAULT_SHEET_NAME, "A1", core_go.GetResource(c, title))
	} else {
		safeSetCellValue(excelFile, DEFAULT_SHEET_NAME, "A1", title)
	}
	excelFile.MergeCell(DEFAULT_SHEET_NAME, "A1", maxColStr+"1")
	excelFile.SetCellStyle(DEFAULT_SHEET_NAME, "A1", "A1", style)
	safeSetCellValue(excelFile, DEFAULT_SHEET_NAME, "A2", core_go.GetResource(c, cons.ExcelSearchCondition))
	excelFile.MergeCell(DEFAULT_SHEET_NAME, "A2", maxColStr+"2")
	excelFile.SetCellStyle(DEFAULT_SHEET_NAME, "A2", "A1", style)
}

// 写入标题、sheet
func writeTitleSheet(excelFile *excelize.File, maxColStr string, title string, sheet string, style int) {
	excelFile.NewSheet(sheet)
	safeSetCellValue(excelFile, sheet, "A1", title)
	excelFile.MergeCell(sheet, "A1", maxColStr+"1")
	excelFile.SetCellStyle(sheet, "A1", "A1", style)
}

// 写入搜索条件标题
func writeSearchTitle(excelFile *excelize.File, maxColStr string, sheet string, style int, c *gin.Context) {
	safeSetCellValue(excelFile, sheet, "A2", core_go.GetResource(c, cons.ExcelSearchCondition))
	excelFile.MergeCell(sheet, "A2", maxColStr+"2")
	excelFile.SetCellStyle(sheet, "A2", "A2", style)
}

// 写入搜索条件结果
func writeSearchResult(excelFile *excelize.File, queryConditionCount int, maxColStr string, sheet string, style int, c *gin.Context) {
	rowAdd := strconv.Itoa(queryConditionCount + TITLE_ROWS + 1)
	// ”查询结果“表头
	safeSetCellValue(excelFile, sheet, "A"+rowAdd, core_go.GetResource(c, cons.ExcelSearchResult))
	excelFile.MergeCell(sheet, "A"+rowAdd, maxColStr+rowAdd)
	excelFile.SetCellStyle(sheet, "A"+rowAdd, "A"+rowAdd, style)
}

// 导出文件头部
//
// - fileName:文件名
//
// - extension:excel后缀 utils.XLSX||utils.XLS.
func exportExcelHeaders(c *gin.Context, f *excelize.File, fileName string, extension string) {
	// 获取当前时间
	nowTime := time.Now().Format("20060102150405")
	fileName = url.QueryEscape(core_go.GetResource(c, fileName))
	exFilename := fmt.Sprintf("attachment; filename=%s_%s.%s", fileName, nowTime, extension)
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Access-Control-Expose-Headers", "Content-Disposition") // 本地调试模式下，设置允许跨域
	c.Header("Content-Disposition", exFilename)
	c.Header("Content-Transfer-Encoding", "binary")
	f.WriteTo(c.Writer)
}

// 在避免csv注入的写入excel方法
func safeSetCellValue(f *excelize.File, sheetName string, axis string, value interface{}) {
	//switch value.(type) {
	//case string:
	//	if existSpecialCharacter(value.(string)) {
	//		value = fmt.Sprintf("'%s'", value.(string))
	//	}
	//}
	f.SetCellValue(sheetName, axis, value)
}
