package service

import (
	"asdsec.com/asec/platform/app/console/app/access/dto"
	"asdsec.com/asec/platform/app/console/app/access/repository"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"sync"
)

var StrategyServiceImpl StrategyService

var StrategyServiceInit sync.Once

type strategyService struct {
	db repository.StrategyRepository
}

func (s strategyService) CheckStrategyQuote(c context.Context, req dto.CheckStrategyQuoteReq) ([]dto.CheckStrategyQuoteResp, error) {
	return s.db.CheckStrategyQuote(c, req)
}

func (s strategyService) SyncStrategy(ctx context.Context, req dto.SyncStrategyReq) error {
	var err error
	if len(req.AppIds) > 0 {
		err = s.db.SyncStrategyAppIds(ctx, req.AppIds)
		if err != nil {
			return err
		}
	}
	if len(req.AppGroupIds) > 0 {
		err = s.db.SyncStrategyAppGroupIds(ctx, req.AppGroupIds)
		if err != nil {
			return err
		}
	}
	if len(req.UserIds) > 0 {
		err = s.db.SyncStrategyUserIds(ctx, req.UserIds)
		if err != nil {
			return err
		}
	}
	if len(req.UserGroupIds) > 0 {
		// 用户分组因为层级关系，需要先递归查询出当前分组下的所有分组id
		req.UserGroupIds, err = s.db.RecursiveGetGroup(ctx, req.UserGroupIds)
		if err != nil {
			return err
		}
		err = s.db.SyncStrategyUserGroupIds(ctx, req.UserGroupIds)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s strategyService) StrategyDel(ctx context.Context, ids []int64) error {
	err := s.db.StrategyDel(ctx, ids)
	if err != nil {
		return err
	}
	return nil
}

func (s strategyService) StrategyEnable(ctx context.Context, ids []int64, status int) error {
	err := s.db.StrategyEnable(ctx, ids, status)
	if err != nil {
		return err
	}
	return nil
}

func (s strategyService) StrategyUpdate(ctx context.Context, req dto.StrategyUpdateReq) error {
	accessStrategy := model.AccessStrategy{}
	err := copier.Copy(&accessStrategy, &req)
	accessStrategy.ID = req.Id
	int64s, err := utils.StringsToInt64s(req.AppIds)
	if err != nil {
		return err
	}
	accessStrategy.AppIds = int64s
	int64s, err = utils.StringsToInt64s(req.AppGroupIds)
	if err != nil {
		return err
	}
	accessStrategy.AppGroupIds = int64s
	if accessStrategy.CorpID == "" {
		accessStrategy.CorpID = "0"
	}
	err = s.db.StrategyUpdate(ctx, accessStrategy)
	if err != nil {
		return err
	}
	return nil
}

func (s strategyService) StrategyList(ctx context.Context, req dto.StrategyListReq) (dto.StrategyListResp, error) {
	resp, err := s.db.StrategyList(ctx, req)
	if err != nil {
		return dto.StrategyListResp{}, err
	}
	listData := resp.StrategyListData
	// 转换实体类
	for i, datum := range listData {
		if datum.UserInfo != nil {
			_ = listData[i].UnmarshalUserInStrategy()
		}

		if datum.GroupInfo != nil {
			_ = listData[i].UnmarshalUserGroupInStrategy()
		}
		if datum.RoleInfo != nil {
			_ = listData[i].UnmarshalRoleInStrategy()
		}

		if datum.AppInfo != nil {
			_ = listData[i].UnmarshalAppInStrategy()
		}

		if datum.AppGroupInfo != nil {
			_ = listData[i].UnmarshalAppGroupInStrategy()
		}
	}
	return resp, nil
}

func (s strategyService) CreateStrategy(ctx context.Context, req dto.CreateStrategyReq) error {
	accessStrategy := model.AccessStrategy{}
	err := copier.Copy(&accessStrategy, &req)
	id, err := snowflake.Sf.GetId()
	if err != nil {
		global.SysLog.Error("get snowflake err", zap.Error(err))
		return err
	}
	accessStrategy.ID = id

	int64s, err := utils.StringsToInt64s(req.AppIds)
	if err != nil {
		global.SysLog.Error("appIds string to int err", zap.Error(err))
		return err
	}
	accessStrategy.AppIds = int64s
	int64s, err = utils.StringsToInt64s(req.AppGroupIds)
	if err != nil {
		global.SysLog.Error("appGroupIds string to int err", zap.Error(err))
		return err
	}
	accessStrategy.AppGroupIds = int64s
	// TODO cl 需要改造 租户id
	if accessStrategy.CorpID == "" {
		accessStrategy.CorpID = "0"
	}

	err = s.db.CreateStrategy(ctx, accessStrategy)
	if err != nil {
		return err
	}
	return nil
}

func (s strategyService) StrategyCountByName(ctx context.Context, strategyName string) (int64, error) {
	return s.db.StrategyCountByName(ctx, strategyName)
}

type StrategyService interface {
	CreateStrategy(ctx context.Context, req dto.CreateStrategyReq) error
	StrategyList(ctx context.Context, req dto.StrategyListReq) (dto.StrategyListResp, error)
	StrategyUpdate(ctx context.Context, req dto.StrategyUpdateReq) error
	StrategyDel(ctx context.Context, ids []int64) error
	StrategyEnable(ctx context.Context, ids []int64, status int) error
	SyncStrategy(ctx context.Context, req dto.SyncStrategyReq) error
	CheckStrategyQuote(c context.Context, req dto.CheckStrategyQuoteReq) ([]dto.CheckStrategyQuoteResp, error)
	StrategyCountByName(ctx context.Context, strategyName string) (int64, error)
}

func GetStrategyService() StrategyService {
	StrategyServiceInit.Do(func() {
		StrategyServiceImpl = &strategyService{db: repository.NewStrategyRepository()}
	})
	return StrategyServiceImpl
}
