package service

import (
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"context"

	"asdsec.com/asec/platform/app/auth/internal/common"

	pb "asdsec.com/asec/platform/api/auth/v1/admin"

	"github.com/jinzhu/copier"
)

func (s *AdminService) CreateUserGroup(ctx context.Context, req *pb.CreateUserGroupRequest) (*pb.CreateUserGroupReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateUserGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	var param dto.CreateGroupParam
	if err := copier.Copy(&param, req); err != nil {
		return &pb.CreateUserGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId

	err = s.userGroup.CreateUserGroup(ctx, param)
	if err != nil {
		return &pb.CreateUserGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateUserGroupReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) ListUserGroup(ctx context.Context, req *pb.ListUserGroupRequest) (*pb.ListUserGroupReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListUserGroupReply{}, err
	}
	limit, offset := common.GetLimitOffset(req.Limit, req.Offset)
	resp, err := s.userGroup.ListUserGroup(ctx, corpId, req.ParentGroupId)
	if err != nil {
		return &pb.ListUserGroupReply{}, err
	}
	var result []*pb.ListUserGroupReply_UserGroupTree

	err = copier.Copy(&result, resp.Root.PageChildren(int(offset), int(limit)))
	if err != nil {
		return &pb.ListUserGroupReply{}, err
	}

	return &pb.ListUserGroupReply{UserGroupList: result, Count: resp.Count}, nil
}
func (s *AdminService) CreateUserGroupCustom(ctx context.Context, req *pb.CreateUserGroupRequestCustom) (*pb.CreateUserGroupReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateUserGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	var param dto.CreateGroupParam
	if err := copier.Copy(&param, req); err != nil {
		return &pb.CreateUserGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId

	err = s.userGroup.CreateUserGroup(ctx, param)
	if err != nil {
		return &pb.CreateUserGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateUserGroupReply{Status: pb.StatusCode_SUCCESS}, nil
}
func (s *AdminService) CreateRootGroup(ctx context.Context, req *pb.CreateRootGroupRequest) (*pb.CreateRootGroupReply, error) {
	var param dto.CreateRootGroupParam
	if err := copier.Copy(&param, req); err != nil {
		return &pb.CreateRootGroupReply{}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateRootGroupReply{}, err
	}
	param.CorpId = corpId
	if err := s.userGroup.CreateRootGroup(ctx, param); err != nil {
		return &pb.CreateRootGroupReply{}, err
	}
	return &pb.CreateRootGroupReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) ListRootGroup(ctx context.Context, req *pb.ListRootGroupRequest) (*pb.ListRootGroupReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListRootGroupReply{}, err
	}

	limit, offset := common.GetLimitOffset(req.Limit, req.Offset)
	resp, err := s.userGroup.ListRootGroup(ctx, corpId, limit, offset)
	if err != nil {
		return &pb.ListRootGroupReply{}, err
	}
	var result []*pb.ListRootGroupReply_RootGroupInfo
	for _, g := range resp.RootGroups {
		var temp pb.ListRootGroupReply_RootGroupInfo
		if err := copier.Copy(&temp, &g); err != nil {
			return &pb.ListRootGroupReply{}, err
		}
		temp.Id = g.ID
		if !g.SyncTime.IsZero() && !(g.SyncTime.Local().Format(dto.CommonTimeFormat) == dto.TimeZero) {
			temp.SyncTime = g.SyncTime.Local().Format(dto.CommonTimeFormat)
		}
		result = append(result, &temp)
	}
	return &pb.ListRootGroupReply{GroupInfos: result, Count: resp.Count}, nil
}

func (s *AdminService) GetRootGroupDetail(ctx context.Context, req *pb.GetRootGroupDetailRequest) (*pb.GetRootGroupDetailReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.GetRootGroupDetailReply{}, err
	}
	resp, err := s.userGroup.GetRootGroup(ctx, corpId, req.GroupId)
	if err != nil {
		return &pb.GetRootGroupDetailReply{}, err
	}
	var groupInfo pb.GetRootGroupDetailReply_GroupBasicInfo
	err = copier.Copy(&groupInfo, &resp.GroupBasicInfo)
	if err != nil {
		s.log.Errorf("CopierGroupInfoFailed err=%v", err)
		return &pb.GetRootGroupDetailReply{}, err
	}

	var wxInfo pb.GetRootGroupDetailReply_WxConfig
	var fsInfo pb.GetRootGroupDetailReply_FeiShuConfig
	var dingtalk pb.GetRootGroupDetailReply_DingtalkConfig
	var adInfo pb.GetRootGroupDetailReply_AdConfig
	var infogo pb.GetRootGroupDetailReply_InfogoConfig
	var oauth2 pb.GetRootGroupDetailReply_OAuth2Config
	switch dto.UserSourceType(resp.SourceType) {
	case dto.UserSourceLocal:
		copier.Copy(&wxInfo, &resp.SyncGroupInfo)
		if err != nil {
			s.log.Errorf("CopierLocalInfoFailed err=%v", err)
			return &pb.GetRootGroupDetailReply{}, err
		}
	case dto.UserSourceQiYeWx:
		copier.Copy(&wxInfo, &resp.SyncGroupInfo)
		if err != nil {
			s.log.Errorf("CopierQiYeWxInfoFailed err=%v", err)
			return &pb.GetRootGroupDetailReply{}, err
		}
	case dto.UserSourceFeiShu:
		copier.Copy(&fsInfo, &resp.SyncGroupInfo)
		fsInfo.AppSecret = resp.SyncGroupInfo.FeishuConfig.AppSecret
		if err != nil {
			s.log.Errorf("CopierFeiShuInfoFailed err=%v", err)
			return &pb.GetRootGroupDetailReply{}, err
		}
	case dto.UserSourceDingtalk:
		copier.Copy(&dingtalk, &resp.SyncGroupInfo)
		dingtalk.AppSecret = resp.SyncGroupInfo.DingtalkConfig.AppSecret
		if err != nil {
			s.log.Errorf("CopierDingtalkInfoFailed err=%v", err)
			return &pb.GetRootGroupDetailReply{}, err
		}
	case dto.UserSourceLdap:
		copier.Copy(&adInfo, &resp.SyncGroupInfo)
		adInfo.AdministratorPassword = resp.SyncGroupInfo.AdministratorPassword
		if err != nil {
			s.log.Errorf("CopierLdapInfoFailed err=%v", err)
			return &pb.GetRootGroupDetailReply{}, err
		}
	case dto.UserSourceMsad:
		copier.Copy(&adInfo, &resp.SyncGroupInfo)
		adInfo.AdministratorPassword = resp.SyncGroupInfo.AdministratorPassword
		if err != nil {
			s.log.Errorf("CopierMsadInfoFailed err=%v", err)
			return &pb.GetRootGroupDetailReply{}, err
		}
	case dto.UserSourceInfogo:
		copier.Copy(&infogo, &resp.SyncGroupInfo)
		adInfo.AdministratorPassword = resp.SyncGroupInfo.AdministratorPassword
		if err != nil {
			s.log.Errorf("CopierMsadInfoFailed err=%v", err)
			return &pb.GetRootGroupDetailReply{}, err
		}
	case dto.UserSourceOAuth2:
		copier.Copy(&oauth2, &resp.SyncGroupInfo)
	default:
		switch dto.UserSourceType(resp.TemplateType) {
		case dto.UserSourceOAuth2:
			copier.Copy(&oauth2, &resp.SyncGroupInfo)
		}
	}

	return &pb.GetRootGroupDetailReply{GroupInfo: &groupInfo, WxConfig: &wxInfo, FeishuConfig: &fsInfo, DingtalkConfig: &dingtalk, AdConfig: &adInfo, InfogoConfig: &infogo, OAuth2Config: &oauth2}, nil
}
func (s *AdminService) UpdateRootGroupCustom(ctx context.Context, req *pb.UpdateRootGroupRequest) (*pb.UpdateRootGroupReply, error) {
	return s.UpdateRootGroup(ctx, req)
}
func (s *AdminService) DeleteRootGroupCustom(ctx context.Context, req *pb.DeleteRootGroupRequest) (*pb.DeleteRootGroupReply, error) {
	return s.DeleteRootGroup(ctx, req)
}
func (s *AdminService) SwitchAutoSync(ctx context.Context, req *pb.SwitchAutoSyncRequest) (*pb.SwitchAutoSyncReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.SwitchAutoSyncReply{Status: pb.StatusCode_FAILED}, err
	}
	if err := s.userGroup.SwitchAutoSync(ctx, corpId, req.GroupId, req.AutoSync); err != nil {
		return &pb.SwitchAutoSyncReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.SwitchAutoSyncReply{Status: pb.StatusCode_SUCCESS}, nil
}
func (s *AdminService) UpdateRootGroup(ctx context.Context, req *pb.UpdateRootGroupRequest) (*pb.UpdateRootGroupReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateRootGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	var param dto.UpdateGroupParam
	if err := copier.Copy(&param, &req); err != nil {
		return &pb.UpdateRootGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId
	if err := s.userGroup.UpdateRootGroup(ctx, corpId, param); err != nil {
		return &pb.UpdateRootGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateRootGroupReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) DeleteRootGroup(ctx context.Context, req *pb.DeleteRootGroupRequest) (*pb.DeleteRootGroupReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.DeleteRootGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	if err := s.userGroup.DeleteRootGroup(ctx, corpId, req.GroupId, req.Name); err != nil {
		return &pb.DeleteRootGroupReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.DeleteRootGroupReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) GetRootGroupIdpList(ctx context.Context, req *pb.GetRootGroupIdpListRequest) (*pb.GetRootGroupIdpListReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.GetRootGroupIdpListReply{}, err
	}

	resp, err := s.userGroup.GetRootGroupIdpList(ctx, corpId, req.RootGroupId)
	if err != nil {
		return &pb.GetRootGroupIdpListReply{}, err
	}
	var mainIdpInfos, assistIdpInfos []*pb.GetRootGroupIdpListReply_IdpInfo
	for _, idp := range resp.MainIdpList {
		mainIdpInfos = append(mainIdpInfos, &pb.GetRootGroupIdpListReply_IdpInfo{
			Id:   idp.ID,
			Name: idp.Name,
			Type: idp.Type,
		})
	}
	for _, idp := range resp.AssistIdpList {
		assistIdpInfos = append(assistIdpInfos, &pb.GetRootGroupIdpListReply_IdpInfo{
			Id:   idp.ID,
			Name: idp.Name,
			Type: idp.Type,
		})
	}
	return &pb.GetRootGroupIdpListReply{MainIdpList: mainIdpInfos, AssistIdpList: assistIdpInfos}, nil
}

func (s *AdminService) SyncTrigger(ctx context.Context, req *pb.SyncTriggerRequest) (*pb.SyncTriggerReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.SyncTriggerReply{Status: pb.StatusCode_FAILED}, err
	}
	//TODO 这里需要 1. 检查有没有正在执行的同步认证，如果有则提示用户 2.在synclog表结构中添加taskid，调用时生成taskid并且状态置为运行中
	go func() {
		s.user.SyncUser(ctx, corpId, req.RootGroupId, dto.SyncTypeCustom)
	}()

	return &pb.SyncTriggerReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) ListSyncLog(ctx context.Context, req *pb.ListSyncLogRequest) (*pb.ListSyncLogReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListSyncLogReply{}, err
	}
	limit, offset := common.GetLimitOffset(req.Limit, req.Offset)
	result, err := s.userGroup.ListSyncLog(ctx, corpId, req.RootGroupId, int(limit), int(offset))
	if err != nil {
		return &pb.ListSyncLogReply{}, err
	}
	var syncLogs []*pb.ListSyncLogReply_SyncLog

	for _, e := range result.SyncLogs {
		var syncLog pb.ListSyncLogReply_SyncLog
		if err := copier.Copy(&syncLog, e); err != nil {
			return &pb.ListSyncLogReply{}, err
		}
		if !e.CreatedAt.IsZero() && !(e.CreatedAt.Local().Format(dto.CommonTimeFormat) == dto.TimeZero) {
			syncLog.SyncTime = e.CreatedAt.Local().Format(dto.CommonTimeFormat)
		}
		syncLogs = append(syncLogs, &syncLog)
	}
	return &pb.ListSyncLogReply{SyncLogs: syncLogs, Count: result.Count}, nil
}
