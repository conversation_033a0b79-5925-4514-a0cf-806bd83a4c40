// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.20.0
// source: appliance/v1/info_collect.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InfoCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId       uint64          `protobuf:"varint,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	ProcessInfos  []*ProcessInfo  `protobuf:"bytes,2,rep,name=process_infos,json=processInfos,proto3" json:"process_infos,omitempty"`
	SoftwareInfos []*SoftwareInfo `protobuf:"bytes,3,rep,name=software_infos,json=softwareInfos,proto3" json:"software_infos,omitempty"`
	SystemInfos   []*SystemInfo   `protobuf:"bytes,4,rep,name=system_infos,json=systemInfos,proto3" json:"system_infos,omitempty"`
}

func (x *InfoCollectReq) Reset() {
	*x = InfoCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_info_collect_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoCollectReq) ProtoMessage() {}

func (x *InfoCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_info_collect_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoCollectReq.ProtoReflect.Descriptor instead.
func (*InfoCollectReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_info_collect_proto_rawDescGZIP(), []int{0}
}

func (x *InfoCollectReq) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *InfoCollectReq) GetProcessInfos() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfos
	}
	return nil
}

func (x *InfoCollectReq) GetSoftwareInfos() []*SoftwareInfo {
	if x != nil {
		return x.SoftwareInfos
	}
	return nil
}

func (x *InfoCollectReq) GetSystemInfos() []*SystemInfo {
	if x != nil {
		return x.SystemInfos
	}
	return nil
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_info_collect_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_info_collect_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_appliance_v1_info_collect_proto_rawDescGZIP(), []int{1}
}

type ProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessName   string  `protobuf:"bytes,1,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	ProcPath      string  `protobuf:"bytes,2,opt,name=proc_path,json=procPath,proto3" json:"proc_path,omitempty"`
	CmdLine       string  `protobuf:"bytes,3,opt,name=cmd_line,json=cmdLine,proto3" json:"cmd_line,omitempty"`
	UserName      string  `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	ProcessPid    int32   `protobuf:"varint,5,opt,name=process_pid,json=processPid,proto3" json:"process_pid,omitempty"`
	ParentPid     int32   `protobuf:"varint,6,opt,name=parent_pid,json=parentPid,proto3" json:"parent_pid,omitempty"`
	MemUsedKb     int32   `protobuf:"varint,7,opt,name=mem_used_kb,json=memUsedKb,proto3" json:"mem_used_kb,omitempty"`
	VmmemUsedKb   int32   `protobuf:"varint,8,opt,name=vmmem_used_kb,json=vmmemUsedKb,proto3" json:"vmmem_used_kb,omitempty"`
	ThreadNum     int32   `protobuf:"varint,9,opt,name=thread_num,json=threadNum,proto3" json:"thread_num,omitempty"`
	CpuKernelUsed float64 `protobuf:"fixed64,10,opt,name=cpu_kernel_used,json=cpuKernelUsed,proto3" json:"cpu_kernel_used,omitempty"`
	CpuUserUsed   float64 `protobuf:"fixed64,11,opt,name=cpu_user_used,json=cpuUserUsed,proto3" json:"cpu_user_used,omitempty"`
}

func (x *ProcessInfo) Reset() {
	*x = ProcessInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_info_collect_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInfo) ProtoMessage() {}

func (x *ProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_info_collect_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInfo.ProtoReflect.Descriptor instead.
func (*ProcessInfo) Descriptor() ([]byte, []int) {
	return file_appliance_v1_info_collect_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessInfo) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *ProcessInfo) GetProcPath() string {
	if x != nil {
		return x.ProcPath
	}
	return ""
}

func (x *ProcessInfo) GetCmdLine() string {
	if x != nil {
		return x.CmdLine
	}
	return ""
}

func (x *ProcessInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ProcessInfo) GetProcessPid() int32 {
	if x != nil {
		return x.ProcessPid
	}
	return 0
}

func (x *ProcessInfo) GetParentPid() int32 {
	if x != nil {
		return x.ParentPid
	}
	return 0
}

func (x *ProcessInfo) GetMemUsedKb() int32 {
	if x != nil {
		return x.MemUsedKb
	}
	return 0
}

func (x *ProcessInfo) GetVmmemUsedKb() int32 {
	if x != nil {
		return x.VmmemUsedKb
	}
	return 0
}

func (x *ProcessInfo) GetThreadNum() int32 {
	if x != nil {
		return x.ThreadNum
	}
	return 0
}

func (x *ProcessInfo) GetCpuKernelUsed() float64 {
	if x != nil {
		return x.CpuKernelUsed
	}
	return 0
}

func (x *ProcessInfo) GetCpuUserUsed() float64 {
	if x != nil {
		return x.CpuUserUsed
	}
	return 0
}

type SoftwareInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SoftwareName    string `protobuf:"bytes,1,opt,name=software_name,json=softwareName,proto3" json:"software_name,omitempty"`
	SoftwareVersion string `protobuf:"bytes,2,opt,name=software_version,json=softwareVersion,proto3" json:"software_version,omitempty"`
	SoftwareCompany string `protobuf:"bytes,3,opt,name=software_company,json=softwareCompany,proto3" json:"software_company,omitempty"`
	UninstallCmd    string `protobuf:"bytes,4,opt,name=uninstall_cmd,json=uninstallCmd,proto3" json:"uninstall_cmd,omitempty"`
	InstallPath     string `protobuf:"bytes,5,opt,name=install_path,json=installPath,proto3" json:"install_path,omitempty"`
}

func (x *SoftwareInfo) Reset() {
	*x = SoftwareInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_info_collect_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareInfo) ProtoMessage() {}

func (x *SoftwareInfo) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_info_collect_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareInfo.ProtoReflect.Descriptor instead.
func (*SoftwareInfo) Descriptor() ([]byte, []int) {
	return file_appliance_v1_info_collect_proto_rawDescGZIP(), []int{3}
}

func (x *SoftwareInfo) GetSoftwareName() string {
	if x != nil {
		return x.SoftwareName
	}
	return ""
}

func (x *SoftwareInfo) GetSoftwareVersion() string {
	if x != nil {
		return x.SoftwareVersion
	}
	return ""
}

func (x *SoftwareInfo) GetSoftwareCompany() string {
	if x != nil {
		return x.SoftwareCompany
	}
	return ""
}

func (x *SoftwareInfo) GetUninstallCmd() string {
	if x != nil {
		return x.UninstallCmd
	}
	return ""
}

func (x *SoftwareInfo) GetInstallPath() string {
	if x != nil {
		return x.InstallPath
	}
	return ""
}

type SystemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SystemBit      string `protobuf:"bytes,1,opt,name=system_bit,json=systemBit,proto3" json:"system_bit,omitempty"`
	ProductName    string `protobuf:"bytes,2,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`
	SystemLanguage string `protobuf:"bytes,3,opt,name=system_language,json=systemLanguage,proto3" json:"system_language,omitempty"`
	SystemVersion  string `protobuf:"bytes,4,opt,name=system_version,json=systemVersion,proto3" json:"system_version,omitempty"`
}

func (x *SystemInfo) Reset() {
	*x = SystemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_info_collect_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemInfo) ProtoMessage() {}

func (x *SystemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_info_collect_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemInfo.ProtoReflect.Descriptor instead.
func (*SystemInfo) Descriptor() ([]byte, []int) {
	return file_appliance_v1_info_collect_proto_rawDescGZIP(), []int{4}
}

func (x *SystemInfo) GetSystemBit() string {
	if x != nil {
		return x.SystemBit
	}
	return ""
}

func (x *SystemInfo) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *SystemInfo) GetSystemLanguage() string {
	if x != nil {
		return x.SystemLanguage
	}
	return ""
}

func (x *SystemInfo) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

var File_appliance_v1_info_collect_proto protoreflect.FileDescriptor

var file_appliance_v1_info_collect_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x69,
	0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65,
	0x22, 0xee, 0x01, 0x0a, 0x0e, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3f,
	0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x42, 0x0a, 0x0e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x22, 0x07, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xf4, 0x02, 0x0a, 0x0b, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6d,
	0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6d,
	0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x50, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x6b,
	0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x55, 0x73, 0x65, 0x64,
	0x4b, 0x62, 0x12, 0x22, 0x0a, 0x0d, 0x76, 0x6d, 0x6d, 0x65, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x64,
	0x5f, 0x6b, 0x62, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x76, 0x6d, 0x6d, 0x65, 0x6d,
	0x55, 0x73, 0x65, 0x64, 0x4b, 0x62, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x61, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x70, 0x75, 0x5f, 0x6b, 0x65, 0x72,
	0x6e, 0x65, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d,
	0x63, 0x70, 0x75, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x63, 0x70, 0x75, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x70, 0x75, 0x55, 0x73, 0x65, 0x72, 0x55, 0x73, 0x65,
	0x64, 0x22, 0xd1, 0x01, 0x0a, 0x0c, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x66, 0x74, 0x77,
	0x61, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x6f, 0x66, 0x74, 0x77,
	0x61, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x23, 0x0a,
	0x0d, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6d, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x43,
	0x6d, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x50, 0x61, 0x74, 0x68, 0x22, 0x9e, 0x01, 0x0a, 0x0a, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x62,
	0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x42, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x5b, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a,
	0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x42, 0x2e, 0x5a, 0x2c, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_appliance_v1_info_collect_proto_rawDescOnce sync.Once
	file_appliance_v1_info_collect_proto_rawDescData = file_appliance_v1_info_collect_proto_rawDesc
)

func file_appliance_v1_info_collect_proto_rawDescGZIP() []byte {
	file_appliance_v1_info_collect_proto_rawDescOnce.Do(func() {
		file_appliance_v1_info_collect_proto_rawDescData = protoimpl.X.CompressGZIP(file_appliance_v1_info_collect_proto_rawDescData)
	})
	return file_appliance_v1_info_collect_proto_rawDescData
}

var file_appliance_v1_info_collect_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_appliance_v1_info_collect_proto_goTypes = []interface{}{
	(*InfoCollectReq)(nil), // 0: api.appliance.InfoCollectReq
	(*Reply)(nil),          // 1: api.appliance.Reply
	(*ProcessInfo)(nil),    // 2: api.appliance.ProcessInfo
	(*SoftwareInfo)(nil),   // 3: api.appliance.SoftwareInfo
	(*SystemInfo)(nil),     // 4: api.appliance.SystemInfo
}
var file_appliance_v1_info_collect_proto_depIdxs = []int32{
	2, // 0: api.appliance.InfoCollectReq.process_infos:type_name -> api.appliance.ProcessInfo
	3, // 1: api.appliance.InfoCollectReq.software_infos:type_name -> api.appliance.SoftwareInfo
	4, // 2: api.appliance.InfoCollectReq.system_infos:type_name -> api.appliance.SystemInfo
	0, // 3: api.appliance.ReportCollectedInfo.CollectedInfo:input_type -> api.appliance.InfoCollectReq
	1, // 4: api.appliance.ReportCollectedInfo.CollectedInfo:output_type -> api.appliance.Reply
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_appliance_v1_info_collect_proto_init() }
func file_appliance_v1_info_collect_proto_init() {
	if File_appliance_v1_info_collect_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_appliance_v1_info_collect_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_info_collect_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_info_collect_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_info_collect_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_info_collect_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_appliance_v1_info_collect_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_appliance_v1_info_collect_proto_goTypes,
		DependencyIndexes: file_appliance_v1_info_collect_proto_depIdxs,
		MessageInfos:      file_appliance_v1_info_collect_proto_msgTypes,
	}.Build()
	File_appliance_v1_info_collect_proto = out.File
	file_appliance_v1_info_collect_proto_rawDesc = nil
	file_appliance_v1_info_collect_proto_goTypes = nil
	file_appliance_v1_info_collect_proto_depIdxs = nil
}
