// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbCredential = "tb_credential"

// TbCredential mapped from table <tb_credential>
type TbCredential struct {
	ID             string    `gorm:"column:id;primaryKey" json:"id"`
	UserID         string    `gorm:"column:user_id;not null" json:"user_id"`
	Type           string    `gorm:"column:type;not null" json:"type"`
	SecretData     string    `gorm:"column:secret_data;not null" json:"secret_data"`
	CredentialData string    `gorm:"column:credential_data;not null" json:"credential_data"`
	CorpID         string    `gorm:"column:corp_id" json:"corp_id"`
	CreatedAt      time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbCredential's table name
func (*TbCredential) TableName() string {
	return TableNameTbCredential
}
