package access_log

import (
	"sync"

	pb "asdsec.com/asec/platform/api/accesslog/v1"
)

var (
	Pool = sync.Pool{
		New: func() interface{} {
			return &pb.AccessLogMsg{}
		},
	}
)

func ClearAccessLogsPool(recs []*pb.AccessLogMsg) {
	for _, rec := range recs {
		// 手动释放值
		rec.SrcIp = ""
		rec.SrcPort = 0
		rec.DstIp = ""
		rec.DstPort = 0
		rec.Protocol = ""
		rec.DestinationUrl = ""
		rec.Status = 0
		rec.DenyReason = ""
		rec.AccessUserId = ""
		rec.AccessUsername = ""
		rec.AppId = 0
		rec.AppName = ""
		rec.StrategyId = 0
		rec.StrategyName = ""
		rec.Iss = ""
		rec.AccessTime = nil
		rec.ClientId = 0
		rec.ClientName = ""
		rec.StrategyCheckTime = 0
		Pool.Put(rec)
	}
}
