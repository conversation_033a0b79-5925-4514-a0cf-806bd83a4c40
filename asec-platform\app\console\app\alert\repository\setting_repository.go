package repository

import (
	alertComm "asdsec.com/asec/platform/app/console/app/alert/common"
	"asdsec.com/asec/platform/app/console/app/alert/model"
	global "asdsec.com/asec/platform/app/console/global"
	"context"
	"errors"
	"gorm.io/gorm"
)

type settingRepository struct {
}

func (s *settingRepository) GetSettingByName(ctx context.Context, tenantId uint64, eventName string) (model.AlertSetting, error) {
	settings := model.AlertSetting{}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return settings, err
	}
	conn := db.Table(alertComm.AlertSettingTable)
	err = conn.Where("tenant_id = ? and event_name = ?", tenantId, eventName).Find(&settings).Error
	if err != nil {
		return settings, err
	}
	return settings, nil
}

func (s *settingRepository) GetSetting(ctx context.Context, tenantId uint64) ([]model.AlertSetting, error) {
	var settings []model.AlertSetting
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return settings, err
	}
	conn := db.Table(alertComm.AlertSettingTable)
	err = conn.Where("tenant_id = ?", tenantId).Order("id").Find(&settings).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return settings, nil
}

func (s *settingRepository) UpdateSetting(ctx context.Context, SettingId uint64, setting map[string]interface{}) error {
	var alertSetting []model.AlertSetting
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	conn := db.Table(alertComm.AlertSettingTable)
	err = conn.Where("id = ?", SettingId).Find(&alertSetting).Error
	if err != nil {
		return err
	}
	if len(alertSetting) < 1 {
		return errors.New("record not found")
	}
	return conn.Updates(setting).Error
}

func NewSettingRepository() SettingRepository {
	return &settingRepository{}
}

type SettingRepository interface {
	GetSetting(ctx context.Context, tenantId uint64) ([]model.AlertSetting, error)
	UpdateSetting(ctx context.Context, SettingId uint64, setting map[string]interface{}) error
	GetSettingByName(ctx context.Context, tenantId uint64, eventName string) (model.AlertSetting, error)
}
