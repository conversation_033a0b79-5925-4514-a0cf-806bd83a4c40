package service

import (
	"asdsec.com/asec/platform/pkg/service"
	"asdsec.com/asec/platform/pkg/service/cfg_service"
	"github.com/google/wire"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(
	service.NewApplianceMgtService,
	service.NewAppService,
	service.NewAgentAppService,
	service.NewSenElemService,
	cfg_service.NewApplianceCfgService,
	service.NewES256KeyService,
	service.NewControlService,
	service.NewSpecialConfigService,
	service.NewScanTaskService,
)
