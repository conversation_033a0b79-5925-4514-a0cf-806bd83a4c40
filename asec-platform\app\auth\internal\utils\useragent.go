package utils

import (
	"strings"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/mssola/user_agent"
)

// UserAgentParser 用户代理解析器
type UserAgentParser struct {
	// mssola/user_agent 库不需要预先创建解析器实例
}

// NewUserAgentParser 创建用户代理解析器
func NewUserAgentParser() (*UserAgentParser, error) {
	return &UserAgentParser{}, nil
}

// ParseUserAgent 解析用户代理字符串
func (p *UserAgentParser) ParseUserAgent(userAgent string) *dto.UserAgentInfo {
	if userAgent == "" {
		return &dto.UserAgentInfo{
			ClientType:     dto.ClientTypeUnknown,
			ClientCategory: dto.ClientCategoryPC, // 默认归类为PC
		}
	}

	// 使用 mssola/user_agent 解析
	ua := user_agent.New(userAgent)
	
	// 获取操作系统信息
	osName := ua.OS()
	
	// 获取浏览器信息
	browserName, browserVersion := ua.Browser()
	
	// 映射操作系统到我们的ClientType
	clientType := mapOSToClientTypeSimple(osName, ua.Mobile())

	return &dto.UserAgentInfo{
		UserAgent:      userAgent,
		Browser:        browserName,
		BrowserVer:     browserVersion,
		OS:             osName,
		OSVersion:      "", // mssola/user_agent 不提供详细版本信息
		DeviceType:     getDeviceType(ua),
		DeviceBrand:    "", // mssola/user_agent 不提供设备品牌信息
		DeviceModel:    "", // mssola/user_agent 不提供设备型号信息
		ClientType:     clientType,
		ClientCategory: clientType.GetClientCategory(),
	}
}

// mapOSToClientTypeSimple 将操作系统映射到我们的ClientType（简化版）
func mapOSToClientTypeSimple(osName string, isMobile bool) dto.ClientType {
	osLower := strings.ToLower(osName)
	
	// 移动端操作系统检测
	switch {
	case strings.Contains(osLower, "android"):
		return dto.ClientTypeAndroid
	case strings.Contains(osLower, "ios") || strings.Contains(osLower, "iphone"):
		return dto.ClientTypeIOS
	case strings.Contains(osLower, "ipados") || strings.Contains(osLower, "ipad"):
		return dto.ClientTypeIPadOS
	}
	
	// 桌面操作系统检测
	switch {
	case strings.Contains(osLower, "windows"):
		return dto.ClientTypeWindows
	case strings.Contains(osLower, "mac") || strings.Contains(osLower, "darwin"):
		return dto.ClientTypeMacOS
	case strings.Contains(osLower, "linux"):
		return dto.ClientTypeLinux
	case strings.Contains(osLower, "unix"):
		return dto.ClientTypeUnix
	case strings.Contains(osLower, "freebsd"):
		return dto.ClientTypeFreeBSD
	}
	
	// 如果操作系统无法识别，根据是否移动设备来判断
	if isMobile {
		return dto.ClientTypeAndroid // 默认移动设备类型
	}
	
	// 默认为未知类型
	return dto.ClientTypeUnknown
}

// getDeviceType 获取设备类型
func getDeviceType(ua *user_agent.UserAgent) string {
	if ua.Mobile() {
		return "mobile"
	}
	return "desktop"
}

// ParseUserAgentSimple 简化的用户代理解析（不依赖外部库的备用方案）
func ParseUserAgentSimple(userAgent string) *dto.UserAgentInfo {
	if userAgent == "" {
		return &dto.UserAgentInfo{
			ClientType:     dto.ClientTypeUnknown,
			ClientCategory: dto.ClientCategoryPC,
		}
	}

	userAgent = strings.ToLower(userAgent)

	var clientType dto.ClientType
	var os, browser string

	// 移动端检测
	switch {
	case strings.Contains(userAgent, "android"):
		clientType = dto.ClientTypeAndroid
		os = "Android"
	case strings.Contains(userAgent, "iphone") || strings.Contains(userAgent, "ios"):
		clientType = dto.ClientTypeIOS
		os = "iOS"
	case strings.Contains(userAgent, "ipad"):
		clientType = dto.ClientTypeIPadOS
		os = "iPadOS"
	// 桌面端检测
	case strings.Contains(userAgent, "windows"):
		clientType = dto.ClientTypeWindows
		os = "Windows"
	case strings.Contains(userAgent, "macintosh") || strings.Contains(userAgent, "mac os"):
		clientType = dto.ClientTypeMacOS
		os = "macOS"
	case strings.Contains(userAgent, "linux"):
		clientType = dto.ClientTypeLinux
		os = "Linux"
	default:
		clientType = dto.ClientTypeUnknown
		os = "Unknown"
	}

	// 浏览器检测
	switch {
	case strings.Contains(userAgent, "chrome"):
		browser = "Chrome"
	case strings.Contains(userAgent, "firefox"):
		browser = "Firefox"
	case strings.Contains(userAgent, "safari"):
		browser = "Safari"
	case strings.Contains(userAgent, "edge"):
		browser = "Edge"
	case strings.Contains(userAgent, "opera"):
		browser = "Opera"
	default:
		browser = "Unknown"
	}

	return &dto.UserAgentInfo{
		UserAgent:      userAgent,
		Browser:        browser,
		OS:             os,
		ClientType:     clientType,
		ClientCategory: clientType.GetClientCategory(),
	}
}
