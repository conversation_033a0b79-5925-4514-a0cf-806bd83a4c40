package dto

import pb "asdsec.com/asec/platform/api/auth/v1"

// ErrReasonChineseMap 接口对外输出的错误信息
var ErrReasonChineseMap = map[string]string{
	pb.ErrorReason_PARAM_ERROR.String(): "参数错误",

	pb.ErrorReason_PASS_WORD_NOT_CORRECT.String():       "原密码不正确",
	pb.ErrorReason_ACCOUNT_OR_PASSWORD_ERROR.String():   "账号或者密码错误",
	pb.ErrorReason_DISABLE_ERROR.String():               "已禁用，请联系管理员",
	pb.ErrorReason_NAME_CONFLICT.String():               "名称冲突",
	pb.ErrorReason_USER_ENTITY_UNIQUE_CONFLICT.String(): "用户名与他人重复",
	pb.ErrorReason_TOKEN_EXPIRE.String():                "token已过期",
	pb.ErrorReason_TOKEN_INVALID.String():               "token无效",
	pb.ErrorReason_TOKEN_PARSE_FAILED.String():          "token解析失败",
	pb.ErrorReason_QIYEWX_TRUSTED_IP_ERROR.String():     "企业微信可信ip设置不正确",
	pb.ErrorReason_QIYEWX_CONFIG_ERROR.String():         "企业微信配置不正确",
	pb.ErrorReason_CACHE_ERROR.String():                 "会话过期，请刷新当前页面后重试",
	pb.ErrorReason_EXPIRE_ERROR.String():                "已过期",
	pb.ErrorReason_FIELD_MAP_CONFIG_ERROR.String():      "字段映射配置错误",
	pb.ErrorReason_SMS_CODE_INVALID_ERROR.String():      "验证码已失效",
	pb.ErrorReason_SMS_CODE_ERROR.String():              "验证码错误",
	pb.ErrorReason_AUTH_CHAIN_FAILURE.String():          "验证码已过期",
	pb.ErrorReason_NOT_MAIN_AUTH.String():               "未进行主认证或主认证已失效",
	pb.ErrorReason_NOT_PHONE.String():                   "用户未配置手机号码，请联系管理员配置",
	pb.ErrorReason_FEISHU_SYNC_ERROR.String():           "飞书同步失败，请查看同步日志",
	pb.ErrorReason_SEND_SMS_ERROR.String():              "验证码发送失败，请联系管理员",
	pb.ErrorReason_USER_NOT_FOUND.String():              "用户未找到，请先同步组织架构及用户数据后再尝试登录",
	pb.ErrorReason_EMAIL_NOT_EXISTS.String():            "用户邮箱地址不存在",
	pb.ErrorReason_EMAIL_FORMAT_INVALID.String():        "邮箱格式不正确",
	pb.ErrorReason_EMAIL_CODE_INVALID_ERROR.String():    "邮箱验证码已失效",
	pb.ErrorReason_EMAIL_CODE_ERROR.String():            "邮箱验证码错误",
	pb.ErrorReason_EMAIL_SEND_ERROR.String():            "邮箱验证码发送失败",
	pb.ErrorReason_EMAIL_AUTH_FAILURE.String():          "邮箱验证失败",
	pb.ErrorReason_AUTH_SERVER_CONFIG_ERROR.String():    "认证服务器配置错误，请联系管理员",
	pb.ErrorReason_AUTH_SERVER_CONNECT_ERROR.String():   "认证服务器连接错误，请联系管理员",
	pb.ErrorReason_NETWORK_CONNECTION_ERROR.String():    "网络连接异常，请稍后重试",
	pb.ErrorReason_AUTH_SEARCH_NOT_UNIQUE.String():      "根据用户查找关系，找到多个用户，请联系管理员处理！",
	pb.ErrorReason_AUTH_USER_LOCK.String():              "您的账户已被锁定，请联系管理员处理！",
	pb.ErrorReason_SECURITY_CODE_ERROR.String():         "访问码人码不一致",
	pb.ErrorReason_CLIENT_LIMIT_EXCEEDED.String():       "客户端数量已达上限，请退出其他设备后重试",
}
