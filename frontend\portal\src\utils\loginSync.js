/**
 * 登录同步模块
 * 实现浏览器和客户端之间的登录状态同步
 */

import {
  initLoginSync,
  sendLoginSync,
  sendLogoutSync,
  isWebSocketConnected,
  removeLoginSyncHand<PERSON>,
  removeLogoutSyncHandler,
  addLoginSyncHandler,
  addLogoutSyncHandler
} from './websocket.js'
import { useUserStore } from '@/pinia/modules/user'

/**
 * 登录同步管理器
 */
class LoginSyncManager {
  constructor() {
    this.isInitialized = false
    this.userStore = null
    this.loginSyncHandler = null
    this.logoutSyncHandler = null
    this.initOptions = null

    // 监听WebSocket重连事件
    if (typeof window !== 'undefined') {
      window.addEventListener('websocket-reconnected', this._handleWebSocketReconnected.bind(this))
    }
  }

  /**
   * 初始化登录同步
   * @param {Object} options 配置选项
   * @param {number} options.port WebSocket端口
   * @returns {Promise}
   */
  async init(options = {}) {
    // 保存初始化选项，用于重连时重新注册
    this.initOptions = options

    if (this.isInitialized) {
      logger.log('登录同步已经初始化')
      return
    }

    try {
      this.userStore = useUserStore()

      // 创建同步处理器
      this.loginSyncHandler = this._handleLoginSync.bind(this)
      this.logoutSyncHandler = this._handleLogoutSync.bind(this)

      // 初始化WebSocket连接和同步处理器
      await initLoginSync({
        onLogin: this.loginSyncHandler,
        onLogout: this.logoutSyncHandler,
        port: options.port
      })

      this.isInitialized = true
      logger.log('登录同步初始化成功')
    } catch (error) {
      logger.log('登录同步初始化失败:', error)
      // 不抛出错误，避免阻止应用启动
      // 当WebSocket重连时会自动重试
    }
  }

  /**
   * 处理WebSocket重连事件
   * @private
   */
  _handleWebSocketReconnected() {
    logger.log('收到WebSocket重连事件，重新注册登录同步处理器')

    if (!this.loginSyncHandler || !this.logoutSyncHandler) {
      logger.log('处理器未创建，跳过重新注册')
      return
    }

    if (!this.initOptions) {
      logger.log('没有保存的初始化选项，跳过重新注册')
      return
    }

    // 重新注册处理器
    try {
      logger.log('重新注册登录同步处理器')
      addLoginSyncHandler(this.loginSyncHandler)
      addLogoutSyncHandler(this.logoutSyncHandler)
      logger.log('登录同步处理器重新注册完成')
    } catch (error) {
      logger.log('重新注册登录同步处理器失败:', error)
    }
  }

  /**
   * 处理登录同步
   * @param {Object} loginData 登录数据
   * @private
   */
  async _handleLoginSync(loginData) {
    try {
      if (this.userStore.isTokenValid()) {
        logger.log('本地已登录，忽略登录同步')
        return
      }
      logger.log('开始处理登录同步:', loginData)

      if (!loginData) {
        logger.log('登录数据为空，跳过处理')
        return
      }

      if (!loginData.token) {
        logger.log('登录数据中没有token，跳过处理')
        return
      }

      logger.log('准备设置用户token')

      // 设置用户token
      const tokenData = {
        accessToken: loginData.token,
        refreshToken: loginData.refreshToken,
        tokenType: 'Bearer'
      }

      logger.log('调用 setToken，数据:', tokenData)
      if (loginData.authId) {
        this.userStore.setLoginType(loginData.authId)
      }
      await this.userStore.setToken(tokenData)
      logger.log('setToken 完成')

      // 调用封装的登录后设置函数（包含获取用户信息、设置路由、跳转等逻辑）
      logger.log('开始执行登录后设置')
      await this.userStore.handlePostLoginSetup()
      logger.log('登录后设置完成')

      logger.log('登录同步处理完成')
    } catch (error) {
      logger.log('处理登录同步失败:', error)
      logger.log('错误详情:', error.stack)
    }
  }

  /**
   * 处理退出同步
   * @param {Object} logoutData 退出数据
   * @private
   */
  async _handleLogoutSync(logoutData) {
    try {
      if (!this.userStore.token) {
        logger.log('本地已退出登录，忽略退出登录同步')
        return
      }
      logger.log('处理退出同步:', logoutData)

      // 执行退出登录，传入 true 表示来自广播通知，避免重复发送广播
      await this.userStore.LoginOut(true)

      logger.log('退出同步处理完成')
    } catch (error) {
      logger.log('处理退出同步失败:', error)
    }

    // 退出登录后
    await this.userStore.handlePostLogoutSetup()
  }

  /**
   * 广播登录信息
   * @param {Object} loginData 登录数据
   * @returns {boolean} 发送是否成功
   */
  broadcastLogin(loginData) {
    if (!isWebSocketConnected()) {
      const clineData = {
        action: 0,
        msg: {
          token: loginData.accessToken,
          refreshToken: loginData.refreshToken,
          realm: 'default',
        },
        platform: document.location.hostname
      }
      window.location.href = `asecagent://?web=${JSON.stringify(clineData)}`
      logger.log('登录同步未初始化，无法广播登录信息')
      return false
    }

    const syncData = {
      token: loginData.accessToken,
      refreshToken: loginData.refreshToken,
      authId: loginData.authId,
      source: 'browser',
    }

    logger.log('广播登录信息:', syncData)
    return sendLoginSync(syncData)
  }

  /**
   * 广播退出信息
   * @param {Object} logoutData 退出数据
   * @returns {boolean} 发送是否成功
   */
  broadcastLogout(logoutData = {}) {
    if (!this.isInitialized) {
      logger.log('登录同步未初始化，无法广播退出信息')
      return false
    }

    const syncData = {
      ...logoutData,
      timestamp: Date.now(),
      source: 'browser'
    }

    logger.log('广播退出信息:', syncData)
    return sendLogoutSync(syncData)
  }

  /**
   * 销毁登录同步
   */
  destroy() {
    if (this.loginSyncHandler) {
      removeLoginSyncHandler(this.loginSyncHandler)
      this.loginSyncHandler = null
    }

    if (this.logoutSyncHandler) {
      removeLogoutSyncHandler(this.logoutSyncHandler)
      this.logoutSyncHandler = null
    }

    this.isInitialized = false
    this.userStore = null
    logger.log('登录同步已销毁')
  }
}

// 创建全局登录同步管理器实例
const loginSyncManager = new LoginSyncManager()

/**
 * 初始化登录同步
 * @param {Object} options 配置选项
 * @returns {Promise}
 */
export const initializeLoginSync = (options) => {
  return loginSyncManager.init(options)
}

/**
 * 广播登录信息到所有连接
 * @param {Object} loginData 登录数据
 * @returns {boolean}
 */
export const broadcastLogin = (loginData) => {
  return loginSyncManager.broadcastLogin(loginData)
}

/**
 * 广播退出信息到所有连接
 * @param {Object} logoutData 退出数据
 * @returns {boolean}
 */
export const broadcastLogout = (logoutData) => {
  return loginSyncManager.broadcastLogout(logoutData)
}

/**
 * 销毁登录同步
 */
export const destroyLoginSync = () => {
  loginSyncManager.destroy()
}

/**
 * 检查登录同步是否已初始化
 * @returns {boolean}
 */
export const isLoginSyncInitialized = () => {
  return loginSyncManager.isInitialized
}

export default loginSyncManager
