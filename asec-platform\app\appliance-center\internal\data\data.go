package data

import (
	"asdsec.com/asec/platform/app/appliance-center/internal/conf"
	"asdsec.com/asec/platform/pkg/data"
	"asdsec.com/asec/platform/pkg/data/cfg_data"
	"asdsec.com/asec/platform/pkg/job"
	"fmt"
	"github.com/go-redis/redis/v8"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	glogger "gorm.io/gorm/logger"
	stdlog "log"
	"os"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewGormClient, data.NewApplianceRepo, data.NewAppRepo, data.NewAgentAppRepo, data.NewScanTaskRepo,
	data.NewSenElemRepo, data.NewControlRepo, data.NewES256KeyRepo, cfg_data.NewAppRepo, data.NewSpecialConfigRepo)

// NewData .
func NewData(c *conf.Data, logger log.Logger, pg *gorm.DB) (*data.Data, func(), error) {
	l := log.NewHelper(log.With(logger, "module", "data/appliance-center"))
	rdb := redis.NewClient(&redis.Options{
		Addr:         c.Redis.Addr,
		Password:     c.Redis.Password,
		DB:           int(c.Redis.Db),
		DialTimeout:  c.Redis.DialTimeout.AsDuration(),
		WriteTimeout: c.Redis.WriteTimeout.AsDuration(),
		ReadTimeout:  c.Redis.ReadTimeout.AsDuration(),
	})
	d := &data.Data{
		Log: l,
		PG:  pg,
		Rdb: rdb,
	}
	cleanup := func() {
		l.Info("closing the database")
		db, err := d.PG.DB()
		if err != nil {
			l.Errorf("closing the database error: %v", err)
		}
		err = db.Close()
		if err != nil {
			l.Errorf("closing the database error: %v", err)
		}
	}
	// 启动定时任务
	// TODO cl job需要统一处理调度
	go job.InitTimer(*d)
	go job.BaseTimer(*d)
	return d, cleanup, nil
}

// NewGormClient 创建gorm 数据库
func NewGormClient(c *conf.Data, logger log.Logger) *gorm.DB {
	// 建立链接
	l := log.NewHelper(log.With(logger, "module", "gorm/data/appliance-center"))
	database := c.Database
	dsn := fmt.Sprintf("host=%v user=%v password=%v dbname=%v port=%v sslmode=disable TimeZone=Asia/Shanghai",
		database.Host, database.User, database.Password, database.Dbname, database.Port)
	newLogger := glogger.New(
		stdlog.New(os.Stdout, "\r\n", stdlog.LstdFlags), // io writer
		glogger.Config{
			SlowThreshold:             3 * time.Second, // Slow SQL threshold
			LogLevel:                  glogger.Error,   // Log level
			IgnoreRecordNotFoundError: true,            // Ignore ErrRecordNotFound error for logger
		},
	)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{Logger: newLogger})
	if err != nil {
		l.Fatalf("failed open database: %v", err)
	}

	// 启用数据连接池
	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	d, err := db.DB()
	if err != nil {
		l.Fatalf("failed set db: %v", err)
	}
	d.SetMaxIdleConns(50)

	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	d.SetMaxOpenConns(150)

	// SetConnMaxLifetime 设置了连接可复用的最大时间。
	d.SetConnMaxLifetime(time.Hour)

	return db
}
