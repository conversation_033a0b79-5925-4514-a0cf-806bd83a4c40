package api

import (
	"asdsec.com/asec/platform/app/console/app/alarm_rule/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
)

// GetChannelType godoc
// @Summary 外发通道类型
// @Schemes
// @Description 列表
// @Tags        AlarmRule
// @Produce     application/json
// @Success     200
// @Router      /v1/alert_rule/channel_type [GET]
// @success     200 {object} common.Response{} "application list"
func GetChannelType(ctx *gin.Context) {
	startTimeStr := ctx.Query("start_time")
	local, _ := time.LoadLocation("Local")
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, local)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	endTimeStr := ctx.Query("end_time")
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, local)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	if startTime.After(endTime) {
		global.SysLog.Error("param err,start time cat not more than end time")
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	respData, err := service.ChannelTypeService().GetChannelType(ctx, startTime, endTime)
	if err != nil {
		global.SysLog.Error("query GetChannelType error", zap.Error(err))
		common.Fail(ctx, common.QueryOutboundWayErr)
		return
	}
	common.OkWithData(ctx, respData)
}

// DataType godoc
// @Summary 数据类型
// @Schemes
// @Description 列表
// @Tags        AlarmRule
// @Produce     application/json
// @Success     200
// @Router      /v1/alert_rule/data_type [GET]
// @success     200 {object} common.Response{} "application list"
func DataType(ctx *gin.Context) {
	respData, err := service.GetDataTypeService().GetDataType(ctx)
	if err != nil {
		global.SysLog.Error("query DataType list error", zap.Error(err))
		common.Fail(ctx, common.QueryDataTypeErr)
		return
	}
	common.OkWithData(ctx, respData)
}

// DataTypeSummary godoc
// @Summary 数据类型统计
// @Schemes
// @Description 列表
// @Tags        AlarmRule
// @Produce     application/json
// @Success     200
// @Router      /v1/alert_rule/data_type_summary [GET]
// @success     200 {object} common.Response{} "application list"
func DataTypeSummary(ctx *gin.Context) {
	startTimeStr := ctx.Query("start_time")
	local, _ := time.LoadLocation("Local")
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, local)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	endTimeStr := ctx.Query("end_time")
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, local)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	if startTime.After(endTime) {
		global.SysLog.Error("param err,start time cat not more than end time")
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	respData, err := service.GetDataTypeService().GetDataTypeSummary(ctx, startTime, endTime)
	if err != nil {
		global.SysLog.Error("query DataType list error", zap.Error(err))
		common.Fail(ctx, common.QueryDataTypeErr)
		return
	}
	common.OkWithData(ctx, respData)
}

// GetChannelName godoc
// @Summary 外发通道名称映射
// @Schemes
// @Description 列表
// @Tags        AlarmRule
// @Produce     application/json
// @Success     200
// @Router      /v1/alert_rule/channel_name [GET]
// @success     200 {object} common.Response{} "application list"
func GetChannelName(ctx *gin.Context) {
	respData, err := service.ChannelTypeService().GetChannelName(ctx)
	if err != nil {
		global.SysLog.Error("query GetChannelType error", zap.Error(err))
		common.Fail(ctx, common.QueryOutboundWayErr)
		return
	}
	common.OkWithData(ctx, respData)
}
