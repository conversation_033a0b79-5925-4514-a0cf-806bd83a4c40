/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import t from"./index.63b468e5.js";import{_ as e,f as n,E as s,K as o,C as c,M as a,L as i,h as l,a as r,b as g,d as u,e as h,F as d,i as S,j as p,w as f,n as C,t as m,l as T}from"./index.a794166c.js";const w=""+new URL("success.72d17a7a.svg",import.meta.url).href,P={class:"access-main"},v={class:"content-wrapper"},A={class:"access-proxy-status-span"},y={class:"access-proxy-status-tips"},k={key:0,class:"loading-icon"},b={key:0,class:"access-common-status"},x={class:"access-common-status-span"};const I=e({name:"Access",components:{AppPage:t},data:()=>({userStore:n(),router:s(),connectionStatus:"disconnected",isHovering:!1,loadingInstance:null,statusPollingTimer:null,pollingTimeout:null,showAccessStatus:!1,deviceStatus:!1,autoReconnectEnabled:!0,reconnectAttempts:0,maxReconnectAttempts:2,reconnectTimer:null,agentStatusChangedHandler:null}),watch:{$route:{async handler(t,e){if(t.path&&t.path.includes("main")&&e&&e.path!==t.path){const t=this.userStore.tunState;if(logger.log("路由切换回主页面，检查状态缓存...",t),t&&0!==t)if(101===t||103===t){logger.log("检测到中间状态，验证真实状态并恢复轮询...");try{const e=await this.updateTunnelStatus({useCache:!1,clearPolling:!1,restorePolling:!1,showSuccessMessage:!1,logPrefix:"验证中间状态"});if(e&&void 0!==e.TunState){const n=e.TunState;logger.log("真实状态:",n,"缓存状态:",t),n===t&&(101===t?(logger.log("恢复连接状态轮询..."),this.startConnectPolling()):103===t&&(logger.log("恢复断开连接状态轮询..."),this.startDisconnectPolling()))}}catch(n){console.error("验证状态失败:",n),101===t?this.updateConnectionStatus(102):103===t&&this.updateConnectionStatus(100)}}else logger.log("使用有效的状态缓存，跳过刷新"),this.updateConnectionStatus(t);else logger.log("状态缓存无效，刷新连接状态..."),await this.updateTunnelStatus({useCache:!1,clearPolling:!1,restorePolling:!1,showSuccessMessage:!1,logPrefix:"路由切换状态刷新"})}},immediate:!1}},async mounted(){logger.log("获取用户信息"),this.userStore.GetUserInfo(),await this.loadInitialStatus(),await this.loadAccessStatus(),this.checkAndRestorePolling(),this.checkAutoConnect(),o.on("refreshTunnelStatus",this.handleTunnelStatusRefresh),this.setupAgentStatusListener()},beforeUnmount(){this.clearPolling(),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.reconnectAttempts=0,o.off("refreshTunnelStatus",this.handleTunnelStatusRefresh),this.cleanupAgentStatusListener()},methods:{async updateTunnelStatus(t={}){const{useCache:e=!0,clearPolling:n=!1,restorePolling:s=!1,showSuccessMessage:o=!1,logPrefix:i="更新隧道状态"}=t;try{let t;return logger.log(`${i}...`),e?(t={TunState:this.userStore.tunState},logger.log("缓存状态:",t.TunState),t.TunState&&0!==t.TunState?logger.log("使用缓存状态数据"):(logger.log("缓存无效，调用 API 获取最新状态..."),t=await c.getChannelStatus())):(logger.log("强制从API获取最新状态..."),t=await c.getChannelStatus()),logger.log("隧道状态响应:",t),t&&void 0!==t.TunState?(n&&this.clearPolling(),this.updateConnectionStatus(t.TunState),s&&(101===t.TunState?(logger.log("检测到连接中状态，重新启动连接轮询..."),this.startConnectPolling()):103===t.TunState&&(logger.log("检测到断开中状态，重新启动断开轮询..."),this.startDisconnectPolling())),logger.log("隧道状态已更新为:",t.TunState),o&&a.success("状态已刷新"),t):null}catch(l){throw console.error(`${i}失败:`,l),o&&a.error("刷新状态失败，请重试"),e&&this.updateConnectionStatus(102),l}},async loadInitialStatus(){try{const t=await this.updateTunnelStatus({useCache:!0,clearPolling:!1,restorePolling:!1,showSuccessMessage:!1,logPrefix:"加载通道初始状态"});t&&t.Token&&logger.log("获取到登录token信息")}catch(t){}},async loadAccessStatus(){try{logger.log("加载接入状态...");const t=await c.getAccessStatus();logger.log("接入状态响应:",t),t&&(this.showAccessStatus=1===t.IsShowAccess,this.deviceStatus=1===t.DeviceStatus,logger.log("接入状态设置:",{showAccessStatus:this.showAccessStatus,deviceStatus:this.deviceStatus}))}catch(t){console.error("获取接入状态失败:",t),this.showAccessStatus=!1,this.deviceStatus=!1}},updateConnectionStatus(t){switch(logger.log("更新连接状态，TunState:",t,"当前状态:",this.connectionStatus),this.userStore.setTunState(t),t){case 100:this.connectionStatus="connected",this.reconnectAttempts=0,this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null);break;case 101:this.connectionStatus="connecting";break;case 102:this.connectionStatus="disconnected";break;case 103:this.connectionStatus="disconnecting";break;default:console.warn("未知的隧道状态:",t),this.connectionStatus="disconnected",userStore.setTunState(102)}},handleAutoReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return logger.log("已达到最大重连次数，停止自动重连"),void(this.reconnectAttempts=0);this.reconnectAttempts++,logger.log(`开始第${this.reconnectAttempts}次自动重连...`);const t=Math.min(2e3*this.reconnectAttempts,1e4);this.reconnectTimer=setTimeout((async()=>{try{logger.log(`执行第${this.reconnectAttempts}次自动重连`),await this.connect()}catch(t){console.error(`第${this.reconnectAttempts}次自动重连失败:`,t),this.reconnectAttempts>=this.maxReconnectAttempts&&(this.reconnectAttempts=0)}}),t)},checkAndRestorePolling(){const t=this.connectionStatus;logger.log("检查当前状态是否需要恢复轮询:",t),"connecting"===t?(logger.log("检测到连接中状态，恢复连接轮询..."),this.startConnectPolling()):"disconnecting"===t&&(logger.log("检测到断开中状态，恢复断开轮询..."),this.startDisconnectPolling())},startLoading(t){this.loadingInstance||(this.loadingInstance=i.service({fullscreen:!0,text:t}))},stopLoading(){this.loadingInstance&&(this.loadingInstance.close(),this.loadingInstance=null)},async handleViewDetails(){try{logger.log("打开接入详情..."),await c.openAccessDetail(),logger.log("接入详情打开成功")}catch(t){console.error("打开接入详情失败:",t),a.error("打开详情失败，请重试")}},clearPolling(){let t=!1;this.statusPollingTimer&&(clearInterval(this.statusPollingTimer),this.statusPollingTimer=null,t=!0),this.pollingTimeout&&(clearTimeout(this.pollingTimeout),this.pollingTimeout=null,t=!0),t&&logger.log("轮询定时器已清理")},getButtonText(){return"connecting"===this.connectionStatus?"正在连接":"disconnecting"===this.connectionStatus?"正在断开":"connected"===this.connectionStatus?this.isHovering?"断开连接":"连接成功":"一键连接"},getButtonClass(){return{"btn-disconnected":"disconnected"===this.connectionStatus,"btn-connecting":"connecting"===this.connectionStatus,"btn-disconnecting":"disconnecting"===this.connectionStatus,"btn-connected":"connected"===this.connectionStatus&&!this.isHovering,"btn-disconnect":"connected"===this.connectionStatus&&this.isHovering}},getButtonStyle(){return"disconnected"===this.connectionStatus?{backgroundColor:"#626aef",borderColor:"#626aef",color:"#ffffff"}:"connecting"===this.connectionStatus?{backgroundColor:"#a5b4fc",borderColor:"#a5b4fc",color:"#ffffff"}:"disconnecting"===this.connectionStatus?{backgroundColor:"#fca5a5",borderColor:"#fca5a5",color:"#ffffff"}:"connected"===this.connectionStatus?this.isHovering?{backgroundColor:"#ef4444",borderColor:"#ef4444",color:"#ffffff"}:{backgroundColor:"#29cc65",borderColor:"#29cc65",color:"#ffffff"}:{}},async handleConnect(){"connected"===this.connectionStatus?await this.disconnect():"disconnected"===this.connectionStatus&&await this.connect()},async connect(){try{logger.log("开始连接隧道..."),await c.connectTunnel(),logger.log("连接API调用成功，开始轮询状态..."),this.updateConnectionStatus(101),this.startConnectPolling()}catch(t){console.error("连接失败:",t),this.updateConnectionStatus(102),a.error("连接失败，会话已超时"),logger.log("连接失败，会话已超时，执行退出登录");try{await this.userStore.ClearStorage(),this.userStore.LoginOut(),logger.log("连接失败，服务器注销登录成功")}catch(e){console.error("连接失败，服务器注销登录失败:",e)}this.router.push({name:"ClientNewLogin",query:c.getClientParams()})}},startConnectPolling(){this.clearPolling();let t=0;this.statusPollingTimer=setInterval((async()=>{try{t++,logger.log(`连接状态轮询第${t}次...`);const e=await c.getChannelStatus();if(logger.log("轮询状态响应:",e),e&&100===e.TunState)return logger.log("连接成功！"),this.clearPolling(),void this.updateConnectionStatus(100);t>=30&&(logger.log("连接超时，恢复到未连接状态"),this.clearPolling(),this.updateConnectionStatus(102))}catch(e){console.error("轮询状态失败:",e),t++,t>=30&&(this.clearPolling(),this.updateConnectionStatus(102),a.error("连接失败，请重试"))}}),1e3)},async disconnect(){try{logger.log("开始断开隧道连接..."),await c.disconnectTunnel(),logger.log("断开连接API调用成功，开始轮询状态..."),this.updateConnectionStatus(103),this.startDisconnectPolling()}catch(t){console.error("断开连接失败:",t),a.error("断开连接失败")}},startDisconnectPolling(){this.clearPolling();let t=0;this.statusPollingTimer=setInterval((async()=>{try{t++,logger.log(`断开连接状态轮询第${t}次...`);const e=await c.getChannelStatus();if(logger.log("轮询状态响应:",e),e&&102===e.TunState)return logger.log("断开连接成功！"),this.clearPolling(),void this.updateConnectionStatus(102);t>=10&&(logger.log("断开连接超时，恢复到已连接状态"),this.clearPolling(),this.updateConnectionStatus(100),a.error("断开连接超时"))}catch(e){console.error("轮询状态失败:",e),t++,t>=10&&(this.clearPolling(),this.updateConnectionStatus(100),a.error("断开连接失败"))}}),1e3)},handleMouseEnter(){"connected"===this.connectionStatus&&(this.isHovering=!0)},handleMouseLeave(){this.isHovering=!1},checkAutoConnect(){const t=sessionStorage.getItem("autoConnectChecked");"true"!==t?setTimeout((async()=>{try{if(logger.log("检查自动连接条件：",t),"disconnected"!==this.connectionStatus)return logger.log("当前连接状态不是未连接，跳过自动连接检查"),void sessionStorage.setItem("autoConnectChecked","true");if("login"===t)return logger.log("登录后进行自动连接"),await this.connect(),void sessionStorage.setItem("autoConnectChecked","true");const e=await c.getClientConfig();logger.log("客户端配置:",e);let n=!0;e&&void 0!==e.AutoConnectAfterStartup&&(n=e.AutoConnectAfterStartup),logger.log("启动后自动连接配置:",n),!0===n?(logger.log("启动后自动连接开关已开启，开始自动连接..."),await this.connect()):logger.log("启动后自动连接开关未开启，跳过自动连接"),sessionStorage.setItem("autoConnectChecked","true")}catch(e){console.error("检查自动连接失败:",e),sessionStorage.setItem("autoConnectChecked","true")}}),100):logger.log("本次会话已执行过自动连接检查，跳过")},async handleTunnelStatusRefresh(t){if(logger.log("收到隧道状态刷新请求:",t),t.data&&void 0!==t.data.TunState){const e=t.data.TunState,n=this.connectionStatus;logger.log("客户端主动通知隧道状态变化:",{newTunState:e,currentStatus:n,source:"客户端主动通知"});if({disconnected:102,connecting:101,connected:100,disconnecting:103}[n]===e)return void logger.log("状态未变化，跳过更新");102===e?"disconnecting"===n?(logger.log("手动断开完成，清理轮询，不触发自动重连"),this.clearPolling(),this.updateConnectionStatus(e)):(logger.log("检测到异常断开，更新状态并触发自动重连"),this.updateConnectionStatus(e),this.autoReconnectEnabled&&this.handleAutoReconnect()):this.updateConnectionStatus(e)}else logger.log("手动刷新隧道状态"),await this.updateTunnelStatus({useCache:!1,clearPolling:!0,restorePolling:!0,showSuccessMessage:!1,logPrefix:"手动刷新隧道状态"})},setupAgentStatusListener(){try{this.agentStatusChangedHandler=t=>{var e;logger.log("收到准入状态变更事件 agentStatusChanged:",t.detail);const n=null==(e=t.detail)?void 0:e.deviceStatus;void 0!==n?(logger.log("从事件中提取到设备状态:",n),this.deviceStatus=1===n,logger.log("设备状态已更新:",{"原始值":n,"转换后":this.deviceStatus,"显示文本":this.deviceStatus?"已入网":"未入网"})):logger.warn("无法从事件数据中提取设备状态:",t.detail)},window.addEventListener("agentStatusChanged",this.agentStatusChangedHandler),logger.log("准入状态变更事件监听已设置")}catch(t){console.error("设置准入状态变更事件监听失败:",t)}},cleanupAgentStatusListener(){try{this.agentStatusChangedHandler&&(window.removeEventListener("agentStatusChanged",this.agentStatusChangedHandler),this.agentStatusChangedHandler=null,logger.log("准入状态变更事件监听已清理"))}catch(t){console.error("清理准入状态变更事件监听失败:",t)}}}},[["render",function(t,e,n,s,o,c){const a=l("base-button"),i=l("AppPage");return r(),g("div",P,[u("ul",v,[u("li",{class:h(["access-proxy-status",o.connectionStatus])},[e[4]||(e[4]=u("span",{class:"access-proxy-status-text"}," 连接状态 ",-1)),u("span",A,[u("span",y,["connected"===o.connectionStatus?(r(),g(d,{key:0},[e[1]||(e[1]=u("img",{src:w,alt:"成功",class:"success-icon"},null,-1)),e[2]||(e[2]=S(" 已建立安全连接 "))],64)):(r(),g(d,{key:1},[S(" 点击连接，即可安全便捷地访问应用 ")],64))]),p(a,{class:h(["access-proxy-status-btn",c.getButtonClass()]),style:C(c.getButtonStyle()),onClick:c.handleConnect,onMouseenter:c.handleMouseEnter,onMouseleave:c.handleMouseLeave},{default:f((()=>["connecting"===o.connectionStatus||"disconnecting"===o.connectionStatus?(r(),g("span",k,e[3]||(e[3]=[u("svg",{class:"spinner",viewBox:"0 0 24 24"},[u("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2",fill:"none","stroke-dasharray":"31.416","stroke-dashoffset":"31.416"},[u("animate",{attributeName:"stroke-dasharray",dur:"2s",values:"0 31.416;15.708 15.708;0 31.416",repeatCount:"indefinite"}),u("animate",{attributeName:"stroke-dashoffset",dur:"2s",values:"0;-15.708;-31.416",repeatCount:"indefinite"})])],-1)]))):T("",!0),S(" "+m(c.getButtonText()),1)])),_:1},8,["class","style","onClick","onMouseenter","onMouseleave"])])],2),o.showAccessStatus?(r(),g("li",b,[u("span",x,[e[5]||(e[5]=u("span",null,"准入状态（企业网络下使用）：",-1)),u("span",{style:C({color:o.deviceStatus?"#29cc65":"#ef4444",marginLeft:"8px"})},m(o.deviceStatus?"已入网":"未入网"),5)]),u("span",{class:"access-common-status-detail",onClick:e[0]||(e[0]=(...t)=>c.handleViewDetails&&c.handleViewDetails(...t))},e[6]||(e[6]=[u("span",null,"查看详情",-1)]))])):T("",!0),u("li",{class:h(["access-app",{"access-app-no-access":!o.showAccessStatus}])},[p(i,{class:"access-app-page","is-connected":"connected"===o.connectionStatus},null,8,["is-connected"])],2)])])}],["__scopeId","data-v-0099c131"]]);export{I as default};
