package model

import "time"

type Admin struct {
	Id               string    `gorm:"column:id" json:"id"`
	Name             string    `gorm:"column:name" json:"name"`
	CorpId           string    `gorm:"column:corp_id" json:"corp_id"`
	Phone            string    `gorm:"phone" json:"phone"`
	Email            string    `gorm:"email" json:"email"`
	Status           int       `gorm:"status" json:"status"`
	RoleId           string    `gorm:"role_id" json:"role_id"`
	Desc             string    `gorm:"desc" json:"desc"`
	LoginFailedTimes int       `gorm:"login_failed_times" json:"login_failed_times"`
	ExpireType       int       `gorm:"expire_type" json:"expire_type"`
	ExpireTime       time.Time `gorm:"expire_time" json:"expire_time"`
	LockTime         time.Time `gorm:"lock_time" json:"lock_time"`
	Permissions      string    `gorm:"permissions" json:"permissions"`
	CreateAt         time.Time `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"-"`
	UpdateAt         time.Time `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"-"`
}

func (Admin) TableName() string {
	return "tb_admin_entity"
}

type AdminCorp struct {
	Id       string    `gorm:"column:id" json:"id"`
	Name     string    `gorm:"column:name" json:"name"`
	CreateAt time.Time `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"-"`
	UpdateAt time.Time `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"-"`
}

func (AdminCorp) TableName() string {
	return "tb_admin_corp"
}

type AdminComponent struct {
	Id         string `gorm:"column:id" json:"id"`
	Name       string `gorm:"column:name" json:"name"`
	CorpId     string `gorm:"column:corp_id" json:"corp_id"`
	ProviderId string `gorm:"column:provider_id" json:"provider_id"`
}

func (AdminComponent) TableName() string {
	return "tb_admin_component"
}

type AdminComponentConfig struct {
	Id          string `gorm:"column:id" json:"id"`
	Name        string `gorm:"column:name" json:"name"`
	ComponentId string `gorm:"column:component_id" json:"component_id"`
	Value       string `gorm:"column:value" json:"value"`
}

func (AdminComponentConfig) TableName() string {
	return "tb_admin_component_config"
}

type AdminCredential struct {
	Id             string    `gorm:"column:id" json:"id"`
	UserId         string    `gorm:"column:user_id" json:"user_id"`
	Type           string    `gorm:"column:type" json:"type"`
	SecretData     string    `gorm:"column:secret_data" json:"secret_data"`
	CredentialData string    `gorm:"column:credential_data" json:"credential_data"`
	CorpId         string    `gorm:"column:corp_id" json:"corp_id"`
	CreateAt       time.Time `gorm:"column:created_at;type:timestamptz;comment:创建时间" json:"-"`
	UpdateAt       time.Time `gorm:"column:updated_at;type:timestamptz;comment:更新时间" json:"-"`
}

func (AdminCredential) TableName() string {
	return "tb_admin_credential"
}

type CredData struct {
	UserSalt string
	Iter     int
	KeyLen   int
}

type Token struct {
	AccessToken     string `json:"accessToken"`
	ExpireIn        int64  `json:"expireIn"`
	RefreshToken    string `json:"refreshToken"`
	RefreshExpireIn int64  `json:"refreshExpireIn"`
	TokenType       string `json:"tokenType"`
	LicenseStatus   int    `json:"license_status"`
	AgentLimit      uint64 `json:"agent_limit"`
	UsedCount       uint64 `json:"used_count"`
}
