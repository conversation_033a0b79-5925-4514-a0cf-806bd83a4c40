package events

import (
	v1 "asdsec.com/asec/platform/api/events/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/network"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"asdsec.com/asec/platform/pkg/utils"
	"context"
	"database/sql"
	"google.golang.org/grpc"
	"sync"
)

func SendDlps(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:              ctx,
		Wg:               wg,
		DoStreamSendFunc: doSendDlp,
		RunType:          common.StreamSend,
		GetConnFun:       connection.GetLogCenterConnection,
		WaitSecond:       common.Duration,
		RandomOffset:     1,
	}
	common.Send(param)
}

const dlpQuerySql = `SELECT id,uuid,file_name,
	file_type,file_path,original_file_name,original_file_path,file_size,owner,file_create_time,
	last_change_time,extension_name,file_category_id,real_extension_name,name_match_info,
	content_match_info,md5,sha256,activity,occur_time,channel,channel_type,software_path,
	dst_path,compress_encrypt,trace_id,sub_src_trace_id,src_path,sensitive_rule_id,sensitive_rule_name,sensitive_level,
	data_category,source_id,source_name,source_type,sensitive_info,file_hide_suffix,file_event_id,
	policy_id,policy_name,alert_type,engine_name,enable_analysis,dispose_action
	FROM tb_ddr_alert where id > ? limit ?;`

func doSendDlp(conn *grpc.ClientConn, ctx context.Context, wg *sync.WaitGroup) error {
	if !common.DdrSwitch {
		return nil
	}
	// 获取当前offset
	currentOffset, err := common.GetOffset(common.DlpAlertOffset)
	if err != nil {
		global.Logger.Sugar().Errorf("doSendDlp getOffset err:%v", err)
		return err
	}
	// 查询需要发送的记录
	rows, err := global.SqliteClient.Query(dlpQuerySql, currentOffset, common.Limit)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorf("doSendDlp query err:%v", err)
		return err
	}

	var dlpAlertSend v1.DlpAlertReq
	//记录本次上报的offset
	var nextOffset = currentOffset
	userInfo := user.GetUserInfo()

	for rows.Next() {
		// 扫描转换为发送请求体
		dlpAlert, curId, scanErr := scanDlp(rows, userInfo)
		if scanErr != nil {
			global.Logger.Sugar().Errorf("scanDlp err:%v", scanErr)
			return scanErr
		}

		dlpAlertSend.Alert = append(dlpAlertSend.Alert, dlpAlert)
		if curId > nextOffset {
			nextOffset = curId
		}
	}
	if len(dlpAlertSend.Alert) > 0 {
		// 发送
		err = sendDlpAlert(&dlpAlertSend, conn)
		if err != nil {
			global.Logger.Sugar().Errorf("sendDlpAlert err:%v", err)
			return err
		}

		// 更新offset
		err = common.UpdateOffset(nextOffset, common.DlpAlertOffset)
		if err != nil {
			global.Logger.Sugar().Errorf("updateOffset err:%v", err)
			return err
		}
	} else {
		// 没有查询结果时兜底offset. 防止客户端删了表不报事件了
		var res int64
		row := global.SqliteClient.QueryRow("SELECT id from tb_ddr_alert ORDER BY id desc limit 1")
		_ = row.Scan(&res)
		if currentOffset > res {
			err := common.UpdateOffset(res, common.DlpAlertOffset)
			if err != nil {
				global.Logger.Sugar().Errorf("event update offset err:%v", err)
			}
			return err
		}
	}
	return nil
}

func sendDlpAlert(dlpAlertReq *v1.DlpAlertReq, conn *grpc.ClientConn) error {
	stream, err := v1.NewDlpAlertReportClient(conn).CreateDlpLog(context.TODO())
	if stream != nil {
		defer stream.CloseAndRecv()
	}
	if err != nil {
		return err
	}
	err = stream.Send(dlpAlertReq)
	if err != nil {
		return err
	}
	return nil
}

func scanDlp(rows *sql.Rows, userInfo user.UserInfo) (*v1.DlpAlert, int64, error) {
	alert := v1.DlpAlert{}
	//当前上传事件offset
	var curId int64
	var subSrcTraceIdText string
	var traceIdText string
	err := rows.Scan(&curId, &alert.Uuid, &alert.FileName, &alert.FileType, &alert.FilePath,
		&alert.OriginalFileName, &alert.OriginalFilePath, &alert.FileSize, &alert.Owner, &alert.FileCreateTime,
		&alert.LastChangeTime, &alert.ExtensionName, &alert.FileCategoryId, &alert.RealExtensionName, &alert.NameMatchInfo,
		&alert.ContentMatchInfo, &alert.Md5, &alert.Sha256, &alert.Activity, &alert.OccurTime, &alert.Channel, &alert.ChannelType, &alert.SoftwarePath,
		&alert.DstPath, &alert.CompressEncrypt, &traceIdText, &subSrcTraceIdText, &alert.SrcPath, &alert.SensitiveRuleId, &alert.SensitiveRuleName, &alert.SensitiveLevel,
		&alert.DataCategory, &alert.SourceId, &alert.SourceName, &alert.SourceType, &alert.SensitiveInfo, &alert.FileHideSuffix, &alert.FileEventId,
		&alert.PolicyId, &alert.PolicyName, &alert.AlertType, &alert.EngineName, &alert.EnableAnalysis, &alert.DisposeAction,
	)
	if err != nil {
		return nil, 0, err
	}

	alert.EventSubType = DDREventType
	alert.AgentId = global.ApplianceID
	alert.UserId = userInfo.UserId
	alert.UserName = userInfo.UserName
	ips, macs := network.GetIpsAndMac()
	alert.AgentIp = ips
	alert.AgentMac = macs
	alert.AgentName = global.ApplianceName
	alert.PlatType = global.PlatType
	alert.EventSource = getEventSource()
	alert.EventType = DDREventType
	alert.TraceId = transJsonArrayStr(traceIdText)
	alert.SubSrcTraceId = transJsonArrayStr(subSrcTraceIdText)
	alert.PublicIp = utils.GetPublicIp().PublicIp
	return &alert, curId, nil
}
