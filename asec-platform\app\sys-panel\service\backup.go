package service

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"path"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/sys-panel/model"
	"gorm.io/gorm"
)

type BackupService struct {
	db *gorm.DB
}

// PostgreSQL 配置
const (
	PG_CONTAINER = "postgres"             // PostgreSQL 容器名
	PG_USER      = "asec"                 // 数据库用户名
	PG_DATABASE  = "asec_platform"        // 数据库名
	BACKUP_DIR   = "/opt/platform/backup" // 备份文件存储目录
)

// 默认备份配置
var defaultBackupConfig = &model.BackupConfig{
	ID:         1,
	Enabled:    true,     // 默认启用
	Interval:   "weekly", // 每周备份
	MaxBackups: 7,        // 保留7个备份
	Hour:       23,       // 23点
	DayOfWeek:  5,        // 周五 (0=周日, 5=周五)
	DayOfMonth: 1,        // 每月备份时默认1号
}

func NewBackupService(db *gorm.DB) *BackupService {
	// 确保备份目录存在
	if err := os.MkdirAll(BACKUP_DIR, os.ModePerm); err != nil {
		panic(fmt.Sprintf("create backup directory failed: %v", err))
	}

	// 初始化备份配置
	var config model.BackupConfig
	if err := db.First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			db.Create(defaultBackupConfig)
		}
	}
	fmt.Printf("start backup service with config: %v", config)
	service := &BackupService{db: db}

	// 启动定期备份任务
	go service.startScheduledBackup()

	return service
}

func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(&model.Backup{}, &model.BackupConfig{}, &model.WechatVerify{})
}

// 启动定期备份任务
func (s *BackupService) startScheduledBackup() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		var config model.BackupConfig
		if err := s.db.First(&config).Error; err != nil {
			continue
		}

		if !config.Enabled {
			continue
		}

		now := time.Now()
		if now.After(config.NextBackupTime) {
			// 执行自动备份
			s.CreateBackup("auto")

			// 更新最后备份时间和下次备份时间
			config.LastBackupTime = now
			config.NextBackupTime = s.calculateNextBackupTime(&config)
			s.db.Model(&config).Updates(map[string]interface{}{
				"last_backup_time": config.LastBackupTime,
				"next_backup_time": config.NextBackupTime,
			})

			// 立即清理过期备份
			s.CleanupOldBackups(config.MaxBackups)
		}
	}
}

// 计算下一次备份时间
func (s *BackupService) calculateNextBackupTime(config *model.BackupConfig) time.Time {
	now := time.Now()

	switch config.Interval {
	case "daily":
		// 找到今天的指定小时，如果当前时间已经过了指定小时，则是明天的指定小时
		targetHour := config.Hour
		if targetHour < 0 || targetHour > 23 {
			targetHour = 0 // 默认凌晨0点
		}

		targetTime := time.Date(now.Year(), now.Month(), now.Day(), targetHour, 0, 0, 0, now.Location())
		if now.After(targetTime) {
			targetTime = targetTime.AddDate(0, 0, 1)
		}
		return targetTime

	case "weekly":
		// 找到本周的指定星期几和小时
		targetDay := config.DayOfWeek
		if targetDay < 0 || targetDay > 6 {
			targetDay = 5 // 默认周五
		}

		targetHour := config.Hour
		if targetHour < 0 || targetHour > 23 {
			targetHour = 23 // 默认23点
		}

		// 计算当前距离目标星期几有多少天
		daysUntilTarget := (targetDay - int(now.Weekday()) + 7) % 7
		if daysUntilTarget == 0 {
			// 如果今天就是目标日，检查时间是否已过
			targetTime := time.Date(now.Year(), now.Month(), now.Day(), targetHour, 0, 0, 0, now.Location())
			if now.After(targetTime) {
				daysUntilTarget = 7 // 等待下一周期
			}
		}

		return time.Date(now.Year(), now.Month(), now.Day(), targetHour, 0, 0, 0, now.Location()).AddDate(0, 0, daysUntilTarget)

	case "monthly":
		// 找到本月或下月的指定日期和小时
		targetDay := config.DayOfMonth
		if targetDay < 1 || targetDay > 31 {
			targetDay = 1 // 默认每月1号
		}

		targetHour := config.Hour
		if targetHour < 0 || targetHour > 23 {
			targetHour = 23 // 默认23点
		}

		// 计算目标时间
		targetTime := time.Date(now.Year(), now.Month(), targetDay, targetHour, 0, 0, 0, now.Location())

		// 如果目标日期已过，或者目标日期不存在于当月（如2月30日），则取下个月
		if now.After(targetTime) || targetTime.Month() != now.Month() {
			// 移到下个月的1号
			nextMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location()).AddDate(0, 1, 0)
			// 然后设置为下个月的目标日期
			targetTime = time.Date(nextMonth.Year(), nextMonth.Month(), targetDay, targetHour, 0, 0, 0, now.Location())

			// 处理超出月份天数的情况（如4月31日不存在，将变为5月1日）
			if targetTime.Month() != nextMonth.Month() {
				// 回退到月末
				targetTime = time.Date(nextMonth.Year(), nextMonth.Month()+1, 0, targetHour, 0, 0, 0, now.Location())
			}
		}

		return targetTime

	default:
		// 默认24小时后
		return now.AddDate(0, 0, 1)
	}
}

// 清理过期备份
func (s *BackupService) CleanupOldBackups(maxBackups int) error {
	if maxBackups <= 0 {
		return nil
	}

	var backups []model.Backup
	// 只查询已完成的备份
	if err := s.db.Where("status = ?", "completed").
		Order("create_time desc").
		Find(&backups).Error; err != nil {
		return err
	}

	if len(backups) <= maxBackups {
		return nil
	}

	// 删除超出数量的旧备份
	for i := maxBackups; i < len(backups); i++ {
		backup := backups[i]
		// 删除文件
		filePath := path.Join(BACKUP_DIR, backup.FileName)
		if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
			// 记录错误但继续执行
			fmt.Printf("failed to delete backup file %s: %v\n", filePath, err)
		}
		// 删除数据库记录
		if err := s.db.Delete(&backup).Error; err != nil {
			fmt.Printf("failed to delete backup record %d: %v\n", backup.ID, err)
		}
	}

	return nil
}

// 获取备份配置
func (s *BackupService) GetBackupConfig() (*model.BackupConfig, error) {
	var config model.BackupConfig
	err := s.db.First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return defaultBackupConfig, nil
		}
		return nil, err
	}
	return &config, nil
}

// 更新备份配置
func (s *BackupService) UpdateBackupConfig(config *model.BackupConfig) error {
	var existingConfig model.BackupConfig
	err := s.db.First(&existingConfig).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			config.ID = 1
			// 计算下一次备份时间
			config.NextBackupTime = s.calculateNextBackupTime(config)
			return s.db.Create(config).Error
		}
		return err
	}

	// 计算下一次备份时间
	config.NextBackupTime = s.calculateNextBackupTime(config)

	return s.db.Model(&existingConfig).Updates(map[string]interface{}{
		"enabled":          config.Enabled,
		"interval":         config.Interval,
		"max_backups":      config.MaxBackups,
		"hour":             config.Hour,
		"day_of_week":      config.DayOfWeek,
		"day_of_month":     config.DayOfMonth,
		"next_backup_time": config.NextBackupTime,
	}).Error
}

func (s *BackupService) GetBackupList(page, pageSize int) (*model.BackupList, error) {
	var total int64
	var backups []model.Backup

	if err := s.db.Model(&model.Backup{}).Count(&total).Error; err != nil {
		return nil, err
	}

	if err := s.db.Order("create_time desc").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&backups).Error; err != nil {
		return nil, err
	}

	return &model.BackupList{
		Total: total,
		List:  backups,
	}, nil
}

func (s *BackupService) CreateBackup(backupType string) error {
	// 生成备份文件名
	fileName := fmt.Sprintf("backup_%s.gz", time.Now().Format("20060102_150405"))
	filePath := path.Join(BACKUP_DIR, fileName)

	// 创建备份记录
	backup := &model.Backup{
		FileName:   fileName,
		Status:     "processing",
		Type:       backupType,
		CreateTime: time.Now(),
	}
	if err := s.db.Create(backup).Error; err != nil {
		return fmt.Errorf("create backup record failed: %v", err)
	}

	// 异步执行备份
	go func() {
		err := s.executeBackup(filePath)
		status := "completed"
		if err != nil {
			status = "failed"
		}

		// 更新备份状态和文件大小
		fileInfo, _ := os.Stat(filePath)
		var fileSize int64
		if fileInfo != nil {
			fileSize = fileInfo.Size()
		}

		s.db.Model(backup).Updates(map[string]interface{}{
			"status":    status,
			"file_size": fileSize,
		})
	}()

	return nil
}

func (s *BackupService) executeBackup(filePath string) error {
	// 创建输出文件
	outfile, err := os.OpenFile(filePath, os.O_RDWR|os.O_CREATE, 0644)
	if err != nil {
		return fmt.Errorf("create backup file failed: %v", err)
	}
	defer outfile.Close()

	// 执行 pg_dump 命令
	dumpCmd := exec.Command("docker", "exec", PG_CONTAINER,
		"pg_dump", "-F", "c", "-U", PG_USER, "-d", PG_DATABASE)
	var stderr bytes.Buffer
	dumpCmd.Stderr = &stderr

	// 创建 gzip 压缩管道
	gzipCmd := exec.Command("gzip", "-cf")
	gzipCmd.Stdin, _ = dumpCmd.StdoutPipe()
	gzipCmd.Stdout = outfile

	// 启动 gzip
	if err := gzipCmd.Start(); err != nil {
		return fmt.Errorf("start gzip failed: %v", err)
	}

	// 执行 pg_dump
	if err := dumpCmd.Run(); err != nil {
		return fmt.Errorf("execute pg_dump failed: %v, stderr: %s", err, stderr.String())
	}

	// 等待 gzip 完成
	if err := gzipCmd.Wait(); err != nil {
		return fmt.Errorf("gzip compression failed: %v", err)
	}

	return nil
}

func (s *BackupService) RestoreBackup(id uint) error {
	// 查找备份记录
	var backup model.Backup
	if err := s.db.First(&backup, id).Error; err != nil {
		return fmt.Errorf("backup not found: %v", err)
	}

	// 检查备份文件是否存在
	filePath := path.Join(BACKUP_DIR, backup.FileName)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("backup file not found: %s", filePath)
	}

	// 更新状态为处理中
	if err := s.db.Model(&backup).Update("status", "processing").Error; err != nil {
		return fmt.Errorf("update backup status failed: %v", err)
	}

	// 异步执行恢复
	go func() {
		err := s.executeRestore(filePath)
		status := "completed"
		if err != nil {
			status = "failed"
		}
		s.db.Model(&backup).Update("status", status)
	}()

	return nil
}

func (s *BackupService) executeRestore(filePath string) error {
	// 打开备份文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("open backup file failed: %v", err)
	}
	defer file.Close()

	// 创建 pg_restore 命令
	cmd := exec.Command("docker", "exec", "-i", PG_CONTAINER,
		"pg_restore", "-F", "c", "-c", "-U", PG_USER, "-d", PG_DATABASE)

	// 如果是 gzip 文件，需要先解压
	if strings.HasSuffix(filePath, ".gz") {
		gzipCmd := exec.Command("gunzip", "-c", filePath)
		var stderr bytes.Buffer
		gzipCmd.Stderr = &stderr

		// 连接 gunzip 的输出到 pg_restore 的输入
		pipe, err := cmd.StdinPipe()
		if err != nil {
			return fmt.Errorf("create pipe failed: %v", err)
		}
		gzipCmd.Stdout = pipe

		// 启动 pg_restore
		if err := cmd.Start(); err != nil {
			return fmt.Errorf("start pg_restore failed: %v", err)
		}

		// 执行 gunzip
		if err := gzipCmd.Run(); err != nil {
			return fmt.Errorf("gunzip failed: %v, stderr: %s", err, stderr.String())
		}

		// 关闭管道并等待 pg_restore 完成
		pipe.Close()
		if err := cmd.Wait(); err != nil {
			return fmt.Errorf("pg_restore failed: %v", err)
		}
	} else {
		cmd.Stdin = file
		output, err := cmd.CombinedOutput()
		if err != nil || strings.HasPrefix(string(output), "ERROR ") {
			return fmt.Errorf("pg_restore failed: %s", string(output))
		}
	}

	return nil
}

func (s *BackupService) DeleteBackup(id uint) error {
	var backup model.Backup
	if err := s.db.First(&backup, id).Error; err != nil {
		return fmt.Errorf("backup not found: %v", err)
	}

	// 删除备份文件
	filePath := path.Join(BACKUP_DIR, backup.FileName)
	if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("delete backup file failed: %v", err)
	}

	// 删除数据库记录
	return s.db.Delete(&backup).Error
}

// DB 返回数据库实例，供处理程序使用
func (s *BackupService) DB() *gorm.DB {
	return s.db
}
