package qiyewx

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	pb "asdsec.com/asec/platform/api/auth/v1"
)

// 错误码 https://developer.work.weixin.qq.com/document/path/90313
const (
	errorCodeSuccess      = 0
	errorCodeNotTrustedIp = 60020 // 可信ip未配置
	errorCodeCorpIdWrong  = 40013 // corp id 不正确
	errorCodeSecretWrong  = 40091 // secret 不正确
	errorAgentIdWrong     = 40056 // secret 不正确
)

// https://developer.work.weixin.qq.com/document/path/91023
// https://developer.work.weixin.qq.com/document/path/90196

type WxIDProvider struct {
	client *http.Client

	providerId   string
	wxCorpId     string
	secret       string
	cacheHandler CacheHandler
}

type CacheHandler interface {
	GetWxWebAccessTokenFromCache(ctx context.Context, providerId string) (string, error)
	SetWxWebAccessTokenToCache(ctx context.Context, providerId, accessToken string, ttl time.Duration) error
}

func NewWxIDProvider(providerId, wxCorpId, secret string, handler CacheHandler) WxIDProvider {
	return WxIDProvider{
		client: &http.Client{Timeout: time.Second * 30},

		providerId:   providerId,
		wxCorpId:     wxCorpId,
		secret:       secret,
		cacheHandler: handler,
	}
}

type GetAccessTokenResp struct {
	Errcode     int    `json:"errcode"`
	Errmsg      string `json:"errmsg"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

type GetUserIDResp struct {
	Errcode        int    `json:"errcode"`
	Errmsg         string `json:"errmsg"`
	Userid         string `json:"userid"`
	UserTicket     string `json:"user_ticket"`
	Openid         string `json:"openid"`
	ExternalUserid string `json:"external_userid"`
}

type GetUserInfo struct {
	Errcode        int      `json:"errcode"`
	Errmsg         string   `json:"errmsg"`
	Userid         string   `json:"userid"`
	Name           string   `json:"name"`
	Department     []int    `json:"department"`
	Order          []int    `json:"order"`
	Position       string   `json:"position"`
	Mobile         string   `json:"mobile"`
	Gender         string   `json:"gender"`
	Email          string   `json:"email"`
	BizMail        string   `json:"biz_mail"`
	IsLeaderInDept []int    `json:"is_leader_in_dept"`
	DirectLeader   []string `json:"direct_leader"`
	Avatar         string   `json:"avatar"`
	ThumbAvatar    string   `json:"thumb_avatar"`
	Telephone      string   `json:"telephone"`
	Alias          string   `json:"alias"`
	Address        string   `json:"address"`
	OpenUserid     string   `json:"open_userid"`
	MainDepartment int      `json:"main_department"`
	Extattr        struct {
		Attrs []struct {
			Type int    `json:"type"`
			Name string `json:"name"`
			Text struct {
				Value string `json:"value"`
			} `json:"text,omitempty"`
			Web struct {
				Url   string `json:"url"`
				Title string `json:"title"`
			} `json:"web,omitempty"`
		} `json:"attrs"`
	} `json:"extattr"`
	Status           int    `json:"status"`
	QrCode           string `json:"qr_code"`
	ExternalPosition string `json:"external_position"`
	ExternalProfile  struct {
		ExternalCorpName string `json:"external_corp_name"`
		WechatChannels   struct {
			Nickname string `json:"nickname"`
			Status   int    `json:"status"`
		} `json:"wechat_channels"`
		ExternalAttr []struct {
			Type int    `json:"type"`
			Name string `json:"name"`
			Text struct {
				Value string `json:"value"`
			} `json:"text,omitempty"`
			Web struct {
				Url   string `json:"url"`
				Title string `json:"title"`
			} `json:"web,omitempty"`
			Miniprogram struct {
				Appid    string `json:"appid"`
				Pagepath string `json:"pagepath"`
				Title    string `json:"title"`
			} `json:"miniprogram,omitempty"`
		} `json:"external_attr"`
	} `json:"external_profile"`
}

func (w WxIDProvider) getAccessToken() (string, error) {
	accessToken, err := w.cacheHandler.GetWxWebAccessTokenFromCache(context.Background(), w.providerId)
	if pb.IsRecordNotFound(err) {
		params := url.Values{}
		params.Add("corpid", w.wxCorpId)
		params.Add("corpsecret", w.secret)
		getAccessTokenUrl := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/gettoken?%v", params.Encode())
		resp, err := w.client.Get(getAccessTokenUrl)
		if err != nil {
			return "", err
		}
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				return
			}
		}(resp.Body)
		buf := new(bytes.Buffer)
		_, err = buf.ReadFrom(resp.Body)
		if err != nil {
			return "", err
		}

		var getAccessTokenResp GetAccessTokenResp
		if err = json.Unmarshal(buf.Bytes(), &getAccessTokenResp); err != nil {
			return "", err
		}

		if getAccessTokenResp.Errcode != 0 {
			return "", pb.ErrorRecordNotFound("token not found. getAccessTokenResp: %+v", getAccessTokenResp)
		}

		if err = w.cacheHandler.SetWxWebAccessTokenToCache(
			context.Background(), w.providerId,
			getAccessTokenResp.AccessToken, time.Second*time.Duration(getAccessTokenResp.ExpiresIn)); err != nil {
			return "", err
		}

		return getAccessTokenResp.AccessToken, nil
	}
	return accessToken, nil
}

func (w WxIDProvider) getUserId(code string) (GetUserIDResp, error) {
	accessToken, err := w.getAccessToken()
	if err != nil {
		return GetUserIDResp{}, err
	}

	params := url.Values{}
	params.Add("access_token", accessToken)
	params.Add("code", code)
	getUserIdUrl := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?%v", params.Encode())
	resp, err := w.client.Get(getUserIdUrl)
	if err != nil {
		return GetUserIDResp{}, nil
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			return
		}
	}(resp.Body)

	buf := new(bytes.Buffer)
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return GetUserIDResp{}, err
	}

	var userIDResp GetUserIDResp
	if err = json.Unmarshal(buf.Bytes(), &userIDResp); err != nil {
		return GetUserIDResp{}, err
	}
	err = w.errorHandler(userIDResp.Errcode, userIDResp.Errmsg)
	if err != nil {
		return GetUserIDResp{}, err
	}

	return userIDResp, nil
}

func (w WxIDProvider) errorHandler(errorCode int, errorMsg string) error {
	if errorCode == errorCodeSuccess {
		return nil
	} else if errorCode == errorCodeNotTrustedIp {
		return pb.ErrorQiyewxTrustedIpError("errorCode=%v, errorMsg=%v", errorCode, errorMsg)
	} else if errorCode == errorAgentIdWrong || errorCode == errorCodeSecretWrong || errorCode == errorCodeCorpIdWrong {
		return pb.ErrorQiyewxConfigError("errorCode=%v, errorMsg=%v", errorCode, errorMsg)
	} else {
		return pb.ErrorParamError("errorCode=%v, errorMsg=%v", errorCode, errorMsg)
	}
}

func (w WxIDProvider) GetUserInfo(code string) (GetUserInfo, error) {
	accessToken, err := w.getAccessToken()
	if err != nil {
		return GetUserInfo{}, err
	}

	userId, err := w.getUserId(code)
	if err != nil {
		return GetUserInfo{}, err
	}
	if userId.Userid == "" {
		return GetUserInfo{}, pb.ErrorRecordNotFound("user not found. userIdResp: %+v, code=%v", userId, code)
	}

	params := url.Values{}
	params.Add("access_token", accessToken)
	params.Add("userid", userId.Userid)

	getUserInfoUrl := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/user/get?%v", params.Encode())

	resp, err := w.client.Get(getUserInfoUrl)
	if err != nil {
		return GetUserInfo{}, err
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			return
		}
	}(resp.Body)

	buf := new(bytes.Buffer)
	_, err = buf.ReadFrom(resp.Body)
	if err != nil {
		return GetUserInfo{}, err
	}

	var userInfoResp GetUserInfo
	if err = json.Unmarshal(buf.Bytes(), &userInfoResp); err != nil {
		return GetUserInfo{}, err
	}

	err = w.errorHandler(userInfoResp.Errcode, userInfoResp.Errmsg)
	if err != nil {
		return GetUserInfo{}, err
	}

	return userInfoResp, nil
}
