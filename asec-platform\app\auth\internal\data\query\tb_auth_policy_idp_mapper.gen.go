// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbAuthPolicyIdpMapper(db *gorm.DB, opts ...gen.DOOption) tbAuthPolicyIdpMapper {
	_tbAuthPolicyIdpMapper := tbAuthPolicyIdpMapper{}

	_tbAuthPolicyIdpMapper.tbAuthPolicyIdpMapperDo.UseDB(db, opts...)
	_tbAuthPolicyIdpMapper.tbAuthPolicyIdpMapperDo.UseModel(&model.TbAuthPolicyIdpMapper{})

	tableName := _tbAuthPolicyIdpMapper.tbAuthPolicyIdpMapperDo.TableName()
	_tbAuthPolicyIdpMapper.ALL = field.NewAsterisk(tableName)
	_tbAuthPolicyIdpMapper.PolicyID = field.NewString(tableName, "policy_id")
	_tbAuthPolicyIdpMapper.IdpID = field.NewString(tableName, "idp_id")
	_tbAuthPolicyIdpMapper.CorpID = field.NewString(tableName, "corp_id")
	_tbAuthPolicyIdpMapper.CreatedAt = field.NewTime(tableName, "created_at")
	_tbAuthPolicyIdpMapper.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbAuthPolicyIdpMapper.fillFieldMap()

	return _tbAuthPolicyIdpMapper
}

type tbAuthPolicyIdpMapper struct {
	tbAuthPolicyIdpMapperDo tbAuthPolicyIdpMapperDo

	ALL       field.Asterisk
	PolicyID  field.String
	IdpID     field.String
	CorpID    field.String
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (t tbAuthPolicyIdpMapper) Table(newTableName string) *tbAuthPolicyIdpMapper {
	t.tbAuthPolicyIdpMapperDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbAuthPolicyIdpMapper) As(alias string) *tbAuthPolicyIdpMapper {
	t.tbAuthPolicyIdpMapperDo.DO = *(t.tbAuthPolicyIdpMapperDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbAuthPolicyIdpMapper) updateTableName(table string) *tbAuthPolicyIdpMapper {
	t.ALL = field.NewAsterisk(table)
	t.PolicyID = field.NewString(table, "policy_id")
	t.IdpID = field.NewString(table, "idp_id")
	t.CorpID = field.NewString(table, "corp_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbAuthPolicyIdpMapper) WithContext(ctx context.Context) *tbAuthPolicyIdpMapperDo {
	return t.tbAuthPolicyIdpMapperDo.WithContext(ctx)
}

func (t tbAuthPolicyIdpMapper) TableName() string { return t.tbAuthPolicyIdpMapperDo.TableName() }

func (t tbAuthPolicyIdpMapper) Alias() string { return t.tbAuthPolicyIdpMapperDo.Alias() }

func (t *tbAuthPolicyIdpMapper) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbAuthPolicyIdpMapper) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["policy_id"] = t.PolicyID
	t.fieldMap["idp_id"] = t.IdpID
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbAuthPolicyIdpMapper) clone(db *gorm.DB) tbAuthPolicyIdpMapper {
	t.tbAuthPolicyIdpMapperDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbAuthPolicyIdpMapper) replaceDB(db *gorm.DB) tbAuthPolicyIdpMapper {
	t.tbAuthPolicyIdpMapperDo.ReplaceDB(db)
	return t
}

type tbAuthPolicyIdpMapperDo struct{ gen.DO }

func (t tbAuthPolicyIdpMapperDo) Debug() *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Debug())
}

func (t tbAuthPolicyIdpMapperDo) WithContext(ctx context.Context) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbAuthPolicyIdpMapperDo) ReadDB() *tbAuthPolicyIdpMapperDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbAuthPolicyIdpMapperDo) WriteDB() *tbAuthPolicyIdpMapperDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbAuthPolicyIdpMapperDo) Session(config *gorm.Session) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbAuthPolicyIdpMapperDo) Clauses(conds ...clause.Expression) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbAuthPolicyIdpMapperDo) Returning(value interface{}, columns ...string) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbAuthPolicyIdpMapperDo) Not(conds ...gen.Condition) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbAuthPolicyIdpMapperDo) Or(conds ...gen.Condition) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbAuthPolicyIdpMapperDo) Select(conds ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbAuthPolicyIdpMapperDo) Where(conds ...gen.Condition) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbAuthPolicyIdpMapperDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbAuthPolicyIdpMapperDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbAuthPolicyIdpMapperDo) Order(conds ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbAuthPolicyIdpMapperDo) Distinct(cols ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbAuthPolicyIdpMapperDo) Omit(cols ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbAuthPolicyIdpMapperDo) Join(table schema.Tabler, on ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbAuthPolicyIdpMapperDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbAuthPolicyIdpMapperDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbAuthPolicyIdpMapperDo) Group(cols ...field.Expr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbAuthPolicyIdpMapperDo) Having(conds ...gen.Condition) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbAuthPolicyIdpMapperDo) Limit(limit int) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbAuthPolicyIdpMapperDo) Offset(offset int) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbAuthPolicyIdpMapperDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbAuthPolicyIdpMapperDo) Unscoped() *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbAuthPolicyIdpMapperDo) Create(values ...*model.TbAuthPolicyIdpMapper) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbAuthPolicyIdpMapperDo) CreateInBatches(values []*model.TbAuthPolicyIdpMapper, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbAuthPolicyIdpMapperDo) Save(values ...*model.TbAuthPolicyIdpMapper) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbAuthPolicyIdpMapperDo) First() (*model.TbAuthPolicyIdpMapper, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthPolicyIdpMapper), nil
	}
}

func (t tbAuthPolicyIdpMapperDo) Take() (*model.TbAuthPolicyIdpMapper, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthPolicyIdpMapper), nil
	}
}

func (t tbAuthPolicyIdpMapperDo) Last() (*model.TbAuthPolicyIdpMapper, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthPolicyIdpMapper), nil
	}
}

func (t tbAuthPolicyIdpMapperDo) Find() ([]*model.TbAuthPolicyIdpMapper, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbAuthPolicyIdpMapper), err
}

func (t tbAuthPolicyIdpMapperDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbAuthPolicyIdpMapper, err error) {
	buf := make([]*model.TbAuthPolicyIdpMapper, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbAuthPolicyIdpMapperDo) FindInBatches(result *[]*model.TbAuthPolicyIdpMapper, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbAuthPolicyIdpMapperDo) Attrs(attrs ...field.AssignExpr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbAuthPolicyIdpMapperDo) Assign(attrs ...field.AssignExpr) *tbAuthPolicyIdpMapperDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbAuthPolicyIdpMapperDo) Joins(fields ...field.RelationField) *tbAuthPolicyIdpMapperDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbAuthPolicyIdpMapperDo) Preload(fields ...field.RelationField) *tbAuthPolicyIdpMapperDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbAuthPolicyIdpMapperDo) FirstOrInit() (*model.TbAuthPolicyIdpMapper, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthPolicyIdpMapper), nil
	}
}

func (t tbAuthPolicyIdpMapperDo) FirstOrCreate() (*model.TbAuthPolicyIdpMapper, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAuthPolicyIdpMapper), nil
	}
}

func (t tbAuthPolicyIdpMapperDo) FindByPage(offset int, limit int) (result []*model.TbAuthPolicyIdpMapper, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbAuthPolicyIdpMapperDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbAuthPolicyIdpMapperDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbAuthPolicyIdpMapperDo) Delete(models ...*model.TbAuthPolicyIdpMapper) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbAuthPolicyIdpMapperDo) withDO(do gen.Dao) *tbAuthPolicyIdpMapperDo {
	t.DO = *do.(*gen.DO)
	return t
}
