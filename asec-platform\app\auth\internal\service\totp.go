package service

import (
	"bytes"
	"encoding/base64"
	"github.com/pquerna/otp"
	"github.com/pquerna/otp/totp"
	"image/png"
)

type TotpService struct {
}

func (t TotpService) generateTOTPKey(user string, period uint) (*otp.Key, string, error) {
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      "ASec",
		AccountName: user,
		Period:      period,        // 令牌有效期（秒）
		SecretSize:  20,            // 密钥长度
		Digits:      otp.DigitsSix, // 6位验证码
		Algorithm:   otp.AlgorithmSHA1,
	})
	if err != nil {
		return nil, "", err
	}

	// 生成二维码图片（Base64）
	var buf bytes.Buffer
	img, _ := key.Image(260, 260)
	err = png.Encode(&buf, img)
	if err != nil {
		return nil, "", err
	}
	qrCode := base64.StdEncoding.EncodeToString(buf.Bytes())
	return key, qrCode, nil
}
