package data

import (
	"context"
	"time"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"github.com/go-kratos/kratos/v2/log"
)

type jwtBlacklistRepo struct {
	data *Data
	log  *log.Helper
}

// NewJWTBlacklistRepo 创建 JWT 黑名单仓库实例
func NewJWTBlacklistRepo(data *Data, logger log.Logger) biz.JWTBlacklistRepo {
	return &jwtBlacklistRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// AddToBlacklist 将 token 添加到黑名单
func (r *jwtBlacklistRepo) AddToBlacklist(ctx context.Context, tokenJTI string, expiredAt time.Time) error {
	// 使用 Redis 存储黑名单，key 为 JTI，value 为过期时间
	key := "jwt_blacklist:" + tokenJTI
	// 设置过期时间，确保黑名单条目在 token 过期后自动清理
	duration := time.Until(expiredAt)
	if duration <= 0 {
		// 如果 token 已经过期，直接返回
		return nil
	}
	
	return r.data.rdb.Set(ctx, key, expiredAt.Unix(), duration).Err()
}

// IsBlacklisted 检查 token 是否在黑名单中
func (r *jwtBlacklistRepo) IsBlacklisted(ctx context.Context, tokenJTI string) (bool, error) {
	key := "jwt_blacklist:" + tokenJTI
	result := r.data.rdb.Exists(ctx, key)
	if result.Err() != nil {
		return false, result.Err()
	}
	return result.Val() > 0, nil
}

// RemoveFromBlacklist 从黑名单中移除 token
func (r *jwtBlacklistRepo) RemoveFromBlacklist(ctx context.Context, tokenJTI string) error {
	key := "jwt_blacklist:" + tokenJTI
	return r.data.rdb.Del(ctx, key).Err()
}

// RemoveExpiredFromBlacklist 清理过期的黑名单条目（Redis 自动过期，此方法为兼容性保留）
func (r *jwtBlacklistRepo) RemoveExpiredFromBlacklist(ctx context.Context) error {
	// Redis 会自动清理过期的 key，这里不需要手动实现
	// 可以通过 SCAN 命令查找特定模式的 key 进行批量清理，但通常不需要
	r.log.Debug("JWT blacklist cleanup is handled automatically by Redis TTL")
	return nil
}
