package ddr_source

import (
	"asdsec.com/asec/platform/app/console/app/ddr_source/ddr_source_api"
	"github.com/gin-gonic/gin"
)

func DdrSourceApi(r *gin.RouterGroup) {
	v := r.Group("/v1/ddr_source")
	{
		v.POST("/source", ddr_source_api.CreateSource)
		v.PUT("/source", ddr_source_api.UpdateSource)
		v.DELETE("/source", ddr_source_api.DelSource)
		v.POST("/source_list", ddr_source_api.SourceList)
		v.GET("/source", ddr_source_api.DetailSource)
		v.GET("/source_list_quote", ddr_source_api.SourceListQuote)
	}
}
