// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbAccessConfig(db *gorm.DB, opts ...gen.DOOption) tbAccessConfig {
	_tbAccessConfig := tbAccessConfig{}

	_tbAccessConfig.tbAccessConfigDo.UseDB(db, opts...)
	_tbAccessConfig.tbAccessConfigDo.UseModel(&model.TbAccessConfig{})

	tableName := _tbAccessConfig.tbAccessConfigDo.TableName()
	_tbAccessConfig.ALL = field.NewAsterisk(tableName)
	_tbAccessConfig.ID = field.NewInt32(tableName, "id")
	_tbAccessConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_tbAccessConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbAccessConfig.DeletedAt = field.NewField(tableName, "deleted_at")
	_tbAccessConfig.LanAddress = field.NewString(tableName, "lan_address")
	_tbAccessConfig.InternetAddress = field.NewString(tableName, "internet_address")
	_tbAccessConfig.AliasAddresses = field.NewString(tableName, "alias_addresses")

	_tbAccessConfig.fillFieldMap()

	return _tbAccessConfig
}

type tbAccessConfig struct {
	tbAccessConfigDo tbAccessConfigDo

	ALL             field.Asterisk
	ID              field.Int32
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field
	LanAddress      field.String
	InternetAddress field.String
	AliasAddresses  field.String

	fieldMap map[string]field.Expr
}

func (t tbAccessConfig) Table(newTableName string) *tbAccessConfig {
	t.tbAccessConfigDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbAccessConfig) As(alias string) *tbAccessConfig {
	t.tbAccessConfigDo.DO = *(t.tbAccessConfigDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbAccessConfig) updateTableName(table string) *tbAccessConfig {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt32(table, "id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.DeletedAt = field.NewField(table, "deleted_at")
	t.LanAddress = field.NewString(table, "lan_address")
	t.InternetAddress = field.NewString(table, "internet_address")
	t.AliasAddresses = field.NewString(table, "alias_addresses")

	t.fillFieldMap()

	return t
}

func (t *tbAccessConfig) WithContext(ctx context.Context) *tbAccessConfigDo {
	return t.tbAccessConfigDo.WithContext(ctx)
}

func (t tbAccessConfig) TableName() string { return t.tbAccessConfigDo.TableName() }

func (t tbAccessConfig) Alias() string { return t.tbAccessConfigDo.Alias() }

func (t tbAccessConfig) Columns(cols ...field.Expr) gen.Columns {
	return t.tbAccessConfigDo.Columns(cols...)
}

func (t *tbAccessConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbAccessConfig) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 7)
	t.fieldMap["id"] = t.ID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["deleted_at"] = t.DeletedAt
	t.fieldMap["lan_address"] = t.LanAddress
	t.fieldMap["internet_address"] = t.InternetAddress
	t.fieldMap["alias_addresses"] = t.AliasAddresses
}

func (t tbAccessConfig) clone(db *gorm.DB) tbAccessConfig {
	t.tbAccessConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbAccessConfig) replaceDB(db *gorm.DB) tbAccessConfig {
	t.tbAccessConfigDo.ReplaceDB(db)
	return t
}

type tbAccessConfigDo struct{ gen.DO }

func (t tbAccessConfigDo) Debug() *tbAccessConfigDo {
	return t.withDO(t.DO.Debug())
}

func (t tbAccessConfigDo) WithContext(ctx context.Context) *tbAccessConfigDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbAccessConfigDo) ReadDB() *tbAccessConfigDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbAccessConfigDo) WriteDB() *tbAccessConfigDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbAccessConfigDo) Session(config *gorm.Session) *tbAccessConfigDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbAccessConfigDo) Clauses(conds ...clause.Expression) *tbAccessConfigDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbAccessConfigDo) Returning(value interface{}, columns ...string) *tbAccessConfigDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbAccessConfigDo) Not(conds ...gen.Condition) *tbAccessConfigDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbAccessConfigDo) Or(conds ...gen.Condition) *tbAccessConfigDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbAccessConfigDo) Select(conds ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbAccessConfigDo) Where(conds ...gen.Condition) *tbAccessConfigDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbAccessConfigDo) Order(conds ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbAccessConfigDo) Distinct(cols ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbAccessConfigDo) Omit(cols ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbAccessConfigDo) Join(table schema.Tabler, on ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbAccessConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbAccessConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbAccessConfigDo) Group(cols ...field.Expr) *tbAccessConfigDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbAccessConfigDo) Having(conds ...gen.Condition) *tbAccessConfigDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbAccessConfigDo) Limit(limit int) *tbAccessConfigDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbAccessConfigDo) Offset(offset int) *tbAccessConfigDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbAccessConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbAccessConfigDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbAccessConfigDo) Unscoped() *tbAccessConfigDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbAccessConfigDo) Create(values ...*model.TbAccessConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbAccessConfigDo) CreateInBatches(values []*model.TbAccessConfig, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbAccessConfigDo) Save(values ...*model.TbAccessConfig) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbAccessConfigDo) First() (*model.TbAccessConfig, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAccessConfig), nil
	}
}

func (t tbAccessConfigDo) Take() (*model.TbAccessConfig, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAccessConfig), nil
	}
}

func (t tbAccessConfigDo) Last() (*model.TbAccessConfig, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAccessConfig), nil
	}
}

func (t tbAccessConfigDo) Find() ([]*model.TbAccessConfig, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbAccessConfig), err
}

func (t tbAccessConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbAccessConfig, err error) {
	buf := make([]*model.TbAccessConfig, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbAccessConfigDo) FindInBatches(result *[]*model.TbAccessConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbAccessConfigDo) Attrs(attrs ...field.AssignExpr) *tbAccessConfigDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbAccessConfigDo) Assign(attrs ...field.AssignExpr) *tbAccessConfigDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbAccessConfigDo) Joins(fields ...field.RelationField) *tbAccessConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbAccessConfigDo) Preload(fields ...field.RelationField) *tbAccessConfigDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbAccessConfigDo) FirstOrInit() (*model.TbAccessConfig, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAccessConfig), nil
	}
}

func (t tbAccessConfigDo) FirstOrCreate() (*model.TbAccessConfig, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbAccessConfig), nil
	}
}

func (t tbAccessConfigDo) FindByPage(offset int, limit int) (result []*model.TbAccessConfig, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbAccessConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbAccessConfigDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbAccessConfigDo) Delete(models ...*model.TbAccessConfig) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbAccessConfigDo) withDO(do gen.Dao) *tbAccessConfigDo {
	t.DO = *do.(*gen.DO)
	return t
}
