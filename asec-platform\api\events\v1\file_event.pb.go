// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: events/v1/file_event.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Severity int32

const (
	Severity_UNUSED   Severity = 0
	Severity_INFO     Severity = 1
	Severity_LOW      Severity = 2
	Severity_MEDIUM   Severity = 3
	Severity_HIGH     Severity = 4
	Severity_CRITICAL Severity = 5
)

// Enum value maps for Severity.
var (
	Severity_name = map[int32]string{
		0: "UNUSED",
		1: "INFO",
		2: "LOW",
		3: "MEDIUM",
		4: "HIGH",
		5: "CRITICAL",
	}
	Severity_value = map[string]int32{
		"UNUSED":   0,
		"INFO":     1,
		"LOW":      2,
		"MEDIUM":   3,
		"HIGH":     4,
		"CRITICAL": 5,
	}
)

func (x Severity) Enum() *Severity {
	p := new(Severity)
	*p = x
	return p
}

func (x Severity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Severity) Descriptor() protoreflect.EnumDescriptor {
	return file_events_v1_file_event_proto_enumTypes[0].Descriptor()
}

func (Severity) Type() protoreflect.EnumType {
	return &file_events_v1_file_event_proto_enumTypes[0]
}

func (x Severity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Severity.Descriptor instead.
func (Severity) EnumDescriptor() ([]byte, []int) {
	return file_events_v1_file_event_proto_rawDescGZIP(), []int{0}
}

type ChannelType int32

const (
	ChannelType_IM      ChannelType = 0
	ChannelType_EMAIL   ChannelType = 1
	ChannelType_FTP     ChannelType = 2
	ChannelType_USB     ChannelType = 3
	ChannelType_UNKNOWN ChannelType = 4
)

// Enum value maps for ChannelType.
var (
	ChannelType_name = map[int32]string{
		0: "IM",
		1: "EMAIL",
		2: "FTP",
		3: "USB",
		4: "UNKNOWN",
	}
	ChannelType_value = map[string]int32{
		"IM":      0,
		"EMAIL":   1,
		"FTP":     2,
		"USB":     3,
		"UNKNOWN": 4,
	}
)

func (x ChannelType) Enum() *ChannelType {
	p := new(ChannelType)
	*p = x
	return p
}

func (x ChannelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelType) Descriptor() protoreflect.EnumDescriptor {
	return file_events_v1_file_event_proto_enumTypes[1].Descriptor()
}

func (ChannelType) Type() protoreflect.EnumType {
	return &file_events_v1_file_event_proto_enumTypes[1]
}

func (x ChannelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelType.Descriptor instead.
func (ChannelType) EnumDescriptor() ([]byte, []int) {
	return file_events_v1_file_event_proto_rawDescGZIP(), []int{1}
}

type FileEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Events []*FileEvent `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *FileEventReq) Reset() {
	*x = FileEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_events_v1_file_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileEventReq) ProtoMessage() {}

func (x *FileEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_file_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileEventReq.ProtoReflect.Descriptor instead.
func (*FileEventReq) Descriptor() ([]byte, []int) {
	return file_events_v1_file_event_proto_rawDescGZIP(), []int{0}
}

func (x *FileEventReq) GetEvents() []*FileEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

type FileEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid              string   `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	CorpId            string   `protobuf:"bytes,2,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	EventType         string   `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	EventSubType      string   `protobuf:"bytes,4,opt,name=event_sub_type,json=eventSubType,proto3" json:"event_sub_type,omitempty"`
	EventSource       string   `protobuf:"bytes,5,opt,name=event_source,json=eventSource,proto3" json:"event_source,omitempty"`
	UserId            string   `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName          string   `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	AgentId           uint64   `protobuf:"varint,8,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName         string   `protobuf:"bytes,9,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	AgentIp           []string `protobuf:"bytes,10,rep,name=agent_ip,json=agentIp,proto3" json:"agent_ip,omitempty"`
	AgentMac          []string `protobuf:"bytes,11,rep,name=agent_mac,json=agentMac,proto3" json:"agent_mac,omitempty"`
	FileName          string   `protobuf:"bytes,12,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType          string   `protobuf:"bytes,13,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FilePath          string   `protobuf:"bytes,14,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	OriginalFileName  string   `protobuf:"bytes,15,opt,name=original_file_name,json=originalFileName,proto3" json:"original_file_name,omitempty"`
	OriginalFilePath  string   `protobuf:"bytes,16,opt,name=original_file_path,json=originalFilePath,proto3" json:"original_file_path,omitempty"`
	FileSize          int64    `protobuf:"varint,17,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	Owner             string   `protobuf:"bytes,18,opt,name=owner,proto3" json:"owner,omitempty"`
	FileCreateTime    int64    `protobuf:"varint,19,opt,name=file_create_time,json=fileCreateTime,proto3" json:"file_create_time,omitempty"`
	LastChangeTime    int64    `protobuf:"varint,20,opt,name=last_change_time,json=lastChangeTime,proto3" json:"last_change_time,omitempty"`
	ExtensionName     string   `protobuf:"bytes,21,opt,name=extension_name,json=extensionName,proto3" json:"extension_name,omitempty"`
	FileCategoryId    int64    `protobuf:"varint,22,opt,name=file_category_id,json=fileCategoryId,proto3" json:"file_category_id,omitempty"`
	RealExtensionName string   `protobuf:"bytes,23,opt,name=real_extension_name,json=realExtensionName,proto3" json:"real_extension_name,omitempty"`
	NameMatchInfo     string   `protobuf:"bytes,24,opt,name=name_match_info,json=nameMatchInfo,proto3" json:"name_match_info,omitempty"`
	ContentMatchInfo  string   `protobuf:"bytes,25,opt,name=content_match_info,json=contentMatchInfo,proto3" json:"content_match_info,omitempty"`
	Md5               string   `protobuf:"bytes,26,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256            string   `protobuf:"bytes,27,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Activity          string   `protobuf:"bytes,28,opt,name=activity,proto3" json:"activity,omitempty"`
	OccurTime         int64    `protobuf:"varint,29,opt,name=occur_time,json=occurTime,proto3" json:"occur_time,omitempty"`
	Channel           string   `protobuf:"bytes,30,opt,name=channel,proto3" json:"channel,omitempty"`
	ChannelType       string   `protobuf:"bytes,31,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	SoftwarePath      string   `protobuf:"bytes,32,opt,name=software_path,json=softwarePath,proto3" json:"software_path,omitempty"`
	DstPath           string   `protobuf:"bytes,33,opt,name=dst_path,json=dstPath,proto3" json:"dst_path,omitempty"`
	Score             int32    `protobuf:"varint,34,opt,name=score,proto3" json:"score,omitempty"`
	SensitiveRuleId   string   `protobuf:"bytes,35,opt,name=sensitive_rule_id,json=sensitiveRuleId,proto3" json:"sensitive_rule_id,omitempty"`
	SensitiveRuleName string   `protobuf:"bytes,36,opt,name=sensitive_rule_name,json=sensitiveRuleName,proto3" json:"sensitive_rule_name,omitempty"`
	SensitiveLevel    int32    `protobuf:"varint,37,opt,name=sensitive_level,json=sensitiveLevel,proto3" json:"sensitive_level,omitempty"`
	DataCategory      string   `protobuf:"bytes,38,opt,name=data_category,json=dataCategory,proto3" json:"data_category,omitempty"`
	Severity          Severity `protobuf:"varint,39,opt,name=severity,proto3,enum=api.asdsec.file_event.Severity" json:"severity,omitempty"`
	SeverityId        int32    `protobuf:"varint,40,opt,name=severity_id,json=severityId,proto3" json:"severity_id,omitempty"`
	PlatType          string   `protobuf:"bytes,41,opt,name=plat_type,json=platType,proto3" json:"plat_type,omitempty"`
	TraceId           []string `protobuf:"bytes,42,rep,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	CompressEncrypt   int32    `protobuf:"varint,43,opt,name=compress_encrypt,json=compressEncrypt,proto3" json:"compress_encrypt,omitempty"`
	UserTags          []string `protobuf:"bytes,44,rep,name=user_tags,json=userTags,proto3" json:"user_tags,omitempty"`
	IngestionTime     int64    `protobuf:"varint,45,opt,name=ingestion_time,json=ingestionTime,proto3" json:"ingestion_time,omitempty"`
	CreateTime        int64    `protobuf:"varint,46,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	FileProperties    []string `protobuf:"bytes,47,rep,name=file_properties,json=fileProperties,proto3" json:"file_properties,omitempty"`
	// 网关接入日志信息
	SrcIp             string   `protobuf:"bytes,48,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	SrcPort           int32    `protobuf:"varint,49,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`
	SrcCountry        string   `protobuf:"bytes,50,opt,name=src_country,json=srcCountry,proto3" json:"src_country,omitempty"`
	SrcCountryName    string   `protobuf:"bytes,51,opt,name=src_country_name,json=srcCountryName,proto3" json:"src_country_name,omitempty"`
	SrcProvince       string   `protobuf:"bytes,52,opt,name=src_province,json=srcProvince,proto3" json:"src_province,omitempty"`
	SrcCity           string   `protobuf:"bytes,53,opt,name=src_city,json=srcCity,proto3" json:"src_city,omitempty"`
	SrcLatitude       string   `protobuf:"bytes,54,opt,name=src_latitude,json=srcLatitude,proto3" json:"src_latitude,omitempty"`
	SrcLongitude      string   `protobuf:"bytes,55,opt,name=src_longitude,json=srcLongitude,proto3" json:"src_longitude,omitempty"`
	DstIp             string   `protobuf:"bytes,56,opt,name=dst_ip,json=dstIp,proto3" json:"dst_ip,omitempty"`
	DstPort           int32    `protobuf:"varint,57,opt,name=dst_port,json=dstPort,proto3" json:"dst_port,omitempty"`
	DstUrl            string   `protobuf:"bytes,58,opt,name=dst_url,json=dstUrl,proto3" json:"dst_url,omitempty"`
	AppId             int64    `protobuf:"varint,59,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName           string   `protobuf:"bytes,60,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	Protocol          string   `protobuf:"bytes,61,opt,name=protocol,proto3" json:"protocol,omitempty"`
	StrategyId        int64    `protobuf:"varint,62,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id,omitempty"`
	StrategyName      string   `protobuf:"bytes,63,opt,name=strategy_name,json=strategyName,proto3" json:"strategy_name,omitempty"`
	AccessStatus      int32    `protobuf:"varint,64,opt,name=access_status,json=accessStatus,proto3" json:"access_status,omitempty"`
	AccessMethod      string   `protobuf:"bytes,65,opt,name=access_method,json=accessMethod,proto3" json:"access_method,omitempty"`
	DenyReason        string   `protobuf:"bytes,66,opt,name=deny_reason,json=denyReason,proto3" json:"deny_reason,omitempty"`
	AuthError         string   `protobuf:"bytes,67,opt,name=auth_error,json=authError,proto3" json:"auth_error,omitempty"`
	AuthErrorDetail   string   `protobuf:"bytes,68,opt,name=auth_error_detail,json=authErrorDetail,proto3" json:"auth_error_detail,omitempty"`
	SubSrcTraceId     []string `protobuf:"bytes,69,rep,name=sub_src_trace_id,json=subSrcTraceId,proto3" json:"sub_src_trace_id,omitempty"`
	PublicIp          string   `protobuf:"bytes,70,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	ScoreReason       string   `protobuf:"bytes,71,opt,name=score_reason,json=scoreReason,proto3" json:"score_reason,omitempty"`
	SrcPath           string   `protobuf:"bytes,72,opt,name=src_path,json=srcPath,proto3" json:"src_path,omitempty"`
	SourceId          string   `protobuf:"bytes,73,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceName        string   `protobuf:"bytes,74,opt,name=source_name,json=sourceName,proto3" json:"source_name,omitempty"`
	SourceType        string   `protobuf:"bytes,75,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	CheckFileSuffix   bool     `protobuf:"varint,76,opt,name=check_file_suffix,json=checkFileSuffix,proto3" json:"check_file_suffix,omitempty"`
	CheckFileCompress bool     `protobuf:"varint,77,opt,name=check_file_compress,json=checkFileCompress,proto3" json:"check_file_compress,omitempty"`
	SensitiveInfo     string   `protobuf:"bytes,78,opt,name=sensitive_info,json=sensitiveInfo,proto3" json:"sensitive_info,omitempty"`
	FileHideSuffix    uint32   `protobuf:"varint,79,opt,name=file_hide_suffix,json=fileHideSuffix,proto3" json:"file_hide_suffix,omitempty"`
	DataCategoryName  string   `protobuf:"bytes,80,opt,name=data_category_name,json=dataCategoryName,proto3" json:"data_category_name,omitempty"`
	// 策略命中时间(ms)
	StrategyCheckTime uint32 `protobuf:"varint,81,opt,name=strategy_check_time,json=strategyCheckTime,proto3" json:"strategy_check_time,omitempty"`
	ProcessInfo       string `protobuf:"bytes,82,opt,name=process_info,json=processInfo,proto3" json:"process_info,omitempty"`
}

func (x *FileEvent) Reset() {
	*x = FileEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_events_v1_file_event_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileEvent) ProtoMessage() {}

func (x *FileEvent) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_file_event_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileEvent.ProtoReflect.Descriptor instead.
func (*FileEvent) Descriptor() ([]byte, []int) {
	return file_events_v1_file_event_proto_rawDescGZIP(), []int{1}
}

func (x *FileEvent) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *FileEvent) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *FileEvent) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *FileEvent) GetEventSubType() string {
	if x != nil {
		return x.EventSubType
	}
	return ""
}

func (x *FileEvent) GetEventSource() string {
	if x != nil {
		return x.EventSource
	}
	return ""
}

func (x *FileEvent) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *FileEvent) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *FileEvent) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *FileEvent) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *FileEvent) GetAgentIp() []string {
	if x != nil {
		return x.AgentIp
	}
	return nil
}

func (x *FileEvent) GetAgentMac() []string {
	if x != nil {
		return x.AgentMac
	}
	return nil
}

func (x *FileEvent) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileEvent) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *FileEvent) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FileEvent) GetOriginalFileName() string {
	if x != nil {
		return x.OriginalFileName
	}
	return ""
}

func (x *FileEvent) GetOriginalFilePath() string {
	if x != nil {
		return x.OriginalFilePath
	}
	return ""
}

func (x *FileEvent) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileEvent) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *FileEvent) GetFileCreateTime() int64 {
	if x != nil {
		return x.FileCreateTime
	}
	return 0
}

func (x *FileEvent) GetLastChangeTime() int64 {
	if x != nil {
		return x.LastChangeTime
	}
	return 0
}

func (x *FileEvent) GetExtensionName() string {
	if x != nil {
		return x.ExtensionName
	}
	return ""
}

func (x *FileEvent) GetFileCategoryId() int64 {
	if x != nil {
		return x.FileCategoryId
	}
	return 0
}

func (x *FileEvent) GetRealExtensionName() string {
	if x != nil {
		return x.RealExtensionName
	}
	return ""
}

func (x *FileEvent) GetNameMatchInfo() string {
	if x != nil {
		return x.NameMatchInfo
	}
	return ""
}

func (x *FileEvent) GetContentMatchInfo() string {
	if x != nil {
		return x.ContentMatchInfo
	}
	return ""
}

func (x *FileEvent) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileEvent) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileEvent) GetActivity() string {
	if x != nil {
		return x.Activity
	}
	return ""
}

func (x *FileEvent) GetOccurTime() int64 {
	if x != nil {
		return x.OccurTime
	}
	return 0
}

func (x *FileEvent) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *FileEvent) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *FileEvent) GetSoftwarePath() string {
	if x != nil {
		return x.SoftwarePath
	}
	return ""
}

func (x *FileEvent) GetDstPath() string {
	if x != nil {
		return x.DstPath
	}
	return ""
}

func (x *FileEvent) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *FileEvent) GetSensitiveRuleId() string {
	if x != nil {
		return x.SensitiveRuleId
	}
	return ""
}

func (x *FileEvent) GetSensitiveRuleName() string {
	if x != nil {
		return x.SensitiveRuleName
	}
	return ""
}

func (x *FileEvent) GetSensitiveLevel() int32 {
	if x != nil {
		return x.SensitiveLevel
	}
	return 0
}

func (x *FileEvent) GetDataCategory() string {
	if x != nil {
		return x.DataCategory
	}
	return ""
}

func (x *FileEvent) GetSeverity() Severity {
	if x != nil {
		return x.Severity
	}
	return Severity_UNUSED
}

func (x *FileEvent) GetSeverityId() int32 {
	if x != nil {
		return x.SeverityId
	}
	return 0
}

func (x *FileEvent) GetPlatType() string {
	if x != nil {
		return x.PlatType
	}
	return ""
}

func (x *FileEvent) GetTraceId() []string {
	if x != nil {
		return x.TraceId
	}
	return nil
}

func (x *FileEvent) GetCompressEncrypt() int32 {
	if x != nil {
		return x.CompressEncrypt
	}
	return 0
}

func (x *FileEvent) GetUserTags() []string {
	if x != nil {
		return x.UserTags
	}
	return nil
}

func (x *FileEvent) GetIngestionTime() int64 {
	if x != nil {
		return x.IngestionTime
	}
	return 0
}

func (x *FileEvent) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *FileEvent) GetFileProperties() []string {
	if x != nil {
		return x.FileProperties
	}
	return nil
}

func (x *FileEvent) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *FileEvent) GetSrcPort() int32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *FileEvent) GetSrcCountry() string {
	if x != nil {
		return x.SrcCountry
	}
	return ""
}

func (x *FileEvent) GetSrcCountryName() string {
	if x != nil {
		return x.SrcCountryName
	}
	return ""
}

func (x *FileEvent) GetSrcProvince() string {
	if x != nil {
		return x.SrcProvince
	}
	return ""
}

func (x *FileEvent) GetSrcCity() string {
	if x != nil {
		return x.SrcCity
	}
	return ""
}

func (x *FileEvent) GetSrcLatitude() string {
	if x != nil {
		return x.SrcLatitude
	}
	return ""
}

func (x *FileEvent) GetSrcLongitude() string {
	if x != nil {
		return x.SrcLongitude
	}
	return ""
}

func (x *FileEvent) GetDstIp() string {
	if x != nil {
		return x.DstIp
	}
	return ""
}

func (x *FileEvent) GetDstPort() int32 {
	if x != nil {
		return x.DstPort
	}
	return 0
}

func (x *FileEvent) GetDstUrl() string {
	if x != nil {
		return x.DstUrl
	}
	return ""
}

func (x *FileEvent) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *FileEvent) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *FileEvent) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *FileEvent) GetStrategyId() int64 {
	if x != nil {
		return x.StrategyId
	}
	return 0
}

func (x *FileEvent) GetStrategyName() string {
	if x != nil {
		return x.StrategyName
	}
	return ""
}

func (x *FileEvent) GetAccessStatus() int32 {
	if x != nil {
		return x.AccessStatus
	}
	return 0
}

func (x *FileEvent) GetAccessMethod() string {
	if x != nil {
		return x.AccessMethod
	}
	return ""
}

func (x *FileEvent) GetDenyReason() string {
	if x != nil {
		return x.DenyReason
	}
	return ""
}

func (x *FileEvent) GetAuthError() string {
	if x != nil {
		return x.AuthError
	}
	return ""
}

func (x *FileEvent) GetAuthErrorDetail() string {
	if x != nil {
		return x.AuthErrorDetail
	}
	return ""
}

func (x *FileEvent) GetSubSrcTraceId() []string {
	if x != nil {
		return x.SubSrcTraceId
	}
	return nil
}

func (x *FileEvent) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *FileEvent) GetScoreReason() string {
	if x != nil {
		return x.ScoreReason
	}
	return ""
}

func (x *FileEvent) GetSrcPath() string {
	if x != nil {
		return x.SrcPath
	}
	return ""
}

func (x *FileEvent) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *FileEvent) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

func (x *FileEvent) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

func (x *FileEvent) GetCheckFileSuffix() bool {
	if x != nil {
		return x.CheckFileSuffix
	}
	return false
}

func (x *FileEvent) GetCheckFileCompress() bool {
	if x != nil {
		return x.CheckFileCompress
	}
	return false
}

func (x *FileEvent) GetSensitiveInfo() string {
	if x != nil {
		return x.SensitiveInfo
	}
	return ""
}

func (x *FileEvent) GetFileHideSuffix() uint32 {
	if x != nil {
		return x.FileHideSuffix
	}
	return 0
}

func (x *FileEvent) GetDataCategoryName() string {
	if x != nil {
		return x.DataCategoryName
	}
	return ""
}

func (x *FileEvent) GetStrategyCheckTime() uint32 {
	if x != nil {
		return x.StrategyCheckTime
	}
	return 0
}

func (x *FileEvent) GetProcessInfo() string {
	if x != nil {
		return x.ProcessInfo
	}
	return ""
}

type MatchInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchInfo []*MatchInfo `protobuf:"bytes,1,rep,name=matchInfo,proto3" json:"matchInfo,omitempty"`
}

func (x *MatchInfoList) Reset() {
	*x = MatchInfoList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_events_v1_file_event_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchInfoList) ProtoMessage() {}

func (x *MatchInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_file_event_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchInfoList.ProtoReflect.Descriptor instead.
func (*MatchInfoList) Descriptor() ([]byte, []int) {
	return file_events_v1_file_event_proto_rawDescGZIP(), []int{2}
}

func (x *MatchInfoList) GetMatchInfo() []*MatchInfo {
	if x != nil {
		return x.MatchInfo
	}
	return nil
}

type MatchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleId        int64  `protobuf:"varint,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	Desc          string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	SensitiveData string `protobuf:"bytes,3,opt,name=sensitive_data,json=sensitiveData,proto3" json:"sensitive_data,omitempty"`
}

func (x *MatchInfo) Reset() {
	*x = MatchInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_events_v1_file_event_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchInfo) ProtoMessage() {}

func (x *MatchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_file_event_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchInfo.ProtoReflect.Descriptor instead.
func (*MatchInfo) Descriptor() ([]byte, []int) {
	return file_events_v1_file_event_proto_rawDescGZIP(), []int{3}
}

func (x *MatchInfo) GetRuleId() int64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

func (x *MatchInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *MatchInfo) GetSensitiveData() string {
	if x != nil {
		return x.SensitiveData
	}
	return ""
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_events_v1_file_event_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_events_v1_file_event_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_events_v1_file_event_proto_rawDescGZIP(), []int{4}
}

var File_events_v1_file_event_proto protoreflect.FileDescriptor

var file_events_v1_file_event_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x22, 0x48, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xf3, 0x15,
	0x0a, 0x09, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x2c, 0x0a, 0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x28,
	0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x72, 0x65, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x61,
	0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x13, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x25, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64,
	0x61, 0x74, 0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x08, 0x73,
	0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08,
	0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x76, 0x65,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x28, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73,
	0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x65, 0x6e,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x6f, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x69, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x2e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x2f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x72,
	0x63, 0x5f, 0x69, 0x70, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x72, 0x63, 0x49,
	0x70, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x31, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x72, 0x63, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x32, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x72, 0x63, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x72, 0x63, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x72, 0x63, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x72, 0x63, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x72, 0x63, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72,
	0x63, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x72,
	0x63, 0x43, 0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x72, 0x63, 0x5f, 0x6c, 0x61, 0x74,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x72, 0x63,
	0x4c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x72, 0x63, 0x5f,
	0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x73, 0x72, 0x63, 0x4c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x64, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64,
	0x73, 0x74, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x39, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x64, 0x73, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x3c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x40, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6e, 0x79, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6e,
	0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x43, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x75, 0x74,
	0x68, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x44, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x27, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x72, 0x63, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x45, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75,
	0x62, 0x53, 0x72, 0x63, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x46, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x49, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x47, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x72, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x48, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x72, 0x63, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x49, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x4c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x75, 0x66, 0x66, 0x69,
	0x78, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x68, 0x69, 0x64, 0x65, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x4f, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x69, 0x64, 0x65, 0x53, 0x75, 0x66, 0x66,
	0x69, 0x78, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x50, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x64, 0x61, 0x74, 0x61, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x51, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x52, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x4f, 0x0a, 0x0d, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73,
	0x64, 0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5f, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x07, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2a, 0x4d,
	0x0a, 0x08, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x4e,
	0x55, 0x53, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x01,
	0x12, 0x07, 0x0a, 0x03, 0x4c, 0x4f, 0x57, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x45, 0x44,
	0x49, 0x55, 0x4d, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x49, 0x47, 0x48, 0x10, 0x04, 0x12,
	0x0c, 0x0a, 0x08, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x05, 0x2a, 0x3f, 0x0a,
	0x0b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x06, 0x0a, 0x02,
	0x49, 0x4d, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x01, 0x12,
	0x07, 0x0a, 0x03, 0x46, 0x54, 0x50, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x53, 0x42, 0x10,
	0x03, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x32, 0x59,
	0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x67, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x28, 0x01, 0x42, 0x40, 0x0a, 0x13, 0x63, 0x6f, 0x6d,
	0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x5a, 0x29, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65,
	0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_events_v1_file_event_proto_rawDescOnce sync.Once
	file_events_v1_file_event_proto_rawDescData = file_events_v1_file_event_proto_rawDesc
)

func file_events_v1_file_event_proto_rawDescGZIP() []byte {
	file_events_v1_file_event_proto_rawDescOnce.Do(func() {
		file_events_v1_file_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_events_v1_file_event_proto_rawDescData)
	})
	return file_events_v1_file_event_proto_rawDescData
}

var file_events_v1_file_event_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_events_v1_file_event_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_events_v1_file_event_proto_goTypes = []interface{}{
	(Severity)(0),         // 0: api.asdsec.file_event.Severity
	(ChannelType)(0),      // 1: api.asdsec.file_event.ChannelType
	(*FileEventReq)(nil),  // 2: api.asdsec.file_event.FileEventReq
	(*FileEvent)(nil),     // 3: api.asdsec.file_event.FileEvent
	(*MatchInfoList)(nil), // 4: api.asdsec.file_event.MatchInfoList
	(*MatchInfo)(nil),     // 5: api.asdsec.file_event.MatchInfo
	(*Reply)(nil),         // 6: api.asdsec.file_event.Reply
}
var file_events_v1_file_event_proto_depIdxs = []int32{
	3, // 0: api.asdsec.file_event.FileEventReq.events:type_name -> api.asdsec.file_event.FileEvent
	0, // 1: api.asdsec.file_event.FileEvent.severity:type_name -> api.asdsec.file_event.Severity
	5, // 2: api.asdsec.file_event.MatchInfoList.matchInfo:type_name -> api.asdsec.file_event.MatchInfo
	2, // 3: api.asdsec.file_event.Event.CreateLog:input_type -> api.asdsec.file_event.FileEventReq
	6, // 4: api.asdsec.file_event.Event.CreateLog:output_type -> api.asdsec.file_event.Reply
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_events_v1_file_event_proto_init() }
func file_events_v1_file_event_proto_init() {
	if File_events_v1_file_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_events_v1_file_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_events_v1_file_event_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_events_v1_file_event_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchInfoList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_events_v1_file_event_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_events_v1_file_event_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_events_v1_file_event_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_events_v1_file_event_proto_goTypes,
		DependencyIndexes: file_events_v1_file_event_proto_depIdxs,
		EnumInfos:         file_events_v1_file_event_proto_enumTypes,
		MessageInfos:      file_events_v1_file_event_proto_msgTypes,
	}.Build()
	File_events_v1_file_event_proto = out.File
	file_events_v1_file_event_proto_rawDesc = nil
	file_events_v1_file_event_proto_goTypes = nil
	file_events_v1_file_event_proto_depIdxs = nil
}
