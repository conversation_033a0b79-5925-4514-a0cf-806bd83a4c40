package alert_event

import (
	"asdsec.com/asec/platform/app/console/app/alert_event/api"
	"github.com/gin-gonic/gin"
)

func AlertEventApi(r *gin.RouterGroup) {
	v := r.Group("/v1/alert/event")
	{
		v.POST("/total", api.GetAlertEventTotal)
		v.POST("/list", api.GetAlertEventList)
		v.GET("/detail", api.GetAlertEventDetail)
		v.POST("/export", api.Export)
		v.PUT("/setting", api.UpdateDlpScoreConfig)
	}
}
