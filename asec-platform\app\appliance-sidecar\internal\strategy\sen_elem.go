package strategy

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"path/filepath"
	"sync"
	"time"

	v1 "asdsec.com/asec/platform/api/strategy/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/pkg/utils"
)

var ElemFilePath string

func init() {
	ElemFilePath = filepath.Join(utils.GetConfigDir(), "AsecSensitiveDefaultData.json")
}

func GetSenElem(ctx context.Context, wg *sync.WaitGroup) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			err := SyncSenElem(ctx)
			if err != nil {
				global.Logger.Sugar().Errorf("error: %v", err)
				return
			}
		}
	}
}

func SyncSenElem(ctx context.Context) error {
	conn, err := connection.GetPlatformConnection(ctx)
	if err != nil {
		global.Logger.Sugar().Errorf("SyncRouteAddr GetPlatformConnection error : %v", err)
		return err
	}
	client := v1.NewGetSenElemClient(conn)
	req := v1.GetElemReq{
		UserId: "",
	}
	elem, err := client.AgentGetSenElem(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("get client error : %v", err)
		return err
	}
	marshal, err := json.Marshal(elem.GetInfo())
	if err != nil {
		global.Logger.Sugar().Errorf("json marshal error : %v", err)
		return err
	}
	//写入文件
	err = ioutil.WriteFile(ElemFilePath, marshal, 0640)
	if err != nil {
		global.Logger.Sugar().Errorf("WriteFile error : %v", err)
		return err
	}
	connection.CloseConnection(conn)
	return nil
}
