#!/bin/bash

set -e

# 定义日志文件路径
LOG_DIR="/opt/platform/logs"
UPGRADE_LOG_FILE="${LOG_DIR}/upgrade_$(date +'%Y%m%d-%H%M%S').log"

# 创建日志目录
mkdir -p $LOG_DIR

# 函数用于记录日志
function log() {
    echo "[`date +'%Y-%m-%d %H:%M:%S'`] $1" | tee -a $UPGRADE_LOG_FILE
}

log "Starting upgrade process."

# 记录当前版本信息
function record_current_version() {
    if [ -f "/opt/platform/current/version.conf" ]; then
        log "Current version information:"
        cat /opt/platform/current/version.conf | tee -a $UPGRADE_LOG_FILE
    else
        log "No current version information found."
    fi
}

# 记录新版本信息
function record_new_version() {
    if [ -f "$current_dir/version.conf" ]; then
        log "New version information:"
        cat "$current_dir/version.conf" | tee -a $UPGRADE_LOG_FILE
    else
        log "No new version information found."
    fi
}

#宿主机环境执行shell升级
shell_upgrade(){
  cd ${upgrade_dir}
  # 执行shell升级目录下的shell文件
  echo "--------------------------start execute shell------------------------------"
  echo "[`TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S"`] Step3: start execute shell file" >> ${upgrade_log_file}
  if [ ! -f ./shell_upgrade.sh ];then
      echo "shell upgrade file not exist, current_path=${upgrade_dir}"
  fi
      chmod +x ./shell_upgrade.sh
  if ./shell_upgrade.sh; then
    echo "shell_upgrade.sh 执行成功"
  else
    echo "shell_upgrade.sh 执行失败"
    exit 1
  fi

}


data_upgrade(){
#该目录用来上传图标文件
  icon_dir="/opt/platform/icon/"
  mkdir -p  $icon_dir
  chmod 777 $icon_dir

  cd ${upgrade_dir}
  log "start database upgrade...."
#  chmod +x ${upgrade_dir}/upgrader/upgrade_entry.sh
  docker compose run upgrader sh "-c" "chmod +x /opt/upgrader/upgrade_entry.sh && /opt/upgrader/upgrade_entry.sh"
}

function install_upgrader_asec_services() {
  current_dir=$(dirname "$PWD")
  if [ -f "/etc/asec/slave" ]; then
      # 如果文件存在,则将 console和auth的配置primary置为false,禁用掉定时任务
      sed -i 's/primary: true/primary: false/' "$current_dir"/asec-console/config/application.yaml
      sed -i 's/primary: true/primary: false/' "$current_dir"/auth/config/config.yaml
  fi

  # 进入 console 服务目录并更新
  cd "$current_dir/asec-console"
  docker compose up -d
  log "Upgrade [asec-console] Service Successful!"

  # 进入 appliance-center 服务目录并更新
  cd "$current_dir/appliance-center"
  docker compose up -d
  log "Upgrade [appliance-center] Service Successful!"

  # 进入 log-center 服务目录并更新
  cd "$current_dir/log-center"
  docker compose up -d
  log "Upgrade [log-center] Service Successful!"

  # 进入 auth 服务目录并更新
  cd "$current_dir/auth"
  docker compose up -d
  log "Upgrade [auth] Service Successful!"

  # 进入 ueba-engine 服务目录并更新
  cd "$current_dir/ueba-engine"
  docker compose up -d
  log "Upgrade [ueba-engine] Service Successful!"

  # 企业微信可信域名验证文件locations文件更新
  if [ ! -f /opt/asdsec-compose/openresty/conf.d/wechat-verify-locations.conf ]; then
    # 确保目标目录存在
    mkdir -p /opt/asdsec-compose/openresty/conf.d/
    cp "$current_dir/openresty/conf.d/wechat-verify-locations.conf" /opt/asdsec-compose/openresty/conf.d/
    log "Copied wechat-verify-locations.conf to openresty configuration directory"
  fi

  if [ -f "/etc/asec/slave" ]; then
    log "Skip version update for slave node."
  else
    POSTGRES_CONTAINER=$(docker ps --format "{{.Names}}" | grep -E "^(postgres|pg-0)$")
    if [ -z "$POSTGRES_CONTAINER" ]; then
      log "Error: No Postgres container found. Exiting."
      exit 1
    fi

    # 执行更新
    log "Update system version in $POSTGRES_CONTAINER: branch: $BRANCH_NAME, version: $VERSION, package_name: $PACKAGE_NAME"

    SQL_COMMAND="INSERT INTO public.tb_system_version (id, name, branch, version, package_name, update_time)
    VALUES (1, 'platform', '$BRANCH_NAME', '$VERSION', '$PACKAGE_NAME', now())
    ON CONFLICT (id)
    DO UPDATE SET branch = '$BRANCH_NAME', version = '$VERSION', package_name = '$PACKAGE_NAME', update_time = now();"

    log "docker exec -it $POSTGRES_CONTAINER psql -U asec -d asec_platform -c \"$SQL_COMMAND\""

    docker exec  -e PGPASSWORD=pg@asd@1234! -it $POSTGRES_CONTAINER psql -U asec -d asec_platform -c "$SQL_COMMAND"

    if [ $? -eq 0 ]; then
      log "Update system version successful!"
    else
      log "Error: Failed to update system version."
      exit 1
    fi
  fi
  log "Service upgrade completed!"
}

function install_upgrader_frontend() {
  #frontend服务更新
  cd $current_dir/frontend
  log "---------------upgrade web---------------"
  if [ ! -x ./install.sh ];then
      chmod +x ./install.sh
  fi
  ./install.sh
}

function restart_openresty() {
  container_name="openresty"
  if docker ps -a | grep -q $container_name; then
    log "Container $container_name exists, recreating to update volume mappings..."
    cd "$current_dir/openresty"
    docker compose down
    docker compose up -d
    log "Container $container_name recreated successfully"
  else
    log "Container $container_name does not exist."
  fi
}
function install_sys_panel() {
    log "[install sys panel] start..."
    chmod +x /opt/platform/current/sys-panel/sys-panel

    cp -f $current_dir/sys-panel/sys-panel.service /etc/systemd/system/sys-panel.service
    systemctl daemon-reload

    # 启用并启动服务
    log "启用并启动 sys panel 服务..."
    systemctl enable sys-panel.service
    systemctl restart sys-panel.service

    log "检查服务状态..."
    systemctl --no-pager status sys-panel.service | grep "Active:" || true
    log "[install sys panel] finished..."
}

# 服务升级
service_upgrade(){
  #加载版本TAG文件环境变量
  source $current_dir/version.conf
  log "[`TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S"`] Do not skip service updates"
  # 升级服务并且替换前端代码
  log "--------------------------start upgrade service------------------------------"
  log "[`TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S"`] Step4: start upgrade service"

  install_upgrader_asec_services
  install_sys_panel
  install_upgrader_frontend
  restart_openresty

  log "[`TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S"`] Upgrade [frontend] Successful!"
  log "--------------------------upgrade service complete------------------------------"
  log "[`TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S"`] Step4: upgrade service complete!"
}
function docker_login() {

    # 设置最大重试次数
    max_retries=5
    retry_count=0

    # 定义登录命令
    login_command="docker login --username=asdsec registry.cn-guangzhou.aliyuncs.com -p asd@1234!"

    # 循环重试直到成功或达到最大重试次数
    while true; do
      # 执行登录命令
      $login_command

      # 检查登录是否成功
      login_status=$?

      if [ $login_status -eq 0 ]; then
        log "登录成功"
        break
      elif [ $retry_count -lt $max_retries ]; then
        # 增加重试计数
        retry_count=$((retry_count+1))
        log "登录失败，重试 (${retry_count}/${max_retries})"
        sleep 1  # 可以增加一些延迟时间

        continue
      else
        log "达到最大重试次数，登录失败"
        break
      fi
    done
}

function reserve_last() {
    if [ -e /opt/platform/current ]; then
        mkdir -p /opt/platform/last
        rm -fr /opt/platform/last/*
        mv -f /opt/platform/current /opt/platform/last
    else
        log "current does not exist, skipping mv"
    fi
}

# === spa-node-ebpf 交互式安装逻辑（全局变量） ===
spa_ebpf_selected="n"
spa_ebpf_iface=""

function ask_spa_ebpf() {
  # 优先读取/etc/asec/spa_ebpf.conf
  if [ -f /etc/asec/spa_ebpf.conf ]; then
    source /etc/asec/spa_ebpf.conf
    spa_ebpf_selected="$SPA_EBPF_SELECTED"
    spa_ebpf_iface="$SPA_EBPF_IFACE"
    return
  fi
  default_iface=$(ip -o link show | awk -F': ' '$2 != "lo" {print $2; exit}')
  read -e -p "请输入要用于ebpf的网卡名称: " -i "$default_iface" spa_ebpf_iface
}

function install_spa_ebpf_service() {
    echo "正在安装并启用 spa-node-ebpf 服务，使用网卡: $spa_ebpf_iface ..."
    mkdir -p /opt/platform/current/spa
    echo "SPA_EBPF_IFACE=$spa_ebpf_iface" > /opt/platform/current/spa/spa.conf
    echo "SPA_MANAGER_URL=http://127.0.0.1:9000" >> /opt/platform/current/spa/spa.conf
    echo "CONTAINER_NAMES=openresty,asec_gateway" >> /opt/platform/current/spa/spa.conf
    cp -f /opt/platform/current/spa/spa-node-ebpf.service /etc/systemd/system/spa-node-ebpf.service
    systemctl daemon-reload
    systemctl enable spa-node-ebpf.service
    systemctl restart spa-node-ebpf.service
    systemctl --no-pager status spa-node-ebpf.service | grep "Active:" || true
    echo "spa-node-ebpf 服务已安装并启动。"
}

function upgrade() {
    ask_spa_ebpf
    current_dir=$(cd "$(dirname "$0")"; pwd)
    # 记录版本信息
    record_current_version
    record_new_version
    if [ "$1" != "skip" ]; then
        reserve_last

        mkdir -p /opt/platform/current
        cp -fr "$current_dir"/* /opt/platform/current/
        cd /opt/platform/current
    fi

    # 全局变量定义
    start_time=`TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S"`
    upgrade_date=`TZ=UTC-8 date +"%Y%m%d-%H%M%S"`
    # 获取当前文件路径
    current_dir="/opt/platform/current"
    upgrade_log_file="/var/log/${upgrade_date}_upgrade.log"

    log "Start Upgrade: ${start_time}"
    log "--------------------------start upgrade------------------------------"

    upgrade_dir="${current_dir}/upgrade"

    # 如果离线镜像不存在，说明是在线安装，需要登录在线仓库
    if [ ! -f "$current_dir/asec_services_imgs.tar.gz" ]; then
      log "start docker login"
      docker_login
    fi

    #shell_upgrade 已移除，暂时不需要执行。减少了部署依赖
    data_upgrade
    service_upgrade
    install_spa_ebpf_service
    # 清理旧版docker 镜像
    docker image prune -a -f
    # 升级结束
    end_time=`TZ=UTC-8 date +"%Y-%m-%d %H:%M:%S"`
    log "--------------------------complete upgrade------------------------------"
    log "Upgrade Complete: ${end_time}"
}
upgrade "$1"