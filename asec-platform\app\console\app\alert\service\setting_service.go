package service

import (
	"asdsec.com/asec/platform/app/console/app/alert/model"
	"asdsec.com/asec/platform/app/console/app/alert/repository"
	"context"
	"sync"
)

var SettingInstance SettingService

var SettingInit sync.Once

type SettingDB struct {
	db repository.SettingRepository
}

func (s *SettingDB) GetSettingByName(ctx context.Context, tenantId uint64, eventName string) (model.AlertSetting, error) {
	return s.db.GetSettingByName(ctx, tenantId, eventName)
}

func (s *SettingDB) GetAlertSetting(ctx context.Context, tenantId uint64) ([]model.AlertSetting, error) {
	return s.db.GetSetting(ctx, tenantId)
}

func (s *SettingDB) SaveAlertSetting(ctx context.Context, SettingId uint64, setting map[string]interface{}) error {
	return s.db.UpdateSetting(ctx, SettingId, setting)
}

func GetSettingService() SettingService {
	SettingInit.Do(func() {
		SettingInstance = &SettingDB{db: repository.NewSettingRepository()}
	})
	return SettingInstance
}

type SettingService interface {
	SaveAlertSetting(ctx context.Context, SettingId uint64, setting map[string]interface{}) error
	GetAlertSetting(ctx context.Context, tenantId uint64) ([]model.AlertSetting, error)
	GetSettingByName(ctx context.Context, tenantId uint64, eventName string) (model.AlertSetting, error)
}
