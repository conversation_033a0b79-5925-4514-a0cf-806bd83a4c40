package virtualip_log

import (
	"context"
	"strconv"
	"sync"

	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// ========== 虚拟IP分配事件相关结构体 ==========

type VirtualIPAllocationEvent struct {
	Action       string
	UserId       string
	PoolName     string
	VirtualIp    string
	Username     string
	ClientId     string
	ClientName   string
	Iss          string
	SrcIp        string
	SrcPort      uint32
	Status       int32
	ErrorMessage string
	Timestamp    *timestamppb.Timestamp // 当前事件时间戳
	AllocatedAt  int64                  // 真实的分配时间
	ExpiresAt    int64                  // 过期时间
	LastUsedAt   int64                  // 最后使用时间
}

type TrafficStatsEntry struct {
	UserId          string
	VirtualIp       string
	PoolName        string
	UpstreamBytes   int64
	DownstreamBytes int64
	SessionDuration int64
	Timestamp       *timestamppb.Timestamp
}

// ========== 缓冲区管理 ==========

var (
	// 事件缓冲区
	mu     = &sync.Mutex{}
	buf    = [2048]*VirtualIPAllocationEvent{} // 增大缓冲区
	offset = 0
)

// WriteVirtualIPEvent 写入虚拟IP事件到缓冲区
func WriteVirtualIPEvent(event *VirtualIPAllocationEvent) {
	mu.Lock()
	if offset < len(buf) {
		buf[offset] = event
		offset++
	}
	mu.Unlock()
}

// ReadVirtualIPEvents 读取缓冲区中的事件
func ReadVirtualIPEvents() (ret []*VirtualIPAllocationEvent) {
	mu.Lock()
	ret = make([]*VirtualIPAllocationEvent, offset)
	for i := 0; i < offset; i++ {
		ret[i] = buf[i]
	}
	offset = 0
	mu.Unlock()
	return
}

// ========== 对象池管理 ==========

var (
	Pool = sync.Pool{
		New: func() interface{} {
			return &VirtualIPAllocationEvent{}
		},
	}
)

func ClearVirtualIPEventsPool(events []*VirtualIPAllocationEvent) {
	for _, event := range events {
		// 手动释放值
		event.Action = ""
		event.UserId = ""
		event.PoolName = ""
		event.VirtualIp = ""
		event.Username = ""
		event.ClientId = ""
		event.ClientName = ""
		event.Iss = ""
		event.SrcIp = ""
		event.SrcPort = 0
		event.Status = 0
		event.ErrorMessage = ""
		event.Timestamp = nil
		event.AllocatedAt = 0
		event.ExpiresAt = 0
		event.LastUsedAt = 0
		Pool.Put(event)
	}
}

func GetVirtualIPAllocationEventPool() *VirtualIPAllocationEvent {
	return Pool.Get().(*VirtualIPAllocationEvent)
}

func ReleaseVirtualIPAllocationEvent(event *VirtualIPAllocationEvent) {
	if event == nil {
		return
	}

	// 清理事件数据
	event.Action = ""
	event.UserId = ""
	event.PoolName = ""
	event.VirtualIp = ""
	event.Username = ""
	event.ClientId = ""
	event.ClientName = ""
	event.Iss = ""
	event.SrcIp = ""
	event.SrcPort = 0
	event.Status = 0
	event.ErrorMessage = ""
	event.Timestamp = nil
	event.AllocatedAt = 0
	event.ExpiresAt = 0
	event.LastUsedAt = 0

	// 放回对象池
	Pool.Put(event)
}

// ========== 事件收集接口 ==========

// CollectVirtualIPAllocation 收集虚拟IP分配事件
func CollectVirtualIPAllocation(event *VirtualIPAllocationEvent) {
	defer func() {
		if err := recover(); err != nil {
			global.Logger.Sugar().Errorf("CollectVirtualIPAllocation err:%v", err)
		}
	}()

	if event == nil {
		return
	}

	// 所有事件都写入缓冲区，由定时上报处理
	WriteVirtualIPEvent(event)
	global.Logger.Sugar().Debugf("收集虚拟IP事件: Action=%s, IP=%s, UserId=%s, ClientId=%s, PoolName=%s, AllocatedAt=%d, ExpiresAt=%d, LastUsedAt=%d (缓冲区大小: %d)",
		event.Action, event.VirtualIp, event.UserId, event.ClientId, event.PoolName, event.AllocatedAt, event.ExpiresAt, event.LastUsedAt, offset)
}

// CollectTrafficStats 收集流量统计
func CollectTrafficStats(entry *TrafficStatsEntry) {
	// 暂时记录日志，后续可以实现类似的缓冲机制
	global.Logger.Sugar().Debugf("收集流量统计: user=%s, ip=%s, up=%d, down=%d",
		entry.UserId, entry.VirtualIp, entry.UpstreamBytes, entry.DownstreamBytes)
}

// ========== 使用 common.Send 框架的上报服务 ==========

// StartSendVirtualIPEvents 启动虚拟IP事件上报服务（使用 common.Send 框架）
func StartSendVirtualIPEvents(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   ReportVirtualIPEventsPlat,
		RunType:      common.SimpleSend,
		WaitSecond:   10,
		RandomOffset: 2,
	}
	common.Send(param)
}

// ReportVirtualIPEventsPlat 上报虚拟IP事件到平台
func ReportVirtualIPEventsPlat(conn *grpc.ClientConn, ctx context.Context) error {
	// 读取缓冲区中的事件
	events := ReadVirtualIPEvents()
	if len(events) == 0 {
		return nil
	}

	// global.Logger.Sugar().Debugf("上报 %d 个虚拟IP事件到平台", len(events))

	// 使用复用的连接
	client := pb.NewAppClient(conn)
	// 转换为平台格式
	allocations := make([]*pb.IPAllocation, 0, len(events))
	for _, event := range events {
		allocation := &pb.IPAllocation{
			IpAddress: event.VirtualIp,
			UserId:    event.UserId,
			DeviceId:  event.ClientId,
			PoolName:  event.PoolName,
			// 直接使用事件中的真实时间字段
			AllocatedAt: event.AllocatedAt,
			ExpiresAt:   event.ExpiresAt,
			Status:      getStatusFromAction(event.Action, event.Status),
		}

		// 根据动作类型设置最后使用时间
		switch event.Action {
		case "allocate":
			allocation.LastUsedAt = 0
		default:
			allocation.LastUsedAt = event.LastUsedAt
		}

		allocations = append(allocations, allocation)
	}

	req := &pb.ReportVirtualIPAllocationsReq{
		ApplianceId: strconv.FormatUint(global.ApplianceID, 10),
		Allocations: allocations,
	}

	_, err := client.ReportVirtualIPAllocations(ctx, req)
	if err != nil {
		global.Logger.Sugar().Errorf("上报虚拟IP事件失败: %v", err)
		// 失败时重新缓存事件
		for _, event := range events {
			WriteVirtualIPEvent(event)
		}
		return err
	}

	global.Logger.Sugar().Debugf("成功上报 %d 个虚拟IP事件到平台", len(events))

	// 清理对象池
	ClearVirtualIPEventsPool(events)

	return nil
}

// ========== 辅助函数 ==========

func getStatusFromAction(action string, status int32) string {
	if status != 0 {
		return "failed"
	}
	switch action {
	case "allocate", "usage_update", "renew":
		return "active"
	case "release":
		return "released"
	case "expire":
		return "expired"
	case "init":
		return "init"
	default:
		return "active"
	}
}
