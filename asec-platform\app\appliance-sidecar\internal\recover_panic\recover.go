package recover_panic

import (
	"context"
	"sync"
	"time"

	"google.golang.org/grpc"

	"asdsec.com/asec/platform/pkg/utils"
	"go.uber.org/zap"
)

type StartHeartbeatFunc func(ctx context.Context, pids []int32, wg *sync.WaitGroup)
type HeartbeatSend func(ctx context.Context, cli *grpc.ClientConn, pids []int32, wg *sync.WaitGroup)
type AccessLogSend func(ctx context.Context, wg *sync.WaitGroup, conn *grpc.ClientConn)

func RecoverAccessLogSend(logger *zap.Logger, f AccessLogSend, ctx context.Context, wg *sync.WaitGroup, conn *grpc.ClientConn) {
	if err := recover(); err != nil {
		utils.WritePanicLog(err, logger)
		if conn != nil {
			_ = conn.Close()
		}
	}
}

func RecoverStartHeartbeat(logger *zap.Logger, f StartHeartbeatFunc, ctx context.Context, pids []int32, wg *sync.WaitGroup) {
	if err := recover(); err != nil {
		utils.WritePanicLog(err, logger)
		defer RecoverStartHeartbeat(logger, f, ctx, pids, wg)
		time.Sleep(time.Second * utils.BaseSleepTime)
		// re do function
		f(ctx, pids, wg)
	}
}

func RecoverHeartbeatSend(logger *zap.Logger, f HeartbeatSend, ctx context.Context, cli *grpc.ClientConn, pids []int32, wg *sync.WaitGroup) {
	if err := recover(); err != nil {
		utils.WritePanicLog(err, logger)
		if cli != nil {
			_ = cli.Close()
		}
	}
}
