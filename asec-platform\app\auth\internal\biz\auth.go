package biz

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	crand "crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/url"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/pquerna/otp"

	"github.com/go-redis/redis/v8"
	"github.com/golang-jwt/jwt/v4"
	"google.golang.org/grpc/metadata"

	"asdsec.com/asec/platform/api/auth/v1/admin"
	"asdsec.com/asec/platform/api/auth/v1/auth"
	"asdsec.com/asec/platform/app/auth/internal/idp/ad"
	"asdsec.com/asec/platform/app/auth/internal/idp/bamboocloud"
	"asdsec.com/asec/platform/app/auth/internal/idp/cas"
	"asdsec.com/asec/platform/app/auth/internal/idp/dingtalk"
	"asdsec.com/asec/platform/app/auth/internal/idp/email"
	"asdsec.com/asec/platform/app/auth/internal/idp/feishu"
	"asdsec.com/asec/platform/app/auth/internal/idp/oauth2"
	provider "asdsec.com/asec/platform/app/auth/internal/idp/qiyewx"
	"asdsec.com/asec/platform/app/auth/internal/idp/webauth"
	"asdsec.com/asec/platform/app/auth/internal/utils"
	ipCommon "asdsec.com/asec/platform/pkg/ip2region/common"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/strategy_model"
	gosmssender "github.com/casdoor/go-sms-sender"
	kratosErrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/google/uuid"
	"github.com/pquerna/otp/totp"
	"gorm.io/gorm"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/pkg/utils/encrypt"
	"asdsec.com/asec/platform/pkg/utils/jwt_util"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/common"

	"asdsec.com/asec/platform/app/auth/internal/dto"

	"github.com/go-kratos/kratos/v2/log"
)

const activationCodePrefix = "activation_code:"
const userActiveCodePrefix = "user_active_code:"

// ActivationCodeKey 为激活码生成带前缀的redis key
func ActivationCodeKey(code string) string {
	return activationCodePrefix + code
}

// UserActiveCodeKey 为用户ID生成反向映射的redis key
func UserActiveCodeKey(userID string) string {
	return userActiveCodePrefix + userID
}

type AuthRepo interface {
	GenerateAuthCode(ctx context.Context, user model.TbUserEntity) (code string, err error)
	GetUserCache(ctx context.Context, key string) (model.TbUserEntity, error)
	GetEcdsaPrivateKey(ctx context.Context, corpId string) (string, error)
	GetEcdsaPublicKeys(ctx context.Context) ([]dto.PublicKey, error)
	GetEcdsaPublicKeyByCorpId(ctx context.Context, corpId string) (dto.PublicKey, error)
	GetAdminEcdsaPublicKey(ctx context.Context) (dto.PublicKey, error)

	SetNX(ctx context.Context, key string, val interface{}, ttl time.Duration) error
	GetStringV(ctx context.Context, key string) (string, error)
	ExpireNX(ctx context.Context, key string, ttl time.Duration) (bool, error)
	Expire(ctx context.Context, key string, ttl time.Duration) (bool, error)

	GetSet(c context.Context, key string, value string) error
	SAdd(c context.Context, key string, value string) error
	SMembers(c context.Context, key string) ([]string, error)
	SPop(c context.Context, key string) error
	Set(c context.Context, key string, value string, expireTime time.Duration) error
	Get(c context.Context, key string) (string, error)
	SRem(c context.Context, key string, item []string) error
	Del(c context.Context, key string) error
	LPop(c context.Context, key string) error
	RPush(c context.Context, key string, value string) error
	LRem(c context.Context, key string, count int64, value string) error
	LLen(c context.Context, key string) (int64, error)
	LRange(c context.Context, key string, startIndex, endIndex int64) ([]string, error)
	QueryUserByName(ctx context.Context, corpId, name string) (*model.TbUserEntity, error)
	CreateLoginLog(ctx context.Context, param model.TbUserLoginLog) error
	GetSpecialConfig(key string) string
	QuerySecondaryAuth(ctx context.Context, policies []string) ([]*model.TbIdentityProvider, error)
	CheckUserIdExist(ctx context.Context, userId string, userType string) (bool, error)
	GetGroupByAdServerAddr(ctx context.Context, serverAddr, entry, username string) (model.TbUserEntity, error)
	GetLicenseId(ctx context.Context) (string, error)

	// 密码锁定相关方法
	SetPasswordFailureInfo(ctx context.Context, userID string, info dto.PasswordFailureInfo, ttl time.Duration) error
	GetPasswordFailureInfo(ctx context.Context, userID string) (*dto.PasswordFailureInfo, error)
	ClearPasswordFailureInfo(ctx context.Context, userID string) error
	SetAccountLockStatus(ctx context.Context, userID string, lockInfo dto.AccountLockInfo, ttl time.Duration) error
	GetAccountLockStatus(ctx context.Context, userID string) (*dto.AccountLockInfo, error)
	ClearAccountLockStatus(ctx context.Context, userID string) error
	CheckAccountLockStatus(ctx context.Context, userID string) (*dto.AccountLockInfo, error)

	// IP锁定相关方法
	SetIPFailureInfo(ctx context.Context, ip string, info dto.IPFailureInfo, ttl time.Duration) error
	GetIPFailureInfo(ctx context.Context, ip string) (*dto.IPFailureInfo, error)
	ClearIPFailureInfo(ctx context.Context, ip string) error
	SetIPLockStatus(ctx context.Context, ip string, lockInfo dto.IPLockInfo, ttl time.Duration) error
	GetIPLockStatus(ctx context.Context, ip string) (*dto.IPLockInfo, error)
	ClearIPLockStatus(ctx context.Context, ip string) error
	CheckIPLockStatus(ctx context.Context, ip string) (*dto.IPLockInfo, error)

	// 操作日志相关方法
	CreateAdminOperationLog(ctx context.Context, log modelTable.Oprlog) error
}

// PasswordAuthProvider 定义密码方式身份提供商接口
type PasswordAuthProvider interface {
	HandleAuthorization(username, password string, ctx context.Context) (*dto.ExternalUser, error)
}

type AuthUsecase struct {
	repo              AuthRepo
	userRepo          UserRepo
	userSource        UserSourceRepo
	idpRepo           IdpRepo
	ugRepo            UserGroupRepo
	corpRepo          CorpRepo
	apRepo            AuthPolicyRepo
	commonRepo        CommonRepo
	accountPolicyRepo AuthAccountPolicyRepo
	blacklistRepo     JWTBlacklistRepo       // 新增：JWT黑名单仓库
	trackerRepo       SessionTrackerRepo     // 新增：会话跟踪仓库
	clientLimitUc     *ClientLimitUsecase    // 新增：客户端限制业务逻辑
	uaParser          *utils.UserAgentParser // 新增：UserAgent解析器
	log               *log.Helper
}

func NewAuthUsecase(repo AuthRepo, idpRepo IdpRepo, userRepo UserRepo, ugGroup UserGroupRepo, userSource UserSourceRepo, corpRepo CorpRepo, apRepo AuthPolicyRepo, commonRepo CommonRepo, accountPolicyRepo AuthAccountPolicyRepo, blacklistRepo JWTBlacklistRepo, trackerRepo SessionTrackerRepo, clientLimitUc *ClientLimitUsecase, logger log.Logger) *AuthUsecase {
	return &AuthUsecase{
		repo:              repo,
		idpRepo:           idpRepo,
		userRepo:          userRepo,
		ugRepo:            ugGroup,
		userSource:        userSource,
		corpRepo:          corpRepo,
		apRepo:            apRepo,
		commonRepo:        commonRepo,
		accountPolicyRepo: accountPolicyRepo,
		blacklistRepo:     blacklistRepo,
		trackerRepo:       trackerRepo,
		clientLimitUc:     clientLimitUc,
		log:               log.NewHelper(logger),
	}
}

const (
	loginParamError         = "login param error"
	loginCredError          = "account or password error"
	redisNotFound           = "get data from redis empty"
	authorizationKey string = dto.AuthorizationKey
	bearerWord       string = dto.TokenTypeBear
)

var (
	paramError   = pb.ErrorTokenParseFailed("param error")
	tokenExpired = pb.ErrorTokenExpire("token expire")
	tokenInvalid = pb.ErrorTokenInvalid("token invalid")
)

func (a AuthUsecase) ParseJwtToken(ctx context.Context, tokenType dto.TokenType, jwtToken string) (*jwt_util.TokenClaims, error) {
	parser := jwt_util.NewJwtParse(jwt_util.Ecdsa256)
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		log.Errorf("GetCorpId failed. err=%v", err)
		return nil, paramError
	}
	publicKey, err := a.GetPublicKeyOfCorp(ctx, corpId)
	if err != nil {
		log.Errorf("GetPublicKey failed. err=%v", err)
		return nil, paramError
	}
	claims, err := parser.ParseToken(jwtToken, publicKey)
	if err != nil {
		ve, ok := err.(*jwt.ValidationError)
		if !ok {
			log.Errorf("err type error. err=%v, token=%v, type=%v", err, jwtToken, tokenType)
			return nil, paramError
		}
		if ve.Errors&jwt.ValidationErrorMalformed != 0 {
			log.Warnf("token malformed. token=%v, type=%v", jwtToken, tokenType)
			return nil, tokenInvalid
		}
		if ve.Errors&(jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
			//log.Warnf("token expired. token=%v, type=%v", jwtToken, tokenType)
			return nil, tokenExpired
		}
		log.Errorf("ParseToken err. err=%v, token=%v, type=%v", err, jwtToken, tokenType)
		return nil, paramError
	}
	if tokenType == dto.AccessTokenTyp {
		if a.CheckTokenFromRedis(ctx, jwtToken) {
			log.Errorf("redis token invalid")
			return nil, tokenInvalid
		}
	}

	if claims.Typ != string(tokenType) {
		log.Errorf("jwt typ=%v not support", claims.Typ)
		return nil, tokenInvalid
	}
	return claims, nil
}

func (a AuthUsecase) CheckTokenFromRedis(ctx context.Context, token string) bool {
	ret, err := a.CheckTokenOfRedisByType(ctx, token, dto.UserAuthType)
	if err != nil {
		log.Errorf("CheckTokenFromRedis failed. err=%v", err)
		return false
	}
	return ret
}

func (a AuthUsecase) GetTokeByCode(ctx context.Context, code string) (string, error) {
	token, err := a.repo.Get(ctx, code)
	if err != nil {
		a.log.Errorf("get token from redis failed. err=%v", err)
		return "", err
	}
	err = a.repo.Del(ctx, code)
	if err != nil && !errors.Is(err, redis.Nil) {
		a.log.Errorf("del token from redis failed. err=%v", err)
		return "", err
	}
	return token, nil
}

func (a AuthUsecase) RedirectVerify(ctx context.Context) (code string, err error) {
	userId, err := common.GetUserId(ctx)
	if err != nil {
		a.log.Errorf("get userId failed from header of token. err=%v", err)
		return "", err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		a.log.Errorf("get corpId failed. err=%v", err)
		return "", err
	}
	userEntity, err := a.userRepo.QueryUserEntity(ctx, corpId, userId)
	if err != nil {
		a.log.Errorf("get userId failed from header of token. err=%v", err)
		return "", err
	}
	code, err = a.repo.GenerateAuthCode(ctx, *userEntity)
	if err != nil {
		a.log.Errorf("get auth code failed. err=%v", err)
		return "", err
	}
	//设置redis缓存 code:token
	token, err := common.GetToken(ctx)
	if err != nil {
		a.log.Errorf("get user token failed from header. err=%v", err)
		return "", err
	}
	err = a.repo.Set(ctx, code, token, time.Second*60)
	if err != nil {
		a.log.Errorf("set redis key failed. err=%v", err)
		return "", err
	}

	return code, nil
}

func (a AuthUsecase) Login(ctx context.Context, param dto.LoginForm) (any, dto.LoginAuthInfo, error) {
	// 获取认证所需基础组件
	idp, err := a.idpRepo.GetIDP(ctx, param.CorpId, param.IdpId)
	if err != nil {
		a.log.Errorf("GetIDP failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	if param.Encryption == "rsa" {
		// rsa解密密码
		param.Password, err = encrypt.RsaDecrypt(param.Password)
	}
	if err != nil {
		a.log.Errorf("Decrypt Password failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	var authBasic dto.AuthBasic
	switch dto.IDPType(idp.Type) {
	case dto.IDPTypeWeb, dto.IDPTypeEmail, dto.IDPTypeMsad, dto.IDPTypeLdap:
		authBasic, err = a.getThirdAuthBasic(ctx, param, idp)
	default:
		authBasic, err = a.getAuthBasic(ctx, param, idp)
	}
	if err != nil {
		a.log.Errorf("getAuthBasic failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	// 进行主认证服务器认证
	if err = a.mainIdpAuth(ctx, authBasic, param); err != nil {
		a.log.Errorf("mainIdpAuth failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}

	if authBasic.User.LockStatus {
		return nil, dto.LoginAuthInfo{}, pb.ErrorAuthUserLock("您的账户已被锁定，请联系管理员处理！")
	}
	// 删除应用二次认证的sms_idp
	err = a.DelSecondarySmsIdp(ctx, authBasic.User.ID)
	if err != nil {
		a.log.Errorf("del secondary failed. err=%v", err)
	}
	userName := authBasic.User.Name
	if authBasic.User.DisplayName != "" {
		userName = authBasic.User.DisplayName
	}
	// 判断是否存在二次认证
	secondary, err := a.getSecondaryAuth(ctx, authBasic.Policy, authBasic.User)
	if err != nil {
		a.log.Errorf("getSecondaryAuth failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}

	if len(secondary) != 0 {
		var idpAttr dto.IdpAttr
		secondaryCategory, secondaryType := a.determineSecondaryType(secondary)
		if secondaryType == "totp" {
			attrs, err := a.idpRepo.GetIDPAttr(ctx, secondary[0].ID) //其实这里就是能绑定一个辅助认证方式
			if err != nil {
				a.log.Errorf("secondary attr get failed. err=%v", err)
				return nil, dto.LoginAuthInfo{}, err
			}
			idpAttr = dto.KVsToIdpAttr(attrs)
		}
		return secondary, dto.LoginAuthInfo{
			UserId:            authBasic.User.ID,
			Name:              userName,
			Account:           authBasic.User.Name,
			NeedSecondary:     true,
			Phone:             authBasic.User.Phone,
			Email:             authBasic.User.Email,
			SecondaryCategory: secondaryCategory,
			SecondaryType:     secondaryType,
			IdpAttr:           idpAttr,
			CurrentSecret:     authBasic.User.CurrentSecret,
		}, nil
	}
	// 根据当前主认证的idp类型，判断是否存在增强认证的策略,取最长链路
	authEnhancement, err := a.getAuthEnhancement(ctx, param.IdpId, authBasic.User.RootGroupID, param.CorpId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		a.log.Errorf("getAuthEnhancement failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	// 存在增强认证，则验证条件是否符合，符合则再次校验增强认证
	if authEnhancement.IdpId != "" {
		authEnhancementInfo := a.checkFactor(ctx, authEnhancement.Factor, authBasic.User.ID)
		if authEnhancementInfo.IsRegionEnhancement && authEnhancementInfo.IsTimeEnhancement {
			// 返回增强认证
			idp, err := a.idpRepo.GetIDP(ctx, param.CorpId, authEnhancement.IdpId)
			if err != nil {
				a.log.Errorf("getAuthEnhancementIdp Failed. err=%v", err)
				return nil, dto.LoginAuthInfo{}, err
			}
			enhancementIdps := []*model.TbIdentityProvider{idp}
			secondaryCategory, secondaryType := a.determineSecondaryType(enhancementIdps)
			return []*model.TbIdentityProvider{idp}, dto.LoginAuthInfo{
				UserId:              authBasic.User.ID,
				Name:                userName,
				Account:             authBasic.User.Name,
				NeedSecondary:       true,
				Phone:               authBasic.User.Phone,
				Email:               authBasic.User.Email,
				SecondaryCategory:   secondaryCategory,
				SecondaryType:       secondaryType,
				AuthEnhancementInfo: authEnhancementInfo}, nil
		}
	}
	// 检查SPA功能是否开启
	spaEnabled, err := a.isSPAEnabled(ctx, authBasic.User.CorpID)
	if err != nil {
		a.log.Errorf("check SPA enabled failed. err=%v", err)
		// 检查失败时，为了安全起见，忽略激活码验证但不阻止登录
		a.log.Warnf("SPA status check failed, ignoring activation code for user %s", authBasic.User.ID)
	} else if spaEnabled && param.ActivationCode != "" {
		// SPA功能开启且提供了激活码，进行激活码验证
		activationCode, err := a.repo.Get(ctx, "user_active_code:"+authBasic.User.ID)
		if err != nil {
			a.log.Errorf("get user cache failed. err=%v", err)
			return nil, dto.LoginAuthInfo{}, pb.ErrorSecurityCodeError("")
		}
		if activationCode != param.ActivationCode {
			return nil, dto.LoginAuthInfo{}, pb.ErrorSecurityCodeError("")
		}

		// 验证通过, 在事务中删除激活码和反向映射关系
		activationKey := ActivationCodeKey(param.ActivationCode)
		userActiveCodeKey := UserActiveCodeKey(authBasic.User.ID)
		err = a.repo.Del(ctx, activationKey)
		if err != nil {
			a.log.Errorf("del activation code failed. err=%v", err)
		}
		err = a.repo.Del(ctx, userActiveCodeKey)
		if err != nil {
			a.log.Errorf("del user active code failed. err=%v", err)
		}
	} else if !spaEnabled && param.ActivationCode != "" {
		// SPA功能未开启但提供了激活码，忽略激活码验证
		a.log.Infof("SPA功能未开启，忽略激活码验证 for user %s", authBasic.User.ID)
	}
	// 授权
	authzResp, err := a.authz(ctx, authBasic.User, param.AuthBasicForm)
	if err != nil {
		a.log.Errorf("authz failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	return authzResp, dto.LoginAuthInfo{
		UserId:        authBasic.User.ID,
		Name:          userName,
		Account:       authBasic.User.Name,
		NeedSecondary: false,
		Phone:         authBasic.User.Phone,
		Email:         authBasic.User.Email,
	}, nil
}

// isSPAEnabled 检查指定企业是否启用了SPA功能
func (a AuthUsecase) isSPAEnabled(ctx context.Context, corpId string) (bool, error) {
	// 注意：当前SPA配置是全局的（id=1），不区分企业
	// 如果将来需要按企业区分，可以修改这里的逻辑

	a.log.Debugf("checking SPA enabled status for corp %s", corpId)

	// 方案1：通过Redis缓存快速查询（推荐）
	// 可以在SPA配置更新时同步更新Redis缓存
	cacheKey := "spa_config:enable_spa"
	enabledStr, err := a.repo.Get(ctx, cacheKey)
	if err == nil {
		// 缓存命中，直接返回
		return enabledStr == "true", nil
	}

	// 方案2：如果缓存未命中，返回默认值
	// 在实际部署中，可以通过以下方式实现：
	// 1. 直接查询数据库 tb_spa_config 表（需要添加数据库连接）
	// 2. 调用console服务的SPA配置接口（需要HTTP客户端）
	// 3. 通过共享的配置服务获取（需要配置服务）

	// 临时实现：默认返回false，表示SPA功能关闭
	// 建议在SPA配置更新时，同步更新Redis缓存：
	// redis.Set("spa_config:enable_spa", "true/false", 0)
	a.log.Debugf("SPA config cache miss, using default value false for corp %s", corpId)
	return false, nil
}

func (a AuthUsecase) UpdateTotpKey(ctx context.Context, userId string, totpKey string) {
	updateInfo := model.TbUserEntity{
		ActivationSecret: totpKey,
	}
	err := a.userRepo.UpdateUserRow(ctx, userId, updateInfo)
	if err != nil {
		return
	}
}

func (a AuthUsecase) getAuthEnhancementInfo() {

}

func (a AuthUsecase) checkFactor(ctx context.Context, factor []dto.Factor, userId string) dto.AuthEnhancementInfo {
	var timeFacIds []string
	var regionFacIds []string
	var regionOperator string
	var timeOperator string
	AbnormalOperator := false
	res := dto.AuthEnhancementInfo{
		IsRegionEnhancement: true,
		IsTimeEnhancement:   true,
	}
	for _, v := range factor {
		if v.Type == dto.TimeFactor {
			timeFacIds = append(timeFacIds, v.Ids...)
			timeOperator = v.Operator
		}
		if v.Type == dto.RegionFactor {
			regionFacIds = append(regionFacIds, v.Ids...)
			regionOperator = v.Operator
			for _, regionType := range v.RegionType {
				if regionType == strconv.Itoa(dto.AbnormalLogin) {
					AbnormalOperator = true
				}
			}
		}
	}
	if len(timeFacIds) > 0 {
		timeFac, err := a.commonRepo.GetFactorTime(ctx, timeFacIds)
		if err != nil {
			a.log.Errorf("GetTimeFactor Failed. err = %v", err)
			return dto.AuthEnhancementInfo{}
		}
		res.IsTimeEnhancement, res.Time = a.checkTimeFactor(timeFac, timeOperator)
	}

	if len(regionFacIds) > 0 {
		regionFac, err := a.commonRepo.GetFactorRegion(ctx, regionFacIds)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			a.log.Errorf("GetRegionFactor Failed. err = %v", err)
			return dto.AuthEnhancementInfo{}
		}
		ip := common.GetClientHost(ctx)
		ipList := strings.Split(ip, ",")
		if len(ipList) > 1 {
			ip = ipList[0]
		}
		if AbnormalOperator {
			regionFac = append(regionFac, &strategy_model.UebaStrategyCondition{ID: dto.AbnormalLogin})
		}
		res.IsRegionEnhancement, res.Region = a.checkRegionFactor(ctx, regionFac, regionOperator, ip, userId)
	}
	return res
}

func (a AuthUsecase) checkRegionFactor(ctx context.Context, data []*strategy_model.UebaStrategyCondition, regionOpt, ip, userId string) (bool, string) {
	loginLog, err := a.commonRepo.GetLastLoginIp(ctx, userId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		a.log.Errorf("GetLastLoginLog Failed.user-id=%s, err=%v", userId, err)
		return false, ""
	}
	ipPrivateIp, err := ipCommon.IsPrivate(ip)
	if err != nil {
		a.log.Errorf("ParseIp Failed.ip=%s, err=%v", ip, err)
	}

	if ipPrivateIp {
		return false, ""
	}
	ip2Region := a.commonRepo.GetIp2Region()
	nowRegion, err := ip2Region.Search(ip)

	if err != nil {
		a.log.Errorf("ParseNowIp2Region Failed.ip=%s, err=%v", ip, err)
	}
	abnormalLoginHas := false
	if loginLog.IPAddress != "" {
		isPrivateIp, err := ipCommon.IsPrivate(loginLog.IPAddress)
		if err != nil {
			a.log.Errorf("ParseOldIp Failed.ip=%s, err=%v", loginLog.IPAddress, err)
		}
		if !isPrivateIp {
			oldIpList := strings.Split(loginLog.IPAddress, ",")
			oldRegion, err := ip2Region.Search(oldIpList[0])
			if err != nil {
				a.log.Errorf("ParseOldIp2Region Failed.ip=%s, err=%v", loginLog.IPAddress, err)
			}
			if oldRegion.Province != nowRegion.Province {
				abnormalLoginHas = true
			}
		}
	}
	for _, v := range data {
		// 异地登陆判断
		if v.ID == dto.AbnormalLogin {
			if abnormalLoginHas {
				return true, nowRegion.CountryName
			}
		} else {
			// 指定地区判断
			if regionOpt == dto.NotInRegionOpt && strings.Contains(nowRegion.Province, v.ConditionName) {
				return false, ""
			} else {
				if strings.Contains(nowRegion.Province, v.ConditionName) {
					return true, nowRegion.Province
				}
			}
		}
	}
	if regionOpt == dto.NotInRegionOpt {
		return true, nowRegion.Province
	}
	return false, ""
}

func (a AuthUsecase) checkTimeFactor(data []*strategy_model.FactorTime, timeOperator string) (bool, string) {
	nHour := time.Now().Hour()
	nMinute := time.Now().Minute()
	day := time.Now().Weekday()

	for _, v := range data {
		hitTime := false
		curTime := time.Now().Format("2006-01-02 15:04:05")
		if v.IntervalType == dto.EveryDayIntervalType {
			if v.AllDayEnable {
				hitTime = true
			} else {
				if a.compareStr(v.DailyStartTime, v.DailyEndTime, nHour, nMinute) {
					hitTime = true
				}
			}
		} else {
			dayEnable := false
			switch day {
			case time.Sunday:
				dayEnable = a.compareWeekDay(v.DailyStartTime, v.DailyEndTime, v.Sunday, v.AllDayEnable, nHour, nMinute)
				break
			case time.Monday:
				dayEnable = a.compareWeekDay(v.DailyStartTime, v.DailyEndTime, v.Monday, v.AllDayEnable, nHour, nMinute)
				break
			case time.Tuesday:
				dayEnable = a.compareWeekDay(v.DailyStartTime, v.DailyEndTime, v.Tuesday, v.AllDayEnable, nHour, nMinute)
				break
			case time.Wednesday:
				dayEnable = a.compareWeekDay(v.DailyStartTime, v.DailyEndTime, v.Wednesday, v.AllDayEnable, nHour, nMinute)
				break
			case time.Thursday:
				dayEnable = a.compareWeekDay(v.DailyStartTime, v.DailyEndTime, v.Thursday, v.AllDayEnable, nHour, nMinute)
				break
			case time.Friday:
				dayEnable = a.compareWeekDay(v.DailyStartTime, v.DailyEndTime, v.Friday, v.AllDayEnable, nHour, nMinute)
				break
			case time.Saturday:
				dayEnable = a.compareWeekDay(v.DailyStartTime, v.DailyEndTime, v.Saturday, v.AllDayEnable, nHour, nMinute)
				break
			}
			if dayEnable {
				hitTime = true
			}
		}
		if timeOperator == dto.InRegionOpt && hitTime {
			return hitTime, curTime
		}
		if timeOperator == dto.NotInRegionOpt && hitTime {
			return false, ""
		}
	}
	if timeOperator == dto.NotInRegionOpt {
		return true, time.Now().Format("2006-01-02 15:04:05")
	}
	return false, ""
}

func (a AuthUsecase) compareWeekDay(startTStr, endTStr string, dayEnable bool, allDay bool, nHour, nMinute int) bool {
	if dayEnable {
		if allDay {
			return true
		} else {
			return a.compareStr(startTStr, endTStr, nHour, nMinute)
		}
	}
	return dayEnable
}

func (a AuthUsecase) compareStr(startStr, endStr string, nHour, nMinute int) bool {
	// start time format
	startTime, err := strconv.Atoi(strings.Replace(startStr, ":", "", -1))
	if err != nil {
		a.log.Errorf("ParseDailyStartTimeHour Failed. err = %v", err)
		return false
	}
	// end time format
	endTime, err := strconv.Atoi(strings.Replace(endStr, ":", "", -1))
	if err != nil {
		a.log.Errorf("ParseDailyEndTimeHour Failed. err = %v", err)
		return false
	}

	target := nHour*100 + nMinute
	// compare
	if target >= startTime && target <= endTime {
		return true
	}
	return false
}

func (a AuthUsecase) getAuthEnhancement(ctx context.Context, idpId string, rootGroupId, corpId string) (dto.AuthEnhancement, error) {
	authPolicy, err := a.apRepo.ListPolicyInRootGroup(ctx, corpId, rootGroupId, -1, 0)
	if err != nil {
		return dto.AuthEnhancement{}, err
	}
	for _, v := range authPolicy {
		var authEnhancement dto.AuthEnhancement
		if len(v.AuthEnhancement.Bytes) > 0 {
			err = json.Unmarshal(v.AuthEnhancement.Bytes, &authEnhancement)
			if err != nil {
				return dto.AuthEnhancement{}, err
			}
		}
		if authEnhancement.IdpId != "" {
			return authEnhancement, nil
		}
	}
	return dto.AuthEnhancement{}, err
}

func (a AuthUsecase) getAuthBasic(ctx context.Context, param dto.LoginForm, idp *model.TbIdentityProvider) (dto.AuthBasic, error) {
	// 获取main idp
	if idp.SourceID == dto.AssistIDPSourceID {
		a.log.Errorf("only handle main idp=%+v", idp)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	if !idp.Enable {
		return dto.AuthBasic{}, pb.ErrorDisableError("idp=%+v is disable", idp)
	}

	// 根据 idpId 查询绑定来源
	allSource, err := a.idpRepo.GetAllSourceOfBind(ctx, param.CorpId, param.IdpId)
	if err != nil {
		a.log.Errorf("GetAllSourceOfBind failed. err=%v", err)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	if len(allSource) != 1 {
		a.log.Errorf("idp bind source error. allSource=%v", err)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}

	// 查看对应来源下有没有该用户
	user, err := a.userRepo.QueryUserByIdInSource(ctx, param.CorpId, allSource[0].SourceID, param.UserName, param.UserId)
	if err != nil {
		if !pb.IsRecordNotFound(err) {
			a.log.Errorf("QueryUserByNameInSource failed. err=%v", err)
		}
		return dto.AuthBasic{}, pb.ErrorAccountOrPasswordError(loginCredError)
	}
	if err := a.isUserValidate(user); err != nil {
		return dto.AuthBasic{}, err
	}
	// 确认用户是否开启了对应的认证策略，取最新可用的那条
	policy, err := a.getAuthPolicyOfUser(ctx, param.CorpId, param.IdpId, user)
	if err != nil {
		a.log.Errorf("getAuthPolicyOfUser failed. err=%v", err)
		return dto.AuthBasic{}, err
	}
	// todo 获取assistIDP
	return dto.AuthBasic{
		Idp:    idp,
		User:   user,
		Policy: policy,
	}, nil
}

func (a AuthUsecase) WebAuthTest(ctx context.Context, param dto.LoginForm) (string, error) {
	idp, err := a.idpRepo.GetIDP(ctx, param.CorpId, param.IdpId)
	if err != nil {
		a.log.Errorf("GetIDP failed. err=%v", err)
		return "", err
	}

	// 获取第三方服务器属性
	attrs, err := a.idpRepo.GetIDPAttr(ctx, idp.ID)
	if err != nil {
		a.log.Errorf("GetIDPAttr failed. err=%v", err)
		return "", pb.ErrorLoginError(loginParamError)
	}

	idpAttrs := dto.KVsToIdpAttr(attrs)
	logger := log.With(log.GetLogger())
	authProvider, err := webauth.NewWebAuthProvider(
		idp.ID,
		idp.Name,
		idpAttrs.UserData,
		logger,
	)
	if err != nil {
		a.log.Errorf("webauth provider 创建失败. err=%v", err)
		return "", pb.ErrorLoginError(loginParamError)
	}
	executor := authProvider.GetExecutor()
	if executor != nil {
		// 重置日志
		executor.ClearLogs()
	}
	var logs []string
	logs = append(logs, "开始执行WebAuth处理流程，获取用户信息")
	userInfo, err := authProvider.HandleAuthorization(param.UserName, param.Password, ctx)

	// 获取执行器中的详细HTTP请求日志
	if executor != nil {
		httpLogs := executor.GetLogs()
		if len(httpLogs) > 0 {
			logs = append(logs, "---- WebAuth请求执行日志 ----")
			logs = append(logs, httpLogs...)
			logs = append(logs, "------------------------")
		}
	}
	if err != nil {
		logs = append(logs, fmt.Sprintf("webauth 处理回调失败. err=%v", err))
		return strings.Join(logs, "\n"), pb.ErrorAccountOrPasswordError(loginCredError)
	}

	a.log.Debugf("webauth 处理回调成功. userInfo=%+v", userInfo)
	return strings.Join(logs, "\n"), nil
}

func (a AuthUsecase) getThirdAuthBasic(ctx context.Context, param dto.LoginForm, idp *model.TbIdentityProvider) (dto.AuthBasic, error) {
	// 获取main idp
	if idp.SourceID == dto.AssistIDPSourceID {
		a.log.Errorf("only handle main idp=%+v", idp)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	if !idp.Enable {
		return dto.AuthBasic{}, pb.ErrorDisableError("idp=%+v is disable", idp)
	}

	// 根据 idpId 查询绑定来源
	allSource, err := a.idpRepo.GetAllSourceOfBind(ctx, param.CorpId, param.IdpId)
	if err != nil {
		a.log.Errorf("GetAllSourceOfBind failed. err=%v", err)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	if len(allSource) != 1 {
		a.log.Errorf("idp bind source error. allSource=%v", err)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	// 根据 idpId 查询绑定根目录，三方登陆只能绑定一个根目录
	groups, err := a.idpRepo.GetIDPBindGroup(ctx, param.CorpId, idp.ID)
	if err != nil {
		a.log.Errorf("GetIDPBindGroup failed. err=%v", err)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	if len(groups) != 1 {
		a.log.Errorf("IDP Bind group not correct. groups=%v", groups)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	rootGroup := groups[0]

	// 获取第三方服务器属性
	attrs, err := a.idpRepo.GetIDPAttr(ctx, idp.ID)
	if err != nil {
		a.log.Errorf("GetIDPAttr failed. err=%v", err)
		return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
	}
	idpAttrs := dto.KVsToIdpAttr(attrs)

	// 从第三方查有没有该用户且密码是否正确
	var authProvider PasswordAuthProvider
	var externalUser = &dto.ExternalUser{}
	var localUser *model.TbExternalUser

	switch dto.IDPType(idp.Type) {
	case dto.IDPTypeWeb:
		logger := log.With(log.GetLogger())
		authProvider, err = webauth.NewWebAuthProvider(
			idp.ID,
			idp.Name,
			idpAttrs.UserData,
			logger,
		)
		if err != nil {
			a.log.Errorf("webauth provider 创建失败. err=%v", err)
			return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
		}
		userInfo, err := authProvider.HandleAuthorization(param.UserName, param.Password, ctx)
		if err != nil {
			a.log.Errorf("webauth 处理回调失败. err=%v", err)
			return dto.AuthBasic{}, pb.ErrorAccountOrPasswordError(loginCredError)
		}

		// 设置外部用户信息
		externalUser = userInfo
	case dto.IDPTypeEmail:
		logger := log.With(log.GetLogger())
		emailProvider, err := email.NewEmailProvider(
			idp.ID,
			idp.Name,
			idpAttrs.ConfigData,
			logger,
		)
		if err != nil {
			a.log.Errorf("创建邮箱认证提供者失败: %v", err)
			return dto.AuthBasic{}, pb.ErrorLoginError(loginParamError)
		}
		// 验证邮箱凭据并获取用户信息
		extUser, err := emailProvider.HandleAuthorization(ctx, param.UserName, param.Password)
		if err != nil {
			// 根据错误类型进行分类处理
			if strings.Contains(err.Error(), "no such host") {
				return dto.AuthBasic{}, pb.ErrorAuthServerConfigError("邮箱服务器地址无法解析，请检查网络连接")
			}
			if strings.Contains(err.Error(), "connection refused") {
				return dto.AuthBasic{}, pb.ErrorAuthServerConnectError("邮箱服务器连接被拒绝")
			}
			if strings.Contains(err.Error(), "authentication failed") {
				return dto.AuthBasic{}, pb.ErrorAccountOrPasswordError("邮箱账号或密码错误")
			}
			// 其他网络相关错误
			if strings.Contains(err.Error(), "dial tcp") || strings.Contains(err.Error(), "timeout") {
				return dto.AuthBasic{}, pb.ErrorAuthServerConnectError("网络连接异常，请稍后重试")
			}

			a.log.Errorf("邮箱认证失败: %v", err)
			return dto.AuthBasic{}, pb.ErrorAccountOrPasswordError(loginCredError)
		}

		externalUser = extUser
	default:
		a.log.Errorf("not support idp type=%v", idp.Type)
		return dto.AuthBasic{}, err
	}

	if externalUser.Type == "" {
		source, e := a.userSource.GetUserSource(ctx, param.CorpId, idp.SourceID)
		if e != nil {
			return dto.AuthBasic{}, e
		}
		externalUser.Type = source.SourceType

	}
	localUser, err = a.createThirdUserIfNotExist(ctx, rootGroup, allSource, externalUser, param.CorpId, idpAttrs.SearchMap)
	if err != nil {
		a.log.Errorf("第三方用户创建或查询失败: %v", err)
		return dto.AuthBasic{}, err
	}
	// 查实际本地导入的用户
	user, err := a.userRepo.QueryUserEntity(ctx, param.CorpId, localUser.LocalUserID)
	if err != nil {
		a.log.Errorf("QueryUserEntity failed. err=%v", err)
		return dto.AuthBasic{}, err
	}
	if err := a.isUserValidate(user); err != nil {
		a.log.Errorf("QueryUserValidate failed. err=%v", err)
		return dto.AuthBasic{}, err
	}

	// 确认用户是否开启了对应的认证策略，取最新可用的那条
	policy, err := a.getAuthPolicyOfUser(ctx, param.CorpId, param.IdpId, user)
	if err != nil {
		a.log.Errorf("getAuthPolicyOfUser failed. err=%v", err)
		return dto.AuthBasic{}, err
	}
	// todo 获取assistIDP
	return dto.AuthBasic{
		Idp:    idp,
		User:   user,
		Policy: policy,
	}, nil

}

func (a AuthUsecase) getAuthPolicyOfUser(ctx context.Context, corpId, idpId string, user *model.TbUserEntity) ([]*model.TbAuthPolicy, error) {
	// 确认用户是否开启了对应的认证策略，取最新可用的那条
	authPolicies, err := a.idpRepo.QueryAuthPolicy(ctx, corpId, idpId)
	if err != nil {
		a.log.Errorf("QueryAuthPolicy failed. err=%v, corpId=%v, idpId=%v", err, corpId, idpId)
		return []*model.TbAuthPolicy{}, pb.ErrorLoginError(loginParamError)
	}
	if len(authPolicies) == 0 {
		return []*model.TbAuthPolicy{}, pb.ErrorLoginError("idp not bind available auth policy. corpId=%v, idpId=%v", corpId, idpId)
	}
	// 获取用户分组以及对应组与组之间的关系
	groups, err := a.ugRepo.ListUserGroup(ctx, corpId)
	if err != nil {
		a.log.Errorf("ListUserGroup failed. err=%v", err)
		return []*model.TbAuthPolicy{}, pb.ErrorLoginError(loginParamError)
	}
	groupConnect, err := common.BuildGroupToRootConnect(groups)
	if err != nil {
		a.log.Errorf("BuildGroupToRootConnect failed. err=%v", err)
		return []*model.TbAuthPolicy{}, pb.ErrorLoginError(loginParamError)
	}
	// 找到该用户所绑定的认证策略
	policy, err := a.getUserAuthPolicy(authPolicies, user, groupConnect)
	if len(policy) == 0 {
		a.log.Errorf("getUserAuthPolicy failed. err=%v", err)
		return []*model.TbAuthPolicy{}, pb.ErrorLoginError(loginParamError)
	}
	return policy, nil
}

func (a AuthUsecase) mainIdpAuth(ctx context.Context, authBasic dto.AuthBasic, param dto.LoginForm) error {
	// 首先进行主认证服务校验
	idpType := authBasic.Idp.Type
	switch dto.IDPType(idpType) {
	case dto.IDPTypeLocal:
		if err := a.localPasswdAuth(ctx, authBasic, param); err != nil {
			return err
		}
		return nil
	case dto.IDPTypeWeb, dto.IDPTypeEmail, dto.IDPTypeMsad, dto.IDPTypeLdap:
		if authBasic.User == nil {
			a.log.Errorf("Get User failed.")
			return pb.ErrorLoginError(loginParamError)
		}
		return nil
	default:
		a.log.Errorf("not support IDP type=%v", idpType)
		return pb.ErrorLoginError(loginParamError)
	}
}

// localPasswdAuth 本地账号密码认证
func (a AuthUsecase) localPasswdAuth(ctx context.Context, authBasic dto.AuthBasic, param dto.LoginForm) error {
	idp, user := authBasic.Idp, authBasic.User

	// 获取客户端IP地址
	clientIP := common.GetClientHost(ctx)

	// 检查账户锁定状态
	if err := a.checkAccountLockout(ctx, user.ID, clientIP); err != nil {
		return err
	}

	attr, err := a.idpRepo.GetIDPAttrValue(ctx, idp.ID, dto.AttrKeyAuthType)
	if err != nil {
		a.log.Errorf("GetIDPAttrValue failed. err=%v", err)
		return pb.ErrorLoginError(loginParamError)
	}
	if attr.Value == dto.AttrValPasswd {
		// 取出用户的credential进行校验
		cred, err := a.userRepo.QueryCredential(ctx, param.CorpId, user.ID, string(dto.CredTypePassword))
		if err != nil {
			a.log.Errorf("QueryCredential failed. err=%v, corpId=%v, userId=%v, credType=%v",
				err, param.CorpId, user.ID, string(dto.CredTypePassword))
			return pb.ErrorAccountOrPasswordError(loginCredError)
		}
		var credData common.CredData
		err = json.Unmarshal([]byte(cred.CredentialData), &credData)
		if err != nil {
			a.log.Errorf("Unmarshal failed. err=%v", err)
			return pb.ErrorLoginError(loginParamError)
		}
		manager := common.NewPbkdf2SaltCredManager()

		// 验证密码
		if !manager.IsPasswordCorrect(param.Password, cred.SecretData, credData) {
			// 获取客户端IP地址
			clientIP := common.GetClientHost(ctx)
			a.log.Debugf("password verification failed for user %s from IP %s", user.ID, clientIP)

			// 密码错误，处理密码失败逻辑
			isLocked, err := a.handlePasswordFailure(ctx, user.ID, clientIP)
			if err != nil {
				a.log.Errorf("handlePasswordFailure failed. err=%v, userId=%v, clientIP=%v", err, user.ID, clientIP)
				// 如果处理失败，仍然返回密码错误
				return pb.ErrorAccountOrPasswordError(loginCredError)
			}

			// 如果已被锁定，直接返回锁定信息
			if isLocked {
				lockInfo, err := a.repo.CheckAccountLockStatus(ctx, user.ID)
				if err == nil && lockInfo != nil {
					remainingTime := time.Until(lockInfo.LockedUntil).Seconds()
					if remainingTime > 0 {
						a.log.Warnf("account locked after password failure. userId=%v, locked_until=%v", user.ID, lockInfo.LockedUntil)
						return pb.ErrorAuthUserLock(fmt.Sprintf("账户已被锁定，剩余时间：%.0f分钟", remainingTime/60))
					}
				}
			}

			// 如果没有被锁定，返回密码错误
			return pb.ErrorAccountOrPasswordError(loginCredError)
		}

		// 密码正确，清除失败记录
		if err := a.repo.ClearPasswordFailureInfo(ctx, user.ID); err != nil {
			a.log.Warnf("clear password failure info failed. err=%v, userId=%v", err, user.ID)
		}

		// 密码正确，同时清除IP失败记录
		if clientIP != "" {
			if err := a.repo.ClearIPFailureInfo(ctx, clientIP); err != nil {
				a.log.Warnf("clear IP failure info failed. err=%v, clientIP=%v", err, clientIP)
			}
		}

		return nil
	}
	a.log.Errorf("auth type=%v not support.", attr.Value)
	return pb.ErrorLoginError(loginParamError)
}

// checkAccountLockout 检查账户和IP锁定状态
func (a AuthUsecase) checkAccountLockout(ctx context.Context, userID string, clientIP string) error {
	// 检查账户锁定状态
	lockInfo, err := a.repo.CheckAccountLockStatus(ctx, userID)
	if err != nil {
		a.log.Errorf("check account lock status failed. err=%v, userId=%v", err, userID)
		return pb.ErrorLoginError("账户状态检查失败")
	}

	if lockInfo != nil {
		remainingTime := time.Until(lockInfo.LockedUntil).Seconds()
		if remainingTime > 0 {
			a.log.Warnf("account locked by brute force protection. userId=%v, locked_until=%v", userID, lockInfo.LockedUntil)
			return pb.ErrorAuthUserLock(fmt.Sprintf("账户因连续密码错误已被锁定，剩余时间：%.0f分钟", remainingTime/60))
		}
	}

	// 检查IP锁定状态（如果提供了IP地址）
	if clientIP != "" {
		ipLockInfo, err := a.repo.CheckIPLockStatus(ctx, clientIP)
		if err != nil {
			a.log.Errorf("check IP lock status failed. err=%v, clientIP=%v", err, clientIP)
			// IP锁定状态检查失败不阻止登录，只记录日志
		} else if ipLockInfo != nil {
			remainingTime := time.Until(ipLockInfo.LockedUntil).Seconds()
			if remainingTime > 0 {
				a.log.Warnf("IP locked by brute force protection. clientIP=%v, locked_until=%v", clientIP, ipLockInfo.LockedUntil)
				return pb.ErrorAuthUserLock(fmt.Sprintf("IP %s 因连续密码错误已被锁定，剩余时间：%.0f分钟", clientIP, remainingTime/60))
			}
		}
	}

	return nil
}

// handlePasswordFailure 处理密码错误逻辑，支持用户和IP锁定，返回是否已锁定账户
func (a AuthUsecase) handlePasswordFailure(ctx context.Context, userID string, clientIP string) (bool, error) {
	a.log.Warnf("password failure for userId=%v, clientIP=%v", userID, clientIP)

	// 获取用户信息以便获取企业ID
	user, err := a.userRepo.QueryUserEntity(ctx, "", userID)
	if err != nil {
		return false, fmt.Errorf("get user failed: %w", err)
	}

	// 处理密码锁定逻辑
	passwordLocked, err := a.handlePasswordFailureForUser(ctx, userID, user.CorpID)
	if err != nil {
		a.log.Errorf("handle password failure for user failed: %v", err)
	}

	// 处理IP锁定逻辑（如果提供了IP地址）
	ipLocked := false
	if clientIP != "" {
		ipLocked, err = a.handlePasswordFailureForIP(ctx, clientIP, user.CorpID)
		if err != nil {
			a.log.Errorf("handle password failure for IP failed: %v", err)
		}
	}

	// 返回是否有任何锁定发生
	return passwordLocked || ipLocked, nil
}

// handlePasswordFailureForUser 处理用户密码失败逻辑
func (a AuthUsecase) handlePasswordFailureForUser(ctx context.Context, userID string, corpID string) (bool, error) {
	// 获取密码锁定策略
	policy, err := a.getPasswordLockoutPolicy(ctx, userID)
	a.log.Debugf("password lockout policy: %+v", policy)
	if err != nil {
		return false, fmt.Errorf("get password lockout policy failed: %w", err)
	}

	// 如果未启用密码锁定，直接返回
	if policy == nil || !policy.Enabled {
		return false, nil
	}

	// 获取当前失败信息
	failureInfo, err := a.repo.GetPasswordFailureInfo(ctx, userID)
	a.log.Debugf("current password failure info: %+v", failureInfo)
	if err != nil {
		return false, fmt.Errorf("get password failure info failed: %w", err)
	}

	// 增加失败次数
	failureInfo.Count++
	failureInfo.LastFailureTime = time.Now()

	// 检查是否需要重置（基于时间窗口）
	if failureInfo.Count > 1 {
		timeSinceLastFailure := time.Since(failureInfo.LastFailureTime)
		resetWindow := time.Duration(policy.ResetFailureWindowSec) * time.Second
		if timeSinceLastFailure > resetWindow {
			// 超过重置窗口，重置为1
			failureInfo.Count = 1
			failureInfo.LastFailureTime = time.Now()
		}
	}

	// 更新失败信息
	ttl := time.Duration(policy.ResetFailureWindowSec) * time.Second
	if err := a.repo.SetPasswordFailureInfo(ctx, userID, *failureInfo, ttl); err != nil {
		return false, fmt.Errorf("set password failure info failed: %w", err)
	}

	// 检查是否达到锁定阈值
	if failureInfo.Count >= int(policy.MaxFailureCount) {
		lockInfo := dto.AccountLockInfo{
			LockedUntil: time.Now().Add(time.Duration(policy.LockoutDurationSec) * time.Second),
			Reason:      fmt.Sprintf("连续密码错误%d次", failureInfo.Count),
		}
		lockTTL := time.Duration(policy.LockoutDurationSec) * time.Second
		if err := a.repo.SetAccountLockStatus(ctx, userID, lockInfo, lockTTL); err != nil {
			return false, fmt.Errorf("set account lock status failed: %w", err)
		}

		// 将用户添加到锁定用户索引中
		go func() {
			lockKey := fmt.Sprintf("account_locked:%s", userID)
			corpLockSetKey := fmt.Sprintf("locked_users:%s", corpID)
			a.repo.SAdd(context.Background(), corpLockSetKey, lockKey)
		}()

		a.log.Warnf("account locked due to password failures. userId=%v, failureCount=%d", userID, failureInfo.Count)
		return true, nil // 返回true表示账户已被锁定
	}

	return false, nil // 返回false表示账户未被锁定
}

// handlePasswordFailureForIP 处理IP密码失败逻辑
func (a AuthUsecase) handlePasswordFailureForIP(ctx context.Context, clientIP string, corpID string) (bool, error) {
	// 获取IP锁定策略
	policy, err := a.getIPLockoutPolicy(ctx, corpID)
	a.log.Debugf("IP lockout policy: %+v", policy)
	if err != nil {
		return false, fmt.Errorf("get IP lockout policy failed: %w", err)
	}

	// 如果未启用IP锁定，直接返回
	if policy == nil || !policy.Enabled {
		return false, nil
	}

	// 获取当前IP失败信息
	ipFailureInfo, err := a.repo.GetIPFailureInfo(ctx, clientIP)
	a.log.Debugf("current IP failure info: %+v", ipFailureInfo)
	if err != nil {
		return false, fmt.Errorf("get IP failure info failed: %w", err)
	}

	// 增加失败次数
	ipFailureInfo.Count++
	ipFailureInfo.LastFailureTime = time.Now()

	// 检查是否需要重置（基于时间窗口）
	if ipFailureInfo.Count > 1 {
		timeSinceLastFailure := time.Since(ipFailureInfo.LastFailureTime)
		resetWindow := time.Duration(policy.ResetFailureWindowSec) * time.Second
		if timeSinceLastFailure > resetWindow {
			// 超过重置窗口，重置为1
			ipFailureInfo.Count = 1
			ipFailureInfo.LastFailureTime = time.Now()
		}
	}

	// 更新IP失败信息
	ttl := time.Duration(policy.ResetFailureWindowSec) * time.Second
	if err := a.repo.SetIPFailureInfo(ctx, clientIP, *ipFailureInfo, ttl); err != nil {
		return false, fmt.Errorf("set IP failure info failed: %w", err)
	}

	// 检查是否达到锁定阈值
	if ipFailureInfo.Count >= int(policy.MaxFailureCount) {
		lockInfo := dto.IPLockInfo{
			LockedUntil: time.Now().Add(time.Duration(policy.LockoutDurationSec) * time.Second),
			Reason:      fmt.Sprintf("IP连续密码错误%d次", ipFailureInfo.Count),
		}
		lockTTL := time.Duration(policy.LockoutDurationSec) * time.Second
		if err := a.repo.SetIPLockStatus(ctx, clientIP, lockInfo, lockTTL); err != nil {
			return false, fmt.Errorf("set IP lock status failed: %w", err)
		}

		// 将IP添加到锁定IP索引中
		go func() {
			lockKey := fmt.Sprintf("ip_locked:%s", clientIP)
			corpLockSetKey := fmt.Sprintf("locked_ips:%s", corpID)
			a.repo.SAdd(context.Background(), corpLockSetKey, lockKey)
		}()

		a.log.Warnf("IP locked due to password failures. clientIP=%v, failureCount=%d", clientIP, ipFailureInfo.Count)
		return true, nil // 返回true表示IP已被锁定
	}

	return false, nil // 返回false表示IP未被锁定
}

// getPasswordLockoutPolicy 获取密码锁定策略配置
func (a AuthUsecase) getPasswordLockoutPolicy(ctx context.Context, userID string) (*dto.AccountPolicyPasswordLockout, error) {
	// 获取用户信息
	user, err := a.userRepo.QueryUserEntity(ctx, "", userID)
	if err != nil {
		return nil, fmt.Errorf("get user failed: %w", err)
	}

	// 从账户策略表获取密码锁定策略
	policy, err := a.accountPolicyRepo.GetPasswordLockoutPolicy(ctx, user.CorpID)
	if err != nil {
		a.log.Warnf("get password lockout policy failed. err=%v, corpId=%v", err, user.CorpID)
		return nil, nil // 如果获取失败，返回nil表示不启用锁定
	}

	return policy, nil
}

// getIPLockoutPolicy 获取IP锁定策略配置
func (a AuthUsecase) getIPLockoutPolicy(ctx context.Context, corpID string) (*dto.AccountPolicyIPLockout, error) {
	// 从账户策略表获取IP锁定策略
	policy, err := a.accountPolicyRepo.GetIPLockoutPolicy(ctx, corpID)
	if err != nil {
		a.log.Warnf("get IP lockout policy failed. err=%v, corpId=%v", err, corpID)
		return nil, nil // 如果获取失败，返回nil表示不启用锁定
	}

	return policy, nil
}

// UnlockAccount 解锁账户（仅清除防暴破锁定，不影响管理员锁定）
func (a AuthUsecase) UnlockAccount(ctx context.Context, userID string) error {
	a.log.Infof("unlocking account for userId=%v", userID)

	// 获取用户信息，用于从锁定用户索引中移除
	user, err := a.userRepo.QueryUserEntity(ctx, "", userID)
	if err != nil {
		a.log.Warnf("get user entity for unlock failed: %v", err)
	}

	// 清除Redis中的防暴破锁定状态
	if err := a.repo.ClearAccountLockStatus(ctx, userID); err != nil {
		a.log.Errorf("clear account lock status failed. err=%v, userId=%v", err, userID)
		return fmt.Errorf("清除账户锁定状态失败: %w", err)
	}

	// 清除密码失败计数
	if err := a.repo.ClearPasswordFailureInfo(ctx, userID); err != nil {
		a.log.Errorf("clear password failure info failed. err=%v, userId=%v", err, userID)
		return fmt.Errorf("清除密码失败记录失败: %w", err)
	}

	// 从锁定用户索引中移除
	if user != nil {
		// 记录账户解锁日志
		a.recordAccountUnlockLog(ctx, user)

		go func() {
			lockKey := fmt.Sprintf("account_locked:%s", userID)
			corpLockSetKey := fmt.Sprintf("locked_users:%s", user.CorpID)
			a.repo.SRem(context.Background(), corpLockSetKey, []string{lockKey})
		}()
	}

	a.log.Infof("account unlocked successfully for userId=%v", userID)
	return nil
}

// UnlockIP 解锁IP（清除IP防暴破锁定）
func (a AuthUsecase) UnlockIP(ctx context.Context, clientIP string, corpID string) error {
	a.log.Infof("unlocking IP for clientIP=%v, corpID=%v", clientIP, corpID)

	// 清除Redis中的IP锁定状态
	if err := a.repo.ClearIPLockStatus(ctx, clientIP); err != nil {
		a.log.Errorf("clear IP lock status failed. err=%v, clientIP=%v", err, clientIP)
		return fmt.Errorf("清除IP锁定状态失败: %w", err)
	}

	// 清除IP失败计数
	if err := a.repo.ClearIPFailureInfo(ctx, clientIP); err != nil {
		a.log.Errorf("clear IP failure info failed. err=%v, clientIP=%v", err, clientIP)
		return fmt.Errorf("清除IP失败记录失败: %w", err)
	}

	// 从锁定IP索引中移除
	go func() {
		lockKey := fmt.Sprintf("ip_locked:%s", clientIP)
		corpLockSetKey := fmt.Sprintf("locked_ips:%s", corpID)
		a.repo.SRem(context.Background(), corpLockSetKey, []string{lockKey})
	}()

	// 记录管理员操作日志
	a.recordUnlockIPLog(ctx, clientIP, corpID)

	a.log.Infof("IP unlocked successfully for clientIP=%v", clientIP)
	return nil
}

// GetAccountLockInfo 获取账户锁定信息（用于管理界面显示）
func (a AuthUsecase) GetAccountLockInfo(ctx context.Context, userID string) (*dto.AccountLockInfo, error) {
	lockInfo, err := a.repo.GetAccountLockStatus(ctx, userID)
	if err != nil {
		a.log.Errorf("get account lock status failed. err=%v, userId=%v", err, userID)
		return nil, fmt.Errorf("获取账户锁定信息失败: %w", err)
	}

	// 如果锁定信息存在但已过期，返回nil
	if lockInfo != nil && time.Now().After(lockInfo.LockedUntil) {
		return nil, nil
	}

	return lockInfo, nil
}

// GetPasswordFailureCount 获取密码失败次数（用于管理界面显示）
func (a AuthUsecase) GetPasswordFailureCount(ctx context.Context, userID string) (*dto.PasswordFailureInfo, error) {
	failureInfo, err := a.repo.GetPasswordFailureInfo(ctx, userID)
	if err != nil {
		a.log.Errorf("get password failure info failed. err=%v, userId=%v", err, userID)
		return nil, fmt.Errorf("获取密码失败信息失败: %w", err)
	}

	return failureInfo, nil
}

// GetIPLockInfo 获取IP锁定信息（用于管理界面显示）
func (a AuthUsecase) GetIPLockInfo(ctx context.Context, clientIP string) (*dto.IPLockInfo, error) {
	lockInfo, err := a.repo.GetIPLockStatus(ctx, clientIP)
	if err != nil {
		a.log.Errorf("get IP lock status failed. err=%v, clientIP=%v", err, clientIP)
		return nil, fmt.Errorf("获取IP锁定信息失败: %w", err)
	}

	// 如果锁定信息存在但已过期，返回nil
	if lockInfo != nil && time.Now().After(lockInfo.LockedUntil) {
		return nil, nil
	}

	return lockInfo, nil
}

// GetIPFailureCount 获取IP失败次数（用于管理界面显示）
func (a AuthUsecase) GetIPFailureCount(ctx context.Context, clientIP string) (*dto.IPFailureInfo, error) {
	failureInfo, err := a.repo.GetIPFailureInfo(ctx, clientIP)
	if err != nil {
		a.log.Errorf("get IP failure info failed. err=%v, clientIP=%v", err, clientIP)
		return nil, fmt.Errorf("获取IP失败信息失败: %w", err)
	}

	return failureInfo, nil
}

// ListLockedAccounts 获取锁定账户列表（用于管理界面显示）- 高度优化版本
func (a *AuthUsecase) ListLockedAccounts(ctx context.Context, corpID string, limit, offset int, search, lockType string) ([]*dto.LockedAccountInfo, int, error) {
	// 获取租户下所有锁定用户的ID列表（从Redis Set中获取）
	lockedUserKeys, err := a.repo.SMembers(ctx, fmt.Sprintf("locked_users:%s", corpID))
	if err != nil {
		a.log.Errorf("get locked users from redis failed. err=%v, corpId=%v", err, corpID)
		// 如果Redis操作失败，回退到原始实现
		return a.listLockedAccountsFallback(ctx, corpID, limit, offset, search, lockType)
	}

	if len(lockedUserKeys) == 0 {
		return []*dto.LockedAccountInfo{}, 0, nil
	}

	// 从锁定用户key中提取用户ID
	var userIDs []string
	for _, key := range lockedUserKeys {
		// key格式: account_locked:userId，提取userId部分
		if parts := strings.Split(key, ":"); len(parts) >= 2 {
			userIDs = append(userIDs, parts[len(parts)-1])
		}
	}

	if len(userIDs) == 0 {
		return []*dto.LockedAccountInfo{}, 0, nil
	}

	// 批量获取用户信息
	var users []*model.TbUserEntity
	for _, userID := range userIDs {
		user, err := a.userRepo.QueryUserEntity(ctx, corpID, userID)
		if err != nil {
			a.log.Warnf("get user entity failed for user %s: %v", userID, err)
			continue
		}
		if user != nil {
			users = append(users, user)
		}
	}

	// 批量获取用户组信息（避免N+1查询）
	groupMap := make(map[string]string)
	if len(users) > 0 {
		// 一次性获取租户下所有用户组，构建完整路径
		groups, err := a.ugRepo.ListUserGroup(ctx, corpID)
		if err != nil {
			a.log.Warnf("get user groups failed: %v", err)
		} else {
			// 构建树处理器
			treeHandler := common.RootToNodeSimpleTree{}
			err = treeHandler.Build(groups)
			if err != nil {
				a.log.Warnf("构建组织树失败: error=%v", err)
			} else {
				// 构建组节点映射
				groupNodeMap := common.BuildGroupNodeMap(groups)
				// 为每个用户的组ID构建完整路径
				var groupIDs []string
				groupIDSet := make(map[string]bool)
				for _, user := range users {
					if user.GroupID != "" && !groupIDSet[user.GroupID] {
						groupIDs = append(groupIDs, user.GroupID)
						groupIDSet[user.GroupID] = true
					}
				}

				for _, groupID := range groupIDs {
					// 获取从根到节点的完整路径
					groupPathIds := treeHandler.GetRootToNodePath(dto.FakeRootUserGroupId, groupID)
					groupPath := ""
					for _, id := range groupPathIds {
						if id == dto.FakeRootUserGroupId {
							continue
						}
						if node, exists := groupNodeMap[id]; exists {
							groupPath = groupPath + "/" + node.Name
						}
					}
					groupMap[groupID] = groupPath
				}
			}
		}
	}

	// 处理锁定账户信息
	var lockedAccounts []*dto.LockedAccountInfo
	now := time.Now()

	for _, user := range users {
		// 检查账户锁定状态
		lockInfo, err := a.repo.CheckAccountLockStatus(ctx, user.ID)
		if err != nil {
			a.log.Warnf("get account lock status failed for user %s: %v", user.ID, err)
			continue
		}

		// 如果没有锁定信息或已过期，跳过并从索引中移除
		if lockInfo == nil || now.After(lockInfo.LockedUntil) {
			// 异步清理过期的锁定记录
			go func(userID string) {
				a.repo.SRem(ctx, fmt.Sprintf("locked_users:%s", corpID), []string{fmt.Sprintf("account_locked:%s", userID)})
			}(user.ID)
			continue
		}

		// 根据锁定类型筛选
		if lockType != "" && lockType != "all" {
			if lockType == "password" && lockInfo.Reason != "密码错误次数过多" {
				continue
			}
			if lockType == "admin" && lockInfo.Reason == "密码错误次数过多" {
				continue
			}
		}

		// 获取用户组名称
		groupName := groupMap[user.GroupID]
		if groupName == "" && user.GroupID != "" {
			groupName = "未知组"
		}

		// 应用搜索过滤
		if search != "" {
			searchLower := strings.ToLower(search)
			if !strings.Contains(strings.ToLower(user.Name), searchLower) &&
				!strings.Contains(strings.ToLower(groupName), searchLower) {
				continue
			}
		}

		accountInfo := &dto.LockedAccountInfo{
			UserID:      user.ID,
			Username:    user.Name,
			GroupID:     user.GroupID,
			GroupName:   groupName,
			LockedAt:    now.Add(-time.Hour), // 默认一小时前锁定
			LockedUntil: lockInfo.LockedUntil,
			LockReason:  lockInfo.Reason,
			LockType:    "password_policy",
		}

		lockedAccounts = append(lockedAccounts, accountInfo)
	}

	// 分页处理
	total := len(lockedAccounts)
	start := offset
	end := start + limit

	if start >= total {
		return []*dto.LockedAccountInfo{}, total, nil
	}
	if end > total {
		end = total
	}

	return lockedAccounts[start:end], total, nil
}

// listLockedAccountsFallback 回退实现（原始逻辑的简化版本）
func (a *AuthUsecase) listLockedAccountsFallback(ctx context.Context, corpID string, limit, offset int, search, lockType string) ([]*dto.LockedAccountInfo, int, error) {
	// 获取所有用户列表
	users, err := a.userRepo.ListUser(ctx, corpID)
	if err != nil {
		a.log.Errorf("list users failed. err=%v, corpId=%v", err, corpID)
		return nil, 0, fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 批量获取用户组信息
	groupMap := make(map[string]string)
	// 一次性获取租户下所有用户组，构建完整路径
	groups, err := a.ugRepo.ListUserGroup(ctx, corpID)
	if err != nil {
		a.log.Warnf("get user groups failed: %v", err)
	} else {
		// 构建树处理器
		treeHandler := common.RootToNodeSimpleTree{}
		err = treeHandler.Build(groups)
		if err != nil {
			a.log.Warnf("构建组织树失败: error=%v", err)
		} else {
			// 构建组节点映射
			groupNodeMap := common.BuildGroupNodeMap(groups)
			// 为每个用户的组ID构建完整路径
			var groupIDs []string
			groupIDSet := make(map[string]bool)
			for _, user := range users {
				if user.GroupID != "" && !groupIDSet[user.GroupID] {
					groupIDs = append(groupIDs, user.GroupID)
					groupIDSet[user.GroupID] = true
				}
			}

			for _, groupID := range groupIDs {
				// 获取从根到节点的完整路径
				groupPathIds := treeHandler.GetRootToNodePath(dto.FakeRootUserGroupId, groupID)
				groupPath := ""
				for _, id := range groupPathIds {
					if id == dto.FakeRootUserGroupId {
						continue
					}
					if node, exists := groupNodeMap[id]; exists {
						groupPath = groupPath + "/" + node.Name
					}
				}
				groupMap[groupID] = groupPath
			}
		}
	}

	var lockedAccounts []*dto.LockedAccountInfo
	now := time.Now()

	for _, user := range users {
		lockInfo, err := a.repo.CheckAccountLockStatus(ctx, user.ID)
		if err != nil || lockInfo == nil || now.After(lockInfo.LockedUntil) {
			continue
		}

		if lockType != "" && lockType != "all" {
			if lockType == "password" && lockInfo.Reason != "密码错误次数过多" {
				continue
			}
			if lockType == "admin" && lockInfo.Reason == "密码错误次数过多" {
				continue
			}
		}

		groupName := groupMap[user.GroupID]
		if groupName == "" && user.GroupID != "" {
			groupName = "未知组"
		}

		if search != "" {
			searchLower := strings.ToLower(search)
			if !strings.Contains(strings.ToLower(user.Name), searchLower) &&
				!strings.Contains(strings.ToLower(groupName), searchLower) {
				continue
			}
		}

		accountInfo := &dto.LockedAccountInfo{
			UserID:      user.ID,
			Username:    user.Name,
			GroupID:     user.GroupID,
			GroupName:   groupName,
			LockedAt:    now.Add(-time.Hour),
			LockedUntil: lockInfo.LockedUntil,
			LockReason:  lockInfo.Reason,
			LockType:    "password_policy",
		}

		lockedAccounts = append(lockedAccounts, accountInfo)
	}

	total := len(lockedAccounts)
	start := offset
	end := start + limit

	if start >= total {
		return []*dto.LockedAccountInfo{}, total, nil
	}
	if end > total {
		end = total
	}

	return lockedAccounts[start:end], total, nil
}

// BatchUnlockIPs 批量解锁IP
func (a AuthUsecase) BatchUnlockIPs(ctx context.Context, corpID string, ipAddresses []string, reason string) (int32, int32, []string, error) {
	a.log.Infof("Batch unlocking IPs: corpID=%s, count=%d, reason=%s", corpID, len(ipAddresses), reason)

	successCount := int32(0)
	failedCount := int32(0)
	var failedIPs []string

	for _, ip := range ipAddresses {
		err := a.UnlockIP(ctx, ip, corpID)
		if err != nil {
			failedCount++
			failedIPs = append(failedIPs, ip)
			a.log.Warnf("Failed to unlock IP %s: %v", ip, err)
		} else {
			successCount++
		}
	}

	return successCount, failedCount, failedIPs, nil
}

// ListLockedIPs 获取锁定IP列表（用于管理界面显示）
func (a *AuthUsecase) ListLockedIPs(ctx context.Context, corpID string, limit, offset int, search string) ([]*dto.LockedIPInfo, int, error) {
	// 获取企业下所有锁定IP的key列表（从Redis Set中获取）
	lockedIPKeys, err := a.repo.SMembers(ctx, fmt.Sprintf("locked_ips:%s", corpID))
	if err != nil {
		a.log.Errorf("get locked IPs from redis failed. err=%v, corpId=%v", err, corpID)
		return []*dto.LockedIPInfo{}, 0, nil
	}

	if len(lockedIPKeys) == 0 {
		return []*dto.LockedIPInfo{}, 0, nil
	}

	// 从锁定IP key中提取IP地址
	var ipAddresses []string
	for _, key := range lockedIPKeys {
		// key格式: ip_locked:***********，提取IP部分
		if parts := strings.Split(key, ":"); len(parts) >= 2 {
			ipAddress := strings.Join(parts[1:], ":") // 处理IPv6地址可能包含冒号的情况
			ipAddresses = append(ipAddresses, ipAddress)
		}
	}

	if len(ipAddresses) == 0 {
		return []*dto.LockedIPInfo{}, 0, nil
	}

	// 获取企业信息
	corp, err := a.corpRepo.GetCorpByID(ctx, corpID)
	var corpName string
	if err != nil {
		a.log.Warnf("get corp info failed. err=%v, corpId=%v", err, corpID)
		corpName = "未知企业"
	} else {
		corpName = corp.Name
	}

	// 批量获取IP锁定信息和失败信息
	var lockedIPs []*dto.LockedIPInfo
	for _, ip := range ipAddresses {
		// 应用搜索过滤
		if search != "" && !strings.Contains(ip, search) {
			continue
		}

		// 获取IP锁定信息
		lockInfo, err := a.repo.GetIPLockStatus(ctx, ip)
		if err != nil {
			a.log.Warnf("get IP lock status failed. err=%v, ip=%v", err, ip)
			continue
		}

		// 如果没有锁定信息或已过期，跳过
		if lockInfo == nil || time.Now().After(lockInfo.LockedUntil) {
			continue
		}

		// 获取IP失败信息
		failureInfo, err := a.repo.GetIPFailureInfo(ctx, ip)
		if err != nil {
			a.log.Warnf("get IP failure info failed. err=%v, ip=%v", err, ip)
			failureInfo = &dto.IPFailureInfo{Count: 0, LastFailureTime: time.Time{}}
		}

		// 构建锁定IP信息
		remainingSeconds := int32(time.Until(lockInfo.LockedUntil).Seconds())
		if remainingSeconds < 0 {
			remainingSeconds = 0
		}

		ipLockInfo := &dto.IPLockInfo{
			IsLocked:         true,
			LockedUntil:      lockInfo.LockedUntil,
			Reason:           lockInfo.Reason,
			FailureCount:     failureInfo.Count,
			LastFailureTime:  failureInfo.LastFailureTime,
			RemainingSeconds: remainingSeconds,
			IPAddress:        ip,
		}

		lockedIPInfo := &dto.LockedIPInfo{
			IPAddress:     ip,
			CorpID:        corpID,
			CorpName:      corpName,
			LockInfo:      ipLockInfo,
			Location:      "", // TODO: 实现IP地理位置查询
			TotalAttempts: failureInfo.Count,
		}

		lockedIPs = append(lockedIPs, lockedIPInfo)
	}

	// 排序（按锁定时间倒序）
	sort.Slice(lockedIPs, func(i, j int) bool {
		return lockedIPs[i].LockInfo.LastFailureTime.After(lockedIPs[j].LockInfo.LastFailureTime)
	})

	// 分页处理
	total := len(lockedIPs)
	start := offset
	end := start + limit

	if start >= total {
		return []*dto.LockedIPInfo{}, total, nil
	}
	if end > total {
		end = total
	}

	return lockedIPs[start:end], total, nil
}

func (a AuthUsecase) getUserAuthPolicy(authPolicies []*model.TbAuthPolicy, user *model.TbUserEntity, groupConnect map[string]string) ([]*model.TbAuthPolicy, error) {
	var policies []*model.TbAuthPolicy
	for _, p := range authPolicies {
		// 判断用户绑定
		if p.EnableAllUser {
			policies = append(policies, p)
			continue
		}
		if len(p.UserIds) != 0 && common.IsInSlice(user.ID, p.UserIds) {
			policies = append(policies, p)
			continue
		}
		// 判断用户组绑定
		if len(p.GroupIds) != 0 {
			for _, gid := range p.GroupIds {
				is, err := common.GroupIsChild(groupConnect, user.GroupID, gid)
				if err != nil {
					//policies = append(policies, p)
					continue
				}
				if is {
					policies = append(policies, p)
					continue
				}
			}
		}
	}
	return policies, nil
}

func (a AuthUsecase) authz(ctx context.Context, user *model.TbUserEntity, param dto.AuthBasicForm) (any, error) {
	if err := a.authzCheck(ctx, param.CorpId, param.ClientId, param.Scope, param.GrantType); err != nil {
		return nil, err
	}
	switch param.GrantType {
	case dto.GrantTypeImplicit:
		token, err := a.issueToken(ctx, *user, dto.AccessTokenTyp)
		if err != nil {
			a.log.Errorf("issueToken failed. grant type=%v", dto.GrantTypeImplicit)
			return nil, err
		}
		return token, nil
	case dto.GrantTypeAuthzCode:
		code, err := a.repo.GenerateAuthCode(ctx, *user)
		if err != nil {
			a.log.Errorf("GenerateAuthCode failed. grant type=%v", dto.GrantTypeAuthzCode)
			return nil, err
		}
		return dto.AuthzLoginResp{
			Code:        code,
			GrantType:   dto.GrantTypeAuthzCode,
			RedirectUri: param.RedirectUri,
		}, nil
	default:
		a.log.Errorf("GrantType=%v not support", param.GrantType)
		return nil, pb.ErrorLoginError(loginParamError)
	}
}

func (a AuthUsecase) authzCheck(ctx context.Context, corpId, clientId, scope string, grantType dto.GrantType) error {
	// todo 进行授权校验，判断应用是否有对应资源访问权限
	return nil
}

func (a AuthUsecase) GetToken(ctx context.Context, grantType, code, clientId string) (dto.Token, error) {
	var user model.TbUserEntity
	var err error
	//竹云定制
	if clientId == bamboocloud.ClientId {
		token, err := bamboocloud.DoGetToken(ctx, code)
		if err != nil {
			return dto.Token{}, err
		}
		userInfo, err := bamboocloud.GetUserInfo(token.AccessToken)
		if err != nil {
			return dto.Token{}, err
		}
		user, err = a.GetUserInfoByName(ctx, userInfo)
		if err != nil {
			return dto.Token{}, err
		}
	} else {
		// todo 根据clientId 获取对应client 配置
		// 根据 auth code 获取用户信息
		user, err = a.repo.GetUserCache(ctx, code)
		if err != nil {
			a.log.Errorf("GetUserCache failed. err=%v", err)
			return dto.Token{}, err
		}
	}
	token, err := a.issueToken(ctx, user, dto.AccessTokenTyp)
	if err != nil {
		return dto.Token{}, err
	}
	return token, nil
}

func (a AuthUsecase) issueParams(ctx context.Context, corpId string) (dto.IssuerParams, error) {
	key, err := a.repo.GetEcdsaPrivateKey(ctx, corpId)
	if err != nil || key == "" {
		a.log.Errorf("GetEcdsaPrivateKey failed. err=%v key=%v", err, key)
		return dto.IssuerParams{}, pb.ErrorRecordNotFound("private key not found")
	}
	// 签发token
	issuer := jwt_util.NewJwtIssuer(jwt_util.Ecdsa256)
	privateKey, err := issuer.LoadStringPrivateKey("-----BEGIN EC PRIVATE KEY-----\n" + key + "\n-----END EC PRIVATE KEY-----")
	if err != nil {
		a.log.Errorf("LoadStringPrivateKey failed. err=%v", err)
		return dto.IssuerParams{}, err
	}
	accessTokenDuration := a.GetTokenTime(dto.AccessTokenDuration)
	if accessTokenDuration == 0 {
		a.log.Errorf("GetAccessToken config failed. err=%v", err)
		return dto.IssuerParams{}, err
	}
	defaultRefreshDuration := a.GetTokenTime(dto.RefreshTokenDuration)
	if defaultRefreshDuration == 0 {
		a.log.Errorf("GetRefreshToken config failed. err=%v", err)
		return dto.IssuerParams{}, err
	}
	//RefreshTokenLeastDuration， 刷新refresh token检测的最小间隔事件，默认为刷新token有效期的一半。比如刷新token有效果7天，则当检测到刷新token有效期小于3天时，会重新签发一个7天有效期的token
	return dto.IssuerParams{PrivateKey: privateKey, AccessTokenDuration: accessTokenDuration, RefreshTokenDuration: defaultRefreshDuration, RefreshTokenLeastDuration: defaultRefreshDuration / 2}, nil
}

func (a AuthUsecase) getAccessToken(ctx context.Context, issuer jwt_util.JwtIssuer, params dto.IssuerParams, userInfo *dto.IssueUserInfo) (string, error) {
	token, _, err := a.getAccessTokenWithJTI(ctx, issuer, params, userInfo)
	return token, err
}

// getAccessTokenWithJTI 获取AccessToken和JTI
func (a AuthUsecase) getAccessTokenWithJTI(ctx context.Context, issuer jwt_util.JwtIssuer, params dto.IssuerParams, userInfo *dto.IssueUserInfo) (string, string, error) {
	// 签发access_token
	jti := uuid.New().String() // 生成JTI
	accessClaims := jwt_util.TokenClaims{
		Typ:               string(dto.AccessTokenTyp),
		Name:              userInfo.UserName,
		PreferredUsername: userInfo.PreferredName,
		Identifier:        userInfo.Identifier,
		Email:             userInfo.Email,
		Phone:             userInfo.Phone,
		GroupId:           userInfo.GroupId,
		SessionID:         userInfo.SessionID, // 添加SessionID
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti, // 使用生成的JTI
			Subject:   userInfo.UserId,
			IssuedAt:  jwt.NewNumericDate(time.Now().In(time.Local)),
			ExpiresAt: jwt.NewNumericDate(time.Now().In(time.Local).Add(params.AccessTokenDuration)),
		},
		SecurityCode: userInfo.SecurityCode,
	}
	accessToken, err := issuer.IssueToken(params.PrivateKey, accessClaims)
	if err != nil {
		a.log.Errorf("IssueToken failed. err=%v", err)
		return "", "", err
	}
	err = a.SetCorpUserToken(ctx, userInfo.CorpId, userInfo.UserId, accessToken, params.AccessTokenDuration)
	if err != nil {
		a.log.Errorf("Update redis user status failed. err=%v", err)
		return "", "", err
	}
	return accessToken, jti, err
}

func (a AuthUsecase) getRefreshToken(ctx context.Context, issuer jwt_util.JwtIssuer, params dto.IssuerParams, userInfo *dto.IssueUserInfo) (string, error) {
	token, _, err := a.getRefreshTokenWithJTI(ctx, issuer, params, userInfo)
	return token, err
}

// getRefreshTokenWithJTI 获取RefreshToken和JTI
func (a AuthUsecase) getRefreshTokenWithJTI(ctx context.Context, issuer jwt_util.JwtIssuer, params dto.IssuerParams, userInfo *dto.IssueUserInfo) (string, string, error) {
	// 签发refresh_token
	jti := uuid.New().String() // 生成JTI
	refreshClaims := jwt_util.TokenClaims{
		Typ:               string(dto.RefreshTokenTyp),
		Name:              userInfo.UserName,
		PreferredUsername: userInfo.PreferredName,
		Identifier:        userInfo.Identifier,
		Email:             userInfo.Email,
		Phone:             userInfo.Phone,
		GroupId:           userInfo.GroupId,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti, // 使用生成的JTI
			Subject:   userInfo.UserId,
			IssuedAt:  jwt.NewNumericDate(time.Now().In(time.Local)),
			ExpiresAt: jwt.NewNumericDate(time.Now().In(time.Local).Add(params.RefreshTokenDuration)),
		},
		SecurityCode: userInfo.SecurityCode,
	}
	var refreshToken string

	refreshToken, err := issuer.IssueToken(params.PrivateKey, refreshClaims)
	if err != nil {
		a.log.Errorf("IssueRefreshToken failed. err=%v", err)
		return "", "", err
	}
	return refreshToken, jti, nil
}

func encryptSecurityCode(securityCode string, licenseId string) (string, error) {
	if securityCode == "" {
		return "", nil
	}

	key := []byte(strings.ReplaceAll(licenseId, "-", ""))
	if len(key) != 32 {
		return "", fmt.Errorf("invalid key length: must be 32 bytes, but got %d for key %s", len(key), licenseId)
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create AES cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(crand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(securityCode), nil)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func (a AuthUsecase) issueToken(ctx context.Context, user model.TbUserEntity, tokenType dto.TokenType) (dto.Token, error) {
	params, err := a.issueParams(ctx, user.CorpID)
	if err != nil {
		a.log.Errorf("Get issueToken config failed. err=%v", err)
		return dto.Token{}, err
	}

	userName := user.DisplayName
	if userName == "" {
		userName = user.Name
	}
	var encryptedSecurityCode string
	if user.SecurityCode != "" {
		encryptedSecurityCode, err = encryptSecurityCode(user.SecurityCode, "7464fdfe3f795a01affda2848ece9f79")
		if err != nil {
			a.log.Errorf("encryptSecurityCode failed. err=%v", err)
			return dto.Token{}, err
		}
	}
	userInfo := &dto.IssueUserInfo{
		CorpId:        user.CorpID,
		UserId:        user.ID,
		Identifier:    user.Identify,
		GroupId:       user.GroupID,
		PreferredName: userName,
		UserName:      user.Name,
		Phone:         user.Phone,
		Email:         user.Email,
		// SecurityCode:  user.SecurityCode,
		SecurityCode: encryptedSecurityCode,
	}

	// 生成SessionID，在生成token前设置
	sessionID := uuid.New().String()
	userInfo.SessionID = sessionID

	// 签发token
	issuer := jwt_util.NewJwtIssuer(jwt_util.Ecdsa256)
	refreshToken, refreshJti, err := a.getRefreshTokenWithJTI(ctx, issuer, params, userInfo)
	if err != nil {
		a.log.Errorf("getRefreshToken failed. err=%v", err)
		return dto.Token{}, err
	}

	accessToken, jti, err := a.getAccessTokenWithJTI(ctx, issuer, params, userInfo)
	if err != nil {
		a.log.Errorf("getAccessToken failed. err=%v", err)
		return dto.Token{}, err
	}

	// 创建会话追踪记录
	clientType := ""
	deviceID := ""
	userAgent := ""

	// 从上下文获取客户端信息
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if values := md.Get("client-type"); len(values) > 0 {
			clientType = values[0]
		}
		if values := md.Get("device-id"); len(values) > 0 {
			deviceID = values[0]
		}
		if values := md.Get("user-agent"); len(values) > 0 {
			userAgent = values[0]
		}
	}

	clientIP := common.GetClientHost(ctx)

	// 如果从metadata中获取不到User-Agent，尝试从HTTP请求头中获取
	if userAgent == "" {
		if httpReq, err := common.GetHttpRequest(ctx); err == nil {
			userAgent = httpReq.Header.Get("User-Agent")
		}
	}

	// 解析User-Agent获取详细信息
	var detectedClientType string
	var clientCategory dto.ClientCategory

	if userAgent != "" {
		// 使用 ParseUserAgentSimple 作为简单解析方案
		parsed := utils.ParseUserAgentSimple(userAgent)
		detectedClientType = string(parsed.ClientType)
		clientCategory = parsed.ClientCategory
	}

	// 如果没有User-Agent或解析失败，使用传入的clientType或默认值
	if detectedClientType == "" || detectedClientType == string(dto.ClientTypeUnknown) {
		switch clientType {
		case "pc", "desktop", "web":
			detectedClientType = string(dto.ClientTypeWindows)
			clientCategory = dto.ClientCategoryPC
		case "mobile", "app", "android", "ios":
			detectedClientType = string(dto.ClientTypeAndroid)
			clientCategory = dto.ClientCategoryMobile
		default:
			detectedClientType = string(dto.ClientTypeWindows)
			clientCategory = dto.ClientCategoryPC
		}
	}

	a.log.Debugf("User-Agent: %s -> OS: %s, Category: %s, IP: %s", userAgent, detectedClientType, clientCategory, clientIP)

	// 检查客户端数量限制
	clientLimitParam := dto.ClientLimitCheckParam{
		CorpID:         user.CorpID,
		UserID:         user.ID,
		ClientType:     dto.ClientType(detectedClientType),
		ClientCategory: clientCategory,
	}

	a.log.Infof("Checking client limit for user %s (corp: %s): clientType=%s, clientCategory=%s",
		user.ID, user.CorpID, detectedClientType, clientCategory)

	err = a.clientLimitUc.CheckClientLimit(ctx, clientLimitParam)
	if err != nil {
		a.log.Errorf("Client limit check failed for user %s: %v", user.ID, err)
		return dto.Token{}, err
	}

	a.log.Infof("Client limit check passed for user %s", user.ID)

	sessionParam := dto.CreateSessionTrackerParam{
		ID:             sessionID, // 使用之前生成的sessionID
		JWTId:          jti,
		RefreshJWTId:   refreshJti, // 新增：保存刷新令牌的JWT ID
		UserID:         user.ID,
		CorpID:         user.CorpID,
		ClientType:     dto.ClientType(detectedClientType),
		ClientCategory: clientCategory,
		DeviceID:       deviceID,
		ExpiresAt:      time.Now().Add(params.AccessTokenDuration),
		IPAddress:      clientIP,
	}

	err = a.trackerRepo.CreateSessionTracker(ctx, sessionParam)
	if err != nil {
		a.log.Errorf("Create session tracker failed. err=%v", err)
		// 不影响登录流程，只记录错误
	}
	return dto.Token{
		AccessToken:     accessToken,
		ExpireIn:        int64(params.AccessTokenDuration.Seconds()),
		RefreshToken:    refreshToken,
		RefreshExpireIn: int64(params.RefreshTokenDuration.Seconds()),
		TokenType:       dto.TokenTypeBear,
	}, nil

}

func (a AuthUsecase) ThirdLogin(ctx context.Context, param dto.ThirdLoginForm) (any, dto.LoginAuthInfo, error) {
	var loginParam dto.LoginForm
	loginParam.CorpId = param.CorpId
	loginParam.IdpId = param.IdpId
	redirectUri := param.RedirectUri
	idp, err := a.idpRepo.GetIDP(ctx, param.CorpId, param.IdpId)
	isEnhancement := false
	var user = &model.TbUserEntity{}
	if err != nil {
		a.log.Errorf("GetIDP failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	if idp.SourceID == dto.AssistIDPSourceID {
		a.log.Errorf("only handle main idp=%+v", idp)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	if !idp.Enable {
		idpName := idp.Name
		if idpName == "" {
			idpName = "当前认证源"
		}
		errMsg := fmt.Sprintf("认证源 '%s' 已被禁用，请联系系统管理员", idpName)
		a.log.Errorf("disable idp=%s", idpName)
		return nil, dto.LoginAuthInfo{}, pb.ErrorDisableError(errMsg)
	}
	// 判断已拿到用户信息，则直接返回，给ThirdLogin接口调用
	fmt.Println("param.AuthWeb.AuthWebToken=", param.AuthWeb.AuthWebToken)
	if param.AuthWeb.AuthWebToken != "" {
		jsonString, err := a.repo.Get(ctx, param.AuthWeb.AuthWebToken)
		if err != nil {
			errMsg := "认证流程已超时，请重新认证"
			a.log.Errorf("redis failed. err=%v", err)
			return nil, dto.LoginAuthInfo{}, pb.ErrorCacheError(errMsg)
		}
		ret := make(map[string]interface{})
		err = json.Unmarshal([]byte(jsonString), &ret)
		if err == nil {
			fmt.Println("AuthWebToken.ret=", ret["result"])
			// 修复：正确处理authInfo结构体的恢复
			var authInfo dto.LoginAuthInfo
			if authInfoData, exists := ret["authInfo"]; exists {
				// 将map[string]interface{}转换为JSON再反序列化为结构体
				authInfoBytes, err := json.Marshal(authInfoData)
				if err == nil {
					if err = json.Unmarshal(authInfoBytes, &authInfo); err != nil {
						a.log.Warnf("恢复authInfo结构体失败: %v", err)
					} else {
						a.log.Debugf("成功从缓存恢复authInfo: NeedSecondary=%v, UserId=%v",
							authInfo.NeedSecondary, authInfo.UserId)
					}
				}
			}
			a.log.Debugf("authInfo=%+v", authInfo)
			return ret["result"], authInfo, nil
		}
	}

	// 根据 mainIdp 类型 选择 idp

	// 获取第三方服务器属性
	attrs, err := a.idpRepo.GetIDPAttr(ctx, idp.ID)
	if err != nil {
		a.log.Errorf("GetIDPAttr failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	idpAttrs := dto.KVsToIdpAttr(attrs)
	var externalUser = &dto.ExternalUser{}
	idpType := dto.GetIdpType(idp.Type, idp.TemplateType)
	defer func() {
		errMsg := ""
		loginLogType := dto.LoginLogType
		desc := ""
		if err != nil {
			errMsg = err.Error()
			loginLogType = dto.LoginErrorLogType
			desc = dto.ErrReasonChineseMap[kratosErrors.FromError(err).Reason]
		}
		if dto.IDPType(idpType) == "ldap" || dto.IDPType(idpType) == "msad" {
			user.Name = param.AdUsername
		}
		loginLogParam := dto.CreateLoginLogParam{
			Id:          uuid.New().String(),
			CorpId:      param.CorpId,
			Error:       errMsg,
			IpAddress:   common.GetClientHost(ctx),
			Type:        loginLogType,
			ClientId:    dto.AuthLoginLog,
			EventTime:   time.Now().UnixMilli(),
			UserName:    user.Name,
			SourceId:    idp.SourceID,
			Description: desc,
		}
		// 增强认证触发，不收集日志，在增强认证的地方收集日志
		if !isEnhancement {
			err = a.CreateUserLoginLog(ctx, loginLogParam)
			if err != nil {
				a.log.Errorf("CreateLoginLogFailed err=%v", err)
				return
			}
		}
	}()

	switch dto.IDPType(idpType) {
	case dto.IDPTypeQiYeWx:
		// 获取微信用户信息
		idProvider := provider.NewWxIDProvider(idp.ID, idpAttrs.CorpId, idpAttrs.Secret, a.idpRepo)
		wxUserInfo, wxErr := idProvider.GetUserInfo(param.AuthWeb.AuthWebCode)
		if wxErr != nil {
			err = wxErr
			a.log.Errorf("wx idp GetUserInfo failed. code=%s err=%v", param.AuthWeb.AuthWebCode, err)
			return nil, dto.LoginAuthInfo{}, err
		}
		externalUser.Userid = wxUserInfo.Userid
		externalUser.Email = wxUserInfo.Email
		externalUser.Mobile = wxUserInfo.Mobile
		externalUser.Name = wxUserInfo.Name
		externalUser.MainDepartment = string(wxUserInfo.MainDepartment)
	case dto.IDPTypeFeiShu:
		// 获取飞书用户信息
		idProvider := feishu.NewFsIDProvider(idp.ID, idpAttrs.AppId, idpAttrs.FeishuConfig.AppSecret, a.idpRepo)
		fsUserInfo, fsErr := idProvider.GetUserInfo(param.AuthWeb.AuthWebCode, redirectUri)
		if fsErr != nil {
			err = fsErr
			a.log.Errorf("fs idp GetUserInfo failed. code=%s  err=%v", param.AuthWeb.AuthWebCode, err)
			return nil, dto.LoginAuthInfo{}, err
		}
		externalUser.Userid = fsUserInfo.UserId
		externalUser.Email = fsUserInfo.Email
		externalUser.Mobile = fsUserInfo.Mobile
		externalUser.Name = fsUserInfo.Name
	case dto.IDPTypeDingtalk:
		// 获取钉钉用户信息
		dingtalkUserInfo, dErr := dingtalk.GetUserInfoBySNSCode(idpAttrs.DingtalkConfig.AppKey, idpAttrs.DingtalkConfig.AppSecret, param.AuthWeb.AuthWebCode)
		if dErr != nil {
			err = dErr
			a.log.Errorf("dingtalk idp GetUserInfo failed. err=%v", err)
			return nil, dto.LoginAuthInfo{}, err
		}
		externalUser.Userid = dingtalkUserInfo.UserId
		externalUser.Email = dingtalkUserInfo.Email
		externalUser.Mobile = dingtalkUserInfo.Mobile
		externalUser.Name = dingtalkUserInfo.Name

	case dto.IDPTypeWeb, dto.IDPTypeEmail, dto.IDPTypeMsad, dto.IDPTypeLdap:
		// 验证ad域下的用户名密码是否正确
		adConf := ad.ActiveDirectory{
			Server:   idpAttrs.ServerAddress,
			DN:       idpAttrs.SearchEntry,
			UserName: idpAttrs.AdministratorAccount,
			Pwd:      idpAttrs.AdministratorPassword,
		}
		if param.Encryption == "rsa" {
			param.AdUsername, err = encrypt.RsaDecrypt(param.AdUsername)
			param.AdPwd, err = encrypt.RsaDecrypt(param.AdPwd)
		}

		var userInfo dto.ExternalUser // 根据实际情况替换具体类型
		if dto.IDPType(idp.Type) == dto.IDPTypeLdap {
			userInfo, err = ad.GetLdapUser(adConf, param.AdUsername, param.AdPwd)
			if err != nil {
				a.log.Errorf("ldap auth err:%v", err)
				return nil, dto.LoginAuthInfo{}, err
			}
		} else {
			userInfo, err = ad.GetAdUser(adConf, param.AdUsername, param.AdPwd)
			if err != nil {
				a.log.Errorf("adms auth err:%v", err)
				return nil, dto.LoginAuthInfo{}, err
			}
		}

		externalUser = &userInfo
	case dto.IDPTypeOAuth2:
		// 用于回调处理，OAuth2Callback方法调用
		logger := log.With(log.GetLogger())
		oauth2Provider, oauth2Err := oauth2.NewOAuth2Provider(
			idp.ID,
			idp.Name,
			idpAttrs.GlobalData,
			idpAttrs.CodeData,
			idpAttrs.UserData,
			idpAttrs.LogoutData,
			idpAttrs.LogoutOpen == "1",
			redirectUri,
			logger,
		)
		if oauth2Err != nil {
			err = oauth2Err
			a.log.Errorf("oauth2 provider 创建失败. err=%v", err)
			return nil, dto.LoginAuthInfo{}, err
		}

		// 处理OAuth2回调
		oauth2UserInfo, Rerr := oauth2Provider.HandleCallback(ctx, param.AuthWeb.AuthWebCode, "")
		if Rerr != nil {
			err = Rerr
			a.log.Errorf("oauth2 处理回调失败. code=%s err=%v", param.AuthWeb.AuthWebCode, err)
			return nil, dto.LoginAuthInfo{}, err
		}

		// 如果启用了登出功能，缓存用户信息
		if idpAttrs.LogoutOpen == "1" {
			// 构建缓存键
			cacheKey := fmt.Sprintf("oauth2_logout_info:%s:%s", oauth2UserInfo.Userid, idp.ID)

			exec := oauth2Provider.GetExecutor()
			userInfo := exec.GetUserInfo()
			envInfo := exec.GetEnvVars()
			globalInfo := exec.GetGlobalVars()

			logoutInfo := make(map[string]interface{})
			for _, item := range []struct {
				prefix string
				iface  interface{}
			}{
				{"", userInfo},
				{"{Env.", envInfo},
				{"{Global.", globalInfo},
			} {
				switch m := item.iface.(type) {
				case map[string]interface{}:
					for k, v := range m {
						key := k
						logoutInfo[key] = v
					}
				case map[string]string:
					for k, v := range m {
						logoutInfo[item.prefix+k+"}"] = v
					}
				}
			}
			// 序列化用户信息
			if logoutInfoBytes, Jerr := json.Marshal(logoutInfo); Jerr == nil {
				//固定15天过期，不在RefreshToken时延长
				//每次登录都会缓存用户登出信息
				a.repo.Set(ctx, cacheKey, string(logoutInfoBytes), 15*24*time.Hour)
				a.log.Debugf("已缓存用户登出信息: %s", cacheKey)
			} else {
				err = Jerr
				a.log.Warnf("序列化用户信息失败: %v", Jerr)
			}
		}

		// 设置外部用户信息
		externalUser = oauth2UserInfo
	case dto.IDPTypeCas:
		// 用于回调处理，OAuth2Callback方法调用
		logger := log.With(log.GetLogger())
		casProvider, caserr := cas.NewCasProvider(
			idp.ID,
			idp.Name,
			idpAttrs.AuthData,
			idpAttrs.FieldMap,
			redirectUri,
			logger,
		)
		if caserr != nil {
			err = caserr
			a.log.Errorf("cas provider 创建失败. err=%v", err)
			return nil, dto.LoginAuthInfo{}, err
		}

		// 处理Cas回调
		casUserInfo, cRerr := casProvider.HandleCallback(ctx, param.AuthWeb.AuthWebCode, "")
		if cRerr != nil {
			err = cRerr
			a.log.Errorf("cas 处理回调失败. code=%s err=%v", param.AuthWeb.AuthWebCode, err)
			return nil, dto.LoginAuthInfo{}, err
		}

		// 设置外部用户信息
		externalUser = casUserInfo
	default:
		a.log.Errorf("idp type=%v not support.", idp.Type)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	// 根据 idpId 查询绑定来源 -> 查询是否已经有对应用户
	allSource, idpErr := a.idpRepo.GetAllSourceOfBind(ctx, param.CorpId, idp.ID)
	if idpErr != nil {
		err = idpErr
		a.log.Errorf("GetAllSourceOfBind failed. err=%v", idpErr)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	if len(allSource) != 1 {
		a.log.Errorf("idp bind source error. allSource=%v", allSource)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	// 根据 idpId 查询绑定根目录，三方登陆只能绑定一个根目录
	groups, gerr := a.idpRepo.GetIDPBindGroup(ctx, param.CorpId, idp.ID)
	if gerr != nil {
		a.log.Errorf("GetIDPBindGroup failed. err=%v", gerr)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	if len(groups) != 1 {
		a.log.Errorf("IDP Bind group not correct. groups=%v", groups)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	rootGroup := groups[0]
	// 首先查外部表中是否有对应用户
	var localUser *model.TbExternalUser
	// 特殊处理：OAuth2、Cas类型的认证支持自动创建用户
	if dto.IDPType(idpType) == dto.IDPTypeOAuth2 || dto.IDPType(idpType) == dto.IDPTypeCas {
		if externalUser.Type == "" {
			source, e := a.userSource.GetUserSource(ctx, param.CorpId, idp.SourceID)
			if e != nil {
				return nil, dto.LoginAuthInfo{}, e
			}
			externalUser.Type = source.SourceType
		}
		localUser, err = a.createThirdUserIfNotExist(ctx, rootGroup, allSource, externalUser, param.CorpId, idpAttrs.SearchMap)
		if err != nil {
			a.log.Errorf("OAuth2用户创建或查询失败: %v", err)
			return nil, dto.LoginAuthInfo{}, err
		}
	} else {
		// 其他类型的IDP按原流程处理
		localUser, err = a.userRepo.GetExternalUser(ctx, rootGroup.RootGroupID, externalUser.Userid, idpType)
		if err != nil {
			a.log.Errorf("GetExternalUser failed. err=%v", err)

			// 检查是否是"记录未找到"错误
			if errors.Is(err, gorm.ErrRecordNotFound) || pb.IsRecordNotFound(err) {
				// 针对不同IDP类型提供具体的错误信息
				var idpTypeDesc string
				switch dto.IDPType(idpType) {
				case dto.IDPTypeQiYeWx:
					idpTypeDesc = "企业微信"
				case dto.IDPTypeFeiShu:
					idpTypeDesc = "飞书"
				case dto.IDPTypeDingtalk:
					idpTypeDesc = "钉钉"
				default:
					idpTypeDesc = "第三方"
				}

				a.log.Debugf("用户尝试使用未同步的%s账号名:%s id%s登录,请先同步组织架构及用户数据后再尝试登录",
					idpTypeDesc, externalUser.Name, externalUser.Userid)
				errorMsg := fmt.Sprintf("未找到%s用户(%s)，请先同步组织架构及用户数据后再尝试登录",
					idpTypeDesc, externalUser.Name)
				return nil, dto.LoginAuthInfo{}, pb.ErrorUserNotFound(errorMsg)
			}

			// 其他类型错误仍使用通用错误
			return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
		}
	}
	// 查实际本地导入的用户
	user, err = a.userRepo.QueryUserEntity(ctx, param.CorpId, localUser.LocalUserID)
	if err != nil {
		a.log.Errorf("QueryUserEntity failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, pb.ErrorLoginError(loginParamError)
	}
	if err := a.isUserValidate(user); err != nil {
		return nil, dto.LoginAuthInfo{}, err
	}
	// 这里只确认用户相关认证策略是否存在即可，后续添加双因素认证再获取实际的policy
	_, err = a.getAuthPolicyOfUser(ctx, param.CorpId, idp.ID, user)
	if err != nil {
		a.log.Errorf("getAuthPolicyOfUser failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}

	// 登录日志
	loginParam.UserId = user.ID
	// 获取认证所需基础组件
	authBasic, err := a.getAuthBasic(ctx, loginParam, idp)
	if err != nil {
		a.log.Errorf("getAuthBasic failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	userName := user.Name
	if user.DisplayName != "" {
		userName = user.DisplayName
	}
	// 删除应用二次认证的sms_idp
	err = a.DelSecondarySmsIdp(ctx, authBasic.User.ID)
	if err != nil {
		a.log.Errorf("del secondary failed. err=%v", err)
	}
	// 判断是否存在二次认证
	secondary, err := a.getSecondaryAuth(ctx, authBasic.Policy, authBasic.User)
	if err != nil {
		a.log.Errorf("getSecondaryAuth failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	if len(secondary) != 0 {
		secondaryCategory, secondaryType := a.determineSecondaryType(secondary)
		return secondary, dto.LoginAuthInfo{
			UserId:            authBasic.User.ID,
			Name:              userName,
			NeedSecondary:     true,
			Phone:             authBasic.User.Phone,
			Email:             authBasic.User.Email,
			CurrentSecret:     authBasic.User.CurrentSecret,
			SecondaryCategory: secondaryCategory,
			SecondaryType:     secondaryType,
		}, nil
	}
	// 根据当前主认证的idp类型，判断是否存在增强认证的策略,取最长链路
	authEnhancement, err := a.getAuthEnhancement(ctx, param.IdpId, authBasic.User.RootGroupID, param.CorpId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		a.log.Errorf("getAuthEnhancement failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	// 存在增强认证，则验证条件是否符合，符合则再次校验增强认证
	if authEnhancement.IdpId != "" {
		authEnhancementInfo := a.checkFactor(ctx, authEnhancement.Factor, authBasic.User.ID)
		if authEnhancementInfo.IsRegionEnhancement && authEnhancementInfo.IsTimeEnhancement {
			// 返回增强认证
			idp, err := a.idpRepo.GetIDP(ctx, param.CorpId, authEnhancement.IdpId)
			if err != nil {
				a.log.Errorf("getAuthEnhancementIdp Failed. err=%v", err)
				return nil, dto.LoginAuthInfo{}, err
			}
			enhancementIdps := []*model.TbIdentityProvider{idp}
			secondaryCategory, secondaryType := a.determineSecondaryType(enhancementIdps)
			return []*model.TbIdentityProvider{idp}, dto.LoginAuthInfo{
				UserId:              authBasic.User.ID,
				Name:                userName,
				NeedSecondary:       true,
				Phone:               authBasic.User.Phone,
				Email:               authBasic.User.Email,
				SecondaryCategory:   secondaryCategory,
				SecondaryType:       secondaryType,
				AuthEnhancementInfo: authEnhancementInfo}, nil
		}
		isEnhancement = authEnhancementInfo.IsRegionEnhancement && authEnhancementInfo.IsTimeEnhancement
	}
	if dto.IDPType(idpType) == dto.IDPTypeOAuth2 {
		a.saveUserIdpMapping(ctx, authBasic.User.ID, param.IdpId, string(dto.IDPTypeOAuth2))
	}
	// 授权
	authzResp, err := a.authz(ctx, authBasic.User, param.AuthBasicForm)
	if err != nil {
		a.log.Errorf("authz failed. err=%v", err)
		return nil, dto.LoginAuthInfo{}, err
	}
	return authzResp, dto.LoginAuthInfo{}, nil
}

func (a AuthUsecase) isUserValidate(user *model.TbUserEntity) error {
	if user == nil {
		return pb.ErrorDisableError("user is nil")
	}
	if !user.Enable {
		return pb.ErrorDisableError("用户 %s 已禁用", user.Name)
	}
	if dto.ExpireType(user.ExpireType) == dto.ExpireTypeCustom && user.ExpireEnd.Before(time.Now()) {
		return pb.ErrorExpireError("用户 %s 已过期", user.Name)
	}
	return nil
}

// AssistAuth 处理辅助认证
func (a AuthUsecase) AssistAuth(ctx context.Context) {
	panic("todo")
}

func (a AuthUsecase) GetPublicKeyOfCorp(ctx context.Context, corpId string) (any, error) {
	publicKey, err := a.repo.GetEcdsaPublicKeyByCorpId(ctx, corpId)
	if err != nil || publicKey.PublicKey == "" {
		a.log.Errorf("GetEcdsaPublicKeyByCorpId failed. err=%v, publicKey=%+v", err, publicKey)
		return nil, pb.ErrorRecordNotFound("public key not found.")
	}
	parser := jwt_util.NewJwtParse(jwt_util.Ecdsa256)
	key, err := parser.LoadStringPublicKey("-----BEGIN PUBLIC KEY-----\n" + publicKey.PublicKey + "\n-----END PUBLIC KEY-----")
	if err != nil {
		a.log.Errorf("LoadPublicKey failed. err=%v", err)
		return nil, err
	}
	return key, nil
}

func (a AuthUsecase) GetAdminEcdsaPublicKey(ctx context.Context) (any, error) {
	publicKey, err := a.repo.GetAdminEcdsaPublicKey(ctx)
	if err != nil || publicKey.PublicKey == "" {
		a.log.Errorf("GetEcdsaPublicKeyByCorpId failed. err=%v, publicKey=%+v", err, publicKey)
		return nil, pb.ErrorRecordNotFound("public key not found.")
	}
	parser := jwt_util.NewJwtParse(jwt_util.Ecdsa256)
	key, err := parser.LoadStringPublicKey("-----BEGIN PUBLIC KEY-----\n" + publicKey.PublicKey + "\n-----END PUBLIC KEY-----")
	if err != nil {
		a.log.Errorf("LoadPublicKey failed. err=%v", err)
		return nil, err
	}
	return key, nil
}

func (a AuthUsecase) RefreshToken(ctx context.Context) (dto.Token, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		a.log.Errorf("GetCorpId failed. err=%v", err)
		return dto.Token{}, err
	}
	params, err := a.issueParams(ctx, corpId)
	if err != nil {
		a.log.Errorf("Get issueToken config failed. err=%v", err)
		return dto.Token{}, err
	}

	var refreshToken string
	// 获取当前refresh claim
	refreshClaims, err := a.GetCurrentClaim(ctx)
	if err != nil {
		return dto.Token{}, err
	}

	// 通过refresh token的JTI获取对应的会话信息
	sessionInfo, err := a.trackerRepo.GetSessionByRefreshJWTId(ctx, refreshClaims.ID)
	if err != nil {
		a.log.Errorf("GetSessionByRefreshJWTId failed. err=%v", err)
		return dto.Token{}, err
	}

	// 将旧的access token JTI加入黑名单
	oldAccessTokenJTI := sessionInfo.JWTId
	if oldAccessTokenJTI != "" {
		// 使用blacklistRepo统一管理黑名单
		expiredAt := time.Now().Add(params.AccessTokenDuration)
		if err := a.blacklistRepo.AddToBlacklist(ctx, oldAccessTokenJTI, expiredAt); err != nil {
			a.log.Errorf("Failed to blacklist old access token: %v", err)
			// 不影响刷新流程，只记录错误
		} else {
			a.log.Infof("Old access token blacklisted: %s", oldAccessTokenJTI)
		}
	}

	// 获取refresh剩余duration
	remainRefreshDuration, err := a.GetCurrentTokenDuration(refreshClaims)
	if err != nil {
		a.log.Errorf("GetRefreshTokenDuration failed. err=%v", err)
		return dto.Token{}, err
	}

	userInfo := &dto.IssueUserInfo{
		CorpId:        corpId,
		UserId:        refreshClaims.Subject,
		Identifier:    refreshClaims.Identifier,
		GroupId:       refreshClaims.GroupId,
		PreferredName: refreshClaims.PreferredUsername,
		UserName:      refreshClaims.Name,
		Phone:         refreshClaims.Phone,
		Email:         refreshClaims.Email,
		SessionID:     sessionInfo.ID, // 使用会话中的SessionID
	}

	issuer := jwt_util.NewJwtIssuer(jwt_util.Ecdsa256)
	//如果刷新token即将，过期，签发新的refresh token,否则继续使用原刷新token
	if remainRefreshDuration <= params.RefreshTokenLeastDuration {
		refreshToken, err = a.getRefreshToken(ctx, issuer, params, userInfo)
		remainRefreshDuration = params.RefreshTokenDuration
	} else {
		// 获取当前token
		refreshToken, err = common.GetToken(ctx)
		if err != nil {
			a.log.Errorf("GetTokenFromCtx failed. err=%v", err)
			return dto.Token{}, err
		}
	}

	// 签发新的access token
	accessToken, newAccessTokenJTI, err := a.getAccessTokenWithJTI(ctx, issuer, params, userInfo)
	if err != nil {
		a.log.Errorf("getAccessToken failed. err=%v", err)
		return dto.Token{}, err
	}

	// 更新会话中的access token JTI
	err = a.trackerRepo.UpdateSessionAccessToken(ctx, sessionInfo.ID, newAccessTokenJTI)
	if err != nil {
		a.log.Errorf("UpdateSessionAccessToken failed: %v", err)
		// 不影响 refresh token 流程，只记录错误
	}

	// 更新会话的最后活跃时间（这个方法现在通过新的JTI更新）
	err = a.trackerRepo.UpdateLastActiveTime(ctx, newAccessTokenJTI)
	if err != nil {
		a.log.Errorf("UpdateLastActiveTime failed: %v", err)
		// 不影响 refresh token 流程，只记录错误
	}

	// RefreshToken中不再延长缓存
	// userId := refreshClaims.Subject
	// if userId != "" {
	// 	a.tryExtendOAuth2LogoutCache(ctx, userId)
	// }
	updateInfo := model.TbUserEntity{
		ActiveTime: time.Now(),
	}
	err = a.userRepo.UpdateUserRow(ctx, userInfo.UserId, updateInfo)
	if err != nil {
		return dto.Token{}, err
	}

	return dto.Token{
		AccessToken:     accessToken,
		ExpireIn:        int64(params.AccessTokenDuration.Seconds()),
		RefreshToken:    refreshToken,
		RefreshExpireIn: int64(remainRefreshDuration.Seconds()),
		TokenType:       dto.TokenTypeBear,
	}, nil
}

func (a AuthUsecase) Cache(ctx context.Context, cacheType dto.CacheType, data any, ttl int) (string, error) {
	uniqKey := uuid.New().String()
	switch cacheType {
	case dto.CacheTypeWxIdp:
		if err := a.repo.SetNX(ctx, uniqKey, data, dto.WxWebCacheTTL); err != nil {
			return "", err
		}
	case dto.CacheTypeFsIdp:
		if err := a.repo.SetNX(ctx, uniqKey, data, dto.FsWebCacheTTL); err != nil {
			return "", err
		}
	case dto.CacheTypeDingtalkIdp:
		if err := a.repo.SetNX(ctx, uniqKey, data, dto.DtWebCacheTTL); err != nil {
			return "", err
		}
	case dto.CacheTypeSmsIdp:
		if err := a.repo.SetNX(ctx, uniqKey, data, time.Duration(ttl)); err != nil {
			return "", err
		}
	case dto.CacheTypeOAuth2Idp:
		if err := a.repo.SetNX(ctx, uniqKey, data, time.Duration(ttl)*time.Second); err != nil {
			return "", err
		}
	case dto.CacheTypeCasIdp:
		if err := a.repo.SetNX(ctx, uniqKey, data, time.Duration(ttl)*time.Second); err != nil {
			return "", err
		}
	default:
		return "", pb.ErrorTypeError("cache type=%v not support", cacheType)
	}
	return uniqKey, nil
}

func (a *AuthUsecase) CacheTest(ctx context.Context, testId string, data string, ttl int) error {
	cacheKey := fmt.Sprintf("%s:%s", string(dto.CacheTypeOAuth2Test), testId)
	return a.repo.Set(ctx, cacheKey, data, time.Duration(ttl)*time.Second)
}

// GetTestCache 获取测试结果
func (a *AuthUsecase) GetTestCache(ctx context.Context, testId string) (string, error) {
	cacheKey := fmt.Sprintf("%s:%s", string(dto.CacheTypeOAuth2Test), testId)
	return a.repo.Get(ctx, cacheKey)
}

func (a AuthUsecase) CacheAuthEnhancement(ctx context.Context, uniqKey string, cacheType dto.CacheType, data dto.AuthEnhancementInfo, ttl int) error {
	dataByte, err := json.Marshal(data)
	if err != nil {
		return err
	}
	switch cacheType {
	case dto.CacheTypeSmsIdp:
		if err := a.repo.SetNX(ctx, fmt.Sprintf("%s_%s", dto.AuthEnhancementPrefix, uniqKey), string(dataByte), time.Duration(ttl)); err != nil {
			return err
		}
	default:
		return pb.ErrorTypeError("cache type=%v not support", cacheType)
	}
	return nil
}

func (a AuthUsecase) SetCorpUserToken(c context.Context, corpId string, userId string, token string, duration time.Duration) error {
	corp, err := a.corpRepo.GetCorpByID(c, corpId)
	if err != nil {
		a.log.Errorf("GetCorpId failed, err=%v", err)
		return err
	}
	corpKey := a.GetCorpKey(corp.Name)
	userKey := a.GetUserKey(userId)
	err = a.repo.SAdd(c, corpKey, userId)
	if err != nil {
		a.log.Errorf("SAddCorp failed, err=%v", err)
		return err
	}
	err = a.repo.Set(c, userKey, dto.TokenBlackDefaultValue, duration)
	if err != nil {
		a.log.Errorf("RPushUserToken failed, err=%v", err)
		return err
	}
	return nil
}

func (a AuthUsecase) GetUserCount(ctx context.Context, corpId string) (dto.UserCount, error) {
	user, err := a.userRepo.ListUser(ctx, corpId)
	if err != nil {
		a.log.Errorf("QueryUserForCorpId failed. err=%v", err)
		return dto.UserCount{}, err
	}
	corp, err := a.corpRepo.GetCorpByID(ctx, corpId)
	if err != nil {
		a.log.Errorf("QueryCorpById failed. err=%v", err)
		return dto.UserCount{}, err
	}
	activeCorpKey := a.GetCorpKey(corp.Name)
	loginUser, err := a.repo.SMembers(ctx, activeCorpKey)
	if err != nil {
		a.log.Errorf("GetLoginUserFromRedis failed. err=%v, corp-id=%s", err, corpId)
		return dto.UserCount{}, err
	}
	var onlineUser int64
	var needDelRedisKeys []string
	for _, v := range loginUser {
		key := a.GetUserKey(v)
		_, err := a.repo.Get(ctx, key)
		if err != nil {
			if err == redis.Nil {
				// 获取set中需要删掉的key
				needDelRedisKeys = append(needDelRedisKeys, v)
			} else {
				a.log.Warnf("GetUserTokenFromRedis failed. err=%v, user-id=%s", err, v)
			}
			continue
		}
		onlineUser++
	}
	if len(needDelRedisKeys) > 0 {
		err = a.repo.SRem(ctx, activeCorpKey, needDelRedisKeys)
		if err != nil {
			a.log.Errorf("DelUnUseRedisKey failed. err=%v", err)
			return dto.UserCount{}, err
		}
	}
	offlineCount := uint32(len(user)) - uint32(onlineUser)
	if uint32(len(user)) < uint32(onlineUser) {
		offlineCount = 0
	}
	return dto.UserCount{
		TotalCount:   uint32(len(user)),
		OnlineCount:  uint32(onlineUser),
		OfflineCount: offlineCount,
	}, nil
}

func (a AuthUsecase) Logout(ctx context.Context, corpId, token string) (string, error) {
	// 1. 获取当前用户的认证方式信息
	userId, err := common.GetUserId(ctx)
	if err != nil {
		// 仅记录日志，不中断流程
		a.log.Warnf("获取用户ID失败,无法执行OAuth2登出: %v", err)
	} else {
		// 根据用户ID查询用户的IDP信息
		idpId, err := a.getIdpIdFromContext(ctx, userId)
		if err == nil && idpId != "" {
			// 处理OAuth2登出
			idp, err := a.idpRepo.GetIDP(ctx, corpId, idpId)
			idpType := dto.GetIdpType(idp.Type, idp.TemplateType)
			if err == nil && idpType == string(dto.IDPTypeOAuth2) {
				// 尝试执行OAuth2登出流程
				redirectURL, err := a.executeOAuth2Logout(ctx, idp, token)
				if err == nil && redirectURL != "" {
					// 返回重定向URL
					return redirectURL, nil
				}
			}
		}
	}
	idpKey := fmt.Sprintf("user_idp:%s", userId)
	if err := a.repo.Del(ctx, idpKey); err != nil && !errors.Is(err, redis.Nil) {
		// 仅记录日志，不影响注销流程
		a.log.Warnf("清理用户IDP映射失败: %v", err)
	}
	// } else {
	// 	a.log.Debugf("已清理用户(%s)的IDP映射", userId)
	// }

	// 使用新的JWT黑名单机制替代旧的token黑名单
	claim, err := a.GetCurrentClaim(ctx)
	if err != nil {
		a.log.Errorf("GetCurrentClaim failed. err=%v", err)
		return "", err
	}

	// 检查JWT是否包含JTI字段
	if claim.ID == "" {
		a.log.Warnf("JWT token does not contain JTI, falling back to legacy blacklist")
		// 如果没有JTI，使用旧的黑名单机制（向后兼容）
		corpBlackTokenKey := a.GetBlackTokenKey(corpId, token)
		duration, err := a.GetCurrentTokenDuration(claim)
		if err != nil {
			a.log.Errorf("GetCurrentClaimDuration failed, corp-id=%s, err=%v", corpId, err)
			return "", err
		}
		err = a.repo.Set(ctx, corpBlackTokenKey, dto.TokenBlackDefaultValue, duration)
		if err != nil {
			a.log.Errorf("SAddUserTokenToRedis failed, corp-id=%s, err=%v", corpId, err)
			return "", err
		}
	} else {
		// 使用新的JWT黑名单机制
		expiredAt := claim.RegisteredClaims.ExpiresAt.Time
		err = a.blacklistRepo.AddToBlacklist(ctx, claim.ID, expiredAt)
		if err != nil {
			a.log.Errorf("AddToBlacklist failed, jti=%s, err=%v", claim.ID, err)
			return "", err
		}
		a.log.Debugf("Token added to JWT blacklist successfully, jti=%s", claim.ID)

		// 更新session表中的对应记录状态为logout
		err = a.trackerRepo.LogoutSession(ctx, claim.ID, "user_logout")
		if err != nil {
			// 记录错误但不影响登出流程，因为JWT黑名单已经生效
			a.log.Errorf("Failed to update session status to logout, jti=%s, err=%v", claim.ID, err)
		} else {
			a.log.Debugf("Session status updated to logout successfully, jti=%s", claim.ID)
		}
	}
	return "", nil
}

func (a AuthUsecase) CheckTokenOfRedis(ctx context.Context, token string) (bool, error) {
	// 首先尝试解析JWT获取JTI
	parser := jwt_util.NewJwtParse(jwt_util.Ecdsa256)
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return true, err
	}

	// 尝试解析JWT
	publicKey, err := a.GetPublicKeyOfCorp(ctx, corpId)
	if err == nil {
		claims, err := parser.ParseToken(token, publicKey)
		if err == nil && claims.ID != "" {
			// 如果是JWT且有JTI，检查JWT黑名单
			isBlacklisted, err := a.blacklistRepo.IsBlacklisted(ctx, claims.ID)
			if err != nil {
				a.log.Errorf("CheckJWTBlacklist failed, jti=%s, err=%v", claims.ID, err)
				// 如果JWT黑名单检查失败，继续检查旧黑名单
			} else {
				return isBlacklisted, nil
			}
		}
	}

	// 如果不是JWT或JWT黑名单检查失败，使用旧的黑名单机制（向后兼容）
	tokenBlackKey := a.GetBlackTokenKey(corpId, token)
	value, err := a.repo.Get(ctx, tokenBlackKey)
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		a.log.Errorf("CheckUserTokenListOfRedis failed, err=%v", err)
		return true, err
	}
	if value != "" {
		return true, nil
	}
	return false, nil
}

func (a AuthUsecase) CheckTokenOfRedisByType(ctx context.Context, token, authType string) (bool, error) {
	if authType == dto.UserAuthType {
		return a.CheckTokenOfRedis(ctx, token)
	}
	adminKey := fmt.Sprintf("%s_%s_%s", dto.TokenBlackList, dto.AdminAuthType, token)
	value, err := a.repo.Get(ctx, adminKey)
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		a.log.Errorf("CheckUserTokenListOfRedis failed, err=%v", err)
		return true, err
	}
	if value != "" {
		return true, nil
	}
	return false, nil
}

// recordAccountUnlockLog 记录账户解锁的管理员操作日志
func (a AuthUsecase) recordAccountUnlockLog(ctx context.Context, user *model.TbUserEntity) {
	// 获取操作管理员ID
	authUserID, _ := common.GetUserId(ctx)

	// 构造管理员操作日志
	oplog := modelTable.Oprlog{
		Id:             uuid.New().String(),
		CorpId:         user.CorpID,
		ResourceType:   common.UserType,
		OperationType:  common.OperateUnlock, // 账户解锁操作类型
		Representation: user.Name,
		Error:          "", // 解锁操作没有错误
		AuthUserID:     authUserID,
		AdminEventTime: time.Now().UnixMilli(),
		IpAddress:      common.GetClientHost(ctx),
	}

	// 异步记录操作日志，避免影响主流程性能
	go func() {
		if err := a.repo.CreateAdminOperationLog(context.Background(), oplog); err != nil {
			a.log.Errorf("record account unlock admin operation log failed. err=%v, userId=%v", err, user.ID)
		}
	}()
}

// recordKickSessionLog 记录踢出会话的管理员操作日志
func (a *AuthUsecase) recordKickSessionLog(ctx context.Context, jwtId string, sessionInfo *dto.UserSessionInfo, reason string) {
	// 获取操作管理员ID
	authUserID, _ := common.GetUserId(ctx)

	// 确定描述信息
	representation := jwtId
	if sessionInfo != nil && sessionInfo.UserName != "" {
		representation = fmt.Sprintf("%s (Jid: %s)", sessionInfo.UserName, jwtId)
	}

	// 构造管理员操作日志
	oplog := modelTable.Oprlog{
		Id:             uuid.New().String(),
		CorpId:         "", // 如果sessionInfo存在，使用其CorpID
		ResourceType:   common.SessionType,
		OperationType:  common.OperateKickSession,
		Representation: representation,
		Error:          "", // 踢出会话操作没有错误
		AuthUserID:     authUserID,
		AdminEventTime: time.Now().UnixMilli(),
		IpAddress:      common.GetClientHost(ctx),
	}

	// 设置企业ID
	if sessionInfo != nil && sessionInfo.CorpID != "" {
		oplog.CorpId = sessionInfo.CorpID
	}

	// 异步记录操作日志，避免影响主流程性能
	go func() {
		if err := a.repo.CreateAdminOperationLog(context.Background(), oplog); err != nil {
			a.log.Errorf("record kick session admin operation log failed. err=%v, jwtId=%v", err, jwtId)
		}
	}()
}

// recordUnlockIPLog 记录解锁IP的管理员操作日志
func (a *AuthUsecase) recordUnlockIPLog(ctx context.Context, clientIP string, corpID string) {
	// 获取操作管理员ID
	authUserID, _ := common.GetUserId(ctx)

	// 构造管理员操作日志
	oplog := modelTable.Oprlog{
		Id:             uuid.New().String(),
		CorpId:         corpID,
		ResourceType:   common.IPType,
		OperationType:  common.OperateUnlockIP,
		Representation: clientIP,
		Error:          "", // 解锁IP操作没有错误
		AuthUserID:     authUserID,
		AdminEventTime: time.Now().UnixMilli(),
		IpAddress:      common.GetClientHost(ctx),
	}

	// 异步记录操作日志，避免影响主流程性能
	go func() {
		if err := a.repo.CreateAdminOperationLog(context.Background(), oplog); err != nil {
			a.log.Errorf("record unlock IP admin operation log failed. err=%v, clientIP=%v", err, clientIP)
		}
	}()
}

func (a AuthUsecase) CreateUserLoginLog(ctx context.Context, param dto.CreateLoginLogParam) error {
	var userID string
	var userFound bool

	// 如果用户名不为空，尝试查找用户ID
	if param.UserName != "" {
		user, err := a.repo.QueryUserByName(ctx, param.CorpId, param.UserName)
		if err != nil {
			a.log.Warnf("QueryUserByName failed for login log, userName=%v, err=%v", param.UserName, err)
			// 如果查找失败，将用户名记录到UserID字段（用于未知用户的情况）
			// 确保用户名不超过36个字符（数据库字段限制）
			if len(param.UserName) > 36 {
				userID = param.UserName[:36]
			} else {
				userID = param.UserName
			}
			userFound = false
		} else {
			userID = user.ID
			userFound = true
		}
	} else {
		// 用户名为空的情况
		userID = "EMPTY_USERNAME"
		userFound = false
	}

	loginLog := model.TbUserLoginLog{
		ID:          param.Id,
		SourceID:    param.SourceId,
		CorpID:      param.CorpId,
		ClientID:    param.ClientId,
		Error:       param.Error,
		Type:        param.Type,
		EventTime:   param.EventTime,
		UserID:      userID,
		IPAddress:   param.IpAddress,
		Description: param.Description,
	}
	err := a.repo.CreateLoginLog(ctx, loginLog)

	if err != nil {
		a.log.Errorf("CreateLoginLog failed. err=%v, loginLog=%+v", err, loginLog)
		return err
	}

	// 只有在登录成功且找到了用户时才更新活跃时间
	if param.Type == "LOGIN" && userFound {
		updateInfo := model.TbUserEntity{
			ActiveTime: time.Now(),
		}
		updateErr := a.userRepo.UpdateUserRow(ctx, userID, updateInfo)
		if updateErr != nil {
			a.log.Errorf("UpdateUser failed. err=%v", updateErr)
		}
	}
	return err
}

func (a AuthUsecase) GetUserKey(userId string) string {
	return fmt.Sprintf("%s_%s_%s", dto.ActiveUserToken, dto.ActiveUser, userId)
}
func (a AuthUsecase) GetCorpKey(corpName string) string {
	return fmt.Sprintf("%s_%s_%s", dto.ActiveCorp, dto.ActiveUser, corpName)
}
func (a AuthUsecase) GetBlackTokenKey(corpId string, token string) string {
	return fmt.Sprintf("%s_%s_%s", dto.TokenBlackList, corpId, token)
}

func (a AuthUsecase) GetTokenTime(key string) time.Duration {
	accessTokenDuration, err := strconv.ParseUint(a.repo.GetSpecialConfig(key), 10, 64)
	if err != nil {
		a.log.Errorf("GetAccessToken config failed. err=%v", err)
		return 0
	}
	return time.Duration(accessTokenDuration)
}

func (a AuthUsecase) GetCurrentClaim(ctx context.Context) (jwt_util.TokenClaims, error) {
	parser := jwt_util.NewJwtParse(jwt_util.Ecdsa256)
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		a.log.Errorf("GetCorpId failed. err=%v", err)
		return jwt_util.TokenClaims{}, err
	}
	publicKey, err := a.GetPublicKeyOfCorp(ctx, corpId)
	if err != nil {
		a.log.Errorf("GetPublicKey failed. err=%v", err)
		return jwt_util.TokenClaims{}, err
	}
	jwtToken, err := common.GetToken(ctx)
	if err != nil {
		a.log.Errorf("GetPublicKey failed. err=%v", err)
		return jwt_util.TokenClaims{}, err
	}
	ret, err := parser.ParseToken(jwtToken, publicKey)
	return *ret, err
}

func (a AuthUsecase) GetAccessTokenDuration(accessTokenDuration time.Duration, claim jwt_util.TokenClaims) (time.Duration, error) {
	expireTime := claim.RegisteredClaims.ExpiresAt
	if time.Now().Add(accessTokenDuration).After(expireTime.Time) {
		return expireTime.Time.Sub(time.Now()), nil
	}
	return accessTokenDuration, nil
}

func (a AuthUsecase) GetCurrentTokenDuration(claim jwt_util.TokenClaims) (time.Duration, error) {
	expireTime := claim.RegisteredClaims.ExpiresAt
	return expireTime.Time.Sub(time.Now()), nil
}

func (a AuthUsecase) getSecondaryAuth(ctx context.Context, policy []*model.TbAuthPolicy, user *model.TbUserEntity) ([]*model.TbIdentityProvider, error) {
	// 获取认证策略关联的可用的辅助认证源
	var policies []string
	for _, p := range policy {
		if p.RootGroupId == user.RootGroupID {
			policies = append(policies, p.ID)
		}
	}
	idp, err := a.repo.QuerySecondaryAuth(ctx, policies)
	if err != nil {
		a.log.Errorf("getSecondaryAuth failed. err=%v", err)
		return []*model.TbIdentityProvider{}, err
	}
	return idp, err
}

// SendSms 发送短信验证码
func (a AuthUsecase) SendSms(ctx context.Context, param *auth.SendSmsRequest) (*auth.SendSmsReply, error) {
	return a.SendVerifyCode(ctx, param)
}

func (a AuthUsecase) SendVerifyCode(ctx context.Context, param *auth.SendSmsRequest) (*auth.SendSmsReply, error) {
	//通过uniq_key获取用户id
	value, err := a.repo.Get(ctx, param.UniqKey)
	if err != nil {
		a.log.Errorf("redis failed. err=%v", err)
		return nil, pb.ErrorNotMainAuth("未进行主认证或主认证已失效")
		//return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, nil
	}
	var redisValue map[string]interface{}
	err = json.Unmarshal([]byte(value), &redisValue)
	if err != nil {
		a.log.Errorf("redis parse failed. err=%v", err)
		return nil, pb.ErrorNotMainAuth("未进行主认证或主认证已失效")
	}
	userId, ok := redisValue["userId"].(string)
	if !ok {
		a.log.Errorf("get userId failed. err=%v", err)
		return nil, pb.ErrorNotMainAuth("未进行主认证或主认证已失效")
	}
	// 通过用户id获取用户信息
	user, err := a.userRepo.QueryUserEntity(ctx, "", userId)
	if err != nil {
		a.log.Errorf("QueryUserById failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, nil
	}
	//获取辅助认证信息
	var idpInfo *model.TbIdentityProvider
	idpInfo, err = a.idpRepo.GetIDP(ctx, user.CorpID, param.IdpId)
	if err != nil {
		a.log.Errorf("get idp info failed. err=%v", err)
		return nil, pb.ErrorSendSmsError("辅助信息获取异常")
	}
	//获取辅助认证配置
	var attrs []dto.KV
	attrs, err = a.idpRepo.GetIDPAttr(ctx, param.IdpId)
	if err != nil {
		a.log.Errorf("GetIDPAttr failed. err=%v", err)
		return nil, pb.ErrorSendSmsError("辅助信息获取异常")
	}
	// 生成验证码缓存键
	verificationKey := uuid.New().String()

	// 生成验证码 (6位数字)
	rand.Seed(time.Now().UnixNano())
	code := rand.Intn(900000) + 100000
	verificationCode := fmt.Sprintf("%d", code)

	// 根据认证类型发送验证码
	var expirationTime int32 = 5 // 默认5分钟

	if idpInfo.Type == "verify_code" {
		switch idpInfo.TemplateType {
		case "sms":
			// 验证用户是否有手机号
			if user.Phone == "" {
				a.log.Errorf("The user(%v) mobile number does not exist", user.ID)
				return nil, pb.ErrorNotPhone("用户手机号码不存在")
			}
			// 构建发送短信配置
			idpAttrs := dto.KVsToSmsIdpAttr(attrs)
			expirationTime = idpAttrs.ExpirationTime

			// 发送短信验证码
			err = a.sendSmsCode(ctx, idpAttrs.SmsConfig, user.Phone, verificationCode)
			if err != nil {
				a.log.Errorf("send sms failed. err=%v", err)
				return nil, pb.ErrorSendSmsError(fmt.Sprintf("短信发送异常: %v", err))
			}
		case "email":
			// 验证用户是否有邮箱
			if user.Email == "" {
				a.log.Errorf("The user(%v) email address does not exist", user.ID)
				return nil, pb.ErrorEmailNotExists("用户邮箱地址不存在")
			}

			idpAttrs := dto.KVsToIdpAttr(attrs)

			// 创建邮箱服务提供者
			emailProvider, err := email.NewEmailProvider(
				idpInfo.ID,
				idpInfo.Name,
				idpAttrs.ConfigData,
				log.With(log.GetLogger()),
			)
			if err != nil {
				a.log.Errorf("创建邮箱服务提供者失败: %v", err)
				return nil, pb.ErrorSendSmsError(fmt.Sprintf("邮箱验证码配置异常: %v", err))
			}

			// 发送验证码邮件
			if err := emailProvider.SendVerificationCode(ctx, user.Email, verificationCode); err != nil {
				a.log.Errorf("发送邮箱验证码失败: %v", err)
				return nil, pb.ErrorSendSmsError(fmt.Sprintf("邮箱验证码发送失败: %v", err))
			}

			// 从邮箱配置中获取过期时间
			var emailConfig map[string]interface{}
			if err := json.Unmarshal([]byte(idpAttrs.ConfigData), &emailConfig); err == nil {
				if expTime, exists := emailConfig["expiration_time"]; exists {
					switch v := expTime.(type) {
					case float64:
						if v > 0 {
							expirationTime = int32(v)
						}
					case string:
						if t, err := strconv.Atoi(v); err == nil && t > 0 {
							expirationTime = int32(t)
						}
					}
				}
			}

		default:
			a.log.Errorf("不支持的辅助认证类型: %s", idpInfo.Type)
			return nil, pb.ErrorSendSmsError("不支持的辅助认证类型")
		}
	} else {
		return nil, pb.ErrorSendSmsError("不支持的辅助认证类型")
	}
	// 更新redis缓存
	// 保存验证码密钥到用户会话
	cacheValue := map[string]interface{}{
		"codeKey": verificationKey,
		"userId":  userId,
		"idpType": idpInfo.Type,
	}

	cacheJsonBytes, err := json.Marshal(cacheValue)
	if err != nil {
		a.log.Errorf("Marshal failed. err=%v", err)
		return nil, pb.ErrorSendSmsError(fmt.Sprintf("验证码处理异常: %v", err))
	}

	// 更新会话信息
	err = a.repo.GetSet(ctx, param.UniqKey, string(cacheJsonBytes))
	if err != nil {
		a.log.Errorf("GetSet failed. err=%v", err)
		return nil, pb.ErrorSendSmsError(fmt.Sprintf("验证码处理异常: %v", err))
	}

	// 缓存验证码
	verifyInfo := map[string]interface{}{
		"code":   verificationCode,
		"userId": userId,
	}

	verifyJsonBytes, err := json.Marshal(verifyInfo)
	if err != nil {
		a.log.Errorf("Marshal failed. err=%v", err)
		return nil, pb.ErrorSendSmsError(fmt.Sprintf("验证码处理异常: %v", err))
	}

	// 设置验证码缓存，带有过期时间
	expires := time.Duration(expirationTime) * time.Minute
	if err := a.repo.Set(ctx, verificationKey, string(verifyJsonBytes), expires); err != nil {
		a.log.Errorf("Set redis failed. err=%v", err)
		return nil, pb.ErrorSendSmsError(fmt.Sprintf("验证码处理异常: %v", err))
	}

	return &auth.SendSmsReply{Status: auth.StatusCode_SUCCESS}, nil
}

func (a AuthUsecase) sendSmsCode(ctx context.Context, param dto.SmsConfig, phoneNumber string, code string) error {
	phone := strings.TrimPrefix(phoneNumber, "+86")
	switch param.SmsType {
	case "Aliyun":
		client, err := gosmssender.NewSmsClient(gosmssender.Aliyun, param.AccessKeyId, param.AccessKeySecret, param.SignName, param.TemplateCode)
		if err != nil {
			return fmt.Errorf("创建阿里云SMS客户端失败: %w", err)
		}

		params := map[string]string{"code": code}
		return client.SendMessage(params, phone)

	case "Volc":
		client, err := gosmssender.NewSmsClient(gosmssender.VolcEngine, param.AccessKeyId, param.AccessKeySecret, param.SignName, param.TemplateCode, param.SdkAppId)
		if err != nil {
			return fmt.Errorf("创建火山引擎SMS客户端失败: %w", err)
		}

		params := map[string]string{"code": code}
		return client.SendMessage(params, phone)

	case "TencentCloud":
		client, err := gosmssender.NewSmsClient(gosmssender.TencentCloud, param.SecretId, param.SecretKey, param.SignName, param.TemplateCode, param.SdkAppId)
		if err != nil {
			return fmt.Errorf("创建腾讯云SMS客户端失败: %w", err)
		}

		params := map[string]string{"0": code}
		return client.SendMessage(params, phone)

	case "HuaweiCloud":
		client, err := gosmssender.NewSmsClient(gosmssender.HuaweiCloud, param.AppKey, param.AppSecret, param.SignName, param.TemplateCode, param.ChannelNo)
		if err != nil {
			return fmt.Errorf("创建华为云SMS客户端失败: %w", err)
		}

		params := map[string]string{"code": code}
		return client.SendMessage(params, phone)
	default:
		return fmt.Errorf("不支持的短信类型: %s", param.SmsType)
	}
}

func (a AuthUsecase) TotpVerify(ctx context.Context, param dto.LoginForm) (any, error) {
	//通过uniq_key获取用户id
	value, err := a.repo.Get(ctx, param.UniqKey)
	if err != nil {
		a.log.Errorf("redis failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("认证链已失效")
	}
	var redisValue map[string]interface{}
	err = json.Unmarshal([]byte(value), &redisValue)
	if err != nil {
		a.log.Errorf("redis parse failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("认证链已失效")
	}

	// 从 redisValue 获取 period（令牌有效时间） 并解析为 uint 类型
	periodVal := redisValue["period"]
	periodStr, ok := periodVal.(string)
	if !ok {
		// 非 string 类型则尝试格式化为字符串
		periodStr = fmt.Sprintf("%v", periodVal)
	}
	parsedPeriod, err := strconv.ParseUint(periodStr, 10, 32)
	if err != nil {
		a.log.Warnf("解析 period 失败，使用默认值 30. 原始值: %v", periodStr)
		parsedPeriod = 30 // 默认周期（秒）
	}

	userId, ok := redisValue["userId"].(string)
	if !ok {
		a.log.Errorf("get userId failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("获取用户信息失败")
	}
	// 通过用户id获取用户信息
	user, err := a.userRepo.QueryUserEntity(ctx, "", userId)
	if err != nil {
		a.log.Errorf("QueryUserById failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("获取用户信息失败")
	}
	opts := totp.ValidateOpts{
		Period:    uint(parsedPeriod), // 令牌有效时间 这里一定要传管理后台配置的值，不然会出现验证失败
		Digits:    otp.DigitsSix,
		Algorithm: otp.AlgorithmSHA1,
	}
	isValid, err := totp.ValidateCustom(param.TotpKey, user.ActivationSecret, time.Now(), opts)
	if !isValid {
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorSmsCodeError("口令验证错误")
	}
	a.repo.Del(ctx, param.UniqKey)
	a.repo.Del(ctx, "auth_enhancement_"+param.UniqKey)

	updateInfo := model.TbUserEntity{
		CurrentSecret: user.ActivationSecret,
	}
	err = a.userRepo.UpdateUserRow(ctx, userId, updateInfo)
	if err != nil {
		return nil, err
	}

	// 授权
	authResp, err := a.authz(ctx, user, param.AuthBasicForm)
	if err != nil {
		return nil, err
	}
	return authResp, nil
}

func (a AuthUsecase) VerifyCode(ctx context.Context, param dto.LoginForm) (any, error) {
	//通过uniq_key获取用户id
	value, err := a.repo.Get(ctx, param.UniqKey)
	if err != nil {
		a.log.Errorf("redis failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("认证链已失效")
	}
	var redisValue map[string]interface{}
	err = json.Unmarshal([]byte(value), &redisValue)
	if err != nil {
		a.log.Errorf("redis parse failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("认证链已失效")
	}
	userId, ok := redisValue["userId"].(string)
	if !ok {
		a.log.Errorf("get userId failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("获取用户信息失败")
	}
	codeKey, ok := redisValue["codeKey"].(string)
	if !ok {
		a.log.Errorf("get codeKey failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("获取验证码信息失败")
	}

	idpType, _ := redisValue["idpType"].(string)
	codeInfo, err := a.repo.Get(ctx, codeKey)
	if err != nil {
		a.log.Errorf("get code info failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorSmsCodeInvalidError("验证码已失效")
	}
	var codeValue map[string]interface{}
	err = json.Unmarshal([]byte(codeInfo), &codeValue)
	if err != nil {
		a.log.Errorf("unmarshal code info failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorSmsCodeInvalidError("验证码已失效")
	}
	storedCode, ok := codeValue["code"].(string)
	if !ok {
		a.log.Errorf("get auth code failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorSmsCodeInvalidError("验证码已失效")
	}

	// 验证码校验
	if storedCode != param.AuthCode {
		a.log.Errorf("code verification failed, expected %s got %s", storedCode, param.AuthCode)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorSmsCodeError("验证码错误")
	}

	// 通过用户id获取用户信息
	user, err := a.userRepo.QueryUserEntity(ctx, "", userId)
	if err != nil {
		a.log.Errorf("QueryUserById failed. err=%v", err)
		return &auth.SendSmsReply{Status: auth.StatusCode_FAILED}, pb.ErrorAuthChainFailure("获取用户信息失败")
	}

	//销毁redis中缓存，缓存时间是短信配置的过期时间（最大10分钟），所以不管销毁成功还是失败都不影响正常登录
	a.repo.Del(ctx, param.UniqKey)
	a.repo.Del(ctx, codeKey)
	// 授权
	authzResp, err := a.authz(ctx, user, param.AuthBasicForm)
	if err != nil {
		return nil, err
	}
	// 记录已使用的辅助认证IDP
	var cachePrefix string
	switch dto.IDPType(idpType) {
	case dto.IDPTypeSMS:
		cachePrefix = dto.SmsIdpPrefix
	case dto.IDPTypeEmail:
		cachePrefix = dto.EmailIdpPrefix
	default:
		cachePrefix = "enhancement_idp"
	}

	err = a.repo.SAdd(ctx, fmt.Sprintf("%s_%s", cachePrefix, userId), param.IdpId)
	if err != nil {
		a.log.Errorf("记录辅助认证使用情况失败. err=%v", err)
	}

	return authzResp, nil
}

func (a AuthUsecase) DelSecondarySmsIdp(ctx context.Context, userId string) error {
	err := a.repo.Del(ctx, fmt.Sprintf("%s_%s", dto.SmsIdpPrefix, userId))
	if err != nil && !errors.Is(err, redis.Nil) {
		a.log.Errorf("DelSmsIdpId failed. err=%v", err)
		return err
	}
	return nil
}

func (a AuthUsecase) GetSecondarySmsIdp(ctx context.Context, userId string) ([]string, error) {
	res, err := a.repo.SMembers(ctx, fmt.Sprintf("%s_%s", dto.SmsIdpPrefix, userId))
	if err != nil && !errors.Is(err, redis.Nil) {
		a.log.Errorf("DelSmsIdpId failed. err=%v", err)
		return nil, err
	}
	return res, nil
}

func (a AuthUsecase) GetRedisAuthEnhancementInfo(ctx context.Context, param dto.LoginForm) (dto.AuthEnhancementInfo, error) {
	//通过uniq_key获取用户id
	value, err := a.repo.Get(ctx, fmt.Sprintf("%s_%s", dto.AuthEnhancementPrefix, param.UniqKey))
	if err != nil {
		a.log.Errorf("redis failed. err=%v", err)
		return dto.AuthEnhancementInfo{}, pb.ErrorAuthChainFailure("认证链已失效")
	}
	var res dto.AuthEnhancementInfo
	err = json.Unmarshal([]byte(value), &res)
	if err != nil {
		a.log.Errorf("json unmarshal failed. err=%v", err)
		return dto.AuthEnhancementInfo{}, pb.ErrorAuthChainFailure("认证链已失效")
	}
	return res, err
}

func (a AuthUsecase) GetUserInfoById(ctx context.Context, userId string) (model.TbUserEntity, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return model.TbUserEntity{}, err
	}
	userInfo, err := a.userRepo.QueryUserEntity(ctx, corpId, userId)
	if err != nil {
		return model.TbUserEntity{}, err
	}
	return *userInfo, nil
}

func (a AuthUsecase) GetUserInfoByName(ctx context.Context, name string) (model.TbUserEntity, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return model.TbUserEntity{}, err
	}
	userInfo, err := a.userRepo.QueryUserByName(ctx, corpId, name)
	if err != nil {
		return model.TbUserEntity{}, err
	}
	return *userInfo, nil
}

func (a AuthUsecase) CheckUserIdExist(ctx context.Context, userId string, userType string) (bool, error) {
	return a.repo.CheckUserIdExist(ctx, userId, userType)
}

func (a AuthUsecase) LoginAdms(ctx context.Context, serverAddr, entry, username string) (dto.Token, error) {
	var token dto.Token
	user, err := a.repo.GetGroupByAdServerAddr(ctx, serverAddr, entry, username)
	if err != nil {
		return token, err
	}

	token, err = a.issueToken(ctx, user, dto.AccessTokenTyp)
	if err != nil {
		return dto.Token{}, err
	}
	return token, nil
}

func (a AuthUsecase) UserBind(ctx context.Context, externalUserId string) (*dto.UserBindInfo, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 从外部用户表查询用户信息
	externalUser, err := a.userRepo.GetExternalUserByUserId(ctx, externalUserId)
	if err != nil {
		return &dto.UserBindInfo{
			Success: false,
			Message: "平台用户不存在",
		}, nil
	}

	// 2. 根据外部用户的LocalUserID查询本地用户
	localUser, err := a.userRepo.QueryUserEntity(ctx, corpId, externalUser.LocalUserID)
	if err != nil {
		return &dto.UserBindInfo{
			Success: false,
			Message: "平台用户不存在",
		}, nil
	}

	return &dto.UserBindInfo{
		Success:       true,
		LocalUserId:   localUser.ID,
		LocalUserName: localUser.Name,
		Message:       "绑定成功",
	}, nil
}

// 从第三方认证服务器中根据查询字段找到对应的字段值
func (a AuthUsecase) getFieldValue(obj interface{}, field string) (interface{}, bool) {
	v := reflect.ValueOf(obj).Elem()
	t := v.Type()
	for i := 0; i < t.NumField(); i++ {
		if t.Field(i).Name == field {
			return v.Field(i).Interface(), true
		}
	}
	return nil, false
}

// whereCondition 数据库查询条件组织 用于用户查找方式
func (a AuthUsecase) whereCondition(ctx context.Context, key, val string) []dto.KV {
	var condition []dto.KV
	dType := "="
	if key == "Name" {
		key = "name"
	} else if key == "Email" {
		key = "email"
	} else if key == "Mobile" {
		key = "phone"
		dType = "like"
		//对value处理 去掉val字符串最前面的+86
		val = strings.TrimPrefix(val, "+86")
		val = "%" + val
	} else if key == "Userid" {
		key = "identify"
	} else if key == "DisplayName" {
		key = "display_name"
	} else if key == "TrueName" {
		key = "true_name"
	} else if key == "NickName" {
		key = "nick_name"
	}
	condition = append(condition, dto.KV{
		Key:   key,
		Value: val,
		Type:  dType,
	})
	return condition
}

// createThirdUserIfNotExist 如果用户不存在则创建用户
// 仅用于第三方认证类型的IDP自动创建用户
func (a AuthUsecase) createThirdUserIfNotExist(ctx context.Context, rootGroup *model.TbUserGroup,
	allSource []dto.SourceGroupResult, externalUser *dto.ExternalUser, corpId, SearchMap string) (*model.TbExternalUser, error) {

	authType := "oauth2" // 默认值
	if externalUser.Type != "" {
		authType = externalUser.Type
	}

	//解析用户查找结构体
	var userSearch []struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}
	searchKey := ""
	searchValue := ""
	searchResultId := ""

	// 1. 尝试查询现有外部用户
	localUser, err := a.userRepo.GetExternalUser(ctx, rootGroup.RootGroupID, externalUser.Userid, authType)
	if err == nil {
		// 用户已存在，检查是否需要更新
		needUpdate := false
		// 更新外部用户表
		updatedUser := model.TbExternalUser{
			UniqKey:          localUser.UniqKey,
			LocalRootGroupID: localUser.LocalRootGroupID,
			LocalUserID:      localUser.LocalUserID,
			Userid:           externalUser.Userid,
			UpdatedAt:        time.Now(),
		}

		// 检查关键字段是否有变化
		if externalUser.Name != "" && localUser.Name != externalUser.Name {
			needUpdate = true
			updatedUser.Name = externalUser.Name
		}
		if externalUser.NickName != "" && localUser.NickName != externalUser.NickName {
			needUpdate = true
			updatedUser.NickName = externalUser.NickName
		}
		if externalUser.DisplayName != "" && localUser.DisplayName != externalUser.DisplayName {
			needUpdate = true
			updatedUser.DisplayName = externalUser.DisplayName
		}
		if externalUser.Email != "" && localUser.Email != externalUser.Email {
			needUpdate = true
			updatedUser.Email = externalUser.Email
		}
		if externalUser.Mobile != "" && localUser.Mobile != externalUser.Mobile {
			needUpdate = true
			updatedUser.Mobile = externalUser.Mobile
		}
		if externalUser.TrueName != "" && localUser.TrueName != externalUser.TrueName {
			needUpdate = true
			updatedUser.TrueName = externalUser.TrueName
		}
		// 如需更新，则更新外部用户信息
		if needUpdate {
			a.log.Debugf("更新OAuth2/CAS用户信息: %s", externalUser.Userid)
			// 调用更新方法
			if err := a.userRepo.UpdateExternalUser(ctx, updatedUser); err != nil {
				a.log.Warnf("更新外部用户失败: %v", err)
				// 继续使用现有用户，不中断认证流程
			}
		}
		return localUser, nil
	}

	//查询关联用户目录的类型
	source, e := a.userSource.GetUserSource(ctx, corpId, rootGroup.SourceID)
	if e != nil {
		return nil, e
	}

	if SearchMap != "" {
		err := json.Unmarshal([]byte(SearchMap), &userSearch)
		if err != nil {
			a.log.Warnf("用户查找关系解析失败: %v", err)
		} else {
			for _, v := range userSearch {
				if strings.HasPrefix(v.Key, "{User.") {
					searchKey = strings.TrimSuffix(strings.TrimPrefix(v.Key, "{User."), "}")
					if val, exists := a.getFieldValue(externalUser, searchKey); exists {
						searchValue = val.(string)
						a.log.Debugf("根据用户查找关系: %v,查找结果: %v", v.Key, val)
					} else {
						a.log.Debugf("字段 %s 不存在\n", searchKey)
					}
				}
			}

			if searchValue != "" {
				condition := a.whereCondition(ctx, searchKey, searchValue)
				exist, err := a.userRepo.QueryUserByFieldMap(ctx, corpId, allSource[0].SourceID, condition)
				if err != nil {
					return nil, err
				}
				if len(exist) > 1 {
					errMsg := fmt.Sprintf("根据用户查找关系:%s【%s】,找到多个用户，请联系管理员处理！", searchKey, searchValue)
					return nil, pb.ErrorAuthSearchNotUnique(errMsg)
				}
				if len(exist) == 1 {
					searchResultId = exist[0].ID
					a.log.Debugf("根据用户查找关系，找到一个主账号: %v", searchResultId)
				}
			}
		}

	}
	// 一些业务逻辑注释
	// 1、非本地用户目录 都要求是可以批量同步组织和用户的
	// 2、oauth2认证源如果选择了非本地用户目录，则要求一定要配置用户查找关系
	// 3、同步的账户作为主账号，根据查找关系，一定要求找到唯一一个主账号 找到多个、或则找不到 都不允许认证通过
	// 4、如果关联目录是本地用户目录，如果配置了查找关系，且查找到多个的情况下，不允许认证通过，找到一个的话就做账户绑定，没找到的话，就插入，并且作为主账户
	if source.SourceType != "local" && searchResultId == "" { //关联用户目录如果不是本地目录
		return nil, pb.ErrorUserNotFound("未查找到主账号！")
	}

	// 只处理用户不存在的错误
	if !errors.Is(err, gorm.ErrRecordNotFound) && !pb.IsRecordNotFound(err) {
		a.log.Errorf("查询外部用户失败(非不存在错误): %v", err)
		return nil, pb.ErrorLoginError(loginParamError)
	}
	// 验证用户名不能为空
	if externalUser.Name == "" {
		a.log.Errorf("无法创建用户: 第三方认证返回的用户名为空")
		return nil, pb.ErrorLoginError("第三方认证未提供用户名，无法创建用户")
	}

	if searchResultId == "" {
		a.log.Debugf("外部用户 %s 不存在，即将创建新用户", externalUser.Userid)
	}

	// 2. 创建新用户
	newUserId := uuid.New().String()
	if searchResultId != "" {
		newUserId = searchResultId
	}

	// 设置默认主部门，如果为空
	mainDepartment := externalUser.MainDepartment
	if mainDepartment == "" {
		mainDepartment = "0" // 默认主部门ID
	}

	var groupID string = rootGroup.ID // 默认使用根组ID

	// 2.1 如果用户有主部门信息，则尝试查找对应的本地部门
	if externalUser.MainDepartment != "" {
		// 获取部门映射关系 - 查询外部部门ID到本地部门ID的映射
		externalDepts, err := a.userRepo.ListExternalDeps(ctx, rootGroup.ID)
		if err != nil {
			a.log.Warnf("获取外部部门映射失败，将使用根部门: %v", err)
		} else {
			// 查找匹配的部门
			for _, dept := range externalDepts {
				if dept.ID == externalUser.MainDepartment {
					// 找到匹配的部门，使用本地对应的ID
					groupID = dept.LocalGroupID
					a.log.Debugf("找到用户(%s)的对应部门映射: 外部ID=%s -> 本地ID=%s",
						externalUser.Userid, externalUser.MainDepartment, groupID)
					break
				}
			}
		}
	}

	if searchResultId == "" {

		// 创建用户DAO参数
		userParam := dto.CreateUserDaoParam{
			Id:          newUserId,
			CorpId:      corpId,
			Name:        externalUser.Name,
			DisplayName: externalUser.DisplayName,
			TrueName:    externalUser.TrueName,
			NickName:    externalUser.NickName,
			Email:       externalUser.Email,
			Identifier:  externalUser.Userid,
			Phone:       externalUser.Mobile,
			SourceId:    allSource[0].SourceID,
			RootGroupID: rootGroup.RootGroupID,
			GroupId:     groupID,
			ExpireType:  dto.ExpireTypeForever,
			Enable:      &[]bool{true}[0],
			AuthType:    authType,
		}

		// 创建本地用户
		err = a.userRepo.CreateUser(ctx, userParam)
		if err != nil {
			a.log.Errorf("创建本地用户失败: %v", err)
			return nil, pb.ErrorLoginError("创建用户失败")
		}
	}

	// 3. 创建外部用户关联
	externalUserEntity := &model.TbExternalUser{
		UniqKey:          uuid.New().String(),
		LocalRootGroupID: rootGroup.RootGroupID,
		LocalUserID:      newUserId,
		MainDepartment:   mainDepartment,
		Userid:           externalUser.Userid,
		Name:             externalUser.Name,
		NickName:         externalUser.NickName,
		DisplayName:      externalUser.DisplayName,
		TrueName:         externalUser.TrueName,
		Email:            externalUser.Email,
		Mobile:           externalUser.Mobile,
		Status:           true,
		Type:             authType,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// 将实体转换为DTO用于批量插入
	err = a.userRepo.BatchInsertExternalUsers(ctx, []*dto.ExternalUser{
		{
			UniqKey:          externalUserEntity.UniqKey,
			LocalRootGroupID: rootGroup.RootGroupID,
			Userid:           externalUser.Userid,
			Name:             externalUser.Name,
			NickName:         externalUser.NickName,
			TrueName:         externalUser.TrueName,
			DisplayName:      externalUser.DisplayName,
			MainDepartment:   externalUser.MainDepartment,
			Email:            externalUser.Email,
			Mobile:           externalUser.Mobile,
			Type:             externalUserEntity.Type,
			LocalUserID:      newUserId,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
	})
	if err != nil {
		a.log.Errorf("创建外部用户关联失败: %v", err)
		return nil, pb.ErrorLoginError("创建用户关联失败")
	}

	a.log.Debugf("成功创建用户 %s -> %s", externalUser.Userid, newUserId)
	return externalUserEntity, nil
}

// 从上下文获取IDP ID
func (a AuthUsecase) getIdpIdFromContext(ctx context.Context, userId string) (string, error) {
	idpKey := fmt.Sprintf("user_idp:%s", userId)
	idpId, err := a.repo.Get(ctx, idpKey)
	if err == nil && idpId != "" {
		return idpId, nil
	}

	return "", fmt.Errorf("无法确定用户的IDP来源")
}

// 执行OAuth2登出
func (a AuthUsecase) executeOAuth2Logout(ctx context.Context, idp *model.TbIdentityProvider, token string) (string, error) {
	// 1. 获取并检查IDP登出配置
	attrs, err := a.idpRepo.GetIDPAttr(ctx, idp.ID)
	if err != nil {
		a.log.Warnf("获取IDP属性失败: %v", err)
		return "", err
	}

	// 转换为易于查询的map并检查登出功能是否启用
	attrMap := convertAttrsToMap(attrs)
	if attrMap[dto.AttrKeyOAuth2LogoutOpen] != "1" {
		a.log.Debugf("OAuth2登出功能未启用")
		return "", nil
	}

	// 2. 解析登出配置，判断请求类型
	var logoutConfig webauth.Step
	if err := json.Unmarshal([]byte(attrMap[dto.AttrKeyOAuth2LogoutData]), &logoutConfig); err != nil {
		a.log.Warnf("解析登出配置失败: %v", err)
		return "", err
	}

	// 3. 根据请求类型执行不同处理
	if strings.ToUpper(logoutConfig.Method) == "GET" {
		// GET请求 - 返回URL给前端
		return a.handleGetLogout(ctx, idp.ID, token, logoutConfig, attrMap)
	} else {
		// POST或其他请求类型 - 使用执行器发送实际请求
		a.handlePostLogout(ctx, idp, token, attrMap)
		return "", nil // POST登出不需要返回URL
	}
}

// handleGetLogout 处理GET类型的登出请求
func (a AuthUsecase) handleGetLogout(ctx context.Context, idpId, token string, config webauth.Step, attrMap map[string]string) (string, error) {
	// 1. 解析基本URL
	baseURL := config.URL
	if baseURL == "" {
		a.log.Warnf("登出配置中未指定URL")
		return "", fmt.Errorf("登出配置中未指定URL")
	}

	// 2. 准备用户、环境变量信息用于变量替换
	userInfo := make(map[string]string)
	envInfo := make(map[string]string)

	// 2.1 从缓存恢复更多信息
	userId, _ := common.GetUserId(ctx)
	if userId != "" {
		externalUser, err := a.userRepo.GetExternalUserByLocalUserId(ctx, userId)
		if err == nil {
			cacheKey := fmt.Sprintf("oauth2_logout_info:%s:%s", externalUser.Userid, idpId)
			if userInfoJson, err := a.repo.Get(ctx, cacheKey); err == nil && userInfoJson != "" {
				var cachedInfo map[string]interface{}
				if err := json.Unmarshal([]byte(userInfoJson), &cachedInfo); err == nil {
					for k, v := range cachedInfo {
						s := fmt.Sprintf("%v", v)
						switch {
						case strings.HasPrefix(k, "{User."):
							key := strings.TrimSuffix(strings.TrimPrefix(k, "{User."), "}")
							userInfo[key] = s
						case strings.HasPrefix(k, "{Env."):
							key := strings.TrimSuffix(strings.TrimPrefix(k, "{Env."), "}")
							envInfo[key] = s
						default:
							key := strings.TrimSuffix(strings.TrimPrefix(k, "{Global."), "}")
							attrMap[key] = s
						}
					}
				}
			}
		}
	}

	// 2.2 替换 baseURL 中的占位变量（User.xxx / Env.xxx / Global.xxx）
	prefixMaps := map[string]map[string]string{
		"User.":   userInfo,
		"Env.":    envInfo,
		"Global.": attrMap,
	}
	pairs := make([]string, 0, len(userInfo)+len(envInfo)+len(attrMap)*2)
	for prefix, m := range prefixMaps {
		for k, v := range m {
			pairs = append(pairs, "{"+prefix+k+"}", v)
		}
	}
	replacer := strings.NewReplacer(pairs...)
	baseURL = replacer.Replace(baseURL)

	// 3. 添加GET参数
	params := url.Values{}
	for _, param := range config.Input.Gets {
		value := replacer.Replace(param.Value)
		params.Add(param.Key, value)
	}

	// 4. 构建完整URL
	fullURL := baseURL
	if enc := params.Encode(); enc != "" {
		sep := "?"
		if strings.Contains(baseURL, "?") {
			sep = "&"
		}
		fullURL += sep + enc
	}

	// 5. 记录生成的URL
	a.log.Debugf("生成OAuth2登出URL: %s", fullURL)

	// 6. 返回构建好的URL
	return fullURL, nil
}

// handlePostLogout 处理POST类型的登出请求
func (a AuthUsecase) handlePostLogout(ctx context.Context, idp *model.TbIdentityProvider, token string, attrMap map[string]string) {
	// 1. 创建OAuth2Provider并执行登出
	logger := log.With(log.GetLogger(), "module", "oauth2_logout")
	oauth2Provider, err := oauth2.NewOAuth2Provider(
		idp.ID, idp.Name, attrMap[dto.AttrKeyOAuth2GlobalData],
		attrMap[dto.AttrKeyOAuth2CodeData], attrMap[dto.AttrKeyOAuth2UserData],
		attrMap[dto.AttrKeyOAuth2LogoutData], true, "", logger,
	)

	if err != nil {
		a.log.Warnf("创建OAuth2Provider失败: %v", err)
		return
	}

	userId, err := common.GetUserId(ctx)
	if err == nil {
		// 通过内部用户ID查找对应的外部用户
		externalUser, err := a.userRepo.GetExternalUserByLocalUserId(ctx, userId)
		if err == nil {
			// 使用外部用户ID构建相同的缓存键
			cacheKey := fmt.Sprintf("oauth2_logout_info:%s:%s", externalUser.Userid, idp.ID)

			// 从Redis缓存中获取用户信息
			userInfoJson, err := a.repo.Get(ctx, cacheKey)
			if err == nil && userInfoJson != "" {
				var userInfo map[string]interface{}
				if err := json.Unmarshal([]byte(userInfoJson), &userInfo); err == nil {
					// 恢复用户信息到OAuth2Provider
					for k, v := range userInfo {
						oauth2Provider.GetExecutor().SetUserInfo(k, v)
					}
					a.log.Debugf("已从缓存恢复用户(%s/%s)的登出信息", userId, externalUser.Userid)
				}
			} else {
				a.log.Warnf("未能从缓存恢复用户信息，将使用基本登出: %v", err)
			}
		} else {
			a.log.Warnf("无法找到用户(%s)对应的外部用户: %v", userId, err)
		}
	}

	// 设置基本登出凭据
	oauth2Provider.SetLogoutCredentials(token)

	// 执行登出
	if err := oauth2Provider.Logout(ctx, token); err != nil {
		a.log.Warnf("OAuth2登出失败: %v", err)
	} else {
		a.log.Debugf("OAuth2登出成功,IDP名称: %s", idp.Name)
	}
}

// 将IDP属性数组转换为map
func convertAttrsToMap(attrs []dto.KV) map[string]string {
	attrMap := make(map[string]string)
	for _, attr := range attrs {
		attrMap[attr.Key] = attr.Value
	}
	return attrMap
}

func (a AuthUsecase) saveUserIdpMapping(ctx context.Context, userId, idpId string, idpType string) {
	// 将用户与IDP关联保存到Redis
	idpKey := fmt.Sprintf("user_idp:%s", userId)
	err := a.repo.Set(ctx, idpKey, idpId, time.Hour*24*30) // 保存30天
	if err != nil {
		a.log.Warnf("保存用户IDP映射失败: %v", err)
		return
	}

	idpTypeKey := fmt.Sprintf("user_idp_type:%s", userId)
	if err := a.repo.Set(ctx, idpTypeKey, idpType, time.Hour*24*30); err != nil {
		a.log.Warnf("保存用户IDP类型失败: %v", err)
	} else {
		a.log.Debugf("已保存用户(%s)的IDP类型: %s", userId, idpType)
	}

	a.log.Debugf("已保存用户(%s)的IDP映射: %s", userId, idpId)
}

// 实现获取缓存的逻辑
func (a *AuthUsecase) GetCache(ctx context.Context, cacheType dto.CacheType, key string) (string, error) {
	cacheKey := fmt.Sprintf("%s:%s", string(cacheType), key)
	return a.repo.Get(ctx, cacheKey)
}

// tryExtendOAuth2LogoutCache 尝试延长OAuth2登出信息缓存的有效期
func (a AuthUsecase) tryExtendOAuth2LogoutCache(ctx context.Context, userId string) {
	// 1. 获取用户的IDP来源
	idpTypeKey := fmt.Sprintf("user_idp_type:%s", userId)
	idpType, err := a.repo.Get(ctx, idpTypeKey)
	if err == nil && idpType != "" {
		// 如果缓存中有IDP类型信息，直接判断
		if idpType != string(dto.IDPTypeOAuth2) {
			return // 非OAuth2类型，直接跳过
		}
	} else {
		return // 无法获取IDP类型，跳过
	}

	// 2. 获取用户的IDP来源
	idpId, err := a.getIdpIdFromContext(ctx, userId)
	if err != nil || idpId == "" {
		return // 无法获取用户IDP，跳过
	}

	// 3. 查找对应的外部用户
	externalUser, err := a.userRepo.GetExternalUserByLocalUserId(ctx, userId)
	if err != nil {
		return
	}

	// 4. 检查并延长缓存
	cacheKey := fmt.Sprintf("oauth2_logout_info:%s:%s", externalUser.Userid, idpId)
	userInfoJson, err := a.repo.Get(ctx, cacheKey)
	if err == nil && userInfoJson != "" {
		// 缓存存在，延长其有效期
		a.repo.Set(ctx, cacheKey, userInfoJson, 7*24*time.Hour) // 延长到7天
		a.log.Debugf("RefreshToken时更新了用户(%s)的OAuth2登出缓存", userId)
	}
}

// determineSecondaryType 确定二次认证类型
func (a AuthUsecase) determineSecondaryType(idps []*model.TbIdentityProvider) (string, string) {
	if len(idps) == 0 {
		return "", ""
	}

	// 按优先级确定认证类型和渠道
	for _, idp := range idps {
		switch dto.IDPType(idp.TemplateType) {
		case dto.IDPTypeSMS:
			return "verification_code", "sms" // 类别, 发送通道
		case dto.IDPTypeEmail:
			return "verification_code", "email" // 类别, 发送通道
		case dto.IDPTypeTotp:
			return "totp", "totp" // 假设未来的TOTP类型
			// 可添加其他类型
		}
	}

	// 默认使用第一个IDP的类型
	return "other", idps[0].Type
}

// 会话管理相关方法

// ListUserSessions 获取用户会话列表
func (a *AuthUsecase) ListUserSessions(ctx context.Context, corpId, userId, userName, clientType string, limit, offset int) ([]*dto.UserSessionInfo, int, error) {
	// 构建查询参数
	var sessions []*dto.UserSessionInfo
	var total int

	// 从会话跟踪中获取活跃会话
	var activeJWTIds []string
	var err error

	if userId != "" {
		// 如果指定了用户ID，获取该用户的会话
		if clientType == "" || clientType == "all" {
			// 获取所有类型的会话
			pcJWTIds, err := a.getActiveJWTIds(ctx, corpId, userId, dto.ClientCategoryPC)
			if err != nil {
				return nil, 0, err
			}
			mobileJWTIds, err := a.getActiveJWTIds(ctx, corpId, userId, dto.ClientCategoryMobile)
			if err != nil {
				return nil, 0, err
			}
			activeJWTIds = append(pcJWTIds, mobileJWTIds...)
		} else {
			// 获取指定类型的会话
			activeJWTIds, err = a.getActiveJWTIds(ctx, corpId, userId, dto.ClientCategory(clientType))
			if err != nil {
				return nil, 0, err
			}
		}
	} else {
		// 获取所有用户会话的逻辑
		// 使用新增的方法直接从数据库获取，支持按用户名和客户端类型过滤
		allSessions, totalCount, err := a.trackerRepo.GetAllActiveSessionsByCorpId(ctx, corpId, userName, clientType, limit, offset)
		if err != nil {
			return nil, 0, fmt.Errorf("获取所有用户会话失败: %w", err)
		}

		// 为了适配新的proto结构，需要为每个会话填充用户信息
		// 使用批量优化方法避免N+1查询问题
		if err := a.populateSessionUsersBatch(ctx, allSessions); err != nil {
			a.log.Warnf("批量填充会话用户信息失败: error=%v", err)
			// 如果批量填充失败，回退到单个填充
			for _, session := range allSessions {
				if err := a.populateSessionUsers(ctx, session); err != nil {
					a.log.Warnf("填充会话用户信息失败: session_id=%s, error=%v", session.ID, err)
				}
			}
		}

		return allSessions, totalCount, nil
	}

	// 转换为UserSessionInfo格式
	// 先收集所有会话，然后批量填充用户信息避免N+1查询
	var tempSessions []*dto.UserSessionInfo
	for i, jwtId := range activeJWTIds {
		if i < offset {
			continue
		}
		if len(tempSessions) >= limit {
			break
		}

		sessionInfo, err := a.getSessionInfoByJWTId(ctx, jwtId)
		if err != nil {
			a.log.Warnf("获取会话信息失败: %v", err)
			continue
		}

		if sessionInfo != nil {
			// 如果指定了用户名，进行过滤
			if userName != "" && !strings.Contains(sessionInfo.UserName, userName) {
				continue
			}

			tempSessions = append(tempSessions, sessionInfo)
		}
	}

	// 批量填充用户信息
	if err := a.populateSessionUsersBatch(ctx, tempSessions); err != nil {
		a.log.Warnf("批量填充会话用户信息失败: error=%v", err)
		// 如果批量填充失败，回退到单个填充
		for _, session := range tempSessions {
			if err := a.populateSessionUsers(ctx, session); err != nil {
				a.log.Warnf("填充会话用户信息失败: session_id=%s, error=%v", session.ID, err)
			}
		}
	}

	sessions = tempSessions

	total = len(activeJWTIds)
	return sessions, total, nil
}

// KickUserSession 踢出用户会话
func (a *AuthUsecase) KickUserSession(ctx context.Context, jwtId, reason string) error {
	// 1. 将当前 access token 的 JWT ID 加入黑名单（立即生效）
	err := a.blacklistRepo.AddToBlacklist(ctx, jwtId, time.Now().Add(24*time.Hour))
	if err != nil {
		a.log.Errorf("Failed to add access token JWT to blacklist: %v", err)
		return err
	}

	// 2. 获取会话信息以便后续拉黑对应的 refresh token
	sessionInfo, err := a.trackerRepo.GetSessionByJWTId(ctx, jwtId)
	if err != nil {
		a.log.Warnf("Failed to get session info: %v", err)
		// 继续执行，即使无法获取会话信息也要完成踢出操作
	}

	// 3. 拉黑对应的 refresh token
	if sessionInfo != nil && sessionInfo.RefreshJWTId != "" {
		// 将refresh token的JTI加入黑名单
		err = a.blacklistRepo.AddToBlacklist(ctx, sessionInfo.RefreshJWTId, time.Now().Add(24*time.Hour))
		if err != nil {
			a.log.Errorf("Failed to add refresh token JWT to blacklist: %v", err)
		} else {
			a.log.Infof("Successfully added refresh token to blacklist: refreshJti=%s", sessionInfo.RefreshJWTId)
		}
	}

	// 4. 软删除会话记录（标记为kicked状态）
	err = a.trackerRepo.KickSession(ctx, jwtId, reason)
	if err != nil {
		a.log.Errorf("Failed to kick session tracker: %v", err)
		// 不要因为更新会话记录失败而返回错误，因为JWT已经被加入黑名单了
		// 这样可以确保踢出功能总是有效的
	}

	// 5. 记录管理员操作日志
	a.recordKickSessionLog(ctx, jwtId, sessionInfo, reason)

	// 6. 记录踢出日志
	// a.log.Infof("User session kicked: jwtId=%s, reason=%s", jwtId, reason)

	return nil
}

// KickUserSessionBySessionId 通过会话ID踢出用户会话
func (a *AuthUsecase) KickUserSessionBySessionId(ctx context.Context, sessionId, reason string) error {
	// 1. 根据session ID查询会话信息
	sessionInfo, err := a.trackerRepo.GetSessionBySessionId(ctx, sessionId)
	if err != nil {
		a.log.Errorf("Failed to get session info by sessionId: %v", err)
		return fmt.Errorf("获取会话信息失败: %v", err)
	}

	if sessionInfo == nil {
		return fmt.Errorf("会话不存在或已失效")
	}

	// 2. 将当前 access token 的 JWT ID 加入黑名单（立即生效）
	if sessionInfo.JWTId != "" {
		expiredAt := sessionInfo.ExpiresAt
		err = a.blacklistRepo.AddToBlacklist(ctx, sessionInfo.JWTId, expiredAt)
		if err != nil {
			a.log.Errorf("Failed to add access token JWT to blacklist: %v", err)
		} else {
			a.log.Infof("Access token blacklisted: %s", sessionInfo.JWTId)
		}
	}

	// 3. 拉黑对应的 refresh token
	if sessionInfo.RefreshJWTId != "" {
		// refresh token通常有更长的过期时间，使用合理的默认值
		refreshExpiredAt := time.Now().Add(7 * 24 * time.Hour) // 7天
		err = a.blacklistRepo.AddToBlacklist(ctx, sessionInfo.RefreshJWTId, refreshExpiredAt)
		if err != nil {
			a.log.Errorf("Failed to add refresh token JWT to blacklist: %v", err)
		} else {
			a.log.Infof("Refresh token blacklisted: %s", sessionInfo.RefreshJWTId)
		}
	}

	// 4. 软删除会话记录（标记为kicked状态）
	err = a.trackerRepo.KickSession(ctx, sessionInfo.JWTId, reason)
	if err != nil {
		a.log.Errorf("Failed to kick session tracker: %v", err)
		// 不要因为更新会话记录失败而返回错误，因为JWT已经被加入黑名单了
	}

	// 5. 记录管理员操作日志
	a.recordKickSessionLog(ctx, sessionInfo.JWTId, sessionInfo, reason)

	// 6. 记录踢出日志
	// a.log.Infof("User session kicked by sessionId: sessionId=%s, userId=%s, jwtId=%s, reason=%s",
	// 	sessionId, sessionInfo.UserID, sessionInfo.JWTId, reason)

	return nil
}

// getActiveJWTIds 获取用户活跃的JWT IDs（辅助方法）
func (a *AuthUsecase) getActiveJWTIds(ctx context.Context, corpId, userId string, clientCategory dto.ClientCategory) ([]string, error) {
	return a.trackerRepo.GetUserActiveJWTIds(ctx, corpId, userId, clientCategory)
}

// getSessionInfoByJWTId 根据JWT ID获取会话信息（辅助方法）
func (a *AuthUsecase) getSessionInfoByJWTId(ctx context.Context, jwtId string) (*dto.UserSessionInfo, error) {
	return a.trackerRepo.GetSessionByJWTId(ctx, jwtId)
}

// populateSessionUsersBatch 批量填充会话用户信息，避免N+1查询问题
func (a *AuthUsecase) populateSessionUsersBatch(ctx context.Context, sessions []*dto.UserSessionInfo) error {
	if len(sessions) == 0 {
		return nil
	}

	// 按租户ID分组会话
	corpSessionsMap := make(map[string][]*dto.UserSessionInfo)
	for _, session := range sessions {
		if session.CorpID != "" {
			corpSessionsMap[session.CorpID] = append(corpSessionsMap[session.CorpID], session)
		}
	}

	// 为每个租户批量处理
	for corpId, corpSessions := range corpSessionsMap {
		// 一次性获取租户下所有用户组（避免每个用户都查询一次）
		var treeHandler common.RootToNodeSimpleTree
		var groupNodeMap map[string]*model.TbUserGroup

		groups, err := a.ugRepo.ListUserGroup(ctx, corpId)
		if err != nil {
			a.log.Warnf("获取用户组列表失败: corpId=%s, error=%v", corpId, err)
		} else {
			// 构建树处理器
			err = treeHandler.Build(groups)
			if err != nil {
				a.log.Warnf("构建组织树失败: corpId=%s, error=%v", corpId, err)
			} else {
				// 构建组节点映射
				groupNodeMap = common.BuildGroupNodeMap(groups)
			}
		}

		// 为每个会话单独查询用户信息（保持现有逻辑）
		for _, session := range corpSessions {
			if session.UserID == "" {
				continue
			}

			// 从数据库查询用户详细信息
			userEntity, err := a.userRepo.QueryUserEntity(ctx, session.CorpID, session.UserID)
			if err != nil {
				a.log.Warnf("查询用户实体信息失败: userId=%s, error=%v", session.UserID, err)
				// 如果查询失败，使用会话中的基本信息作为备选
				userDto := dto.UserEntity{
					ID:          session.UserID,
					Name:        session.UserName,
					DisplayName: session.DisplayName,
				}
				session.Users = []dto.UserEntity{userDto}
				continue
			}

			// 查询用户组名称，组装完整部门路径名称（复用已查询的用户组）
			var groupName string
			if userEntity.GroupID != "" && groupNodeMap != nil {
				// 获取从根到节点的完整路径
				groupPathIds := treeHandler.GetRootToNodePath(dto.FakeRootUserGroupId, userEntity.GroupID)
				groupPath := ""
				for _, id := range groupPathIds {
					if id == dto.FakeRootUserGroupId {
						continue
					}
					if node, exists := groupNodeMap[id]; exists {
						groupPath = groupPath + "/" + node.Name
					}
				}
				groupName = groupPath
			}

			// 查询用户来源类型
			var sourceType string
			if userEntity.SourceID != "" {
				source, err := a.userSource.GetUserSource(ctx, session.CorpID, userEntity.SourceID)
				if err != nil {
					a.log.Warnf("查询用户来源信息失败: sourceId=%s, error=%v", userEntity.SourceID, err)
					sourceType = ""
				} else {
					sourceType = source.SourceType
				}
			}

			// 构建完整的用户实体
			userDto := dto.UserEntity{
				ID:           userEntity.ID,
				Name:         userEntity.Name,
				GroupID:      userEntity.GroupID,
				SourceID:     userEntity.SourceID,
				Phone:        userEntity.Phone,
				Email:        userEntity.Email,
				Roles:        []*admin.RoleInfo{}, // 不查询角色信息，设置为空
				Enable:       userEntity.Enable,
				ExpireType:   string(userEntity.ExpireType),
				ExpireEnd:    userEntity.ExpireEnd.Format("2006-01-02 15:04:05"),
				DisplayName:  userEntity.DisplayName,
				Identifier:   userEntity.Identify, // 注意：数据库字段是Identify
				AuthType:     userEntity.AuthType,
				ActiveTime:   userEntity.ActiveTime.Format("2006-01-02 15:04:05"),
				LockStatus:   userEntity.LockStatus,
				SecurityCode: userEntity.SecurityCode,
				GroupName:    groupName,  // 查询到的组名
				SourceType:   sourceType, // 查询到的来源类型
				IdleDay:      "",         // 不需要这个字段
			}

			session.Users = []dto.UserEntity{userDto}

			// 设置ClientCategory字段
			if session.ClientType != "" {
				clientType := dto.ClientType(session.ClientType)
				session.ClientCategory = clientType.GetClientCategory()
			}
		}
	}

	return nil
}

// populateSessionUsers 填充会话的用户信息列表（适配新的proto结构）
func (a *AuthUsecase) populateSessionUsers(ctx context.Context, session *dto.UserSessionInfo) error {
	if session == nil {
		return fmt.Errorf("session is nil")
	}

	// 如果UserID为空，无法填充用户信息
	if session.UserID == "" {
		return fmt.Errorf("session UserID is empty")
	}

	// 从数据库查询用户详细信息
	userEntity, err := a.userRepo.QueryUserEntity(ctx, session.CorpID, session.UserID)
	if err != nil {
		a.log.Warnf("查询用户实体信息失败: userId=%s, error=%v", session.UserID, err)
		// 如果查询失败，使用会话中的基本信息作为备选
		userDto := dto.UserEntity{
			ID:          session.UserID,
			Name:        session.UserName,
			DisplayName: session.DisplayName,
		}
		session.Users = []dto.UserEntity{userDto}
	} else {
		// 查询用户组名称，组装完整部门路径名称
		var groupName string
		if userEntity.GroupID != "" {
			// 获取租户下所有组
			groups, err := a.ugRepo.ListUserGroup(ctx, session.CorpID)
			if err != nil {
				a.log.Warnf("获取用户组列表失败: error=%v", err)
				groupName = ""
			} else {
				// 构建树处理器
				treeHandler := common.RootToNodeSimpleTree{}
				err = treeHandler.Build(groups)
				if err != nil {
					a.log.Warnf("构建组织树失败: error=%v", err)
					groupName = ""
				} else {
					// 构建组节点映射
					groupNodeMap := common.BuildGroupNodeMap(groups)
					// 获取从根到节点的完整路径
					groupPathIds := treeHandler.GetRootToNodePath(dto.FakeRootUserGroupId, userEntity.GroupID)
					groupPath := ""
					for _, id := range groupPathIds {
						if id == dto.FakeRootUserGroupId {
							continue
						}
						groupPath = groupPath + "/" + groupNodeMap[id].Name
					}
					groupName = groupPath
				}
			}
		}

		// 查询用户来源类型
		var sourceType string
		if userEntity.SourceID != "" {
			source, err := a.userSource.GetUserSource(ctx, session.CorpID, userEntity.SourceID)
			if err != nil {
				a.log.Warnf("查询用户来源信息失败: sourceId=%s, error=%v", userEntity.SourceID, err)
				sourceType = ""
			} else {
				sourceType = source.SourceType
			}
		}

		// 构建完整的用户实体（不包含角色信息）
		userDto := dto.UserEntity{
			ID:           userEntity.ID,
			Name:         userEntity.Name,
			GroupID:      userEntity.GroupID,
			SourceID:     userEntity.SourceID,
			Phone:        userEntity.Phone,
			Email:        userEntity.Email,
			Roles:        []*admin.RoleInfo{}, // 不查询角色信息，设置为空
			Enable:       userEntity.Enable,
			ExpireType:   string(userEntity.ExpireType),
			ExpireEnd:    userEntity.ExpireEnd.Format("2006-01-02 15:04:05"),
			DisplayName:  userEntity.DisplayName,
			Identifier:   userEntity.Identify, // 注意：数据库字段是Identify
			AuthType:     userEntity.AuthType,
			ActiveTime:   userEntity.ActiveTime.Format("2006-01-02 15:04:05"),
			LockStatus:   userEntity.LockStatus,
			SecurityCode: userEntity.SecurityCode,
			GroupName:    groupName,  // 查询到的组名
			SourceType:   sourceType, // 查询到的来源类型
			IdleDay:      "",         // 不需要这个字段
		}

		session.Users = []dto.UserEntity{userDto}
	}

	// 设置ClientCategory字段
	if session.ClientType != "" {
		clientType := dto.ClientType(session.ClientType)
		session.ClientCategory = clientType.GetClientCategory()
	}

	return nil
}

// ListAllUserSessions 获取企业下所有用户会话（便捷方法）
func (a *AuthUsecase) ListAllUserSessions(ctx context.Context, corpId, userName, clientType string, limit, offset int) ([]*dto.UserSessionInfo, int, error) {
	return a.ListUserSessions(ctx, corpId, "", userName, clientType, limit, offset)
}
