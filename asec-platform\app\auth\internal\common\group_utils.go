package common

import (
	"fmt"
	"sort"
	"strings"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
)

// BuildGroupTree 自顶向下具备多种属性的树
func BuildGroupTree(input []*dto.UserGroupNode) (*dto.UserGroupTree, error) {
	nodes := make(map[string]*dto.UserGroupTree)
	for _, n := range input {
		// 修复带有PostgreSQL类型注解的ParentGroupId
		if strings.Contains(n.ParentGroupId, "'::character varying") {
			n.ParentGroupId = strings.Split(n.ParentGroupId, "'::")[0]
		}
		nodes[n.Id] = &dto.UserGroupTree{
			UserGroupNode: *n,
		}
	}

	var root = &dto.UserGroupTree{UserGroupNode: dto.UserGroupNode{Id: dto.FakeRootUserGroupId}}
	for _, n := range nodes {
		if n.ParentGroupId == dto.FakeRootUserGroupId {
			root.Children = append(root.Children, n)
			sort.Slice(root.Children, func(i, j int) bool {
				return root.Children[i].CreatedAt.After(root.Children[j].CreatedAt)
			})
		} else {
			parent, ok := nodes[n.ParentGroupId]
			if !ok {
				return nil, fmt.Errorf("n's parent not found. node=%+v", n)
			}
			parent.Children = append(parent.Children, n)
			sort.Slice(parent.Children, func(i, j int) bool {
				return parent.Children[i].CreatedAt.After(parent.Children[j].CreatedAt)
			})
		}
	}
	return root, nil
}

// SearchSubTree 查找子树
func SearchSubTree(tree *dto.UserGroupTree, id string) *dto.UserGroupTree {
	if tree == nil {
		return nil
	}
	if tree.Id == id {
		return tree
	}
	for _, child := range tree.Children {
		subTree := SearchSubTree(child, id)
		if subTree != nil && subTree.Id == id {
			return subTree
		}
	}
	return &dto.UserGroupTree{}
}

// BuildGroupToRootConnect 构造一颗自底向上指向root的简易树
func BuildGroupToRootConnect(input []*model.TbUserGroup) (map[string]string, error) {
	result := make(map[string]string)
	for _, e := range input {
		if _, ok := result[e.ID]; ok {
			return map[string]string{}, pb.ErrorUserGroupDuplicate("id=%v duplicate", e.ID)
		}
		result[e.ID] = e.ParentGroupID
	}
	return result, nil
}

func SearchGroupRoot(connect map[string]string, groupId string) (string, error) {
	record := make(map[string]struct{})
	p := groupId
	for {
		parent, ok := connect[p]
		if !ok {
			return "", pb.ErrorGroupNotFound("id=%v not found", p)
		}
		if parent == dto.FakeRootUserGroupId {
			return p, nil
		}
		if _, ok := record[parent]; ok {
			return "", pb.ErrorUserGroupCircle("connect=%v has circle", connect)
		}
		record[parent] = struct{}{}
		p = parent
	}
}

func GroupIsChild(connect map[string]string, child string, target string) (bool, error) {
	if child == target {
		return true, nil
	}
	record := make(map[string]struct{})
	p := child
	for {
		parent, ok := connect[p]
		if !ok {
			return false, pb.ErrorGroupNotFound("id=%v not found", p)
		}
		if parent == dto.FakeRootUserGroupId {
			return false, nil
		}
		if parent == target {
			return true, nil
		}
		if _, ok := record[parent]; ok {
			return false, pb.ErrorUserGroupCircle("connect=%v has circle", connect)
		}
		record[parent] = struct{}{}
		p = parent
	}
}

func BuildGroupNodeMap(input []*model.TbUserGroup) map[string]*model.TbUserGroup {
	var result = make(map[string]*model.TbUserGroup)
	for _, g := range input {
		result[g.ID] = g
	}
	return result
}

type RootToNodeSimpleTree struct {
	tree map[string][]string
}

// Build 构造一颗自顶向下指向叶子节点的简易树
func (rnst *RootToNodeSimpleTree) Build(input []*model.TbUserGroup) error {
	result := make(map[string][]string)
	for _, e := range input {
		result[e.ID] = []string{}
	}

	for _, e := range input {
		result[e.ParentGroupID] = append(result[e.ParentGroupID], e.ID)
	}
	rnst.tree = result
	if rnst.CircleCheck(dto.FakeRootUserGroupId) {
		return pb.ErrorParamError("input build tree has circle. build result=%v", result)
	}
	return nil
}

func (rnst *RootToNodeSimpleTree) CircleCheck(root string) bool {
	curLevel := []string{root}
	recorded := make(map[string]struct{})
	for {
		var nextLevel []string
		for _, curNode := range curLevel {
			if _, ok := recorded[curNode]; ok {
				return true
			}
			recorded[curNode] = struct{}{}
			nextLevel = append(nextLevel, rnst.tree[curNode]...)
		}
		if len(nextLevel) == 0 {
			break
		}
		curLevel = nextLevel
	}
	return false
}

func (rnst *RootToNodeSimpleTree) GetAllSubNode(root string) []string {
	child, ok := rnst.tree[root]
	if !ok {
		return []string{}
	}
	var result []string
	result = append(result, root)
	for _, c := range child {
		subNode := rnst.GetAllSubNode(c)
		result = append(result, subNode...)
	}
	return result
}

// GetRootToNodePath 这个函数的tree必须要经过CircleCheck，不然递归会打爆内存
func (rnst *RootToNodeSimpleTree) GetRootToNodePath(root, node string) []string {
	if root == node {
		return []string{root}
	}

	child, ok := rnst.tree[root]
	if !ok || len(child) == 0 {
		return []string{}
	}
	var path []string
	for _, c := range child {
		path = rnst.GetRootToNodePath(c, node)
		if len(path) != 0 {
			path = append([]string{root}, path...)
			break
		}
	}
	return path
}
