package service

import (
	"asdsec.com/asec/platform/app/console/app/access/dto"
	"asdsec.com/asec/platform/app/console/app/access/repository"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"
	"sync"
	"time"
)

var AccessLogServiceImpl AccessLogService

var AccessLogServiceInit sync.Once

type accessLogService struct {
	db repository.AccessLogRepository
}

func (a accessLogService) AccessTopN(ctx context.Context, top int, days int) ([]dto.AccessTopData, error) {
	data, err := a.db.AccessTopN(ctx, top, days)
	if err != nil {
		return nil, err
	}
	var result []dto.AccessTopData
	for _, value := range data {
		var tmpTopData dto.AccessTopData
		err := copier.Copy(&tmpTopData, &value)
		if err != nil {
			return nil, err
		}

		jsonb, err := value.Data.MarshalJSON()
		if err != nil {
			return nil, err
		}

		var dateList []dto.DateItem
		err = json.Unmarshal(jsonb, &dateList)
		if err != nil {
			return nil, err
		}
		//将时间序列转换为字典加快速度
		timeMap := make(map[string]int, 0)
		for _, it := range dateList {
			timeMap[it.Date] = it.Count
		}
		dateList = dateList[:0]
		//遍历时间列表
		currentTime := time.Now()
		currentTime = currentTime.AddDate(0, 0, -days)
		for i := 0; i < days; currentTime = currentTime.AddDate(0, 0, 1) {
			currentTimeStr := currentTime.Format("2006-01-02")
			if _, ok := timeMap[currentTimeStr]; ok {
				tmpTopData.Data = append(tmpTopData.Data, dto.DateItem{Date: currentTimeStr, Count: timeMap[currentTimeStr]})
			} else {
				tmpTopData.Data = append(tmpTopData.Data, dto.DateItem{Date: currentTimeStr, Count: 0})
			}
			i += 1
		}
		result = append(result, tmpTopData)
	}

	return result, nil
}

func (a accessLogService) AccessCount(ctx context.Context, days int) (count dto.AccessCount, err error) {
	return a.db.AccessCount(ctx, days)
}

func (a accessLogService) ListAccessLogs(ctx context.Context, req dto.ListAccessLogReq) (model.Pagination, error) {
	return a.db.ListAccessLogs(ctx, req)
}

type AccessLogService interface {
	ListAccessLogs(ctx context.Context, req dto.ListAccessLogReq) (model.Pagination, error)
	AccessCount(ctx context.Context, days int) (dto.AccessCount, error)
	AccessTopN(ctx context.Context, top int, days int) ([]dto.AccessTopData, error)
}

func GetAccessLogService() AccessLogService {
	AccessLogServiceInit.Do(func() {
		AccessLogServiceImpl = &accessLogService{db: repository.NewAccessLogRepository()}
	})
	return AccessLogServiceImpl
}
