// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: application/v1/scan_task.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpsetScanTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskList []*ScanTaskResult `protobuf:"bytes,1,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
}

func (x *UpsetScanTaskReq) Reset() {
	*x = UpsetScanTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_scan_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsetScanTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsetScanTaskReq) ProtoMessage() {}

func (x *UpsetScanTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_scan_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsetScanTaskReq.ProtoReflect.Descriptor instead.
func (*UpsetScanTaskReq) Descriptor() ([]byte, []int) {
	return file_application_v1_scan_task_proto_rawDescGZIP(), []int{0}
}

func (x *UpsetScanTaskReq) GetTaskList() []*ScanTaskResult {
	if x != nil {
		return x.TaskList
	}
	return nil
}

type ScanTaskResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId      string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskDate    string `protobuf:"bytes,2,opt,name=task_date,json=taskDate,proto3" json:"task_date,omitempty"`
	UserId      string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName    string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	AgentId     string `protobuf:"bytes,5,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName   string `protobuf:"bytes,6,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	TaskStatus  string `protobuf:"bytes,7,opt,name=task_status,json=taskStatus,proto3" json:"task_status,omitempty"`
	L1FileCount uint32 `protobuf:"varint,8,opt,name=l1_file_count,json=l1FileCount,proto3" json:"l1_file_count,omitempty"`
	L2FileCount uint32 `protobuf:"varint,9,opt,name=l2_file_count,json=l2FileCount,proto3" json:"l2_file_count,omitempty"`
	L3FileCount uint32 `protobuf:"varint,10,opt,name=l3_file_count,json=l3FileCount,proto3" json:"l3_file_count,omitempty"`
	L4FileCount uint32 `protobuf:"varint,11,opt,name=l4_file_count,json=l4FileCount,proto3" json:"l4_file_count,omitempty"`
	ScanCount   uint32 `protobuf:"varint,12,opt,name=scan_count,json=scanCount,proto3" json:"scan_count,omitempty"`
}

func (x *ScanTaskResult) Reset() {
	*x = ScanTaskResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_scan_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanTaskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanTaskResult) ProtoMessage() {}

func (x *ScanTaskResult) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_scan_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanTaskResult.ProtoReflect.Descriptor instead.
func (*ScanTaskResult) Descriptor() ([]byte, []int) {
	return file_application_v1_scan_task_proto_rawDescGZIP(), []int{1}
}

func (x *ScanTaskResult) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ScanTaskResult) GetTaskDate() string {
	if x != nil {
		return x.TaskDate
	}
	return ""
}

func (x *ScanTaskResult) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ScanTaskResult) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ScanTaskResult) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *ScanTaskResult) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *ScanTaskResult) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

func (x *ScanTaskResult) GetL1FileCount() uint32 {
	if x != nil {
		return x.L1FileCount
	}
	return 0
}

func (x *ScanTaskResult) GetL2FileCount() uint32 {
	if x != nil {
		return x.L2FileCount
	}
	return 0
}

func (x *ScanTaskResult) GetL3FileCount() uint32 {
	if x != nil {
		return x.L3FileCount
	}
	return 0
}

func (x *ScanTaskResult) GetL4FileCount() uint32 {
	if x != nil {
		return x.L4FileCount
	}
	return 0
}

func (x *ScanTaskResult) GetScanCount() uint32 {
	if x != nil {
		return x.ScanCount
	}
	return 0
}

type CommonScanTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *CommonScanTaskResp) Reset() {
	*x = CommonScanTaskResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_scan_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonScanTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonScanTaskResp) ProtoMessage() {}

func (x *CommonScanTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_scan_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonScanTaskResp.ProtoReflect.Descriptor instead.
func (*CommonScanTaskResp) Descriptor() ([]byte, []int) {
	return file_application_v1_scan_task_proto_rawDescGZIP(), []int{2}
}

func (x *CommonScanTaskResp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonScanTaskResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CreateScanFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScanFile []*ScanFile `protobuf:"bytes,1,rep,name=scan_file,json=scanFile,proto3" json:"scan_file,omitempty"`
}

func (x *CreateScanFileReq) Reset() {
	*x = CreateScanFileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_scan_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateScanFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateScanFileReq) ProtoMessage() {}

func (x *CreateScanFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_scan_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateScanFileReq.ProtoReflect.Descriptor instead.
func (*CreateScanFileReq) Descriptor() ([]byte, []int) {
	return file_application_v1_scan_task_proto_rawDescGZIP(), []int{3}
}

func (x *CreateScanFileReq) GetScanFile() []*ScanFile {
	if x != nil {
		return x.ScanFile
	}
	return nil
}

type ScanFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TaskId            string `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	AgentId           string `protobuf:"bytes,3,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	UserId            string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FilePath          string `protobuf:"bytes,5,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	FileSensitiveInfo string `protobuf:"bytes,6,opt,name=file_sensitive_info,json=fileSensitiveInfo,proto3" json:"file_sensitive_info,omitempty"`
	TraceIds          string `protobuf:"bytes,7,opt,name=trace_ids,json=traceIds,proto3" json:"trace_ids,omitempty"`
	SubTraceIds       string `protobuf:"bytes,8,opt,name=sub_trace_ids,json=subTraceIds,proto3" json:"sub_trace_ids,omitempty"`
	BasicSrcPaths     string `protobuf:"bytes,9,opt,name=basic_src_paths,json=basicSrcPaths,proto3" json:"basic_src_paths,omitempty"`
	ExtensionName     string `protobuf:"bytes,10,opt,name=extension_name,json=extensionName,proto3" json:"extension_name,omitempty"`
	Md5               string `protobuf:"bytes,11,opt,name=md5,proto3" json:"md5,omitempty"`
	FileCategoryId    uint32 `protobuf:"varint,12,opt,name=file_category_id,json=fileCategoryId,proto3" json:"file_category_id,omitempty"`
	FileTypeSuffix    uint32 `protobuf:"varint,13,opt,name=file_type_suffix,json=fileTypeSuffix,proto3" json:"file_type_suffix,omitempty"`
}

func (x *ScanFile) Reset() {
	*x = ScanFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_scan_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanFile) ProtoMessage() {}

func (x *ScanFile) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_scan_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanFile.ProtoReflect.Descriptor instead.
func (*ScanFile) Descriptor() ([]byte, []int) {
	return file_application_v1_scan_task_proto_rawDescGZIP(), []int{4}
}

func (x *ScanFile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ScanFile) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ScanFile) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *ScanFile) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ScanFile) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *ScanFile) GetFileSensitiveInfo() string {
	if x != nil {
		return x.FileSensitiveInfo
	}
	return ""
}

func (x *ScanFile) GetTraceIds() string {
	if x != nil {
		return x.TraceIds
	}
	return ""
}

func (x *ScanFile) GetSubTraceIds() string {
	if x != nil {
		return x.SubTraceIds
	}
	return ""
}

func (x *ScanFile) GetBasicSrcPaths() string {
	if x != nil {
		return x.BasicSrcPaths
	}
	return ""
}

func (x *ScanFile) GetExtensionName() string {
	if x != nil {
		return x.ExtensionName
	}
	return ""
}

func (x *ScanFile) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ScanFile) GetFileCategoryId() uint32 {
	if x != nil {
		return x.FileCategoryId
	}
	return 0
}

func (x *ScanFile) GetFileTypeSuffix() uint32 {
	if x != nil {
		return x.FileTypeSuffix
	}
	return 0
}

type SensitiveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SensitiveId         string `protobuf:"bytes,1,opt,name=sensitive_id,json=sensitiveId,proto3" json:"sensitive_id,omitempty"`
	SensitiveCategoryId string `protobuf:"bytes,2,opt,name=sensitive_category_id,json=sensitiveCategoryId,proto3" json:"sensitive_category_id,omitempty"`
	SensitiveLevel      uint32 `protobuf:"varint,3,opt,name=sensitive_level,json=sensitiveLevel,proto3" json:"sensitive_level,omitempty"`
	SensitiveName       string `protobuf:"bytes,4,opt,name=sensitive_name,json=sensitiveName,proto3" json:"sensitive_name,omitempty"`
}

func (x *SensitiveInfo) Reset() {
	*x = SensitiveInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_scan_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensitiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensitiveInfo) ProtoMessage() {}

func (x *SensitiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_scan_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensitiveInfo.ProtoReflect.Descriptor instead.
func (*SensitiveInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_scan_task_proto_rawDescGZIP(), []int{5}
}

func (x *SensitiveInfo) GetSensitiveId() string {
	if x != nil {
		return x.SensitiveId
	}
	return ""
}

func (x *SensitiveInfo) GetSensitiveCategoryId() string {
	if x != nil {
		return x.SensitiveCategoryId
	}
	return ""
}

func (x *SensitiveInfo) GetSensitiveLevel() uint32 {
	if x != nil {
		return x.SensitiveLevel
	}
	return 0
}

func (x *SensitiveInfo) GetSensitiveName() string {
	if x != nil {
		return x.SensitiveName
	}
	return ""
}

var File_application_v1_scan_task_proto protoreflect.FileDescriptor

var file_application_v1_scan_task_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x13, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x22, 0x54, 0x0a, 0x10, 0x55, 0x70, 0x73, 0x65, 0x74, 0x53, 0x63,
	0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a, 0x09, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61,
	0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x86, 0x03, 0x0a, 0x0e,
	0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x31, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6c, 0x31,
	0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x32, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0b, 0x6c, 0x32, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a,
	0x0d, 0x6c, 0x33, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6c, 0x33, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x34, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6c, 0x34, 0x46, 0x69, 0x6c, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x42, 0x0a, 0x12, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x63,
	0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x4f, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3a, 0x0a,
	0x09, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x08, 0x73, 0x63, 0x61, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x22, 0xaa, 0x03, 0x0a, 0x08, 0x53, 0x63,
	0x61, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x2e, 0x0a, 0x13, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66,
	0x69, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a,
	0x0d, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x73, 0x72, 0x63, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x61, 0x73, 0x69,
	0x63, 0x53, 0x72, 0x63, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x64, 0x35, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x22, 0xb6, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x73,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x32,
	0xce, 0x01, 0x0a, 0x08, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x5f, 0x0a, 0x0d,
	0x55, 0x70, 0x73, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x25, 0x2e,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x61, 0x0a,
	0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x26, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x42, 0x28, 0x5a, 0x26, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61,
	0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_application_v1_scan_task_proto_rawDescOnce sync.Once
	file_application_v1_scan_task_proto_rawDescData = file_application_v1_scan_task_proto_rawDesc
)

func file_application_v1_scan_task_proto_rawDescGZIP() []byte {
	file_application_v1_scan_task_proto_rawDescOnce.Do(func() {
		file_application_v1_scan_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_application_v1_scan_task_proto_rawDescData)
	})
	return file_application_v1_scan_task_proto_rawDescData
}

var file_application_v1_scan_task_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_application_v1_scan_task_proto_goTypes = []interface{}{
	(*UpsetScanTaskReq)(nil),   // 0: asdsec.core.api.app.UpsetScanTaskReq
	(*ScanTaskResult)(nil),     // 1: asdsec.core.api.app.ScanTaskResult
	(*CommonScanTaskResp)(nil), // 2: asdsec.core.api.app.CommonScanTaskResp
	(*CreateScanFileReq)(nil),  // 3: asdsec.core.api.app.CreateScanFileReq
	(*ScanFile)(nil),           // 4: asdsec.core.api.app.ScanFile
	(*SensitiveInfo)(nil),      // 5: asdsec.core.api.app.SensitiveInfo
}
var file_application_v1_scan_task_proto_depIdxs = []int32{
	1, // 0: asdsec.core.api.app.UpsetScanTaskReq.task_list:type_name -> asdsec.core.api.app.ScanTaskResult
	4, // 1: asdsec.core.api.app.CreateScanFileReq.scan_file:type_name -> asdsec.core.api.app.ScanFile
	0, // 2: asdsec.core.api.app.ScanTask.UpsetScanTask:input_type -> asdsec.core.api.app.UpsetScanTaskReq
	3, // 3: asdsec.core.api.app.ScanTask.CreateScanFile:input_type -> asdsec.core.api.app.CreateScanFileReq
	2, // 4: asdsec.core.api.app.ScanTask.UpsetScanTask:output_type -> asdsec.core.api.app.CommonScanTaskResp
	2, // 5: asdsec.core.api.app.ScanTask.CreateScanFile:output_type -> asdsec.core.api.app.CommonScanTaskResp
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_application_v1_scan_task_proto_init() }
func file_application_v1_scan_task_proto_init() {
	if File_application_v1_scan_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_application_v1_scan_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsetScanTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_scan_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanTaskResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_scan_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonScanTaskResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_scan_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateScanFileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_scan_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_scan_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensitiveInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_application_v1_scan_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_application_v1_scan_task_proto_goTypes,
		DependencyIndexes: file_application_v1_scan_task_proto_depIdxs,
		MessageInfos:      file_application_v1_scan_task_proto_msgTypes,
	}.Build()
	File_application_v1_scan_task_proto = out.File
	file_application_v1_scan_task_proto_rawDesc = nil
	file_application_v1_scan_task_proto_goTypes = nil
	file_application_v1_scan_task_proto_depIdxs = nil
}
