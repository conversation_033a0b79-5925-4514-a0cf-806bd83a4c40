package common

const (
	NotifyTable       = "tb_notify_setting"
	AlertSettingTable = "tb_alert_setting"
	AlertRecordTable  = "alert_record"
	ERRRobotExit      = "robot name exit"
	ErrRecordNotFound = "record not found"
	ConsumerGroup     = "alert_consumer"
	AlertTopic        = "alert_sec_event"
	TenantId          = uint64(1200)
	WeChat            = "wechat"
	DingTalk          = "dingtalk"
	LocalIp           = "127.0.0.1"
)

var AlertType = map[string]string{
	"bruteForce":               "暴力破解",
	"suspiciousCmdExecution":   "异常命令",
	"suspectedHackerCmd":       "异常命令",
	"maliciousCmdExecution":    "异常命令",
	"exceptionCodeInstruction": "异常命令",
	"PortForward":              "端口转发",
	"remoteCmdExecution":       "远程命令执行",
	"maliciousIpVisit":         "访问恶意地址",
	"maliciousDomainVisit":     "访问恶意地址",
	"illegalLoginTime":         "异常登录",
	"illegalIpLogin":           "异常登录",
	"portScan":                 "异常扫描",
	"hostScan":                 "异常扫描",
	"webMemTrojan":             "内存后门",
	"hackTool":                 "内存后门",
	"webshell":                 "webshell",
	"reboundShell":             "反弹shell",
	"privilegeEscalation":      "权限提升",
	"credentialAccess":      "凭证窃取",
	"lateralMovement":      "横向移动",
}
