syntax = "proto3";
package kratos.api;

option go_package = "asec.com/asec/appliance-center/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string host = 1;
    int32 port = 2;
    string user = 3;
    string password = 4;
    string dbname = 5;
  }
  message Redis {
    string addr = 1;
    string password = 2;
    int32 db = 3;
    google.protobuf.Duration dial_timeout = 4;
    google.protobuf.Duration read_timeout = 5;
    google.protobuf.Duration write_timeout = 6;
  }
  Database database = 1;
  Redis redis = 2;
}