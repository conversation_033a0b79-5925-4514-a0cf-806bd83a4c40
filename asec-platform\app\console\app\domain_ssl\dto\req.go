package dto

type CreateCertificateReq struct {
	Name        string `json:"name" binding:"required"`
	Certificate string `json:"certificate" binding:"required"`
	PrivateKey  string `json:"private_key" binding:"required"`
}

type UpdateCertificateReq struct {
	Id          string `json:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Certificate string `json:"certificate" binding:"required"`
	PrivateKey  string `json:"private_key" binding:"required"`
}

type DelCertificateReq struct {
	Ids  []string `json:"ids" binding:"required"`
	Name string   `json:"name"`
}
