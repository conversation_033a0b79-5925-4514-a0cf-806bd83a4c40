package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"google.golang.org/protobuf/types/known/structpb"

	"asdsec.com/asec/platform/app/auth/internal/common"

	pb "asdsec.com/asec/platform/api/auth/v1/admin"

	"github.com/jinzhu/copier"
)

func (s *AdminService) ListIDPType(ctx context.Context, req *pb.ListIDPTypeRequest) (*pb.ListIDPTypeReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListIDPTypeReply{}, err
	}

	result, err := s.idp.ListIDPType(ctx, corpId)
	if err != nil {
		return &pb.ListIDPTypeReply{}, err
	}
	var resp pb.ListIDPTypeReply
	err = copier.Copy(&resp, &result)
	if err != nil {
		return &pb.ListIDPTypeReply{}, err
	}
	sort.Slice(resp.MainIdp, func(i, j int) bool {
		return resp.MainIdp[i].Type < resp.MainIdp[j].Type
	})
	sort.Slice(resp.AssistIdp, func(i, j int) bool {
		return resp.AssistIdp[i].Type < resp.AssistIdp[j].Type
	})
	return &resp, nil
}

func (s *AdminService) CreateIDP(ctx context.Context, req *pb.CreateIDPRequest) (*pb.CreateIDPReply, error) {
	var param dto.CreateIDPParam
	if err := copier.Copy(&param, req); err != nil {
		return &pb.CreateIDPReply{Status: pb.StatusCode_FAILED}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateIDPReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId
	err = s.idp.CreateIDP(ctx, param)
	if err != nil {
		return &pb.CreateIDPReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateIDPReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) TestResult(ctx context.Context, req *pb.TestResultRequest) (*pb.TestResultReply, error) {
	log := s.log.WithContext(ctx)
	testId := req.TestId
	log.Infof("查询测试结果请求: %v", testId)

	// 检查是否为base64编码的state
	if testId != "" && strings.Contains(testId, "test:") == false {
		// 尝试解码base64
		decoded, err := base64.StdEncoding.DecodeString(testId)
		if err == nil {
			decodedStr := string(decoded)
			if strings.HasPrefix(decodedStr, "test:") {
				parts := strings.Split(decodedStr, ":")
				if len(parts) >= 2 {
					// 提取真正的测试ID
					testId = parts[1]
					log.Infof("从base64编码state中提取测试ID: %s", testId)
				}
			}
		}
	}
	if testId == "" {
		return &pb.TestResultReply{
			Success: false,
			Message: "测试ID不能为空",
		}, nil
	}
	// 解析测试结果
	resultData := make(map[string]interface{})

	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.TestResultReply{
			Success: false,
			Message: "",
		}, nil
	}

	fmt.Println("req.Type:", req.Type)

	// 若为web则调用webauth认证
	if req.Type == "web" {
		loginForm := dto.LoginForm{}
		loginForm.CorpId = corpId
		// 尝试解码base64
		userName, _ := base64.StdEncoding.DecodeString(req.Username)
		loginForm.UserName = string(userName)
		password, _ := base64.StdEncoding.DecodeString(req.Password)
		loginForm.Password = string(password)
		loginForm.IdpId = testId

		logs, err := s.auth.WebAuthTest(ctx, loginForm)
		if err != nil {
			log.Errorf("获取测试结果失败: %v", err)
			logs = logs + "\n" + fmt.Sprintf("获取测试结果失败: %v", err)
		}
		resultData["logs"] = logs
		resultStruct, err := structpb.NewStruct(resultData)
		if err != nil {
			log.Errorf("转换测试结果失败: %v", err)
			return &pb.TestResultReply{
				Success: false,
				Message: "转换测试结果失败",
			}, nil
		}
		return &pb.TestResultReply{
			Success: true,
			Message: "成功获取测试结果",
			Data:    resultStruct,
		}, nil
	}

	// 从缓存获取测试结果
	cacheResult, err := s.auth.GetTestCache(ctx, testId)
	if err != nil {
		log.Errorf("获取测试结果失败: %v", err)
		return &pb.TestResultReply{
			Success: false,
			Message: fmt.Sprintf("获取测试结果失败: %v", err),
		}, nil
	}

	// 如果没有结果，可能测试尚未完成或已过期
	if cacheResult == "" {
		return &pb.TestResultReply{
			Success: false,
			Message: "没有找到测试结果或测试尚未完成",
		}, nil
	}

	if err := json.Unmarshal([]byte(cacheResult), &resultData); err != nil {
		log.Errorf("解析测试结果失败: %v", err)
		return &pb.TestResultReply{
			Success: false,
			Message: fmt.Sprintf("解析测试结果失败: %v", err),
		}, nil
	}

	// 构建响应
	resultStruct, _ := structpb.NewStruct(resultData)
	return &pb.TestResultReply{
		Success: true,
		Message: "成功获取测试结果",
		Data:    resultStruct,
		Result:  cacheResult, // 保留原始结果字符串
	}, nil
}

func (s *AdminService) ListIDP(ctx context.Context, req *pb.ListIDPRequest) (*pb.ListIDPReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListIDPReply{}, err
	}

	limit, offset := common.GetLimitOffset(req.Limit, req.Offset)
	resp, err := s.idp.ListIDP(ctx, corpId, int(limit), int(offset))
	if err != nil {
		return &pb.ListIDPReply{}, err
	}
	var result []*pb.ListIDPReply_IDP
	for _, idp := range resp.IdpInfos {
		groupInfoList := make([]*pb.ListIDPReply_BindGroup, 0)
		for _, e := range idp.BindGroupList {
			groupInfoList = append(groupInfoList, &pb.ListIDPReply_BindGroup{
				Id:   e.ID,
				Name: e.Name,
			})
		}
		var temp pb.ListIDPReply_IDP
		if err := copier.Copy(&temp, &idp); err != nil {
			return &pb.ListIDPReply{}, err
		}
		temp.BindGroupList = groupInfoList
		temp.Id = idp.ID
		if idp.TemplateType == "" || idp.Type == "verify_code" || idp.Type == "totp" {
			temp.TypeName = common.GetIDPTypeName(dto.IDPType(idp.Type))
		} else {
			temp.TypeName = idp.TypeName
		}
		temp.TemplateType = idp.TemplateType
		result = append(result, &temp)
	}
	return &pb.ListIDPReply{IdpList: result, Count: resp.Count}, nil
}

func (s *AdminService) DeleteIDP(ctx context.Context, req *pb.DeleteIDPRequest) (*pb.DeleteIDPReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.DeleteIDPReply{Status: pb.StatusCode_FAILED}, err
	}

	if err := s.idp.DeleteIDP(ctx, corpId, req.Id, req.Name); err != nil {
		return &pb.DeleteIDPReply{Status: pb.StatusCode_FAILED}, err
	}

	return &pb.DeleteIDPReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) GetIDPDetail(ctx context.Context, req *pb.GetIDPDetailRequest) (*pb.GetIDPDetailReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.GetIDPDetailReply{}, nil
	}
	detail, err := s.idp.GetIDPDetail(ctx, corpId, req.Id)
	if err != nil {
		return &pb.GetIDPDetailReply{}, err
	}
	var result pb.GetIDPDetailReply
	if err := copier.Copy(&result, &detail); err != nil {
		return &pb.GetIDPDetailReply{}, err
	}
	var bindGroup []*pb.GetIDPDetailReply_BindGroup
	for _, g := range detail.BindGroupList {
		bindGroup = append(bindGroup, &pb.GetIDPDetailReply_BindGroup{
			Id:   g.ID,
			Name: g.Name,
		})
	}
	result.BindGroupList = bindGroup
	return &result, nil
}

func (s *AdminService) UpdateIDP(ctx context.Context, req *pb.UpdateIDPRequest) (*pb.UpdateIDPReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateIDPReply{Status: pb.StatusCode_FAILED}, err
	}
	var param dto.UpdateIDPParam
	param.CorpId = corpId
	if err := copier.Copy(&param, req); err != nil {
		return &pb.UpdateIDPReply{Status: pb.StatusCode_FAILED}, err
	}
	if err := s.idp.UpdateIDP(ctx, param); err != nil {
		return &pb.UpdateIDPReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateIDPReply{Status: pb.StatusCode_SUCCESS}, nil
}
