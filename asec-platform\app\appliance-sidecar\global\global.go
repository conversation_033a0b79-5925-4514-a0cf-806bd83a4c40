package global

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"time"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/config"
	"asdsec.com/asec/platform/app/console/utils/crypt"
	"asdsec.com/asec/platform/pkg/utils"
	_ "github.com/mattn/go-sqlite3"
	"go.uber.org/zap"
)

const RetryOpenDbCount = 3
const (
	ConfDbName        = "config/asec-client-conf.db"
	EventsDbName      = "asec-client.db"
	CollectInfoDbName = "collect-info.db"
	OffsetDbName      = "upload-offset.db"
	EvidDbName        = "asec-evid.db"
	TaskDbName        = "sensit/task-result.db"
	UserInfoDbName    = "asec-account.db"
	FileResultDbName  = "sensit/file-result.db"
)
const EnableSqliteWalSql = "PRAGMA journal_mode= WAL"

var (
	Conf                   config.SidecarConfig
	Logger                 *zap.Logger
	Context, Cancel        = context.WithCancel(context.Background())
	InnerHost              string
	PublicHost             string
	PrivateHost            string
	PrivateIp              string
	LogServerHost          string
	IDFilePath             string
	Version                string           //版本
	UpgradeTime            string           //客户端升级时间
	ApplianceType          v1.ApplianceType // ApplianceType 设备类型
	ApplianceID            uint64           // ApplianceID 设备ID
	ApplianceName          string           //设备名称
	SqliteClient           *sql.DB
	EvidenceSqliteClient   *sql.DB
	OffsetSqliteClient     *sql.DB
	UserInfoSqliteClient   *sql.DB
	TaskSqliteClient       *sql.DB
	FileResultSqliteClient *sql.DB
	RpcPort                int
	HttpsPort              int
	PlatType               = runtime.GOOS

	LoginUser       string //当前登录用户Id
	DeviceOsVersion string //设备名称/类型,移动端使用
)

func init() {
	IDFilePath = filepath.Join(utils.GetConfigDir(), ".appliance_id")
	//移动端使用外部传入的设备名称，不采用hostname
	if runtime.GOOS != "android" && runtime.GOOS != "ios" {
		ApplianceName = GetAppName()
	}
}

type HostId struct {
	Host string
	Id   uint64
}

func GetAppName() string {
	name, err := os.Hostname()
	if err != nil {
		return ""
	}
	return name
}

// FromIDFile TODO ID文件加密
func FromIDFile(file string, host string) (id uint64, err error) {
	idBytes, err := os.ReadFile(file)
	if err == nil {
		decrypted := crypt.AesDecryptCBC(idBytes, []byte("wertyuiop~!@#$%^&*90-+ZXCVBNM<>?"))
		if decrypted == nil {
			return
		}
		tmp := HostId{}
		err = json.Unmarshal(decrypted, &tmp)
		if err != nil {
			err = errors.New("illegal id file,can not to unmarshal")
			return
		}
		if host != tmp.Host {
			return
		}
		id = tmp.Id
	}
	return
}

func InitSqlite() (*sql.DB, error) {
	var err error
	for i := 0; i < RetryOpenDbCount; i++ {
		db, e := sql.Open("sqlite3", fmt.Sprintf("file:%s/asec-client.db?_auth&_auth_user=admin&_auth_pass=admin&_auth_crypt=sha256", utils.GetConfigDir()))
		if e != nil {
			Logger.Sugar().Errorf("open sqlite db err:%v", err)
			time.Sleep(time.Millisecond * 600)
			err = e
			continue
		} else {
			err := EnableWalForDB(db)
			if err != nil {
				// use zap default logger可以无需关注global.Logger是否已经被初始化，避免发送空指针
				zap.S().Errorf("enble asec-client.db WAL failed,error : %v", err)
			}
			return db, nil
		}
	}
	return nil, fmt.Errorf("open db err:%w", err)
}

func EnableWalForDB(db *sql.DB) error {
	_, err := db.Exec(EnableSqliteWalSql)
	if err != nil {
		return err
	}
	return nil
}

func InitSqliteByName(dbName string) (*sql.DB, error) {
	var err error
	for i := 0; i < RetryOpenDbCount; i++ {
		sqlPath := fmt.Sprintf("file:%s/%s?_auth&_auth_user=admin&_auth_pass=admin&_auth_crypt=sha256", utils.GetConfigDir(), dbName)
		db, e := sql.Open("sqlite3", sqlPath)
		if e != nil {
			Logger.Sugar().Errorf("open sqlite db err:%v", err)
			time.Sleep(time.Millisecond * 600)
			err = e
			continue
		} else {
			return db, nil
		}
	}
	return nil, fmt.Errorf("open db err:%w", err)
}

func CloseSqlite(client *sql.DB) {
	if client != nil {
		_ = client.Close()
	}
}

func CloseStatement(stmt *sql.Stmt) {
	if stmt != nil {
		_ = stmt.Close()
	}
}

func InitTable() {
	sqlTable := `CREATE TABLE IF NOT EXISTS "tb_app_addr" (
    "app_id" INTEGER NOT NULL,
    "port" VARCHAR(64) NULL,
    "se_id" INTEGER NOT NULL,
    "address" VARCHAR(64) NOT NULL,
    "protocol" VARCHAR(64) NULL,
    "update_at" TIMESTAMP default (datetime('now', 'localtime')),
    primary key("address", "port")
    );
    CREATE TABLE IF NOT EXISTS "tb_se_info" (
    "se_id" INTEGER primary key NOT NULL,
    "se_ip" VARCHAR(64) NOT NULL,
    "se_port" INTEGER NOT NULL,
    "app_name" VARCHAR(64),
    "tls" INTEGER,
    "skip_cert_verify" INTEGER,
    "min_tls_version" VARCHAR(16) NOT NULL,
    "max_tls_version" VARCHAR(16) NOT NULL,
    "update_at" TIMESTAMP default (datetime('now', 'localtime'))
    );
    CREATE TABLE IF NOT EXISTS "tb_fake_ip_pool" (
    "fake_id" INTEGER primary key NOT NULL,
    "fake_ip_range" VARCHAR(64) NOT NULL,
    "update_at" TIMESTAMP default (datetime('now', 'localtime'))
    );
	CREATE TABLE IF NOT EXISTS "tb_events_offset" (
	    "event_type" string NOT NULL,
	    "current_offset" INTEGER NOT NULL 
	);

	CREATE TABLE IF NOT EXISTS "tb_agent_config"
	(
		id          text constraint agent_config_pk primary key,
		config_type text,
		config_data blob
	);

    `
	_, err := SqliteClient.Exec(sqlTable)
	if err != nil {
		fmt.Println(err.Error())
	}
	evidenceSqlTable := `
	CREATE TABLE IF NOT EXISTS "tb_events_offset" (
	    "event_type" string NOT NULL,
	    "current_offset" INTEGER NOT NULL 
	);`
	_, err = EvidenceSqliteClient.Exec(evidenceSqlTable)
	if err != nil {
		fmt.Println(err.Error())
	}
}

func InitConfTable() {

	db, err := InitSqliteByName(ConfDbName)
	if err != nil {
		Logger.Sugar().Errorf("initConfTable get Db err:%v", err)
		return
	}
	err = EnableWalForDB(db)
	if err != nil {
		// use zap default logger可以无需关注global.Logger是否已经被初始化，避免发送空指针
		zap.S().Errorf("enble %s WAL failed,error : %v", ConfDbName, err)
	}
	sqlTable := `
	CREATE TABLE IF NOT EXISTS "tb_conf_agent_version" (
	  "conf_type" text NOT NULL constraint cfg_type_pk  primary key,
	  "version" integer NOT NULL
	);
	CREATE TABLE IF NOT EXISTS  "tb_conf_agent" (
	  "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
	  "conf_id" text,
	  "conf_type" text,
	  "conf_data" blob,
	  "conf_md5" text,
	  "update_time" DATETIME DEFAULT CURRENT_TIMESTAMP
    );
		
	CREATE TRIGGER update_time
		BEFORE UPDATE
		ON tb_conf_agent
	BEGIN
		UPDATE tb_conf_agent SET update_time = datetime('now') WHERE rowid = old.rowid;
	END;
	`
	_, err = db.Exec(sqlTable)
	if err != nil {
		zap.S().Errorf("initConfTable exec sqlTable:%v", err)
	}
	//兼容老版本客户端升级上来场景，添加字段防止配置更新失败
	checkAddColumn(db, "tb_conf_agent", "update_time", "DATETIME ")
	checkAddColumn(db, "tb_conf_agent", "data_format", "text")
	checkAddColumn(db, "tb_conf_agent", "conf_text", "text")
}
func checkAddColumn(db *sql.DB, tableName string, columnName string, columnType string) {
	// 检查列是否存在
	exists, err := checkColumn(db, tableName, columnName)
	if err != nil {
		zap.S().Errorf("Failed to check  column : %v", err)
		return
	}
	if !exists {
		// 列不存在，添加列
		_, err := db.Exec(fmt.Sprintf("ALTER TABLE %s ADD COLUMN %s %s;", tableName, columnName, columnType))
		if err != nil {
			zap.S().Errorf("Failed to add column %s to table %s: %v", columnName, tableName, err)
		}
	}
}

func checkColumn(db *sql.DB, tableName, columnName string) (bool, error) {
	checkSQL := fmt.Sprintf("SELECT name FROM pragma_table_info('%s');", tableName)
	rows, err := db.Query(checkSQL)
	if err != nil {
		return false, err
	}
	defer rows.Close()

	var columnNames []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return false, err
		}
		columnNames = append(columnNames, name)
	}

	if err := rows.Err(); err != nil {
		return false, err
	}

	for _, name := range columnNames {
		if name == columnName {
			return true, nil
		}
	}

	return false, nil
}
func InitOffsetTable() {

	// 判断原表是否存在
	var exists int64
	err := OffsetSqliteClient.QueryRow("SELECT EXISTS(SELECT 1 FROM sqlite_master WHERE type='table' AND name='tb_events_offset');").
		Scan(&exists)
	if err != nil {
		Logger.Sugar().Errorf("get offset exists err:%v", err)
		return
	}
	if exists >= 1 {
		return
	}

	sqlTable := `
	CREATE TABLE IF NOT EXISTS "tb_events_offset" (
	    "event_type" string NOT NULL,
	    "current_offset" INTEGER NOT NULL 
	);
	`
	_, err = OffsetSqliteClient.Exec(sqlTable)
	if err != nil {
		Logger.Sugar().Errorf("initConfTable exec sqlTable:%v", err)
	}

	// 将老的offset移植到新的表中
	query, err := SqliteClient.Query("SELECT event_type,current_offset FROM tb_events_offset")
	if err != nil || query == nil {
		Logger.Sugar().Infof("no need copy offset")
		return
	}
	m := make(map[string]int)
	for query.Next() {
		var eventType string
		var offset int
		err := query.Scan(&eventType, &offset)
		if err != nil || eventType == "" {
			continue
		}
		m[eventType] = offset

	}
	query.Close()
	if len(m) <= 0 {
		return
	}
	for k, v := range m {
		sprintf := fmt.Sprintf("INSERT INTO tb_events_offset (event_type, current_offset) VALUES ('%v', %v);", k, v)
		_, err = OffsetSqliteClient.Exec(sprintf)
		if err != nil {
			Logger.Sugar().Errorf("copy offset err:%v", err)
			continue
		}
	}
}
