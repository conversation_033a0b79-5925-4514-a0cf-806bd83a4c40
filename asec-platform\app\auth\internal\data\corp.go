package data

import (
	"context"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type corpRepo struct {
	data *Data
	log  *log.Helper
}

func (c corpRepo) CreateCorp(ctx context.Context, id, name string) error {
	corp := query.Use(c.data.db).TbCorp
	return corp.WithContext(ctx).Create(&model.TbCorp{
		ID:   id,
		Name: name,
	})
}

func (c corpRepo) DeleteCorp(ctx context.Context, id string) (*model.TbCorp, error) {
	//TODO implement me
	// 租户id被外键引用的表太多了，考虑软删除
	panic("implement me")
}

func (c corpRepo) UpdateCorp(ctx context.Context, id, name string) error {
	corp := query.Use(c.data.db).TbCorp
	_, err := corp.WithContext(ctx).Where(corp.ID.Eq(id)).Update(corp.Name, name)
	return err
}

func (c corpRepo) GetCorpByID(ctx context.Context, id string) (*model.TbCorp, error) {
	corp := query.Use(c.data.db).TbCorp
	result, err := corp.WithContext(ctx).Where(corp.ID.Eq(id)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbCorp{}, pb.ErrorRecordNotFound("corpId=%v not found", id)
		}
		return &model.TbCorp{}, err
	}
	return result, nil
}

func (c corpRepo) GetCorpByName(ctx context.Context, name string) (*model.TbCorp, error) {
	corp := query.Use(c.data.db).TbCorp
	result, err := corp.WithContext(ctx).Where(corp.Name.Eq(name)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbCorp{}, pb.ErrorRecordNotFound("name=%v not found", name)
		}
		return &model.TbCorp{}, err
	}
	return result, nil
}

func (c corpRepo) ListCorp(ctx context.Context) ([]*model.TbCorp, error) {
	// TODO: 加筛选和排序条件
	corp := query.Use(c.data.db).TbCorp
	return corp.WithContext(ctx).Find()
}

func NewCorpRepo(data *Data, logger log.Logger) biz.CorpRepo {
	return &corpRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
