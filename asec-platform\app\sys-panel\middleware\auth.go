package middleware

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
	"github.com/limitcool/starter/global"
	"github.com/limitcool/starter/pkg/code"
)

func ParseToken(token, secret string) (*jwt.MapClaims, error) {
	// 解析token
	jwtToken, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {

		return []byte(secret), nil
	})
	if err != nil {
		return nil, err
	}
	if jwtToken != nil {
		// 校验token
		if Claims, ok := jwtToken.Claims.(jwt.MapClaims); ok && jwtToken.Valid {
			return &Claims, nil
		}
	}
	return nil, err
}

func ParseTokenWithoutVerification(tokenString string) (*jwt.MapClaims, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &jwt.MapClaims{})
	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*jwt.MapClaims); ok {
		return claims, nil
	}

	return nil, errors.New("无法解析令牌内容")
}

// validateJWTAndSetContext 通用JWT验证函数
func validateJWTAndSetContext(c *gin.Context, requireAdmin bool) bool {
	// 获取 Authorization header
	authorization := c.GetHeader("Authorization")

	// 检查前缀并提取 token
	token := ""
	if strings.HasPrefix(authorization, "Bearer ") {
		token = strings.Split(authorization, " ")[1]
	}

	// 如果没有token,返回错误
	if token == "" {
		c.AbortWithStatusJSON(http.StatusUnauthorized, code.ApiResponse{
			ErrorCode: code.UserAuthFailed,
			Message:   code.GetMsg(code.UserAuthFailed),
			Data:      nil,
		})
		return false
	}

	claims, err := ParseTokenWithoutVerification(token)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusUnauthorized, code.ApiResponse{
			ErrorCode: code.UserAuthFailed,
			Message:   code.GetMsg(code.UserAuthFailed) + ":" + err.Error(),
			Data:      nil,
		})
		return false
	}

	fmt.Printf("claims: %v\n", claims)

	// 如果需要验证管理员权限
	if requireAdmin && (*claims)["name"] != "admin" {
		c.AbortWithStatusJSON(http.StatusUnauthorized, code.ApiResponse{
			ErrorCode: code.UserAuthFailed,
			Message:   code.GetMsg(code.UserAuthFailed),
			Data:      nil,
		})
		return false
	}

	c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), global.Token, claims))
	// 将token存入请求上下文
	c.Set("token", token)
	return true
}

func IsAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		if validateJWTAndSetContext(c, true) {
			// 继续处理该请求
			c.Next()
		}
	}
}

// APIKeyOrJWT 支持API Key或JWT两种认证方式的中间件
func APIKeyOrJWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 首先检查X-API-KEY头部
		apiKey := c.GetHeader("X-API-KEY")
		if apiKey != "" {
			const validAPIKey = "c34ff517ed828d279f91c884caac8f1be1efb999"
			if apiKey == validAPIKey {
				// API Key验证通过，直接继续处理请求
				c.Next()
				return
			} else {
				// API Key无效
				c.AbortWithStatusJSON(http.StatusUnauthorized, code.ApiResponse{
					ErrorCode: code.UserAuthFailed,
					Message:   "无效的API Key",
					Data:      nil,
				})
				return
			}
		}

		// 如果没有API Key，则走JWT验证流程
		if validateJWTAndSetContext(c, true) {
			// 继续处理该请求
			c.Next()
		}
	}
}
