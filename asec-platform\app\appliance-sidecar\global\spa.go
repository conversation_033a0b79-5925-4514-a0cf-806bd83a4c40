package global

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"asdsec.com/asec/platform/app/appliance-sidecar/internal/machine"
	"asdsec.com/asec/platform/pkg/types"
	"asdsec.com/asec/platform/pkg/utils"

	"github.com/fsnotify/fsnotify"
	"github.com/go-ini/ini"
	"github.com/pquerna/otp/totp"
)

const (
	// SpaExtensionID is the TLS extension ID for Single Packet Authorization.
	SpaExtensionID uint16 = 0x4D59
	// SpaUDPKey is the encryption key for SPA UDP packets.
	SpaUDPKey = "7464fdfe3f795a01affda2848ece9f79"
)

// SPATarget 表示一个SPA敲门目标
type SPATarget struct {
	Host string
	Port int
}

var (
	spaCodeCache      string
	spaCodeCacheMutex sync.RWMutex

	configFileWatcher *fsnotify.Watcher
	configFilePath    string
	watcherStarted    bool
	watcherMutex      sync.Mutex

	spaUdpCancel context.CancelFunc
	spaUdpMu     sync.Mutex

	// 新增SPA目标列表
	spaTargetList  []SPATarget
	spaTargetMutex sync.RWMutex

	// 防抖机制相关变量
	lastConfigChange  time.Time
	configChangeMutex sync.Mutex
	configChangeTimer *time.Timer
)

type ActivationMessage struct {
	ActivationCode string `json:"activation_code"`
	Timestamp      int64  `json:"timestamp"`
}

// SecurityAuthMessage 表示安全码认证消息结构
type SecurityAuthMessage struct {
	SecurityCode string `json:"security_code"`
	Timestamp    int64  `json:"timestamp"`
}

type UDPRequest struct {
	MessageType string `json:"message_type"`
	Data        string `json:"data"`
}

// StartSpaConfigWatcher 启动配置文件监控
func StartSpaConfigWatcher() error {
	watcherMutex.Lock()
	defer watcherMutex.Unlock()

	if watcherStarted {
		return nil
	}

	configFilePath = filepath.Join(utils.GetConfigDir(), "config", "common_config.ini")
	appConfigFilePath := filepath.Join(utils.GetConfigDir(), "config", "app_config.json")

	firstSpaCode, _ := ReadSpaCodeFromFile()
	spaCodeCacheMutex.Lock()
	spaCodeCache = firstSpaCode
	spaCodeCacheMutex.Unlock()

	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return err
	}
	configFileWatcher = watcher

	configDir := filepath.Dir(configFilePath)
	if err := watcher.Add(configDir); err != nil {
		watcher.Close()
		return err
	}

	go func() {
		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}
				if event.Name == configFilePath && (event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create) {
					if Logger != nil {
						Logger.Sugar().Infof("SPA config file changed: %s", event.Name)
					}
					// 使用防抖机制，避免短时间内的重复配置变化
					handleConfigChangeWithDebounce()
				} else if event.Name == appConfigFilePath && (event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create) {
					if Logger != nil {
						Logger.Sugar().Infof("App config file changed: %s", event.Name)
					}
					// 重新初始化SPA目标列表
					InitSPATargetList()
				}
			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				if Logger != nil {
					Logger.Sugar().Errorf("SPA config file watcher error: %v", err)
				}

			}
		}
	}()

	watcherStarted = true
	//os.Stdout.WriteString("SPA config file watcher started\n")
	return nil
}

// StopSpaConfigWatcher 停止配置文件监控
func StopSpaConfigWatcher() {
	watcherMutex.Lock()
	defer watcherMutex.Unlock()

	if configFileWatcher != nil {
		configFileWatcher.Close()
		configFileWatcher = nil
		watcherStarted = false
		//os.Stdout.WriteString("SPA config file watcher stopped\n")
	}
}

// GetSpaCode 获取spaCode，只返回缓存值
func GetSpaCode() string {
	spaCodeCacheMutex.RLock()
	spaCode := spaCodeCache
	spaCodeCacheMutex.RUnlock()
	return spaCode
}

// 获取配置文件路径（与 readSpaCodeFromFile 保持一致）
func getCommonConfigPath() string {
	return filepath.Join(utils.GetConfigDir(), "config", "common_config.ini")
}

// GetProcessedSpaCode a 6-digit code is a temporary activation code
// and needs to be prefixed with "act_".
func GetProcessedSpaCode() string {
	spaCode := GetSpaCode()
	if Logger != nil {
		Logger.Sugar().Debugf("Processing SPA code, length: %d", len(spaCode))
	}

	if len(spaCode) == 6 {
		// 激活码，带前缀
		result := "act_" + spaCode
		if Logger != nil {
			Logger.Sugar().Debugf("Processed as activation code: %s", result)
		}
		return result
	} else if len(spaCode) > 6 && spaCode != "" {
		// 安全码，直接生成TOTP
		opts := totp.ValidateOpts{
			Period: 7200,
			Skew:   1,
			Digits: 10,
		}
		code, err := totp.GenerateCodeCustom(spaCode, time.Now(), opts)
		if err != nil {
			if Logger != nil {
				Logger.Sugar().Errorf("Failed to generate TOTP: %v", err)
			}
			return "" // 生成失败返回空
		}
		if Logger != nil {
			Logger.Sugar().Debugf("Generated TOTP code: %s", code)
		}
		return code
	}
	if Logger != nil {
		Logger.Sugar().Debugf("Using original SPA code as fallback")
	}
	return spaCode // 兜底
}

func encryptData(data interface{}, key []byte) (string, error) {
	if len(key) != 32 {
		return "", fmt.Errorf("无效的密钥长度，必须是32字节(256位)")
	}
	plaintext, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("JSON编码失败: %w", err)
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建AES加密块失败: %w", err)
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM模式失败: %w", err)
	}
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("生成nonce失败: %w", err)
	}
	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func sendSpaUDP(ctx context.Context, spaCode, host string, port int) error {
	if Logger != nil {
		Logger.Sugar().Debugf("Sending SPA UDP to %s:%d with code length: %d", host, port, len(spaCode))
	}

	var msg interface{}
	var messageType string

	if len(spaCode) == 6 {
		msg = ActivationMessage{
			ActivationCode: spaCode,
			Timestamp:      time.Now().Unix(),
		}
		messageType = "0"
		if Logger != nil {
			Logger.Sugar().Debugf("Using activation code message type")
		}
	} else if len(spaCode) > 6 && spaCode != "" {
		processedCode := GetProcessedSpaCode()
		msg = SecurityAuthMessage{
			SecurityCode: processedCode,
			Timestamp:    time.Now().Unix(),
		}
		messageType = "1"
		if Logger != nil {
			Logger.Sugar().Debugf("Using security code message type, processed code length: %d", len(processedCode))
		}
	} else {
		err := fmt.Errorf("无效的spaCode长度: %d", len(spaCode))
		if Logger != nil {
			Logger.Sugar().Errorf("Invalid SPA code length: %d", len(spaCode))
		}
		return err
	}

	key := []byte(SpaUDPKey)
	enc, err := encryptData(msg, key)
	if err != nil {
		return err
	}
	request := UDPRequest{
		MessageType: messageType,
		Data:        enc,
	}
	requestBytes, err := json.Marshal(request)
	if err != nil {
		return err
	}
	addr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", host, port))
	if err != nil {
		return err
	}
	conn, err := net.DialUDP("udp", nil, addr)
	if err != nil {
		return err
	}
	defer conn.Close()
	conn.SetDeadline(time.Now().Add(5 * time.Second))
	_, err = conn.Write(requestBytes)
	return err
}

func StartSpaUDP(ctx context.Context) error {
	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] StartSpaUDP() called")
	}

	spaUdpMu.Lock()
	defer spaUdpMu.Unlock()

	hadCancel := spaUdpCancel != nil
	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] StartSpaUDP: existing cancel function: %v", hadCancel)
	}

	// 不在这里取消之前的UDP，由调用方负责取消
	// 直接使用传入的context创建子context
	udpCtx, cancel := context.WithCancel(ctx)
	spaUdpCancel = cancel

	spaCode := GetSpaCode()
	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] StartSpaUDP: SPA code length=%d, empty=%v", len(spaCode), spaCode == "")
	}

	if spaCode == "" {
		if Logger != nil {
			Logger.Sugar().Errorf("[SPA-TRACK] StartSpaUDP: FAILED - spa code is empty")
		}
		return errors.New("spa code is empty")
	}

	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] StartSpaUDP: sending immediate UDP...")
	}
	// 立即发送一次
	sendToAllTargets(udpCtx, spaCode)

	// 定期发送
	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] StartSpaUDP: starting periodic timer (10s)...")
	}
	go func() {
		if Logger != nil {
			Logger.Sugar().Infof("[SPA-TRACK] UDP Timer goroutine started")
		}
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		defer func() {
			if Logger != nil {
				Logger.Sugar().Infof("[SPA-TRACK] UDP Timer goroutine EXITING")
			}
		}()

		for {
			select {
			case <-udpCtx.Done():
				if Logger != nil {
					Logger.Sugar().Infof("[SPA-TRACK] UDP Timer: context cancelled, stopping...")
				}
				return
			case <-ticker.C:
				if Logger != nil {
					Logger.Sugar().Debugf("[SPA-TRACK] UDP Timer: tick, sending periodic UDP...")
				}
				sendToAllTargets(udpCtx, spaCode)
			}
		}
	}()

	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] StartSpaUDP: SUCCESS - timer started")
	}
	return nil
}

// 向所有目标发送敲门包
func sendToAllTargets(ctx context.Context, spaCode string) {
	if Logger != nil {
		Logger.Sugar().Debugf("[SPA-TRACK] sendToAllTargets: called with spaCode length=%d", len(spaCode))
	}

	// 读取SPA目标列表
	targets := GetSPATargetList()
	if Logger != nil {
		Logger.Sugar().Debugf("[SPA-TRACK] sendToAllTargets: got %d targets", len(targets))
	}

	if len(targets) == 0 {
		if Logger != nil {
			Logger.Sugar().Warnf("[SPA-TRACK] sendToAllTargets: NO TARGETS - UDP sending skipped")
		}
		return
	}

	successCount := 0
	failCount := 0
	for i, target := range targets {
		if Logger != nil {
			Logger.Sugar().Debugf("[SPA-TRACK] sendToAllTargets: sending to target %d/%d: %s:%d", i+1, len(targets), target.Host, target.Port)
		}

		err := sendSpaUDP(ctx, spaCode, target.Host, target.Port)
		if err != nil {
			failCount++
			if Logger != nil {
				Logger.Sugar().Errorf("[SPA-TRACK] sendToAllTargets: FAILED %s:%d: %v", target.Host, target.Port, err)
			}
		} else {
			successCount++
			if Logger != nil {
				Logger.Sugar().Infof("[SPA-TRACK] sendToAllTargets: SUCCESS %s:%d", target.Host, target.Port)
			}
		}
	}

	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] sendToAllTargets: completed - success=%d, failed=%d, total=%d", successCount, failCount, len(targets))
	}
}

// 统一启动入口
func StartSPA() error {
	if Logger != nil {
		Logger.Sugar().Infof("Starting SPA system...")
	}

	// 初始化SPA目标列表
	InitSPATargetList()

	// 启动SPA配置监听
	if err := StartSpaConfigWatcher(); err != nil {
		if Logger != nil {
			Logger.Sugar().Errorf("Failed to start SPA config watcher: %v", err)
		}
		return err
	}

	// 启动UDP敲门
	ctx := context.Background()
	err := StartSpaUDP(ctx)
	if err != nil {
		if Logger != nil {
			Logger.Sugar().Errorf("Failed to start SPA UDP: %v", err)
		}
	} else {
		if Logger != nil {
			Logger.Sugar().Infof("SPA system started successfully")
		}
	}
	return err
}

// AES-256-GCM 解密 security_code
func decryptSecurityCode(encryptedB64 string, keyString string) ([]byte, error) {
	const NONCE_SIZE = 12
	const TAG_SIZE = 16
	if encryptedB64 == "" {
		return nil, fmt.Errorf("加密字符串为空")
	}
	key := []byte(keyString)
	if len(key) != 32 {
		return nil, fmt.Errorf("无效的密钥长度：必须为32字节，但密钥长度为 %d", len(key))
	}
	combinedData, err := base64.StdEncoding.DecodeString(encryptedB64)
	if err != nil {
		return nil, fmt.Errorf("无效的Base64编码字符串: %w", err)
	}
	if len(combinedData) < NONCE_SIZE+TAG_SIZE {
		return nil, fmt.Errorf("加密数据太短，无法包含 Nonce 和认证标签")
	}
	nonce := combinedData[:NONCE_SIZE]
	ciphertextWithTag := combinedData[NONCE_SIZE:]
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("创建AES cipher失败: %w", err)
	}
	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %w", err)
	}
	decryptedBytes, err := aesgcm.Open(nil, nonce, ciphertextWithTag, nil)
	if err != nil {
		return nil, fmt.Errorf("解密失败：数据可能已损坏或密钥不正确: %w", err)
	}
	return decryptedBytes, nil
}

// 获取CPU ID作为硬件唯一标识
func getCpuID() (string, error) {
	machineData := machine.GetMachineData()
	cpuId := machineData.CpuId
	if cpuId == "" {
		return "", fmt.Errorf("无法获取CPU ID")
	}
	return cpuId, nil
}

// 读取 security_code（注释掉CPU ID加密，保留SPA UDP Key解密）
func ReadSecurityCodeFromFile() (string, error) {
	cfgPath := getCommonConfigPath()
	cfg, err := ini.Load(cfgPath)
	if err != nil {
		return "", err
	}
	sec := cfg.Section("common")
	enc := sec.Key("security_code").String()
	if enc == "" {
		return "", nil
	}

	// 注释掉第一步：CPU ID解密
	/*
		cpuID, err := getCpuID()
		if err != nil {
			return "", fmt.Errorf("无法获取CPU ID: %w", err)
		}

		spaEncryptedCode := cpuXorDecrypt(enc, cpuID)
		if spaEncryptedCode == "" {
			return "", fmt.Errorf("CPU ID解密失败")
		}
	*/

	// 第二步：用SPA UDP Key解密（保留此步骤）
	dec, err := decryptSecurityCode(enc, SpaUDPKey)
	if err != nil {
		return "", fmt.Errorf("SPA UDP Key解密失败: %w", err)
	}

	return string(dec), nil
}

// 读取 activation_code（明文）
func ReadActivationCodeFromFile() (string, error) {
	cfgPath := getCommonConfigPath()
	cfg, err := ini.Load(cfgPath)
	if err != nil {
		return "", err
	}
	sec := cfg.Section("common")
	activationCode := sec.Key("activation_code").String()
	return activationCode, nil
}

// 读取SPA码（优先读取security_code，如果没有则读取activation_code）
func ReadSpaCodeFromFile() (string, error) {
	// 首先尝试读取security_code
	securityCode, err := ReadSecurityCodeFromFile()
	if err == nil && securityCode != "" {
		if Logger != nil {
			Logger.Sugar().Infof("Using security_code for SPA (length: %d)", len(securityCode))
		}
		return securityCode, nil
	}

	// 如果没有security_code，尝试读取activation_code
	activationCode, err := ReadActivationCodeFromFile()
	if err == nil && activationCode != "" {
		if Logger != nil {
			Logger.Sugar().Infof("Using activation_code for SPA: %s", activationCode)
		}
		return activationCode, nil
	}

	// 都没有找到
	if Logger != nil {
		Logger.Sugar().Warnf("No valid SPA code found, error: %v", err)
	}
	if err != nil {
		return "", err
	}
	return "", nil
}

// 基于CPU ID的简单XOR解密（不使用Base64）
func cpuXorDecrypt(encryptedData, cpuKey string) string {
	if encryptedData == "" || cpuKey == "" {
		return ""
	}

	// 直接XOR解密
	result := make([]byte, len(encryptedData))
	for i := 0; i < len(encryptedData); i++ {
		result[i] = encryptedData[i] ^ cpuKey[i%len(cpuKey)]
	}

	return string(result)
}

func onSpaConfigChanged() {
	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] onSpaConfigChanged() called")
	}

	spaUdpMu.Lock()
	defer spaUdpMu.Unlock()

	// 记录当前状态
	hadCancel := spaUdpCancel != nil
	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] Current UDP cancel function exists: %v", hadCancel)
	}

	if spaUdpCancel != nil {
		if Logger != nil {
			Logger.Sugar().Infof("[SPA-TRACK] Cancelling current UDP sending...")
		}
		spaUdpCancel()
		spaUdpCancel = nil
		if Logger != nil {
			Logger.Sugar().Infof("[SPA-TRACK] Current UDP sending cancelled")
		}
	}

	currentSpaCode := GetSpaCode()
	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] Current SPA code length: %d, empty: %v", len(currentSpaCode), currentSpaCode == "")
	}

	if currentSpaCode != "" {
		ctx, cancel := context.WithCancel(context.Background())
		spaUdpCancel = cancel
		if Logger != nil {
			Logger.Sugar().Infof("[SPA-TRACK] Starting new UDP sending asynchronously...")
		}
		go func() {
			if Logger != nil {
				Logger.Sugar().Infof("[SPA-TRACK] Async StartSpaUDP() beginning...")
			}
			err := StartSpaUDP(ctx)
			if err != nil {
				if Logger != nil {
					Logger.Sugar().Errorf("[SPA-TRACK] Async StartSpaUDP() FAILED: %v", err)
				}
			} else {
				if Logger != nil {
					Logger.Sugar().Infof("[SPA-TRACK] Async StartSpaUDP() SUCCESS")
				}
			}
		}()
	} else {
		if Logger != nil {
			Logger.Sugar().Warnf("[SPA-TRACK] SPA code is empty, UDP sending will NOT be started")
		}
	}

	if Logger != nil {
		Logger.Sugar().Infof("[SPA-TRACK] onSpaConfigChanged() completed")
	}
}

// 初始化SPA目标列表（包含平台地址和SE地址）
func InitSPATargetList() {
	var targets []SPATarget

	// 1. 添加平台地址
	privateHost := PrivateHost
	if privateHost == "" && Conf.Endpoints.PrivateHost != "" {
		privateHost = Conf.Endpoints.PrivateHost
	}

	if privateHost != "" {
		host := privateHost
		port := 443
		if strings.Contains(privateHost, ":") {
			parts := strings.Split(privateHost, ":")
			host = parts[0]
			if len(parts) > 1 {
				fmt.Sscanf(parts[1], "%d", &port)
			}
		}

		if host != "" {
			targets = append(targets, SPATarget{Host: host, Port: port})
			if Logger != nil {
				Logger.Sugar().Infof("Added platform target: %s:%d", host, port)
			}
		}
	}

	// 2. 读取AppConfig中的SE列表
	configPath := filepath.Join(utils.GetConfigDir(), "config", "app_config.json")
	data, err := os.ReadFile(configPath)
	if err != nil {
		if Logger != nil {
			Logger.Sugar().Warnf("Failed to read app_config.json: %v", err)
		}
	} else {
		var config types.SyncedConfig
		if err := json.Unmarshal(data, &config); err != nil {
			if Logger != nil {
				Logger.Sugar().Errorf("Failed to parse app_config.json: %v", err)
			}
		} else {
			// 从SeList中提取地址和端口
			for _, se := range config.SeList {
				host := se.SeIp
				port := int(se.SePort)
				port = 443 // 默认端口

				if host != "" {
					targets = append(targets, SPATarget{Host: host, Port: port})
					if Logger != nil {
						Logger.Sugar().Infof("Added SE target from config: %s:%d", host, port)
					}
				}
			}
		}
	}

	// 更新SPA目标列表
	UpdateSPATargetList(targets)
}

// 更新SPA目标列表
func UpdateSPATargetList(targets []SPATarget) {
	spaTargetMutex.Lock()
	defer spaTargetMutex.Unlock()
	spaTargetList = targets

	// 记录日志
	if Logger != nil {
		Logger.Sugar().Infof("Updated SPA target list, total targets: %d", len(targets))
	}
}

// 获取SPA目标列表
func GetSPATargetList() []SPATarget {
	spaTargetMutex.RLock()
	defer spaTargetMutex.RUnlock()

	// 返回副本，避免外部修改
	result := make([]SPATarget, len(spaTargetList))
	copy(result, spaTargetList)
	return result
}

// handleConfigChangeWithDebounce 使用防抖机制处理配置变化
func handleConfigChangeWithDebounce() {
	configChangeMutex.Lock()
	defer configChangeMutex.Unlock()

	// 如果已有定时器在运行，取消它
	if configChangeTimer != nil {
		configChangeTimer.Stop()
	}

	// 设置新的定时器，200ms后执行配置变化处理（缩短延迟提高响应性）
	configChangeTimer = time.AfterFunc(200*time.Millisecond, func() {
		if Logger != nil {
			Logger.Sugar().Infof("Processing debounced SPA config change")
		}

		newSpaCode, _ := ReadSpaCodeFromFile()
		spaCodeCacheMutex.Lock()
		if newSpaCode != spaCodeCache {
			spaCodeCache = newSpaCode
			spaCodeCacheMutex.Unlock()
			if Logger != nil {
				Logger.Sugar().Infof("SPA code changed, notifying onSpaConfigChanged")
			}
			onSpaConfigChanged()
		} else {
			spaCodeCacheMutex.Unlock()
			if Logger != nil {
				Logger.Sugar().Debugf("SPA code unchanged after debounce, skipping onSpaConfigChanged")
			}
		}
	})
}
