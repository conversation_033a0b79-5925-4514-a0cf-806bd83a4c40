syntax = "proto3";
package api.strategy;

option go_package = "asdsec.com/asec/platform/api/strategy/v1;v1";


message GetElemReq{
  string user_id = 1;
}

message GetElemResp{
  repeated SenElemInfo info = 1;
}

message SenElemInfo{
  uint64 id = 1;
  string desc = 2;
  repeated RuleList rule_list = 3;
}

message RuleList{
    string type = 1;
    repeated string value = 2;
}

service GetSenElem{
  rpc AgentGetSenElem(GetElemReq) returns (GetElemResp);
}

