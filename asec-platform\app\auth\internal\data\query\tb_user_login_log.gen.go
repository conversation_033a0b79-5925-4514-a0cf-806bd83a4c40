// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newTbUserLoginLog(db *gorm.DB, opts ...gen.DOOption) tbUserLoginLog {
	_tbUserLoginLog := tbUserLoginLog{}

	_tbUserLoginLog.tbUserLoginLogDo.UseDB(db, opts...)
	_tbUserLoginLog.tbUserLoginLogDo.UseModel(&model.TbUserLoginLog{})

	tableName := _tbUserLoginLog.tbUserLoginLogDo.TableName()
	_tbUserLoginLog.ALL = field.NewAsterisk(tableName)
	_tbUserLoginLog.ID = field.NewString(tableName, "id")
	_tbUserLoginLog.ClientID = field.NewString(tableName, "client_id")
	_tbUserLoginLog.Error = field.NewString(tableName, "error")
	_tbUserLoginLog.IPAddress = field.NewString(tableName, "ip_address")
	_tbUserLoginLog.CorpID = field.NewString(tableName, "corp_id")
	_tbUserLoginLog.EventTime = field.NewInt64(tableName, "event_time")
	_tbUserLoginLog.Type = field.NewString(tableName, "type")
	_tbUserLoginLog.UserID = field.NewString(tableName, "user_id")
	_tbUserLoginLog.SourceID = field.NewString(tableName, "source_id")
	_tbUserLoginLog.Description = field.NewString(tableName, "description")

	_tbUserLoginLog.fillFieldMap()

	return _tbUserLoginLog
}

type tbUserLoginLog struct {
	tbUserLoginLogDo tbUserLoginLogDo

	ALL         field.Asterisk
	ID          field.String
	ClientID    field.String
	Error       field.String
	IPAddress   field.String
	CorpID      field.String
	EventTime   field.Int64
	Type        field.String
	UserID      field.String
	SourceID    field.String
	Description field.String

	fieldMap map[string]field.Expr
}

func (t tbUserLoginLog) Table(newTableName string) *tbUserLoginLog {
	t.tbUserLoginLogDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserLoginLog) As(alias string) *tbUserLoginLog {
	t.tbUserLoginLogDo.DO = *(t.tbUserLoginLogDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserLoginLog) updateTableName(table string) *tbUserLoginLog {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.ClientID = field.NewString(table, "client_id")
	t.Error = field.NewString(table, "error")
	t.IPAddress = field.NewString(table, "ip_address")
	t.CorpID = field.NewString(table, "corp_id")
	t.EventTime = field.NewInt64(table, "event_time")
	t.Type = field.NewString(table, "type")
	t.UserID = field.NewString(table, "user_id")
	t.SourceID = field.NewString(table, "source_id")
	t.Description = field.NewString(table, "description")

	t.fillFieldMap()

	return t
}

func (t *tbUserLoginLog) WithContext(ctx context.Context) *tbUserLoginLogDo {
	return t.tbUserLoginLogDo.WithContext(ctx)
}

func (t tbUserLoginLog) TableName() string { return t.tbUserLoginLogDo.TableName() }

func (t tbUserLoginLog) Alias() string { return t.tbUserLoginLogDo.Alias() }

func (t *tbUserLoginLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserLoginLog) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["id"] = t.ID
	t.fieldMap["client_id"] = t.ClientID
	t.fieldMap["error"] = t.Error
	t.fieldMap["ip_address"] = t.IPAddress
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["event_time"] = t.EventTime
	t.fieldMap["type"] = t.Type
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["source_id"] = t.SourceID
	t.fieldMap["description"] = t.Description
}

func (t tbUserLoginLog) clone(db *gorm.DB) tbUserLoginLog {
	t.tbUserLoginLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserLoginLog) replaceDB(db *gorm.DB) tbUserLoginLog {
	t.tbUserLoginLogDo.ReplaceDB(db)
	return t
}

type tbUserLoginLogDo struct{ gen.DO }

func (t tbUserLoginLogDo) Debug() *tbUserLoginLogDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserLoginLogDo) WithContext(ctx context.Context) *tbUserLoginLogDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserLoginLogDo) ReadDB() *tbUserLoginLogDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserLoginLogDo) WriteDB() *tbUserLoginLogDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserLoginLogDo) Session(config *gorm.Session) *tbUserLoginLogDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserLoginLogDo) Clauses(conds ...clause.Expression) *tbUserLoginLogDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserLoginLogDo) Returning(value interface{}, columns ...string) *tbUserLoginLogDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserLoginLogDo) Not(conds ...gen.Condition) *tbUserLoginLogDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserLoginLogDo) Or(conds ...gen.Condition) *tbUserLoginLogDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserLoginLogDo) Select(conds ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserLoginLogDo) Where(conds ...gen.Condition) *tbUserLoginLogDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserLoginLogDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserLoginLogDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserLoginLogDo) Order(conds ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserLoginLogDo) Distinct(cols ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserLoginLogDo) Omit(cols ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserLoginLogDo) Join(table schema.Tabler, on ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserLoginLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserLoginLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserLoginLogDo) Group(cols ...field.Expr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserLoginLogDo) Having(conds ...gen.Condition) *tbUserLoginLogDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserLoginLogDo) Limit(limit int) *tbUserLoginLogDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserLoginLogDo) Offset(offset int) *tbUserLoginLogDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserLoginLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserLoginLogDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserLoginLogDo) Unscoped() *tbUserLoginLogDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserLoginLogDo) Create(values ...*model.TbUserLoginLog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserLoginLogDo) CreateInBatches(values []*model.TbUserLoginLog, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserLoginLogDo) Save(values ...*model.TbUserLoginLog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserLoginLogDo) First() (*model.TbUserLoginLog, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserLoginLog), nil
	}
}

func (t tbUserLoginLogDo) Take() (*model.TbUserLoginLog, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserLoginLog), nil
	}
}

func (t tbUserLoginLogDo) Last() (*model.TbUserLoginLog, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserLoginLog), nil
	}
}

func (t tbUserLoginLogDo) Find() ([]*model.TbUserLoginLog, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserLoginLog), err
}

func (t tbUserLoginLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserLoginLog, err error) {
	buf := make([]*model.TbUserLoginLog, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserLoginLogDo) FindInBatches(result *[]*model.TbUserLoginLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserLoginLogDo) Attrs(attrs ...field.AssignExpr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserLoginLogDo) Assign(attrs ...field.AssignExpr) *tbUserLoginLogDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserLoginLogDo) Joins(fields ...field.RelationField) *tbUserLoginLogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserLoginLogDo) Preload(fields ...field.RelationField) *tbUserLoginLogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserLoginLogDo) FirstOrInit() (*model.TbUserLoginLog, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserLoginLog), nil
	}
}

func (t tbUserLoginLogDo) FirstOrCreate() (*model.TbUserLoginLog, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserLoginLog), nil
	}
}

func (t tbUserLoginLogDo) FindByPage(offset int, limit int) (result []*model.TbUserLoginLog, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserLoginLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserLoginLogDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserLoginLogDo) Delete(models ...*model.TbUserLoginLog) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserLoginLogDo) withDO(do gen.Dao) *tbUserLoginLogDo {
	t.DO = *do.(*gen.DO)
	return t
}
