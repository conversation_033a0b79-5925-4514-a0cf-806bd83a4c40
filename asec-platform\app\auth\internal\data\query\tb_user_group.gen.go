// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserGroup(db *gorm.DB, opts ...gen.DOOption) tbUserGroup {
	_tbUserGroup := tbUserGroup{}

	_tbUserGroup.tbUserGroupDo.UseDB(db, opts...)
	_tbUserGroup.tbUserGroupDo.UseModel(&model.TbUserGroup{})

	tableName := _tbUserGroup.tbUserGroupDo.TableName()
	_tbUserGroup.ALL = field.NewAsterisk(tableName)
	_tbUserGroup.ID = field.NewString(tableName, "id")
	_tbUserGroup.Name = field.NewString(tableName, "name")
	_tbUserGroup.Description = field.NewString(tableName, "description")
	_tbUserGroup.ParentGroupID = field.NewString(tableName, "parent_group_id")
	_tbUserGroup.Path = field.NewString(tableName, "path")
	_tbUserGroup.CorpID = field.NewString(tableName, "corp_id")
	_tbUserGroup.SourceID = field.NewString(tableName, "source_id")
	_tbUserGroup.CreatedAt = field.NewTime(tableName, "created_at")
	_tbUserGroup.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbUserGroup.IsDefault = field.NewBool(tableName, "is_default")
	_tbUserGroup.RootGroupID = field.NewString(tableName, "root_group_id")

	_tbUserGroup.fillFieldMap()

	return _tbUserGroup
}

type tbUserGroup struct {
	tbUserGroupDo tbUserGroupDo

	ALL           field.Asterisk
	ID            field.String // 分组id
	Name          field.String // 分组名，在同级目录下唯一
	Description   field.String
	ParentGroupID field.String // 父级分组id，用于维护分组的树形结构，为0时表示最顶层
	Path 		  field.String
	CorpID        field.String // 租户id
	SourceID      field.String // 来源id
	CreatedAt     field.Time
	UpdatedAt     field.Time
	IsDefault     field.Bool
	RootGroupID   field.String

	fieldMap map[string]field.Expr
}

func (t tbUserGroup) Table(newTableName string) *tbUserGroup {
	t.tbUserGroupDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserGroup) As(alias string) *tbUserGroup {
	t.tbUserGroupDo.DO = *(t.tbUserGroupDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserGroup) updateTableName(table string) *tbUserGroup {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.Description = field.NewString(table, "description")
	t.ParentGroupID = field.NewString(table, "parent_group_id")
	t.Path = field.NewString(table, "path")
	t.CorpID = field.NewString(table, "corp_id")
	t.SourceID = field.NewString(table, "source_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.IsDefault = field.NewBool(table, "is_default")
	t.RootGroupID = field.NewString(table, "root_group_id")

	t.fillFieldMap()

	return t
}

func (t *tbUserGroup) WithContext(ctx context.Context) *tbUserGroupDo {
	return t.tbUserGroupDo.WithContext(ctx)
}

func (t tbUserGroup) TableName() string { return t.tbUserGroupDo.TableName() }

func (t tbUserGroup) Alias() string { return t.tbUserGroupDo.Alias() }

func (t *tbUserGroup) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserGroup) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["description"] = t.Description
	t.fieldMap["parent_group_id"] = t.ParentGroupID
	t.fieldMap["path"] = t.Path
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["source_id"] = t.SourceID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["is_default"] = t.IsDefault
	t.fieldMap["root_group_id"] = t.RootGroupID
}

func (t tbUserGroup) clone(db *gorm.DB) tbUserGroup {
	t.tbUserGroupDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserGroup) replaceDB(db *gorm.DB) tbUserGroup {
	t.tbUserGroupDo.ReplaceDB(db)
	return t
}

type tbUserGroupDo struct{ gen.DO }

func (t tbUserGroupDo) Debug() *tbUserGroupDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserGroupDo) WithContext(ctx context.Context) *tbUserGroupDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserGroupDo) ReadDB() *tbUserGroupDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserGroupDo) WriteDB() *tbUserGroupDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserGroupDo) Session(config *gorm.Session) *tbUserGroupDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserGroupDo) Clauses(conds ...clause.Expression) *tbUserGroupDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserGroupDo) Returning(value interface{}, columns ...string) *tbUserGroupDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserGroupDo) Not(conds ...gen.Condition) *tbUserGroupDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserGroupDo) Or(conds ...gen.Condition) *tbUserGroupDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserGroupDo) Select(conds ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserGroupDo) Where(conds ...gen.Condition) *tbUserGroupDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserGroupDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserGroupDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserGroupDo) Order(conds ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserGroupDo) Distinct(cols ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserGroupDo) Omit(cols ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserGroupDo) Join(table schema.Tabler, on ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserGroupDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserGroupDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserGroupDo) Group(cols ...field.Expr) *tbUserGroupDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserGroupDo) Having(conds ...gen.Condition) *tbUserGroupDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserGroupDo) Limit(limit int) *tbUserGroupDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserGroupDo) Offset(offset int) *tbUserGroupDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserGroupDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserGroupDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserGroupDo) Unscoped() *tbUserGroupDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserGroupDo) Create(values ...*model.TbUserGroup) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserGroupDo) CreateInBatches(values []*model.TbUserGroup, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserGroupDo) Save(values ...*model.TbUserGroup) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserGroupDo) First() (*model.TbUserGroup, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroup), nil
	}
}

func (t tbUserGroupDo) Take() (*model.TbUserGroup, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroup), nil
	}
}

func (t tbUserGroupDo) Last() (*model.TbUserGroup, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroup), nil
	}
}

func (t tbUserGroupDo) Find() ([]*model.TbUserGroup, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserGroup), err
}

func (t tbUserGroupDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserGroup, err error) {
	buf := make([]*model.TbUserGroup, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserGroupDo) FindInBatches(result *[]*model.TbUserGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserGroupDo) Attrs(attrs ...field.AssignExpr) *tbUserGroupDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserGroupDo) Assign(attrs ...field.AssignExpr) *tbUserGroupDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserGroupDo) Joins(fields ...field.RelationField) *tbUserGroupDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserGroupDo) Preload(fields ...field.RelationField) *tbUserGroupDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserGroupDo) FirstOrInit() (*model.TbUserGroup, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroup), nil
	}
}

func (t tbUserGroupDo) FirstOrCreate() (*model.TbUserGroup, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroup), nil
	}
}

func (t tbUserGroupDo) FindByPage(offset int, limit int) (result []*model.TbUserGroup, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserGroupDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserGroupDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserGroupDo) Delete(models ...*model.TbUserGroup) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserGroupDo) withDO(do gen.Dao) *tbUserGroupDo {
	t.DO = *do.(*gen.DO)
	return t
}
