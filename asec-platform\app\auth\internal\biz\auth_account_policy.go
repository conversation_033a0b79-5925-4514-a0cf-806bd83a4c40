package biz

import (
	"context"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
)

// AuthAccountPolicyRepo 账户认证策略数据访问接口
type AuthAccountPolicyRepo interface {
	// 基础CRUD操作
	CreateAccountPolicy(ctx context.Context, policy *model.TbAuthAccountPolicy) error
	UpdateAccountPolicy(ctx context.Context, policy *model.TbAuthAccountPolicy) error
	DeleteAccountPolicy(ctx context.Context, corpID, id string) error
	GetAccountPolicy(ctx context.Context, corpID, id string) (*model.TbAuthAccountPolicy, error)

	// 查询操作
	GetDefaultAccountPolicy(ctx context.Context, corpID string) (*model.TbAuthAccountPolicy, error)
	GetAccountPolicyByPriority(ctx context.Context, corpID string) (*model.TbAuthAccountPolicy, error)
	ListAccountPolicies(ctx context.Context, corpID string) ([]*model.TbAuthAccountPolicy, error)

	// 密码锁定策略获取
	GetPasswordLockoutPolicy(ctx context.Context, corpID string) (*dto.AccountPolicyPasswordLockout, error)

	// IP锁定策略获取
	GetIPLockoutPolicy(ctx context.Context, corpID string) (*dto.AccountPolicyIPLockout, error)
}

// AuthAccountPolicyUsecase 账户认证策略业务逻辑
type AuthAccountPolicyUsecase struct {
	repo AuthAccountPolicyRepo
	log  *log.Helper
}

// NewAuthAccountPolicyUsecase 创建账户认证策略业务逻辑实例
func NewAuthAccountPolicyUsecase(repo AuthAccountPolicyRepo, logger log.Logger) *AuthAccountPolicyUsecase {
	return &AuthAccountPolicyUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// CreateAccountPolicy 创建账户策略
func (uc *AuthAccountPolicyUsecase) CreateAccountPolicy(ctx context.Context, policy *model.TbAuthAccountPolicy) error {
	return uc.repo.CreateAccountPolicy(ctx, policy)
}

// UpdateAccountPolicy 更新账户策略
func (uc *AuthAccountPolicyUsecase) UpdateAccountPolicy(ctx context.Context, policy *model.TbAuthAccountPolicy) error {
	return uc.repo.UpdateAccountPolicy(ctx, policy)
}

// DeleteAccountPolicy 删除账户策略
func (uc *AuthAccountPolicyUsecase) DeleteAccountPolicy(ctx context.Context, corpID, id string) error {
	return uc.repo.DeleteAccountPolicy(ctx, corpID, id)
}

// GetAccountPolicy 获取账户策略
func (uc *AuthAccountPolicyUsecase) GetAccountPolicy(ctx context.Context, corpID, id string) (*model.TbAuthAccountPolicy, error) {
	return uc.repo.GetAccountPolicy(ctx, corpID, id)
}

// GetDefaultAccountPolicy 获取默认账户策略
func (uc *AuthAccountPolicyUsecase) GetDefaultAccountPolicy(ctx context.Context, corpID string) (*model.TbAuthAccountPolicy, error) {
	return uc.repo.GetDefaultAccountPolicy(ctx, corpID)
}

// ListAccountPolicies 列出账户策略
func (uc *AuthAccountPolicyUsecase) ListAccountPolicies(ctx context.Context, corpID string) ([]*model.TbAuthAccountPolicy, error) {
	return uc.repo.ListAccountPolicies(ctx, corpID)
}

// GetPasswordLockoutPolicy 获取密码锁定策略
func (uc *AuthAccountPolicyUsecase) GetPasswordLockoutPolicy(ctx context.Context, corpID string) (*dto.AccountPolicyPasswordLockout, error) {
	return uc.repo.GetPasswordLockoutPolicy(ctx, corpID)
}

// GetIPLockoutPolicy 获取IP锁定策略
func (uc *AuthAccountPolicyUsecase) GetIPLockoutPolicy(ctx context.Context, corpID string) (*dto.AccountPolicyIPLockout, error) {
	return uc.repo.GetIPLockoutPolicy(ctx, corpID)
}
