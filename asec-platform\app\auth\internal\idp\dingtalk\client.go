package dingtalk

import (
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp"
	"errors"
	"github.com/mozillazg/go-pinyin"
	"github.com/zhaoyunxing92/dingtalk/v2"
	"github.com/zhaoyunxing92/dingtalk/v2/request"
	"strconv"
)

type DingtalkkClient struct {
	client *dingtalk.DingTalk
}

func NewLarkClient(appKey, appSecret string) *DingtalkkClient {
	client, _ := dingtalk.NewClient(appKey, appSecret)
	return &DingtalkkClient{client: client}
}

func (dt DingtalkkClient) GetAllDepts(rootGroupId string) (ret []*dto.ExternalDepartment, err error) {
	deptsList, err := dt.GetDepts(1, rootGroupId)
	if err != nil {
		return nil, err
	}
	if len(deptsList) == 0 {
		return ret, nil
	}
	for _, dept := range deptsList {
		ret = append(ret, dept)
	}
	return ret, nil
}

func (dt DingtalkkClient) GetDepts(groupId int, rootGroupId string) (ret []*dto.ExternalDepartment, err error) {
	var res *request.DeptList
	res = new(request.DeptList)
	res.DeptId = groupId
	res.Language = "zh_CN"
	depts, err := dt.client.GetDeptList(res)
	if err != nil {
		return nil, err
	}

	if depts.Code != 0 {
		return ret, errors.New(depts.Msg)
	}

	if depts.List == nil {
		return ret, nil
	}

	for _, dept := range depts.List {
		var ele dto.ExternalDepartment
		ele.Name = dept.Name
		ele.ID = strconv.Itoa(dept.Id)
		if dept.ParentId == 1 {
			ele.Parentid = rootGroupId
		} else {
			ele.Parentid = strconv.Itoa(dept.ParentId)
		}
		ele.UniqKey = idp.GetKey(ele)
		ret = append(ret, &ele)
		chDepts, err := dt.GetDepts(dept.Id, rootGroupId)
		if err != nil {
			return nil, err
		}
		ret = append(ret, chDepts...)
	}
	return ret, nil
}

func (dt DingtalkkClient) GetAllUsers(depts []*dto.ExternalDepartment, rootGroupId string) (ret []*dto.ExternalUser, err error) {
	parentId := "1"
	parent := &dto.ExternalDepartment{
		LocalRootGroupID: "",
		ID:               parentId,
		Name:             "",
		Parentid:         "",
		Order:            0,
		LocalGroupID:     "",
	}
	idMap := make(map[string]bool)
	depts = append(depts, parent)
	for _, dept := range depts {
		userList, err := dt.GetUsers(dept.ID, rootGroupId)
		if err != nil {
			return nil, err
		}
		for _, user := range userList {
			if !idMap[user.Userid] {
				idMap[user.Userid] = true
				ret = append(ret, user)
			}
		}
	}
	return ret, nil
}

func (dt DingtalkkClient) GetUsers(departmentId, rootGroupId string) (ret []*dto.ExternalUser, err error) {
	var nextCursor int
	var res *request.DeptDetailUserInfo
	res = new(request.DeptDetailUserInfo)
	for {
		res.DeptId, err = strconv.Atoi(departmentId)
		if err != nil {
			return ret, err
		}
		res.Cursor = nextCursor
		res.Size = 100

		users, err := dt.client.GetDeptDetailUserInfo(res)
		if err != nil {
			return nil, err
		}
		if users.Code != 0 {
			return ret, errors.New(users.Msg)
		}
		if users.Page.List == nil {
			return ret, nil
		}
		idMap := make(map[string]bool)
		for _, user := range users.Page.List {
			var ele dto.ExternalUser
			ele.Name = user.Name
			ele.Userid = user.UnionId //认证时无法获取到userid，只能获取到UnionId
			if user.DeptIds[0] == 1 {
				ele.MainDepartment = rootGroupId
			} else {
				ele.MainDepartment = strconv.Itoa(user.DeptIds[0])
			}
			ele.Mobile = user.Mobile
			ele.Email = user.Email
			if !idMap[ele.Userid] {
				ele.UniqKey = idp.GetKey(ele)
				idMap[ele.Userid] = true
				ret = append(ret, &ele)
			}
		}

		if !users.Page.HasMore {
			return ret, nil
		}
		nextCursor = users.Page.NextCursor
	}
}

func ConvertToPinYin(src string) (dst string) {
	args := pinyin.NewArgs()
	args.Fallback = func(r rune, args pinyin.Args) []string {
		return []string{string(r)}
	}

	for _, singleResult := range pinyin.Pinyin(src, args) {
		for _, result := range singleResult {
			dst = dst + result
		}
	}
	return
}
