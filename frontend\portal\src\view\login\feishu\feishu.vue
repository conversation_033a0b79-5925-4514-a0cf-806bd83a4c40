<template>
  <div :key="refresh_auth">
    <div style="text-align: center">
      <span class="title" style="height:24px;line-height: 24px;margin: 0 auto;color: #0082ef;font-size: 20px;text-align: center">
        <svg aria-hidden="true" class="icon" style="height: 24px;width: 29px;vertical-align: top;margin-right: 8px;display: inline-block">
          <use :xlink:href="'#icon-auth-feishu'" />
        </svg>
        飞书认证
      </span>
    </div>
    <!-- 基于Demo版本的容器ID -->
    <div id="feishu_qrcode_login" class="feishu-qrcode-container" />
    <!-- 隐藏的iframe用于处理认证回调 -->
    <iframe :src="qrCodeSrc" style="display: none" />
  </div>
</template>

<script>
export default {
  name: '<PERSON>ish<PERSON>'
}
</script>
<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { getCurrentHost } from '@/utils/request'
import { useUserStore } from '@/pinia/modules/user'
import { Loading } from '@/components/base'
import agentApi from '@/api/agentApi'
import { useSecondaryAuth } from '@/utils/secondaryAuth'

const userStore = useUserStore()
// 双因子验证
const { handleSecondaryAuthResponse } = useSecondaryAuth()

const refresh_auth = ref(0)
const feishuUrl = ref('https://passport.feishu.cn/suite/passport/oauth/authorize')
const qrCodeSrc = ref('')
const taskId = ref(null)
const time = ref('')
const loadingInstance = ref(null)
const redirectUrl = ref('')
const qrCheckCount = ref(0)
const qrLoginObj = ref(null)
const goto = ref('')

// 防重复调用标志
const isAuthenticating = ref(false)
const lastAuthCode = ref(null)
const authTimestamp = ref(0)

const props = defineProps({
  authInfo: {
    type: Array,
    default: function() {
      return []
    },
  },
  authId: {
    type: String,
    default: function() {
      return ''
    }
  }
})

// 懒加载飞书SDK
const loadFeishuSDK = () => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载过
    if (window.QRLogin) {
      resolve(window.QRLogin)
      return
    }

    // 创建script标签
    const script = document.createElement('script')
    script.src = agentApi.getNativeJsPath('/js/feishu.js')
    script.async = true

    script.onload = () => {
      if (window.QRLogin) {
        logger.log('飞书SDK加载成功')
        resolve(window.QRLogin)
      } else {
        reject(new Error('飞书SDK加载失败：QRLogin未定义'))
      }
    }

    script.onerror = () => {
      reject(new Error('飞书SDK加载失败：网络错误'))
    }

    // 添加到head
    document.head.appendChild(script)
  })
}

// 获取设备ID
const getDeviceId = () => {
  return 'web_' + Date.now()
}

// 绘制二维码（基于Demo版本）
const drawQrCode = async() => {
  try {
    logger.log('开始绘制飞书二维码')

    // 重置防重复标志
    isAuthenticating.value = false
    lastAuthCode.value = null
    authTimestamp.value = 0

    clearQrInterval()
    time.value = new Date().getTime()

    // 获取飞书配置
    const appid = props.authInfo.fsAppId

    if (!appid) {
      console.error('飞书配置缺失:', { appid })
      showError('飞书配置缺失')
      return
    }

    const urlParam = {
      deviceid: getDeviceId(),
      appid,
      time: time.value
    }

    const redHost = getCurrentHost()

    // 构建回调URL（基于Demo版本的方式）
    redirectUrl.value = `${redHost}/auth_callback/?auth_type=feishu&${new URLSearchParams(urlParam).toString()}`

    const indexParam = {
      client_id: appid,
      state: props.authId,
      redirect_uri: redirectUrl.value,
      response_type: 'code'
    }

    goto.value = feishuUrl.value + '?' + new URLSearchParams(indexParam).toString()

    logger.log('飞书认证参数:', {
      appid,
      redirectUrl: redirectUrl.value,
      goto: goto.value
    })

    // 清空容器
    document.getElementById('feishu_qrcode_login').innerHTML = ''
    qrCheckCount.value = 0

    // 懒加载飞书SDK
    await loadFeishuSDK()

    // 调用QRLogin方法（基于Demo版本）
    qrLoginObj.value = window.QRLogin({
      id: 'feishu_qrcode_login',
      goto: goto.value,
      width: '260',
      height: '300',
      style: 'border:none;background-color:#FFFFFF;'
    })

    // 添加消息监听（基于Demo版本）
    if (typeof window.addEventListener !== 'undefined') {
      window.addEventListener('message', handleMessage, false)
    } else if (typeof window.attachEvent !== 'undefined') {
      window.attachEvent('onmessage', handleMessage)
    }

    logger.log('飞书二维码绘制完成')
  } catch (error) {
    console.error('飞书二维码绘制失败:', error)
    showError('二维码绘制失败: ' + error.message)
  }
}

// 基于Demo版本的消息处理
const handleMessage = async(event) => {
  try {
    logger.log('收到飞书消息:', event)
    if (!event || !event.data || event.data === '[tea-sdk]ready') {
      logger.log('飞书其他消息:', event.data)
      return
    }

    const origin = event.origin

    // 处理来自飞书登录页面的消息（基于Demo版本）
    if (qrLoginObj.value && qrLoginObj.value.matchOrigin && qrLoginObj.value.matchOrigin(origin)) {
      loadingInstance.value = Loading.service({
        fullscreen: true,
        text: '登录中，请稍候...',
      })
      logger.log('飞书消息数据:', event.data)

      const loginTmpCode = event.data
      logger.log('收到飞书loginTmpCode:', loginTmpCode)

      // 构建带loginTmpCode的认证URL（基于Demo版本）
      const finalUrl = `${goto.value}&tmp_code=${loginTmpCode}`
      logger.log('飞书认证URL:', finalUrl)

      // 创建隐藏的iframe来处理认证回调
      createCallbackIframe(finalUrl)
    } else if (event.data && event.data.type === 'feishu_auth_callback') {
      logger.log('收到callback.html认证结果:', event.data)

      const { code, state, error } = event.data

      if (error) {
        console.error('飞书认证失败:', error)
        showError('认证失败: ' + error)
        return
      }

      if (code && state) {
        // 快速防重复检查
        if (isAuthenticating.value) {
          logger.log('飞书认证正在进行中，忽略callback消息')
          return
        }

        if (lastAuthCode.value === code) {
          logger.log('飞书认证重复的callback消息，忽略')
          return
        }

        logger.log('飞书认证成功，code:', code, 'state:', state)
        await handleAuthSuccess(code, state)
      } else {
        console.error('认证结果缺少必要参数')
        showError('认证结果无效')
      }
    }
  } catch (error) {
    console.error('飞书消息处理失败:', error)
  }
}

// 创建隐藏的iframe来处理认证回调
const createCallbackIframe = (authUrl) => {
  logger.log('创建认证回调iframe:', authUrl)

  // 移除已存在的回调iframe
  const existingIframe = document.getElementById('feishu-callback-iframe')
  if (existingIframe) {
    existingIframe.remove()
  }

  // 创建新的隐藏iframe
  const iframe = document.createElement('iframe')
  iframe.id = 'feishu-callback-iframe'
  iframe.src = authUrl
  iframe.style.display = 'none'
  iframe.style.width = '0'
  iframe.style.height = '0'
  iframe.style.border = 'none'

  // 添加到页面
  document.body.appendChild(iframe)

  logger.log('认证回调iframe已创建')
}

// 处理认证成功
const handleAuthSuccess = async(code, state) => {
  try {
    const currentTime = Date.now()

    logger.log('飞书认证回调触发:', { code, state, currentTime })

    // 多重防重复机制
    // 1. 检查是否正在认证中
    if (isAuthenticating.value) {
      logger.log('飞书认证正在进行中，忽略重复调用 - 状态检查')
      return
    }

    // 2. 检查是否是相同的认证码
    if (lastAuthCode.value === code) {
      logger.log('飞书认证重复的authCode，忽略重复调用 - 认证码检查')
      return
    }

    // 3. 检查时间间隔（防止短时间内多次调用）
    if (currentTime - authTimestamp.value < 2000) { // 2秒内不允许重复
      logger.log('飞书认证时间间隔过短，忽略重复调用 - 时间检查')
      return
    }

    // 设置防重复标志
    isAuthenticating.value = true
    lastAuthCode.value = code
    authTimestamp.value = currentTime

    logger.log('飞书认证成功，开始处理:', { code, state })

    // 构建登录参数
    const query = {
      clientId: 'client_portal',
      grantType: 'implicit',
      redirect_uri: `${getCurrentHost()}/auth_callback/`,
      idpId: Array.isArray(state) ? state[0] : state,
      authWeb: {
        authWebCode: Array.isArray(code) ? code[0] : code,
      }
    }

    logger.log('调用登录接口，参数:', query)
    loadingInstance.value && loadingInstance.value.close()

    // 调用登录接口
    const res = await userStore.LoginIn(query, 'feishu', props.authId)

    if (res && res.code !== -1) {
      // 双因子验证
      const needSecondaryAuth = await handleSecondaryAuthResponse(res)
      if (needSecondaryAuth) {
        logger.log('飞书登录成功，进入双因子验证')
        return
      }
      logger.log('飞书登录成功')
    } else {
      console.error('飞书登录失败')
      showError('登录失败，请重试')
      drawQrCode() // 重新绘制二维码
    }

    // 5秒后重置标志，允许重新认证
    setTimeout(() => {
      isAuthenticating.value = false
      logger.log('飞书认证状态已重置')
    }, 5000)
  } catch (error) {
    console.error('飞书认证处理失败:', error)
    showError('认证处理失败: ' + error.message)
    drawQrCode() // 重新绘制二维码

    // 发生错误时也要重置标志
    isAuthenticating.value = false
    lastAuthCode.value = null
  }
}

// 清除定时器
const clearQrInterval = () => {
  qrCheckCount.value = 0
  if (taskId.value) {
    clearInterval(taskId.value)
    taskId.value = null
  }
}

// 显示错误信息
const showError = (message) => {
  const container = document.getElementById('feishu_qrcode_login')
  if (container) {
    container.innerHTML = `
      <div style="text-align: center; padding: 20px; color: #f56c6c;">
        <div style="margin-bottom: 10px;">飞书认证失败</div>
        <div style="font-size: 12px; color: #909399;">${message}</div>
        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">
          重试
        </button>
      </div>
    `
  }
}

// 组件挂载时初始化
onMounted(() => {
  logger.log('飞书认证组件挂载')
  drawQrCode()
})

// 组件卸载时清理
onUnmounted(() => {
  logger.log('飞书认证组件卸载')

  // 重置防重复标志
  isAuthenticating.value = false
  lastAuthCode.value = null
  authTimestamp.value = 0

  // 移除回调iframe
  const existingIframe = document.getElementById('feishu-callback-iframe')
  if (existingIframe) {
    existingIframe.remove()
  }

  if (typeof window.addEventListener !== 'undefined') {
    window.removeEventListener('message', handleMessage, false)
  } else if (typeof window.attachEvent !== 'undefined') {
    window.detachEvent('onmessage', handleMessage)
  }
})

// 监听props变化
watch(props, () => {
  logger.log('飞书认证props变化，重新绘制二维码')
  refresh_auth.value++
  drawQrCode()
})
</script>

<style lang="scss" scoped>
.feishu-qrcode-container {
  padding-top: 15px;
  overflow: hidden;
  text-align: center;
}
</style>
