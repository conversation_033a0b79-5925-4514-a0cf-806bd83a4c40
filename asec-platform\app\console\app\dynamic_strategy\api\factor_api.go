package api

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/consts"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/service"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strings"
)

// FactorList godoc
// @Summary 动态策略条件因子列表
// @Schemes
// @Description 动态策略条件因子列表
// @Tags        DynamicFactor
// @Produce     application/json
// @Success     200
// @Router      /v1/dynamic_strategy/factor_list [GET]
// @success     200 {object} common.Response{data=[]vo.FactorListResp} "ok"
func FactorList(c *gin.Context) {
	list, aError := service.GetFactorService().FactorList(c)
	if aError != nil {
		global.SysLog.Error("get factorList err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, list)
}

// AddFactorTime godoc
// @Summary 添加时间间隔模板
// @Schemes
// @Description 添加时间间隔模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.CreateFactorTimeReq true "添加时间间隔模板参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_time [POST]
// @success     200 {object} common.Response{} "ok"
func AddFactorTime(c *gin.Context) {
	req := vo.CreateFactorTimeReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorTimeTYpe,
			OperationType:  common.OperateCreate,
			Representation: req.GapName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().AddFactorTime(c, req)
	if aError != nil {
		global.SysLog.Error("addFactorTime err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// FactorTimeList godoc
// @Summary 获取时间间隔模板
// @Schemes
// @Description 获取时间间隔模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Success     200
// @Router      /v1/dynamic_strategy/factor_time_list [GET]
// @success     200 {object} common.Response{data=[]vo.FactorTimeListResp} "ok"
func FactorTimeList(c *gin.Context) {
	list, aError := service.GetFactorService().FactorTimeList(c)
	if aError != nil {
		global.SysLog.Error("addFactorTime err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, list)
}

// FactorTimeListPage godoc
// @Summary 分页获取时间间隔模板
// @Schemes
// @Description 分页获取时间间隔模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.FactorTimeListPageReq true "分页获取时间间隔模板参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_time_list_page [POST]
// @success     200 {object} common.Response{data=vo.FactorTimeListPageResp} "ok"
func FactorTimeListPage(c *gin.Context) {
	req := vo.FactorTimeListPageReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	listPage, aError := service.GetFactorService().FactorTimeListPage(c, req)
	if aError != nil {
		global.SysLog.Error("get factorTimeListPage err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, listPage)
}

// UpdateFactorTime godoc
// @Summary 更新时间间隔模板
// @Schemes
// @Description 更新时间间隔模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.UpdateFactorTimeReq true "更新时间间隔模板参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_time [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateFactorTime(c *gin.Context) {
	req := vo.UpdateFactorTimeReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorTimeTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.GapName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().UpdateFactorTime(c, req)
	if aError != nil {
		global.SysLog.Error("updateFactorTime err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// DelFactorTime godoc
// @Summary 删除时间间隔模板
// @Schemes
// @Description 删除时间间隔模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.DelReq true "删除时间间隔模板参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_time [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelFactorTime(c *gin.Context) {
	req := vo.DelReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	quote, authPolicy, err := service.GetFactorService().CheckDelTimeQuote(c, req.Ids)
	if err != nil {
		global.SysLog.Error("CheckDelQuote err:", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if len(quote) > 0 {
		common.FailFormat(c, consts.CheckFactorQuoteErr, "rule_names", quote)
		return
	}
	if len(authPolicy) > 0 {
		common.FailFormat(c, consts.CheckFactorTimePolicyErr, "name", authPolicy)
		return
	}

	//日志操作
	var errorLog = ""
	representationVal := strings.Join(req.Names, ",")
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorTimeTYpe,
			OperationType:  common.OperateDelete,
			Representation: representationVal,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().DelFactorTime(c, req)
	if aError != nil {
		global.SysLog.Error("delFactorTime err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// CreateFactorIp godoc
// @Summary 添加IP地址模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.CreateFactorIpReq true "添加IP地址模板参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_ip [POST]
// @success     200 {object} common.Response{} "ok"
func CreateFactorIp(c *gin.Context) {
	req := vo.CreateFactorIpReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorIpTYpe,
			OperationType:  common.OperateCreate,
			Representation: req.IpName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().CreateFactorIp(c, req)
	if aError != nil {
		global.SysLog.Error("CreateFactorIp err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// FactorIpList godoc
// @Summary IP地址模板列表
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.ListReq true "IP地址模板列表参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_ip_list [POST]
// @success     200 {object} common.Response{data=vo.FactorIpListResp} "ok"
func FactorIpList(c *gin.Context) {
	req := vo.ListReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, aError := service.GetFactorService().FactorIpList(c, req)
	if aError != nil {
		global.SysLog.Error("FactorIpList err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, data)
}

// UpdateFactorIp godoc
// @Summary 修改IP地址模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.UpdateFactorIpReq true "修改IP地址模板参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_ip [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateFactorIp(c *gin.Context) {
	req := vo.UpdateFactorIpReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorIpTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.IpName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().UpdateFactorIp(c, req)
	if aError != nil {
		global.SysLog.Error("UpdateFactorIp err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// DelFactorIp godoc
// @Summary     删除IP地址模板
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.DelReq true "删除IP地址模板参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_ip [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelFactorIp(c *gin.Context) {
	req := vo.DelReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	quote, err := service.GetFactorService().CheckDelQuote(c, req.Ids)
	if err != nil {
		global.SysLog.Error("CheckDelQuote err:", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if len(quote) > 0 {
		common.FailFormat(c, consts.CheckFactorQuoteErr, "rule_names", quote)
		return
	}

	//日志操作
	var errorLog = ""
	representationVal := strings.Join(req.Names, ",")
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorIpTYpe,
			OperationType:  common.OperateDelete,
			Representation: representationVal,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().DelFactorIp(c, req)
	if aError != nil {
		global.SysLog.Error("DelFactorIp err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// CreateNetLocation godoc
// @Summary 	添加网络位置
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.CreateNetLocationReq true "添加网络位置参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_net_location [POST]
// @success     200 {object} common.Response{} "ok"
func CreateNetLocation(c *gin.Context) {
	req := vo.CreateNetLocationReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if len(req.PrivateIp) <= 0 && len(req.PublicIp) <= 0 && len(req.Dns) <= 0 && len(req.WifiSsd) <= 0 {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, consts.NetLocationReqNilErr)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorNetLocationTYpe,
			OperationType:  common.OperateCreate,
			Representation: req.NetLocationName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().CreateNetLocation(c, req)
	if aError != nil {
		global.SysLog.Error("CreateNetLocation err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// NetLocationList godoc
// @Summary 	网络位置列表
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.ListReq true "网络位置列表"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_net_location_list [POST]
// @success     200 {object} common.Response{data=vo.NetLocationListResp} "ok"
func NetLocationList(c *gin.Context) {
	req := vo.ListReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, aError := service.GetFactorService().NetLocationList(c, req)
	if aError != nil {
		global.SysLog.Error("NetLocationList err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, data)
}

// UpdateNetLocation godoc
// @Summary 	修改网络位置
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.UpdateNetLocationReq true "修改网络位置"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_net_location [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateNetLocation(c *gin.Context) {
	req := vo.UpdateNetLocationReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorNetLocationTYpe,
			OperationType:  common.OperateUpdate,
			Representation: req.NetLocationName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().UpdateNetLocation(c, req)
	if aError != nil {
		global.SysLog.Error("UpdateNetLocation err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// DelNetLocation godoc
// @Summary     删除网络位置
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.DelReq true "删除网络位置参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_net_location [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelNetLocation(c *gin.Context) {
	req := vo.DelReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	quote, err := service.GetFactorService().CheckDelQuote(c, req.Ids)
	if err != nil {
		global.SysLog.Error("CheckDelQuote err:", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if len(quote) > 0 {
		common.FailFormat(c, consts.CheckFactorQuoteErr, "rule_names", quote)
		return
	}

	//日志操作
	var errorLog = ""
	representation := strings.Join(req.Names, ",")
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorNetLocationTYpe,
			OperationType:  common.OperateDelete,
			Representation: representation,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().DelNetLocation(c, req)
	if aError != nil {
		global.SysLog.Error("DelNetLocation err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// CreateFactorProcess godoc
// @Summary 	添加进程配置
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.CreateFactorProcessReq true "添加进程配置参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_process [POST]
// @success     200 {object} common.Response{} "ok"
func CreateFactorProcess(c *gin.Context) {
	req := vo.CreateFactorProcessReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	representation := strings.Join(req.ProcessName, ",")
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorProcessTYpe,
			OperationType:  common.OperateCreate,
			Representation: representation,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().CreateFactorProcess(c, req)
	if aError != nil {
		global.SysLog.Error("CreateFactorProcess err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// FactorProcessList godoc
// @Summary 	进程配置列表
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.ListReq true "进程配置列表参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_process_list [POST]
// @success     200 {object} common.Response{data=vo.FactorProcessListResp} "ok"
func FactorProcessList(c *gin.Context) {
	req := vo.ListReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, aError := service.GetFactorService().FactorProcessList(c, req)
	if aError != nil {
		global.SysLog.Error("FactorProcessList err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, data)

}

// UpdateFactorProcess godoc
// @Summary 	修改进程配置
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.UpdateFactorProcessReq true "修改进程配置参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_process [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateFactorProcess(c *gin.Context) {
	req := vo.UpdateFactorProcessReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	representation := strings.Join(req.ProcessName, ",")
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorProcessTYpe,
			OperationType:  common.OperateUpdate,
			Representation: representation,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().UpdateFactorProcess(c, req)
	if aError != nil {
		global.SysLog.Error("UpdateFactorProcess err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// DelFactorProcess godoc
// @Summary     删除进程配置
// @Tags        DynamicFactor
// @Produce     application/json
// @Param       req body vo.DelReq true "删除进程配置参数"
// @Success     200
// @Router      /v1/dynamic_strategy/factor_process [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelFactorProcess(c *gin.Context) {
	req := vo.DelReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	quote, err := service.GetFactorService().CheckDelQuote(c, req.Ids)
	if err != nil {
		global.SysLog.Error("CheckDelQuote err:", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if len(quote) > 0 {
		common.FailFormat(c, consts.CheckFactorQuoteErr, "rule_names", quote)
		return
	}

	//日志操作
	var errorLog = ""
	representation := strings.Join(req.Names, ",")
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.FactorProcessTYpe,
			OperationType:  common.OperateDelete,
			Representation: representation,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	aError := service.GetFactorService().DelFactorProcess(c, req)
	if aError != nil {
		global.SysLog.Error("DelFactorProcess err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// FactorIp godoc
// @Summary ip因子
// @Schemes
// @Description ip因子
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Success     200
// @Router      /v1/dynamic_strategy/factor/ip [GET]
// @success     200 {object} common.Response{data=[]vo.FactorIpResp} "ok"
func FactorIp(c *gin.Context) {
	data, aError := service.GetFactorService().FactorIp(c)
	if aError != nil {
		global.SysLog.Error("DelFactorProcess err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, data)
}

// FactorProcess godoc
// @Summary 进程因子
// @Schemes
// @Description 进程因子
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Success     200
// @Router      /v1/dynamic_strategy/factor/process [GET]
// @success     200 {object} common.Response{data=[]vo.FactorProcessResp} "ok"
func FactorProcess(c *gin.Context) {
	data, aError := service.GetFactorService().FactorProcess(c)
	if aError != nil {
		global.SysLog.Error("DelFactorProcess err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, data)
}

// FactorNetLocation godoc
// @Summary 网络位置因子
// @Schemes
// @Description 网络位置因子
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Success     200
// @Router      /v1/dynamic_strategy/factor/net_location [GET]
// @success     200 {object} common.Response{data=[]vo.FactorNetLocationResp} "ok"
func FactorNetLocation(c *gin.Context) {
	data, aError := service.GetFactorService().FactorNetLocation(c)
	if aError != nil {
		global.SysLog.Error("DelFactorProcess err:", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, data)
}
