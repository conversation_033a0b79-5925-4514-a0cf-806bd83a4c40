// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserGroupSync = "tb_user_group_sync"

// TbUserGroupSync mapped from table <tb_user_group_sync>
type TbUserGroupSync struct {
	ID           string    `gorm:"column:id;primaryKey" json:"id"`
	GroupID      string    `gorm:"column:group_id;not null" json:"group_id"`
	AutoSync     bool      `gorm:"column:auto_sync" json:"auto_sync"`
	LastSyncTime time.Time `gorm:"column:last_sync_time" json:"last_sync_time"`
	NextSyncTime time.Time `gorm:"column:next_sync_time" json:"next_sync_time"`
	SyncStatus   string    `gorm:"column:sync_status" json:"sync_status"`
	SyncCycle    int32     `gorm:"column:sync_cycle" json:"sync_cycle"`
	SyncUnit     string    `gorm:"column:sync_unit" json:"sync_unit"`
	CreatedAt    time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbUserGroupSync's table name
func (*TbUserGroupSync) TableName() string {
	return TableNameTbUserGroupSync
}
