package feishu

import (
	"context"
	"errors"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp"
	"github.com/go-kratos/kratos/v2/log"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"github.com/mozillazg/go-pinyin"
)

type LarkClient struct {
	client *lark.Client
}

func NewLarkClient(appId, secret string) *LarkClient {
	client := lark.NewClient(appId, secret)
	return &LarkClient{client: client}
}

// 官方文档： https://open.feishu.cn/document/server-docs/contact-v3/department/children
// GET /open-apis/contact/v3/departments/:department_id/children
// GetAllDepts 获取子级部门
func (lk LarkClient) GetAllDepts(rootGroupId string) (ret []*dto.ExternalDepartment, allUser []*dto.ExternalUser, err error) {
	deptsList, allUser, err := lk.GetDeptsAndUsers("0", rootGroupId)
	if err != nil {
		return nil, nil, err
	}
	if len(deptsList) == 0 {
		return nil, nil, err
	}
	for _, dept := range deptsList {
		ret = append(ret, dept)
	}
	return ret, allUser, nil
}

// 分页查询组下所有用户
// page_token分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
func (lk LarkClient) GetDeptsAndUsers(groupId string, rootGroupId string) (ret []*dto.ExternalDepartment, allUser []*dto.ExternalUser, err error) {
	var pageToken *string
	var req = larkcontact.NewChildrenDepartmentReqBuilder().Build()
	req = larkcontact.NewChildrenDepartmentReqBuilder().
		DepartmentId(groupId).
		UserIdType("open_id").
		DepartmentIdType("open_department_id").
		FetchChild(true).
		PageSize(50).
		Build()
	depts, err := lk.client.Contact.Department.Children(context.Background(), req)
	if err != nil || depts == nil || !depts.Success() {
		// Handle the case where depts is nil or there's an error
		msg := "unknown error"
		if depts != nil {
			msg = depts.CodeError.Msg
		} else if err != nil {
			msg = err.Error()
		}
		return nil, nil, errors.New(msg)
	}
	var deptList []*larkcontact.Department
	deptList = append(deptList, depts.Data.Items...)
	for *depts.Data.HasMore && depts.Success() {
		pageToken = depts.Data.PageToken
		req = larkcontact.NewChildrenDepartmentReqBuilder().
			DepartmentId(groupId).
			UserIdType("open_id").
			DepartmentIdType("open_department_id").
			FetchChild(true).
			PageSize(50).
			PageToken(*pageToken).
			Build()
		depts, err = lk.client.Contact.Department.Children(context.Background(), req)
		if err != nil || !depts.Success() {
			log.Errorf("get depts failed. err=%v", err)
			return nil, nil, errors.New(depts.CodeError.Msg)
		}
		deptList = append(deptList, depts.Data.Items...)
	}

	fcMap := make(map[string]string)
	lastDeptId := "0"
	rootDeptId := "0"
	allLarkUser := make([]*larkcontact.User, 0)
	for index, dept := range deptList {
		// get user by dept
		if *dept.ParentDepartmentId == rootDeptId && len(fcMap) > 0 {
			tmpId := lastDeptId
			for tmpId != rootDeptId {
				userList, err := lk.GetUsers(tmpId)
				if err != nil {
					log.Errorf("get user from lark failed. err=%v", err)
				}
				if len(userList) > 0 {
					allLarkUser = append(allLarkUser, userList...)
				}
				tmpId = fcMap[tmpId]
			}
			fcMap[*dept.OpenDepartmentId] = rootDeptId
		} else {
			fcMap[*dept.OpenDepartmentId] = lastDeptId
		}
		lastDeptId = *dept.OpenDepartmentId
		// 最后的根部门
		if index == (len(deptList) - 1) {
			tmpId := lastDeptId
			for tmpId != rootDeptId {
				userList, err := lk.GetUsers(tmpId)
				if err != nil {
					log.Errorf("get user from lark failed. err=%v", err)
				}
				if len(userList) > 0 {
					allLarkUser = append(allLarkUser, userList...)
				}
				tmpId = fcMap[tmpId]
			}
		}
		// get dept
		var ele dto.ExternalDepartment
		ele.Name = *dept.Name
		ele.ID = *dept.OpenDepartmentId
		if *dept.ParentDepartmentId == "0" {
			ele.Parentid = rootGroupId
		} else {
			ele.Parentid = *dept.ParentDepartmentId
		}
		ele.LocalRootGroupID = rootGroupId
		ele.UniqKey = idp.GetKey(ele)
		ret = append(ret, &ele)
	}
	// root user
	userList, err := lk.GetUsers(rootDeptId)
	if err != nil {
		log.Errorf("get user from lark failed. err=%v", err)
	}
	allLarkUser = append(allLarkUser, userList...)
	allUser = structExtUser(allLarkUser, rootGroupId)
	return ret, allUser, nil
}

func structExtUser(users []*larkcontact.User, rootGroupId string) []*dto.ExternalUser {
	userIdMap := make(map[string]bool)
	ret := make([]*dto.ExternalUser, 0)
	for _, user := range users {
		if user == nil || user.Name == nil || user.UserId == nil {
			continue
		}
		var ele = dto.ExternalUser{
			Name:   *user.Name,
			Userid: *user.UserId,
			Mobile: *user.Mobile,
			Email:  *user.Email,
		}
		if *user.Orders[0].DepartmentId == "0" {
			ele.MainDepartment = rootGroupId
		} else {
			ele.MainDepartment = *user.Orders[0].DepartmentId
		}
		// 避免重复插入
		if !userIdMap[*user.UserId] {
			ele.UniqKey = idp.GetKey(ele)
			ret = append(ret, &ele)
			userIdMap[ele.Userid] = true
		}
	}
	return ret
}

// 官方文档： https://developer.work.weixin.qq.com/document/path/90201
// GetAllUsers 获取所有员工信息
//func (lk LarkClient) GetAllUsers(log *log.Helper, depts []*dto.ExternalDepartment, rootGroupId string) (ret []*dto.ExternalUser, err error) {
//	parentId := "0"
//	userIdMap := make(map[string]bool)
//	allUser := make([]*dto.ExternalUser, 0)
//	wg := sync.WaitGroup{}
//	for _, dept := range depts {
//		wg.Add(1)
//		go func() {
//			userList, err := lk.GetUsers(dept.ID, rootGroupId)
//			if err != nil {
//				log.Errorf("get user from lark failed. err=%v", err)
//			}
//			if len(userList) > 0 {
//				allUser = append(allUser, userList...)
//			}
//			wg.Done()
//		}()
//	}
//	wg.Wait()
//	// 根组下的用户
//	userList, err := lk.GetUsers(parentId, rootGroupId)
//	if err != nil {
//		log.Errorf("get user from lark failed. err=%v", err)
//	}
//	allUser = append(allUser, userList...)
//	for _, user := range allUser {
//		if !userIdMap[user.Userid] {
//			ret = append(ret, user)
//			userIdMap[user.Userid] = true
//		}
//	}
//	return ret, nil
//}

// 分页查询组下所有用户
// page_token分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
func (lk LarkClient) GetUsers(departmentId string) (ret []*larkcontact.User, err error) {
	var pageToken *string
	var req = larkcontact.NewFindByDepartmentUserReqBuilder().
		UserIdType("open_id").
		DepartmentIdType("open_department_id").
		DepartmentId(departmentId).
		PageSize(50).
		Build()
	firstTime := true
	var users = &larkcontact.FindByDepartmentUserResp{
		Data: &larkcontact.FindByDepartmentUserRespData{
			HasMore: &firstTime,
		},
	}

	for *users.Data.HasMore {
		if users.Data.PageToken != nil {
			req = larkcontact.NewFindByDepartmentUserReqBuilder().
				UserIdType("open_id").
				DepartmentIdType("open_department_id").
				DepartmentId(departmentId).
				PageSize(50).
				PageToken(*pageToken).
				Build()
		}
		users, err = lk.client.Contact.User.FindByDepartment(context.Background(), req)
		if err != nil {
			return nil, err
		}
		if !users.Success() {
			return ret, users.CodeError
		}
		pageToken = users.Data.PageToken
		ret = append(ret, users.Data.Items...)
	}

	return ret, nil
}

func ConvertToPinYin(src string) (dst string) {
	args := pinyin.NewArgs()
	args.Fallback = func(r rune, args pinyin.Args) []string {
		return []string{string(r)}
	}

	for _, singleResult := range pinyin.Pinyin(src, args) {
		for _, result := range singleResult {
			dst = dst + result
		}
	}
	return
}
