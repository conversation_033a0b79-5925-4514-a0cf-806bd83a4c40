syntax = "proto3";

package api.conf;
option go_package = "asdsec.com/asec/platform/api/conf/v1;v1";

message ScanTask
{
  // 任务id
  string task_id = 1;
  // 任务优先级
  int32 task_priority = 2;
  // 任务启用状态 1启用 2禁用
  int32 task_enable = 3;
  // 扫描指定的文件code
  repeated int32 file_type_code = 4;
  // 扫描位置类型 1全盘 ; 2指定
  int32 scan_position_type = 5;
  // 自定义目录
  repeated string scan_directory = 6;
  // 自定义排除目录
  repeated string scan_exclude_directory = 7;
  // 扫描时间
  TimePolicy scan_time = 8;
  // 扫描文件最小大小 kb
  int32 scan_min_file_size = 9;
  // 扫描文件最大大小 kb
  int32 scan_max_file_size = 10;
  // 仅空闲状态扫描
  bool idle_scan_switch = 11;
  // 仅充电状态扫描
  bool charging_scan_switch = 12;
  // 每分钟扫描阈值
  int32 minute_count_limit = 13;
}

message TimePolicy{
  // 每日时间起
  string dailyStartTime = 1;
  // 每日事件止
  string dailyEndTime = 2;
  // 是否全天生效
  bool allDayEnable = 3;
}
