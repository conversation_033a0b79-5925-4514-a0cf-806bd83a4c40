// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbAuthPolicyIdpMapper = "tb_auth_policy_idp_mapper"

// TbAuthPolicyIdpMapper mapped from table <tb_auth_policy_idp_mapper>
type TbAuthPolicyIdpMapper struct {
	PolicyID  string    `gorm:"column:policy_id;not null" json:"policy_id"`
	IdpID     string    `gorm:"column:idp_id" json:"idp_id"`
	CorpID    string    `gorm:"column:corp_id" json:"corp_id"`
	CreatedAt time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbAuthPolicyIdpMapper's table name
func (*TbAuthPolicyIdpMapper) TableName() string {
	return TableNameTbAuthPolicyIdpMapper
}
