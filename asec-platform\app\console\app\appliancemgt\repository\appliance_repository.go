package repository

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	commonApi "asdsec.com/asec/platform/app/console/common/api"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model/agent_model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"asdsec.com/asec/platform/pkg/utils"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"github.com/lib/pq"
	"gopkg.in/ini.v1"

	"asdsec.com/asec/platform/app/console/app/appliancemgt/common"
	comm "asdsec.com/asec/platform/app/console/common"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/console/app/appliancemgt/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/model/auth_model"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// AppRepository 接口定义
type AppRepository interface {
	GetById(ctx context.Context, id uint64) (model.Appliance, error)
	GetAgents(ctx context.Context, pagination model.Pagination, req dto.GetAgentListReq) (model.Pagination, error)
	GetAppListByID(ctx context.Context, ids []string) ([]dto.AgentResp, error)
	CreateInstallCMD(ctx context.Context, cmd model.ApplianceInstall) (model.ApplianceInstall, error)
	GetInstallCMD(ctx context.Context, name string) (model.ApplianceInstall, error)
	GeAppliances(ctx context.Context, req model.Pagination) (model.Pagination, error)
	DeleteById(ctx context.Context, id uint64) error
	GetSEAppRelation(ctx context.Context, id uint64) ([]dto.ApplianceAPPRelation, error)
	DeviceCount(ctx context.Context) ([]dto.DeviceCount, error)
	UpdateAppliance(ctx context.Context, appliance model.Appliance) error
	UpdateInstallCMD(ctx context.Context, applianceId uint64, name string, desc string, gateIp string, port uint16, gatewayLocalIP string, platformDomain string) error
	GetDeployChannel(ctx context.Context) (model.DeployModeInfo, error)
	GetSystemVersionByName(ctx context.Context, name string) (model.SystemVersion, error)

	GetUpgradePolicy(ctx context.Context, corpId, platform string) (dto.TbAgentUpgradePolicy, error)
	GetUpgradePolicyByID(ctx context.Context, id string) (dto.TbAgentUpgradePolicy, error)
	GetGrayPolicy(ctx context.Context, id string) (dto.TbAgentUpgradeGrayPolicy, error)
	GetGrayPolicyByUpgradeID(ctx context.Context, policyID string) (dto.TbAgentUpgradeGrayPolicy, error)
	UpdateUpgradePolicy(ctx context.Context, policy dto.TbAgentUpgradePolicy) error
	UpdateGrayPolicy(ctx context.Context, policy dto.TbAgentUpgradeGrayPolicy) error
	CreateGrayPolicy(ctx context.Context, id string, policy dto.TbAgentUpgradeGrayPolicy) error
	SetPolicyLatestVersion(ctx context.Context, id, version string) error
	SetPolicyUpgradeTime(ctx context.Context, id string) error

	GetUpgradeLog(ctx context.Context, agentId int64, nextVersion string) (dto.TbAgentUpgradeLog, error)
	GetAgentIDsOfStatus(ctx context.Context, policyID, nextVersion string, status []string) ([]int64, error)
	CreateUpgradeLog(ctx context.Context, log dto.TbAgentUpgradeLog) error
	SetUpgradeLogStatus(ctx context.Context, id int64, status string) error
	SetAgentsToGray(ctx context.Context, policyID string, agentIds []int64, nextVersion string) error
	CountUpgradeLogsOfStatus(ctx context.Context, policyID, nextVersion, status string) (int64, error)
	GetUpgradeLogsOfStatus(ctx context.Context, policyID, nextVersion, status string) ([]dto.TbAgentUpgradeLog, error)
	CountAgentUpgradeLogsOfStatus(ctx context.Context, agentID int64, nextVersion, status string) (int64, error)
	CountFailedAgentUpgradeLogs(ctx context.Context, agentID int64, nextVersion string) (int64, error)
	BatchCleanPendingGrayTask(ctx context.Context, policyID, version string) error
	SetTaskFailed(ctx context.Context, id int64, failedReason, failedSource string) error
	BatchCreateGrayTask(ctx context.Context, tasks []dto.TbAgentUpgradeLog) error
	SetGrayPolicyEndTime(ctx context.Context, id string, endTime time.Time) error
	UpdateGrayPolicyPeriod(ctx context.Context, id string, startTime, endTime time.Time) error

	GetGroupNameByID(ctx context.Context, groupId string) (string, error)
	GetAgentCount(ctx context.Context, pagination model.Pagination) (dto.AgentCountRsp, error)
	DeleteAgentsRecord(ctx context.Context, req dto.ReqIds) aerrors.AError

	CountAgent(ctx context.Context) (int64, error)
	BindAgentUser(ctx context.Context, req dto.AgentBindUserReq) error
	AgentLogList(ctx context.Context, req dto.AgentLogsListReq) (model.Pagination, error)
	CheckOffline(ctx context.Context) error

	QueryAgentDetails(ctx context.Context, agentIds pq.Int64Array) ([]dto.AgentEntity, error)
	QueryGroupDetails(ctx context.Context, groupIds []string) ([]dto.GroupEntity, error)
	QueryUserDetails(ctx context.Context, userIds []string) ([]dto.UserEntity, error)

	// 新增的灰度代理检查相关方法
	GetEnabledGrayPolicies(ctx context.Context) ([]dto.TbAgentUpgradePolicy, error)
	GetGrayPolicyByID(ctx context.Context, id string) (dto.TbAgentUpgradeGrayPolicy, error)
	GetGrayTasksByPolicyID(ctx context.Context, policyID string) ([]dto.TbAgentUpgradeLog, error)
}

// NewAppRepository 创建接口实现接口实现
func NewAppRepository() AppRepository {
	return &appRepository{}
}

type appRepository struct {
}

func (u *appRepository) CheckOffline(ctx context.Context) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil
	}

	rdb, err := global.GetRedisClient(ctx)
	if err != nil {
		return nil
	}

	// 1. 从Redis获取所有心跳key
	pattern := fmt.Sprintf("AGENT_HEARTBEAT_*")
	iter := rdb.Scan(ctx, 0, pattern, 0).Iterator()

	var onlineAgents []model.AgentHeartbeat
	var potentialOfflineAgents []model.AgentHeartbeat
	nowTime := utils.GetFormattedNowTime()

	// 2. 遍历所有心跳记录检查状态
	for iter.Next(ctx) {
		key := iter.Val()
		lastHbStr, err := rdb.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				continue
			}
			global.SysLog.Error("get redis heartbeat err", zap.Error(err))
			continue
		}

		lastHbTime, err := strconv.ParseInt(lastHbStr, 10, 64)
		if err != nil {
			global.SysLog.Error("parse heartbeat time err", zap.Error(err))
			continue
		}

		// 提取agent ID
		agentIDStr := strings.TrimPrefix(key, "AGENT_HEARTBEAT_")
		agentID, err := strconv.ParseUint(agentIDStr, 10, 64)
		if err != nil {
			global.SysLog.Error("parse agent id err", zap.Error(err))
			continue
		}

		// 创建心跳记录
		hb := model.AgentHeartbeat{
			AgentID: agentID,
			ApplianceHeartbeatBase: model.ApplianceHeartbeatBase{
				UpdatedAt: time.Unix(lastHbTime, 0),
			},
		}

		// 检查是否超过离线时间阈值
		if time.Now().Unix()-lastHbTime > int64(common.OfflineCheckInterval*60) {
			hb.CurrentStatus = common.OfflineStatus
			potentialOfflineAgents = append(potentialOfflineAgents, hb)
		} else {
			hb.CurrentStatus = "online"
			onlineAgents = append(onlineAgents, hb)
		}
	}

	// 3. 批量更新数据库
	return db.Transaction(func(tx *gorm.DB) error {
		// 批量更新在线状态
		if len(onlineAgents) > 0 {
			// 每100条记录一批
			for i := 0; i < len(onlineAgents); i += 100 {
				end := i + 100
				if end > len(onlineAgents) {
					end = len(onlineAgents)
				}
				batch := onlineAgents[i:end]

				// 使用 Upsert 语句
				if err := tx.Clauses(clause.OnConflict{
					Columns: []clause.Column{{Name: "agent_id"}},
					DoUpdates: clause.AssignmentColumns([]string{
						"current_status",
						"updated_at",
					}),
				}).Create(&batch).Error; err != nil {
					return err
				}
			}
		}

		// 检查潜在离线终端的当前状态并处理
		if len(potentialOfflineAgents) > 0 {
			for i := 0; i < len(potentialOfflineAgents); i += 100 {
				end := i + 100
				if end > len(potentialOfflineAgents) {
					end = len(potentialOfflineAgents)
				}
				batch := potentialOfflineAgents[i:end]

				// 获取当前在线的终端列表
				var agentIDs []uint64
				for _, agent := range batch {
					agentIDs = append(agentIDs, agent.AgentID)
				}

				var currentOnlineAgents []model.AgentHeartbeat
				if err := tx.Where("agent_id IN ? AND (current_status != ? OR current_status IS NULL)",
					agentIDs, common.OfflineStatus).
					Find(&currentOnlineAgents).Error; err != nil {
					return err
				}

				// 创建在线终端ID的map用于快速查找
				onlineAgentMap := make(map[uint64]struct{})
				for _, agent := range currentOnlineAgents {
					onlineAgentMap[agent.AgentID] = struct{}{}
				}

				// 筛选出需要更新状态和创建事件的终端
				var needUpdateAgents []model.AgentHeartbeat
				var events []agent_model.AgentEscapeTask
				for _, agent := range batch {
					if _, isOnline := onlineAgentMap[agent.AgentID]; isOnline {
						needUpdateAgents = append(needUpdateAgents, agent)
						id, _ := snowflake.Sf.GetId()
						events = append(events, agent_model.AgentEscapeTask{
							Id:          strconv.FormatUint(id, 10),
							TaskDesc:    "平台检测终端已离线",
							ApplianceId: strconv.FormatUint(agent.AgentID, 10),
							TaskStatus:  "success",
							Cmd:         common.OfflineLogType,
							CreateTime:  nowTime,
						})
					}
				}

				// 更新状态
				if len(needUpdateAgents) > 0 {
					if err := tx.Clauses(clause.OnConflict{
						Columns: []clause.Column{{Name: "agent_id"}},
						DoUpdates: clause.AssignmentColumns([]string{
							"current_status",
							"updated_at",
						}),
					}).Create(&needUpdateAgents).Error; err != nil {
						return err
					}

					// 创建离线事件
					if err := tx.CreateInBatches(events, 100).Error; err != nil {
						return err
					}
				}
			}
		}

		return nil
	})
}

func (u *appRepository) AgentLogList(ctx context.Context, req dto.AgentLogsListReq) (model.Pagination, error) {
	var resp []agent_model.AgentEscapeTask
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return req.Pagination, aerrors.NewWithError(err, comm.DBOperateError)
	}
	db = db.Model(agent_model.AgentEscapeTask{}).Where("appliance_id = ? and UPPER(cmd) in ?", req.AgentId, common.QueryList)
	if req.Type != "" {
		db = db.Where("cmd = ? ", req.Type)
	}
	if req.Duration > 0 {
		db = db.Where(fmt.Sprintf("NOW() - create_time < INTERVAL '%d days'", req.Duration))
	}
	db = db.Order("create_time desc")
	return model.Paginate(&resp, &req.Pagination, db)
}

func (u *appRepository) BindAgentUser(ctx context.Context, req dto.AgentBindUserReq) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		lastHb := model.AgentHeartbeat{}
		err := db.Model(model.AgentHeartbeat{}).First(&lastHb, "agent_id=?", req.AgentId).Error
		if err != nil {
			return err
		}
		if lastHb.UserType > common.AgentUserTypeBind {
			return errors.New("客户端已登录用户，不允许修改绑定")
		}

		if req.UserId != "" {
			user := auth_model.TbUserEntity{}
			err = db.Model(auth_model.TbUserEntity{}).First(&user, "id = ?", req.UserId).Error
			if err != nil {
				return err
			}
			req.UserName = user.Name
		}
		agent := model.Agent{}
		err = db.Model(model.Agent{}).First(&agent, "appliance_id =?", req.AgentId).Error
		if err != nil {
			return err
		}
		err = db.Model(agent).Update("bind_userid", req.UserId).Error
		if err != nil {
			return err
		}
		changeType := conf_center.UpdateConf
		//如果没有绑定过用户则创建,否则更新
		if agent.BindUserId == "" && req.UserId != "" {
			changeType = conf_center.AddConf
		}
		if req.UserId == "" {
			changeType = conf_center.DelConf
		}
		err = commonApi.PushCommonAgentConf(
			tx,
			req,
			common.AgentBindUserConfType,
			"bind_user_"+req.AgentId,
			changeType,
			conf_center.NonGlobalConf,
			[]string{req.AgentId}, nil, nil, nil)
		if err != nil {
			return err
		}
		return nil
	})
}

func (u *appRepository) CountAgent(ctx context.Context) (int64, error) {
	db, err := global.GetDBClient(ctx)
	var count int64
	if err != nil {
		return count, err
	}
	err = db.Model(model.Agent{}).Count(&count).Error
	if err != nil {
		return count, err
	}
	return count, nil
}

func (u *appRepository) DeleteAgentsRecord(ctx context.Context, req dto.ReqIds) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, comm.DBOperateError)
	}
	err = db.Transaction(func(tx *gorm.DB) error {
		err = db.Where("appliance_id in ?", req.Ids).Delete(model.Agent{}).Error
		if err != nil {
			return err
		}
		err = db.Where("agent_id in ?", req.Ids).Delete(model.AgentHeartbeat{}).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return aerrors.NewWithError(err, comm.DBOperateError)
	}
	return nil
}

func (u *appRepository) GetGroupNameByID(ctx context.Context, groupId string) (string, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return "", err
	}
	var result string
	err = db.Table("tb_user_group").Select("name").Where("id = ?", groupId).Scan(&result).Error
	return result, err
}

func (u *appRepository) UpdateGrayPolicyPeriod(ctx context.Context, id string, startTime, endTime time.Time) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeGrayPolicy{}).Where("id = ?", id).Select("start_time", "end_time").
		Updates(dto.TbAgentUpgradeGrayPolicy{StartTime: startTime, EndTime: endTime}).Error
}

func (u *appRepository) GetGrayPolicyByUpgradeID(ctx context.Context, policyID string) (dto.TbAgentUpgradeGrayPolicy, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.TbAgentUpgradeGrayPolicy{}, err
	}
	var grayPolicy dto.TbAgentUpgradeGrayPolicy
	err = db.Model(&dto.TbAgentUpgradeGrayPolicy{}).Where("upgrade_policy_id = ?", policyID).First(&grayPolicy).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return dto.TbAgentUpgradeGrayPolicy{}, common.ErrRecordNotFound
		}
		return dto.TbAgentUpgradeGrayPolicy{}, err
	}
	return grayPolicy, nil
}

func (u *appRepository) SetGrayPolicyEndTime(ctx context.Context, id string, endTime time.Time) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeGrayPolicy{}).Where("id = ?", id).Select("end_time").
		Updates(dto.TbAgentUpgradeGrayPolicy{EndTime: endTime}).Error
}

func (u *appRepository) BatchCreateGrayTask(ctx context.Context, tasks []dto.TbAgentUpgradeLog) error {
	if len(tasks) == 0 {
		return nil
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeLog{}).Create(tasks).Error
}

func (u *appRepository) SetTaskFailed(ctx context.Context, id int64, failedReason, failedSource string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeLog{}).Where("id = ?", id).
		Select("status", "failed_reason", "failed_source").
		Updates(dto.TbAgentUpgradeLog{Status: common.UpdateLogFailed, FailedReason: failedReason, FailedSource: failedSource}).Error
}

func (u *appRepository) BatchCleanPendingGrayTask(ctx context.Context, policyID, version string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeLog{}).Where("policy_id = ? and next_version = ? and is_gray = true and status = ?", policyID, version, common.UpdateLogPending).
		Delete(&dto.TbAgentUpgradeLog{}).Error
}

func (u *appRepository) CountAgentUpgradeLogsOfStatus(ctx context.Context, agentID int64, nextVersion, status string) (int64, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, err
	}
	var result int64
	err = db.Model(&dto.TbAgentUpgradeLog{}).Where("agent_id = ? and next_version = ? and status = ?", agentID, nextVersion, status).Count(&result).Error
	return result, err
}

func (u *appRepository) CountFailedAgentUpgradeLogs(ctx context.Context, agentID int64, nextVersion string) (int64, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, err
	}
	var result int64
	err = db.Model(&dto.TbAgentUpgradeLog{}).
		Where("agent_id = ? and next_version = ? and status = ?", agentID, nextVersion, common.UpdateLogFailed).
		Count(&result).Error
	return result, err
}

func (u *appRepository) CountUpgradeLogsOfStatus(ctx context.Context, policyID, nextVersion, status string) (int64, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return 0, err
	}
	var result int64
	err = db.Model(&dto.TbAgentUpgradeLog{}).Where("policy_id = ? and next_version = ? and status = ?", policyID, nextVersion, status).
		Count(&result).Error
	return result, err
}

func (u *appRepository) GetUpgradeLogsOfStatus(ctx context.Context, policyID, nextVersion, status string) ([]dto.TbAgentUpgradeLog, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return []dto.TbAgentUpgradeLog{}, err
	}
	var result []dto.TbAgentUpgradeLog
	err = db.Model(&dto.TbAgentUpgradeLog{}).Where("policy_id = ? and next_version = ? and status = ?", policyID, nextVersion, status).
		Find(&result).Error
	return result, err
}

func (u *appRepository) GetUpgradeLog(ctx context.Context, agentId int64, nextVersion string) (dto.TbAgentUpgradeLog, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.TbAgentUpgradeLog{}, err
	}
	var result dto.TbAgentUpgradeLog
	if err = db.Model(&dto.TbAgentUpgradeLog{}).Where("agent_id = ? and next_version = ?", agentId, nextVersion).
		Order("id desc").First(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return dto.TbAgentUpgradeLog{}, common.ErrRecordNotFound
		}
		return dto.TbAgentUpgradeLog{}, err
	}
	return result, nil
}

func (u *appRepository) GetAgentIDsOfStatus(ctx context.Context, policyID, nextVersion string, status []string) ([]int64, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return []int64{}, err
	}

	var result []int64
	err = db.Model(&dto.TbAgentUpgradeLog{}).Select("agent_id").
		Where("policy_id = ? and next_version = ? and status in ?", policyID, nextVersion, status).
		Distinct("agent_id").Find(&result).Error
	return result, nil
}

func (u *appRepository) CreateUpgradeLog(ctx context.Context, log dto.TbAgentUpgradeLog) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeLog{}).Create(&log).Error
}

func (u *appRepository) SetUpgradeLogStatus(ctx context.Context, id int64, status string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeLog{}).Where("id = ?", id).Select("status").
		Updates(dto.TbAgentUpgradeLog{Status: status}).Error
}

func (u *appRepository) SetAgentsToGray(ctx context.Context, policyID string, agentIds []int64, nextVersion string) error {
	if len(agentIds) == 0 {
		return nil
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	return db.Model(&dto.TbAgentUpgradeLog{}).
		Where("policy_id = ? and next_version = ? and agent_id in ?", policyID, nextVersion, agentIds).
		Select("is_gray").Updates(dto.TbAgentUpgradeLog{IsGray: true}).Error
}

func (u *appRepository) SetPolicyUpgradeTime(ctx context.Context, id string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradePolicy{}).Where("id = ?", id).Select("upgrade_time").
		Updates(&dto.TbAgentUpgradePolicy{UpgradeTime: time.Now().Local()}).Error
}

func (u *appRepository) SetPolicyLatestVersion(ctx context.Context, id, version string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradePolicy{}).Where("id = ?", id).Select("latest_version").
		Updates(&dto.TbAgentUpgradePolicy{LatestVersion: version}).Error
}

func (u *appRepository) GetUpgradePolicyByID(ctx context.Context, id string) (dto.TbAgentUpgradePolicy, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.TbAgentUpgradePolicy{}, err
	}
	var result dto.TbAgentUpgradePolicy
	err = db.Model(&dto.TbAgentUpgradePolicy{}).Where("id = ?", id).First(&result).Error
	return result, err
}

func (u *appRepository) CreateGrayPolicy(ctx context.Context, id string, policy dto.TbAgentUpgradeGrayPolicy) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&dto.TbAgentUpgradePolicy{}).Where("id = ?", id).
			Select("gray_policy_id").Updates(dto.TbAgentUpgradePolicy{GrayPolicyID: policy.ID}).Error; err != nil {
			return err
		}

		if err := tx.Model(&dto.TbAgentUpgradeGrayPolicy{}).Create(&policy).Error; err != nil {
			return err
		}
		return nil
	})

}

func (u *appRepository) UpdateUpgradePolicy(ctx context.Context, policy dto.TbAgentUpgradePolicy) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradePolicy{}).Where("id = ?", policy.ID).
		Select("mode", "concurrency_count", "gray_enable").Updates(&policy).Error
}

func (u *appRepository) UpdateGrayPolicy(ctx context.Context, policy dto.TbAgentUpgradeGrayPolicy) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Model(&dto.TbAgentUpgradeGrayPolicy{}).Where("id = ?", policy.ID).
		Select("mode", "random_count", "group_ids",
			"user_ids", "agent_ids", "duration", "duration_unit", "start_time", "end_time").Updates(&policy).Error
}

func (u *appRepository) GetUpgradePolicy(ctx context.Context, corpId string, platform string) (dto.TbAgentUpgradePolicy, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.TbAgentUpgradePolicy{}, err
	}
	var policy dto.TbAgentUpgradePolicy
	err = db.Model(&dto.TbAgentUpgradePolicy{}).Where("corp_id = ? and platform = ?", corpId, platform).First(&policy).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return dto.TbAgentUpgradePolicy{}, common.ErrRecordNotFound
		}
		return dto.TbAgentUpgradePolicy{}, err
	}
	return policy, nil
}

func (u *appRepository) GetGrayPolicy(ctx context.Context, id string) (dto.TbAgentUpgradeGrayPolicy, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.TbAgentUpgradeGrayPolicy{}, err
	}
	var policy dto.TbAgentUpgradeGrayPolicy
	err = db.Model(&dto.TbAgentUpgradeGrayPolicy{}).Where("id = ?", id).First(&policy).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return dto.TbAgentUpgradeGrayPolicy{}, common.ErrRecordNotFound
		}
		return dto.TbAgentUpgradeGrayPolicy{}, err
	}
	return policy, nil
}

func (u *appRepository) UpdateInstallCMD(ctx context.Context, applianceId uint64, name string, desc string, gatewayIp string, port uint16, gatewayLocalIP string, platformDomain string) error {
	var cmd model.ApplianceInstall
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	err = db.Where("appliance_id= ?", applianceId).Find(&cmd).Error
	if err != nil {
		//不存在则直接返回
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		global.SysLog.Error(err.Error())
		return err
	}
	if cmd.ApplianceID > 0 {
		cmd.Name = name
		cmd.Desc = desc
		cmd.GatewayPort = port
		cmd.GatewayIP = gatewayIp
		cmd.GatewayLocalIP = gatewayLocalIP
		cmd.PlatformDomain = platformDomain

		// 重新生成安装命令
		if err := u.regenerateInstallCommands(ctx, &cmd); err != nil {
			global.SysLog.Error("regenerateInstallCommands failed: " + err.Error())
			return err
		}

		return db.Model(&cmd).Select("name", "desc", "gateway_port", "gateway_ip", "gateway_local_ip", "platform_domain", "command_linux", "command_windows").Updates(&cmd).Error
	}
	return nil
}

func (u *appRepository) UpdateAppliance(ctx context.Context, appliance model.Appliance) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}

	return db.Model(&appliance).Updates(appliance).Error
}

func (u *appRepository) DeviceCount(ctx context.Context) ([]dto.DeviceCount, error) {
	var count []dto.DeviceCount
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return count, err
	}
	err = db.Model(&model.Agent{}).Select("count(appliance_id) as count, app_plat as plat_type").Group("app_plat").Find(&count).Error
	return count, err
}

func (u *appRepository) GetSEAppRelation(ctx context.Context, id uint64) ([]dto.ApplianceAPPRelation, error) {
	var relations []dto.ApplianceAPPRelation

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return relations, err
	}
	rTable := model.SeAppRelation{}.TableName()
	aTable := model.Application{}.TableName()
	err = db.Model(&model.SeAppRelation{}).
		Select(fmt.Sprintf("%s.app_id, %s.se_id, %s.connector_id,%s.app_name", rTable, rTable, rTable, aTable)).
		Joins(fmt.Sprintf("left join %s on %s.app_id = %s.id", aTable, rTable, aTable)).
		Where(rTable+".se_id = ? or "+rTable+".connector_id = ?", id, id).
		Scan(&relations).Error
	return relations, err
}

func (u *appRepository) DeleteById(ctx context.Context, id uint64) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Delete(&model.Appliance{}, id).Error
		if err != nil {
			return err
		}
		err = tx.Delete(&model.ApplianceInstall{}, id).Error
		if err != nil {
			return err
		}
		//心跳表删除的错误忽略
		tx.Delete(&model.ApplianceHeartbeat{}, id)
		return nil
	})
}

// GeAppliances 获取设备列表，如果设备没有安装也返回安装码
func (u *appRepository) GeAppliances(ctx context.Context, req model.Pagination) (model.Pagination, error) {
	var appliances []dto.ApplianceResp

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return req, err
	}
	installT := model.ApplianceInstall{}.TableName()
	applianceT := model.Appliance{}.TableName()
	hbT := model.ApplianceHeartbeat{}.TableName()
	maxHbInterval := "2 min"

	displayTypes := []v1.ApplianceType{v1.ApplianceType_CONNECTOR, v1.ApplianceType_GATEWAY}

	db = db.Model(&model.Appliance{}).
		//组装在线状态
		Select(installT+".command_linux", installT+".command_windows", installT+".Name", installT+".Platform", installT+".type", installT+".gateway_ip", installT+".gateway_port", installT+".gateway_protocol", installT+".gateway_local_ip", installT+".platform_domain",
			applianceT+".*",
			fmt.Sprintf("current_timestamp - to_timestamp(%s.updated_at::TEXT,'yyyy-MM-dd hh24:mi:ss') < interval '%s' as online", hbT, maxHbInterval),
			//如果收到过心跳包，则认为已经安装过
			fmt.Sprintf("CASE WHEN %s.appliance_id IS NULL THEN 0 ELSE 1 END AS installed", hbT),
			//设备ID,如果appliance表则取install表的设备ID
			fmt.Sprintf("CASE WHEN %s.appliance_id IS NULL THEN %s.appliance_id ELSE %s.appliance_id END AS appliance_id", applianceT, installT, applianceT),
			fmt.Sprintf("CASE WHEN %s.app_name IS NULL THEN %s.name ELSE %s.app_name END AS app_name", applianceT, installT, applianceT),
			fmt.Sprintf("CASE WHEN %s.desc IS NULL THEN %s.desc ELSE %s.desc END AS desc", applianceT, installT, applianceT),
			//设备类型，如果appliance表为空，则去install cmd中的type
			fmt.Sprintf("CASE WHEN %s.app_type IS NULL THEN %s.type ELSE %s.app_type END AS display_app_type", applianceT, installT, applianceT),
		).
		//没有安装也要展示安装命令,所以使用full join
		Joins(fmt.Sprintf("full join %s on %s.appliance_id = %s.appliance_id", installT, installT, applianceT)).
		//心跳使用left join
		Joins(fmt.Sprintf("left join %s on %s.appliance_id = %s.appliance_id", hbT, hbT, applianceT)).
		Where(applianceT+".app_type in ? or "+installT+".type in ?", displayTypes, displayTypes)
	return model.Paginate(&appliances, &req, db)
}

// GetInstallCMD 通过SE名称查询SE
func (u *appRepository) GetInstallCMD(ctx context.Context, name string) (model.ApplianceInstall, error) {
	var cmd model.ApplianceInstall
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return cmd, err
	}
	err = db.Where("name= ? and expire_time > ?", name, time.Now()).Find(&cmd).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return cmd, err
	}
	return cmd, nil
}

func (u *appRepository) CreateInstallCMD(ctx context.Context, cmd model.ApplianceInstall) (model.ApplianceInstall, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return cmd, err
	}
	err = db.Create(&cmd).Error
	return cmd, err
}

func (u *appRepository) GetAppListByID(ctx context.Context, ids []string) ([]dto.AgentResp, error) {
	var app []dto.AgentResp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return app, err
	}
	db = db.Model(&model.Agent{})
	err = db.Select("appliance_id,app_name,app_plat").Find(&app, ids).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return app, err
	}
	return app, nil
}

func (u *appRepository) GetById(ctx context.Context, id uint64) (model.Appliance, error) {
	var app model.Appliance
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return model.Appliance{}, err
	}
	err = db.Find(&app, id).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return model.Appliance{}, err
	}
	return app, nil
}

func (u *appRepository) GetAgents(ctx context.Context, pagination model.Pagination, req dto.GetAgentListReq) (model.Pagination, error) {
	var app []dto.AgentResp
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return pagination, err
	}
	db = db.Model(&model.Agent{})
	agentTable := model.Agent{}.TableName()
	hbTable := model.AgentHeartbeat{}.TableName()
	userTable := auth_model.TableNameTbUserEntity
	maxHbInterval := "10 min"
	if pagination.Search != "" {
		pagination.SearchColumns = []string{"app_name", "app_plat", "TEXT(mac_info)", "platform",
			"TEXT(app_ips)", "app_version", fmt.Sprintf("%s.name", userTable), fmt.Sprintf("%s.user_name", hbTable),
			fmt.Sprintf("%s.display_name", userTable), fmt.Sprintf("%s.version", hbTable)}
	}
	status := req.Status
	if status != 0 {
		statusCondition := "<"
		if status > 1 {
			statusCondition = ">="
		}
		db = db.Where(fmt.Sprintf("current_timestamp - to_timestamp(%s.updated_at::TEXT,'yyyy-MM-dd hh24:mi:ss') %s interval '%s'", hbTable, statusCondition, maxHbInterval))
	}
	if req.AgentName != "" {
		agentName := "%" + dbutil.EscapeForLike(req.AgentName) + "%"
		db = db.Where("app_name ilike ?", agentName)
	}

	if req.IP != "" {
		IP := "%" + dbutil.EscapeForLike(req.IP) + "%"
		db = db.Where("array_to_string(app_ips,' ') ilike ?", IP)
	}
	if req.Mac != "" {
		Mac := "%" + dbutil.EscapeForLike(req.Mac) + "%"
		db = db.Where("array_to_string(mac_info,' ') ilike ?", Mac)
	}
	db = db.Select(
		fmt.Sprintf(
			"%s.*, "+ // 选取基础字段
				"current_timestamp - to_timestamp(%s.updated_at::TEXT, 'yyyy-MM-dd hh24:mi:ss') < interval '%s' as online, "+ // 判断是否在线，处理 updated_at
				"COALESCE(NULLIF(%s.user_id, ''), %s.bind_userid) as user_id, "+ // user_id 的逻辑
				"COALESCE(NULLIF(%s.user_name, ''), %s.name) as user_name, "+ // user_name 的逻辑
				"%s.user_type, %s.bind_userid as bind_userid, "+ // user_type 和 bind_userid
				"platform as os_info, "+ // os 信息
				"COALESCE(%s.updated_at, %s.update_time) as updated_at, "+ // updated_at 的处理
				"%s.version as version,"+ // 版本信息
				"  CASE WHEN tb_user_group.path = '/' THEN tb_user_group.path || tb_user_group.name  ELSE  tb_user_group.path || '/' || tb_user_group.name END AS path,"+
				"replace(%s.upgrade_time, '.', '-') as upgrade_time, "+ // 升级时间格式化
				"%s.name as bind_username, %s.display_name as display_name", // 用户绑定信息
			agentTable,             // %s.*, 表名
			hbTable, maxHbInterval, // 判断是否在线的时间差逻辑
			hbTable, agentTable, // user_id 的逻辑
			hbTable, userTable, // user_name 的逻辑
			hbTable, agentTable, // user_type 和 bind_userid
			hbTable, agentTable, // COALESCE 处理 updated_at
			hbTable,              // version 字段
			hbTable,              // upgrade_time 格式化
			userTable, userTable, // bind_username 和 display_name
		)).
		Joins(fmt.Sprintf(
			"left join %s on %s.appliance_id = %s.agent_id", // Heartbeat 表连接
			hbTable, agentTable, hbTable)).
		Joins(fmt.Sprintf(
			"left join %s on COALESCE(NULLIF(%s.user_id, ''), %s.bind_userid) = %s.id", // 用户表连接
			userTable, hbTable, agentTable, userTable)).
		Joins("left join tb_user_group on tb_user_group.id = tb_user_entity.group_id")

	if req.UserName != "" {
		UserName := "%" + dbutil.EscapeForLike(req.UserName) + "%"
		db = db.Where(fmt.Sprintf("%s.name ilike ?", userTable), UserName).
			Or(fmt.Sprintf("%s.display_name ilike ?", userTable), UserName).
			Or(fmt.Sprintf("%s.user_name ilike ?", hbTable), UserName)
	}
	if req.OSType != "" {
		OSType := "%" + dbutil.EscapeForLike(req.OSType) + "%"
		db = db.Where(fmt.Sprintf("%s.platform ilike ?", hbTable), OSType)
	}
	if req.Version != "" {
		version := "%" + dbutil.EscapeForLike(req.Version) + "%"
		db = db.Where(fmt.Sprintf("%s.version ilike ?", hbTable), version)
	}
	if req.OrderBy != "" {
		//validate req.Order,默认使用倒序
		if !strings.EqualFold(req.Order, "ASC") && !strings.EqualFold(req.Order, "DESC") {
			req.Order = "DESC"
		}
		pagination.Sort = fmt.Sprintf("%s %s", req.OrderBy, req.Order)
	}

	return model.Paginate(&app, &pagination, db)
}

func (u *appRepository) GetAgentCount(ctx context.Context, pagination model.Pagination) (dto.AgentCountRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.AgentCountRsp{}, err
	}
	agentTable := model.Agent{}.TableName()
	hbTable := model.AgentHeartbeat{}.TableName()
	userTable := auth_model.TableNameTbUserEntity
	if pagination.Search != "" {
		pagination.SearchColumns = []string{"app_name", "app_plat", "TEXT(mac_info)",
			"TEXT(app_ips)", "app_version", fmt.Sprintf("%s.name", userTable), fmt.Sprintf("%s.display_name", userTable)}
	}
	maxHbInterval := "10 min"
	pagination.Limit = -1
	pagination.Offset = 0
	var res dto.AgentCountRsp
	db = db.Model(model.Agent{}).
		Joins(fmt.Sprintf("left join %s on %s.appliance_id = %s.agent_id", hbTable, agentTable, hbTable)).
		Joins(fmt.Sprintf("left join %s on %s.user_id = %s.id", userTable, hbTable, userTable))
	onlineDb := db
	db = db.Select("COUNT(DISTINCT tb_agent.appliance_id) AS total_appliance_count")
	_, err = model.Paginate(&res, &pagination, db)
	if err != nil {
		return dto.AgentCountRsp{}, err
	}
	onlineDb = onlineDb.Select("COUNT(DISTINCT tb_agent.appliance_id) AS online_appliance_count").
		Where(fmt.Sprintf("current_timestamp - to_timestamp(%s.updated_at::TEXT,'yyyy-MM-dd hh24:mi:ss') < interval '%s'", hbTable, maxHbInterval))
	_, err = model.Paginate(&res, &pagination, db)
	if err != nil {
		return dto.AgentCountRsp{}, err
	}
	res.OfflineApplianceCount = res.TotalApplianceCount - res.OnlineApplianceCount
	return res, nil
}

func (u *appRepository) GetDeployChannel(ctx context.Context) (model.DeployModeInfo, error) {
	result := model.DeployModeInfo{}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&model.DeployModeInfo{}).Where("config_type=?", "deploy").First(&result).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error(err.Error())
		return result, err
	}
	return result, nil
}

func (u *appRepository) GetSystemVersionByName(ctx context.Context, name string) (model.SystemVersion, error) {
	var result model.SystemVersion
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return result, err
	}
	err = db.Model(&result).Select("*").Where("name = ?", name).Find(&result).Error
	return result, err
}

// regenerateInstallCommands 重新生成安装命令
func (u *appRepository) regenerateInstallCommands(_ context.Context, cmd *model.ApplianceInstall) error {
	// 获取平台地址
	address, err := u.getPlatformAddress()
	if err != nil {
		global.SysLog.Error("获取平台地址失败", zap.Error(err))
		address = "127.0.0.1:443"
	}

	// 提取平台IP（不带端口）用于 -p 参数
	platformIP := strings.Split(address, ":")[0]

	// 命令生成的常量，与 service 层保持一致
	const LinuxCMDFormatter = "wget --no-check-certificate \"https://%s/console/v1/gateway/gateway.tar.gz\" && mkdir -p /opt/gateway && " +
		"tar -zxf gateway.tar.gz -C /opt/gateway && cd /opt/gateway --strip-components=1&& chmod +x env.sh && chmod +x %s && ./env.sh -o $(pwd)/asec_env.sh && %s -e asec_env.sh"
	const WindowsCMDFormatter = "& $([scriptblock]::Create((New-Object System.Net.WebClient).DownloadString('%s/console/archives/latest/connector_install_windows.ps1'))) -CODE %d"

	// 根据类型确定脚本和命令
	var script string
	var command string
	if cmd.Type == 2 {
		// connector 类型
		command = fmt.Sprintf("./install-connector.sh -s %s -p %s -c %d", address, address, cmd.ApplianceID)
		script = "install-connector.sh"
	} else {
		// 网关类型，使用平台IP（不带端口）作为 -p 参数
		baseCommand := fmt.Sprintf("./install.sh -m gateway -s run -p %s -c %d", platformIP, cmd.ApplianceID)

		// 对于网关类型，添加 -g 参数（网关本地IP）
		if cmd.GatewayLocalIP != "" {
			baseCommand += fmt.Sprintf(" -g %s", cmd.GatewayLocalIP)
		}

		// 如果有平台域名，添加 -d 参数
		if cmd.PlatformDomain != "" {
			baseCommand += fmt.Sprintf(" -d %s", cmd.PlatformDomain)
		}

		command = baseCommand
		script = "install.sh"
	}

	// 重新生成 Linux 命令
	cmd.CommandLinux = fmt.Sprintf(
		LinuxCMDFormatter,
		address,
		script,
		command,
	)

	// 重新生成 Windows 命令
	cmd.CommandWindows = fmt.Sprintf(WindowsCMDFormatter, address, cmd.ApplianceID)

	return nil
}
func (u *appRepository) QueryAgentDetails(ctx context.Context, agentIds pq.Int64Array) ([]dto.AgentEntity, error) {
	if len(agentIds) == 0 {
		return []dto.AgentEntity{}, nil
	}

	var results []dto.AgentEntity

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return []dto.AgentEntity{}, err
	}

	query := `
		WITH user_paths AS (
			SELECT
				tue.id AS user_id,
				COALESCE(tg.path, '') || '/' || tg.name AS path
			FROM
				` + auth_model.TableNameTbUserEntity + ` AS tue
			LEFT JOIN
				` + auth_model.TableNameTbUserGroup + ` AS tg
			ON
				tue.group_id = tg.id
		),
		agent_paths AS (
			SELECT
				tah.agent_id AS appliance_id,
				COALESCE(tg_hb.path, '') || '/' || tg_hb.name AS path
			FROM
				` + model.AgentHeartbeat{}.TableName() + ` AS tah
			LEFT JOIN
				` + auth_model.TableNameTbUserEntity + ` AS tue_hb
			ON
				tah.user_id = tue_hb.id
			LEFT JOIN
				` + auth_model.TableNameTbUserGroup + ` AS tg_hb
			ON
				tue_hb.group_id = tg_hb.id
		)
		SELECT
			ta.appliance_id::text AS id,
			ta.app_name AS name,
			ta.app_plat AS icon,
			COALESCE(up.path, ap.path, '') AS path,
			'angent' AS type
		FROM
			` + model.Agent{}.TableName() + ` AS ta
		LEFT JOIN
			user_paths AS up
		ON
			ta.bind_userid = up.user_id
		LEFT JOIN
			agent_paths AS ap
		ON
			ta.appliance_id = ap.appliance_id
		WHERE
			ta.appliance_id = ANY($1)`

	err = db.Raw(query, agentIds).Scan(&results).Error
	if err != nil {
		return []dto.AgentEntity{}, err
	}

	return results, nil
}

func (u *appRepository) QueryGroupDetails(ctx context.Context, groupIds []string) ([]dto.GroupEntity, error) {
	if len(groupIds) == 0 {
		return []dto.GroupEntity{}, nil
	}

	var results []dto.GroupEntity

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return []dto.GroupEntity{}, err
	}

	query := `
		SELECT
			tg.id AS id,
			tg.name AS name,
			CASE
        		WHEN tg.path NOT LIKE '/%' THEN '/' || tg.path
        		ELSE tg.path
    		END AS path,
			COALESCE(tus.source_type, '') AS source_type,
			'GROUP' AS type
		FROM
			` + auth_model.TableNameTbUserGroup + ` AS tg
		LEFT JOIN
			` + auth_model.TableNameTbUserSource + ` AS tus
		ON
			tg.source_id = tus.id
		WHERE
			tg.id = ANY($1)`

	err = db.Raw(query, pq.StringArray(groupIds)).Scan(&results).Error
	if err != nil {
		return []dto.GroupEntity{}, err
	}

	return results, nil
}

func (u *appRepository) QueryUserDetails(ctx context.Context, userIds []string) ([]dto.UserEntity, error) {
	if len(userIds) == 0 {
		return []dto.UserEntity{}, nil
	}

	var results []dto.UserEntity

	db, err := global.GetDBClient(ctx)
	if err != nil {
		return []dto.UserEntity{}, err
	}

	query := `
		SELECT
			tue.id AS id,
			tue.display_name AS display_name,
			tue.name AS name,
			CASE
				WHEN tg.path = '/' THEN tg.path || tg.name
				ELSE COALESCE(tg.path, '') || '/' || tg.name
			END AS path,
			'USER' AS type
		FROM
			` + auth_model.TableNameTbUserEntity + ` AS tue
		LEFT JOIN
			` + auth_model.TableNameTbUserGroup + ` AS tg
		ON
			tue.group_id = tg.id
		WHERE
			tue.id = ANY($1)`

	err = db.Raw(query, pq.StringArray(userIds)).Scan(&results).Error
	if err != nil {
		return []dto.UserEntity{}, err
	}

	return results, nil
}

// getPlatformAddress 获取平台地址
func (u *appRepository) getPlatformAddress() (string, error) {
	cfg, err := ini.Load(common.ConfigPath)
	if err != nil {
		return "", fmt.Errorf("failed to load config file: %v", err)
	}

	// 从default section获取plat_ip和user_port
	section := cfg.Section("default")
	platIP := section.Key("plat_ip").String()
	userPort := section.Key("user_port").String()

	if platIP == "" || userPort == "" {
		return "", fmt.Errorf("plat_ip or user_port not found in config")
	}

	// 组合成ip:port格式
	return fmt.Sprintf("%s:%s", platIP, userPort), nil
}

// GetEnabledGrayPolicies 获取所有启用灰度的升级策略
func (u *appRepository) GetEnabledGrayPolicies(ctx context.Context) ([]dto.TbAgentUpgradePolicy, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	var policies []dto.TbAgentUpgradePolicy
	err = db.Model(&dto.TbAgentUpgradePolicy{}).
		Where("gray_enable = ? AND gray_policy_id != ''", true).
		Find(&policies).Error
	if err != nil {
		return nil, err
	}

	return policies, nil
}

// GetGrayPolicyByID 根据ID获取灰度策略
func (u *appRepository) GetGrayPolicyByID(ctx context.Context, id string) (dto.TbAgentUpgradeGrayPolicy, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.TbAgentUpgradeGrayPolicy{}, err
	}

	var grayPolicy dto.TbAgentUpgradeGrayPolicy
	err = db.Model(&dto.TbAgentUpgradeGrayPolicy{}).
		Where("id = ?", id).
		First(&grayPolicy).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return dto.TbAgentUpgradeGrayPolicy{}, common.ErrRecordNotFound
		}
		return dto.TbAgentUpgradeGrayPolicy{}, err
	}

	return grayPolicy, nil
}

// GetGrayTasksByPolicyID 根据策略ID获取灰度任务列表
func (u *appRepository) GetGrayTasksByPolicyID(ctx context.Context, policyID string) ([]dto.TbAgentUpgradeLog, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return nil, err
	}

	var tasks []dto.TbAgentUpgradeLog
	err = db.Model(&dto.TbAgentUpgradeLog{}).
		Where("policy_id = ? AND status IN (?)", policyID, []string{common.UpdateLogPending, common.UpdateLogProcessing}).
		Find(&tasks).Error
	if err != nil {
		return nil, err
	}

	return tasks, nil
}
