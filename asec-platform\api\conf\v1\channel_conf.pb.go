// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/channel_conf.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FieldName int32

const (
	// 文件路径
	FieldName_File_Path FieldName = 0
	// 文件名
	FieldName_File_Name FieldName = 1
	// 文件扩展名
	FieldName_File_Extension FieldName = 2
	// 进程名
	FieldName_Process_Name FieldName = 3
	// 全文件路径
	FieldName_Full_File_Path FieldName = 4
)

// Enum value maps for FieldName.
var (
	FieldName_name = map[int32]string{
		0: "File_Path",
		1: "File_Name",
		2: "File_Extension",
		3: "Process_Name",
		4: "Full_File_Path",
	}
	FieldName_value = map[string]int32{
		"File_Path":      0,
		"File_Name":      1,
		"File_Extension": 2,
		"Process_Name":   3,
		"Full_File_Path": 4,
	}
)

func (x FieldName) Enum() *FieldName {
	p := new(FieldName)
	*p = x
	return p
}

func (x FieldName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FieldName) Descriptor() protoreflect.EnumDescriptor {
	return file_conf_v1_channel_conf_proto_enumTypes[0].Descriptor()
}

func (FieldName) Type() protoreflect.EnumType {
	return &file_conf_v1_channel_conf_proto_enumTypes[0]
}

func (x FieldName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FieldName.Descriptor instead.
func (FieldName) EnumDescriptor() ([]byte, []int) {
	return file_conf_v1_channel_conf_proto_rawDescGZIP(), []int{0}
}

type ExpressionSymbol int32

const (
	// 包含
	ExpressionSymbol_Contains ExpressionSymbol = 0
	// 等于
	ExpressionSymbol_Is ExpressionSymbol = 1
	// 不等于
	ExpressionSymbol_Is_Not ExpressionSymbol = 2
	// 正则表达式
	ExpressionSymbol_Regex ExpressionSymbol = 3
	// 通配符
	ExpressionSymbol_Wildcard ExpressionSymbol = 4
	// 以XXX开始
	ExpressionSymbol_Starts_with ExpressionSymbol = 5
)

// Enum value maps for ExpressionSymbol.
var (
	ExpressionSymbol_name = map[int32]string{
		0: "Contains",
		1: "Is",
		2: "Is_Not",
		3: "Regex",
		4: "Wildcard",
		5: "Starts_with",
	}
	ExpressionSymbol_value = map[string]int32{
		"Contains":    0,
		"Is":          1,
		"Is_Not":      2,
		"Regex":       3,
		"Wildcard":    4,
		"Starts_with": 5,
	}
)

func (x ExpressionSymbol) Enum() *ExpressionSymbol {
	p := new(ExpressionSymbol)
	*p = x
	return p
}

func (x ExpressionSymbol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpressionSymbol) Descriptor() protoreflect.EnumDescriptor {
	return file_conf_v1_channel_conf_proto_enumTypes[1].Descriptor()
}

func (ExpressionSymbol) Type() protoreflect.EnumType {
	return &file_conf_v1_channel_conf_proto_enumTypes[1]
}

func (x ExpressionSymbol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExpressionSymbol.Descriptor instead.
func (ExpressionSymbol) EnumDescriptor() ([]byte, []int) {
	return file_conf_v1_channel_conf_proto_rawDescGZIP(), []int{1}
}

// 自定义通道配置
type Channel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 通道名称
	Channel string `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	// 通道类型
	ChannelType string `protobuf:"bytes,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// 应用系统
	Plat string `protobuf:"bytes,3,opt,name=plat,proto3" json:"plat,omitempty"`
	// 进程列表
	ProcessList []string `protobuf:"bytes,4,rep,name=process_list,json=processList,proto3" json:"process_list,omitempty"`
	// 文件路径白名单
	FilePathWhiteList []string `protobuf:"bytes,5,rep,name=file_path_white_list,json=filePathWhiteList,proto3" json:"file_path_white_list,omitempty"`
}

func (x *Channel) Reset() {
	*x = Channel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_channel_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Channel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Channel) ProtoMessage() {}

func (x *Channel) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_channel_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Channel.ProtoReflect.Descriptor instead.
func (*Channel) Descriptor() ([]byte, []int) {
	return file_conf_v1_channel_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Channel) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *Channel) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *Channel) GetPlat() string {
	if x != nil {
		return x.Plat
	}
	return ""
}

func (x *Channel) GetProcessList() []string {
	if x != nil {
		return x.ProcessList
	}
	return nil
}

func (x *Channel) GetFilePathWhiteList() []string {
	if x != nil {
		return x.FilePathWhiteList
	}
	return nil
}

type EventFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// GlobalFilter
	GlobalFilterItems []*FilterItem `protobuf:"bytes,1,rep,name=global_filter_items,json=globalFilterItems,proto3" json:"global_filter_items,omitempty"`
	// ProcessFilter
	ProcessFilterItems []*ProcessFilterItem `protobuf:"bytes,2,rep,name=process_filter_items,json=processFilterItems,proto3" json:"process_filter_items,omitempty"`
	// 开启敏感扫描 1-开启 2-关闭
	ScanContent uint32 `protobuf:"varint,3,opt,name=scan_content,json=scanContent,proto3" json:"scan_content,omitempty"`
}

func (x *EventFilters) Reset() {
	*x = EventFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_channel_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventFilters) ProtoMessage() {}

func (x *EventFilters) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_channel_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventFilters.ProtoReflect.Descriptor instead.
func (*EventFilters) Descriptor() ([]byte, []int) {
	return file_conf_v1_channel_conf_proto_rawDescGZIP(), []int{1}
}

func (x *EventFilters) GetGlobalFilterItems() []*FilterItem {
	if x != nil {
		return x.GlobalFilterItems
	}
	return nil
}

func (x *EventFilters) GetProcessFilterItems() []*ProcessFilterItem {
	if x != nil {
		return x.ProcessFilterItems
	}
	return nil
}

func (x *EventFilters) GetScanContent() uint32 {
	if x != nil {
		return x.ScanContent
	}
	return 0
}

type ProcessFilterItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 属性名称
	ProcessName *FilterItem `protobuf:"bytes,1,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	// 操作类型
	Filters []*FilterItem `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *ProcessFilterItem) Reset() {
	*x = ProcessFilterItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_channel_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessFilterItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessFilterItem) ProtoMessage() {}

func (x *ProcessFilterItem) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_channel_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessFilterItem.ProtoReflect.Descriptor instead.
func (*ProcessFilterItem) Descriptor() ([]byte, []int) {
	return file_conf_v1_channel_conf_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessFilterItem) GetProcessName() *FilterItem {
	if x != nil {
		return x.ProcessName
	}
	return nil
}

func (x *ProcessFilterItem) GetFilters() []*FilterItem {
	if x != nil {
		return x.Filters
	}
	return nil
}

type FilterItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 属性名称
	FieldName string `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	// 操作类型
	ExpressionSymbol string `protobuf:"bytes,2,opt,name=expression_symbol,json=expressionSymbol,proto3" json:"expression_symbol,omitempty"`
	// 值
	ExpressionValue string `protobuf:"bytes,3,opt,name=expression_value,json=expressionValue,proto3" json:"expression_value,omitempty"`
	// 匹配方式
	Action string `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *FilterItem) Reset() {
	*x = FilterItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_channel_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterItem) ProtoMessage() {}

func (x *FilterItem) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_channel_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterItem.ProtoReflect.Descriptor instead.
func (*FilterItem) Descriptor() ([]byte, []int) {
	return file_conf_v1_channel_conf_proto_rawDescGZIP(), []int{3}
}

func (x *FilterItem) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *FilterItem) GetExpressionSymbol() string {
	if x != nil {
		return x.ExpressionSymbol
	}
	return ""
}

func (x *FilterItem) GetExpressionValue() string {
	if x != nil {
		return x.ExpressionValue
	}
	return ""
}

func (x *FilterItem) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

var File_conf_v1_channel_conf_proto protoreflect.FileDescriptor

var file_conf_v1_channel_conf_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0xae, 0x01, 0x0a, 0x07, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x6c, 0x61, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x57, 0x68,
	0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc6, 0x01, 0x0a, 0x0c, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x44, 0x0a, 0x13, 0x67, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x11, 0x67, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x4d,
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x22, 0x7c, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x37, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x9b,
	0x01, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a,
	0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11,
	0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x63, 0x0a, 0x09,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x69, 0x6c,
	0x65, 0x5f, 0x50, 0x61, 0x74, 0x68, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x69, 0x6c, 0x65,
	0x5f, 0x4e, 0x61, 0x6d, 0x65, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x5f,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x4e, 0x61, 0x6d, 0x65, 0x10, 0x03, 0x12, 0x12, 0x0a,
	0x0e, 0x46, 0x75, 0x6c, 0x6c, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x5f, 0x50, 0x61, 0x74, 0x68, 0x10,
	0x04, 0x2a, 0x5e, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x73, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x73, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x49,
	0x73, 0x5f, 0x4e, 0x6f, 0x74, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x65, 0x67, 0x65, 0x78,
	0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x69, 0x6c, 0x64, 0x63, 0x61, 0x72, 0x64, 0x10, 0x04,
	0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x10,
	0x05, 0x42, 0x29, 0x5a, 0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_channel_conf_proto_rawDescOnce sync.Once
	file_conf_v1_channel_conf_proto_rawDescData = file_conf_v1_channel_conf_proto_rawDesc
)

func file_conf_v1_channel_conf_proto_rawDescGZIP() []byte {
	file_conf_v1_channel_conf_proto_rawDescOnce.Do(func() {
		file_conf_v1_channel_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_channel_conf_proto_rawDescData)
	})
	return file_conf_v1_channel_conf_proto_rawDescData
}

var file_conf_v1_channel_conf_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_conf_v1_channel_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_conf_v1_channel_conf_proto_goTypes = []interface{}{
	(FieldName)(0),            // 0: api.conf.FieldName
	(ExpressionSymbol)(0),     // 1: api.conf.ExpressionSymbol
	(*Channel)(nil),           // 2: api.conf.Channel
	(*EventFilters)(nil),      // 3: api.conf.EventFilters
	(*ProcessFilterItem)(nil), // 4: api.conf.ProcessFilterItem
	(*FilterItem)(nil),        // 5: api.conf.FilterItem
}
var file_conf_v1_channel_conf_proto_depIdxs = []int32{
	5, // 0: api.conf.EventFilters.global_filter_items:type_name -> api.conf.FilterItem
	4, // 1: api.conf.EventFilters.process_filter_items:type_name -> api.conf.ProcessFilterItem
	5, // 2: api.conf.ProcessFilterItem.process_name:type_name -> api.conf.FilterItem
	5, // 3: api.conf.ProcessFilterItem.filters:type_name -> api.conf.FilterItem
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_conf_v1_channel_conf_proto_init() }
func file_conf_v1_channel_conf_proto_init() {
	if File_conf_v1_channel_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_channel_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Channel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_channel_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_channel_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessFilterItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_channel_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_channel_conf_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_channel_conf_proto_goTypes,
		DependencyIndexes: file_conf_v1_channel_conf_proto_depIdxs,
		EnumInfos:         file_conf_v1_channel_conf_proto_enumTypes,
		MessageInfos:      file_conf_v1_channel_conf_proto_msgTypes,
	}.Build()
	File_conf_v1_channel_conf_proto = out.File
	file_conf_v1_channel_conf_proto_rawDesc = nil
	file_conf_v1_channel_conf_proto_goTypes = nil
	file_conf_v1_channel_conf_proto_depIdxs = nil
}
