package dto

// CreateWechatVerifyRequest 创建企业微信验证文件请求
type CreateWechatVerifyRequest struct {
	FileName    string `json:"fileName" binding:"required" example:"WW_verify_b9To9JT945BLlfF5.txt"`
	Content     string `json:"content" binding:"required" example:"file content"`
	Description string `json:"description" example:"企业微信域名验证文件"`
}

// UpdateWechatVerifyRequest 更新企业微信验证文件请求
type UpdateWechatVerifyRequest struct {
	Content     string `json:"content" binding:"required"`
	Description string `json:"description"`
	IsEnabled   *bool  `json:"isEnabled"` // 使用指针类型以支持明确的true/false设置
}

// WechatVerifyResponse 企业微信验证文件响应
type WechatVerifyResponse struct {
	ID          uint   `json:"id"`
	FileName    string `json:"fileName"`
	Content     string `json:"content"`
	IsEnabled   bool   `json:"isEnabled"`
	Description string `json:"description"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// WechatVerifyListResponse 企业微信验证文件列表响应
type WechatVerifyListResponse struct {
	Total int64                  `json:"total"`
	List  []WechatVerifyResponse `json:"list"`
}
