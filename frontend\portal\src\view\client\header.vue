<template>
  <div class="layout-header">
    <div class="header-logo">
      <!--如果图片加载失败就隐藏-->
      <img
        v-prevent-drag
        src="@/assets/logo.png"
        alt=""
        draggable="false"
        onload="this.style.display = 'block'"
        onerror="this.style.display = 'none'"
      >
    </div>
    <div id="u-electron-drag" />
    <ul id="u-header-menu" class="right-wrapper">
      <li id="u-avator" ref="countMenu">
        <div id="ui-headNav-header-div-account_info" v-click-outside="closeDropdown" class="base-dropdown" @click="handleUserInfoClick">
          <div class="user-info" :class="{ 'not-logged-in': !isLoggedIn }">
            <div class="user-face">
              <img
                v-prevent-drag
                :src="userAvatar"
                alt=""
                draggable="false"
                onload="this.style.display = 'block'"
                onerror="this.style.display = 'none'"
              >
            </div>
            <span class="user-name">{{ displayUsername }}</span>
          </div>
          <div v-show="dropdownVisible && isLoggedIn" class="dropdown-menu header-count-menu">
            <div id="ui-headNav-header-li-cancel_account" class="dropdown-item" @click="userMenuHandle('lougOut')">
              <base-icon class="dropdown-item-icon" name="logout" /><span class="dropdown-item-text">注销登录</span>
            </div>
          </div>
        </div>
      </li>
      <div class="user-divider" />
      <base-icon class="window-operate" name="minus" @click="minimizeWnd" />
      <base-icon class="window-operate" :name="isMaxWindow ? 'fullscreen_exit' : 'fullscreen'" @click="maximizeWndOrNot" />
      <base-icon class="window-operate" name="close" style="margin-right: 16px;" @click="closeWnd" />
    </ul>
  </div>
</template>
<script>
import agentApi from '@/api/agentApi'
import { useUserStore } from '@/pinia/modules/user'
import { useRouter } from 'vue-router'
import { Message, MessageBox, Loading } from '@/components/base'
import defaultAvatar from '@/assets/default_avator.png'
import userAvatar from '@/assets/avator.png'

export default {
  name: 'ClientHeader',
  setup() {
    const userStore = useUserStore()
    const router = useRouter()

    return {
      userStore,
      router
    }
  },
  data() {
    return {
      countCommand: 'changePassword',
      isMaxWindow: false, // 当前窗口是最最大化还是正常形态
      dropdownVisible: false
    }
  },
  computed: {
    // 是否已登录
    isLoggedIn() {
      return !!this.userStore.token
    },

    // 用户头像
    userAvatar() {
      if (this.isLoggedIn) {
        return userAvatar
      } else {
        return defaultAvatar
      }
    },

    // 显示的用户名
    displayUsername() {
      if (this.isLoggedIn) {
        const userInfo = this.userStore.userInfo
        return userInfo.displayName ? userInfo.displayName : userInfo.name
      } else {
        return '未登录'
      }
    },
  },
  watch: {
    userId(newVal, oldVal) {
      logger.log('用户id变动', newVal, oldVal)
      console.debug('用户id变动')
    }
  },
  mounted() {
    // this.addEventBus()

    // 获取内外网配置
    // this.getSwNetworkData()
    // this.autochangeMode()
    // this.getCountMenuWidth() // 小助手初始化时网络变动header还没渲染监听不到，这里补掉，bugID=16790

    // 监听客户端主动调用的退出登录事件
    this.setupClientLogoutListener()
  },
  beforeDestroy() {
    // EventBus.$off('openPassword')

    // 移除客户端退出登录事件监听器
    this.removeClientLogoutListener()
  },
  methods: {
    // 最小化
    minimizeWnd() {
      agentApi.minimizeWnd()
    },
    maximizeWndOrNot() {
      if (this.isMaxWindow) {
        agentApi.normalnizeWnd()
        this.isMaxWindow = false
      } else {
        agentApi.maximizeWnd()
        this.isMaxWindow = true
      }
    },
    // 处理用户信息点击
    handleUserInfoClick() {
      // 只有在已登录状态下才允许切换下拉菜单
      if (this.isLoggedIn) {
        this.toggleDropdown()
      }
    },
    // 切换下拉菜单显示状态
    toggleDropdown() {
      this.dropdownVisible = !this.dropdownVisible
    },
    // 关闭下拉菜单
    closeDropdown() {
      this.dropdownVisible = false
    },
    dropdownVisiHandle() {

    },
    async closeWnd() {
      agentApi.hideWend()
    },
    userMenuHandle(command) {
      // 关闭下拉菜单
      this.closeDropdown()

      this.countCommand = command
      switch (command) {
        case 'lougOut':
          this.handleLogoutConfirm()
          break
      }
    },
    // 处理注销确认
    async handleLogoutConfirm() {
      try {
        await MessageBox.confirm(
          '确认注销登录吗？',
          '提示',
          {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        )

        // 用户确认后执行注销
        await this.performLogout()
      } catch (action) {
        // 用户取消了操作
        logger.log('用户取消注销操作')
      }
    },

    // 执行注销
    async performLogout() {
      logger.log('开始注销，显示Loading')
      // 显示全屏loading
      const loadingInstance = Loading.service({
        fullscreen: true,
        text: '正在注销登录...'
      })

      try {
        logger.log('开始注销登录...')

        // 断开连接
        logger.log('正在断开隧道连接...')
        agentApi.disconnectTunnel()
        this.userStore.setTunState(0)
        logger.log('断开连接API调用成功，开始轮询状态...')

        // 轮询检查断开状态，超时10秒
        /*
        const disconnectSuccess = await this.waitForDisconnect()

        if (!disconnectSuccess) {
          console.error('断开连接超时，注销失败')
          Message.error('断开连接超时，注销失败')
          // 关闭loading后再返回
          loadingInstance.close()
          return
        }
        */

        logger.log('隧道连接断开完成，继续注销流程')

        // 客户端退出登录
        agentApi.setLoginStatus({
          Token: ''
        })

        // 调用用户store的登出方法
        try {
          await this.userStore.ClearStorage()
          this.userStore.LoginOut()
          logger.log('服务器注销登录成功')
        } catch (error) {
          console.error('服务器注销登录失败:', error)
        }

        // 跳转到客户端登录页面
        this.router.push({
          name: 'ClientNewLogin',
          query: agentApi.getClientParams()
        })
      } catch (error) {
        console.error('注销登录失败:', error)
        Message.error('注销失败，请重试')
      } finally {
        // 关闭loading
        loadingInstance.close()
      }
    },

    // 等待断开连接完成
    async waitForDisconnect() {
      return new Promise((resolve) => {
        let pollCount = 0
        const maxPolls = 10 // 10秒超时

        const pollTimer = setInterval(async() => {
          try {
            pollCount++
            logger.log(`断开连接状态轮询第${pollCount}次...`)

            const response = await agentApi.getChannelStatus()
            logger.log('轮询状态响应:', response)

            if (response && response.TunState === 102) {
              // 断开成功
              logger.log('断开连接成功！')
              clearInterval(pollTimer)
              resolve(true)
              return
            }

            // 检查是否超时
            if (pollCount >= maxPolls) {
              logger.log('断开连接超时')
              clearInterval(pollTimer)
              resolve(false)
            }
          } catch (error) {
            console.error('轮询状态失败:', error)
            pollCount++

            if (pollCount >= maxPolls) {
              clearInterval(pollTimer)
              resolve(false)
            }
          }
        }, 1000) // 每1秒轮询一次
      })
    },
    /* 获取顶部头像昵称总长度*/
    async getCountMenuWidth() {
      const addWidth = this.isZtpUser ? 44 : 0
      const width = parseInt(document.getElementById('u-avator') ? document.getElementById('u-avator').offsetWidth : 0)
      try {
        await agentApi.init()
        await agentApi.ipcClient.$ipcSend('UIPlatform_Window', 'SetTitleDimension', { nHeight: 50, nNameWidth: parseFloat(width) + addWidth })
      } catch (error) {
        console.warn('设置标题尺寸失败:', error)
      }
    },
    hdEventHandle(data) {
      switch (data.type) {
        case 'router' :
          this.userMenuHandle(data.val)
          break
      }
    },

    // 设置客户端退出登录事件监听器
    setupClientLogoutListener() {
      if (typeof window !== 'undefined') {
        this.clientLogoutHandler = async(event) => {
          logger.log('收到客户端退出登录事件:', event.detail)

          // 检查当前是否已登录
          if (!this.isLoggedIn) {
            logger.log('用户未登录，跳过退出登录处理')
            return
          }

          logger.log('客户端已登录，开始执行退出登录流程')

          try {
            // 直接调用现有的 performLogout 方法
            await this.performLogout()
          } catch (error) {
            logger.log('处理客户端退出登录事件失败:', error)
          }
        }

        window.addEventListener('clientLogoutReceived', this.clientLogoutHandler)
        logger.log('已注册客户端退出登录事件监听器')
      }
    },

    // 移除客户端退出登录事件监听器
    removeClientLogoutListener() {
      if (typeof window !== 'undefined' && this.clientLogoutHandler) {
        window.removeEventListener('clientLogoutReceived', this.clientLogoutHandler)
        this.clientLogoutHandler = null
        logger.log('已移除客户端退出登录事件监听器')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.layout-header {
  height: 42px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(315deg, $--color-primary, $--color-primary-light-1);
  box-shadow: 0px 2px 6px 0px rgba(46, 60, 128, 0.2);
  color: $light-color;
  .header-title {
    line-height: 42px;
    font-size: 18px;
    font-weight: 500;
  }
  .header-logo{
    margin-left: 16px;
    height: 42px;
    display: flex;
    align-items: center;
    img{
      max-width: 79px;
      max-height: 28px;
    }
  }
  #u-electron-drag{
    display: flex;
    flex: 1;
    height: 100%;
    -webkit-app-region: drag;
  }
  .right-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    & > li:hover {
      background: $--color-primary-dark-2;
    }
    .user-divider {
      width: 1px;
      height: 14px;
      margin-left: 16px;
      margin-right: 16px;
      background: #e6e6e6;
    }
    .base-dropdown {
      position: relative;
      display: inline-block;
    }

    .user-info {
      display: flex;
      align-items: center;
      height: 42px;
      padding: 0 14px;
      cursor: pointer;

      // 未登录状态的样式
      &.not-logged-in {
        cursor: default;
        opacity: 0.8;

        &:hover {
          background: none !important;
        }
      }
      .user-face {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 6px;
        img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
      .user-name {
        color: $light-color;
        display: inline-block;
        max-width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      z-index: 1000;
      width: 114px;
      background: #ffffff;
      border-radius: 4px;
      box-shadow: 0px 2px 20px 0px rgba(46,60,128,0.10);

      .dropdown-item {
        display: flex;
        padding-left: 40px;
        position: relative;
        align-items: center;
        justify-content: flex-start;
        width: 98px;
        height: 28px;
        margin: 8px;
        border-radius: 4px;
        font-size: 14px;
        color: #282a33;
        cursor: pointer;
        font-family: PingFang SC, PingFang SC-Regular, "Microsoft YaHei", "微软雅黑";
        font-weight: Regular;

        .dropdown-item-icon {
          position: absolute;
          transform: rotate(180deg);
          font-size: 14px;
          display: flex;
          align-items: center;
          width: 14px;
          height: 14px;
          top: 6px;
          left: 10px;
        }

        .dropdown-item-text {
          width: 56px;
          height: 20px;
          position: absolute;
          right: 8px;
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Regular, "Microsoft YaHei", "微软雅黑";
          font-weight: Regular !important;
          text-align: left;
          line-height: 20px;
        }

        &:hover {
          background-color: #ff4d4d;
          color: #ffffff;
        }

        &:active {
          background-color: #e6e8eb;
        }
      }
    }
    .set-icon-wrapper, .menu-msg {
      width: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      height: 42px;
      position: relative;
      .icon-shezhi {
        color: $icon-color;
        font-size: 18px;
      }
    }
    .window-operate {
      width: 24px;
      height: 24px;
      margin-left: 4px;
      padding-top: 4px;
      filter: brightness(1.5);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        background: $--color-primary-dark-2;;
        border-radius: 4px;
      }
      svg {
        width: 12px;
        height: 12px;
      }
    }
  }
}

.count-title {
  color: $default-color;
  i {
    font-style: normal;
    color: $title-color;
  }
}

.s-title{
  margin-top: 18px;
  margin-left: 18px;
  font-size: 13px;
  line-height: 18px;
  font-weight: 500;
  color: $title-color;
}
.s-content{
  padding: 24px 32px 29px 32px;
  font-size: 13px;
  line-height: 18px;
  .s-text{
    color: $default-color
  }
}
.change-reg-info{
  padding-left: 8px;
  line-height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: $title-color;
}
</style>
<style lang="scss">
body .el-dialog-ip-box {
  width: 260px;
  .el-message-box__content {
    padding: 20px 15px;
  }
}
.s-content .el-radio{
  margin-right: 13px;
  .el-radio__label{
    padding-left: 8px;
    font-size: 13px;
    color: $title-color;
    line-height: 18px;
  }
}
#ip-info-dialog{
  .ip-content{
    margin-top: 24px;
    margin-bottom: 24px;
    padding: 0 24px;
    line-height: 20px;
    font-size: 14px;
    color: $title-color;
  }
  .netcard-list{
    margin-top: 16px;
    padding: 0 24px;
    li{
      display: flex;
      align-items: center;
      line-height: 20px;
      font-size: 14px;
      color: $title-color;
      margin-bottom: 10px;
      &:last-child{
        margin-bottom: 24px;
      }
      i{
        font-size: 16px;
        margin-left: 16px;
      }
      .icon-lianjie{
        color: $success;
      }
      .icon-duankailianjie{
        color: $error;
      }
    }
  }
  .el-dialog__footer button{
    height: 40px;
    line-height: 40px;
    border-bottom-right-radius: 4px;
  }
}

.loginout-m-confirm-dialog{
    .v-header{
        line-height: 45px;
        border-bottom: 1px solid $line-color;
        padding: 0 24px;
        font-size: 16px;
        color: $title-color;
        i{
            font-size: 16px;
            color: $yellow-1;
            margin-right: 6px;
            font-weight: 400;
        }
    }
    .outline-tips{
        padding: 24px;
        line-height: 20px;
        color: $title-color;
        font-size: 14px;
    }
}
</style>
