// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"gorm.io/plugin/dbresolver"
)

func newTbIdpTemplateAttribute(db *gorm.DB, opts ...gen.DOOption) tbIdpTemplateAttribute {
	_tbIdpTemplateAttribute := tbIdpTemplateAttribute{}

	_tbIdpTemplateAttribute.tbIdpTemplateAttributeDo.UseDB(db, opts...)
	_tbIdpTemplateAttribute.tbIdpTemplateAttributeDo.UseModel(&model.TbIdpTemplateAttribute{})

	tableName := _tbIdpTemplateAttribute.tbIdpTemplateAttributeDo.TableName()
	_tbIdpTemplateAttribute.ALL = field.NewAsterisk(tableName)
	_tbIdpTemplateAttribute.Key = field.NewString(tableName, "key")
	_tbIdpTemplateAttribute.Value = field.NewString(tableName, "value")
	_tbIdpTemplateAttribute.ProviderID = field.NewString(tableName, "provider_id")
	_tbIdpTemplateAttribute.CreatedAt = field.NewTime(tableName, "created_at")
	_tbIdpTemplateAttribute.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbIdpTemplateAttribute.fillFieldMap()

	return _tbIdpTemplateAttribute
}

type tbIdpTemplateAttribute struct {
	tbIdpTemplateAttributeDo tbIdpTemplateAttributeDo

	ALL        field.Asterisk
	Key        field.String
	Value      field.String
	ProviderID field.String
	CreatedAt  field.Time
	UpdatedAt  field.Time

	fieldMap map[string]field.Expr
}

func (t tbIdpTemplateAttribute) Table(newTableName string) *tbIdpTemplateAttribute {
	t.tbIdpTemplateAttributeDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbIdpTemplateAttribute) As(alias string) *tbIdpTemplateAttribute {
	t.tbIdpTemplateAttributeDo.DO = *(t.tbIdpTemplateAttributeDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbIdpTemplateAttribute) updateTableName(table string) *tbIdpTemplateAttribute {
	t.ALL = field.NewAsterisk(table)
	t.Key = field.NewString(table, "key")
	t.Value = field.NewString(table, "value")
	t.ProviderID = field.NewString(table, "provider_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbIdpTemplateAttribute) WithContext(ctx context.Context) *tbIdpTemplateAttributeDo {
	return t.tbIdpTemplateAttributeDo.WithContext(ctx)
}

func (t tbIdpTemplateAttribute) TableName() string { return t.tbIdpTemplateAttributeDo.TableName() }

func (t tbIdpTemplateAttribute) Alias() string { return t.tbIdpTemplateAttributeDo.Alias() }

func (t tbIdpTemplateAttribute) Columns(cols ...field.Expr) gen.Columns {
	return t.tbIdpTemplateAttributeDo.Columns(cols...)
}

func (t *tbIdpTemplateAttribute) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbIdpTemplateAttribute) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["key"] = t.Key
	t.fieldMap["value"] = t.Value
	t.fieldMap["provider_id"] = t.ProviderID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbIdpTemplateAttribute) clone(db *gorm.DB) tbIdpTemplateAttribute {
	t.tbIdpTemplateAttributeDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbIdpTemplateAttribute) replaceDB(db *gorm.DB) tbIdpTemplateAttribute {
	t.tbIdpTemplateAttributeDo.ReplaceDB(db)
	return t
}

type tbIdpTemplateAttributeDo struct{ gen.DO }

func (t tbIdpTemplateAttributeDo) Debug() *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Debug())
}

func (t tbIdpTemplateAttributeDo) WithContext(ctx context.Context) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbIdpTemplateAttributeDo) ReadDB() *tbIdpTemplateAttributeDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbIdpTemplateAttributeDo) WriteDB() *tbIdpTemplateAttributeDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbIdpTemplateAttributeDo) Session(config *gorm.Session) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbIdpTemplateAttributeDo) Clauses(conds ...clause.Expression) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbIdpTemplateAttributeDo) Returning(value interface{}, columns ...string) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbIdpTemplateAttributeDo) Not(conds ...gen.Condition) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbIdpTemplateAttributeDo) Or(conds ...gen.Condition) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbIdpTemplateAttributeDo) Select(conds ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbIdpTemplateAttributeDo) Where(conds ...gen.Condition) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbIdpTemplateAttributeDo) Order(conds ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbIdpTemplateAttributeDo) Distinct(cols ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbIdpTemplateAttributeDo) Omit(cols ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbIdpTemplateAttributeDo) Join(table schema.Tabler, on ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbIdpTemplateAttributeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbIdpTemplateAttributeDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbIdpTemplateAttributeDo) Group(cols ...field.Expr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbIdpTemplateAttributeDo) Having(conds ...gen.Condition) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbIdpTemplateAttributeDo) Limit(limit int) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbIdpTemplateAttributeDo) Offset(offset int) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbIdpTemplateAttributeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbIdpTemplateAttributeDo) Unscoped() *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbIdpTemplateAttributeDo) Create(values ...*model.TbIdpTemplateAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbIdpTemplateAttributeDo) CreateInBatches(values []*model.TbIdpTemplateAttribute, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbIdpTemplateAttributeDo) Save(values ...*model.TbIdpTemplateAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbIdpTemplateAttributeDo) First() (*model.TbIdpTemplateAttribute, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpTemplateAttribute), nil
	}
}

func (t tbIdpTemplateAttributeDo) Take() (*model.TbIdpTemplateAttribute, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpTemplateAttribute), nil
	}
}

func (t tbIdpTemplateAttributeDo) Last() (*model.TbIdpTemplateAttribute, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpTemplateAttribute), nil
	}
}

func (t tbIdpTemplateAttributeDo) Find() ([]*model.TbIdpTemplateAttribute, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbIdpTemplateAttribute), err
}

func (t tbIdpTemplateAttributeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbIdpTemplateAttribute, err error) {
	buf := make([]*model.TbIdpTemplateAttribute, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbIdpTemplateAttributeDo) FindInBatches(result *[]*model.TbIdpTemplateAttribute, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbIdpTemplateAttributeDo) Attrs(attrs ...field.AssignExpr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbIdpTemplateAttributeDo) Assign(attrs ...field.AssignExpr) *tbIdpTemplateAttributeDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbIdpTemplateAttributeDo) Joins(fields ...field.RelationField) *tbIdpTemplateAttributeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbIdpTemplateAttributeDo) Preload(fields ...field.RelationField) *tbIdpTemplateAttributeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbIdpTemplateAttributeDo) FirstOrInit() (*model.TbIdpTemplateAttribute, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpTemplateAttribute), nil
	}
}

func (t tbIdpTemplateAttributeDo) FirstOrCreate() (*model.TbIdpTemplateAttribute, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdpTemplateAttribute), nil
	}
}

func (t tbIdpTemplateAttributeDo) FindByPage(offset int, limit int) (result []*model.TbIdpTemplateAttribute, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbIdpTemplateAttributeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbIdpTemplateAttributeDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbIdpTemplateAttributeDo) Delete(models ...*model.TbIdpTemplateAttribute) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbIdpTemplateAttributeDo) withDO(do gen.Dao) *tbIdpTemplateAttributeDo {
	t.DO = *do.(*gen.DO)
	return t
}
