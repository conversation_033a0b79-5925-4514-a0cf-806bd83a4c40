// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.1
// source: auth/v1/auth/auth.proto

package auth

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAuthAdmsLogin = "/api.auth.v1.auth.Auth/AdmsLogin"
const OperationAuthAuthCallback = "/api.auth.v1.auth.Auth/AuthCallback"
const OperationAuthCache = "/api.auth.v1.auth.Auth/Cache"
const OperationAuthGetSendSmsKey = "/api.auth.v1.auth.Auth/GetSendSmsKey"
const OperationAuthGetToken = "/api.auth.v1.auth.Auth/GetToken"
const OperationAuthListMainIDP = "/api.auth.v1.auth.Auth/ListMainIDP"
const OperationAuthLogin = "/api.auth.v1.auth.Auth/Login"
const OperationAuthOAuth2Callback = "/api.auth.v1.auth.Auth/OAuth2Callback"
const OperationAuthRefreshToken = "/api.auth.v1.auth.Auth/RefreshToken"
const OperationAuthSendSms = "/api.auth.v1.auth.Auth/SendSms"
const OperationAuthSmsVerify = "/api.auth.v1.auth.Auth/SmsVerify"
const OperationAuthThirdLogin = "/api.auth.v1.auth.Auth/ThirdLogin"
const OperationAuthTokenVerify = "/api.auth.v1.auth.Auth/TokenVerify"
const OperationAuthUserBind = "/api.auth.v1.auth.Auth/UserBind"

type AuthHTTPServer interface {
	AdmsLogin(context.Context, *AdmsLoginRequest) (*AdmsLoginReply, error)
	// AuthCallback 通用认证回调统一入口
	AuthCallback(context.Context, *AuthCallbackRequest) (*LoginReply, error)
	Cache(context.Context, *CacheRequest) (*CacheReply, error)
	GetSendSmsKey(context.Context, *SendSmsKeyRequest) (*SendSmsKeyReply, error)
	GetToken(context.Context, *GetTokenRequest) (*GetTokenReply, error)
	ListMainIDP(context.Context, *ListMainIDPRequest) (*ListMainIDPReply, error)
	Login(context.Context, *LoginRequest) (*LoginReply, error)
	OAuth2Callback(context.Context, *OAuth2CallbackRequest) (*LoginReply, error)
	RefreshToken(context.Context, *RefreshTokenRequest) (*GetTokenReply, error)
	SendSms(context.Context, *SendSmsRequest) (*SendSmsReply, error)
	SmsVerify(context.Context, *SmsVerifyRequest) (*LoginReply, error)
	ThirdLogin(context.Context, *ThirdLoginRequest) (*LoginReply, error)
	TokenVerify(context.Context, *TokenVerifyRequest) (*TokenVerifyReply, error)
	UserBind(context.Context, *UserBindRequest) (*UserBindReply, error)
}

func RegisterAuthHTTPServer(s *http.Server, srv AuthHTTPServer) {
	r := s.Route("/")
	r.POST("/auth/login/v1/user", _Auth_Login0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/user/token", _Auth_GetToken0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/user/main_idp/list", _Auth_ListMainIDP0_HTTP_Handler(srv))
	r.GET("/auth/authz/v1/user/refresh_token", _Auth_RefreshToken0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/user/third", _Auth_ThirdLogin0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/cache", _Auth_Cache0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/send_sms", _Auth_SendSms0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/sms_verify", _Auth_SmsVerify0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/token_verify", _Auth_TokenVerify0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/sms_key", _Auth_GetSendSmsKey0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/adms", _Auth_AdmsLogin0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/userbind", _Auth_UserBind0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/callback/{idp_id}", _Auth_OAuth2Callback0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/callback/{idp_id}", _Auth_OAuth2Callback1_HTTP_Handler(srv))
	r.POST("/auth/login/v1/callback", _Auth_AuthCallback0_HTTP_Handler(srv))
}

func _Auth_Login0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LoginRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthLogin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Login(ctx, req.(*LoginRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_GetToken0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthGetToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetToken(ctx, req.(*GetTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTokenReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_ListMainIDP0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListMainIDPRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthListMainIDP)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMainIDP(ctx, req.(*ListMainIDPRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListMainIDPReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_RefreshToken0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RefreshTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthRefreshToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RefreshToken(ctx, req.(*RefreshTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTokenReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_ThirdLogin0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ThirdLoginRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthThirdLogin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ThirdLogin(ctx, req.(*ThirdLoginRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_Cache0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CacheRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthCache)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Cache(ctx, req.(*CacheRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CacheReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_SendSms0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendSmsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthSendSms)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendSms(ctx, req.(*SendSmsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendSmsReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_SmsVerify0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SmsVerifyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthSmsVerify)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SmsVerify(ctx, req.(*SmsVerifyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_TokenVerify0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TokenVerifyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthTokenVerify)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TokenVerify(ctx, req.(*TokenVerifyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TokenVerifyReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_GetSendSmsKey0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendSmsKeyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthGetSendSmsKey)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSendSmsKey(ctx, req.(*SendSmsKeyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendSmsKeyReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_AdmsLogin0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AdmsLoginRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthAdmsLogin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AdmsLogin(ctx, req.(*AdmsLoginRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AdmsLoginReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_UserBind0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserBindRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthUserBind)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserBind(ctx, req.(*UserBindRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserBindReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_OAuth2Callback0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OAuth2CallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthOAuth2Callback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OAuth2Callback(ctx, req.(*OAuth2CallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_OAuth2Callback1_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OAuth2CallbackRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthOAuth2Callback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OAuth2Callback(ctx, req.(*OAuth2CallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

func _Auth_AuthCallback0_HTTP_Handler(srv AuthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AuthCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAuthAuthCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AuthCallback(ctx, req.(*AuthCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginReply)
		return ctx.Result(200, reply)
	}
}

type AuthHTTPClient interface {
	AdmsLogin(ctx context.Context, req *AdmsLoginRequest, opts ...http.CallOption) (rsp *AdmsLoginReply, err error)
	AuthCallback(ctx context.Context, req *AuthCallbackRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	Cache(ctx context.Context, req *CacheRequest, opts ...http.CallOption) (rsp *CacheReply, err error)
	GetSendSmsKey(ctx context.Context, req *SendSmsKeyRequest, opts ...http.CallOption) (rsp *SendSmsKeyReply, err error)
	GetToken(ctx context.Context, req *GetTokenRequest, opts ...http.CallOption) (rsp *GetTokenReply, err error)
	ListMainIDP(ctx context.Context, req *ListMainIDPRequest, opts ...http.CallOption) (rsp *ListMainIDPReply, err error)
	Login(ctx context.Context, req *LoginRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	OAuth2Callback(ctx context.Context, req *OAuth2CallbackRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	RefreshToken(ctx context.Context, req *RefreshTokenRequest, opts ...http.CallOption) (rsp *GetTokenReply, err error)
	SendSms(ctx context.Context, req *SendSmsRequest, opts ...http.CallOption) (rsp *SendSmsReply, err error)
	SmsVerify(ctx context.Context, req *SmsVerifyRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	ThirdLogin(ctx context.Context, req *ThirdLoginRequest, opts ...http.CallOption) (rsp *LoginReply, err error)
	TokenVerify(ctx context.Context, req *TokenVerifyRequest, opts ...http.CallOption) (rsp *TokenVerifyReply, err error)
	UserBind(ctx context.Context, req *UserBindRequest, opts ...http.CallOption) (rsp *UserBindReply, err error)
}

type AuthHTTPClientImpl struct {
	cc *http.Client
}

func NewAuthHTTPClient(client *http.Client) AuthHTTPClient {
	return &AuthHTTPClientImpl{client}
}

func (c *AuthHTTPClientImpl) AdmsLogin(ctx context.Context, in *AdmsLoginRequest, opts ...http.CallOption) (*AdmsLoginReply, error) {
	var out AdmsLoginReply
	pattern := "/auth/login/v1/adms"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthAdmsLogin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) AuthCallback(ctx context.Context, in *AuthCallbackRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/auth/login/v1/callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthAuthCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) Cache(ctx context.Context, in *CacheRequest, opts ...http.CallOption) (*CacheReply, error) {
	var out CacheReply
	pattern := "/auth/login/v1/cache"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthCache))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) GetSendSmsKey(ctx context.Context, in *SendSmsKeyRequest, opts ...http.CallOption) (*SendSmsKeyReply, error) {
	var out SendSmsKeyReply
	pattern := "/auth/login/v1/sms_key"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthGetSendSmsKey))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) GetToken(ctx context.Context, in *GetTokenRequest, opts ...http.CallOption) (*GetTokenReply, error) {
	var out GetTokenReply
	pattern := "/auth/login/v1/user/token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthGetToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) ListMainIDP(ctx context.Context, in *ListMainIDPRequest, opts ...http.CallOption) (*ListMainIDPReply, error) {
	var out ListMainIDPReply
	pattern := "/auth/login/v1/user/main_idp/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthListMainIDP))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) Login(ctx context.Context, in *LoginRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/auth/login/v1/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthLogin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) OAuth2Callback(ctx context.Context, in *OAuth2CallbackRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/auth/login/v1/callback/{idp_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthOAuth2Callback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...http.CallOption) (*GetTokenReply, error) {
	var out GetTokenReply
	pattern := "/auth/authz/v1/user/refresh_token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthRefreshToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) SendSms(ctx context.Context, in *SendSmsRequest, opts ...http.CallOption) (*SendSmsReply, error) {
	var out SendSmsReply
	pattern := "/auth/login/v1/send_sms"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthSendSms))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) SmsVerify(ctx context.Context, in *SmsVerifyRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/auth/login/v1/sms_verify"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthSmsVerify))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) ThirdLogin(ctx context.Context, in *ThirdLoginRequest, opts ...http.CallOption) (*LoginReply, error) {
	var out LoginReply
	pattern := "/auth/login/v1/user/third"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthThirdLogin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) TokenVerify(ctx context.Context, in *TokenVerifyRequest, opts ...http.CallOption) (*TokenVerifyReply, error) {
	var out TokenVerifyReply
	pattern := "/auth/login/v1/token_verify"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAuthTokenVerify))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AuthHTTPClientImpl) UserBind(ctx context.Context, in *UserBindRequest, opts ...http.CallOption) (*UserBindReply, error) {
	var out UserBindReply
	pattern := "/auth/login/v1/userbind"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAuthUserBind))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
