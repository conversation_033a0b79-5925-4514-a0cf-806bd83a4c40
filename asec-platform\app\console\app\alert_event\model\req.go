package model

import "asdsec.com/asec/platform/pkg/model"

type GetAlertEventReq struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

type GetAlertEventListReq struct {
	model.Pagination
	Channel      []string `json:"channel"`
	SensitiveIds []string `json:"sensitive_ids"`
	Action       string   `json:"action"`
	SeverityId   int      `form:"severity_id" json:"severity_id"`
	StartTime    string   `json:"start_time,omitempty"`
	EndTime      string   `json:"end_time,omitempty"`
}

type UpdateScoreConfig struct {
	Id       string `json:"id"`
	MinScore int    `json:"min_score"`
	MaxScore int    `json:"max_score"`
}
