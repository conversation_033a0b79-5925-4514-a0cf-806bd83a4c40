package repository

import (
	model2 "asdsec.com/asec/platform/app/console/app/access/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"context"
	"fmt"
	"github.com/lib/pq"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strconv"
)

type strategyRepository struct {
}

func (s strategyRepository) CheckStrategyQuote(ctx context.Context, req model2.CheckStrategyQuoteReq) ([]model2.CheckStrategyQuoteResp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	var appId uint64
	if req.AppId != "" {
		appId, err = strconv.ParseUint(req.AppId, 10, 64)
		if err != nil {
			global.SysLog.Error("ParseUint err", zap.Error(err))
			return nil, err
		}
	}
	db = db.Model(&model.AccessStrategy{}).
		Select("id as strategy_id,strategy_name").
		Where("?=any (app_ids)", appId).
		Or("enable_all_app = 1").
		Or("?=any (app_group_ids)", req.AppGroupId).
		Or("? = any (role_ids) ", req.RoleId).
		Or("app_group_ids && ?", pq.Array(req.AppGroupIds)).
		Or("app_ids && ?", pq.Array(req.AppIds))
	var resp []model2.CheckStrategyQuoteResp
	err = db.Find(&resp).Error
	if err != nil {
		global.SysLog.Error("checkStrategyQuote err", zap.Error(err))
		return nil, err
	}
	return resp, nil
}

func (s strategyRepository) RecursiveGetGroup(ctx context.Context, groupIds []string) ([]string, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return nil, err
	}
	sql := `
WITH RECURSIVE child_groups AS (
    SELECT id
    FROM tb_user_group
    WHERE parent_group_id in (?)
    UNION ALL

    SELECT g.id
    FROM tb_user_group AS g
             JOIN child_groups AS cg ON g.parent_group_id = cg.id)
SELECT id
FROM child_groups;
`
	var res []string
	err = db.Raw(sql, groupIds).Find(&res).Error
	if err != nil {
		global.SysLog.Error("recursiveGetGroup err", zap.Error(err))
		return nil, err
	}
	// 加上本身的查询id
	res = append(res, groupIds...)
	return res, nil
}

func (s strategyRepository) SyncStrategyAppIds(ctx context.Context, ids []uint64) error {
	tx, err := global.GetTxDBClient(ctx)
	if err != nil {
		return err
	}
	for _, id := range ids {
		err = tx.Session(&gorm.Session{AllowGlobalUpdate: true}).
			Model(&model.AccessStrategy{}).
			Update("app_ids", gorm.Expr("array_remove(app_ids,?)", id)).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

func (s strategyRepository) SyncStrategyAppGroupIds(ctx context.Context, ids []uint64) error {
	tx, err := global.GetTxDBClient(ctx)
	if err != nil {
		return err
	}
	for _, id := range ids {
		err = tx.Session(&gorm.Session{AllowGlobalUpdate: true}).
			Model(&model.AccessStrategy{}).
			Update("app_group_ids", gorm.Expr("array_remove(app_group_ids,?)", id)).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

func (s strategyRepository) SyncStrategyUserIds(ctx context.Context, ids []string) error {
	tx, err := global.GetTxDBClient(ctx)
	if err != nil {
		return err
	}
	for _, id := range ids {
		err = tx.Session(&gorm.Session{AllowGlobalUpdate: true}).
			Model(&model.AccessStrategy{}).
			Update("user_ids", gorm.Expr("array_remove(user_ids,?)", id)).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

func (s strategyRepository) SyncStrategyUserGroupIds(ctx context.Context, ids []string) error {
	tx, err := global.GetTxDBClient(ctx)
	if err != nil {
		return err
	}
	for _, id := range ids {
		err = tx.Session(&gorm.Session{AllowGlobalUpdate: true}).
			Model(&model.AccessStrategy{}).
			Update("user_group_ids", gorm.Expr("array_remove(user_group_ids,?)", id)).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

func (s strategyRepository) StrategyEnable(ctx context.Context, ids []int64, status int) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return err
	}
	err = db.Where("id in ?", ids).Updates(model.AccessStrategy{StrategyStatus: status}).Error
	if err != nil {
		global.SysLog.Error("update StrategyEnable err", zap.Error(err))
		return err
	}
	return nil
}

func (s strategyRepository) StrategyDel(ctx context.Context, ids []int64) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err:", zap.Error(err))
		return err
	}
	err = db.Where("id in ?", ids).Delete(&model.AccessStrategy{}).Error
	if err != nil {
		global.SysLog.Error("strategyDel err:", zap.Error(err))
		return err
	}
	return nil
}

func (s strategyRepository) StrategyUpdate(ctx context.Context, req model.AccessStrategy) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	err = db.Model(&model.AccessStrategy{}).Where("id=?", req.ID).Save(req).Error
	if err != nil {
		global.SysLog.Error("strategyUpdate err:", zap.Error(err))
		return err
	}
	return nil
}

func (s strategyRepository) StrategyList(ctx context.Context, req model2.StrategyListReq) (model2.StrategyListResp, error) {
	db, err := global.GetDBClient(ctx)
	resp := model2.StrategyListResp{}
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return resp, err
	}
	pagination := model.Pagination{Limit: req.Limit, Offset: req.Offset}

	var listData []model2.StrategyListData

	subQuery := db.Model(&model.AccessStrategy{}).Select(`tb_access_strategy.id`).
		Joins("left outer join tb_user_entity on tb_user_entity.id = ANY (tb_access_strategy.user_ids)").
		Joins("left outer join tb_user_group on tb_user_group.id = any (tb_access_strategy.user_group_ids)").
		Joins("left outer join tb_application on tb_application.id = any (tb_access_strategy.app_ids)").
		Joins("left outer join tb_app_group on tb_app_group.id = any (tb_access_strategy.app_group_ids)").
		Joins("left outer join tb_role on tb_role.id = any (tb_access_strategy.role_ids)").
		Group("tb_access_strategy.id").
		Order("tb_access_strategy.id desc")
	if req.Search != "" {
		subQuery = subQuery.Where("tb_application.app_name like ?", fmt.Sprintf("%v%%", req.Search)).
			Or("tb_user_entity.name like ?", fmt.Sprintf("%v%%", req.Search)).
			Or("tb_user_entity.display_name like ?", fmt.Sprintf("%v%%", req.Search)).
			Or("tb_role.name like ?", fmt.Sprintf("%v%%", req.Search)).
			Or("tb_access_strategy.strategy_name like ?", fmt.Sprintf("%v%%", req.Search)).
			Or("tb_access_strategy.strategy_detail like ?", fmt.Sprintf("%v%%", req.Search))
	}
	d := db.Model(&model.AccessStrategy{}).
		Select("tb_access_strategy.*,"+
			"json_agg(distinct jsonb_build_object('user_id', tb_user_entity.id,'user_name', CASE WHEN (tb_user_entity.display_name is null or tb_user_entity.display_name ='') THEN tb_user_entity.name ELSE tb_user_entity.display_name END,'type','user')) filter ( where tb_user_entity.id is not null ) as user_info,"+
			"json_agg(distinct jsonb_build_object('user_group_id', tb_user_group.id,'user_group_name', tb_user_group.name,'type','group')) filter ( where tb_user_group.id is not null ) as group_info,"+
			"json_agg(distinct jsonb_build_object('app_name',tb_application.app_name,'app_id',tb_application.id::varchar,'type','app')) filter ( where tb_application.id is not null ) as app_info,"+
			"json_agg(distinct jsonb_build_object('app_group_name',tb_app_group.group_name,'app_group_id',tb_app_group.id::varchar,'type','app_group')) filter ( where tb_app_group.id is not null ) as app_group_info,"+
			"json_agg(distinct jsonb_build_object('role_id', kr.id, 'role_name', kr.name,'type','role')) filter ( where kr.id is not null ) as role_info").
		Joins("left outer join tb_user_entity on tb_user_entity.id = ANY (tb_access_strategy.user_ids)").
		Joins("left outer join tb_user_group on tb_user_group.id = any (tb_access_strategy.user_group_ids)").
		Joins("left outer join tb_application on tb_application.id = any (tb_access_strategy.app_ids)").
		Joins("left outer join tb_app_group on tb_app_group.id = any (tb_access_strategy.app_group_ids)").
		Joins("left outer join tb_role kr on kr.id = any (tb_access_strategy.role_ids)").
		Group("tb_access_strategy.id").
		Order("tb_access_strategy.id desc").
		Where("tb_access_strategy.id in (?)", subQuery)

	pagination, err = model.Paginate(&listData, &pagination, d)
	if err != nil {
		global.SysLog.Error("get StrategyList err:", zap.Error(err))
		return resp, err
	}
	resp.StrategyListData = listData
	resp.CurrentPage = pagination.Page
	resp.TotalNum = int(pagination.TotalRows)
	resp.PageSize = pagination.TotalPages
	return resp, nil
}

func (s strategyRepository) CreateStrategy(ctx context.Context, accessStrategy model.AccessStrategy) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err", zap.Error(err))
		return err
	}
	err = db.Model(model.AccessStrategy{}).Create(&accessStrategy).Error
	if err != nil {
		global.SysLog.Error("createStrategy err", zap.Error(err))
		return err
	}
	return nil
}

func (s strategyRepository) StrategyCountByName(ctx context.Context, strategyName string) (int64, error) {
	var count int64
	db, err := global.GetDBClient(ctx)
	if err != nil {
		global.SysLog.Error("get db err:", zap.Error(err))
		return count, err
	}
	err = db.Model(&model.AccessStrategy{}).Where("strategy_name = ?", strategyName).Count(&count).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.SysLog.Error("find strategy by name err:", zap.Error(err))
		return count, err
	}
	return count, nil
}

type StrategyRepository interface {
	CreateStrategy(ctx context.Context, accessStrategy model.AccessStrategy) error
	StrategyList(ctx context.Context, req model2.StrategyListReq) (model2.StrategyListResp, error)
	StrategyUpdate(ctx context.Context, req model.AccessStrategy) error
	StrategyDel(ctx context.Context, ids []int64) error
	StrategyEnable(ctx context.Context, ids []int64, status int) error
	SyncStrategyAppIds(ctx context.Context, ids []uint64) error
	SyncStrategyAppGroupIds(ctx context.Context, ids []uint64) error
	SyncStrategyUserIds(ctx context.Context, ids []string) error
	SyncStrategyUserGroupIds(ctx context.Context, ids []string) error
	RecursiveGetGroup(ctx context.Context, groupIds []string) ([]string, error)
	CheckStrategyQuote(ctx context.Context, req model2.CheckStrategyQuoteReq) ([]model2.CheckStrategyQuoteResp, error)
	StrategyCountByName(ctx context.Context, strategyName string) (int64, error)
}

func NewStrategyRepository() StrategyRepository {
	return strategyRepository{}
}
