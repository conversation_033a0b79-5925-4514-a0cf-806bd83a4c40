package webauth

import (
	"encoding/json"
	"fmt"
	"strings"
)

// Step 表示一个HTTP请求步骤
type Step struct {
	URL     string     `json:"url"`
	Method  string     `json:"method"`
	Timeout int        `json:"timeout"`
	Input   StepInput  `json:"input"`
	Output  StepOutput `json:"output"`
}

// StepInput 包含HTTP请求的输入配置
type StepInput struct {
	Headers    []KeyValue `json:"headers"`
	Gets       []KeyValue `json:"gets"`
	Cookies    []KeyValue `json:"cookies"`
	BodyType   int        `json:"bodyType"` // 添加：0是json  1是XML 2是formdata
	Body       string     `json:"body"`
	Formdata   []KeyValue `json:"formdata"` // 添加：表单数据字段
	OpenScript bool       `json:"openScript"`
	Script     string     `json:"script"`
}

// StepOutput 包含处理HTTP响应的配置
type StepOutput struct {
	Type       int        `json:"type"` // 0:JSON, 1:JWT, 2:XML, 3:Text
	Params     []ParamMap `json:"params"`
	Cookies    []ParamMap `json:"cookies"`
	OpenScript bool       `json:"openScript"`
	Script     string     `json:"script"`
	Column     string     `json:"column"`
	JudgeCond  string     `json:"judgeCond"`
	Value      string     `json:"value"`
}

// KeyValue 表示键值对
type KeyValue struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// ParamMap 表示响应参数映射
type ParamMap struct {
	Key    string `json:"key"`
	Jx     string `json:"jx"`
	Select string `json:"select"`
}

// ResponseData 包含请求响应数据和用户信息
type ResponseData struct {
	Response  map[string]interface{}
	UserInfo  map[string]interface{}
	Variables map[string]string
}

// JudgeCondType 条件判断类型
type JudgeCondType string

const (
	CondEqual      JudgeCondType = "Equal"      // 相等
	CondNotEqual   JudgeCondType = "NotEqual"   // 不相等
	CondContain    JudgeCondType = "Contain"    // 包含
	CondNotContain JudgeCondType = "NotContain" // 不包含
	CondEmpty      JudgeCondType = "Empty"      // 为空
	CondNotEmpty   JudgeCondType = "NotEmpty"   // 不为空
)

// 检查条件是否满足
func CheckCondition(value interface{}, condType JudgeCondType, condValue string) bool {
	strValue := ""
	if value != nil {
		strValue = strings.TrimSpace(toString(value))
	}

	switch condType {
	case CondEqual:
		return strValue == condValue
	case CondNotEqual:
		return strValue != condValue
	case CondContain:
		return strings.Contains(strValue, condValue)
	case CondNotContain:
		return !strings.Contains(strValue, condValue)
	case CondEmpty:
		return strValue == ""
	case CondNotEmpty:
		return strValue != ""
	default:
		return false
	}
}

// 转换为字符串
func toString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%g", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	case []byte:
		return string(v)
	case fmt.Stringer:
		return v.String()
	default:
		jsonBytes, err := json.Marshal(v)
		if err != nil {
			return fmt.Sprintf("%v", v)
		}
		return string(jsonBytes)
	}
}
