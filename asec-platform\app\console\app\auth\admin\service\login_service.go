package service

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/console/app/auth/admin/constants"
	model2 "asdsec.com/asec/platform/app/console/app/auth/admin/model"
	"asdsec.com/asec/platform/app/console/app/auth/admin/repository"
	"asdsec.com/asec/platform/pkg/utils/jwt_util"
	"asdsec.com/asec/platform/pkg/utils/license"
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
)

var LoginServiceImpl LoginService

// LoginServiceInit 单例对象
var LoginServiceInit sync.Once

type LoginService interface {
	Login(ctx context.Context, req model2.AdminLoginReq) (model2.Token, error)
	RefreshToken(ctx context.Context, req model2.RefreshTokenReq) (model2.Token, error)
	GetEcdsaKeyByCorpId(ctx context.Context, corpId string, componentNameKey string) (string, error)
	GetAdminByCorpIdAndUserId(ctx context.Context, userId string, corpId string) (model2.Admin, error)
	Logout(ctx context.Context, userId, token string, tokenClaim jwt_util.TokenClaims) error
	GetLicenseInfo(ctx context.Context, corpId string) (license.GetLicenseInfoRsp, error)
	GetLicenseStatus(ctx context.Context, corpId string) (int, int, error)
}

type loginService struct {
	db repository.LoginRepository
}

func (a loginService) GetLicenseStatus(ctx context.Context, corpId string) (int, int, error) {
	return a.db.GetLicenseStatus(ctx, corpId)
}

func (a loginService) GetLicenseInfo(ctx context.Context, corpId string) (license.GetLicenseInfoRsp, error) {
	return a.db.GetLicenseInfo(ctx, corpId)
}

func (a loginService) Logout(ctx context.Context, userId, token string, tokenClaim jwt_util.TokenClaims) error {
	return a.db.Logout(ctx, userId, token, tokenClaim)
}

func (a loginService) GetEcdsaKeyByCorpId(ctx context.Context, corpId string, componentNameKey string) (string, error) {
	return a.db.GetEcdsaKeyByCorpId(ctx, corpId, componentNameKey)
}

func (a loginService) GetAdminByCorpIdAndUserId(ctx context.Context, userId string, corpId string) (model2.Admin, error) {
	return a.db.GetAdminByCorpIdAndUserId(ctx, userId, corpId)
}

func (a loginService) RefreshToken(ctx context.Context, req model2.RefreshTokenReq) (model2.Token, error) {
	return a.db.RefreshToken(ctx, req)
}

func (a loginService) Login(ctx context.Context, req model2.AdminLoginReq) (model2.Token, error) {
	token, err := a.db.Login(ctx, req)
	if err != nil && err.Error() == constants.AdminLoginPwdInCorrectError {
		loginFail, err := a.db.RecordLoginFail(ctx, req.Name, req.CorpId)
		if err != nil {
			return model2.Token{}, err
		}
		config, err := GetAdminService().GetAdminAuthConfig(ctx)
		if err != nil {
			return model2.Token{}, err
		}
		if uint64(loginFail) >= config.MaxFailedTimes {
			lockMin := time.Duration(config.LockedDuration)
			err = a.db.LockUser(ctx, req.Name, req.CorpId, lockMin*time.Minute)
			if err != nil {
				global.Logger.Sugar().Errorf("lock user failed %v", err)
				return model2.Token{}, err
			}
			return model2.Token{}, errors.New(fmt.Sprintf("登录失败次数达到%d次，锁定%d分钟", config.MaxFailedTimes, config.LockedDuration))
		}
	}
	return token, err
}

func GetLoginService() LoginService {
	LoginServiceInit.Do(func() {
		LoginServiceImpl = &loginService{db: repository.NewLoginRepository()}
	})
	return LoginServiceImpl
}
