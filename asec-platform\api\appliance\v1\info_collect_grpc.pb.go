// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: appliance/v1/info_collect.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ReportCollectedInfoClient is the client API for ReportCollectedInfo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportCollectedInfoClient interface {
	CollectedInfo(ctx context.Context, in *InfoCollectReq, opts ...grpc.CallOption) (*Reply, error)
}

type reportCollectedInfoClient struct {
	cc grpc.ClientConnInterface
}

func NewReportCollectedInfoClient(cc grpc.ClientConnInterface) ReportCollectedInfoClient {
	return &reportCollectedInfoClient{cc}
}

func (c *reportCollectedInfoClient) CollectedInfo(ctx context.Context, in *InfoCollectReq, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, "/api.appliance.ReportCollectedInfo/CollectedInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportCollectedInfoServer is the server API for ReportCollectedInfo service.
// All implementations must embed UnimplementedReportCollectedInfoServer
// for forward compatibility
type ReportCollectedInfoServer interface {
	CollectedInfo(context.Context, *InfoCollectReq) (*Reply, error)
	mustEmbedUnimplementedReportCollectedInfoServer()
}

// UnimplementedReportCollectedInfoServer must be embedded to have forward compatible implementations.
type UnimplementedReportCollectedInfoServer struct {
}

func (UnimplementedReportCollectedInfoServer) CollectedInfo(context.Context, *InfoCollectReq) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectedInfo not implemented")
}
func (UnimplementedReportCollectedInfoServer) mustEmbedUnimplementedReportCollectedInfoServer() {}

// UnsafeReportCollectedInfoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportCollectedInfoServer will
// result in compilation errors.
type UnsafeReportCollectedInfoServer interface {
	mustEmbedUnimplementedReportCollectedInfoServer()
}

func RegisterReportCollectedInfoServer(s grpc.ServiceRegistrar, srv ReportCollectedInfoServer) {
	s.RegisterService(&ReportCollectedInfo_ServiceDesc, srv)
}

func _ReportCollectedInfo_CollectedInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportCollectedInfoServer).CollectedInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.appliance.ReportCollectedInfo/CollectedInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportCollectedInfoServer).CollectedInfo(ctx, req.(*InfoCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ReportCollectedInfo_ServiceDesc is the grpc.ServiceDesc for ReportCollectedInfo service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReportCollectedInfo_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.appliance.ReportCollectedInfo",
	HandlerType: (*ReportCollectedInfoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CollectedInfo",
			Handler:    _ReportCollectedInfo_CollectedInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "appliance/v1/info_collect.proto",
}
