package api

import (
	"asdsec.com/asec/platform/app/console/app/auth/role/model"
	"asdsec.com/asec/platform/app/console/app/auth/role/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/web"
	globalModel "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateAdminRole godoc
// @Summary 创建角色
// @Schemes
// @Description 创建角色
// @Tags        Role
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/role [POST]
// @success     200 {object} common.Response{data=model.CreateAdminRoleReq} "create role"
func CreateAdminRole(ctx *gin.Context) {
	req := model.CreateAdminRoleReq{}
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.AdminRoleResourceType,
			OperationType:  common.OperateCreate,
			Representation: "create role:" + req.RoleName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	req.CorpId = web.GetAdminCorpId(ctx)
	err = service.GetAdminRoleService().CreateAdminRole(ctx, req)
	if err != nil {
		global.SysLog.Error("create admin role error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// DeleteAdminRole godoc
// @Summary 删除管理员角色
// @Schemes
// @Description 删除管理员角色
// @Tags        AdminRole
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/role [DELETE]
// @success     200 {object} common.Response{} "delete admin role"
func DeleteAdminRole(ctx *gin.Context) {
	roleId := ctx.Query("id")
	corpId := web.GetAdminCorpId(ctx)
	//日志操作
	var errorLog = ""
	defer func() {
		oplog := globalModel.Oprlog{
			ResourceType:   common.AdminRoleResourceType,
			OperationType:  common.OperateDelete,
			Representation: "delete admin role,role_id:" + roleId,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(ctx, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err := service.GetAdminRoleService().DeleteAdminRole(ctx, roleId, corpId)
	if err != nil {
		global.SysLog.Error("admin role delete error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.Ok(ctx)
}

// GetAdminRoleList godoc
// @Summary 获取管理员角色列表
// @Schemes
// @Description 获取管理员角色列表
// @Tags       	Get Admin Role List
// @Produce     application/json
// @Success     200
// @Router      /v1/admin/role/list [GET]
// @success     200 {object} common.Response{data=model.GetAdminRoleListReq} "get admin role list"
func GetAdminRoleList(ctx *gin.Context) {
	req := model.GetAdminRoleListReq{}
	err := ctx.ShouldBindQuery(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(ctx, common.ParamInvalidError)
		return
	}
	corpId := web.GetAdminCorpId(ctx)
	req.CorpId = corpId
	data, err := service.GetAdminRoleService().GetAdminRoleList(ctx, req)
	if err != nil {
		global.SysLog.Error("get admin role list error", zap.Error(err))
		common.Fail(ctx, common.OperateError)
		return
	}
	common.OkWithData(ctx, data)
}
