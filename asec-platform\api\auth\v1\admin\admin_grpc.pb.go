// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.20.1
// source: auth/v1/admin/admin.proto

package admin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Admin_CreateCorp_FullMethodName            = "/api.auth.v1.admin.Admin/CreateCorp"
	Admin_UpdateCorp_FullMethodName            = "/api.auth.v1.admin.Admin/UpdateCorp"
	Admin_UpdateLockStatus_FullMethodName      = "/api.auth.v1.admin.Admin/UpdateLockStatus"
	Admin_UpdateIdleTime_FullMethodName        = "/api.auth.v1.admin.Admin/UpdateIdleTime"
	Admin_TotpUnbind_FullMethodName            = "/api.auth.v1.admin.Admin/TotpUnbind"
	Admin_DeleteCorp_FullMethodName            = "/api.auth.v1.admin.Admin/DeleteCorp"
	Admin_GetCorp_FullMethodName               = "/api.auth.v1.admin.Admin/GetCorp"
	Admin_ListCorp_FullMethodName              = "/api.auth.v1.admin.Admin/ListCorp"
	Admin_CreateUserSource_FullMethodName      = "/api.auth.v1.admin.Admin/CreateUserSource"
	Admin_UpdateUserSource_FullMethodName      = "/api.auth.v1.admin.Admin/UpdateUserSource"
	Admin_DeleteUserSource_FullMethodName      = "/api.auth.v1.admin.Admin/DeleteUserSource"
	Admin_GetUserSource_FullMethodName         = "/api.auth.v1.admin.Admin/GetUserSource"
	Admin_ListUserSource_FullMethodName        = "/api.auth.v1.admin.Admin/ListUserSource"
	Admin_ListUserSourceType_FullMethodName    = "/api.auth.v1.admin.Admin/ListUserSourceType"
	Admin_ListIDPType_FullMethodName           = "/api.auth.v1.admin.Admin/ListIDPType"
	Admin_OAuth2Test_FullMethodName            = "/api.auth.v1.admin.Admin/OAuth2Test"
	Admin_TestResult_FullMethodName            = "/api.auth.v1.admin.Admin/TestResult"
	Admin_CreateIDP_FullMethodName             = "/api.auth.v1.admin.Admin/CreateIDP"
	Admin_UpdateIDP_FullMethodName             = "/api.auth.v1.admin.Admin/UpdateIDP"
	Admin_DeleteIDP_FullMethodName             = "/api.auth.v1.admin.Admin/DeleteIDP"
	Admin_ListIDP_FullMethodName               = "/api.auth.v1.admin.Admin/ListIDP"
	Admin_GetIDPDetail_FullMethodName          = "/api.auth.v1.admin.Admin/GetIDPDetail"
	Admin_ListRootGroup_FullMethodName         = "/api.auth.v1.admin.Admin/ListRootGroup"
	Admin_GetRootGroupDetail_FullMethodName    = "/api.auth.v1.admin.Admin/GetRootGroupDetail"
	Admin_CreateRootGroup_FullMethodName       = "/api.auth.v1.admin.Admin/CreateRootGroup"
	Admin_GetRootGroupIdpList_FullMethodName   = "/api.auth.v1.admin.Admin/GetRootGroupIdpList"
	Admin_GetFieldMap_FullMethodName           = "/api.auth.v1.admin.Admin/GetFieldMap"
	Admin_GetFieldOptions_FullMethodName       = "/api.auth.v1.admin.Admin/GetFieldOptions"
	Admin_UpdateRootGroup_FullMethodName       = "/api.auth.v1.admin.Admin/UpdateRootGroup"
	Admin_UpdateRootGroupCustom_FullMethodName = "/api.auth.v1.admin.Admin/UpdateRootGroupCustom"
	Admin_SwitchAutoSync_FullMethodName        = "/api.auth.v1.admin.Admin/SwitchAutoSync"
	Admin_SyncTrigger_FullMethodName           = "/api.auth.v1.admin.Admin/SyncTrigger"
	Admin_ListSyncLog_FullMethodName           = "/api.auth.v1.admin.Admin/ListSyncLog"
	Admin_DeleteRootGroup_FullMethodName       = "/api.auth.v1.admin.Admin/DeleteRootGroup"
	Admin_DeleteRootGroupCustom_FullMethodName = "/api.auth.v1.admin.Admin/DeleteRootGroupCustom"
	Admin_ListUserGroup_FullMethodName         = "/api.auth.v1.admin.Admin/ListUserGroup"
	Admin_CreateUserGroup_FullMethodName       = "/api.auth.v1.admin.Admin/CreateUserGroup"
	Admin_CreateUserGroupCustom_FullMethodName = "/api.auth.v1.admin.Admin/CreateUserGroupCustom"
	Admin_UpdateUserGroup_FullMethodName       = "/api.auth.v1.admin.Admin/UpdateUserGroup"
	Admin_CreateUser_FullMethodName            = "/api.auth.v1.admin.Admin/CreateUser"
	Admin_CreateUserCustom_FullMethodName      = "/api.auth.v1.admin.Admin/CreateUserCustom"
	Admin_DeleteUserCustom_FullMethodName      = "/api.auth.v1.admin.Admin/DeleteUserCustom"
	Admin_UpdateUserCustom_FullMethodName      = "/api.auth.v1.admin.Admin/UpdateUserCustom"
	Admin_ListUser_FullMethodName              = "/api.auth.v1.admin.Admin/ListUser"
	Admin_DeleteUser_FullMethodName            = "/api.auth.v1.admin.Admin/DeleteUser"
	Admin_UpdateUser_FullMethodName            = "/api.auth.v1.admin.Admin/UpdateUser"
	Admin_CreateRole_FullMethodName            = "/api.auth.v1.admin.Admin/CreateRole"
	Admin_ListRole_FullMethodName              = "/api.auth.v1.admin.Admin/ListRole"
	Admin_IdleAccountList_FullMethodName       = "/api.auth.v1.admin.Admin/IdleAccountList"
	Admin_UpdateRole_FullMethodName            = "/api.auth.v1.admin.Admin/UpdateRole"
	Admin_DeleteRole_FullMethodName            = "/api.auth.v1.admin.Admin/DeleteRole"
	Admin_CreateAuthPolicy_FullMethodName      = "/api.auth.v1.admin.Admin/CreateAuthPolicy"
	Admin_UpdateAuthPolicy_FullMethodName      = "/api.auth.v1.admin.Admin/UpdateAuthPolicy"
	Admin_ListAuthPolicy_FullMethodName        = "/api.auth.v1.admin.Admin/ListAuthPolicy"
	Admin_DeleteAuthPolicy_FullMethodName      = "/api.auth.v1.admin.Admin/DeleteAuthPolicy"
	Admin_CreateAccountPolicy_FullMethodName   = "/api.auth.v1.admin.Admin/CreateAccountPolicy"
	Admin_UpdateAccountPolicy_FullMethodName   = "/api.auth.v1.admin.Admin/UpdateAccountPolicy"
	Admin_DeleteAccountPolicy_FullMethodName   = "/api.auth.v1.admin.Admin/DeleteAccountPolicy"
	Admin_GetAccountPolicy_FullMethodName      = "/api.auth.v1.admin.Admin/GetAccountPolicy"
	Admin_ListAccountPolicies_FullMethodName   = "/api.auth.v1.admin.Admin/ListAccountPolicies"
	Admin_UnlockAccount_FullMethodName         = "/api.auth.v1.admin.Admin/UnlockAccount"
	Admin_BatchUnlockAccounts_FullMethodName   = "/api.auth.v1.admin.Admin/BatchUnlockAccounts"
	Admin_GetAccountLockInfo_FullMethodName    = "/api.auth.v1.admin.Admin/GetAccountLockInfo"
	Admin_ListLockedAccounts_FullMethodName    = "/api.auth.v1.admin.Admin/ListLockedAccounts"
	Admin_UnlockIP_FullMethodName              = "/api.auth.v1.admin.Admin/UnlockIP"
	Admin_BatchUnlockIPs_FullMethodName        = "/api.auth.v1.admin.Admin/BatchUnlockIPs"
	Admin_GetIPLockInfo_FullMethodName         = "/api.auth.v1.admin.Admin/GetIPLockInfo"
	Admin_ListLockedIPs_FullMethodName         = "/api.auth.v1.admin.Admin/ListLockedIPs"
	Admin_ValidateWebAuthScript_FullMethodName = "/api.auth.v1.admin.Admin/ValidateWebAuthScript"
	Admin_ListUserSessions_FullMethodName      = "/api.auth.v1.admin.Admin/ListUserSessions"
	Admin_KickUserSession_FullMethodName       = "/api.auth.v1.admin.Admin/KickUserSession"
	Admin_GetClientLimits_FullMethodName       = "/api.auth.v1.admin.Admin/GetClientLimits"
	Admin_UpdateClientLimits_FullMethodName    = "/api.auth.v1.admin.Admin/UpdateClientLimits"
	Admin_GetUserCount_FullMethodName          = "/api.auth.v1.admin.Admin/GetUserCount"
)

// AdminClient is the client API for Admin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdminClient interface {
	CreateCorp(ctx context.Context, in *CreateCorpRequest, opts ...grpc.CallOption) (*CreateCorpReply, error)
	UpdateCorp(ctx context.Context, in *UpdateCorpRequest, opts ...grpc.CallOption) (*UpdateCorpReply, error)
	UpdateLockStatus(ctx context.Context, in *UpdateLockStatusRequest, opts ...grpc.CallOption) (*UpdateLockStatusReply, error)
	UpdateIdleTime(ctx context.Context, in *UpdateIdleTimeRequest, opts ...grpc.CallOption) (*UpdateIdleTimeReply, error)
	TotpUnbind(ctx context.Context, in *TotpUnbindRequest, opts ...grpc.CallOption) (*TotpUnbindTimeReply, error)
	DeleteCorp(ctx context.Context, in *DeleteCorpRequest, opts ...grpc.CallOption) (*DeleteCorpReply, error)
	GetCorp(ctx context.Context, in *GetCorpRequest, opts ...grpc.CallOption) (*GetCorpReply, error)
	ListCorp(ctx context.Context, in *ListCorpRequest, opts ...grpc.CallOption) (*ListCorpReply, error)
	CreateUserSource(ctx context.Context, in *CreateUserSourceRequest, opts ...grpc.CallOption) (*CreateUserSourceReply, error)
	UpdateUserSource(ctx context.Context, in *UpdateUserSourceRequest, opts ...grpc.CallOption) (*UpdateUserSourceReply, error)
	DeleteUserSource(ctx context.Context, in *DeleteUserSourceRequest, opts ...grpc.CallOption) (*DeleteUserSourceReply, error)
	GetUserSource(ctx context.Context, in *GetUserSourceRequest, opts ...grpc.CallOption) (*GetUserSourceReply, error)
	ListUserSource(ctx context.Context, in *ListUserSourceRequest, opts ...grpc.CallOption) (*ListUserSourceReply, error)
	ListUserSourceType(ctx context.Context, in *ListUserSourceTypeRequest, opts ...grpc.CallOption) (*ListUserSourceTypeReply, error)
	ListIDPType(ctx context.Context, in *ListIDPTypeRequest, opts ...grpc.CallOption) (*ListIDPTypeReply, error)
	OAuth2Test(ctx context.Context, in *OAuth2TestRequest, opts ...grpc.CallOption) (*OAuth2TestReply, error)
	TestResult(ctx context.Context, in *TestResultRequest, opts ...grpc.CallOption) (*TestResultReply, error)
	CreateIDP(ctx context.Context, in *CreateIDPRequest, opts ...grpc.CallOption) (*CreateIDPReply, error)
	UpdateIDP(ctx context.Context, in *UpdateIDPRequest, opts ...grpc.CallOption) (*UpdateIDPReply, error)
	DeleteIDP(ctx context.Context, in *DeleteIDPRequest, opts ...grpc.CallOption) (*DeleteIDPReply, error)
	ListIDP(ctx context.Context, in *ListIDPRequest, opts ...grpc.CallOption) (*ListIDPReply, error)
	GetIDPDetail(ctx context.Context, in *GetIDPDetailRequest, opts ...grpc.CallOption) (*GetIDPDetailReply, error)
	ListRootGroup(ctx context.Context, in *ListRootGroupRequest, opts ...grpc.CallOption) (*ListRootGroupReply, error)
	GetRootGroupDetail(ctx context.Context, in *GetRootGroupDetailRequest, opts ...grpc.CallOption) (*GetRootGroupDetailReply, error)
	CreateRootGroup(ctx context.Context, in *CreateRootGroupRequest, opts ...grpc.CallOption) (*CreateRootGroupReply, error)
	GetRootGroupIdpList(ctx context.Context, in *GetRootGroupIdpListRequest, opts ...grpc.CallOption) (*GetRootGroupIdpListReply, error)
	GetFieldMap(ctx context.Context, in *GetFieldMapRequest, opts ...grpc.CallOption) (*GetFieldMapReply, error)
	GetFieldOptions(ctx context.Context, in *GetFieldOptionsRequest, opts ...grpc.CallOption) (*GetFieldOptionsReply, error)
	UpdateRootGroup(ctx context.Context, in *UpdateRootGroupRequest, opts ...grpc.CallOption) (*UpdateRootGroupReply, error)
	UpdateRootGroupCustom(ctx context.Context, in *UpdateRootGroupRequest, opts ...grpc.CallOption) (*UpdateRootGroupReply, error)
	SwitchAutoSync(ctx context.Context, in *SwitchAutoSyncRequest, opts ...grpc.CallOption) (*SwitchAutoSyncReply, error)
	SyncTrigger(ctx context.Context, in *SyncTriggerRequest, opts ...grpc.CallOption) (*SyncTriggerReply, error)
	ListSyncLog(ctx context.Context, in *ListSyncLogRequest, opts ...grpc.CallOption) (*ListSyncLogReply, error)
	DeleteRootGroup(ctx context.Context, in *DeleteRootGroupRequest, opts ...grpc.CallOption) (*DeleteRootGroupReply, error)
	DeleteRootGroupCustom(ctx context.Context, in *DeleteRootGroupRequest, opts ...grpc.CallOption) (*DeleteRootGroupReply, error)
	ListUserGroup(ctx context.Context, in *ListUserGroupRequest, opts ...grpc.CallOption) (*ListUserGroupReply, error)
	CreateUserGroup(ctx context.Context, in *CreateUserGroupRequest, opts ...grpc.CallOption) (*CreateUserGroupReply, error)
	CreateUserGroupCustom(ctx context.Context, in *CreateUserGroupRequestCustom, opts ...grpc.CallOption) (*CreateUserGroupReply, error)
	UpdateUserGroup(ctx context.Context, in *UpdateUserGroupRequest, opts ...grpc.CallOption) (*UpdateUserGroupReply, error)
	CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserReply, error)
	CreateUserCustom(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserReply, error)
	DeleteUserCustom(ctx context.Context, in *CustomDeleteUserRequest, opts ...grpc.CallOption) (*DeleteUserReply, error)
	UpdateUserCustom(ctx context.Context, in *CustomUpdateUserRequest, opts ...grpc.CallOption) (*UpdateUserReply, error)
	ListUser(ctx context.Context, in *ListUserRequest, opts ...grpc.CallOption) (*ListUserReply, error)
	DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*DeleteUserReply, error)
	UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*UpdateUserReply, error)
	CreateRole(ctx context.Context, in *CreateRoleRequest, opts ...grpc.CallOption) (*CreateRoleReply, error)
	ListRole(ctx context.Context, in *ListRoleRequest, opts ...grpc.CallOption) (*ListRoleReply, error)
	// 闲置账号列表
	IdleAccountList(ctx context.Context, in *IdleAccountRequest, opts ...grpc.CallOption) (*IdleAccountReply, error)
	UpdateRole(ctx context.Context, in *UpdateRoleRequest, opts ...grpc.CallOption) (*UpdateRoleReply, error)
	DeleteRole(ctx context.Context, in *DeleteRoleRequest, opts ...grpc.CallOption) (*DeleteRoleReply, error)
	CreateAuthPolicy(ctx context.Context, in *CreateAuthPolicyRequest, opts ...grpc.CallOption) (*CreateAuthPolicyReply, error)
	UpdateAuthPolicy(ctx context.Context, in *UpdateAuthPolicyRequest, opts ...grpc.CallOption) (*UpdateAuthPolicyReply, error)
	ListAuthPolicy(ctx context.Context, in *ListAuthPolicyRequest, opts ...grpc.CallOption) (*ListAuthPolicyReply, error)
	DeleteAuthPolicy(ctx context.Context, in *DeleteAuthPolicyRequest, opts ...grpc.CallOption) (*DeleteAuthPolicyReply, error)
	// 账户策略管理接口
	CreateAccountPolicy(ctx context.Context, in *CreateAccountPolicyRequest, opts ...grpc.CallOption) (*CreateAccountPolicyReply, error)
	UpdateAccountPolicy(ctx context.Context, in *UpdateAccountPolicyRequest, opts ...grpc.CallOption) (*UpdateAccountPolicyReply, error)
	DeleteAccountPolicy(ctx context.Context, in *DeleteAccountPolicyRequest, opts ...grpc.CallOption) (*DeleteAccountPolicyReply, error)
	GetAccountPolicy(ctx context.Context, in *GetAccountPolicyRequest, opts ...grpc.CallOption) (*GetAccountPolicyReply, error)
	ListAccountPolicies(ctx context.Context, in *ListAccountPoliciesRequest, opts ...grpc.CallOption) (*ListAccountPoliciesReply, error)
	// 账户锁定管理接口
	UnlockAccount(ctx context.Context, in *UnlockAccountRequest, opts ...grpc.CallOption) (*UnlockAccountReply, error)
	BatchUnlockAccounts(ctx context.Context, in *BatchUnlockAccountsRequest, opts ...grpc.CallOption) (*BatchUnlockAccountsReply, error)
	GetAccountLockInfo(ctx context.Context, in *GetAccountLockInfoRequest, opts ...grpc.CallOption) (*GetAccountLockInfoReply, error)
	ListLockedAccounts(ctx context.Context, in *ListLockedAccountsRequest, opts ...grpc.CallOption) (*ListLockedAccountsReply, error)
	// IP锁定管理接口
	UnlockIP(ctx context.Context, in *UnlockIPRequest, opts ...grpc.CallOption) (*UnlockIPReply, error)
	BatchUnlockIPs(ctx context.Context, in *BatchUnlockIPsRequest, opts ...grpc.CallOption) (*BatchUnlockIPsReply, error)
	GetIPLockInfo(ctx context.Context, in *GetIPLockInfoRequest, opts ...grpc.CallOption) (*GetIPLockInfoReply, error)
	ListLockedIPs(ctx context.Context, in *ListLockedIPsRequest, opts ...grpc.CallOption) (*ListLockedIPsReply, error)
	ValidateWebAuthScript(ctx context.Context, in *ValidateWebAuthScriptRequest, opts ...grpc.CallOption) (*ValidateWebAuthScriptReply, error)
	// 会话管理接口
	ListUserSessions(ctx context.Context, in *ListUserSessionsRequest, opts ...grpc.CallOption) (*ListUserSessionsReply, error)
	KickUserSession(ctx context.Context, in *KickUserSessionRequest, opts ...grpc.CallOption) (*KickUserSessionReply, error)
	GetClientLimits(ctx context.Context, in *GetClientLimitsRequest, opts ...grpc.CallOption) (*GetClientLimitsReply, error)
	UpdateClientLimits(ctx context.Context, in *UpdateClientLimitsRequest, opts ...grpc.CallOption) (*UpdateClientLimitsReply, error)
	GetUserCount(ctx context.Context, in *GetUserCountRequest, opts ...grpc.CallOption) (*GetUserCountReply, error)
}

type adminClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminClient(cc grpc.ClientConnInterface) AdminClient {
	return &adminClient{cc}
}

func (c *adminClient) CreateCorp(ctx context.Context, in *CreateCorpRequest, opts ...grpc.CallOption) (*CreateCorpReply, error) {
	out := new(CreateCorpReply)
	err := c.cc.Invoke(ctx, Admin_CreateCorp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateCorp(ctx context.Context, in *UpdateCorpRequest, opts ...grpc.CallOption) (*UpdateCorpReply, error) {
	out := new(UpdateCorpReply)
	err := c.cc.Invoke(ctx, Admin_UpdateCorp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateLockStatus(ctx context.Context, in *UpdateLockStatusRequest, opts ...grpc.CallOption) (*UpdateLockStatusReply, error) {
	out := new(UpdateLockStatusReply)
	err := c.cc.Invoke(ctx, Admin_UpdateLockStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateIdleTime(ctx context.Context, in *UpdateIdleTimeRequest, opts ...grpc.CallOption) (*UpdateIdleTimeReply, error) {
	out := new(UpdateIdleTimeReply)
	err := c.cc.Invoke(ctx, Admin_UpdateIdleTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) TotpUnbind(ctx context.Context, in *TotpUnbindRequest, opts ...grpc.CallOption) (*TotpUnbindTimeReply, error) {
	out := new(TotpUnbindTimeReply)
	err := c.cc.Invoke(ctx, Admin_TotpUnbind_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteCorp(ctx context.Context, in *DeleteCorpRequest, opts ...grpc.CallOption) (*DeleteCorpReply, error) {
	out := new(DeleteCorpReply)
	err := c.cc.Invoke(ctx, Admin_DeleteCorp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetCorp(ctx context.Context, in *GetCorpRequest, opts ...grpc.CallOption) (*GetCorpReply, error) {
	out := new(GetCorpReply)
	err := c.cc.Invoke(ctx, Admin_GetCorp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListCorp(ctx context.Context, in *ListCorpRequest, opts ...grpc.CallOption) (*ListCorpReply, error) {
	out := new(ListCorpReply)
	err := c.cc.Invoke(ctx, Admin_ListCorp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateUserSource(ctx context.Context, in *CreateUserSourceRequest, opts ...grpc.CallOption) (*CreateUserSourceReply, error) {
	out := new(CreateUserSourceReply)
	err := c.cc.Invoke(ctx, Admin_CreateUserSource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateUserSource(ctx context.Context, in *UpdateUserSourceRequest, opts ...grpc.CallOption) (*UpdateUserSourceReply, error) {
	out := new(UpdateUserSourceReply)
	err := c.cc.Invoke(ctx, Admin_UpdateUserSource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteUserSource(ctx context.Context, in *DeleteUserSourceRequest, opts ...grpc.CallOption) (*DeleteUserSourceReply, error) {
	out := new(DeleteUserSourceReply)
	err := c.cc.Invoke(ctx, Admin_DeleteUserSource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetUserSource(ctx context.Context, in *GetUserSourceRequest, opts ...grpc.CallOption) (*GetUserSourceReply, error) {
	out := new(GetUserSourceReply)
	err := c.cc.Invoke(ctx, Admin_GetUserSource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListUserSource(ctx context.Context, in *ListUserSourceRequest, opts ...grpc.CallOption) (*ListUserSourceReply, error) {
	out := new(ListUserSourceReply)
	err := c.cc.Invoke(ctx, Admin_ListUserSource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListUserSourceType(ctx context.Context, in *ListUserSourceTypeRequest, opts ...grpc.CallOption) (*ListUserSourceTypeReply, error) {
	out := new(ListUserSourceTypeReply)
	err := c.cc.Invoke(ctx, Admin_ListUserSourceType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListIDPType(ctx context.Context, in *ListIDPTypeRequest, opts ...grpc.CallOption) (*ListIDPTypeReply, error) {
	out := new(ListIDPTypeReply)
	err := c.cc.Invoke(ctx, Admin_ListIDPType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) OAuth2Test(ctx context.Context, in *OAuth2TestRequest, opts ...grpc.CallOption) (*OAuth2TestReply, error) {
	out := new(OAuth2TestReply)
	err := c.cc.Invoke(ctx, Admin_OAuth2Test_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) TestResult(ctx context.Context, in *TestResultRequest, opts ...grpc.CallOption) (*TestResultReply, error) {
	out := new(TestResultReply)
	err := c.cc.Invoke(ctx, Admin_TestResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateIDP(ctx context.Context, in *CreateIDPRequest, opts ...grpc.CallOption) (*CreateIDPReply, error) {
	out := new(CreateIDPReply)
	err := c.cc.Invoke(ctx, Admin_CreateIDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateIDP(ctx context.Context, in *UpdateIDPRequest, opts ...grpc.CallOption) (*UpdateIDPReply, error) {
	out := new(UpdateIDPReply)
	err := c.cc.Invoke(ctx, Admin_UpdateIDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteIDP(ctx context.Context, in *DeleteIDPRequest, opts ...grpc.CallOption) (*DeleteIDPReply, error) {
	out := new(DeleteIDPReply)
	err := c.cc.Invoke(ctx, Admin_DeleteIDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListIDP(ctx context.Context, in *ListIDPRequest, opts ...grpc.CallOption) (*ListIDPReply, error) {
	out := new(ListIDPReply)
	err := c.cc.Invoke(ctx, Admin_ListIDP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetIDPDetail(ctx context.Context, in *GetIDPDetailRequest, opts ...grpc.CallOption) (*GetIDPDetailReply, error) {
	out := new(GetIDPDetailReply)
	err := c.cc.Invoke(ctx, Admin_GetIDPDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListRootGroup(ctx context.Context, in *ListRootGroupRequest, opts ...grpc.CallOption) (*ListRootGroupReply, error) {
	out := new(ListRootGroupReply)
	err := c.cc.Invoke(ctx, Admin_ListRootGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetRootGroupDetail(ctx context.Context, in *GetRootGroupDetailRequest, opts ...grpc.CallOption) (*GetRootGroupDetailReply, error) {
	out := new(GetRootGroupDetailReply)
	err := c.cc.Invoke(ctx, Admin_GetRootGroupDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateRootGroup(ctx context.Context, in *CreateRootGroupRequest, opts ...grpc.CallOption) (*CreateRootGroupReply, error) {
	out := new(CreateRootGroupReply)
	err := c.cc.Invoke(ctx, Admin_CreateRootGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetRootGroupIdpList(ctx context.Context, in *GetRootGroupIdpListRequest, opts ...grpc.CallOption) (*GetRootGroupIdpListReply, error) {
	out := new(GetRootGroupIdpListReply)
	err := c.cc.Invoke(ctx, Admin_GetRootGroupIdpList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetFieldMap(ctx context.Context, in *GetFieldMapRequest, opts ...grpc.CallOption) (*GetFieldMapReply, error) {
	out := new(GetFieldMapReply)
	err := c.cc.Invoke(ctx, Admin_GetFieldMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetFieldOptions(ctx context.Context, in *GetFieldOptionsRequest, opts ...grpc.CallOption) (*GetFieldOptionsReply, error) {
	out := new(GetFieldOptionsReply)
	err := c.cc.Invoke(ctx, Admin_GetFieldOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateRootGroup(ctx context.Context, in *UpdateRootGroupRequest, opts ...grpc.CallOption) (*UpdateRootGroupReply, error) {
	out := new(UpdateRootGroupReply)
	err := c.cc.Invoke(ctx, Admin_UpdateRootGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateRootGroupCustom(ctx context.Context, in *UpdateRootGroupRequest, opts ...grpc.CallOption) (*UpdateRootGroupReply, error) {
	out := new(UpdateRootGroupReply)
	err := c.cc.Invoke(ctx, Admin_UpdateRootGroupCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) SwitchAutoSync(ctx context.Context, in *SwitchAutoSyncRequest, opts ...grpc.CallOption) (*SwitchAutoSyncReply, error) {
	out := new(SwitchAutoSyncReply)
	err := c.cc.Invoke(ctx, Admin_SwitchAutoSync_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) SyncTrigger(ctx context.Context, in *SyncTriggerRequest, opts ...grpc.CallOption) (*SyncTriggerReply, error) {
	out := new(SyncTriggerReply)
	err := c.cc.Invoke(ctx, Admin_SyncTrigger_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListSyncLog(ctx context.Context, in *ListSyncLogRequest, opts ...grpc.CallOption) (*ListSyncLogReply, error) {
	out := new(ListSyncLogReply)
	err := c.cc.Invoke(ctx, Admin_ListSyncLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteRootGroup(ctx context.Context, in *DeleteRootGroupRequest, opts ...grpc.CallOption) (*DeleteRootGroupReply, error) {
	out := new(DeleteRootGroupReply)
	err := c.cc.Invoke(ctx, Admin_DeleteRootGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteRootGroupCustom(ctx context.Context, in *DeleteRootGroupRequest, opts ...grpc.CallOption) (*DeleteRootGroupReply, error) {
	out := new(DeleteRootGroupReply)
	err := c.cc.Invoke(ctx, Admin_DeleteRootGroupCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListUserGroup(ctx context.Context, in *ListUserGroupRequest, opts ...grpc.CallOption) (*ListUserGroupReply, error) {
	out := new(ListUserGroupReply)
	err := c.cc.Invoke(ctx, Admin_ListUserGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateUserGroup(ctx context.Context, in *CreateUserGroupRequest, opts ...grpc.CallOption) (*CreateUserGroupReply, error) {
	out := new(CreateUserGroupReply)
	err := c.cc.Invoke(ctx, Admin_CreateUserGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateUserGroupCustom(ctx context.Context, in *CreateUserGroupRequestCustom, opts ...grpc.CallOption) (*CreateUserGroupReply, error) {
	out := new(CreateUserGroupReply)
	err := c.cc.Invoke(ctx, Admin_CreateUserGroupCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateUserGroup(ctx context.Context, in *UpdateUserGroupRequest, opts ...grpc.CallOption) (*UpdateUserGroupReply, error) {
	out := new(UpdateUserGroupReply)
	err := c.cc.Invoke(ctx, Admin_UpdateUserGroup_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserReply, error) {
	out := new(CreateUserReply)
	err := c.cc.Invoke(ctx, Admin_CreateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateUserCustom(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserReply, error) {
	out := new(CreateUserReply)
	err := c.cc.Invoke(ctx, Admin_CreateUserCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteUserCustom(ctx context.Context, in *CustomDeleteUserRequest, opts ...grpc.CallOption) (*DeleteUserReply, error) {
	out := new(DeleteUserReply)
	err := c.cc.Invoke(ctx, Admin_DeleteUserCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateUserCustom(ctx context.Context, in *CustomUpdateUserRequest, opts ...grpc.CallOption) (*UpdateUserReply, error) {
	out := new(UpdateUserReply)
	err := c.cc.Invoke(ctx, Admin_UpdateUserCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListUser(ctx context.Context, in *ListUserRequest, opts ...grpc.CallOption) (*ListUserReply, error) {
	out := new(ListUserReply)
	err := c.cc.Invoke(ctx, Admin_ListUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*DeleteUserReply, error) {
	out := new(DeleteUserReply)
	err := c.cc.Invoke(ctx, Admin_DeleteUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*UpdateUserReply, error) {
	out := new(UpdateUserReply)
	err := c.cc.Invoke(ctx, Admin_UpdateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateRole(ctx context.Context, in *CreateRoleRequest, opts ...grpc.CallOption) (*CreateRoleReply, error) {
	out := new(CreateRoleReply)
	err := c.cc.Invoke(ctx, Admin_CreateRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListRole(ctx context.Context, in *ListRoleRequest, opts ...grpc.CallOption) (*ListRoleReply, error) {
	out := new(ListRoleReply)
	err := c.cc.Invoke(ctx, Admin_ListRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) IdleAccountList(ctx context.Context, in *IdleAccountRequest, opts ...grpc.CallOption) (*IdleAccountReply, error) {
	out := new(IdleAccountReply)
	err := c.cc.Invoke(ctx, Admin_IdleAccountList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateRole(ctx context.Context, in *UpdateRoleRequest, opts ...grpc.CallOption) (*UpdateRoleReply, error) {
	out := new(UpdateRoleReply)
	err := c.cc.Invoke(ctx, Admin_UpdateRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteRole(ctx context.Context, in *DeleteRoleRequest, opts ...grpc.CallOption) (*DeleteRoleReply, error) {
	out := new(DeleteRoleReply)
	err := c.cc.Invoke(ctx, Admin_DeleteRole_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateAuthPolicy(ctx context.Context, in *CreateAuthPolicyRequest, opts ...grpc.CallOption) (*CreateAuthPolicyReply, error) {
	out := new(CreateAuthPolicyReply)
	err := c.cc.Invoke(ctx, Admin_CreateAuthPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateAuthPolicy(ctx context.Context, in *UpdateAuthPolicyRequest, opts ...grpc.CallOption) (*UpdateAuthPolicyReply, error) {
	out := new(UpdateAuthPolicyReply)
	err := c.cc.Invoke(ctx, Admin_UpdateAuthPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListAuthPolicy(ctx context.Context, in *ListAuthPolicyRequest, opts ...grpc.CallOption) (*ListAuthPolicyReply, error) {
	out := new(ListAuthPolicyReply)
	err := c.cc.Invoke(ctx, Admin_ListAuthPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteAuthPolicy(ctx context.Context, in *DeleteAuthPolicyRequest, opts ...grpc.CallOption) (*DeleteAuthPolicyReply, error) {
	out := new(DeleteAuthPolicyReply)
	err := c.cc.Invoke(ctx, Admin_DeleteAuthPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) CreateAccountPolicy(ctx context.Context, in *CreateAccountPolicyRequest, opts ...grpc.CallOption) (*CreateAccountPolicyReply, error) {
	out := new(CreateAccountPolicyReply)
	err := c.cc.Invoke(ctx, Admin_CreateAccountPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateAccountPolicy(ctx context.Context, in *UpdateAccountPolicyRequest, opts ...grpc.CallOption) (*UpdateAccountPolicyReply, error) {
	out := new(UpdateAccountPolicyReply)
	err := c.cc.Invoke(ctx, Admin_UpdateAccountPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) DeleteAccountPolicy(ctx context.Context, in *DeleteAccountPolicyRequest, opts ...grpc.CallOption) (*DeleteAccountPolicyReply, error) {
	out := new(DeleteAccountPolicyReply)
	err := c.cc.Invoke(ctx, Admin_DeleteAccountPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetAccountPolicy(ctx context.Context, in *GetAccountPolicyRequest, opts ...grpc.CallOption) (*GetAccountPolicyReply, error) {
	out := new(GetAccountPolicyReply)
	err := c.cc.Invoke(ctx, Admin_GetAccountPolicy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListAccountPolicies(ctx context.Context, in *ListAccountPoliciesRequest, opts ...grpc.CallOption) (*ListAccountPoliciesReply, error) {
	out := new(ListAccountPoliciesReply)
	err := c.cc.Invoke(ctx, Admin_ListAccountPolicies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UnlockAccount(ctx context.Context, in *UnlockAccountRequest, opts ...grpc.CallOption) (*UnlockAccountReply, error) {
	out := new(UnlockAccountReply)
	err := c.cc.Invoke(ctx, Admin_UnlockAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) BatchUnlockAccounts(ctx context.Context, in *BatchUnlockAccountsRequest, opts ...grpc.CallOption) (*BatchUnlockAccountsReply, error) {
	out := new(BatchUnlockAccountsReply)
	err := c.cc.Invoke(ctx, Admin_BatchUnlockAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetAccountLockInfo(ctx context.Context, in *GetAccountLockInfoRequest, opts ...grpc.CallOption) (*GetAccountLockInfoReply, error) {
	out := new(GetAccountLockInfoReply)
	err := c.cc.Invoke(ctx, Admin_GetAccountLockInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListLockedAccounts(ctx context.Context, in *ListLockedAccountsRequest, opts ...grpc.CallOption) (*ListLockedAccountsReply, error) {
	out := new(ListLockedAccountsReply)
	err := c.cc.Invoke(ctx, Admin_ListLockedAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UnlockIP(ctx context.Context, in *UnlockIPRequest, opts ...grpc.CallOption) (*UnlockIPReply, error) {
	out := new(UnlockIPReply)
	err := c.cc.Invoke(ctx, Admin_UnlockIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) BatchUnlockIPs(ctx context.Context, in *BatchUnlockIPsRequest, opts ...grpc.CallOption) (*BatchUnlockIPsReply, error) {
	out := new(BatchUnlockIPsReply)
	err := c.cc.Invoke(ctx, Admin_BatchUnlockIPs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetIPLockInfo(ctx context.Context, in *GetIPLockInfoRequest, opts ...grpc.CallOption) (*GetIPLockInfoReply, error) {
	out := new(GetIPLockInfoReply)
	err := c.cc.Invoke(ctx, Admin_GetIPLockInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListLockedIPs(ctx context.Context, in *ListLockedIPsRequest, opts ...grpc.CallOption) (*ListLockedIPsReply, error) {
	out := new(ListLockedIPsReply)
	err := c.cc.Invoke(ctx, Admin_ListLockedIPs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ValidateWebAuthScript(ctx context.Context, in *ValidateWebAuthScriptRequest, opts ...grpc.CallOption) (*ValidateWebAuthScriptReply, error) {
	out := new(ValidateWebAuthScriptReply)
	err := c.cc.Invoke(ctx, Admin_ValidateWebAuthScript_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ListUserSessions(ctx context.Context, in *ListUserSessionsRequest, opts ...grpc.CallOption) (*ListUserSessionsReply, error) {
	out := new(ListUserSessionsReply)
	err := c.cc.Invoke(ctx, Admin_ListUserSessions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) KickUserSession(ctx context.Context, in *KickUserSessionRequest, opts ...grpc.CallOption) (*KickUserSessionReply, error) {
	out := new(KickUserSessionReply)
	err := c.cc.Invoke(ctx, Admin_KickUserSession_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetClientLimits(ctx context.Context, in *GetClientLimitsRequest, opts ...grpc.CallOption) (*GetClientLimitsReply, error) {
	out := new(GetClientLimitsReply)
	err := c.cc.Invoke(ctx, Admin_GetClientLimits_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) UpdateClientLimits(ctx context.Context, in *UpdateClientLimitsRequest, opts ...grpc.CallOption) (*UpdateClientLimitsReply, error) {
	out := new(UpdateClientLimitsReply)
	err := c.cc.Invoke(ctx, Admin_UpdateClientLimits_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) GetUserCount(ctx context.Context, in *GetUserCountRequest, opts ...grpc.CallOption) (*GetUserCountReply, error) {
	out := new(GetUserCountReply)
	err := c.cc.Invoke(ctx, Admin_GetUserCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminServer is the server API for Admin service.
// All implementations must embed UnimplementedAdminServer
// for forward compatibility
type AdminServer interface {
	CreateCorp(context.Context, *CreateCorpRequest) (*CreateCorpReply, error)
	UpdateCorp(context.Context, *UpdateCorpRequest) (*UpdateCorpReply, error)
	UpdateLockStatus(context.Context, *UpdateLockStatusRequest) (*UpdateLockStatusReply, error)
	UpdateIdleTime(context.Context, *UpdateIdleTimeRequest) (*UpdateIdleTimeReply, error)
	TotpUnbind(context.Context, *TotpUnbindRequest) (*TotpUnbindTimeReply, error)
	DeleteCorp(context.Context, *DeleteCorpRequest) (*DeleteCorpReply, error)
	GetCorp(context.Context, *GetCorpRequest) (*GetCorpReply, error)
	ListCorp(context.Context, *ListCorpRequest) (*ListCorpReply, error)
	CreateUserSource(context.Context, *CreateUserSourceRequest) (*CreateUserSourceReply, error)
	UpdateUserSource(context.Context, *UpdateUserSourceRequest) (*UpdateUserSourceReply, error)
	DeleteUserSource(context.Context, *DeleteUserSourceRequest) (*DeleteUserSourceReply, error)
	GetUserSource(context.Context, *GetUserSourceRequest) (*GetUserSourceReply, error)
	ListUserSource(context.Context, *ListUserSourceRequest) (*ListUserSourceReply, error)
	ListUserSourceType(context.Context, *ListUserSourceTypeRequest) (*ListUserSourceTypeReply, error)
	ListIDPType(context.Context, *ListIDPTypeRequest) (*ListIDPTypeReply, error)
	OAuth2Test(context.Context, *OAuth2TestRequest) (*OAuth2TestReply, error)
	TestResult(context.Context, *TestResultRequest) (*TestResultReply, error)
	CreateIDP(context.Context, *CreateIDPRequest) (*CreateIDPReply, error)
	UpdateIDP(context.Context, *UpdateIDPRequest) (*UpdateIDPReply, error)
	DeleteIDP(context.Context, *DeleteIDPRequest) (*DeleteIDPReply, error)
	ListIDP(context.Context, *ListIDPRequest) (*ListIDPReply, error)
	GetIDPDetail(context.Context, *GetIDPDetailRequest) (*GetIDPDetailReply, error)
	ListRootGroup(context.Context, *ListRootGroupRequest) (*ListRootGroupReply, error)
	GetRootGroupDetail(context.Context, *GetRootGroupDetailRequest) (*GetRootGroupDetailReply, error)
	CreateRootGroup(context.Context, *CreateRootGroupRequest) (*CreateRootGroupReply, error)
	GetRootGroupIdpList(context.Context, *GetRootGroupIdpListRequest) (*GetRootGroupIdpListReply, error)
	GetFieldMap(context.Context, *GetFieldMapRequest) (*GetFieldMapReply, error)
	GetFieldOptions(context.Context, *GetFieldOptionsRequest) (*GetFieldOptionsReply, error)
	UpdateRootGroup(context.Context, *UpdateRootGroupRequest) (*UpdateRootGroupReply, error)
	UpdateRootGroupCustom(context.Context, *UpdateRootGroupRequest) (*UpdateRootGroupReply, error)
	SwitchAutoSync(context.Context, *SwitchAutoSyncRequest) (*SwitchAutoSyncReply, error)
	SyncTrigger(context.Context, *SyncTriggerRequest) (*SyncTriggerReply, error)
	ListSyncLog(context.Context, *ListSyncLogRequest) (*ListSyncLogReply, error)
	DeleteRootGroup(context.Context, *DeleteRootGroupRequest) (*DeleteRootGroupReply, error)
	DeleteRootGroupCustom(context.Context, *DeleteRootGroupRequest) (*DeleteRootGroupReply, error)
	ListUserGroup(context.Context, *ListUserGroupRequest) (*ListUserGroupReply, error)
	CreateUserGroup(context.Context, *CreateUserGroupRequest) (*CreateUserGroupReply, error)
	CreateUserGroupCustom(context.Context, *CreateUserGroupRequestCustom) (*CreateUserGroupReply, error)
	UpdateUserGroup(context.Context, *UpdateUserGroupRequest) (*UpdateUserGroupReply, error)
	CreateUser(context.Context, *CreateUserRequest) (*CreateUserReply, error)
	CreateUserCustom(context.Context, *CreateUserRequest) (*CreateUserReply, error)
	DeleteUserCustom(context.Context, *CustomDeleteUserRequest) (*DeleteUserReply, error)
	UpdateUserCustom(context.Context, *CustomUpdateUserRequest) (*UpdateUserReply, error)
	ListUser(context.Context, *ListUserRequest) (*ListUserReply, error)
	DeleteUser(context.Context, *DeleteUserRequest) (*DeleteUserReply, error)
	UpdateUser(context.Context, *UpdateUserRequest) (*UpdateUserReply, error)
	CreateRole(context.Context, *CreateRoleRequest) (*CreateRoleReply, error)
	ListRole(context.Context, *ListRoleRequest) (*ListRoleReply, error)
	// 闲置账号列表
	IdleAccountList(context.Context, *IdleAccountRequest) (*IdleAccountReply, error)
	UpdateRole(context.Context, *UpdateRoleRequest) (*UpdateRoleReply, error)
	DeleteRole(context.Context, *DeleteRoleRequest) (*DeleteRoleReply, error)
	CreateAuthPolicy(context.Context, *CreateAuthPolicyRequest) (*CreateAuthPolicyReply, error)
	UpdateAuthPolicy(context.Context, *UpdateAuthPolicyRequest) (*UpdateAuthPolicyReply, error)
	ListAuthPolicy(context.Context, *ListAuthPolicyRequest) (*ListAuthPolicyReply, error)
	DeleteAuthPolicy(context.Context, *DeleteAuthPolicyRequest) (*DeleteAuthPolicyReply, error)
	// 账户策略管理接口
	CreateAccountPolicy(context.Context, *CreateAccountPolicyRequest) (*CreateAccountPolicyReply, error)
	UpdateAccountPolicy(context.Context, *UpdateAccountPolicyRequest) (*UpdateAccountPolicyReply, error)
	DeleteAccountPolicy(context.Context, *DeleteAccountPolicyRequest) (*DeleteAccountPolicyReply, error)
	GetAccountPolicy(context.Context, *GetAccountPolicyRequest) (*GetAccountPolicyReply, error)
	ListAccountPolicies(context.Context, *ListAccountPoliciesRequest) (*ListAccountPoliciesReply, error)
	// 账户锁定管理接口
	UnlockAccount(context.Context, *UnlockAccountRequest) (*UnlockAccountReply, error)
	BatchUnlockAccounts(context.Context, *BatchUnlockAccountsRequest) (*BatchUnlockAccountsReply, error)
	GetAccountLockInfo(context.Context, *GetAccountLockInfoRequest) (*GetAccountLockInfoReply, error)
	ListLockedAccounts(context.Context, *ListLockedAccountsRequest) (*ListLockedAccountsReply, error)
	// IP锁定管理接口
	UnlockIP(context.Context, *UnlockIPRequest) (*UnlockIPReply, error)
	BatchUnlockIPs(context.Context, *BatchUnlockIPsRequest) (*BatchUnlockIPsReply, error)
	GetIPLockInfo(context.Context, *GetIPLockInfoRequest) (*GetIPLockInfoReply, error)
	ListLockedIPs(context.Context, *ListLockedIPsRequest) (*ListLockedIPsReply, error)
	ValidateWebAuthScript(context.Context, *ValidateWebAuthScriptRequest) (*ValidateWebAuthScriptReply, error)
	// 会话管理接口
	ListUserSessions(context.Context, *ListUserSessionsRequest) (*ListUserSessionsReply, error)
	KickUserSession(context.Context, *KickUserSessionRequest) (*KickUserSessionReply, error)
	GetClientLimits(context.Context, *GetClientLimitsRequest) (*GetClientLimitsReply, error)
	UpdateClientLimits(context.Context, *UpdateClientLimitsRequest) (*UpdateClientLimitsReply, error)
	GetUserCount(context.Context, *GetUserCountRequest) (*GetUserCountReply, error)
	mustEmbedUnimplementedAdminServer()
}

// UnimplementedAdminServer must be embedded to have forward compatible implementations.
type UnimplementedAdminServer struct {
}

func (UnimplementedAdminServer) CreateCorp(context.Context, *CreateCorpRequest) (*CreateCorpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCorp not implemented")
}
func (UnimplementedAdminServer) UpdateCorp(context.Context, *UpdateCorpRequest) (*UpdateCorpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCorp not implemented")
}
func (UnimplementedAdminServer) UpdateLockStatus(context.Context, *UpdateLockStatusRequest) (*UpdateLockStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLockStatus not implemented")
}
func (UnimplementedAdminServer) UpdateIdleTime(context.Context, *UpdateIdleTimeRequest) (*UpdateIdleTimeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIdleTime not implemented")
}
func (UnimplementedAdminServer) TotpUnbind(context.Context, *TotpUnbindRequest) (*TotpUnbindTimeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TotpUnbind not implemented")
}
func (UnimplementedAdminServer) DeleteCorp(context.Context, *DeleteCorpRequest) (*DeleteCorpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCorp not implemented")
}
func (UnimplementedAdminServer) GetCorp(context.Context, *GetCorpRequest) (*GetCorpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCorp not implemented")
}
func (UnimplementedAdminServer) ListCorp(context.Context, *ListCorpRequest) (*ListCorpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCorp not implemented")
}
func (UnimplementedAdminServer) CreateUserSource(context.Context, *CreateUserSourceRequest) (*CreateUserSourceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserSource not implemented")
}
func (UnimplementedAdminServer) UpdateUserSource(context.Context, *UpdateUserSourceRequest) (*UpdateUserSourceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserSource not implemented")
}
func (UnimplementedAdminServer) DeleteUserSource(context.Context, *DeleteUserSourceRequest) (*DeleteUserSourceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserSource not implemented")
}
func (UnimplementedAdminServer) GetUserSource(context.Context, *GetUserSourceRequest) (*GetUserSourceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSource not implemented")
}
func (UnimplementedAdminServer) ListUserSource(context.Context, *ListUserSourceRequest) (*ListUserSourceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserSource not implemented")
}
func (UnimplementedAdminServer) ListUserSourceType(context.Context, *ListUserSourceTypeRequest) (*ListUserSourceTypeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserSourceType not implemented")
}
func (UnimplementedAdminServer) ListIDPType(context.Context, *ListIDPTypeRequest) (*ListIDPTypeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListIDPType not implemented")
}
func (UnimplementedAdminServer) OAuth2Test(context.Context, *OAuth2TestRequest) (*OAuth2TestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OAuth2Test not implemented")
}
func (UnimplementedAdminServer) TestResult(context.Context, *TestResultRequest) (*TestResultReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TestResult not implemented")
}
func (UnimplementedAdminServer) CreateIDP(context.Context, *CreateIDPRequest) (*CreateIDPReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIDP not implemented")
}
func (UnimplementedAdminServer) UpdateIDP(context.Context, *UpdateIDPRequest) (*UpdateIDPReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIDP not implemented")
}
func (UnimplementedAdminServer) DeleteIDP(context.Context, *DeleteIDPRequest) (*DeleteIDPReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIDP not implemented")
}
func (UnimplementedAdminServer) ListIDP(context.Context, *ListIDPRequest) (*ListIDPReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListIDP not implemented")
}
func (UnimplementedAdminServer) GetIDPDetail(context.Context, *GetIDPDetailRequest) (*GetIDPDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIDPDetail not implemented")
}
func (UnimplementedAdminServer) ListRootGroup(context.Context, *ListRootGroupRequest) (*ListRootGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRootGroup not implemented")
}
func (UnimplementedAdminServer) GetRootGroupDetail(context.Context, *GetRootGroupDetailRequest) (*GetRootGroupDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRootGroupDetail not implemented")
}
func (UnimplementedAdminServer) CreateRootGroup(context.Context, *CreateRootGroupRequest) (*CreateRootGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRootGroup not implemented")
}
func (UnimplementedAdminServer) GetRootGroupIdpList(context.Context, *GetRootGroupIdpListRequest) (*GetRootGroupIdpListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRootGroupIdpList not implemented")
}
func (UnimplementedAdminServer) GetFieldMap(context.Context, *GetFieldMapRequest) (*GetFieldMapReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFieldMap not implemented")
}
func (UnimplementedAdminServer) GetFieldOptions(context.Context, *GetFieldOptionsRequest) (*GetFieldOptionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFieldOptions not implemented")
}
func (UnimplementedAdminServer) UpdateRootGroup(context.Context, *UpdateRootGroupRequest) (*UpdateRootGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRootGroup not implemented")
}
func (UnimplementedAdminServer) UpdateRootGroupCustom(context.Context, *UpdateRootGroupRequest) (*UpdateRootGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRootGroupCustom not implemented")
}
func (UnimplementedAdminServer) SwitchAutoSync(context.Context, *SwitchAutoSyncRequest) (*SwitchAutoSyncReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwitchAutoSync not implemented")
}
func (UnimplementedAdminServer) SyncTrigger(context.Context, *SyncTriggerRequest) (*SyncTriggerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncTrigger not implemented")
}
func (UnimplementedAdminServer) ListSyncLog(context.Context, *ListSyncLogRequest) (*ListSyncLogReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSyncLog not implemented")
}
func (UnimplementedAdminServer) DeleteRootGroup(context.Context, *DeleteRootGroupRequest) (*DeleteRootGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRootGroup not implemented")
}
func (UnimplementedAdminServer) DeleteRootGroupCustom(context.Context, *DeleteRootGroupRequest) (*DeleteRootGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRootGroupCustom not implemented")
}
func (UnimplementedAdminServer) ListUserGroup(context.Context, *ListUserGroupRequest) (*ListUserGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserGroup not implemented")
}
func (UnimplementedAdminServer) CreateUserGroup(context.Context, *CreateUserGroupRequest) (*CreateUserGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserGroup not implemented")
}
func (UnimplementedAdminServer) CreateUserGroupCustom(context.Context, *CreateUserGroupRequestCustom) (*CreateUserGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserGroupCustom not implemented")
}
func (UnimplementedAdminServer) UpdateUserGroup(context.Context, *UpdateUserGroupRequest) (*UpdateUserGroupReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserGroup not implemented")
}
func (UnimplementedAdminServer) CreateUser(context.Context, *CreateUserRequest) (*CreateUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedAdminServer) CreateUserCustom(context.Context, *CreateUserRequest) (*CreateUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserCustom not implemented")
}
func (UnimplementedAdminServer) DeleteUserCustom(context.Context, *CustomDeleteUserRequest) (*DeleteUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserCustom not implemented")
}
func (UnimplementedAdminServer) UpdateUserCustom(context.Context, *CustomUpdateUserRequest) (*UpdateUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserCustom not implemented")
}
func (UnimplementedAdminServer) ListUser(context.Context, *ListUserRequest) (*ListUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUser not implemented")
}
func (UnimplementedAdminServer) DeleteUser(context.Context, *DeleteUserRequest) (*DeleteUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedAdminServer) UpdateUser(context.Context, *UpdateUserRequest) (*UpdateUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUser not implemented")
}
func (UnimplementedAdminServer) CreateRole(context.Context, *CreateRoleRequest) (*CreateRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRole not implemented")
}
func (UnimplementedAdminServer) ListRole(context.Context, *ListRoleRequest) (*ListRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRole not implemented")
}
func (UnimplementedAdminServer) IdleAccountList(context.Context, *IdleAccountRequest) (*IdleAccountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IdleAccountList not implemented")
}
func (UnimplementedAdminServer) UpdateRole(context.Context, *UpdateRoleRequest) (*UpdateRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRole not implemented")
}
func (UnimplementedAdminServer) DeleteRole(context.Context, *DeleteRoleRequest) (*DeleteRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRole not implemented")
}
func (UnimplementedAdminServer) CreateAuthPolicy(context.Context, *CreateAuthPolicyRequest) (*CreateAuthPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuthPolicy not implemented")
}
func (UnimplementedAdminServer) UpdateAuthPolicy(context.Context, *UpdateAuthPolicyRequest) (*UpdateAuthPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuthPolicy not implemented")
}
func (UnimplementedAdminServer) ListAuthPolicy(context.Context, *ListAuthPolicyRequest) (*ListAuthPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuthPolicy not implemented")
}
func (UnimplementedAdminServer) DeleteAuthPolicy(context.Context, *DeleteAuthPolicyRequest) (*DeleteAuthPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAuthPolicy not implemented")
}
func (UnimplementedAdminServer) CreateAccountPolicy(context.Context, *CreateAccountPolicyRequest) (*CreateAccountPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccountPolicy not implemented")
}
func (UnimplementedAdminServer) UpdateAccountPolicy(context.Context, *UpdateAccountPolicyRequest) (*UpdateAccountPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccountPolicy not implemented")
}
func (UnimplementedAdminServer) DeleteAccountPolicy(context.Context, *DeleteAccountPolicyRequest) (*DeleteAccountPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccountPolicy not implemented")
}
func (UnimplementedAdminServer) GetAccountPolicy(context.Context, *GetAccountPolicyRequest) (*GetAccountPolicyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountPolicy not implemented")
}
func (UnimplementedAdminServer) ListAccountPolicies(context.Context, *ListAccountPoliciesRequest) (*ListAccountPoliciesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccountPolicies not implemented")
}
func (UnimplementedAdminServer) UnlockAccount(context.Context, *UnlockAccountRequest) (*UnlockAccountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlockAccount not implemented")
}
func (UnimplementedAdminServer) BatchUnlockAccounts(context.Context, *BatchUnlockAccountsRequest) (*BatchUnlockAccountsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUnlockAccounts not implemented")
}
func (UnimplementedAdminServer) GetAccountLockInfo(context.Context, *GetAccountLockInfoRequest) (*GetAccountLockInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountLockInfo not implemented")
}
func (UnimplementedAdminServer) ListLockedAccounts(context.Context, *ListLockedAccountsRequest) (*ListLockedAccountsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLockedAccounts not implemented")
}
func (UnimplementedAdminServer) UnlockIP(context.Context, *UnlockIPRequest) (*UnlockIPReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlockIP not implemented")
}
func (UnimplementedAdminServer) BatchUnlockIPs(context.Context, *BatchUnlockIPsRequest) (*BatchUnlockIPsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUnlockIPs not implemented")
}
func (UnimplementedAdminServer) GetIPLockInfo(context.Context, *GetIPLockInfoRequest) (*GetIPLockInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIPLockInfo not implemented")
}
func (UnimplementedAdminServer) ListLockedIPs(context.Context, *ListLockedIPsRequest) (*ListLockedIPsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLockedIPs not implemented")
}
func (UnimplementedAdminServer) ValidateWebAuthScript(context.Context, *ValidateWebAuthScriptRequest) (*ValidateWebAuthScriptReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateWebAuthScript not implemented")
}
func (UnimplementedAdminServer) ListUserSessions(context.Context, *ListUserSessionsRequest) (*ListUserSessionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserSessions not implemented")
}
func (UnimplementedAdminServer) KickUserSession(context.Context, *KickUserSessionRequest) (*KickUserSessionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KickUserSession not implemented")
}
func (UnimplementedAdminServer) GetClientLimits(context.Context, *GetClientLimitsRequest) (*GetClientLimitsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClientLimits not implemented")
}
func (UnimplementedAdminServer) UpdateClientLimits(context.Context, *UpdateClientLimitsRequest) (*UpdateClientLimitsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClientLimits not implemented")
}
func (UnimplementedAdminServer) GetUserCount(context.Context, *GetUserCountRequest) (*GetUserCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserCount not implemented")
}
func (UnimplementedAdminServer) mustEmbedUnimplementedAdminServer() {}

// UnsafeAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminServer will
// result in compilation errors.
type UnsafeAdminServer interface {
	mustEmbedUnimplementedAdminServer()
}

func RegisterAdminServer(s grpc.ServiceRegistrar, srv AdminServer) {
	s.RegisterService(&Admin_ServiceDesc, srv)
}

func _Admin_CreateCorp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCorpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateCorp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateCorp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateCorp(ctx, req.(*CreateCorpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateCorp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCorpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateCorp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateCorp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateCorp(ctx, req.(*UpdateCorpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateLockStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLockStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateLockStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateLockStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateLockStatus(ctx, req.(*UpdateLockStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateIdleTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIdleTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateIdleTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateIdleTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateIdleTime(ctx, req.(*UpdateIdleTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_TotpUnbind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TotpUnbindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).TotpUnbind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_TotpUnbind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).TotpUnbind(ctx, req.(*TotpUnbindRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteCorp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCorpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteCorp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteCorp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteCorp(ctx, req.(*DeleteCorpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetCorp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCorpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetCorp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetCorp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetCorp(ctx, req.(*GetCorpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListCorp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCorpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListCorp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListCorp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListCorp(ctx, req.(*ListCorpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateUserSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateUserSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateUserSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateUserSource(ctx, req.(*CreateUserSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateUserSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateUserSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateUserSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateUserSource(ctx, req.(*UpdateUserSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteUserSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteUserSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteUserSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteUserSource(ctx, req.(*DeleteUserSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetUserSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetUserSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetUserSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetUserSource(ctx, req.(*GetUserSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListUserSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListUserSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListUserSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListUserSource(ctx, req.(*ListUserSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListUserSourceType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserSourceTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListUserSourceType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListUserSourceType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListUserSourceType(ctx, req.(*ListUserSourceTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListIDPType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListIDPTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListIDPType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListIDPType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListIDPType(ctx, req.(*ListIDPTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_OAuth2Test_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OAuth2TestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).OAuth2Test(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_OAuth2Test_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).OAuth2Test(ctx, req.(*OAuth2TestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_TestResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).TestResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_TestResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).TestResult(ctx, req.(*TestResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateIDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIDPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateIDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateIDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateIDP(ctx, req.(*CreateIDPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateIDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIDPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateIDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateIDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateIDP(ctx, req.(*UpdateIDPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteIDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIDPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteIDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteIDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteIDP(ctx, req.(*DeleteIDPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListIDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListIDPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListIDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListIDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListIDP(ctx, req.(*ListIDPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetIDPDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIDPDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetIDPDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetIDPDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetIDPDetail(ctx, req.(*GetIDPDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListRootGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRootGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListRootGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListRootGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListRootGroup(ctx, req.(*ListRootGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetRootGroupDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRootGroupDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetRootGroupDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetRootGroupDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetRootGroupDetail(ctx, req.(*GetRootGroupDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateRootGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRootGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateRootGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateRootGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateRootGroup(ctx, req.(*CreateRootGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetRootGroupIdpList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRootGroupIdpListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetRootGroupIdpList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetRootGroupIdpList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetRootGroupIdpList(ctx, req.(*GetRootGroupIdpListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetFieldMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFieldMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetFieldMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetFieldMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetFieldMap(ctx, req.(*GetFieldMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetFieldOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFieldOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetFieldOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetFieldOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetFieldOptions(ctx, req.(*GetFieldOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateRootGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRootGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateRootGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateRootGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateRootGroup(ctx, req.(*UpdateRootGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateRootGroupCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRootGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateRootGroupCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateRootGroupCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateRootGroupCustom(ctx, req.(*UpdateRootGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_SwitchAutoSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchAutoSyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).SwitchAutoSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_SwitchAutoSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).SwitchAutoSync(ctx, req.(*SwitchAutoSyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_SyncTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncTriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).SyncTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_SyncTrigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).SyncTrigger(ctx, req.(*SyncTriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListSyncLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSyncLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListSyncLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListSyncLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListSyncLog(ctx, req.(*ListSyncLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteRootGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRootGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteRootGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteRootGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteRootGroup(ctx, req.(*DeleteRootGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteRootGroupCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRootGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteRootGroupCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteRootGroupCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteRootGroupCustom(ctx, req.(*DeleteRootGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListUserGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListUserGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListUserGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListUserGroup(ctx, req.(*ListUserGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateUserGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateUserGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateUserGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateUserGroup(ctx, req.(*CreateUserGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateUserGroupCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserGroupRequestCustom)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateUserGroupCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateUserGroupCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateUserGroupCustom(ctx, req.(*CreateUserGroupRequestCustom))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateUserGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateUserGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateUserGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateUserGroup(ctx, req.(*UpdateUserGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateUser(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateUserCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateUserCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateUserCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateUserCustom(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteUserCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomDeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteUserCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteUserCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteUserCustom(ctx, req.(*CustomDeleteUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateUserCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomUpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateUserCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateUserCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateUserCustom(ctx, req.(*CustomUpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListUser(ctx, req.(*ListUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteUser(ctx, req.(*DeleteUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateUser(ctx, req.(*UpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateRole(ctx, req.(*CreateRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListRole(ctx, req.(*ListRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_IdleAccountList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdleAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).IdleAccountList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_IdleAccountList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).IdleAccountList(ctx, req.(*IdleAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateRole(ctx, req.(*UpdateRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteRole(ctx, req.(*DeleteRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateAuthPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAuthPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateAuthPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateAuthPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateAuthPolicy(ctx, req.(*CreateAuthPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateAuthPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuthPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateAuthPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateAuthPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateAuthPolicy(ctx, req.(*UpdateAuthPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListAuthPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuthPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListAuthPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListAuthPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListAuthPolicy(ctx, req.(*ListAuthPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteAuthPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAuthPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteAuthPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteAuthPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteAuthPolicy(ctx, req.(*DeleteAuthPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_CreateAccountPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).CreateAccountPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_CreateAccountPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).CreateAccountPolicy(ctx, req.(*CreateAccountPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateAccountPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccountPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateAccountPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateAccountPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateAccountPolicy(ctx, req.(*UpdateAccountPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_DeleteAccountPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAccountPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).DeleteAccountPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_DeleteAccountPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).DeleteAccountPolicy(ctx, req.(*DeleteAccountPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetAccountPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetAccountPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetAccountPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetAccountPolicy(ctx, req.(*GetAccountPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListAccountPolicies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountPoliciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListAccountPolicies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListAccountPolicies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListAccountPolicies(ctx, req.(*ListAccountPoliciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UnlockAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlockAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UnlockAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UnlockAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UnlockAccount(ctx, req.(*UnlockAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_BatchUnlockAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUnlockAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).BatchUnlockAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_BatchUnlockAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).BatchUnlockAccounts(ctx, req.(*BatchUnlockAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetAccountLockInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountLockInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetAccountLockInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetAccountLockInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetAccountLockInfo(ctx, req.(*GetAccountLockInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListLockedAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLockedAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListLockedAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListLockedAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListLockedAccounts(ctx, req.(*ListLockedAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UnlockIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlockIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UnlockIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UnlockIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UnlockIP(ctx, req.(*UnlockIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_BatchUnlockIPs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUnlockIPsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).BatchUnlockIPs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_BatchUnlockIPs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).BatchUnlockIPs(ctx, req.(*BatchUnlockIPsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetIPLockInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIPLockInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetIPLockInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetIPLockInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetIPLockInfo(ctx, req.(*GetIPLockInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListLockedIPs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLockedIPsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListLockedIPs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListLockedIPs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListLockedIPs(ctx, req.(*ListLockedIPsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ValidateWebAuthScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateWebAuthScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ValidateWebAuthScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ValidateWebAuthScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ValidateWebAuthScript(ctx, req.(*ValidateWebAuthScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ListUserSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserSessionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ListUserSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ListUserSessions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ListUserSessions(ctx, req.(*ListUserSessionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_KickUserSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickUserSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).KickUserSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_KickUserSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).KickUserSession(ctx, req.(*KickUserSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetClientLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClientLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetClientLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetClientLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetClientLimits(ctx, req.(*GetClientLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_UpdateClientLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClientLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).UpdateClientLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_UpdateClientLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).UpdateClientLimits(ctx, req.(*UpdateClientLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_GetUserCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).GetUserCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_GetUserCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).GetUserCount(ctx, req.(*GetUserCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Admin_ServiceDesc is the grpc.ServiceDesc for Admin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Admin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.auth.v1.admin.Admin",
	HandlerType: (*AdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCorp",
			Handler:    _Admin_CreateCorp_Handler,
		},
		{
			MethodName: "UpdateCorp",
			Handler:    _Admin_UpdateCorp_Handler,
		},
		{
			MethodName: "UpdateLockStatus",
			Handler:    _Admin_UpdateLockStatus_Handler,
		},
		{
			MethodName: "UpdateIdleTime",
			Handler:    _Admin_UpdateIdleTime_Handler,
		},
		{
			MethodName: "TotpUnbind",
			Handler:    _Admin_TotpUnbind_Handler,
		},
		{
			MethodName: "DeleteCorp",
			Handler:    _Admin_DeleteCorp_Handler,
		},
		{
			MethodName: "GetCorp",
			Handler:    _Admin_GetCorp_Handler,
		},
		{
			MethodName: "ListCorp",
			Handler:    _Admin_ListCorp_Handler,
		},
		{
			MethodName: "CreateUserSource",
			Handler:    _Admin_CreateUserSource_Handler,
		},
		{
			MethodName: "UpdateUserSource",
			Handler:    _Admin_UpdateUserSource_Handler,
		},
		{
			MethodName: "DeleteUserSource",
			Handler:    _Admin_DeleteUserSource_Handler,
		},
		{
			MethodName: "GetUserSource",
			Handler:    _Admin_GetUserSource_Handler,
		},
		{
			MethodName: "ListUserSource",
			Handler:    _Admin_ListUserSource_Handler,
		},
		{
			MethodName: "ListUserSourceType",
			Handler:    _Admin_ListUserSourceType_Handler,
		},
		{
			MethodName: "ListIDPType",
			Handler:    _Admin_ListIDPType_Handler,
		},
		{
			MethodName: "OAuth2Test",
			Handler:    _Admin_OAuth2Test_Handler,
		},
		{
			MethodName: "TestResult",
			Handler:    _Admin_TestResult_Handler,
		},
		{
			MethodName: "CreateIDP",
			Handler:    _Admin_CreateIDP_Handler,
		},
		{
			MethodName: "UpdateIDP",
			Handler:    _Admin_UpdateIDP_Handler,
		},
		{
			MethodName: "DeleteIDP",
			Handler:    _Admin_DeleteIDP_Handler,
		},
		{
			MethodName: "ListIDP",
			Handler:    _Admin_ListIDP_Handler,
		},
		{
			MethodName: "GetIDPDetail",
			Handler:    _Admin_GetIDPDetail_Handler,
		},
		{
			MethodName: "ListRootGroup",
			Handler:    _Admin_ListRootGroup_Handler,
		},
		{
			MethodName: "GetRootGroupDetail",
			Handler:    _Admin_GetRootGroupDetail_Handler,
		},
		{
			MethodName: "CreateRootGroup",
			Handler:    _Admin_CreateRootGroup_Handler,
		},
		{
			MethodName: "GetRootGroupIdpList",
			Handler:    _Admin_GetRootGroupIdpList_Handler,
		},
		{
			MethodName: "GetFieldMap",
			Handler:    _Admin_GetFieldMap_Handler,
		},
		{
			MethodName: "GetFieldOptions",
			Handler:    _Admin_GetFieldOptions_Handler,
		},
		{
			MethodName: "UpdateRootGroup",
			Handler:    _Admin_UpdateRootGroup_Handler,
		},
		{
			MethodName: "UpdateRootGroupCustom",
			Handler:    _Admin_UpdateRootGroupCustom_Handler,
		},
		{
			MethodName: "SwitchAutoSync",
			Handler:    _Admin_SwitchAutoSync_Handler,
		},
		{
			MethodName: "SyncTrigger",
			Handler:    _Admin_SyncTrigger_Handler,
		},
		{
			MethodName: "ListSyncLog",
			Handler:    _Admin_ListSyncLog_Handler,
		},
		{
			MethodName: "DeleteRootGroup",
			Handler:    _Admin_DeleteRootGroup_Handler,
		},
		{
			MethodName: "DeleteRootGroupCustom",
			Handler:    _Admin_DeleteRootGroupCustom_Handler,
		},
		{
			MethodName: "ListUserGroup",
			Handler:    _Admin_ListUserGroup_Handler,
		},
		{
			MethodName: "CreateUserGroup",
			Handler:    _Admin_CreateUserGroup_Handler,
		},
		{
			MethodName: "CreateUserGroupCustom",
			Handler:    _Admin_CreateUserGroupCustom_Handler,
		},
		{
			MethodName: "UpdateUserGroup",
			Handler:    _Admin_UpdateUserGroup_Handler,
		},
		{
			MethodName: "CreateUser",
			Handler:    _Admin_CreateUser_Handler,
		},
		{
			MethodName: "CreateUserCustom",
			Handler:    _Admin_CreateUserCustom_Handler,
		},
		{
			MethodName: "DeleteUserCustom",
			Handler:    _Admin_DeleteUserCustom_Handler,
		},
		{
			MethodName: "UpdateUserCustom",
			Handler:    _Admin_UpdateUserCustom_Handler,
		},
		{
			MethodName: "ListUser",
			Handler:    _Admin_ListUser_Handler,
		},
		{
			MethodName: "DeleteUser",
			Handler:    _Admin_DeleteUser_Handler,
		},
		{
			MethodName: "UpdateUser",
			Handler:    _Admin_UpdateUser_Handler,
		},
		{
			MethodName: "CreateRole",
			Handler:    _Admin_CreateRole_Handler,
		},
		{
			MethodName: "ListRole",
			Handler:    _Admin_ListRole_Handler,
		},
		{
			MethodName: "IdleAccountList",
			Handler:    _Admin_IdleAccountList_Handler,
		},
		{
			MethodName: "UpdateRole",
			Handler:    _Admin_UpdateRole_Handler,
		},
		{
			MethodName: "DeleteRole",
			Handler:    _Admin_DeleteRole_Handler,
		},
		{
			MethodName: "CreateAuthPolicy",
			Handler:    _Admin_CreateAuthPolicy_Handler,
		},
		{
			MethodName: "UpdateAuthPolicy",
			Handler:    _Admin_UpdateAuthPolicy_Handler,
		},
		{
			MethodName: "ListAuthPolicy",
			Handler:    _Admin_ListAuthPolicy_Handler,
		},
		{
			MethodName: "DeleteAuthPolicy",
			Handler:    _Admin_DeleteAuthPolicy_Handler,
		},
		{
			MethodName: "CreateAccountPolicy",
			Handler:    _Admin_CreateAccountPolicy_Handler,
		},
		{
			MethodName: "UpdateAccountPolicy",
			Handler:    _Admin_UpdateAccountPolicy_Handler,
		},
		{
			MethodName: "DeleteAccountPolicy",
			Handler:    _Admin_DeleteAccountPolicy_Handler,
		},
		{
			MethodName: "GetAccountPolicy",
			Handler:    _Admin_GetAccountPolicy_Handler,
		},
		{
			MethodName: "ListAccountPolicies",
			Handler:    _Admin_ListAccountPolicies_Handler,
		},
		{
			MethodName: "UnlockAccount",
			Handler:    _Admin_UnlockAccount_Handler,
		},
		{
			MethodName: "BatchUnlockAccounts",
			Handler:    _Admin_BatchUnlockAccounts_Handler,
		},
		{
			MethodName: "GetAccountLockInfo",
			Handler:    _Admin_GetAccountLockInfo_Handler,
		},
		{
			MethodName: "ListLockedAccounts",
			Handler:    _Admin_ListLockedAccounts_Handler,
		},
		{
			MethodName: "UnlockIP",
			Handler:    _Admin_UnlockIP_Handler,
		},
		{
			MethodName: "BatchUnlockIPs",
			Handler:    _Admin_BatchUnlockIPs_Handler,
		},
		{
			MethodName: "GetIPLockInfo",
			Handler:    _Admin_GetIPLockInfo_Handler,
		},
		{
			MethodName: "ListLockedIPs",
			Handler:    _Admin_ListLockedIPs_Handler,
		},
		{
			MethodName: "ValidateWebAuthScript",
			Handler:    _Admin_ValidateWebAuthScript_Handler,
		},
		{
			MethodName: "ListUserSessions",
			Handler:    _Admin_ListUserSessions_Handler,
		},
		{
			MethodName: "KickUserSession",
			Handler:    _Admin_KickUserSession_Handler,
		},
		{
			MethodName: "GetClientLimits",
			Handler:    _Admin_GetClientLimits_Handler,
		},
		{
			MethodName: "UpdateClientLimits",
			Handler:    _Admin_UpdateClientLimits_Handler,
		},
		{
			MethodName: "GetUserCount",
			Handler:    _Admin_GetUserCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "auth/v1/admin/admin.proto",
}
