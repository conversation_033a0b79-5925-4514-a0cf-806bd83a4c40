// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbCredential(db *gorm.DB, opts ...gen.DOOption) tbCredential {
	_tbCredential := tbCredential{}

	_tbCredential.tbCredentialDo.UseDB(db, opts...)
	_tbCredential.tbCredentialDo.UseModel(&model.TbCredential{})

	tableName := _tbCredential.tbCredentialDo.TableName()
	_tbCredential.ALL = field.NewAsterisk(tableName)
	_tbCredential.ID = field.NewString(tableName, "id")
	_tbCredential.UserID = field.NewString(tableName, "user_id")
	_tbCredential.Type = field.NewString(tableName, "type")
	_tbCredential.SecretData = field.NewString(tableName, "secret_data")
	_tbCredential.CredentialData = field.NewString(tableName, "credential_data")
	_tbCredential.CorpID = field.NewString(tableName, "corp_id")
	_tbCredential.CreatedAt = field.NewTime(tableName, "created_at")
	_tbCredential.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbCredential.fillFieldMap()

	return _tbCredential
}

type tbCredential struct {
	tbCredentialDo tbCredentialDo

	ALL            field.Asterisk
	ID             field.String
	UserID         field.String
	Type           field.String
	SecretData     field.String
	CredentialData field.String
	CorpID         field.String
	CreatedAt      field.Time
	UpdatedAt      field.Time

	fieldMap map[string]field.Expr
}

func (t tbCredential) Table(newTableName string) *tbCredential {
	t.tbCredentialDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbCredential) As(alias string) *tbCredential {
	t.tbCredentialDo.DO = *(t.tbCredentialDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbCredential) updateTableName(table string) *tbCredential {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.UserID = field.NewString(table, "user_id")
	t.Type = field.NewString(table, "type")
	t.SecretData = field.NewString(table, "secret_data")
	t.CredentialData = field.NewString(table, "credential_data")
	t.CorpID = field.NewString(table, "corp_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbCredential) WithContext(ctx context.Context) *tbCredentialDo {
	return t.tbCredentialDo.WithContext(ctx)
}

func (t tbCredential) TableName() string { return t.tbCredentialDo.TableName() }

func (t tbCredential) Alias() string { return t.tbCredentialDo.Alias() }

func (t *tbCredential) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbCredential) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 8)
	t.fieldMap["id"] = t.ID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["type"] = t.Type
	t.fieldMap["secret_data"] = t.SecretData
	t.fieldMap["credential_data"] = t.CredentialData
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbCredential) clone(db *gorm.DB) tbCredential {
	t.tbCredentialDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbCredential) replaceDB(db *gorm.DB) tbCredential {
	t.tbCredentialDo.ReplaceDB(db)
	return t
}

type tbCredentialDo struct{ gen.DO }

func (t tbCredentialDo) Debug() *tbCredentialDo {
	return t.withDO(t.DO.Debug())
}

func (t tbCredentialDo) WithContext(ctx context.Context) *tbCredentialDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbCredentialDo) ReadDB() *tbCredentialDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbCredentialDo) WriteDB() *tbCredentialDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbCredentialDo) Session(config *gorm.Session) *tbCredentialDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbCredentialDo) Clauses(conds ...clause.Expression) *tbCredentialDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbCredentialDo) Returning(value interface{}, columns ...string) *tbCredentialDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbCredentialDo) Not(conds ...gen.Condition) *tbCredentialDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbCredentialDo) Or(conds ...gen.Condition) *tbCredentialDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbCredentialDo) Select(conds ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbCredentialDo) Where(conds ...gen.Condition) *tbCredentialDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbCredentialDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbCredentialDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbCredentialDo) Order(conds ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbCredentialDo) Distinct(cols ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbCredentialDo) Omit(cols ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbCredentialDo) Join(table schema.Tabler, on ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbCredentialDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbCredentialDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbCredentialDo) Group(cols ...field.Expr) *tbCredentialDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbCredentialDo) Having(conds ...gen.Condition) *tbCredentialDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbCredentialDo) Limit(limit int) *tbCredentialDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbCredentialDo) Offset(offset int) *tbCredentialDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbCredentialDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbCredentialDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbCredentialDo) Unscoped() *tbCredentialDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbCredentialDo) Create(values ...*model.TbCredential) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbCredentialDo) CreateInBatches(values []*model.TbCredential, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbCredentialDo) Save(values ...*model.TbCredential) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbCredentialDo) First() (*model.TbCredential, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCredential), nil
	}
}

func (t tbCredentialDo) Take() (*model.TbCredential, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCredential), nil
	}
}

func (t tbCredentialDo) Last() (*model.TbCredential, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCredential), nil
	}
}

func (t tbCredentialDo) Find() ([]*model.TbCredential, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbCredential), err
}

func (t tbCredentialDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbCredential, err error) {
	buf := make([]*model.TbCredential, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbCredentialDo) FindInBatches(result *[]*model.TbCredential, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbCredentialDo) Attrs(attrs ...field.AssignExpr) *tbCredentialDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbCredentialDo) Assign(attrs ...field.AssignExpr) *tbCredentialDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbCredentialDo) Joins(fields ...field.RelationField) *tbCredentialDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbCredentialDo) Preload(fields ...field.RelationField) *tbCredentialDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbCredentialDo) FirstOrInit() (*model.TbCredential, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCredential), nil
	}
}

func (t tbCredentialDo) FirstOrCreate() (*model.TbCredential, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbCredential), nil
	}
}

func (t tbCredentialDo) FindByPage(offset int, limit int) (result []*model.TbCredential, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbCredentialDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbCredentialDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbCredentialDo) Delete(models ...*model.TbCredential) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbCredentialDo) withDO(do gen.Dao) *tbCredentialDo {
	t.DO = *do.(*gen.DO)
	return t
}
