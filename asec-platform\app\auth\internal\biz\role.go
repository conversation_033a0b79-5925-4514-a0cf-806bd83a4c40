package biz

import (
	"context"
	"strings"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

type RoleRepo interface {
	CreateRole(ctx context.Context, id, name, corpId, description string, userIds []string) error
	CreateUserRolesMap(ctx context.Context, corpId, userId string, roleIds []string) error
	GetRoleByName(ctx context.Context, name, corpId string) (*model.TbRole, error)
	ListRole(ctx context.Context, corpId string, limit, offset int, search string, isAll bool) ([]*dto.Role, error)
	CountRoles(ctx context.Context, corpId string, search string, isAll bool) (int64, error)
	QueryRolesUsers(ctx context.Context, corpId string, roleIds []string) ([]dto.UserInfo, error)
	DeleteRole(ctx context.Context, corpId, roleId, name string) error
	UpdateRole(ctx context.Context, param dto.Role) error
}

type RoleUsecase struct {
	repo RoleRepo
	log  *log.Helper
}

func NewRoleUsecase(repo RoleRepo, logger log.Logger) *RoleUsecase {
	return &RoleUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (r RoleUsecase) CreateRole(ctx context.Context, name, corpId, description string, uids []string) error {
	_, err := r.repo.GetRoleByName(ctx, name, corpId)
	if err != nil && !pb.IsRecordNotFound(err) {
		r.log.Errorf("GetRoleByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", name)
	}

	id := uuid.New()
	err = r.repo.CreateRole(ctx, id.String(), name, corpId, description, uids)
	if err != nil {
		r.log.Errorf("CreateRole failed. err=%v", err)
		return err
	}

	return nil
}

func (r RoleUsecase) ListRole(ctx context.Context, corpId string, limit, offset uint32, search string) (dto.ListRoleResp, error) {
	var isAll = false
	if limit == 0 && offset == 0 { // 获取所有角色
		isAll = true
	}

	// 将搜索字符串转换为小写，以确保大小写不敏感的搜索
	if search != "" {
		search = strings.ToLower(search)
	}

	// 分页获取角色
	roles, err := r.repo.ListRole(ctx, corpId, int(limit), int(offset), search, isAll)
	if err != nil {
		r.log.Errorf("ListRole failed. err=%v", err)
		return dto.ListRoleResp{}, err
	}

	// 获取角色对应绑定用户信息
	var roleIds []string
	for _, role := range roles {
		roleIds = append(roleIds, role.ID)
	}
	userInfos, err := r.repo.QueryRolesUsers(ctx, corpId, roleIds)
	if err != nil {
		r.log.Errorf("QueryRolesUsers failed. err=%v", err)
		return dto.ListRoleResp{}, err
	}
	roleToUsers := make(map[string][]dto.UserInfo)
	for _, user := range userInfos {
		roleToUsers[user.RoleID] = append(roleToUsers[user.RoleID], user)
	}
	for id, role := range roles {
		roles[id].Users = roleToUsers[role.ID]
	}

	// 获取角色实际数量
	count, err := r.repo.CountRoles(ctx, corpId, search, isAll)
	if err != nil {
		r.log.Errorf("CountRoles failed. err=%v", err)
		return dto.ListRoleResp{}, err
	}
	return dto.ListRoleResp{Roles: roles, Count: uint32(count)}, nil
}

func (r RoleUsecase) UpdateRole(ctx context.Context, roleId, name, corpId, desc string, userInfos []dto.UserInfo) error {
	param := dto.Role{
		Name:        name,
		ID:          roleId,
		CorpID:      corpId,
		Description: desc,
		Users:       userInfos,
	}
	return r.repo.UpdateRole(ctx, param)
}
func (r RoleUsecase) DeleteRole(ctx context.Context, corpId, roleId, name string) error {
	return r.repo.DeleteRole(ctx, corpId, roleId, name)
}
