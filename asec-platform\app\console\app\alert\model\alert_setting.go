package model

import (
	"asdsec.com/asec/platform/app/console/common/utils"
	"time"
)

type AlertSetting struct {
	ID         uint64    `gorm:"column:id" json:"id"`
	TenantId   uint64    `gorm:"column:tenant_id" json:"tenant_id"`
	EventType  string    `gorm:"column:event_type" json:"event_type"`
	EventName  string    `gorm:"column:event_name" json:"event_name"`
	Threshold  string    `gorm:"column:threshold" json:"threshold"` //阈值[{key opr value},{key opr value}]
	IsQyWx     int       `gorm:"column:is_qy_wx;default:0" json:"is_qy_wx"`
	IsDingTalk int       `gorm:"column:is_ding_talk;default:0" json:"is_ding_talk"`
	CreateTime time.Time `gorm:"column:create_time;default:null" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;default:null" json:"update_time"`
}

type LimitSetting struct {
	High   int `json:"high"`
	Middle int `json:"middle"`
	Low    int `json:"low"`
}

type SettingResp struct {
	ID         uint64          `gorm:"column:id" json:"id"`
	TenantId   uint64          `gorm:"column:tenant_id" json:"tenant_id"`
	EventType  string          `gorm:"column:event_type" json:"event_type"`
	EventName  string          `gorm:"column:event_name" json:"event_name"`
	Threshold  LimitSetting    `gorm:"column:threshold" json:"threshold"`
	IsQyWx     int             `gorm:"column:is_qy_wx;default:0" json:"is_qy_wx"`
	IsDingTalk int             `gorm:"column:is_ding_talk;default:0" json:"is_ding_talk"`
	CreateTime utils.FrontTime `gorm:"column:create_time;default:null" json:"create_time"`
	UpdateTime utils.FrontTime `gorm:"column:update_time;default:null" json:"update_time"`
}

func (AlertSetting) TableName() string {
	return "tb_alert_setting"
}
