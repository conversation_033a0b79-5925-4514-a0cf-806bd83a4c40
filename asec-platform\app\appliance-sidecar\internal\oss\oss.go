package oss

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/agent_conf"
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/tls"
	"encoding/base64"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"net/http"
	"os"
	"time"
)

type AwsConfig struct {
	AK         string
	SK         string
	Token      string
	Region     string
	Endpoint   string
	DeployType string
}

type UploadConfig struct {
	Bucket         string
	OriginFilePath string
	OssFilePath    string
	ContentType    string
}

const (
	ImageContentType = "image/png"
	FileContentType  = "application/octet-stream"
)

func StructAwsConfig(config agent_conf.OssConfig) AwsConfig {
	ret := AwsConfig{
		AK:         config.Ak,
		SK:         config.Sk,
		Region:     config.Region,
		Endpoint:   config.Endpoint,
		DeployType: config.DeployType,
	}

	return ret
}

// Upload 直接上传文件
func Upload(service *s3.S3, config UploadConfig) error {
	fp, err := os.Open(config.OriginFilePath)
	if err != nil {
		global.Logger.Sugar().Errorf("open file failed, err=%v", err)
		return err
	}
	defer fp.Close()
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(30)*time.Second)
	defer cancel()
	_, err = service.PutObjectWithContext(ctx, &s3.PutObjectInput{
		// 填写Bucket名称。
		Bucket: aws.String(config.Bucket),
		// 填写Object完整路径，完整路径中不能包含Bucket名称。
		Key:         aws.String(config.OssFilePath),
		Body:        fp,
		ContentType: aws.String(config.ContentType),
	})
	if err != nil {
		return err
	}
	return nil
}

// UploadByPart 分片上传 这里实现的是分片上传，还有一种断点续传，需要用到s3manager这个子包
func UploadByPart(service *s3.S3, config UploadConfig, size, chunkSize int64) error {
	//文件操作
	file, err := os.Open(config.OriginFilePath)
	if err != nil {
		global.Logger.Sugar().Errorf("open file failed, err=%v", err)
		return err
	}
	defer func() {
		if err := file.Close(); err != nil {
			global.Logger.Sugar().Errorf("close file source failed, err=%v", err)
		}
	}()
	buffer := make([]byte, size)
	fileType := http.DetectContentType(buffer)
	if _, err := file.Read(buffer); err != nil {
		global.Logger.Sugar().Errorf("read file buffer failed, err=%v", err)
		return err
	}

	//创建分片
	res, err := service.CreateMultipartUpload(&s3.CreateMultipartUploadInput{
		Bucket:      aws.String(config.Bucket),
		Key:         aws.String(config.OssFilePath),
		ContentType: aws.String(fileType),
	})
	if err != nil {
		global.Logger.Sugar().Errorf("create upload multipart failed, err=%v", err)
		return err
	}
	var remainSize int64
	//获取分片个数
	chunkNum := size / chunkSize
	if size%chunkSize > 0 {
		chunkNum++
		remainSize = size % chunkSize
	}
	var completedParts []*s3.CompletedPart
	var i int64
	for ; i < chunkNum; i++ {
		partNum := i + 1
		partSize := chunkSize
		if partNum == chunkNum {
			if remainSize > 0 {
				partSize = remainSize
			}
		}
		fileBytes := buffer[chunkSize*i : chunkSize*i+partSize]
		uploadResult, err := service.UploadPart(&s3.UploadPartInput{
			Bucket:     aws.String(config.Bucket),
			Key:        aws.String(config.OssFilePath),
			PartNumber: aws.Int64(partNum),
			UploadId:   res.UploadId,
			//
			Body:          bytes.NewReader(fileBytes),
			ContentLength: aws.Int64(int64(len(fileBytes))),
		})
		if err != nil {
			global.Logger.Sugar().Errorf("upload multipart failed, part=%d, err=%v", i, err)
			return err
		}
		completedParts = append(completedParts, &s3.CompletedPart{
			ETag:       uploadResult.ETag,
			PartNumber: aws.Int64(partNum),
		})
	}
	// 完成分片上传
	_, err = service.CompleteMultipartUpload(&s3.CompleteMultipartUploadInput{
		Bucket:   aws.String(config.Bucket),
		Key:      aws.String(config.OssFilePath),
		UploadId: res.UploadId,
		MultipartUpload: &s3.CompletedMultipartUpload{
			Parts: completedParts,
		},
	})
	if err != nil {
		global.Logger.Sugar().Errorf("complete multipartUpload error: %v", err)
		return err
	}
	return nil
}

var client = &http.Client{Transport: &http.Transport{
	TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
}}

func GetOssS3Service(awsConfig AwsConfig, key string, randomStr string) (*s3.S3, error) {
	ak, err := Decrypt(awsConfig.AK, key, randomStr)
	if err != nil {
		global.Logger.Sugar().Errorf("decrypt ak code err %v", err)
		return nil, err
	}
	sk, err := Decrypt(awsConfig.SK, key, randomStr)
	if err != nil {
		global.Logger.Sugar().Errorf("decrypt sk code err %v", err)
		return nil, err
	}
	/*ak := awsConfig.AK
	sk := awsConfig.SK*/
	if awsConfig.Region == "" {
		awsConfig.Region = "oss"
	}
	creds := credentials.NewStaticCredentials(ak, sk, awsConfig.Token)
	pathStyle := false
	if awsConfig.DeployType == "local" {
		pathStyle = true
	}
	config := &aws.Config{
		Region:           aws.String(awsConfig.Region),
		Endpoint:         &awsConfig.Endpoint,
		S3ForcePathStyle: aws.Bool(pathStyle),
		Credentials:      creds,
		Logger:           aws.NewDefaultLogger(),
	}
	// 本地oss 跳过TLS证书验证
	if pathStyle {
		config.HTTPClient = client
	}
	s, err := session.NewSession(config)
	if err != nil {
		global.Logger.Sugar().Errorf("get oss session err %v", err)
		return nil, err
	}
	service := s3.New(s)
	return service, nil
}

func Decode(s string) []byte {
	data, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		panic(err)
	}
	return data
}

func Decrypt(text, aesKey string, bytes string) (string, error) {
	block, err := aes.NewCipher([]byte(aesKey))

	if err != nil {
		return "", err
	}
	cipherText := Decode(text)

	cfb := cipher.NewCFBDecrypter(block, []byte(bytes))
	plainText := make([]byte, len(cipherText))
	cfb.XORKeyStream(plainText, cipherText)

	return string(plainText), nil
}
