// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbIdentityProvider = "tb_identity_provider"

// TbIdentityProvider mapped from table <tb_identity_provider>
type TbIdentityProvider struct {
	ID          string    `gorm:"column:id;primaryKey" json:"id"`                                            // 认证服务器id
	Name        string    `gorm:"column:name;not null" json:"name"`                                          // 认证服务器名称，租户下名称唯一
	Type        string    `gorm:"column:type;not null" json:"type"`                                          // 认证服务器类型
	SourceID    string    `gorm:"column:source_id;not null;default:'0'::character varying" json:"source_id"` // 认证服务器对应的用户来源，为0表示可应用于所有用户
	CorpID      string    `gorm:"column:corp_id" json:"corp_id"`                                             // 租户id
	Avatar      string    `gorm:"column:avatar" json:"avatar"`
	Enable      bool      `gorm:"column:enable;default:true" json:"enable"`
	Description string    `gorm:"column:description" json:"description"`
	IsDefault   bool      `gorm:"column:is_default" json:"is_default"`
	CreatedAt   time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	TemplateType string    `gorm:"column:template_type" json:"template_type"`
}

// TableName TbIdentityProvider's table name
func (*TbIdentityProvider) TableName() string {
	return TableNameTbIdentityProvider
}
