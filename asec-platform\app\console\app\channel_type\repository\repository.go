package repository

import (
	"asdsec.com/asec/platform/app/console/app/channel_type/dto"
	riskConstants "asdsec.com/asec/platform/app/console/app/risk_setting/constants"
	scoreRepo "asdsec.com/asec/platform/app/console/app/risk_setting/repository"
	commonApi "asdsec.com/asec/platform/app/console/common/api"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/dbutil"
	pkgModel "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/utils/conf_center"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strings"
)

// ChannelTypeRepository 接口定义
type ChannelTypeRepository interface {
	CreateChannelType(ctx context.Context, req pkgModel.ChannelType) error
	UpdateChannelType(ctx context.Context, req pkgModel.ChannelType) error
	DeleteChannel(ctx context.Context, id string) error
	FindChannelByName(ctx context.Context, name string) (pkgModel.ChannelType, error)
	GetChannelTypeList(ctx context.Context, req dto.GetChannelTypeListReq) (dto.GetChannelTypeListRsp, error)
	GetChannelList(ctx context.Context, req dto.GetChannelListReq) (dto.GetChannelListRsp, error)
}

// NewChannelTypeRepository 创建接口实现接口实现
func NewChannelTypeRepository() ChannelTypeRepository {
	return &channelTypeRepository{}
}

type channelTypeRepository struct {
}

const (
	ChannelTypeAgentConfType = "channel_type"
	DefaultChannelTypeId     = "999"
	FPid                     = "0"
	DefaultScore             = 2
)

var channelCountSql = `SELECT pid,channel_name as c_name,id,built_in FROM tb_channel_type WHERE pid != '0' GROUP BY id`

func (c channelTypeRepository) GetChannelTypeList(ctx context.Context, req dto.GetChannelTypeListReq) (dto.GetChannelTypeListRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetChannelTypeListRsp{}, err
	}
	pageReq := pkgModel.Pagination{
		Limit:  req.Limit,
		Offset: req.Offset,
		Search: req.Search,
	}
	if req.Search != "" {
		pageReq.SearchColumns = []string{"channel_name"}
	}
	if req.CName != "" {
		cKeyword := "%" + dbutil.EscapeForLike(req.CName) + "%"
		db = db.Where("pt.c_name like ?", cKeyword)
	}
	if req.BuiltIn != 0 {
		db = db.Where("pt.built_in = ?", req.BuiltIn)
	}
	db = db.Table(pkgModel.ChannelType{}.TableName()).
		Select("tb_channel_type.id,channel_name as name,tb_channel_type.built_in,count (distinct pt.id) as count").
		Joins(fmt.Sprintf("LEFT JOIN (%s) pt on pt.pid = tb_channel_type.id", channelCountSql)).
		Where("tb_channel_type.pid = '0'").Group("tb_channel_type.id").Order("create_time desc")
	var channelTypeList []dto.ChannelTypeItem
	pageReq, err = pkgModel.Paginate(&channelTypeList, &pageReq, db)
	if err != nil {
		return dto.GetChannelTypeListRsp{}, err
	}
	return dto.GetChannelTypeListRsp{
		ChannelTypeList: channelTypeList,
		CommonPage:      pkgModel.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page}}, nil
}

func (c channelTypeRepository) GetChannelList(ctx context.Context, req dto.GetChannelListReq) (dto.GetChannelListRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetChannelListRsp{}, err
	}
	pageReq := pkgModel.Pagination{
		Limit:  req.Limit,
		Offset: req.Offset,
	}
	if req.Search != "" {
		keyword := "%" + strings.ToLower(dbutil.EscapeForLike(req.Search)) + "%"
		db = db.Joins("left join (select id, unnest(process_list) as process from tb_channel_type where pid != '0') t2 on t2.id = tb_channel_type.id").
			Where("LOWER(tb_channel_type.channel_name) LIKE ? OR LOWER(t2.process) LIKE ?", keyword, keyword)
	}
	if req.BuiltIn != 0 {
		db = db.Where("built_in = ?", req.BuiltIn)
	}
	if req.Pid != "" {
		db = db.Where("pid = ?", req.Pid)
	}
	db = db.Table(pkgModel.ChannelType{}.TableName()).
		Select("tb_channel_type.id,tb_channel_type.channel_name as name,c1.channel_name as channel_type_name," +
			"built_in,tb_channel_type.pid,status,process_list,include_file_path,tb_channel_type.exclude_file_path").
		Joins("LEFT JOIN (select id,channel_name from tb_channel_type where pid = '0') c1 on c1.id = tb_channel_type.pid ").
		Where("tb_channel_type.pid != '0' ").Group("tb_channel_type.id,c1.channel_name").
		Order("tb_channel_type.create_time,tb_channel_type.channel_name desc")
	var channelTypeList []dto.ChannelItem
	pageReq, err = pkgModel.Paginate(&channelTypeList, &pageReq, db)
	if err != nil {
		return dto.GetChannelListRsp{}, err
	}
	for index, v := range channelTypeList {
		var inc, exc []pkgModel.FilePathRule
		err = json.Unmarshal([]byte(v.IncludeFilePath), &inc)
		if err != nil {
			return dto.GetChannelListRsp{}, err
		}
		err = json.Unmarshal([]byte(v.ExcludeFilePath), &exc)
		if err != nil {
			return dto.GetChannelListRsp{}, err
		}
		channelTypeList[index].IncludeFilePathInfo = inc
		channelTypeList[index].ExcludeFilePathInfo = exc
	}
	return dto.GetChannelListRsp{
		ChannelList: channelTypeList,
		CommonPage:  pkgModel.CommonPage{TotalNum: int(pageReq.TotalRows), PageSize: req.Limit, CurrentPage: pageReq.Page}}, nil
}

func (c channelTypeRepository) CreateChannelType(ctx context.Context, req pkgModel.ChannelType) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Table(pkgModel.ChannelType{}.TableName()).Create(&req).Error
		if err != nil {
			return err
		}
		if req.Pid != "0" {
			err = scoreRepo.NewAppRepository().CascadeCreatRiskSetting(ctx, req.Channel, riskConstants.SendEventScoreIndicator, DefaultScore, "", tx)
			if err != nil {
				return err
			}
		}
		if req.Pid != FPid {
			return commonApi.PushCommonAgentConf(
				tx,
				req,
				ChannelTypeAgentConfType,
				req.Id,
				conf_center.AddConf,
				conf_center.GlobalConf,
				nil, nil, nil, nil)
		}
		return nil
	})
}

func (c channelTypeRepository) UpdateChannelType(ctx context.Context, req pkgModel.ChannelType) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Table(pkgModel.ChannelType{}.TableName()).Where("id = ?", req.Id).
			Select("channel", "pid", "channel_name", "process_list", "include_file_path", "exclude_file_path", "status").
			Updates(&req).Error
		if err != nil {
			return err
		}
		if req.Pid != FPid {
			return commonApi.PushCommonAgentConf(
				tx,
				req,
				ChannelTypeAgentConfType,
				req.Id,
				conf_center.UpdateConf,
				conf_center.GlobalConf,
				nil, nil, nil, nil)
		}
		return nil
	})
}

func (c channelTypeRepository) FindChannelByName(ctx context.Context, name string) (pkgModel.ChannelType, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return pkgModel.ChannelType{}, err
	}
	var data pkgModel.ChannelType
	err = db.Model(pkgModel.ChannelType{}).Where("channel_name = ?", name).Find(&data).Error
	if err != nil {
		return pkgModel.ChannelType{}, err
	}
	return data, err
}

func (c channelTypeRepository) DeleteChannel(ctx context.Context, id string) error {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return err
	}
	var channel string
	err = db.Model(pkgModel.ChannelType{}).Select("channel").Where("id = ?", id).Find(&channel).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {
		err = tx.Where("id = ?", id).Delete(&pkgModel.ChannelType{Id: id}).Error
		if err != nil {
			return err
		}
		err = tx.Model(pkgModel.ChannelType{}).Where("pid = ?", id).
			Select("pid").Updates(&pkgModel.ChannelType{Pid: DefaultChannelTypeId}).Error
		if err != nil {
			return err
		}
		err = scoreRepo.NewAppRepository().CascadeDeleteRiskSetting(ctx, riskConstants.SendEventScoreIndicator, channel, tx)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		return conf_center.ConfChange(conf_center.ConfChangeReq{
			ConfBizId: id, ConfType: ChannelTypeAgentConfType, Tx: tx, RedisCli: global.SysRedisClient,
			ChangeType: conf_center.DelConf})
	})
}
