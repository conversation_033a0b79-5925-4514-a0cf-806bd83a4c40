// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/sensitive_element_conf.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Symbols defined in public import of google/protobuf/any.proto.

type Any = anypb.Any

// 内置敏感元素和自定义敏感元素 共享该pb定义,以不同配置类型区分
// 敏感元素配置
type SensitiveElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 敏感元素id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 敏感元素类型(1关键词 Value;2正则 Value ;3算法 AlgorithmValue;4字典 DictWordValue)
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 关键词,正则类型配置
	KrValue *Value `protobuf:"bytes,3,opt,name=kr_value,json=krValue,proto3" json:"kr_value,omitempty"`
	// 算法敏感
	AlgorithmValue *AlgorithmValue `protobuf:"bytes,4,opt,name=algorithm_value,json=algorithmValue,proto3" json:"algorithm_value,omitempty"`
	// 字典敏感数据
	DictWordValue *DictWordValue `protobuf:"bytes,5,opt,name=dict_word_value,json=dictWordValue,proto3" json:"dict_word_value,omitempty"`
}

func (x *SensitiveElement) Reset() {
	*x = SensitiveElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensitiveElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensitiveElement) ProtoMessage() {}

func (x *SensitiveElement) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensitiveElement.ProtoReflect.Descriptor instead.
func (*SensitiveElement) Descriptor() ([]byte, []int) {
	return file_conf_v1_sensitive_element_conf_proto_rawDescGZIP(), []int{0}
}

func (x *SensitiveElement) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SensitiveElement) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SensitiveElement) GetKrValue() *Value {
	if x != nil {
		return x.KrValue
	}
	return nil
}

func (x *SensitiveElement) GetAlgorithmValue() *AlgorithmValue {
	if x != nil {
		return x.AlgorithmValue
	}
	return nil
}

func (x *SensitiveElement) GetDictWordValue() *DictWordValue {
	if x != nil {
		return x.DictWordValue
	}
	return nil
}

// 算法类型值
type AlgorithmValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 匹配阈值
	Threshold uint32 `protobuf:"varint,1,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// 值类型(用于算法类型敏感:1关键词;2正则)
	ValueType uint32 `protobuf:"varint,2,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`
	// 敏感元素值
	Value []string `protobuf:"bytes,3,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *AlgorithmValue) Reset() {
	*x = AlgorithmValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlgorithmValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlgorithmValue) ProtoMessage() {}

func (x *AlgorithmValue) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlgorithmValue.ProtoReflect.Descriptor instead.
func (*AlgorithmValue) Descriptor() ([]byte, []int) {
	return file_conf_v1_sensitive_element_conf_proto_rawDescGZIP(), []int{1}
}

func (x *AlgorithmValue) GetThreshold() uint32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *AlgorithmValue) GetValueType() uint32 {
	if x != nil {
		return x.ValueType
	}
	return 0
}

func (x *AlgorithmValue) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

// 字典类型配置 本期暂不实现
type DictWordValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DictWord []*DictWord `protobuf:"bytes,1,rep,name=dict_word,json=dictWord,proto3" json:"dict_word,omitempty"`
	// 匹配阈值
	Threshold uint32 `protobuf:"varint,2,opt,name=threshold,proto3" json:"threshold,omitempty"`
}

func (x *DictWordValue) Reset() {
	*x = DictWordValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DictWordValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictWordValue) ProtoMessage() {}

func (x *DictWordValue) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictWordValue.ProtoReflect.Descriptor instead.
func (*DictWordValue) Descriptor() ([]byte, []int) {
	return file_conf_v1_sensitive_element_conf_proto_rawDescGZIP(), []int{2}
}

func (x *DictWordValue) GetDictWord() []*DictWord {
	if x != nil {
		return x.DictWord
	}
	return nil
}

func (x *DictWordValue) GetThreshold() uint32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

type DictWord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 字典值
	DictWord string `protobuf:"bytes,1,opt,name=dict_word,json=dictWord,proto3" json:"dict_word,omitempty"`
	// 权重
	Weight uint32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
}

func (x *DictWord) Reset() {
	*x = DictWord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DictWord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictWord) ProtoMessage() {}

func (x *DictWord) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictWord.ProtoReflect.Descriptor instead.
func (*DictWord) Descriptor() ([]byte, []int) {
	return file_conf_v1_sensitive_element_conf_proto_rawDescGZIP(), []int{3}
}

func (x *DictWord) GetDictWord() string {
	if x != nil {
		return x.DictWord
	}
	return ""
}

func (x *DictWord) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

// 关键词,正则类型配置
type Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 字典值
	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *Value) Reset() {
	*x = Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_sensitive_element_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_conf_v1_sensitive_element_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Value) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_conf_v1_sensitive_element_conf_proto protoreflect.FileDescriptor

var file_conf_v1_sensitive_element_conf_proto_rawDesc = []byte{
	0x0a, 0x24, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe6, 0x01, 0x0a, 0x10,
	0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x6b, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x6b, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x41, 0x0a, 0x0f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0e, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x44, 0x69, 0x63, 0x74, 0x57, 0x6f, 0x72, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x64, 0x69, 0x63, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x63, 0x0a, 0x0e, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68,
	0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5e, 0x0a, 0x0d, 0x44, 0x69, 0x63,
	0x74, 0x57, 0x6f, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x64, 0x69,
	0x63, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x44, 0x69, 0x63, 0x74, 0x57, 0x6f, 0x72,
	0x64, 0x52, 0x08, 0x64, 0x69, 0x63, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x3f, 0x0a, 0x08, 0x44, 0x69, 0x63,
	0x74, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x63, 0x74, 0x57, 0x6f,
	0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x1d, 0x0a, 0x05, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x29, 0x5a, 0x27, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76,
	0x31, 0x3b, 0x76, 0x31, 0x50, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_sensitive_element_conf_proto_rawDescOnce sync.Once
	file_conf_v1_sensitive_element_conf_proto_rawDescData = file_conf_v1_sensitive_element_conf_proto_rawDesc
)

func file_conf_v1_sensitive_element_conf_proto_rawDescGZIP() []byte {
	file_conf_v1_sensitive_element_conf_proto_rawDescOnce.Do(func() {
		file_conf_v1_sensitive_element_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_sensitive_element_conf_proto_rawDescData)
	})
	return file_conf_v1_sensitive_element_conf_proto_rawDescData
}

var file_conf_v1_sensitive_element_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_conf_v1_sensitive_element_conf_proto_goTypes = []interface{}{
	(*SensitiveElement)(nil), // 0: api.conf.SensitiveElement
	(*AlgorithmValue)(nil),   // 1: api.conf.AlgorithmValue
	(*DictWordValue)(nil),    // 2: api.conf.DictWordValue
	(*DictWord)(nil),         // 3: api.conf.DictWord
	(*Value)(nil),            // 4: api.conf.Value
}
var file_conf_v1_sensitive_element_conf_proto_depIdxs = []int32{
	4, // 0: api.conf.SensitiveElement.kr_value:type_name -> api.conf.Value
	1, // 1: api.conf.SensitiveElement.algorithm_value:type_name -> api.conf.AlgorithmValue
	2, // 2: api.conf.SensitiveElement.dict_word_value:type_name -> api.conf.DictWordValue
	3, // 3: api.conf.DictWordValue.dict_word:type_name -> api.conf.DictWord
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_conf_v1_sensitive_element_conf_proto_init() }
func file_conf_v1_sensitive_element_conf_proto_init() {
	if File_conf_v1_sensitive_element_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_sensitive_element_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensitiveElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_sensitive_element_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlgorithmValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_sensitive_element_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DictWordValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_sensitive_element_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DictWord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_sensitive_element_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_sensitive_element_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_sensitive_element_conf_proto_goTypes,
		DependencyIndexes: file_conf_v1_sensitive_element_conf_proto_depIdxs,
		MessageInfos:      file_conf_v1_sensitive_element_conf_proto_msgTypes,
	}.Build()
	File_conf_v1_sensitive_element_conf_proto = out.File
	file_conf_v1_sensitive_element_conf_proto_rawDesc = nil
	file_conf_v1_sensitive_element_conf_proto_goTypes = nil
	file_conf_v1_sensitive_element_conf_proto_depIdxs = nil
}
