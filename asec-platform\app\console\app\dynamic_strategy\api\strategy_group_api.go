package api

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/service"
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/vo"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateGroup godoc
// @Summary 添加策略分组
// @Schemes
// @Description 添加策略分组
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Param       req body vo.CreateGroupReq true "添加策略分组参数"
// @Success     200
// @Router      /v1/dynamic_strategy/group [POST]
// @success     200 {object} common.Response{} "ok"
func CreateGroup(c *gin.Context) {
	req := vo.CreateGroupReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err = service.GetStrategyGroupService().CreateGroup(c, req)
	if err != nil {
		global.SysLog.Error("createGroup err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// DeleteGroup godoc
// @Summary 删除策略分组
// @Schemes
// @Description 删除策略分组
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Param       req body vo.DeleteGroupReq true "删除策略分组参数"
// @Success     200
// @Router      /v1/dynamic_strategy/group [DELETE]
// @success     200 {object} common.Response{} "ok"
func DeleteGroup(c *gin.Context) {
	req := vo.DeleteGroupReq{}
	err := c.ShouldBindJSON(&req)

	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamError)
		return
	}
	aErr := service.GetStrategyGroupService().DeleteGroup(c, req)
	if aErr != nil {
		global.SysLog.Error("deleteGroup err", zap.Error(aErr))
		common.FailAError(c, aErr)
		return
	}
	common.Ok(c)
}

// UpdateGroup godoc
// @Summary 修改策略分组
// @Schemes
// @Description 修改策略分组
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Param       req body vo.UpdateGroupReq true "修改策略分组参数"
// @Success     200
// @Router      /v1/dynamic_strategy/group [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateGroup(c *gin.Context) {
	req := vo.UpdateGroupReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamError)
		return
	}
	aError := service.GetStrategyGroupService().UpdateGroup(c, req)
	if aError != nil {
		global.SysLog.Error("updateGroup err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}

// GroupList godoc
// @Summary 获取策略分组列表
// @Schemes
// @Description 获取策略分组列表
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Param       search query string false "搜索参数"
// @Success     200
// @Router      /v1/dynamic_strategy/group_list [GET]
// @success     200 {object} common.Response{data=vo.GroupListResp} "ok"
func GroupList(c *gin.Context) {
	query := c.Query("search")
	groupList, aError := service.GetStrategyGroupService().GroupList(c, query)
	if aError != nil {
		global.SysLog.Error("get groupList err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.OkWithData(c, groupList)
	return
}

// GroupMove godoc
// @Summary 移动策略分组
// @Schemes
// @Description 移动策略分组
// @Tags        DynamicStrategyGroup
// @Produce     application/json
// @Param       req body vo.GroupMoveReq true "移动策略分组参数"
// @Success     200
// @Router      /v1/dynamic_strategy/group_move [POST]
// @success     200 {object} common.Response{} "ok"
func GroupMove(c *gin.Context) {
	req := vo.GroupMoveReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamError)
		return
	}
	aError := service.GetStrategyGroupService().GroupMove(c, req)
	if aError != nil {
		global.SysLog.Error("groupMove err", zap.Error(aError))
		common.FailAError(c, aError)
		return
	}
	common.Ok(c)
}
