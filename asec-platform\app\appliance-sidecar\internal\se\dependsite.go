package se

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"asdsec.com/asec/platform/pkg/apisix"
	"asdsec.com/asec/platform/pkg/biz/dto"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"
)

// 同步依赖站点路由
func syncDependSiteRoutes(appId string, appGateway dto.ApplicationGatewayDto, webConf apisix.CompatibleConfig, existRouteMap map[string]bool, inheritedPlugins map[string]any, inheritedChecks apisix.Checks) error {
	// 提取主域名
	mainDomain := strings.Split(appGateway.PublishAddress, ":")[0]

	// 生成依赖站点路由
	dependRoutes, err := generateDependSiteRoutes(webConf.DependSite, &apisix.ApisixRouteReq{Name: appGateway.AppName}, mainDomain)
	if err != nil {
		return fmt.Errorf("生成依赖站点路由失败: %v", err)
	}

	// 创建依赖站点路由
	for _, dependRoute := range dependRoutes {
		routeId := fmt.Sprintf("%s-depend-%s", appId, dependRoute.Id)
		existRouteMap[routeId] = true

		// 转换为标准路由格式并发送，使用依赖站点自己的协议，继承主应用的插件配置
		dependSiteScheme := "http" // 默认使用HTTP
		if dependRoute.Upstream.Scheme == "https" {
			dependSiteScheme = "https"
		}
		actualInheritedPlugins, err := createDependRoute(routeId, dependRoute, dependSiteScheme, inheritedPlugins)
		if err != nil {
			global.Logger.Sugar().Errorf("创建依赖站点路由失败: %v, routeId: %s", err, routeId)
			continue
		}
		global.Logger.Sugar().Debugf("成功创建依赖站点路由: %s，协议: %s，原始插件数量: %d，实际继承插件数量: %d，访问域名: %s",
			routeId, dependSiteScheme, len(inheritedPlugins), len(actualInheritedPlugins), dependRoute.Hosts[0])

		// 打印继承的插件详情
		if len(actualInheritedPlugins) > 0 {
			var pluginNames []string
			for pluginName := range actualInheritedPlugins {
				pluginNames = append(pluginNames, pluginName)
			}
			global.Logger.Sugar().Debugf("依赖站点路由 %s 实际继承的插件: %v", routeId, pluginNames)
		} else {
			global.Logger.Sugar().Warnf("依赖站点路由 %s 没有继承任何插件配置", routeId)
		}
	}
	return nil
}

// 创建依赖站点路由
func createDependRoute(routeId string, dependRoute apisix.DependSiteRoute, upstreamScheme string, inheritedPlugins map[string]any) (map[string]any, error) {
	// 继承主应用的插件配置
	plugins := make(map[string]any)
	// 深拷贝插件配置，避免修改原始配置
	// 依赖站点需要继承策略引擎插件以记录日志，但需要特殊处理应用匹配逻辑
	excludePlugins := map[string]bool{
		"redirect": true, // 依赖站点不应该继承重定向插件，它们应该保持自己的协议
	}

	var excludedPlugins []string
	for key, value := range inheritedPlugins {
		if excludePlugins[key] {
			excludedPlugins = append(excludedPlugins, key)
			continue
		}

		// 特殊处理策略引擎插件，添加依赖站点标识
		if key == "ext-plugin-post-req" {
			// 深拷贝策略引擎插件配置
			if extPlugin, ok := value.(dto.ExtPluginPostReqPlugin); ok {
				// 为策略引擎插件添加依赖站点标识
				newConf := make([]dto.ExtPluginConf, len(extPlugin.Conf))
				copy(newConf, extPlugin.Conf)

				// 查找策略引擎插件配置并添加依赖站点标识
				for i, conf := range newConf {
					if conf.Name == "strategy_plugin" {
						// 解析现有配置
						var strategyConf map[string]interface{}
						if err := json.Unmarshal([]byte(conf.Value), &strategyConf); err == nil {
							// 添加依赖站点标识，包含主应用访问域名信息
							strategyConf["is_depend_site"] = true
							strategyConf["main_route_id"] = strings.TrimSuffix(routeId, fmt.Sprintf("-depend-%s", dependRoute.Id))
							// 重新序列化
							if newValue, err := json.Marshal(strategyConf); err == nil {
								newConf[i].Value = string(newValue)
							}
						}
						break
					}
				}

				plugins[key] = dto.ExtPluginPostReqPlugin{
					Conf:             newConf,
					AllowDegradation: extPlugin.AllowDegradation,
				}
			}
		} else {
			plugins[key] = value
		}
	}

	if len(excludedPlugins) > 0 {
		global.Logger.Sugar().Debugf("依赖站点路由 %s 排除插件: %v", routeId, excludedPlugins)
	}

	// 解析依赖站点的原始信息
	// 从 Nodes map 中获取第一个节点作为原始地址信息
	var originalHost string
	var originalPort int
	for nodeKey := range dependRoute.Upstream.Nodes {
		// 解析 "host:port" 格式
		parts := strings.Split(nodeKey, ":")
		if len(parts) == 2 {
			originalHost = parts[0]
			if port, err := strconv.Atoi(parts[1]); err == nil {
				originalPort = port
			}
		} else {
			originalHost = nodeKey
			originalPort = 80 // 默认端口
		}
		break // 只取第一个节点
	}

	if originalHost == "" {
		global.Logger.Sugar().Errorf("无法解析依赖站点上游信息: %v", dependRoute.Upstream.Nodes)
		originalHost = "unknown"
		originalPort = 80
	}

	// 对于依赖站点，我们使用 "rewrite" 模式，将访问域名重写为原始依赖站点的域名
	var upstreamHost string
	if originalPort != 80 && originalPort != 443 {
		upstreamHost = fmt.Sprintf("%s:%d", originalHost, originalPort)
	} else {
		upstreamHost = originalHost
	}

	global.Logger.Sugar().Debugf("依赖站点路由 %s: 访问域名=%s, 上游Host=%s, PassHost=rewrite",
		routeId, dependRoute.Hosts[0], upstreamHost)

	// 创建上游配置
	upstream := apisix.Upstream{
		Type:          dependRoute.Upstream.Type,
		PassHost:      "rewrite",      // 使用 rewrite 模式
		UpstreamHost:  upstreamHost,   // 设置为原始依赖站点的域名
		Scheme:        upstreamScheme, // 使用传递的协议
		Timeout:       apisix.Timeout{Send: 60, Connect: 6, Read: 60},
		KeepalivePool: apisix.KeepalivePool{Size: 320, Requests: 1000, IdleTimeout: 60},
		Nodes:         convertNodesToStandardFormat(dependRoute.Upstream.Nodes),
	}

	standardRoute := apisix.ApisixRouteReq{
		Name:            dependRoute.Name,
		Status:          dependRoute.Status,
		Methods:         dependRoute.Methods,
		Uri:             dependRoute.Uri,
		Host:            dependRoute.Hosts[0],
		EnableWebsocket: true, // 默认启用WebSocket支持
		Upstream:        upstream,
		Plugins:         plugins, // 使用继承的插件配置
	}

	jsonData, err := json.Marshal(standardRoute)
	if err != nil {
		return nil, fmt.Errorf("序列化路由失败: %v", err)
	}

	// 添加调试信息，输出要发送的JSON内容
	// global.Logger.Sugar().Debugf("依赖站点路由 %s 发送的JSON配置: %s", routeId, string(jsonData))

	endpoint := fmt.Sprintf("%s%s/%s", apisix.ApisixEndpoint, apisix.ApisixRoute, routeId)
	resreq, code, err := apisix.SendRequestToApisix(endpoint, apisix.PutMethod, apisix.XApisixKey, bytes.NewBuffer(jsonData))
	if err != nil || code >= 300 {
		global.Logger.Sugar().Errorf("依赖站点路由 %s 发送失败，响应: %s", routeId, resreq)
		return nil, fmt.Errorf("发送请求失败: %v, code: %d, routeId: %s", err, code, routeId)
	}
	return plugins, nil
}

// 解析依赖站点地址并生成访问地址前缀
func parseDependSiteAddress(siteUrl string) (string, string, int, string, error) {
	parsedUrl, err := url.Parse(siteUrl)
	if err != nil {
		return "", "", 0, "", err
	}

	host := parsedUrl.Hostname()
	port := parsedUrl.Port()
	scheme := parsedUrl.Scheme

	// 构建访问地址前缀
	// 先将原始的 "-" 替换为 "--" 以避免与分隔符混淆
	prefix := strings.ReplaceAll(host, "-", "--")
	prefix = strings.ReplaceAll(prefix, ".", "-")
	prefix = strings.ReplaceAll(prefix, ":", "-")

	// 处理端口
	portNum := 80
	if port != "" {
		prefix += "-" + port + "-p"
		portNum, _ = strconv.Atoi(port)
	} else {
		if scheme == "https" {
			portNum = 443
		}
	}

	// 处理https
	if scheme == "https" {
		prefix += "-s"
	}

	return prefix, host, portNum, scheme, nil
}

// 生成依赖站点过滤规则（同时包含Location头部和响应体内容重写）
func generateDependSiteFilters(dependSiteConfig *apisix.DependSiteConfig, mainDomain string, mainPort int, mainSchema string) ([]dto.UpdateConfig, []dto.FilterConfig, error) {
	if dependSiteConfig == nil || dependSiteConfig.Text == "" {
		return nil, nil, nil
	}

	var headerUpdates []dto.UpdateConfig
	var filters []dto.FilterConfig
	sites := strings.Split(dependSiteConfig.Text, "\n")
	baseDomain := extractBaseDomain(mainDomain)

	for _, site := range sites {
		site = strings.TrimSpace(site)
		if site == "" {
			continue
		}

		prefix, _, _, _, err := parseDependSiteAddress(site)
		if err != nil {
			global.Logger.Sugar().Errorf("解析依赖站点地址失败: %v, site: %s", err, site)
			continue
		}

		var accessUrl string
		// 使用主站点的协议和端口
		if mainPort == 80 && mainSchema == "http" {
			accessUrl = fmt.Sprintf("http://%s.%s", prefix, baseDomain)
		} else if mainPort == 443 && mainSchema == "https" {
			accessUrl = fmt.Sprintf("https://%s.%s", prefix, baseDomain)
		} else {
			accessUrl = fmt.Sprintf("%s://%s.%s:%d", mainSchema, prefix, baseDomain, mainPort)
		}

		// 添加Location头部重写规则
		headerUpdates = append(headerUpdates, dto.UpdateConfig{
			HeaderKey:     "Location",
			HeaderOrigin:  site,
			HeaderReplace: accessUrl,
		})

		// 添加响应体内容重写规则
		filters = append(filters, dto.FilterConfig{
			Regex:   site,
			Replace: accessUrl,
			Options: apisix.ResponseRewriteFilterOption, // "jo"
			Scope:   apisix.ResponseRewriteFilterScope,  // "global"
		})

		global.Logger.Sugar().Debugf("生成依赖站点重写规则: %s -> %s", site, accessUrl)
	}

	return headerUpdates, filters, nil
}

// 生成依赖站点路由配置
func generateDependSiteRoutes(dependSiteConfig *apisix.DependSiteConfig, mainRoute *apisix.ApisixRouteReq, mainDomain string) ([]apisix.DependSiteRoute, error) {
	if dependSiteConfig == nil || dependSiteConfig.Text == "" {
		return nil, nil
	}

	var routes []apisix.DependSiteRoute
	baseDomain := extractBaseDomain(mainDomain)

	for _, site := range strings.Split(dependSiteConfig.Text, "\n") {
		site = strings.TrimSpace(site)
		if site == "" {
			continue
		}

		route, err := createSingleDependRoute(site, mainRoute.Name, baseDomain)
		if err != nil {
			global.Logger.Sugar().Errorf("创建依赖站点路由失败: %v, site: %s", err, site)
			continue
		}
		routes = append(routes, route)
	}

	return routes, nil
}

// 提取基础域名 - 去掉最前面的子域名部分
func extractBaseDomain(domain string) string {
	parts := strings.Split(domain, ".")
	if len(parts) >= 3 {
		// 对于 app.infogo.net.cn 这样的域名，去掉第一个部分，保留 infogo.net.cn
		return strings.Join(parts[1:], ".")
	}
	// 如果只有两个部分（如 example.com），直接返回
	return domain
}

// 创建单个依赖站点路由
func createSingleDependRoute(site, routeName, baseDomain string) (apisix.DependSiteRoute, error) {
	prefix, host, port, scheme, err := parseDependSiteAddress(site)
	if err != nil {
		return apisix.DependSiteRoute{}, err
	}

	upstream := apisix.DependSiteUpstream{
		Type:  "roundrobin",
		Nodes: map[string]int{fmt.Sprintf("%s:%d", host, port): 1},
	}

	// 根据协议设置upstream的scheme
	if scheme == "https" {
		upstream.Scheme = "https"
	} else {
		// HTTP协议也需要显式设置，避免默认使用HTTPS
		upstream.Scheme = "http"
	}

	return apisix.DependSiteRoute{
		Id:       prefix,
		Uri:      "/*",
		Hosts:    []string{fmt.Sprintf("%s.%s", prefix, baseDomain)},
		Methods:  []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"},
		Status:   1,
		Name:     fmt.Sprintf("%s-depend-%s", routeName, prefix),
		Upstream: upstream,
	}, nil
}

// 辅助函数：检查规则是否包含指定项
func containsRule(rules []string, rule string) bool {
	for _, r := range rules {
		if r == rule {
			return true
		}
	}
	return false
}

// 辅助函数：将[]any转换为[]dto.FilterConfig
func convertToFilterConfigs(filters []any) []dto.FilterConfig {
	var result []dto.FilterConfig
	for _, filter := range filters {
		if filterMap, ok := filter.(map[string]any); ok {
			config := dto.FilterConfig{}
			if options, exists := filterMap["options"]; exists {
				if optStr, ok := options.(string); ok {
					config.Options = optStr
				}
			}
			if scope, exists := filterMap["scope"]; exists {
				if scopeStr, ok := scope.(string); ok {
					config.Scope = scopeStr
				}
			}
			if regex, exists := filterMap["regex"]; exists {
				if regexStr, ok := regex.(string); ok {
					config.Regex = regexStr
				}
			}
			if replace, exists := filterMap["replace"]; exists {
				if replaceStr, ok := replace.(string); ok {
					config.Replace = replaceStr
				}
			}
			result = append(result, config)
		}
	}
	return result
}

// 将map[string]int格式的节点转换为[]Node格式
func convertNodesToStandardFormat(nodes map[string]int) []apisix.Node {
	var result []apisix.Node
	for nodeKey, weight := range nodes {
		// 解析 "host:port" 格式
		parts := strings.Split(nodeKey, ":")
		if len(parts) == 2 {
			host := parts[0]
			port, err := strconv.Atoi(parts[1])
			if err != nil {
				global.Logger.Sugar().Errorf("解析端口失败: %v, nodeKey: %s", err, nodeKey)
				continue
			}
			result = append(result, apisix.Node{
				Host:   host,
				Port:   port,
				Weight: weight,
			})
		}
	}
	return result
}
