//go:build android || ios

package heartbeat

import (
	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/user"
	"runtime"

	"go.uber.org/zap"
	"time"
)

func GetHeartbeatPacket(now time.Time, pids []int32) *v1.HeartbeatReq {

	rec := v1.HeartbeatReq{}
	//rec.KernelVersion = host.KernelVersion
	rec.Type = global.ApplianceType
	rec.Arch = runtime.GOARCH
	rec.Platform = runtime.GOOS
	//rec.PlatformFamily = host.PlatformFamily
	rec.PlatformVersion = global.DeviceOsVersion
	rec.ApplianceId = global.ApplianceID
	if global.LoginUser != "" {
		rec.UserId = global.LoginUser
	} else {
		if global.ApplianceType == v1.ApplianceType_AGENT {
			curUser := user.GetUserInfo()
			rec.UserId = curUser.UserId
		}
	}

	if global.Version != "" {
		rec.Version = global.Version
	}
	if global.UpgradeTime != "" {
		rec.UpgradeTime = global.UpgradeTime
	}
	zap.S().Debugf("agent heartbeat completed")
	return &rec
}
