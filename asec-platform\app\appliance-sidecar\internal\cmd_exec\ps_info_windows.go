//go:build windows

package cmd_exec

import (
	"fmt"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
	net2 "github.com/shirou/gopsutil/v3/net"
	"github.com/shirou/gopsutil/v3/process"
	"log"
	"os"
)

func WriteSysInfo(logFilePath string) {
	outputFile, err := os.Create(logFilePath)
	if err != nil {
		log.Fatal(err)
	}
	defer outputFile.Close()

	_, _ = fmt.Fprintln(outputFile, "操作系统信息:")
	info, _ := host.Info()
	_, _ = fmt.Fprintln(outputFile, info)

	_, _ = fmt.Fprintln(outputFile, "")
	_, _ = fmt.Fprintln(outputFile, "环境变量:")
	environ := os.Environ()
	for _, s := range environ {
		_, _ = fmt.Fprintln(outputFile, s)

	}

	_, _ = fmt.Fprintln(outputFile, "")
	_, _ = fmt.Fprintln(outputFile, "CPU信息:")
	cpuInfo, _ := cpu.Info()
	for _, stat := range cpuInfo {
		_, _ = fmt.Fprintln(outputFile, stat.String())
	}

	times, _ := cpu.Times(true)
	for _, time := range times {
		_, _ = fmt.Fprintln(outputFile, time.String())
	}

	_, _ = fmt.Fprintln(outputFile, "")
	_, _ = fmt.Fprintln(outputFile, "磁盘信息:")
	counters, _ := disk.IOCounters()
	for s, stat := range counters {
		_, _ = fmt.Fprintln(outputFile, fmt.Sprintf("%v %v", s, stat.String()))
	}
	partitions, _ := disk.Partitions(true)
	for _, partition := range partitions {
		_, _ = fmt.Fprintln(outputFile, partition.String())
	}
	for _, partition := range partitions {
		usage, _ := disk.Usage(partition.Device)
		_, _ = fmt.Fprintln(outputFile, usage.String())
	}

	_, _ = fmt.Fprintln(outputFile, "")
	_, _ = fmt.Fprintln(outputFile, "内存信息:")
	memory, _ := mem.VirtualMemory()
	_, _ = fmt.Fprintln(outputFile, memory.String())
	swapMemory, _ := mem.SwapMemory()
	_, _ = fmt.Fprintln(outputFile, swapMemory.String())

	_, _ = fmt.Fprintln(outputFile, "")
	_, _ = fmt.Fprintln(outputFile, "网络信息:")
	ioCounters, _ := net2.IOCounters(true)
	for _, counter := range ioCounters {
		_, _ = fmt.Fprintln(outputFile, counter.String())
	}
	interfaces, _ := net2.Interfaces()
	for _, interfaceStat := range interfaces {
		_, _ = fmt.Fprintln(outputFile, interfaceStat.String())
	}

	_, _ = fmt.Fprintln(outputFile, "")
	_, _ = fmt.Fprintln(outputFile, "进程信息:")
	processes, _ := process.Processes()
	for _, p := range processes {
		newProcess, err := process.NewProcess(p.Pid)
		if err != nil {
			continue
		}
		percent, err := newProcess.CPUPercent()
		if err != nil {
			continue
		}
		memoryInfo, err := newProcess.MemoryInfo()
		if err != nil {
			continue
		}
		name, err := newProcess.Name()
		if err != nil {
			continue
		}
		cmdline, err := newProcess.Cmdline()
		if err != nil {
			continue
		}
		_, _ = fmt.Fprintln(outputFile, fmt.Sprintf("Pid:%v,进程名:%v,CPU占用率:%v,内存:%v,启动命令:%v",
			newProcess.Pid, name, percent, memoryInfo.String(), cmdline))
	}

}
