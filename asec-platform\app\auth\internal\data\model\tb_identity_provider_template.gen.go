// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbIdentityProviderTemplate = "tb_identity_provider_template"

// TbIdentityProviderTemplate mapped from table <tb_identity_provider_template>
type TbIdentityProviderTemplate struct {
	ID           string    `gorm:"column:id;primaryKey" json:"id"`
	Name         string    `gorm:"column:name;not null" json:"name"`
	Type         string    `gorm:"column:type;not null" json:"type"`
	TemplateType string    `gorm:"column:template_type;not null" json:"template_type"`
	CorpID       string    `gorm:"column:corp_id" json:"corp_id"`
	Avatar       string    `gorm:"column:avatar" json:"avatar"`
	Description  string    `gorm:"column:description" json:"description"`
	CreatedAt    time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
	SourceID     string    `gorm:"column:source_id" json:"source_id"`
}

// TableName TbIdentityProviderTemplate's table name
func (*TbIdentityProviderTemplate) TableName() string {
	return TableNameTbIdentityProviderTemplate
}
