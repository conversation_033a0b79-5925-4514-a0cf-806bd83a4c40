package custom_middleware

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/dto"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
)

const (
	defaultCorp = "default"
)

type CorpIdMapper interface {
	GetCorpId(ctx context.Context, corp string) (string, error)
}

func SetCorp(corpMapper CorpIdMapper, logger log.Logger) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if header, ok := transport.FromServerContext(ctx); ok {
				corp := header.RequestHeader().Get(dto.HeaderCorpKey)
				if corp == "" {
					corp = defaultCorp
				}
				corpId, err := corpMapper.GetCorpId(ctx, corp)
				if err != nil {
					return nil, err
				}
				ctx = context.WithValue(ctx, dto.HeaderCorpKey, corpId)
				return handler(ctx, req)
			}
			return nil, pb.ErrorRecordNotFound("headers not found")
		}
	}
}
