package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	v1 "asdsec.com/asec/platform/api/auth/v1"
	pb "asdsec.com/asec/platform/api/auth/v1/admin"
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp/oauth2"
	"asdsec.com/asec/platform/app/auth/internal/idp/webauth"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/structpb"
)

type AdminService struct {
	pb.UnimplementedAdminServer
	corp          *biz.CorpUsecase
	authPolicy    *biz.AuthPolicyUsecase
	accountPolicy *biz.AuthAccountPolicyUsecase
	idp           *biz.IdpUsecase
	role          *biz.RoleUsecase
	user          *biz.UserUsecase
	userGroup     *biz.UserGroupUsecase
	userSource    *biz.UserSourceUsecase
	auth          *biz.AuthUsecase
	log           *log.Helper
}

func NewAdminService(corp *biz.CorpUsecase, authPolicy *biz.AuthPolicyUsecase, accountPolicy *biz.AuthAccountPolicyUsecase, idp *biz.IdpUsecase, user *biz.UserUsecase, userGroup *biz.UserGroupUsecase, userSource *biz.UserSourceUsecase, role *biz.RoleUsecase, auth *biz.AuthUsecase, logger log.Logger) *AdminService {
	return &AdminService{
		corp:          corp,
		authPolicy:    authPolicy,
		accountPolicy: accountPolicy,
		idp:           idp,
		user:          user,
		userGroup:     userGroup,
		userSource:    userSource,
		role:          role,
		auth:          auth,
		log:           log.NewHelper(logger),
	}
}

func (s *AdminService) GetCorpId(ctx context.Context, corpName string) (string, error) {
	corp, err := s.corp.GetCorpByName(ctx, corpName)
	if err != nil {
		return "", err
	}
	return corp.ID, nil
}

// OAuth2Test 实现测试OAuth2配置的接口
func (s *AdminService) OAuth2Test(ctx context.Context, req *pb.OAuth2TestRequest) (*pb.OAuth2TestReply, error) {
	log.Infof("收到OAuth2配置测试请求: %v", req)

	// 生成唯一测试ID
	testId := uuid.New().String()
	logger := log.With(log.GetLogger())

	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 获取IDP基本信息
	idpBasic, err := s.idp.GetIDPById(ctx, corpId, req.IdpId)
	if err != nil {
		log.Errorf("获取OAuth2提供商失败: %v", err)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("获取OAuth2提供商失败: %v", err),
		}, err
	}

	// 检查是否是OAuth2类型
	if idpBasic.Type != "oauth2" && idpBasic.TemplateType != "oauth2" {
		log.Errorf("身份提供商类型不是OAuth2: %s，%s", idpBasic.Type, idpBasic.TemplateType)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("身份提供商类型不是OAuth2: %s", idpBasic.Type),
		}, fmt.Errorf("身份提供商类型不是OAuth2")
	}

	attrs, err := s.idp.GetIDPAttrs(ctx, corpId, req.IdpId)
	if err != nil {
		log.Errorf("获取IDP属性失败: %v", err)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("获取IDP属性失败: %v", err),
		}, err
	}

	// 创建OAuth2提供商实例
	attrMap := attrs

	// 从属性中获取必要的配置
	globalData := attrMap["oauth2_global_data"]
	codeData := attrMap["oauth2_code_data"]
	userData := attrMap["oauth2_user_data"]
	logoutOpen := attrMap["oauth2_logout_open"]
	logoutData := attrMap["oauth2_logout_data"]
	callbackURL := attrMap["callback_url"]

	// 创建OAuth2提供商
	provider, err := oauth2.NewOAuth2Provider(
		idpBasic.ID,
		idpBasic.Name,
		globalData,
		codeData,
		userData,
		logoutData,
		logoutOpen == "true",
		callbackURL,
		logger,
	)
	if err != nil {
		log.Errorf("创建OAuth2提供商失败: %v", err)
		return &pb.OAuth2TestReply{
			Success: false,
			Message: fmt.Sprintf("创建OAuth2提供商失败: %v", err),
		}, err
	}

	// 设置测试模式环境变量
	ctx = context.WithValue(ctx, common.TestIDKey, testId)
	ctx = context.WithValue(ctx, common.IsTestKey, true)
	ctx = context.WithValue(ctx, common.IdpIDKey, req.IdpId)

	// 执行测试
	testResult, err := provider.TestAuth(ctx)
	if err != nil {
		log.Errorf("OAuth2测试失败: %v", err)

		// 根据错误内容进行分类处理
		errorMessage := fmt.Sprintf("OAuth2测试失败: %v", err)

		// 检查是否为脚本错误
		if strings.Contains(err.Error(), "编译表达式错误") ||
			strings.Contains(err.Error(), "脚本编译错误") {
			errorMessage = fmt.Sprintf("OAuth2脚本编译错误: %v", err)
		} else if strings.Contains(err.Error(), "执行表达式错误") ||
			strings.Contains(err.Error(), "脚本执行错误") {
			errorMessage = fmt.Sprintf("OAuth2脚本执行错误: %v", err)
		}

		// 返回详细错误信息，但不向上传递原始错误
		return &pb.OAuth2TestReply{
			Success: false,
			Message: errorMessage,
			TestId:  testId, // 保留测试ID以便前端引用
		}, nil
	}

	// 添加测试ID到结果
	testResult["test_id"] = testId

	// 缓存测试结果，设置有效期为10分钟
	resultJSON, _ := json.Marshal(testResult)
	log.Infof("testResult测试结果: %s", string(resultJSON))
	if err := s.auth.CacheTest(ctx, testId, string(resultJSON), 10*60); err != nil {
		log.Warnf("缓存OAuth2测试结果失败: %v", err)
	}

	var resultStruct *structpb.Struct
	resultStruct, err = structpb.NewStruct(testResult)
	if err != nil {
		// 如果直接转换失败，尝试通过JSON作为中介进行转换
		log.Warnf("直接转换Struct失败: %v，尝试通过JSON转换", err)

		// 先解析JSON回到新的map
		var jsonMap map[string]interface{}
		if err = json.Unmarshal(resultJSON, &jsonMap); err != nil {
			log.Errorf("JSON解析失败: %v", err)
			// 即使转换失败，也返回基本响应
			return &pb.OAuth2TestReply{
				Success: true,
				Message: "OAuth2配置测试启动成功，但结果格式转换失败",
				TestId:  testId,
			}, nil
		}

		// 使用解析后的map创建Struct
		resultStruct, err = structpb.NewStruct(jsonMap)
		if err != nil {
			log.Errorf("JSON转换Struct失败: %v", err)
			// 即使转换失败，也返回基本响应
			return &pb.OAuth2TestReply{
				Success: true,
				Message: "OAuth2配置测试启动成功，但结果格式转换失败",
				TestId:  testId,
			}, nil
		}
	}

	log.Infof("resultStruct响应数据结构: %+v", resultStruct)
	return &pb.OAuth2TestReply{
		Success: true,
		Message: "OAuth2配置测试启动成功",
		Data:    resultStruct,
		TestId:  testId,
	}, nil
}

// ValidateWebAuthScript 验证WebAuth脚本语法
func (s *AdminService) ValidateWebAuthScript(ctx context.Context, req *pb.ValidateWebAuthScriptRequest) (*pb.ValidateWebAuthScriptReply, error) {
	s.log.Infof("收到脚本验证请求: 类型=%s, 长度=%d", req.ScriptType, len(req.Script))

	if req.Script == "" {
		return &pb.ValidateWebAuthScriptReply{
			Valid: true,
			Error: "",
		}, nil
	}

	// 创建基础环境变量
	mockEnv := make(map[string]interface{})

	// 根据脚本类型添加特定环境变量
	switch req.ScriptType {
	case "input":
		// 为输入脚本添加常用变量
		mockEnv["step"] = map[string]interface{}{
			"Method":  "GET",
			"URL":     "https://example.com/api",
			"Headers": []interface{}{},
			"Gets":    []interface{}{},
			"Cookies": []interface{}{},
		}
	case "output":
		// 为输出脚本添加常用变量
		mockEnv["response"] = map[string]interface{}{
			"status": 200,
			"body":   "{}",
		}
	}

	// 加入常用变量
	mockEnv["UserName"] = "test_user"
	mockEnv["Password"] = "test_pass"

	// 使用webauth包中的ValidateScript函数验证脚本
	err := webauth.ValidateScript(req.Script, mockEnv)

	if err != nil {
		s.log.Warnf("脚本验证失败: %v", err)
		return &pb.ValidateWebAuthScriptReply{
			Valid: false,
			Error: err.Error(),
		}, nil
	}

	return &pb.ValidateWebAuthScriptReply{
		Valid: true,
		Error: "",
	}, nil
}

// CreateAccountPolicy 创建账号策略
func (s *AdminService) CreateAccountPolicy(ctx context.Context, req *pb.CreateAccountPolicyRequest) (*pb.CreateAccountPolicyReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 创建策略对象
	policy := &model.TbAuthAccountPolicy{
		CorpID:      corpId,
		Name:        req.Name,
		Description: req.Description,

		// 密码策略字段
		PasswordMinLength:              req.PasswordMinLength,
		PasswordMaxLength:              req.PasswordMaxLength,
		PasswordValidityDays:           req.PasswordValidityDays,
		PasswordHistoryCount:           req.PasswordHistoryCount,
		PasswordComplexityUppercase:    req.PasswordComplexityUppercase,
		PasswordComplexityLowercase:    req.PasswordComplexityLowercase,
		PasswordComplexityNumbers:      req.PasswordComplexityNumbers,
		PasswordComplexitySpecialChars: req.PasswordComplexitySpecialChars,
		PasswordComplexityNoUsername:   req.PasswordComplexityNoUsername,

		// 密码错误次数限制
		PasswordFailureEnabled:        req.PasswordFailureEnabled,
		PasswordMaxFailureCount:       req.PasswordMaxFailureCount,
		PasswordLockoutDurationSec:    req.PasswordLockoutDurationSec,
		PasswordResetFailureWindowSec: req.PasswordResetFailureWindowSec,

		// IP错误次数限制
		IPFailureEnabled:        req.IpFailureEnabled,
		IPMaxFailureCount:       req.IpMaxFailureCount,
		IPLockoutDurationSec:    req.IpLockoutDurationSec,
		IPResetFailureWindowSec: req.IpResetFailureWindowSec,

		// 通用字段
		Enable:    req.Enable,
		IsDefault: req.IsDefault,
		Priority:  req.Priority,
	}

	// 调用业务层创建策略
	err = s.accountPolicy.CreateAccountPolicy(ctx, policy)
	if err != nil {
		s.log.Errorf("创建账号策略失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("创建账号策略失败: %v", err))
	}

	return &pb.CreateAccountPolicyReply{
		Status: pb.StatusCode_SUCCESS,
		Id:     policy.ID,
	}, nil
}

// UpdateAccountPolicy 更新账号策略
func (s *AdminService) UpdateAccountPolicy(ctx context.Context, req *pb.UpdateAccountPolicyRequest) (*pb.UpdateAccountPolicyReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 创建更新对象
	policy := &model.TbAuthAccountPolicy{
		ID:          req.Id,
		CorpID:      corpId,
		Name:        req.Name,
		Description: req.Description,

		// 密码策略字段
		PasswordMinLength:              req.PasswordMinLength,
		PasswordMaxLength:              req.PasswordMaxLength,
		PasswordValidityDays:           req.PasswordValidityDays,
		PasswordHistoryCount:           req.PasswordHistoryCount,
		PasswordComplexityUppercase:    req.PasswordComplexityUppercase,
		PasswordComplexityLowercase:    req.PasswordComplexityLowercase,
		PasswordComplexityNumbers:      req.PasswordComplexityNumbers,
		PasswordComplexitySpecialChars: req.PasswordComplexitySpecialChars,
		PasswordComplexityNoUsername:   req.PasswordComplexityNoUsername,

		// 密码错误次数限制
		PasswordFailureEnabled:        req.PasswordFailureEnabled,
		PasswordMaxFailureCount:       req.PasswordMaxFailureCount,
		PasswordLockoutDurationSec:    req.PasswordLockoutDurationSec,
		PasswordResetFailureWindowSec: req.PasswordResetFailureWindowSec,

		// IP错误次数限制
		IPFailureEnabled:        req.IpFailureEnabled,
		IPMaxFailureCount:       req.IpMaxFailureCount,
		IPLockoutDurationSec:    req.IpLockoutDurationSec,
		IPResetFailureWindowSec: req.IpResetFailureWindowSec,

		// 通用字段
		Enable:    req.Enable,
		IsDefault: req.IsDefault,
		Priority:  req.Priority,
	}

	// 调用业务层更新策略
	err = s.accountPolicy.UpdateAccountPolicy(ctx, policy)
	if err != nil {
		s.log.Errorf("更新账号策略失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("更新账号策略失败: %v", err))
	}

	return &pb.UpdateAccountPolicyReply{
		Status: pb.StatusCode_SUCCESS,
	}, nil
}

// DeleteAccountPolicy 删除账号策略
func (s *AdminService) DeleteAccountPolicy(ctx context.Context, req *pb.DeleteAccountPolicyRequest) (*pb.DeleteAccountPolicyReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 调用业务层删除策略
	err = s.accountPolicy.DeleteAccountPolicy(ctx, corpId, req.Id)
	if err != nil {
		s.log.Errorf("删除账号策略失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("删除账号策略失败: %v", err))
	}

	return &pb.DeleteAccountPolicyReply{
		Status: pb.StatusCode_SUCCESS,
	}, nil
}

// GetAccountPolicy 获取账号策略
func (s *AdminService) GetAccountPolicy(ctx context.Context, req *pb.GetAccountPolicyRequest) (*pb.GetAccountPolicyReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 调用业务层获取策略
	policy, err := s.accountPolicy.GetAccountPolicy(ctx, corpId, req.Id)
	if err != nil {
		s.log.Errorf("获取账号策略失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("获取账号策略失败: %v", err))
	}

	return &pb.GetAccountPolicyReply{
		Policy: &pb.AccountPolicyInfo{
			Id:          policy.ID,
			CorpId:      policy.CorpID,
			Name:        policy.Name,
			Description: policy.Description,

			// 密码策略字段
			PasswordMinLength:              policy.PasswordMinLength,
			PasswordMaxLength:              policy.PasswordMaxLength,
			PasswordValidityDays:           policy.PasswordValidityDays,
			PasswordHistoryCount:           policy.PasswordHistoryCount,
			PasswordComplexityUppercase:    policy.PasswordComplexityUppercase,
			PasswordComplexityLowercase:    policy.PasswordComplexityLowercase,
			PasswordComplexityNumbers:      policy.PasswordComplexityNumbers,
			PasswordComplexitySpecialChars: policy.PasswordComplexitySpecialChars,
			PasswordComplexityNoUsername:   policy.PasswordComplexityNoUsername,

			// 密码错误次数限制
			PasswordFailureEnabled:        policy.PasswordFailureEnabled,
			PasswordMaxFailureCount:       policy.PasswordMaxFailureCount,
			PasswordLockoutDurationSec:    policy.PasswordLockoutDurationSec,
			PasswordResetFailureWindowSec: policy.PasswordResetFailureWindowSec,

			// IP错误次数限制
			IpFailureEnabled:        policy.IPFailureEnabled,
			IpMaxFailureCount:       policy.IPMaxFailureCount,
			IpLockoutDurationSec:    policy.IPLockoutDurationSec,
			IpResetFailureWindowSec: policy.IPResetFailureWindowSec,

			// 通用字段
			Enable:    policy.Enable,
			IsDefault: policy.IsDefault,
			Priority:  policy.Priority,
			CreatedAt: policy.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: policy.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// ListAccountPolicies 列出账号策略
func (s *AdminService) ListAccountPolicies(ctx context.Context, req *pb.ListAccountPoliciesRequest) (*pb.ListAccountPoliciesReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 调用业务层获取策略列表（当前业务层不支持分页，先获取全部）
	policies, err := s.accountPolicy.ListAccountPolicies(ctx, corpId)
	if err != nil {
		s.log.Errorf("获取账号策略列表失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("获取账号策略列表失败: %v", err))
	}

	// 转换数据格式
	var policyList []*pb.AccountPolicyInfo
	for _, policy := range policies {
		policyList = append(policyList, &pb.AccountPolicyInfo{
			Id:          policy.ID,
			CorpId:      policy.CorpID,
			Name:        policy.Name,
			Description: policy.Description,

			// 密码策略字段
			PasswordMinLength:              policy.PasswordMinLength,
			PasswordMaxLength:              policy.PasswordMaxLength,
			PasswordValidityDays:           policy.PasswordValidityDays,
			PasswordHistoryCount:           policy.PasswordHistoryCount,
			PasswordComplexityUppercase:    policy.PasswordComplexityUppercase,
			PasswordComplexityLowercase:    policy.PasswordComplexityLowercase,
			PasswordComplexityNumbers:      policy.PasswordComplexityNumbers,
			PasswordComplexitySpecialChars: policy.PasswordComplexitySpecialChars,
			PasswordComplexityNoUsername:   policy.PasswordComplexityNoUsername,

			// 密码错误次数限制
			PasswordFailureEnabled:        policy.PasswordFailureEnabled,
			PasswordMaxFailureCount:       policy.PasswordMaxFailureCount,
			PasswordLockoutDurationSec:    policy.PasswordLockoutDurationSec,
			PasswordResetFailureWindowSec: policy.PasswordResetFailureWindowSec,

			// IP错误次数限制
			IpFailureEnabled:        policy.IPFailureEnabled,
			IpMaxFailureCount:       policy.IPMaxFailureCount,
			IpLockoutDurationSec:    policy.IPLockoutDurationSec,
			IpResetFailureWindowSec: policy.IPResetFailureWindowSec,

			// 通用字段
			Enable:    policy.Enable,
			IsDefault: policy.IsDefault,
			Priority:  policy.Priority,
			CreatedAt: policy.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: policy.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return &pb.ListAccountPoliciesReply{
		Policies: policyList,
		Count:    uint32(len(policies)),
	}, nil
}

// UnlockAccount 解锁账号
func (s *AdminService) UnlockAccount(ctx context.Context, req *pb.UnlockAccountRequest) (*pb.UnlockAccountReply, error) {
	// 调用业务层解锁账号
	err := s.auth.UnlockAccount(ctx, req.UserId)
	if err != nil {
		s.log.Errorf("解锁账号失败: %v", err)
		return &pb.UnlockAccountReply{
			Status:  pb.StatusCode_FAILED,
			Message: fmt.Sprintf("解锁账号失败: %v", err),
		}, nil
	}

	return &pb.UnlockAccountReply{
		Status:  pb.StatusCode_SUCCESS,
		Message: "账号解锁成功",
	}, nil
}

// GetAccountLockInfo 获取账号锁定信息
func (s *AdminService) GetAccountLockInfo(ctx context.Context, req *pb.GetAccountLockInfoRequest) (*pb.GetAccountLockInfoReply, error) {
	// 调用业务层获取账号锁定信息
	lockInfo, err := s.auth.GetAccountLockInfo(ctx, req.UserId)
	if err != nil {
		s.log.Errorf("获取账号锁定信息失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("获取账号锁定信息失败: %v", err))
	}

	// 如果没有锁定信息，返回空
	if lockInfo == nil {
		return &pb.GetAccountLockInfoReply{
			LockInfo: &pb.AccountLockInfo{
				IsLocked:         false,
				LockedUntil:      "",
				Reason:           "",
				FailureCount:     0,
				LastFailureTime:  "",
				RemainingSeconds: 0,
			},
		}, nil
	}

	return &pb.GetAccountLockInfoReply{
		LockInfo: &pb.AccountLockInfo{
			IsLocked:         true,
			LockedUntil:      lockInfo.LockedUntil.Format("2006-01-02 15:04:05"),
			Reason:           lockInfo.Reason,
			FailureCount:     0,  // dto.AccountLockInfo 没有这个字段，设为0
			LastFailureTime:  "", // dto.AccountLockInfo 没有这个字段，设为空
			RemainingSeconds: int32(time.Until(lockInfo.LockedUntil).Seconds()),
		},
	}, nil
}

// ListLockedAccounts 获取锁定账户列表
func (s *AdminService) ListLockedAccounts(ctx context.Context, req *pb.ListLockedAccountsRequest) (*pb.ListLockedAccountsReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil || corpId == "" {
		return nil, fmt.Errorf("corp id is required")
	}

	// 设置默认分页参数
	limit := req.Limit
	offset := req.Offset
	if limit <= 0 {
		limit = 10
	}

	// 调用业务层获取锁定账户列表
	lockedAccounts, total, err := s.auth.ListLockedAccounts(ctx, corpId, int(limit), int(offset), req.Search, req.LockType)
	if err != nil {
		return nil, fmt.Errorf("获取锁定账户列表失败: %w", err)
	}

	// 转换为 protobuf 格式
	var accounts []*pb.LockedAccountInfo
	for _, account := range lockedAccounts {
		// 构建 AccountLockInfo
		lockInfo := &pb.AccountLockInfo{
			IsLocked:         true,
			LockedUntil:      account.LockedUntil.Format("2006-01-02 15:04:05"),
			Reason:           account.LockReason,
			FailureCount:     0,  // 这里设为0，因为dto中没有这个字段
			LastFailureTime:  "", // 这里设为空，因为dto中没有这个字段
			RemainingSeconds: int32(time.Until(account.LockedUntil).Seconds()),
		}

		pbAccount := &pb.LockedAccountInfo{
			UserId:    account.UserID,
			Username:  account.Username,
			GroupId:   account.GroupID,
			GroupName: account.GroupName,
			LockInfo:  lockInfo,
		}
		accounts = append(accounts, pbAccount)
	}

	return &pb.ListLockedAccountsReply{
		Accounts: accounts,
		Count:    uint32(total),
	}, nil
}

// BatchUnlockAccounts 批量解锁账号
func (s *AdminService) BatchUnlockAccounts(ctx context.Context, req *pb.BatchUnlockAccountsRequest) (*pb.BatchUnlockAccountsReply, error) {
	if len(req.UserIds) == 0 {
		return &pb.BatchUnlockAccountsReply{
			Status:  pb.StatusCode_FAILED,
			Message: "用户ID列表不能为空",
		}, nil
	}

	var successCount, failedCount int32
	var failedUserIds []string

	// 逐个解锁账号
	for _, userId := range req.UserIds {
		err := s.auth.UnlockAccount(ctx, userId)
		if err != nil {
			s.log.Warnf("解锁账号 %s 失败: %v", userId, err)
			failedCount++
			failedUserIds = append(failedUserIds, userId)
		} else {
			successCount++
		}
	}

	// 构建响应消息
	var status pb.StatusCode
	var message string

	if failedCount == 0 {
		status = pb.StatusCode_SUCCESS
		message = fmt.Sprintf("批量解锁成功，共解锁 %d 个账号", successCount)
	} else if successCount == 0 {
		status = pb.StatusCode_FAILED
		message = fmt.Sprintf("批量解锁失败，%d 个账号解锁失败", failedCount)
	} else {
		status = pb.StatusCode_SUCCESS
		message = fmt.Sprintf("批量解锁部分成功，成功 %d 个，失败 %d 个", successCount, failedCount)
	}

	return &pb.BatchUnlockAccountsReply{
		Status:        status,
		Message:       message,
		SuccessCount:  successCount,
		FailedCount:   failedCount,
		FailedUserIds: failedUserIds,
	}, nil
}

// IP锁定管理相关服务方法

// UnlockIP 解锁IP
func (s *AdminService) UnlockIP(ctx context.Context, req *pb.UnlockIPRequest) (*pb.UnlockIPReply, error) {
	// 获取企业ID - 从上下文获取
	corpID, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UnlockIPReply{
			Status:  pb.StatusCode_FAILED,
			Message: fmt.Sprintf("获取企业ID失败: %v", err),
		}, nil
	}

	// 调用业务层解锁IP
	unlockErr := s.auth.UnlockIP(ctx, req.IpAddress, corpID)
	if unlockErr != nil {
		s.log.Errorf("解锁IP失败: %v", unlockErr)
		return &pb.UnlockIPReply{
			Status:  pb.StatusCode_FAILED,
			Message: fmt.Sprintf("解锁IP失败: %v", unlockErr),
		}, nil
	}

	return &pb.UnlockIPReply{
		Status:  pb.StatusCode_SUCCESS,
		Message: "IP解锁成功",
	}, nil
}

// BatchUnlockIPs 批量解锁IP
func (s *AdminService) BatchUnlockIPs(ctx context.Context, req *pb.BatchUnlockIPsRequest) (*pb.BatchUnlockIPsReply, error) {
	if len(req.IpAddresses) == 0 {
		return &pb.BatchUnlockIPsReply{
			Status:  pb.StatusCode_FAILED,
			Message: "IP地址列表不能为空",
		}, nil
	}

	// 获取企业ID
	corpID, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.BatchUnlockIPsReply{
			Status:  pb.StatusCode_FAILED,
			Message: fmt.Sprintf("获取企业ID失败: %v", err),
		}, nil
	}

	// 调用业务层批量解锁IP
	successCount, failedCount, failedIPs, err := s.auth.BatchUnlockIPs(ctx, corpID, req.IpAddresses, req.Reason)
	if err != nil {
		s.log.Errorf("批量解锁IP失败: %v", err)
		return &pb.BatchUnlockIPsReply{
			Status:  pb.StatusCode_FAILED,
			Message: fmt.Sprintf("批量解锁IP失败: %v", err),
		}, nil
	}

	var status pb.StatusCode
	var message string

	if failedCount == 0 {
		status = pb.StatusCode_SUCCESS
		message = fmt.Sprintf("批量解锁成功，共解锁 %d 个IP", successCount)
	} else if successCount == 0 {
		status = pb.StatusCode_FAILED
		message = fmt.Sprintf("批量解锁失败，%d 个IP解锁失败", failedCount)
	} else {
		status = pb.StatusCode_SUCCESS
		message = fmt.Sprintf("批量解锁部分成功，成功 %d 个，失败 %d 个", successCount, failedCount)
	}

	return &pb.BatchUnlockIPsReply{
		Status:            status,
		Message:           message,
		SuccessCount:      successCount,
		FailedCount:       failedCount,
		FailedIpAddresses: failedIPs,
	}, nil
}

// GetIPLockInfo 获取IP锁定信息
func (s *AdminService) GetIPLockInfo(ctx context.Context, req *pb.GetIPLockInfoRequest) (*pb.GetIPLockInfoReply, error) {
	// 调用业务层获取IP锁定信息
	lockInfo, err := s.auth.GetIPLockInfo(ctx, req.IpAddress)
	if err != nil {
		s.log.Errorf("获取IP锁定信息失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("获取IP锁定信息失败: %v", err))
	}

	// 获取IP失败信息
	failureInfo, err := s.auth.GetIPFailureCount(ctx, req.IpAddress)
	if err != nil {
		s.log.Errorf("获取IP失败信息失败: %v", err)
		failureInfo = &dto.IPFailureInfo{Count: 0, LastFailureTime: time.Time{}}
	}

	// 构建响应
	var ipLockInfo *pb.IPLockInfo
	if lockInfo != nil {
		remainingSeconds := int32(time.Until(lockInfo.LockedUntil).Seconds())
		if remainingSeconds < 0 {
			remainingSeconds = 0
		}

		ipLockInfo = &pb.IPLockInfo{
			IsLocked:         true,
			LockedUntil:      lockInfo.LockedUntil.Format(time.RFC3339),
			Reason:           lockInfo.Reason,
			FailureCount:     int32(failureInfo.Count),
			LastFailureTime:  failureInfo.LastFailureTime.Format(time.RFC3339),
			RemainingSeconds: remainingSeconds,
			IpAddress:        req.IpAddress,
		}
	} else {
		ipLockInfo = &pb.IPLockInfo{
			IsLocked:         false,
			LockedUntil:      "",
			Reason:           "",
			FailureCount:     int32(failureInfo.Count),
			LastFailureTime:  failureInfo.LastFailureTime.Format(time.RFC3339),
			RemainingSeconds: 0,
			IpAddress:        req.IpAddress,
		}
	}

	return &pb.GetIPLockInfoReply{
		LockInfo: ipLockInfo,
	}, nil
}

// ListLockedIPs 获取锁定IP列表
func (s *AdminService) ListLockedIPs(ctx context.Context, req *pb.ListLockedIPsRequest) (*pb.ListLockedIPsReply, error) {
	// 获取企业ID
	corpID := req.CorpId
	if corpID == "" {
		var err error
		corpID, err = common.GetCorpId(ctx)
		if err != nil {
			return nil, v1.ErrorParamError(fmt.Sprintf("获取企业ID失败: %v", err))
		}
	}

	// 设置默认分页参数
	limit := int(req.Limit)
	offset := int(req.Offset)
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 调用业务层获取锁定IP列表
	lockedIPs, total, err := s.auth.ListLockedIPs(ctx, corpID, limit, offset, req.Search)
	if err != nil {
		s.log.Errorf("获取锁定IP列表失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("获取锁定IP列表失败: %v", err))
	}

	// 转换为protobuf格式
	var pbLockedIPs []*pb.LockedIPInfo
	for _, lockedIP := range lockedIPs {
		var ipLockInfo *pb.IPLockInfo
		if lockedIP.LockInfo != nil {
			ipLockInfo = &pb.IPLockInfo{
				IsLocked:         lockedIP.LockInfo.IsLocked,
				LockedUntil:      lockedIP.LockInfo.LockedUntil.Format(time.RFC3339),
				Reason:           lockedIP.LockInfo.Reason,
				FailureCount:     int32(lockedIP.LockInfo.FailureCount),
				LastFailureTime:  lockedIP.LockInfo.LastFailureTime.Format(time.RFC3339),
				RemainingSeconds: lockedIP.LockInfo.RemainingSeconds,
				IpAddress:        lockedIP.LockInfo.IPAddress,
			}
		}

		pbLockedIP := &pb.LockedIPInfo{
			IpAddress:     lockedIP.IPAddress,
			CorpId:        lockedIP.CorpID,
			CorpName:      lockedIP.CorpName,
			LockInfo:      ipLockInfo,
			Location:      lockedIP.Location,
			TotalAttempts: int32(lockedIP.TotalAttempts),
		}

		pbLockedIPs = append(pbLockedIPs, pbLockedIP)
	}

	return &pb.ListLockedIPsReply{
		Ips:   pbLockedIPs,
		Count: uint32(total),
	}, nil
}

// 会话管理相关方法

// ListUserSessions 获取用户会话列表
func (s *AdminService) ListUserSessions(ctx context.Context, req *pb.ListUserSessionsRequest) (*pb.ListUserSessionsReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 设置默认分页参数
	limit := int(req.Limit)
	offset := int(req.Offset)
	if limit <= 0 {
		limit = 20
	}

	// 调用认证业务层获取会话列表
	sessions, total, err := s.auth.ListUserSessions(ctx, corpId, req.UserId, req.UserName, req.ClientCategory, limit, offset)
	if err != nil {
		s.log.Errorf("获取用户会话列表失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("获取用户会话列表失败: %v", err))
	}

	// 转换为protobuf格式
	var pbSessions []*pb.UserSessionInfo
	for _, session := range sessions {
		// 转换用户实体
		var pbUsers []*pb.UserEntity
		for _, user := range session.Users {
			pbUser := &pb.UserEntity{
				Id:           user.ID,
				Name:         user.Name,
				GroupId:      user.GroupID,
				SourceId:     user.SourceID,
				Phone:        user.Phone,
				Email:        user.Email,
				Roles:        user.Roles,
				Enable:       user.Enable,
				ExpireType:   user.ExpireType,
				ExpireEnd:    user.ExpireEnd,
				GroupName:    user.GroupName,
				DisplayName:  user.DisplayName,
				SourceType:   user.SourceType,
				Identifier:   user.Identifier,
				AuthType:     user.AuthType,
				ActiveTime:   user.ActiveTime,
				IdleDay:      user.IdleDay,
				LockStatus:   user.LockStatus,
				SecurityCode: user.SecurityCode,
			}
			pbUsers = append(pbUsers, pbUser)
		}

		pbSession := &pb.UserSessionInfo{
			SessionId:        session.ID,
			Users:            pbUsers,
			OperatingSystem:  string(session.ClientType),     // 详细操作系统信息
			ClientCategory:   string(session.ClientCategory), // pc/mobile分类
			DeviceInfo:       session.DeviceInfo,
			IpAddress:        session.IPAddress,
			LoginTime:        session.LoginTime.Format("2006-01-02 15:04:05"),
			LastActivityTime: session.LastActiveTime.Format("2006-01-02 15:04:05"),
			TokenJti:         session.JWTId,
			PolicyId:         session.PolicyID,
		}
		pbSessions = append(pbSessions, pbSession)
	}

	return &pb.ListUserSessionsReply{
		Sessions: pbSessions,
		Count:    uint32(total),
	}, nil
}

// KickUserSession 踢出用户会话
func (s *AdminService) KickUserSession(ctx context.Context, req *pb.KickUserSessionRequest) (*pb.KickUserSessionReply, error) {
	if req.SessionId == "" {
		return &pb.KickUserSessionReply{
			Status:  pb.StatusCode_FAILED,
			Message: "会话ID不能为空",
		}, nil
	}

	// 调用认证业务层踢出会话
	err := s.auth.KickUserSessionBySessionId(ctx, req.SessionId, req.Reason)
	if err != nil {
		s.log.Errorf("踢出用户会话失败: %v", err)
		return &pb.KickUserSessionReply{
			Status:  pb.StatusCode_FAILED,
			Message: fmt.Sprintf("踢出用户会话失败: %v", err),
		}, nil
	}

	return &pb.KickUserSessionReply{
		Status:  pb.StatusCode_SUCCESS,
		Message: "用户会话已成功踢出",
	}, nil
}

// GetClientLimits 获取客户端限制配置
func (s *AdminService) GetClientLimits(ctx context.Context, req *pb.GetClientLimitsRequest) (*pb.GetClientLimitsReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	// 调用认证策略业务层获取客户端限制配置
	clientLimits, err := s.authPolicy.GetClientLimits(ctx, corpId, req.PolicyId)
	if err != nil {
		s.log.Errorf("获取客户端限制配置失败: %v", err)
		return nil, v1.ErrorParamError(fmt.Sprintf("获取客户端限制配置失败: %v", err))
	}

	// 如果没有配置，返回默认配置
	if clientLimits == nil {
		defaultLimits := dto.DefaultClientLimits()
		clientLimits = &defaultLimits
	}

	return &pb.GetClientLimitsReply{
		Config: &pb.ClientLimitsConfig{
			PcMaxSessions:     int32(clientLimits.PCMaxClients),
			MobileMaxSessions: int32(clientLimits.MobileMaxClients),
			OverflowStrategy:  string(clientLimits.OverflowStrategy),
		},
	}, nil
}

// UpdateClientLimits 更新客户端限制配置
func (s *AdminService) UpdateClientLimits(ctx context.Context, req *pb.UpdateClientLimitsRequest) (*pb.UpdateClientLimitsReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return nil, v1.ErrorParamError("缺少企业ID")
	}

	if req.Config == nil {
		return &pb.UpdateClientLimitsReply{
			Status:  pb.StatusCode_FAILED,
			Message: "客户端限制配置不能为空",
		}, nil
	}

	// 构建更新参数
	clientLimits := &dto.ClientLimits{
		PCMaxClients:     int(req.Config.PcMaxSessions),
		MobileMaxClients: int(req.Config.MobileMaxSessions),
		OverflowStrategy: dto.OverflowStrategy(req.Config.OverflowStrategy),
	}

	// 调用认证策略业务层更新客户端限制配置
	err = s.authPolicy.UpdateClientLimits(ctx, corpId, req.PolicyId, clientLimits)
	if err != nil {
		s.log.Errorf("更新客户端限制配置失败: %v", err)
		return &pb.UpdateClientLimitsReply{
			Status:  pb.StatusCode_FAILED,
			Message: fmt.Sprintf("更新客户端限制配置失败: %v", err),
		}, nil
	}

	return &pb.UpdateClientLimitsReply{
		Status:  pb.StatusCode_SUCCESS,
		Message: "客户端限制配置更新成功",
	}, nil
}
