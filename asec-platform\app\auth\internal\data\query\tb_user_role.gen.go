// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserRole(db *gorm.DB, opts ...gen.DOOption) tbUserRole {
	_tbUserRole := tbUserRole{}

	_tbUserRole.tbUserRoleDo.UseDB(db, opts...)
	_tbUserRole.tbUserRoleDo.UseModel(&model.TbUserRole{})

	tableName := _tbUserRole.tbUserRoleDo.TableName()
	_tbUserRole.ALL = field.NewAsterisk(tableName)
	_tbUserRole.UserID = field.NewString(tableName, "user_id")
	_tbUserRole.RoleID = field.NewString(tableName, "role_id")
	_tbUserRole.CorpID = field.NewString(tableName, "corp_id")
	_tbUserRole.CreatedAt = field.NewTime(tableName, "created_at")
	_tbUserRole.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbUserRole.fillFieldMap()

	return _tbUserRole
}

type tbUserRole struct {
	tbUserRoleDo tbUserRoleDo

	ALL       field.Asterisk
	UserID    field.String
	RoleID    field.String
	CorpID    field.String
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (t tbUserRole) Table(newTableName string) *tbUserRole {
	t.tbUserRoleDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserRole) As(alias string) *tbUserRole {
	t.tbUserRoleDo.DO = *(t.tbUserRoleDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserRole) updateTableName(table string) *tbUserRole {
	t.ALL = field.NewAsterisk(table)
	t.UserID = field.NewString(table, "user_id")
	t.RoleID = field.NewString(table, "role_id")
	t.CorpID = field.NewString(table, "corp_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbUserRole) WithContext(ctx context.Context) *tbUserRoleDo {
	return t.tbUserRoleDo.WithContext(ctx)
}

func (t tbUserRole) TableName() string { return t.tbUserRoleDo.TableName() }

func (t tbUserRole) Alias() string { return t.tbUserRoleDo.Alias() }

func (t *tbUserRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserRole) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["role_id"] = t.RoleID
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbUserRole) clone(db *gorm.DB) tbUserRole {
	t.tbUserRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserRole) replaceDB(db *gorm.DB) tbUserRole {
	t.tbUserRoleDo.ReplaceDB(db)
	return t
}

type tbUserRoleDo struct{ gen.DO }

func (t tbUserRoleDo) Debug() *tbUserRoleDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserRoleDo) WithContext(ctx context.Context) *tbUserRoleDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserRoleDo) ReadDB() *tbUserRoleDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserRoleDo) WriteDB() *tbUserRoleDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserRoleDo) Session(config *gorm.Session) *tbUserRoleDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserRoleDo) Clauses(conds ...clause.Expression) *tbUserRoleDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserRoleDo) Returning(value interface{}, columns ...string) *tbUserRoleDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserRoleDo) Not(conds ...gen.Condition) *tbUserRoleDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserRoleDo) Or(conds ...gen.Condition) *tbUserRoleDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserRoleDo) Select(conds ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserRoleDo) Where(conds ...gen.Condition) *tbUserRoleDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserRoleDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserRoleDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserRoleDo) Order(conds ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserRoleDo) Distinct(cols ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserRoleDo) Omit(cols ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserRoleDo) Join(table schema.Tabler, on ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserRoleDo) Group(cols ...field.Expr) *tbUserRoleDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserRoleDo) Having(conds ...gen.Condition) *tbUserRoleDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserRoleDo) Limit(limit int) *tbUserRoleDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserRoleDo) Offset(offset int) *tbUserRoleDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserRoleDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserRoleDo) Unscoped() *tbUserRoleDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserRoleDo) Create(values ...*model.TbUserRole) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserRoleDo) CreateInBatches(values []*model.TbUserRole, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserRoleDo) Save(values ...*model.TbUserRole) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserRoleDo) First() (*model.TbUserRole, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserRole), nil
	}
}

func (t tbUserRoleDo) Take() (*model.TbUserRole, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserRole), nil
	}
}

func (t tbUserRoleDo) Last() (*model.TbUserRole, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserRole), nil
	}
}

func (t tbUserRoleDo) Find() ([]*model.TbUserRole, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserRole), err
}

func (t tbUserRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserRole, err error) {
	buf := make([]*model.TbUserRole, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserRoleDo) FindInBatches(result *[]*model.TbUserRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserRoleDo) Attrs(attrs ...field.AssignExpr) *tbUserRoleDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserRoleDo) Assign(attrs ...field.AssignExpr) *tbUserRoleDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserRoleDo) Joins(fields ...field.RelationField) *tbUserRoleDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserRoleDo) Preload(fields ...field.RelationField) *tbUserRoleDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserRoleDo) FirstOrInit() (*model.TbUserRole, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserRole), nil
	}
}

func (t tbUserRoleDo) FirstOrCreate() (*model.TbUserRole, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserRole), nil
	}
}

func (t tbUserRoleDo) FindByPage(offset int, limit int) (result []*model.TbUserRole, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserRoleDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserRoleDo) Delete(models ...*model.TbUserRole) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserRoleDo) withDO(do gen.Dao) *tbUserRoleDo {
	t.DO = *do.(*gen.DO)
	return t
}
