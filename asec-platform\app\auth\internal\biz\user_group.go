package biz

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jinzhu/copier"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"github.com/google/uuid"

	"github.com/go-kratos/kratos/v2/log"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
)

type UserGroupRepo interface {
	CreateUserGroup(ctx context.Context, param dto.CreateGroupDaoParam) error
	CreateUserGroupWithSync(ctx context.Context, param dto.CreateRootGroupDaoParam) error
	BatchInsertGroup(ctx context.Context, groups []*dto.CreateGroupDaoParam) error
	DeleteSubGroupsOfRoot(ctx context.Context, corpId, rootGroupId string) error
	UpdateRootGroupSync(ctx context.Context, rootGroupId string, lastSyncTime, nextSyncTime time.Time, syncStatus string) error
	CreateSyncLog(ctx context.Context, param dto.CreateSyncLogParam) error

	SwitchAutoSync(ctx context.Context, groupId string, autoSync bool) error

	GetUserGroupByNameInParent(ctx context.Context, corpId, name, parentGroupId string) (*model.TbUserGroup, error)
	GetUserGroup(ctx context.Context, corpId, id string) (*model.TbUserGroup, error)
	GetIDPIdsOfGroup(ctx context.Context, corpId, id string) ([]string, error)
	GetAllSourceInGroupIds(ctx context.Context, corpId string, groupIds []string) ([]string, error)
	GetGroupSyncConfig(ctx context.Context, corpId string, groupId string) ([]dto.KV, error)
	GetRootGroupSourceType(ctx context.Context, corpId string, rootGroupId string) (string, error)
	GetRootGroupSync(ctx context.Context, rootGroupId string) (*model.TbUserGroupSync, error)
	GetRunningRootGroupSync(ctx context.Context, rootGroupId string) (int64, error)

	ListUserGroup(ctx context.Context, corpId string) ([]*model.TbUserGroup, error)
	ListUserGroupByGroupIds(ctx context.Context, corpId string, groupIds []string) ([]*model.TbUserGroup, error)
	ListGroupNodes(ctx context.Context, corpId string) ([]*dto.UserGroupNode, error)
	ListRootGroup(ctx context.Context, corpId string, limit, offset int) ([]dto.RootGroup, error)
	ListRootGroupIds(ctx context.Context, corpId string, limit, offset int) ([]string, error)
	ListRootGroupWithoutSync(ctx context.Context, corpId string) ([]dto.RootGroup, error)
	ListAllRootGroupWithSync(ctx context.Context) ([]*dto.RootGroupSync, error)
	ListUserGroupWithIDP(ctx context.Context, corpId string) ([]dto.UserGroupWithIDP, error)
	ListBindGroupInfo(ctx context.Context, corpId string, groupIds []string) ([]dto.BindGroupInfo, error)
	ListRootGroupBindMainIdp(ctx context.Context, corpId, groupId string) ([]dto.IDPBasic, error)
	ListRootGroupBindAssistIdp(ctx context.Context, corpId string) ([]dto.IDPBasic, error)
	ListGroupSyncLog(ctx context.Context, corpId, groupId string, limit, offset int) ([]*model.TbUserGroupSyncLog, error)

	CountRootGroup(ctx context.Context, corpId string) (int64, error)
	CountGroupInParent(ctx context.Context, corpId string, parentGroupId string) (int64, error)
	CountRootGroupInGroupIds(ctx context.Context, corpId string, groupIds []string) (int64, error)
	DeleteGroup(ctx context.Context, corpId string, groupIds, userIds []string, sourceType, name string) error
	ListUserGroupByParGroupId(ctx context.Context, corpId string, parGroupId []string) ([]*model.TbUserGroup, error)
	UpdateRootGroup(ctx context.Context, corpId string, param dto.UpdateGroupParam, parentGroup *model.TbUserGroup, group *model.TbUserGroup) error
	UpdateThirdRootGroup(ctx context.Context, corpId string, param dto.UpdateGroupParam, thirdType string, groupName string) error
	GetGroupBasicInfo(ctx context.Context, corpId, groupId string) (dto.GroupBasicInfo, error)
	CountGroupSyncLog(ctx context.Context, corpId, groupId string) (int64, error)
}

type UserGroupUsecase struct {
	repo       UserGroupRepo
	idpRepo    IdpRepo
	userSource UserSourceRepo
	userRepo   UserRepo
	authPolicy AuthPolicyRepo

	log *log.Helper
}

func NewUserGroupUsecase(repo UserGroupRepo, idpRepo IdpRepo, userSource UserSourceRepo, authPolicy AuthPolicyRepo, userRepo UserRepo, logger log.Logger) *UserGroupUsecase {
	return &UserGroupUsecase{
		repo:       repo,
		idpRepo:    idpRepo,
		userSource: userSource,
		authPolicy: authPolicy,
		userRepo:   userRepo,

		log: log.NewHelper(logger),
	}
}

func (u UserGroupUsecase) CreateUserGroup(ctx context.Context, param dto.CreateGroupParam) error {
	if param.ParentGroupId == dto.FakeRootUserGroupId {
		return pb.ErrorRecordNotFound("parent group not found")
	}

	// 重名校验
	_, err := u.repo.GetUserGroupByNameInParent(ctx, param.CorpId, param.Name, param.ParentGroupId)
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("GetUserGroupByNameInParent failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", param.Name)
	}

	// 父组校验
	father, err := u.repo.GetUserGroup(ctx, param.CorpId, param.ParentGroupId)
	if err != nil {
		return err
	}

	var path string
	var id string

	if father.Path == "/" {
		path = father.Path + father.Name
	} else {
		path = father.Path + "/" + father.Name
	}

	//支持从外部指定groupId
	if param.GroupId == "" {
		id = uuid.New().String()
	} else {
		id = param.GroupId
	}

	var daoParam = dto.CreateGroupDaoParam{
		CorpId:        param.CorpId,
		Id:            id,
		Name:          param.Name,
		Description:   param.Description,
		ParentGroupId: param.ParentGroupId,
		Path:          path,
		SourceId:      param.SourceId,
		RootGroupID:   father.RootGroupID,
	}
	if err = u.repo.CreateUserGroup(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUserGroup failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserGroupUsecase) ListUserGroup(ctx context.Context, corpId, parenGroupId string) (dto.ListUserGroupResp, error) {
	var parentGroup = dto.FakeRootUserGroupId
	if parenGroupId != "" {
		parentGroup = parenGroupId
	}

	// 获取租户下的整颗用户目录树
	groupNodes, err := u.repo.ListGroupNodes(ctx, corpId)
	if err != nil {
		u.log.Errorf("ListUserGroup failed. err=%v", err)
		return dto.ListUserGroupResp{}, err
	}
	rootNode, err := common.BuildGroupTree(groupNodes)
	if err != nil {
		u.log.Errorf("BuildGroupTree failed. err=%v", err)
		return dto.ListUserGroupResp{}, err
	}

	// 根据父级组id过滤
	subTree := common.SearchSubTree(rootNode, parentGroup)
	return dto.ListUserGroupResp{
		Root:  subTree,
		Count: uint32(subTree.CountChildren()),
	}, nil
}

func (u UserGroupUsecase) ListRootGroup(ctx context.Context, corpId string, limit, offset uint32) (dto.ListRootGroupResult, error) {
	var result []dto.RootGroup
	rootGroups, err := u.repo.ListRootGroup(ctx, corpId, int(limit), int(offset))
	if err != nil {
		u.log.Errorf("ListRootGroup failed. err=%v", err)
		return dto.ListRootGroupResult{}, err
	}
	result = append(result, rootGroups...)
	count, err := u.repo.CountRootGroup(ctx, corpId)
	if err != nil {
		u.log.Errorf("CountRootGroup failed. err=%v", err)
		return dto.ListRootGroupResult{}, err
	}
	return dto.ListRootGroupResult{RootGroups: result, Count: uint32(count)}, nil
}

func (u UserGroupUsecase) GetRootGroup(ctx context.Context, corpId, groupId string) (dto.GetRootGroupResult, error) {
	basicInfo, err := u.repo.GetGroupBasicInfo(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetGroupBasicInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	if basicInfo.SourceType != string(dto.UserSourceLocal) {
		// 同步配置信息
		var syncGroupInfo dto.SyncGroupInfo
		groupSync, err := u.repo.GetRootGroupSync(ctx, groupId)
		if err != nil {
			u.log.Errorf("GetGroupSync Failed. err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		err = copier.Copy(&syncGroupInfo, &groupSync)
		if err != nil {
			u.log.Errorf("CopyGroupSync Failed. err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		key := []byte(groupId[:32])
		var ciphertext string
		// 根据不同的来源获取对应字段
		switch basicInfo.SourceType {
		case string(dto.UserSourceQiYeWx):
			return u.handleQiYeWx(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
		case string(dto.UserSourceFeiShu):
			return u.handleFeiShu(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
		case string(dto.UserSourceDingtalk):
			return u.handleDingtalk(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
		case string(dto.UserSourceLdap):
			return u.handleSourceLdap(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
		case string(dto.UserSourceMsad):
			return u.handleSourceMsad(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
		case string(dto.UserSourceInfogo):
			return u.handleSourceInfogo(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
		case string(dto.UserSourceOAuth2): // 添加: OAuth2.0用户身份源
			return u.handleOAuth2(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
		default:
			switch basicInfo.TemplateType {
			case string(dto.UserSourceOAuth2):
				return u.handleOAuth2(ctx, corpId, groupId, ciphertext, key, syncGroupInfo, basicInfo)
			}
		}

	}
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo}, nil
}

func (u UserGroupUsecase) handleOAuth2(ctx context.Context, corpId, groupId, ciphertext string, key []byte, syncGroupInfo dto.SyncGroupInfo, basicInfo dto.GroupBasicInfo) (dto.GetRootGroupResult, error) {
	syncConfigInfo, err := u.repo.GetGroupSyncConfig(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetSyncConfigInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncKvMap := make(map[string]string)
	for _, v := range syncConfigInfo {
		syncKvMap[v.Key] = v.Value
	}
	fieldMapValue := syncKvMap[dto.AttrKeyOAuth2FieldMap]
	GlobalData := syncKvMap[dto.AttrKeyOAuth2GlobalData]
	if fieldMapValue != "" {
		var fieldMap []dto.KV
		err = json.Unmarshal([]byte(fieldMapValue), &fieldMap)
		if err != nil {
			u.log.Errorf("ParseFieldMapFailed err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		syncGroupInfo.FieldMap = fieldMap
	}
	syncGroupInfo.OAuth2Config.GlobalData = GlobalData
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo, SyncGroupInfo: syncGroupInfo}, nil
}

func (u UserGroupUsecase) handleQiYeWx(ctx context.Context, corpId, groupId, ciphertext string, key []byte, syncGroupInfo dto.SyncGroupInfo, basicInfo dto.GroupBasicInfo) (dto.GetRootGroupResult, error) {
	syncConfigInfo, err := u.repo.GetGroupSyncConfig(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetSyncConfigInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncKvMap := make(map[string]string)
	for _, v := range syncConfigInfo {
		syncKvMap[v.Key] = v.Value
	}
	plaintext := []byte(syncKvMap[dto.AttrKeyWxAgentSecret])
	ciphertext, err = common.Encrypt(key, plaintext)
	if err != nil {
		u.log.Errorf("Encrypt err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncGroupInfo.CorpId = syncKvMap[dto.AttrKeyWxCorpId]
	syncGroupInfo.AgentId = syncKvMap[dto.AttrKeyWxAgentId]
	syncGroupInfo.Secret = ciphertext
	fieldMapValue := syncKvMap[dto.AttrKeyWxFieldMap]
	if fieldMapValue != "" {
		var fieldMap []dto.KV
		err = json.Unmarshal([]byte(fieldMapValue), &fieldMap)
		if err != nil {
			u.log.Errorf("ParseFieldMapFailed err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		syncGroupInfo.FieldMap = fieldMap
	}
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo, SyncGroupInfo: syncGroupInfo}, nil
}

func (u UserGroupUsecase) handleFeiShu(ctx context.Context, corpId, groupId, ciphertext string, key []byte, syncGroupInfo dto.SyncGroupInfo, basicInfo dto.GroupBasicInfo) (dto.GetRootGroupResult, error) {
	syncConfigInfo, err := u.repo.GetGroupSyncConfig(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetSyncConfigInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncKvMap := make(map[string]string)
	for _, v := range syncConfigInfo {
		syncKvMap[v.Key] = v.Value
	}
	plaintext := []byte(syncKvMap[dto.AttrKeyFeiShuSecret])
	ciphertext, err = common.Encrypt(key, plaintext)
	if err != nil {
		u.log.Errorf("Encrypt err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncGroupInfo.AppId = syncKvMap[dto.AttrKeyFeiShuAppId]
	syncGroupInfo.FeishuConfig.AppSecret = ciphertext
	fieldMapValue := syncKvMap[dto.AttrKeyFeiShuFieldMap]
	if fieldMapValue != "" {
		var fieldMap []dto.KV
		err = json.Unmarshal([]byte(fieldMapValue), &fieldMap)
		if err != nil {
			u.log.Errorf("ParseFieldMapFailed err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		syncGroupInfo.FieldMap = fieldMap
	}
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo, SyncGroupInfo: syncGroupInfo}, nil
}

func (u UserGroupUsecase) handleDingtalk(ctx context.Context, corpId, groupId, ciphertext string, key []byte, syncGroupInfo dto.SyncGroupInfo, basicInfo dto.GroupBasicInfo) (dto.GetRootGroupResult, error) {
	syncConfigInfo, err := u.repo.GetGroupSyncConfig(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetSyncConfigInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncKvMap := make(map[string]string)
	for _, v := range syncConfigInfo {
		syncKvMap[v.Key] = v.Value
	}
	plaintext := []byte(syncKvMap[dto.AttrKeyDingtalkAppSecret])
	ciphertext, err = common.Encrypt(key, plaintext)
	if err != nil {
		u.log.Errorf("Encrypt err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncGroupInfo.AppKey = syncKvMap[dto.AttrKeyDingtalkAppKey]
	syncGroupInfo.DingtalkConfig.AppSecret = ciphertext
	fieldMapValue := syncKvMap[dto.AttrKeyDingtalkFieldMap]
	if fieldMapValue != "" {
		var fieldMap []dto.KV
		err = json.Unmarshal([]byte(fieldMapValue), &fieldMap)
		if err != nil {
			u.log.Errorf("ParseFieldMapFailed err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		syncGroupInfo.FieldMap = fieldMap
	}
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo, SyncGroupInfo: syncGroupInfo}, nil
}

func (u UserGroupUsecase) handleSourceLdap(ctx context.Context, corpId, groupId, ciphertext string, key []byte, syncGroupInfo dto.SyncGroupInfo, basicInfo dto.GroupBasicInfo) (dto.GetRootGroupResult, error) {
	syncConfigInfo, err := u.repo.GetGroupSyncConfig(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetSyncConfigInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncKvMap := make(map[string]string)
	for _, v := range syncConfigInfo {
		syncKvMap[v.Key] = v.Value
	}
	plaintext := []byte(syncKvMap[dto.AttrKeyAdAdministratorPassword])
	ciphertext, err = common.Encrypt(key, plaintext)
	if err != nil {
		u.log.Errorf("Encrypt err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncGroupInfo.ServerAddress = syncKvMap[dto.AttrKeyAdServerAddress]
	syncGroupInfo.AdministratorPassword = ciphertext
	syncGroupInfo.AdministratorAccount = syncKvMap[dto.AttrKeyAdAdministratorAccount]
	syncGroupInfo.SearchEntry = syncKvMap[dto.AttrKeyAdSearchEntry]
	syncGroupInfo.UserFilter = syncKvMap[dto.AttrKeyAdUserFilter]
	syncGroupInfo.GroupFilter = syncKvMap[dto.AttrKeyAdGroupFilter]

	fieldMapValue := syncKvMap[dto.AttrKeyAdFieldMap]
	if fieldMapValue != "" {
		var fieldMap []dto.KV
		err = json.Unmarshal([]byte(fieldMapValue), &fieldMap)
		if err != nil {
			u.log.Errorf("ParseFieldMapFailed err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		syncGroupInfo.FieldMap = fieldMap
	}
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo, SyncGroupInfo: syncGroupInfo}, nil
}
func (u UserGroupUsecase) handleSourceInfogo(ctx context.Context, corpId, groupId, ciphertext string, key []byte, syncGroupInfo dto.SyncGroupInfo, basicInfo dto.GroupBasicInfo) (dto.GetRootGroupResult, error) {
	syncConfigInfo, err := u.repo.GetGroupSyncConfig(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetSyncConfigInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncKvMap := make(map[string]string)
	for _, v := range syncConfigInfo {
		syncKvMap[v.Key] = v.Value
	}
	plaintext := []byte(syncKvMap[dto.AttrKeyInfogoPass])
	ciphertext, err = common.Encrypt(key, plaintext)
	if err != nil {
		u.log.Errorf("Encrypt err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncGroupInfo.Endpoint = syncKvMap[dto.AttrKeyInfogoEndpoint]
	syncGroupInfo.Pass = ciphertext
	syncGroupInfo.Login = syncKvMap[dto.AttrKeyInfogoUsername]
	fieldMapValue := syncKvMap[dto.AttrKeyInfogoFieldMap]
	if fieldMapValue != "" {
		var fieldMap []dto.KV
		err = json.Unmarshal([]byte(fieldMapValue), &fieldMap)
		if err != nil {
			u.log.Errorf("ParseFieldMapFailed err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		syncGroupInfo.FieldMap = fieldMap
	}
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo, SyncGroupInfo: syncGroupInfo}, nil
}
func (u UserGroupUsecase) handleSourceMsad(ctx context.Context, corpId, groupId, ciphertext string, key []byte, syncGroupInfo dto.SyncGroupInfo, basicInfo dto.GroupBasicInfo) (dto.GetRootGroupResult, error) {
	syncConfigInfo, err := u.repo.GetGroupSyncConfig(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetSyncConfigInfo Failed. err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncKvMap := make(map[string]string)
	for _, v := range syncConfigInfo {
		syncKvMap[v.Key] = v.Value
	}
	plaintext := []byte(syncKvMap[dto.AttrKeyAdAdministratorPassword])
	ciphertext, err = common.Encrypt(key, plaintext)
	if err != nil {
		u.log.Errorf("Encrypt err=%v", err)
		return dto.GetRootGroupResult{}, err
	}
	syncGroupInfo.ServerAddress = syncKvMap[dto.AttrKeyAdServerAddress]
	syncGroupInfo.AdministratorPassword = ciphertext
	syncGroupInfo.AdministratorAccount = syncKvMap[dto.AttrKeyAdAdministratorAccount]
	syncGroupInfo.SearchEntry = syncKvMap[dto.AttrKeyAdSearchEntry]
	syncGroupInfo.UserFilter = syncKvMap[dto.AttrKeyAdUserFilter]
	syncGroupInfo.GroupFilter = syncKvMap[dto.AttrKeyAdGroupFilter]

	fieldMapValue := syncKvMap[dto.AttrKeyAdFieldMap]
	if fieldMapValue != "" {
		var fieldMap []dto.KV
		err = json.Unmarshal([]byte(fieldMapValue), &fieldMap)
		if err != nil {
			u.log.Errorf("ParseFieldMapFailed err=%v", err)
			return dto.GetRootGroupResult{}, err
		}
		syncGroupInfo.FieldMap = fieldMap
	}
	return dto.GetRootGroupResult{GroupBasicInfo: basicInfo, SyncGroupInfo: syncGroupInfo}, nil
}

func (u UserGroupUsecase) CreateRootGroup(ctx context.Context, param dto.CreateRootGroupParam) error {
	// 重名校验
	_, err := u.repo.GetUserGroupByNameInParent(ctx, param.CorpId, param.Name, dto.FakeRootUserGroupId)
	if err != nil && !pb.IsRecordNotFound(err) {
		u.log.Errorf("GetUserGroupByNameInParent failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict.", param.Name)
	}

	// 获取来源
	us, err := u.userSource.GetUserSource(ctx, param.CorpId, param.SourceId)
	if err != nil {
		u.log.Errorf("GetUserSource failed. err=%v", err)
		return err
	}
	// 根据不同来源
	switch dto.UserSourceType(us.SourceType) {
	case dto.UserSourceLocal:
		rootGroupId := uuid.New().String()
		var daoParam = dto.CreateGroupDaoParam{
			CorpId:        param.CorpId,
			Id:            rootGroupId,
			Name:          param.Name,
			Description:   param.Description,
			ParentGroupId: dto.FakeRootUserGroupId,
			SourceId:      param.SourceId,
			RootGroupID:   rootGroupId,
			Path:          "/",
		}
		if err := u.repo.CreateUserGroup(ctx, daoParam); err != nil {
			u.log.Errorf("CreateUserGroup failed. err=%v", err)
			return err
		}
		// 绑定默认认证idp
		if err := u.localGroupBindDefaultIDP(ctx, param.CorpId, rootGroupId, param.Name); err != nil {
			u.log.Errorf("localGroupBindDefaultIDP failed. err=%v", err)
			return err
		}
	case dto.UserSourceQiYeWx:
		if err := u.createWxGroup(ctx, param); err != nil {
			u.log.Errorf("createWxGroup failed. err=%v", err)
			return err
		}
	case dto.UserSourceFeiShu:
		if err := u.createFeiShuGroup(ctx, param); err != nil {
			u.log.Errorf("createFeiShuGroup failed. err=%v", err)
			return err
		}
	case dto.UserSourceDingtalk:
		if err := u.createDdingtalkGroup(ctx, param); err != nil {
			u.log.Errorf("createDdingtalkGroup failed. err=%v", err)
			return err
		}
	case dto.UserSourceLdap:
		if err := u.createLdapGroup(ctx, param); err != nil {
			u.log.Errorf("createLdapGroup failed. err=%v", err)
			return err
		}
	case dto.UserSourceMsad:
		if err := u.createLdapGroup(ctx, param); err != nil {
			u.log.Errorf("createAdGroup failed. err=%v", err)
			return err
		}
	case dto.UserSourceInfogo:
		if err := u.createInfogoGroup(ctx, param); err != nil {
			u.log.Errorf("createAdGroup failed. err=%v", err)
			return err
		}
	case dto.UserSourceOAuth2, dto.UserSourceCas, dto.UserSourceWebOAuth: // 新增: OAuth2.0用户身份源
		if err := u.createOAuth2Group(ctx, param); err != nil {
			u.log.Errorf("createOAuth2Group failed. err=%v", err)
			return err
		}
	default:
		switch dto.UserSourceType(us.TemplateType) {
		case dto.UserSourceOAuth2:
			if err := u.createOAuth2Group(ctx, param); err != nil {
				u.log.Errorf("createOAuth2Group failed. err=%v", err)
				return err
			}
		}
	}

	return nil
}

func (u UserGroupUsecase) localGroupBindDefaultIDP(ctx context.Context, corpId, rootGroupId, rootGroupName string) error {
	defaultIdp, err := u.idpRepo.GetDefaultLocalIDP(ctx, corpId)
	if err != nil {
		u.log.Errorf("GetDefaultLocalIDP failed. err=%v", err)
		return err
	}
	if !defaultIdp.Enable {
		return pb.ErrorDisableError("default idp not enable")
	}
	var daoParam = dto.CreateAuthPolicyDaoParam{
		CorpId:        corpId,
		Id:            uuid.New().String(),
		Name:          common.GetDefaultUniqName(rootGroupName),
		GroupIds:      []string{rootGroupId},
		UserIds:       []string{},
		IdpList:       []string{defaultIdp.ID},
		RootGroupId:   rootGroupId,
		EnableAllUser: false,
		Enable:        true,
		IsDefault:     true,
	}
	if err := u.authPolicy.CreatePolicy(ctx, daoParam); err != nil {
		u.log.Errorf("CreatePolicy failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserGroupUsecase) DeleteRootGroup(ctx context.Context, corpId, groupId, name string) error {
	// 查看该group是否存在
	group, err := u.repo.GetUserGroup(ctx, corpId, groupId)
	if err != nil || pb.IsRecordNotFound(err) || group.IsDefault {
		u.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}
	source, err := u.userSource.GetUserSource(ctx, corpId, group.SourceID)
	if err != nil || pb.IsRecordNotFound(err) {
		u.log.Errorf("GetUserGroupSource failed. err=%v", err)
		return err
	}
	// 获取该group下的所有组
	groupIds, err := u.GetAllGroupByGroupId(ctx, corpId, groupId)
	if err != nil {
		return err
	}
	// 获取所有组下的用户
	userList, err := u.userRepo.ListUserOfGroups(ctx, corpId, groupIds, -1, 0, "")
	if err != nil {
		u.log.Errorf("GetUserList failed. err=%v", err)
		return err
	}
	var userIds []string
	for _, v := range userList {
		userIds = append(userIds, v.ID)
	}
	// 删除所有组，以及组下面的用户，用户的角色
	return u.repo.DeleteGroup(ctx, corpId, groupIds, userIds, source.SourceType, name)
}

func (u UserGroupUsecase) GetAllGroupByGroupId(ctx context.Context, corpId, groupId string) ([]string, error) {
	var childrenGroupIds []string
	childrenGroupIds = append(childrenGroupIds, groupId)
	var res []string
	for len(childrenGroupIds) > 0 {
		res = append(res, childrenGroupIds...)
		groupList, err := u.repo.ListUserGroupByParGroupId(ctx, corpId, childrenGroupIds)
		if err != nil && !pb.IsRecordNotFound(err) {
			u.log.Errorf("GetGroupListByParentGroup Failed. err=%v", err)
			return res, err
		}
		childrenGroupIds = childrenGroupIds[:0]
		for _, v := range groupList {
			childrenGroupIds = append(childrenGroupIds, v.ID)
		}
	}
	return res, nil
}

func (u UserGroupUsecase) createInfogoGroup(ctx context.Context, param dto.CreateRootGroupParam) error {
	var daoParam dto.CreateRootGroupDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}

	var configs []dto.KV
	configs = append(configs, param.InfogoConfig.ToKVs()...)

	fieldMap, err := json.Marshal(param.FieldMap)
	if err != nil {
		u.log.Errorf("Marshal failed. err=%v", err)
		return err
	}
	configs = append(configs, dto.KV{
		Key:   dto.AttrKeyInfogoFieldMap,
		Value: string(fieldMap),
	})
	daoParam.Configs = configs
	daoParam.ID = uuid.New().String()
	if err := u.repo.CreateUserGroupWithSync(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUserGroupWithSync failed. err=%v", err)
		return err
	}
	return nil
}

// 新增方法: 处理OAuth2身份源
func (u UserGroupUsecase) createOAuth2Group(ctx context.Context, param dto.CreateRootGroupParam) error {
	var daoParam dto.CreateRootGroupDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}

	var configs []dto.KV
	configs = append(configs, param.OAuth2Config.ToKVs()...)

	// 添加字段映射配置
	fieldMap, err := json.Marshal(param.FieldMap)
	if err != nil {
		u.log.Errorf("Marshal failed. err=%v", err)
		return err
	}
	configs = append(configs, dto.KV{
		Key:   dto.AttrKeyOAuth2FieldMap,
		Value: string(fieldMap),
	})

	daoParam.Configs = configs
	daoParam.ID = uuid.New().String()

	// 调用存储库创建用户组
	if err := u.repo.CreateUserGroupWithSync(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUserGroupWithSync failed. err=%v", err)
		return err
	}

	return nil
}

func (u UserGroupUsecase) createWxGroup(ctx context.Context, param dto.CreateRootGroupParam) error {
	var daoParam dto.CreateRootGroupDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}

	var configs []dto.KV
	configs = append(configs, param.WxConfig.ToKVs()...)

	fieldMap, err := json.Marshal(param.FieldMap)
	if err != nil {
		u.log.Errorf("Marshal failed. err=%v", err)
		return err
	}
	configs = append(configs, dto.KV{
		Key:   dto.AttrKeyWxFieldMap,
		Value: string(fieldMap),
	})
	daoParam.Configs = configs
	daoParam.ID = uuid.New().String()
	if err := u.repo.CreateUserGroupWithSync(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUserGroupWithSync failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserGroupUsecase) createLdapGroup(ctx context.Context, param dto.CreateRootGroupParam) error {
	var daoParam dto.CreateRootGroupDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}
	var configs []dto.KV
	configs = append(configs, param.LdapConfig.ToKVs()...)
	fieldMap, err := json.Marshal(param.FieldMap)
	if err != nil {
		u.log.Errorf("Marshal failed. err=%v", err)
		return err
	}
	configs = append(configs, dto.KV{
		Key:   dto.AttrKeyAdFieldMap,
		Value: string(fieldMap),
	})
	daoParam.Configs = configs
	daoParam.ID = uuid.New().String()
	if err := u.repo.CreateUserGroupWithSync(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUserGroupWithSync failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserGroupUsecase) createDdingtalkGroup(ctx context.Context, param dto.CreateRootGroupParam) error {
	var daoParam dto.CreateRootGroupDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}
	var configs []dto.KV
	configs = append(configs, param.DingtalkConfig.ToKVs()...)
	fieldMap, err := json.Marshal(param.FieldMap)
	if err != nil {
		u.log.Errorf("Marshal failed. err=%v", err)
		return err
	}
	configs = append(configs, dto.KV{
		Key:   dto.AttrKeyDingtalkFieldMap,
		Value: string(fieldMap),
	})
	daoParam.Configs = configs
	daoParam.ID = uuid.New().String()
	if err := u.repo.CreateUserGroupWithSync(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUserGroupWithSync failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserGroupUsecase) createFeiShuGroup(ctx context.Context, param dto.CreateRootGroupParam) error {
	var daoParam dto.CreateRootGroupDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		u.log.Errorf("Copy failed. err=%v", err)
		return err
	}

	var configs []dto.KV
	configs = append(configs, param.FeishuConfig.ToKVs()...)

	fieldMap, err := json.Marshal(param.FieldMap)
	if err != nil {
		u.log.Errorf("Marshal failed. err=%v", err)
		return err
	}
	configs = append(configs, dto.KV{
		Key:   dto.AttrKeyFeiShuFieldMap,
		Value: string(fieldMap),
	})
	daoParam.Configs = configs
	daoParam.ID = uuid.New().String()
	if err := u.repo.CreateUserGroupWithSync(ctx, daoParam); err != nil {
		u.log.Errorf("CreateUserGroupWithSync failed. err=%v", err)
		return err
	}
	return nil
}
func padOrTrim(input string, length int) []byte {
	bytes := []byte(input)
	if len(bytes) > length {
		return bytes[:length]
	}

	paddedBytes := make([]byte, length)
	copy(paddedBytes, bytes)
	return paddedBytes
}
func (u UserGroupUsecase) UpdateRootGroup(ctx context.Context, corpId string, param dto.UpdateGroupParam) error {
	group, err := u.repo.GetUserGroup(ctx, corpId, param.GroupId)
	if err != nil {
		u.log.Errorf("GetUserGroup Failed. err=%v", err)
		return err
	}
	var parentGroup *model.TbUserGroup
	// 判断父级是否修改
	if group.ParentGroupID != "0" && group.ParentGroupID != param.ParentGroupId {
		// 获取本次父级
		parentGroup, err = u.repo.GetUserGroup(ctx, corpId, param.ParentGroupId)
		if err != nil {
			u.log.Errorf("GetUserGroup Failed. err=%v", err)
			return err
		}
	}
	source, err := u.userSource.GetUserSource(ctx, corpId, group.SourceID)
	if err != nil {
		u.log.Errorf("GetUserSourceByGroup Failed. err=%v", err)
		return err
	}
	if param.GroupId == param.ParentGroupId {
		return pb.ErrorParamError("can not use self as parent id")
	}
	// 名称修改则进行重名校验
	if group.Name != param.Name {
		// 重名校验
		_, err = u.repo.GetUserGroupByNameInParent(ctx, param.CorpId, param.Name, param.ParentGroupId)
		if err != nil && !pb.IsRecordNotFound(err) {
			u.log.Errorf("GetUserGroupByNameInParent failed. err=%v", err)
			return err
		}
		if err == nil {
			return pb.ErrorNameConflict("name=%v conflict.", param.Name)
		}
	}

	key := padOrTrim(param.GroupId, 32)
	switch source.SourceType {
	case string(dto.UserSourceQiYeWx):
		if param.WxConfig.Secret != "" {
			// 解密用于保存，能正常解密则用解密后的，不能则用解密前的
			decSecret, err := common.Decrypt(key, param.WxConfig.Secret)
			if err == nil {
				param.WxConfig.Secret = string(decSecret)
			}
		}
		return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.SourceType, group.Name)
	case string(dto.UserSourceFeiShu):
		if param.FeishuConfig.AppSecret != "" {
			// 解密用于保存，能正常解密则用解密后的，不能则用解密前的
			decSecret, err := common.Decrypt(key, param.FeishuConfig.AppSecret)
			if err == nil {
				param.FeishuConfig.AppSecret = string(decSecret)
			}
		}
		return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.SourceType, group.Name)
	case string(dto.UserSourceDingtalk):
		if param.DingtalkConfig.AppSecret != "" {
			// 解密用于保存，能正常解密则用解密后的，不能则用解密前的
			decSecret, err := common.Decrypt(key, param.DingtalkConfig.AppSecret)
			if err == nil {
				param.DingtalkConfig.AppSecret = string(decSecret)
			}
		}
		return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.SourceType, group.Name)
	case string(dto.UserSourceLdap):
		if param.LdapConfig.AdministratorPassword != "" {
			// 解密用于保存，能正常解密则用解密后的，不能则用解密前的
			decSecret, err := common.Decrypt(key, param.LdapConfig.AdministratorPassword)
			if err == nil {
				param.LdapConfig.AdministratorPassword = string(decSecret)
			}
		}
		return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.SourceType, group.Name)
	case string(dto.UserSourceMsad):
		if param.LdapConfig.AdministratorPassword != "" {
			// 解密用于保存，能正常解密则用解密后的，不能则用解密前的
			decSecret, err := common.Decrypt(key, param.LdapConfig.AdministratorPassword)
			if err == nil {
				param.LdapConfig.AdministratorPassword = string(decSecret)
			}
		}
		return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.SourceType, group.Name)
	case string(dto.UserSourceLocal):
		return u.repo.UpdateRootGroup(ctx, corpId, param, parentGroup, group)
	case string(dto.UserSourceInfogo):
		if param.InfogoConfig.Pass != "" {
			// 解密用于保存，能正常解密则用解密后的，不能则用解密前的
			decSecret, err := common.Decrypt(key, param.InfogoConfig.Pass)
			if err == nil {
				param.InfogoConfig.Pass = string(decSecret)
			}
		}
		return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.SourceType, group.Name)
	case string(dto.UserSourceOAuth2), string(dto.UserSourceCas), string(dto.UserSourceWebOAuth):
		return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.SourceType, group.Name)
	default:
		switch source.TemplateType {
		case string(dto.UserSourceOAuth2):
			return u.repo.UpdateThirdRootGroup(ctx, corpId, param, source.TemplateType, group.Name)
		}
	}
	return nil
}

func (u UserGroupUsecase) SwitchAutoSync(ctx context.Context, corpId, groupId string, autoSync bool) error {
	ug, err := u.repo.GetUserGroup(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}
	if ug.ParentGroupID != dto.FakeRootUserGroupId {
		return pb.ErrorUserGroupConflict("this group is not root group. can not set auth sync.")
	}

	if err := u.repo.SwitchAutoSync(ctx, groupId, autoSync); err != nil {
		u.log.Errorf("SwitchAutoSync failed. err=%v", err)
		return err
	}
	return nil
}

func (u UserGroupUsecase) GetRootGroupIdpList(ctx context.Context, corpId, rootGroupId string) (dto.GetRootGroupListResp, error) {
	mainIdpInfos, err := u.repo.ListRootGroupBindMainIdp(ctx, corpId, rootGroupId)
	if err != nil {
		u.log.Errorf("ListRootGroupBindMainIdp failed. err=%v", err)
	}
	// 本地用户目录使用同一个认证源
	rootGroupSource, err := u.repo.GetRootGroupSourceType(ctx, corpId, rootGroupId)
	if err != nil {
		u.log.Errorf("GetRootGroupSource failed. err=%v", err)
	}
	rootGroup, err := u.repo.GetUserGroup(ctx, corpId, rootGroupId)
	if err != nil {
		u.log.Errorf("GetRootGroup failed. err=%v", err)
	}
	if rootGroupSource == string(dto.UserSourceLocal) && !rootGroup.IsDefault {
		localIdp, err := u.idpRepo.GetDefaultLocalIDP(ctx, corpId)
		if err != nil {
			u.log.Errorf("GetDefaultLocalIdp failed. err=%v", err)
		}
		mainIdpInfos = append(mainIdpInfos, dto.IDPBasic{
			ID:          localIdp.ID,
			Name:        localIdp.Name,
			Type:        localIdp.Type,
			Avatar:      localIdp.Avatar,
			Description: localIdp.Description,
			Enable:      localIdp.Enable,
			IsDefault:   localIdp.IsDefault,
		})
	}
	assistIdpList, err := u.repo.ListRootGroupBindAssistIdp(ctx, corpId)
	return dto.GetRootGroupListResp{
		MainIdpList:   mainIdpInfos,
		AssistIdpList: assistIdpList,
	}, nil
}

func (u UserGroupUsecase) ListSyncLog(ctx context.Context, corpId, groupId string, limit, offset int) (dto.SyncLogResp, error) {
	logs, err := u.repo.ListGroupSyncLog(ctx, corpId, groupId, limit, offset)
	if err != nil {
		u.log.Errorf("ListGroupSyncLog failed. err=%v", err)
		return dto.SyncLogResp{}, err
	}
	count, err := u.repo.CountGroupSyncLog(ctx, corpId, groupId)
	if err != nil {
		u.log.Errorf("CountGroupSyncLog failed. err=%v", err)
		return dto.SyncLogResp{}, err
	}
	return dto.SyncLogResp{SyncLogs: logs, Count: uint32(count)}, err
}
