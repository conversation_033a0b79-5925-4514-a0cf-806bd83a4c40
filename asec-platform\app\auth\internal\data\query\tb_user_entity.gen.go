// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserEntity(db *gorm.DB, opts ...gen.DOOption) tbUserEntity {
	_tbUserEntity := tbUserEntity{}

	_tbUserEntity.tbUserEntityDo.UseDB(db, opts...)
	_tbUserEntity.tbUserEntityDo.UseModel(&model.TbUserEntity{})

	tableName := _tbUserEntity.tbUserEntityDo.TableName()
	_tbUserEntity.ALL = field.NewAsterisk(tableName)
	_tbUserEntity.ID = field.NewString(tableName, "id")
	_tbUserEntity.Name = field.NewString(tableName, "name")
	_tbUserEntity.DisplayName = field.NewString(tableName, "display_name")
	_tbUserEntity.TrueName = field.NewString(tableName, "true_name")
	_tbUserEntity.NickName = field.NewString(tableName, "nick_name")
	_tbUserEntity.Avatar = field.NewString(tableName, "avatar")
	_tbUserEntity.GroupID = field.NewString(tableName, "group_id")
	_tbUserEntity.CorpID = field.NewString(tableName, "corp_id")
	_tbUserEntity.SourceID = field.NewString(tableName, "source_id")
	_tbUserEntity.Phone = field.NewString(tableName, "phone")
	_tbUserEntity.Email = field.NewString(tableName, "email")
	_tbUserEntity.Enable = field.NewBool(tableName, "enable")
	_tbUserEntity.ExpireType = field.NewString(tableName, "expire_type")
	_tbUserEntity.ExpireEnd = field.NewTime(tableName, "expire_end")
	_tbUserEntity.RootGroupID = field.NewString(tableName, "root_group_id")
	_tbUserEntity.Identify = field.NewString(tableName, "identify")
	_tbUserEntity.CreatedAt = field.NewTime(tableName, "created_at")
	_tbUserEntity.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbUserEntity.AuthType = field.NewString(tableName, "auth_type")
	_tbUserEntity.ActiveTime = field.NewTime(tableName, "active_time")
	_tbUserEntity.LockStatus = field.NewBool(tableName, "lock_status")
	_tbUserEntity.SecurityCode = field.NewString(tableName, "security_code")
	_tbUserEntity.ActivationSecret = field.NewString(tableName, "activation_secret")
	_tbUserEntity.CurrentSecret = field.NewString(tableName, "current_secret")

	_tbUserEntity.fillFieldMap()

	return _tbUserEntity
}

type tbUserEntity struct {
	tbUserEntityDo tbUserEntityDo

	ALL              field.Asterisk
	ID               field.String // 用户id
	Name             field.String // 用户登陆名
	DisplayName      field.String // 用户显示名
	TrueName         field.String // 用户真实姓名
	NickName         field.String
	Avatar           field.String
	GroupID          field.String // 分组id
	CorpID           field.String // 租户id
	SourceID         field.String // 来源id
	Phone            field.String // 手机号
	Email            field.String // 邮箱
	Enable           field.Bool
	ExpireType       field.String
	ExpireEnd        field.Time
	RootGroupID      field.String
	Identify         field.String
	CreatedAt        field.Time
	UpdatedAt        field.Time
	AuthType         field.String // 用户认证类型:password,oauth2等
	ActiveTime       field.Time   // 上次活跃时间
	LockStatus       field.Bool
	SecurityCode field.String
	ActivationSecret field.String // TOTP验证秘钥
	CurrentSecret    field.String // TOTP当前验证秘钥

	fieldMap map[string]field.Expr
}

func (t tbUserEntity) Table(newTableName string) *tbUserEntity {
	t.tbUserEntityDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserEntity) As(alias string) *tbUserEntity {
	t.tbUserEntityDo.DO = *(t.tbUserEntityDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserEntity) updateTableName(table string) *tbUserEntity {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Name = field.NewString(table, "name")
	t.DisplayName = field.NewString(table, "display_name")
	t.TrueName = field.NewString(table, "true_name")
	t.NickName = field.NewString(table, "nick_name")
	t.Avatar = field.NewString(table, "avatar")
	t.GroupID = field.NewString(table, "group_id")
	t.CorpID = field.NewString(table, "corp_id")
	t.SourceID = field.NewString(table, "source_id")
	t.Phone = field.NewString(table, "phone")
	t.Email = field.NewString(table, "email")
	t.Enable = field.NewBool(table, "enable")
	t.ExpireType = field.NewString(table, "expire_type")
	t.ExpireEnd = field.NewTime(table, "expire_end")
	t.RootGroupID = field.NewString(table, "root_group_id")
	t.Identify = field.NewString(table, "identify")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.AuthType = field.NewString(table, "auth_type")
	t.ActiveTime = field.NewTime(table, "active_time")
	t.LockStatus = field.NewBool(table, "lock_status")
	t.SecurityCode = field.NewString(table, "security_code")
	t.ActivationSecret = field.NewString(table, "activation_secret")
	t.CurrentSecret = field.NewString(table, "current_secret")

	t.fillFieldMap()

	return t
}

func (t *tbUserEntity) WithContext(ctx context.Context) *tbUserEntityDo {
	return t.tbUserEntityDo.WithContext(ctx)
}

func (t tbUserEntity) TableName() string { return t.tbUserEntityDo.TableName() }

func (t tbUserEntity) Alias() string { return t.tbUserEntityDo.Alias() }

func (t tbUserEntity) Columns(cols ...field.Expr) gen.Columns {
	return t.tbUserEntityDo.Columns(cols...)
}

func (t *tbUserEntity) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserEntity) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 23)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["display_name"] = t.DisplayName
	t.fieldMap["true_name"] = t.TrueName
	t.fieldMap["nick_name"] = t.NickName
	t.fieldMap["avatar"] = t.Avatar
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["corp_id"] = t.CorpID
	t.fieldMap["source_id"] = t.SourceID
	t.fieldMap["phone"] = t.Phone
	t.fieldMap["email"] = t.Email
	t.fieldMap["enable"] = t.Enable
	t.fieldMap["expire_type"] = t.ExpireType
	t.fieldMap["expire_end"] = t.ExpireEnd
	t.fieldMap["root_group_id"] = t.RootGroupID
	t.fieldMap["identify"] = t.Identify
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["auth_type"] = t.AuthType
	t.fieldMap["active_time"] = t.ActiveTime
	t.fieldMap["lock_status"] = t.LockStatus
	t.fieldMap["security_code"] = t.SecurityCode
	t.fieldMap["activation_secret"] = t.ActivationSecret
	t.fieldMap["current_secret"] = t.CurrentSecret
}

func (t tbUserEntity) clone(db *gorm.DB) tbUserEntity {
	t.tbUserEntityDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserEntity) replaceDB(db *gorm.DB) tbUserEntity {
	t.tbUserEntityDo.ReplaceDB(db)
	return t
}

type tbUserEntityDo struct{ gen.DO }

// INSERT INTO @@table
// {{if phone != "" && email != ""}}
//
//	(id, name, group_id, corp_id, source_id, phone, email) VALUES (@id, @name, @groupId, @corpId, @sourceId, @phone, @email)
//
// {{else if phone != ""}}
// (id, name, group_id, corp_id, source_id, phone) VALUES (@id, @name, @groupId, @corpId, @sourceId, @phone)
// {{else if email != ""}}
// (id, name, group_id, corp_id, source_id, email) VALUES (@id, @name, @groupId, @corpId, @sourceId, @email)
// {{else}}
// (id, name, group_id, corp_id, source_id) VALUES (@id, @name, @groupId, @corpId, @sourceId)
// {{end}}
func (t tbUserEntityDo) CreateUser(id string, name string, groupId string, corpId string, sourceId string, phone string, email string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	generateSQL.WriteString("INSERT INTO tb_user_entity ")
	if phone != "" && email != "" {
		params = append(params, id)
		params = append(params, name)
		params = append(params, groupId)
		params = append(params, corpId)
		params = append(params, sourceId)
		params = append(params, phone)
		params = append(params, email)
		generateSQL.WriteString("(id, name, group_id, corp_id, source_id, phone, email) VALUES (?, ?, ?, ?, ?, ?, ?) ")
	} else if phone != "" {
		params = append(params, id)
		params = append(params, name)
		params = append(params, groupId)
		params = append(params, corpId)
		params = append(params, sourceId)
		params = append(params, phone)
		generateSQL.WriteString("(id, name, group_id, corp_id, source_id, phone) VALUES (?, ?, ?, ?, ?, ?) ")
	} else if email != "" {
		params = append(params, id)
		params = append(params, name)
		params = append(params, groupId)
		params = append(params, corpId)
		params = append(params, sourceId)
		params = append(params, email)
		generateSQL.WriteString("(id, name, group_id, corp_id, source_id, email) VALUES (?, ?, ?, ?, ?, ?) ")
	} else {
		params = append(params, id)
		params = append(params, name)
		params = append(params, groupId)
		params = append(params, corpId)
		params = append(params, sourceId)
		generateSQL.WriteString("(id, name, group_id, corp_id, source_id) VALUES (?, ?, ?, ?, ?) ")
	}

	var executeSQL *gorm.DB
	executeSQL = t.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (t tbUserEntityDo) Debug() *tbUserEntityDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserEntityDo) WithContext(ctx context.Context) *tbUserEntityDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserEntityDo) ReadDB() *tbUserEntityDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserEntityDo) WriteDB() *tbUserEntityDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserEntityDo) Session(config *gorm.Session) *tbUserEntityDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserEntityDo) Clauses(conds ...clause.Expression) *tbUserEntityDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserEntityDo) Returning(value interface{}, columns ...string) *tbUserEntityDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserEntityDo) Not(conds ...gen.Condition) *tbUserEntityDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserEntityDo) Or(conds ...gen.Condition) *tbUserEntityDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserEntityDo) Select(conds ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserEntityDo) Where(conds ...gen.Condition) *tbUserEntityDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserEntityDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserEntityDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserEntityDo) Order(conds ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserEntityDo) Distinct(cols ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserEntityDo) Omit(cols ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserEntityDo) Join(table schema.Tabler, on ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserEntityDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserEntityDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserEntityDo) Group(cols ...field.Expr) *tbUserEntityDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserEntityDo) Having(conds ...gen.Condition) *tbUserEntityDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserEntityDo) Limit(limit int) *tbUserEntityDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserEntityDo) Offset(offset int) *tbUserEntityDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserEntityDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserEntityDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserEntityDo) Unscoped() *tbUserEntityDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserEntityDo) Create(values ...*model.TbUserEntity) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserEntityDo) CreateInBatches(values []*model.TbUserEntity, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserEntityDo) Save(values ...*model.TbUserEntity) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserEntityDo) First() (*model.TbUserEntity, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserEntity), nil
	}
}

func (t tbUserEntityDo) Take() (*model.TbUserEntity, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserEntity), nil
	}
}

func (t tbUserEntityDo) Last() (*model.TbUserEntity, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserEntity), nil
	}
}

func (t tbUserEntityDo) Find() ([]*model.TbUserEntity, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserEntity), err
}

func (t tbUserEntityDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserEntity, err error) {
	buf := make([]*model.TbUserEntity, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserEntityDo) FindInBatches(result *[]*model.TbUserEntity, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserEntityDo) Attrs(attrs ...field.AssignExpr) *tbUserEntityDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserEntityDo) Assign(attrs ...field.AssignExpr) *tbUserEntityDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserEntityDo) Joins(fields ...field.RelationField) *tbUserEntityDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserEntityDo) Preload(fields ...field.RelationField) *tbUserEntityDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserEntityDo) FirstOrInit() (*model.TbUserEntity, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserEntity), nil
	}
}

func (t tbUserEntityDo) FirstOrCreate() (*model.TbUserEntity, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserEntity), nil
	}
}

func (t tbUserEntityDo) FindByPage(offset int, limit int) (result []*model.TbUserEntity, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserEntityDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserEntityDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserEntityDo) Delete(models ...*model.TbUserEntity) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserEntityDo) withDO(do gen.Dao) *tbUserEntityDo {
	t.DO = *do.(*gen.DO)
	return t
}
