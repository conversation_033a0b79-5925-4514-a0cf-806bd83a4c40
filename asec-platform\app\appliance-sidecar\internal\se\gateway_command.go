package se

import (
	"context"
	"sync"
	"time"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"google.golang.org/grpc"
)

var (
	// 用于标记设备注册通知是否已完成
	applianceRegistrationNotified bool
	// 保护标记的互斥锁
	registrationMutex sync.Mutex
)

// StartGatewayCommandService 启动网关命令服务
func StartGatewayCommandService(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()

	global.Logger.Sugar().Debug("Starting Gateway Command Service...")

	// 等待一段时间确保其他服务已启动
	time.Sleep(5 * time.Second)

	// 创建一个定时器，定期获取平台连接并启动命令轮询
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	// 首次立即尝试启动
	tryStartCommandPolling(ctx)

	for {
		select {
		case <-ctx.Done():
			global.Logger.Sugar().Info("Gateway command service stopped")
			return
		case <-ticker.C:
			tryStartCommandPolling(ctx)
		}
	}
}

// tryStartCommandPolling 尝试启动命令轮询
func tryStartCommandPolling(ctx context.Context) {
	// 仅对网关类型设备进行命令轮询
	if global.ApplianceType != v1.ApplianceType_GATEWAY {
		return
	}

	// 检查设备是否已注册（有有效的设备ID）
	if global.ApplianceID == 0 {
		return
	}

	// 获取平台连接
	conn, err := connection.GetPlatformConnection(ctx)
	if err != nil {
		return
	}

	// 通知网关命令服务设备已注册完成（只通知一次）
	if hooks := connection.GetGatewayCommandHooks(); hooks != nil {
		registrationMutex.Lock()
		shouldNotifyRegistration := !applianceRegistrationNotified
		if shouldNotifyRegistration {
			applianceRegistrationNotified = true
		}
		registrationMutex.Unlock()

		// 只在第一次时通知设备注册完成
		if shouldNotifyRegistration {
			if err := hooks.OnApplianceRegistered(global.ApplianceID); err != nil {
				global.Logger.Sugar().Warnf("Failed to notify appliance registered: %v", err)
				// 如果通知失败，重置标记以便下次重试
				registrationMutex.Lock()
				applianceRegistrationNotified = false
				registrationMutex.Unlock()
				return
			}
			global.Logger.Sugar().Debugf("Appliance registration notification sent successfully for ID: %d", global.ApplianceID)
		}

		// 设置平台连接以启动命令轮询（每次都需要设置以保持连接）
		if bridge, ok := hooks.(interface{ SetPlatformConnection(*grpc.ClientConn) }); ok {
			bridge.SetPlatformConnection(conn)
		}
	}
}
