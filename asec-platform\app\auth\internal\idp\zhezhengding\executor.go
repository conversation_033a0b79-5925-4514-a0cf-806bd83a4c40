package zhezhengding

import (
	"crypto/hmac" // 保留 crypto/rand
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	mathrand "math/rand" // 给 math/rand 添加别名
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// ExecutableClient 浙政钉使用的特殊客户端实现
type ExecutableClient struct {
	configs   map[string]interface{}
	headers   map[string]string
	timestamp int64
	logger    *log.Helper
}

// NewExecutableClient 创建一个新的ExecutableClient实例
func NewExecutableClient() *ExecutableClient {
	mathrand.Seed(time.Now().UnixNano())
	logger := log.With(log.GetLogger(), "module", "zhezhengding_executable_client")
	client := &ExecutableClient{
		configs:   make(map[string]interface{}),
		headers:   make(map[string]string),
		timestamp: time.Now().Unix(),
		logger:    log.NewHelper(logger),
	}

	// 初始化配置
	client.configs["epaas"] = map[string]interface{}{
		"api_version": "1.0",
		"api_timeout": 3, // 超时时间，单位秒
		"params":      make(map[string]interface{}),
	}

	return client
}

// SetDomain 设置域名
func (c *ExecutableClient) SetDomain(domain string) {
	c.getEpaasConfig()["api_server"] = domain
}

// SetAccessKey 设置访问密钥
func (c *ExecutableClient) SetAccessKey(accessKey string) {
	c.getEpaasConfig()["api_key"] = accessKey
}

// SetSecretKey 设置密钥
func (c *ExecutableClient) SetSecretKey(secretKey string) {
	c.getEpaasConfig()["api_secret"] = secretKey
}

// SetApiName 设置API名称
func (c *ExecutableClient) SetApiName(apiName string) {
	c.getEpaasConfig()["api_name"] = apiName
}

// AddParameter 添加参数
func (c *ExecutableClient) AddParameter(key string, value string) {
	params := c.getEpaasParams()

	if _, ok := params[key]; !ok {
		params[key] = []string{}
	}

	valueSlice := params[key].([]string)
	valueSlice = append(valueSlice, value)
	params[key] = valueSlice
}

// SetParameter 设置参数（覆盖现有值）
func (c *ExecutableClient) SetParameter(key string, value string) {
	params := c.getEpaasParams()
	params[key] = []string{value}
}

// SetParameter 设置参数（覆盖现有值）
func (c *ExecutableClient) SetParameters(key string, values []string) {
	params := c.getEpaasParams()
	params[key] = values
}

// ReSetParams 重置参数
func (c *ExecutableClient) ReSetParams() {
	c.getEpaasConfig()["params"] = make(map[string]interface{})
}

// EpaasCurlPost 执行POST请求
func (c *ExecutableClient) EpaasCurlPost(timeout int) (map[string]interface{}, error) {
	headers := c.generateHeaders("POST")
	if headers == nil {
		return nil, fmt.Errorf("生成请求头失败")
	}

	config := c.getEpaasConfig()
	params := c.getEpaasParams()
	api := config["api_name"].(string)
	url := fmt.Sprintf("%s%s", config["api_server"], api)

	// 构建请求参数
	formValues := []string{}
	for key, valuesInterface := range params {
		switch values := valuesInterface.(type) {
		case []string:
			for _, value := range values {
				formValues = append(formValues, fmt.Sprintf("%s=%s", key, value))
			}
		case string:
			formValues = append(formValues, fmt.Sprintf("%s=%s", key, values))
		}
	}

	formData := strings.Join(formValues, "&")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, strings.NewReader(formData))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析JSON响应
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	// 检查请求是否成功
	if success, ok := result["success"].(bool); !ok || !success {
		return result, fmt.Errorf("API请求失败: %s", string(body))
	}

	return result, nil
}

// 生成请求头
func (c *ExecutableClient) generateHeaders(method string) map[string]string {
	// 获取当前时间戳，解决时间偏差问题
	timestamp := time.Now().Unix()

	config := c.getEpaasConfig()
	params := c.getEpaasParams()
	api := config["api_name"].(string)

	// 获取网络接口信息
	nicInfo, err := c.getNicInfo()
	if err != nil {
		c.logger.Errorf("获取网络接口信息失败: %v", err)
		return nil
	}

	// 格式化时间
	formatTime := time.Unix(timestamp, 0).Format("2006-01-02T15:04:05.000-07:00")

	// 生成随机数
	nonce := fmt.Sprintf("%d000%d", timestamp, mathrand.Intn(9000)+1000)

	// 对参数进行排序
	var paramStrs []string
	for k, v := range params {
		switch values := v.(type) {
		case []string:
			for _, val := range values {
				paramStrs = append(paramStrs, fmt.Sprintf("%s=%s", k, val))
			}
		case string:
			paramStrs = append(paramStrs, fmt.Sprintf("%s=%s", k, values))
		}
	}
	sort.Strings(paramStrs)

	// 生成签名
	var sigData string
	if len(paramStrs) > 0 {
		sigData = fmt.Sprintf("%s\n%s\n%s\n%s\n%s", method, formatTime, nonce, api, strings.Join(paramStrs, "&"))
	} else {
		sigData = fmt.Sprintf("%s\n%s\n%s\n%s", method, formatTime, nonce, api)
	}

	signature := c.generateSignature(sigData)

	// 返回请求头
	headers := map[string]string{
		"X-Hmac-Auth-Timestamp": formatTime,
		"X-Hmac-Auth-Version":   config["api_version"].(string),
		"X-Hmac-Auth-Nonce":     nonce,
		"apiKey":                config["api_key"].(string),
		"X-Hmac-Auth-Signature": signature,
		"X-Hmac-Auth-IP":        nicInfo["ip"],
		"X-Hmac-Auth-MAC":       nicInfo["mac"],
		"Content-Type":          "application/x-www-form-urlencoded",
	}

	return headers
}

// 生成签名
func (c *ExecutableClient) generateSignature(data string) string {
	config := c.getEpaasConfig()
	secret := config["api_secret"].(string)

	// 使用HMAC-SHA256生成签名
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}

// 获取网络接口信息
func (c *ExecutableClient) getNicInfo() (map[string]string, error) {
	// 这里返回一个硬编码的MAC和IP地址
	// 在实际生产环境中，应该获取真实的网络接口信息
	return map[string]string{
		"mac": "00:00:00:00:00:01", // 一个虚构的MAC地址
		"ip":  "127.0.0.1",         // 本地IP地址
	}, nil
}

// 辅助方法：获取EPAAS配置
func (c *ExecutableClient) getEpaasConfig() map[string]interface{} {
	return c.configs["epaas"].(map[string]interface{})
}

// 辅助方法：获取EPAAS参数
func (c *ExecutableClient) getEpaasParams() map[string]interface{} {
	config := c.getEpaasConfig()
	return config["params"].(map[string]interface{})
}
