// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.20.0
// source: appliance/v1/appliance_cfg.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CfgApplianceType int32

const (
	CfgApplianceType_UNKNOWN_APPLIANCE_TYPE CfgApplianceType = 0
	// 客户端
	CfgApplianceType_AGENT_CFG CfgApplianceType = 1
	// 安全边缘代理
	CfgApplianceType_SECURITY_EDGE_CFG CfgApplianceType = 2
	// 连接器
	CfgApplianceType_CONNECTOR_CFG CfgApplianceType = 3
	// 网关
	CfgApplianceType_GATEWAY_CFG CfgApplianceType = 4
)

// Enum value maps for CfgApplianceType.
var (
	CfgApplianceType_name = map[int32]string{
		0: "UNKNOWN_APPLIANCE_TYPE",
		1: "AGENT_CFG",
		2: "SECURITY_EDGE_CFG",
		3: "CONNECTOR_CFG",
		4: "GATEWAY_CFG",
	}
	CfgApplianceType_value = map[string]int32{
		"UNKNOWN_APPLIANCE_TYPE": 0,
		"AGENT_CFG":              1,
		"SECURITY_EDGE_CFG":      2,
		"CONNECTOR_CFG":          3,
		"GATEWAY_CFG":            4,
	}
)

func (x CfgApplianceType) Enum() *CfgApplianceType {
	p := new(CfgApplianceType)
	*p = x
	return p
}

func (x CfgApplianceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CfgApplianceType) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_appliance_cfg_proto_enumTypes[0].Descriptor()
}

func (CfgApplianceType) Type() protoreflect.EnumType {
	return &file_appliance_v1_appliance_cfg_proto_enumTypes[0]
}

func (x CfgApplianceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CfgApplianceType.Descriptor instead.
func (CfgApplianceType) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_cfg_proto_rawDescGZIP(), []int{0}
}

type CfgType int32

const (
	CfgType_UNKNOWN_CFG_TYPE CfgType = 0
	// 进程过滤配置
	CfgType_PROCESS_FILTER_CFG CfgType = 1
)

// Enum value maps for CfgType.
var (
	CfgType_name = map[int32]string{
		0: "UNKNOWN_CFG_TYPE",
		1: "PROCESS_FILTER_CFG",
	}
	CfgType_value = map[string]int32{
		"UNKNOWN_CFG_TYPE":   0,
		"PROCESS_FILTER_CFG": 1,
	}
)

func (x CfgType) Enum() *CfgType {
	p := new(CfgType)
	*p = x
	return p
}

func (x CfgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CfgType) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_appliance_cfg_proto_enumTypes[1].Descriptor()
}

func (CfgType) Type() protoreflect.EnumType {
	return &file_appliance_v1_appliance_cfg_proto_enumTypes[1]
}

func (x CfgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CfgType.Descriptor instead.
func (CfgType) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_cfg_proto_rawDescGZIP(), []int{1}
}

// 后续根据需求加上 终端属性\用户属性\配置属性等,先简单处理
type ConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CfgApplianceType CfgApplianceType `protobuf:"varint,1,opt,name=cfg_appliance_type,json=cfgApplianceType,proto3,enum=api.appliance.CfgApplianceType" json:"cfg_appliance_type,omitempty"`
	CfgType          CfgType          `protobuf:"varint,2,opt,name=cfg_type,json=cfgType,proto3,enum=api.appliance.CfgType" json:"cfg_type,omitempty"`
}

func (x *ConfigReq) Reset() {
	*x = ConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_cfg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigReq) ProtoMessage() {}

func (x *ConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_cfg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigReq.ProtoReflect.Descriptor instead.
func (*ConfigReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_cfg_proto_rawDescGZIP(), []int{0}
}

func (x *ConfigReq) GetCfgApplianceType() CfgApplianceType {
	if x != nil {
		return x.CfgApplianceType
	}
	return CfgApplianceType_UNKNOWN_APPLIANCE_TYPE
}

func (x *ConfigReq) GetCfgType() CfgType {
	if x != nil {
		return x.CfgType
	}
	return CfgType_UNKNOWN_CFG_TYPE
}

type ConfigVersionResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CfgType    CfgType `protobuf:"varint,1,opt,name=cfg_type,json=cfgType,proto3,enum=api.appliance.CfgType" json:"cfg_type,omitempty"`
	CfgVersion string  `protobuf:"bytes,2,opt,name=cfg_version,json=cfgVersion,proto3" json:"cfg_version,omitempty"`
}

func (x *ConfigVersionResp) Reset() {
	*x = ConfigVersionResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_cfg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigVersionResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigVersionResp) ProtoMessage() {}

func (x *ConfigVersionResp) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_cfg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigVersionResp.ProtoReflect.Descriptor instead.
func (*ConfigVersionResp) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_cfg_proto_rawDescGZIP(), []int{1}
}

func (x *ConfigVersionResp) GetCfgType() CfgType {
	if x != nil {
		return x.CfgType
	}
	return CfgType_UNKNOWN_CFG_TYPE
}

func (x *ConfigVersionResp) GetCfgVersion() string {
	if x != nil {
		return x.CfgVersion
	}
	return ""
}

// 后续根据需求加上 配置属性\模块属性等
type GetConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CfgType    CfgType `protobuf:"varint,1,opt,name=cfg_type,json=cfgType,proto3,enum=api.appliance.CfgType" json:"cfg_type,omitempty"`
	CfgId      string  `protobuf:"bytes,3,opt,name=cfg_id,json=cfgId,proto3" json:"cfg_id,omitempty"`
	ConfigData []byte  `protobuf:"bytes,2,opt,name=config_data,json=configData,proto3" json:"config_data,omitempty"`
}

func (x *GetConfigResp) Reset() {
	*x = GetConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_cfg_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigResp) ProtoMessage() {}

func (x *GetConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_cfg_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigResp.ProtoReflect.Descriptor instead.
func (*GetConfigResp) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_cfg_proto_rawDescGZIP(), []int{2}
}

func (x *GetConfigResp) GetCfgType() CfgType {
	if x != nil {
		return x.CfgType
	}
	return CfgType_UNKNOWN_CFG_TYPE
}

func (x *GetConfigResp) GetCfgId() string {
	if x != nil {
		return x.CfgId
	}
	return ""
}

func (x *GetConfigResp) GetConfigData() []byte {
	if x != nil {
		return x.ConfigData
	}
	return nil
}

var File_appliance_v1_appliance_cfg_proto protoreflect.FileDescriptor

var file_appliance_v1_appliance_cfg_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x66, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x8d, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12,
	0x4d, 0x0a, 0x12, 0x63, 0x66, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x66, 0x67, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x63, 0x66,
	0x67, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31,
	0x0a, 0x08, 0x63, 0x66, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x43, 0x66, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x63, 0x66, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x67, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x66, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x66, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x63, 0x66, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x66, 0x67,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x66, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x7a, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x66, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x66,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x63, 0x66, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x63, 0x66, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x66, 0x67, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2a, 0x78, 0x0a, 0x10, 0x43, 0x66, 0x67, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x46, 0x47, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x43, 0x46, 0x47, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d,
	0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x43, 0x46, 0x47, 0x10, 0x03, 0x12,
	0x0f, 0x0a, 0x0b, 0x47, 0x41, 0x54, 0x45, 0x57, 0x41, 0x59, 0x5f, 0x43, 0x46, 0x47, 0x10, 0x04,
	0x2a, 0x37, 0x0a, 0x07, 0x43, 0x66, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x46, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x00, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x49, 0x4c,
	0x54, 0x45, 0x52, 0x5f, 0x43, 0x46, 0x47, 0x10, 0x01, 0x32, 0xa0, 0x01, 0x0a, 0x0c, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x66, 0x67, 0x12, 0x4e, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x06, 0x47, 0x65,
	0x74, 0x43, 0x66, 0x67, 0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x42, 0x2e, 0x5a, 0x2c,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_appliance_v1_appliance_cfg_proto_rawDescOnce sync.Once
	file_appliance_v1_appliance_cfg_proto_rawDescData = file_appliance_v1_appliance_cfg_proto_rawDesc
)

func file_appliance_v1_appliance_cfg_proto_rawDescGZIP() []byte {
	file_appliance_v1_appliance_cfg_proto_rawDescOnce.Do(func() {
		file_appliance_v1_appliance_cfg_proto_rawDescData = protoimpl.X.CompressGZIP(file_appliance_v1_appliance_cfg_proto_rawDescData)
	})
	return file_appliance_v1_appliance_cfg_proto_rawDescData
}

var file_appliance_v1_appliance_cfg_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_appliance_v1_appliance_cfg_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_appliance_v1_appliance_cfg_proto_goTypes = []interface{}{
	(CfgApplianceType)(0),     // 0: api.appliance.CfgApplianceType
	(CfgType)(0),              // 1: api.appliance.CfgType
	(*ConfigReq)(nil),         // 2: api.appliance.ConfigReq
	(*ConfigVersionResp)(nil), // 3: api.appliance.ConfigVersionResp
	(*GetConfigResp)(nil),     // 4: api.appliance.GetConfigResp
}
var file_appliance_v1_appliance_cfg_proto_depIdxs = []int32{
	0, // 0: api.appliance.ConfigReq.cfg_appliance_type:type_name -> api.appliance.CfgApplianceType
	1, // 1: api.appliance.ConfigReq.cfg_type:type_name -> api.appliance.CfgType
	1, // 2: api.appliance.ConfigVersionResp.cfg_type:type_name -> api.appliance.CfgType
	1, // 3: api.appliance.GetConfigResp.cfg_type:type_name -> api.appliance.CfgType
	2, // 4: api.appliance.ApplianceCfg.GetConfigVersion:input_type -> api.appliance.ConfigReq
	2, // 5: api.appliance.ApplianceCfg.GetCfg:input_type -> api.appliance.ConfigReq
	3, // 6: api.appliance.ApplianceCfg.GetConfigVersion:output_type -> api.appliance.ConfigVersionResp
	4, // 7: api.appliance.ApplianceCfg.GetCfg:output_type -> api.appliance.GetConfigResp
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_appliance_v1_appliance_cfg_proto_init() }
func file_appliance_v1_appliance_cfg_proto_init() {
	if File_appliance_v1_appliance_cfg_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_appliance_v1_appliance_cfg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_cfg_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigVersionResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_cfg_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_appliance_v1_appliance_cfg_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_appliance_v1_appliance_cfg_proto_goTypes,
		DependencyIndexes: file_appliance_v1_appliance_cfg_proto_depIdxs,
		EnumInfos:         file_appliance_v1_appliance_cfg_proto_enumTypes,
		MessageInfos:      file_appliance_v1_appliance_cfg_proto_msgTypes,
	}.Build()
	File_appliance_v1_appliance_cfg_proto = out.File
	file_appliance_v1_appliance_cfg_proto_rawDesc = nil
	file_appliance_v1_appliance_cfg_proto_goTypes = nil
	file_appliance_v1_appliance_cfg_proto_depIdxs = nil
}
