// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbUserGroupSync(db *gorm.DB, opts ...gen.DOOption) tbUserGroupSync {
	_tbUserGroupSync := tbUserGroupSync{}

	_tbUserGroupSync.tbUserGroupSyncDo.UseDB(db, opts...)
	_tbUserGroupSync.tbUserGroupSyncDo.UseModel(&model.TbUserGroupSync{})

	tableName := _tbUserGroupSync.tbUserGroupSyncDo.TableName()
	_tbUserGroupSync.ALL = field.NewAsterisk(tableName)
	_tbUserGroupSync.ID = field.NewString(tableName, "id")
	_tbUserGroupSync.GroupID = field.NewString(tableName, "group_id")
	_tbUserGroupSync.AutoSync = field.NewBool(tableName, "auto_sync")
	_tbUserGroupSync.LastSyncTime = field.NewTime(tableName, "last_sync_time")
	_tbUserGroupSync.NextSyncTime = field.NewTime(tableName, "next_sync_time")
	_tbUserGroupSync.SyncStatus = field.NewString(tableName, "sync_status")
	_tbUserGroupSync.SyncCycle = field.NewInt32(tableName, "sync_cycle")
	_tbUserGroupSync.SyncUnit = field.NewString(tableName, "sync_unit")
	_tbUserGroupSync.CreatedAt = field.NewTime(tableName, "created_at")
	_tbUserGroupSync.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbUserGroupSync.fillFieldMap()

	return _tbUserGroupSync
}

type tbUserGroupSync struct {
	tbUserGroupSyncDo tbUserGroupSyncDo

	ALL          field.Asterisk
	ID           field.String
	GroupID      field.String
	AutoSync     field.Bool
	LastSyncTime field.Time
	NextSyncTime field.Time
	SyncStatus   field.String
	SyncCycle    field.Int32
	SyncUnit     field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time

	fieldMap map[string]field.Expr
}

func (t tbUserGroupSync) Table(newTableName string) *tbUserGroupSync {
	t.tbUserGroupSyncDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbUserGroupSync) As(alias string) *tbUserGroupSync {
	t.tbUserGroupSyncDo.DO = *(t.tbUserGroupSyncDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbUserGroupSync) updateTableName(table string) *tbUserGroupSync {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.GroupID = field.NewString(table, "group_id")
	t.AutoSync = field.NewBool(table, "auto_sync")
	t.LastSyncTime = field.NewTime(table, "last_sync_time")
	t.NextSyncTime = field.NewTime(table, "next_sync_time")
	t.SyncStatus = field.NewString(table, "sync_status")
	t.SyncCycle = field.NewInt32(table, "sync_cycle")
	t.SyncUnit = field.NewString(table, "sync_unit")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbUserGroupSync) WithContext(ctx context.Context) *tbUserGroupSyncDo {
	return t.tbUserGroupSyncDo.WithContext(ctx)
}

func (t tbUserGroupSync) TableName() string { return t.tbUserGroupSyncDo.TableName() }

func (t tbUserGroupSync) Alias() string { return t.tbUserGroupSyncDo.Alias() }

func (t *tbUserGroupSync) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbUserGroupSync) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["id"] = t.ID
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["auto_sync"] = t.AutoSync
	t.fieldMap["last_sync_time"] = t.LastSyncTime
	t.fieldMap["next_sync_time"] = t.NextSyncTime
	t.fieldMap["sync_status"] = t.SyncStatus
	t.fieldMap["sync_cycle"] = t.SyncCycle
	t.fieldMap["sync_unit"] = t.SyncUnit
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbUserGroupSync) clone(db *gorm.DB) tbUserGroupSync {
	t.tbUserGroupSyncDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbUserGroupSync) replaceDB(db *gorm.DB) tbUserGroupSync {
	t.tbUserGroupSyncDo.ReplaceDB(db)
	return t
}

type tbUserGroupSyncDo struct{ gen.DO }

func (t tbUserGroupSyncDo) Debug() *tbUserGroupSyncDo {
	return t.withDO(t.DO.Debug())
}

func (t tbUserGroupSyncDo) WithContext(ctx context.Context) *tbUserGroupSyncDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbUserGroupSyncDo) ReadDB() *tbUserGroupSyncDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbUserGroupSyncDo) WriteDB() *tbUserGroupSyncDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbUserGroupSyncDo) Session(config *gorm.Session) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbUserGroupSyncDo) Clauses(conds ...clause.Expression) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbUserGroupSyncDo) Returning(value interface{}, columns ...string) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbUserGroupSyncDo) Not(conds ...gen.Condition) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbUserGroupSyncDo) Or(conds ...gen.Condition) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbUserGroupSyncDo) Select(conds ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbUserGroupSyncDo) Where(conds ...gen.Condition) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbUserGroupSyncDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbUserGroupSyncDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbUserGroupSyncDo) Order(conds ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbUserGroupSyncDo) Distinct(cols ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbUserGroupSyncDo) Omit(cols ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbUserGroupSyncDo) Join(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbUserGroupSyncDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbUserGroupSyncDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbUserGroupSyncDo) Group(cols ...field.Expr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbUserGroupSyncDo) Having(conds ...gen.Condition) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbUserGroupSyncDo) Limit(limit int) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbUserGroupSyncDo) Offset(offset int) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbUserGroupSyncDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbUserGroupSyncDo) Unscoped() *tbUserGroupSyncDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbUserGroupSyncDo) Create(values ...*model.TbUserGroupSync) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbUserGroupSyncDo) CreateInBatches(values []*model.TbUserGroupSync, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbUserGroupSyncDo) Save(values ...*model.TbUserGroupSync) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbUserGroupSyncDo) First() (*model.TbUserGroupSync, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSync), nil
	}
}

func (t tbUserGroupSyncDo) Take() (*model.TbUserGroupSync, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSync), nil
	}
}

func (t tbUserGroupSyncDo) Last() (*model.TbUserGroupSync, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSync), nil
	}
}

func (t tbUserGroupSyncDo) Find() ([]*model.TbUserGroupSync, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbUserGroupSync), err
}

func (t tbUserGroupSyncDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbUserGroupSync, err error) {
	buf := make([]*model.TbUserGroupSync, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbUserGroupSyncDo) FindInBatches(result *[]*model.TbUserGroupSync, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbUserGroupSyncDo) Attrs(attrs ...field.AssignExpr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbUserGroupSyncDo) Assign(attrs ...field.AssignExpr) *tbUserGroupSyncDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbUserGroupSyncDo) Joins(fields ...field.RelationField) *tbUserGroupSyncDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbUserGroupSyncDo) Preload(fields ...field.RelationField) *tbUserGroupSyncDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbUserGroupSyncDo) FirstOrInit() (*model.TbUserGroupSync, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSync), nil
	}
}

func (t tbUserGroupSyncDo) FirstOrCreate() (*model.TbUserGroupSync, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbUserGroupSync), nil
	}
}

func (t tbUserGroupSyncDo) FindByPage(offset int, limit int) (result []*model.TbUserGroupSync, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbUserGroupSyncDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbUserGroupSyncDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbUserGroupSyncDo) Delete(models ...*model.TbUserGroupSync) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbUserGroupSyncDo) withDO(do gen.Dao) *tbUserGroupSyncDo {
	t.DO = *do.(*gen.DO)
	return t
}
