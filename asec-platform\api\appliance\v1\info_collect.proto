syntax = "proto3";

package api.appliance;
option go_package = "asdsec.com/asec/platform/api/appliance/v1;v1";

service ReportCollectedInfo {
  rpc CollectedInfo (InfoCollectReq) returns (Reply);
}

message InfoCollectReq {
  uint64   agent_id = 1;
  repeated ProcessInfo  process_infos   = 2;
  repeated SoftwareInfo software_infos  = 3;
  repeated SystemInfo   system_infos    = 4;
}

message Reply {}

message ProcessInfo {
  string  process_name    = 1;
  string  proc_path       = 2;
  string  cmd_line        = 3;
  string  user_name       = 4;
  int32   process_pid     = 5;
  int32   parent_pid      = 6;
  int32   mem_used_kb     = 7;
  int32   vmmem_used_kb   = 8;
  int32   thread_num      = 9;
  double  cpu_kernel_used = 10;
  double  cpu_user_used   = 11;
}

message SoftwareInfo {
  string software_name    = 1;
  string software_version = 2;
  string software_company = 3;
  string uninstall_cmd    = 4;
  string install_path     = 5;
}

message SystemInfo {
  string system_bit       = 1;
  string product_name     = 2;
  string system_language  = 3;
  string system_version   = 4;
}
