package service

import (
	"crypto/sha1"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"fmt"
	"os"
	"os/exec"
	"path"
	"time"
)

const (
	SSL_DIR             = "/opt/asdsec-compose/openresty/ssl"
	CERT_FILE           = "server.crt"
	KEY_FILE            = "server.key"
	OPENRESTY_CONTAINER = "openresty" // OpenResty容器名称
)

type NginxService struct{}

type SSLConfig struct {
	Certificate string `json:"certificate"` // 证书内容
	PrivateKey  string `json:"privateKey"`  // 私钥内容
}

func NewNginxService() *NginxService {
	// 确保SSL目录存在
	if err := os.MkdirAll(SSL_DIR, 0700); err != nil {
		panic(fmt.Sprintf("create ssl directory failed: %v", err))
	}
	return &NginxService{}
}

type SSLCertInfo struct {
	Subject           string    `json:"subject"`       // 证书主体
	Issuer            string    `json:"issuer"`        // 颁发者
	NotBefore         time.Time `json:"notBefore"`     // 生效时间
	NotAfter          time.Time `json:"notAfter"`      // 过期时间
	SerialNumber      string    `json:"serialNumber"`  // 序列号
	DNSNames          []string  `json:"dnsNames"`      // DNS名称列表
	SignatureAlgo     string    `json:"signatureAlgo"` // 签名算法
	PublicKeyAlgo     string    `json:"publicKeyAlgo"` // 公钥算法
	Version           int       `json:"version"`       // 证书版本
	SHA1Fingerprint   string    `json:"sha1"`          // SHA1指纹
	SHA256Fingerprint string    `json:"sha256"`        // SHA256指纹
}

func (s *NginxService) GetSSLConfig() (*SSLCertInfo, error) {
	certPath := path.Join(SSL_DIR, CERT_FILE)

	// 读取证书文件
	certData, err := os.ReadFile(certPath)
	if err != nil {
		return nil, fmt.Errorf("读取证书文件失败: %v", err)
	}

	// 解析证书信息
	block, _ := pem.Decode(certData)
	if block == nil {
		return nil, fmt.Errorf("解析证书PEM格式失败")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("解析证书内容失败: %v", err)
	}

	// 计算证书指纹
	sha1Sum := sha1.Sum(cert.Raw)
	sha256Sum := sha256.Sum256(cert.Raw)

	// 只返回证书信息
	return &SSLCertInfo{
		Subject:           cert.Subject.CommonName,
		Issuer:            cert.Issuer.CommonName,
		NotBefore:         cert.NotBefore,
		NotAfter:          cert.NotAfter,
		SerialNumber:      cert.SerialNumber.String(),
		DNSNames:          cert.DNSNames,
		SignatureAlgo:     cert.SignatureAlgorithm.String(),
		PublicKeyAlgo:     cert.PublicKeyAlgorithm.String(),
		Version:           cert.Version,
		SHA1Fingerprint:   hex.EncodeToString(sha1Sum[:]),
		SHA256Fingerprint: hex.EncodeToString(sha256Sum[:]),
	}, nil
}

// UpdateSSLConfig 更新SSL证书配置
func (s *NginxService) UpdateSSLConfig(config *SSLConfig) error {
	// 1. 备份现有证书
	if err := s.backupCurrentCerts(); err != nil {
		return fmt.Errorf("backup current certificates failed: %v", err)
	}

	// 2. 写入新证书
	certPath := path.Join(SSL_DIR, CERT_FILE)
	keyPath := path.Join(SSL_DIR, KEY_FILE)

	if err := os.WriteFile(certPath, []byte(config.Certificate), 0600); err != nil {
		return fmt.Errorf("write certificate file failed: %v", err)
	}

	if err := os.WriteFile(keyPath, []byte(config.PrivateKey), 0600); err != nil {
		// 如果写入私钥失败，尝试恢复备份
		s.restoreBackup()
		return fmt.Errorf("write private key file failed: %v", err)
	}

	// 3. 重新加载OpenResty配置
	if err := s.reloadOpenresty(); err != nil {
		// 如果重载失败，恢复备份
		s.restoreBackup()
		return fmt.Errorf("reload openresty failed: %v", err)
	}

	// 4. 成功后删除备份
	s.removeBackup()

	return nil
}

// backupCurrentCerts 备份当前证书
func (s *NginxService) backupCurrentCerts() error {
	certPath := path.Join(SSL_DIR, CERT_FILE)
	keyPath := path.Join(SSL_DIR, KEY_FILE)
	backupCertPath := path.Join(SSL_DIR, CERT_FILE+".bak")
	backupKeyPath := path.Join(SSL_DIR, KEY_FILE+".bak")

	// 复制证书文件
	if err := copyFile(certPath, backupCertPath); err != nil {
		return err
	}

	// 复制私钥文件
	if err := copyFile(keyPath, backupKeyPath); err != nil {
		os.Remove(backupCertPath) // 清理已复制的证书备份
		return err
	}

	return nil
}

// restoreBackup 恢复备份的证书
func (s *NginxService) restoreBackup() {
	certPath := path.Join(SSL_DIR, CERT_FILE)
	keyPath := path.Join(SSL_DIR, KEY_FILE)
	backupCertPath := path.Join(SSL_DIR, CERT_FILE+".bak")
	backupKeyPath := path.Join(SSL_DIR, KEY_FILE+".bak")

	os.Rename(backupCertPath, certPath)
	os.Rename(backupKeyPath, keyPath)
}

// removeBackup 删除备份文件
func (s *NginxService) removeBackup() {
	backupCertPath := path.Join(SSL_DIR, CERT_FILE+".bak")
	backupKeyPath := path.Join(SSL_DIR, KEY_FILE+".bak")

	os.Remove(backupCertPath)
	os.Remove(backupKeyPath)
}

// ReloadOpenresty 重新加载OpenResty配置 (公开方法)
func (s *NginxService) ReloadOpenresty() error {
	return s.reloadOpenresty()
}

// reloadOpenresty 重新加载OpenResty配置
func (s *NginxService) reloadOpenresty() error {
	// 1. 首先测试配置文件
	testCmd := exec.Command("docker", "exec", OPENRESTY_CONTAINER, "nginx", "-t")
	output, err := testCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("配置文件测试失败: %v, output: %s", err, string(output))
	}

	// 2. 配置文件测试通过后，执行reload
	reloadCmd := exec.Command("docker", "exec", OPENRESTY_CONTAINER, "nginx", "-s", "reload")
	output, err = reloadCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("重载失败: %v, output: %s", err, string(output))
	}

	return nil
}

// copyFile 辅助函数：复制文件
func copyFile(src, dst string) error {
	input, err := os.ReadFile(src)
	if err != nil {
		return err
	}

	err = os.WriteFile(dst, input, 0600)
	if err != nil {
		return err
	}

	return nil
}
