/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;c=!1}else for(;!(c=(n=a.call(t)).done)&&(l.push(n.value),l.length!==r);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||r(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}function t(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}System.register(["./index-legacy.b871e767.js"],(function(t,n){"use strict";var o,a,i,l,c,u,s,f,d,g,y,p=document.createElement("style");return p.textContent='@charset "UTF-8";body[data-v-0941919a]{font-family:Arial,sans-serif;display:flex;justify-content:center;align-items:center;height:100vh;margin:0;background-color:#f5f5f5}.container[data-v-0941919a]{text-align:center;padding:20px;background:white;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,.1)}.loading[data-v-0941919a]{width:40px;height:40px;margin:0 auto 20px;border:4px solid #f3f3f3;border-top:4px solid #1890ff;border-radius:50%;animation:spin-0941919a 1s linear infinite}@keyframes spin-0941919a{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.message[data-v-0941919a]{color:#666;font-size:14px}.error[data-v-0941919a]{color:#f56c6c}.success[data-v-0941919a]{color:#67c23a}\n',document.head.appendChild(p),{setters:[function(e){o=e._,a=e.u,i=e.r,l=e.c,c=e.o,u=e.a,s=e.b,f=e.d,d=e.n,g=e.t,y=e.e}],execute:function(){var n={class:"container"},p=Object.assign({name:"Status"},{setup:function(t){var o=a(),p=i(null),v=i(null),m=i("正在处理飞书认证..."),w=i("normal"),h=i(!0),b=l((function(){return["message",w.value].filter(Boolean).join(" ")})),x=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal";m.value=e,w.value=r,"normal"!==r&&(h.value=!1)},_=function(){if(o.query&&Object.keys(o.query).length>0)return o.query;var t,n={},a=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){c=!0,i=e},f:function(){try{l||null==n.return||n.return()}finally{if(c)throw i}}}}(new URLSearchParams(window.location.search));try{for(a.s();!(t=a.n()).done;){var i=e(t.value,2),l=i[0],c=i[1];n[l]=c}}catch(u){a.e(u)}finally{a.f()}return n},k=function(e){try{logger.log("向父窗口发送消息:",e),window.parent&&window.parent!==window&&(window.parent.postMessage(e,"*"),logger.log("已向parent发送消息")),window.opener&&(window.opener.postMessage(e,"*"),logger.log("已向opener发送消息")),window.top&&window.top!==window&&(window.top.postMessage(e,"*"),logger.log("已向top发送消息"))}catch(r){console.error("发送消息失败:",r)}};return c((function(){logger.log("飞书认证状态页面挂载"),setTimeout((function(){!function(){try{var e=_();logger.log("URL参数:",e);var r=e.code,t=e.state,n=e.error,o=e.error_description,a="feishu";if(logger.log("检测到的认证类型:",a),n)return console.error("认证失败:",n,o),x("认证失败: "+(o||n),"error"),void k({type:a+"_auth_callback",error:o||n});r&&t?(logger.log("认证成功，code:",r,"state:",t),x("飞书认证成功，正在跳转...","success"),k({type:a+"_auth_callback",code:r,state:t}),setTimeout((function(){try{if(window.parent!==window){var e=a+"-callback-iframe",r=window.parent.document.getElementById(e);r&&(r.style.display="none")}window.opener&&window.close()}catch(t){logger.log("无法自动关闭/隐藏窗口:",t.message)}}),1e3)):(console.error("缺少必要的认证参数"),x("认证参数不完整","error"),k({type:a+"_auth_callback",error:"认证参数不完整"}))}catch(n){console.error("处理回调失败:",n),x("处理认证结果时出错","error"),k({type:"auth_callback",error:"处理认证结果时出错: "+n.message})}}()}),100)})),function(e,r){return u(),s("div",n,[f("div",{ref_key:"loadingRef",ref:p,class:"loading",style:d({display:h.value?"block":"none"})},null,4),f("div",{ref_key:"messageRef",ref:v,class:y(b.value)},g(m.value),3)])}}});t("default",o(p,[["__scopeId","data-v-0941919a"]]))}}}))}();
