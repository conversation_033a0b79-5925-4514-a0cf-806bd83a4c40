package custom_middleware

import (
	"context"
	"strings"

	pb "asdsec.com/asec/platform/api/auth/v1"
	"github.com/golang-jwt/jwt/v4"

	"github.com/go-kratos/kratos/v2/log"

	"asdsec.com/asec/platform/app/auth/internal/dto"

	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/service"
	"asdsec.com/asec/platform/pkg/utils/jwt_util"

	"github.com/go-kratos/kratos/v2/transport"

	"github.com/go-kratos/kratos/v2/middleware"
)

const (
	authorizationKey string = dto.AuthorizationKey
	bearerWord       string = dto.TokenTypeBear
)

var (
	paramError   = pb.ErrorTokenParseFailed("param error")
	tokenExpired = pb.ErrorTokenExpire("token expire")
	tokenInvalid = pb.ErrorTokenInvalid("token invalid")
)

// JwtAccessAuth 普通用户的 accessToken 校验中间件
func JwtAccessAuth(service *service.UserService, logger log.Logger) middleware.Middleware {
	l := log.NewHelper(logger)
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			corpId, err := common.GetCorpId(ctx)
			if err != nil {
				return nil, err
			}
			publicKey, err := service.GetPublicKey(ctx, corpId)
			if err != nil {
				return nil, err
			}
			claims, err := parseJwtToken(ctx, service, dto.AccessTokenTyp, l, publicKey, dto.UserAuthType)
			if err != nil {
				return nil, err
			}
			ctx = common.SetClaim(ctx, claims)
			return handler(ctx, req)
		}
	}
}

// JwtAccessAdminAuth 管理员接口的 accessToken 校验中间件
func JwtAccessAdminAuth(service *service.UserService, logger log.Logger) middleware.Middleware {
	l := log.NewHelper(logger)
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			publicKey, err := service.GetAdminPublicKey(ctx)
			if err != nil {
				return nil, err
			}
			claims, err := parseJwtToken(ctx, service, dto.AccessTokenTyp, l, publicKey, dto.AdminAuthType)
			if err != nil {
				return nil, err
			}
			ctx = common.SetClaim(ctx, claims)
			return handler(ctx, req)
		}
	}
}
func CheckUserId(ctx context.Context, service *service.UserService, userId string, userType string) bool {
	res, err := service.CheckUserIdExist(ctx, userId, userType)
	if err != nil {
		return false
	}
	return res
}
func parseJwtToken(ctx context.Context, service *service.UserService, tokenType dto.TokenType, l *log.Helper, publicKey any, authType string) (jwt.Claims, error) {
	header, ok := transport.FromServerContext(ctx)
	if !ok {
		l.Errorf("wrong context")
		return nil, paramError
	}
	auths := strings.SplitN(header.RequestHeader().Get(authorizationKey), " ", 2)
	if len(auths) != 2 || !strings.EqualFold(auths[0], bearerWord) {
		l.Errorf("missing jwt token. auths=%v, type=%v", auths, tokenType)
		return nil, paramError
	}

	parser := jwt_util.NewJwtParse(jwt_util.Ecdsa256)
	jwtToken := auths[1]
	claims, err := parser.ParseToken(jwtToken, publicKey)
	if err != nil {
		ve, ok := err.(*jwt.ValidationError)
		if !ok {
			l.Errorf("err type error. err=%v, token=%v, type=%v", err, jwtToken, tokenType)
			return nil, paramError
		}
		if ve.Errors&jwt.ValidationErrorMalformed != 0 {
			l.Warnf("token malformed. token=%v, type=%v", jwtToken, tokenType)
			return nil, tokenInvalid
		}
		if ve.Errors&(jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
			//l.Warnf("token expired. token=%v, type=%v", jwtToken, tokenType)
			return nil, tokenExpired
		}
		l.Errorf("ParseToken err. err=%v, token=%v, type=%v", err, jwtToken, tokenType)
		return nil, paramError
	}
	if claims == nil || claims.Subject == "" {
		l.Errorf("token claims or Subject is nil")
		return nil, tokenInvalid
	}
	
	// 检查 token 黑名单（对 access token 和 refresh token 都进行检查）
	if CheckTokenFromRedis(ctx, auths[1], service, l, authType) {
		l.Errorf("token in blacklist: token=%v, type=%v", jwtToken, tokenType)
		return nil, tokenInvalid
	}

	if claims.Typ != string(tokenType) {
		l.Errorf("jwt typ=%v not support", claims.Typ)
		return nil, tokenInvalid
	}
	checkUserId := CheckUserId(ctx, service, claims.Subject, authType)
	if !checkUserId {
		l.Errorf("user not exist,user-id:%v", claims.Subject)
		return nil, tokenInvalid
	}
	return claims, nil
}

func CheckTokenFromRedis(ctx context.Context, token string, userService *service.UserService, l *log.Helper, authType string) bool {
	ret, err := userService.CheckToken(ctx, token, authType)
	if err != nil {
		l.Errorf("CheckTokenFromRedis failed. err=%v", err)
		return false
	}
	return ret
}

// JwtRefreshAuth 普通用户的 refreshToken 校验中间件
func JwtRefreshAuth(service *service.UserService, logger log.Logger) middleware.Middleware {
	l := log.NewHelper(logger)
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			corpId, err := common.GetCorpId(ctx)
			if err != nil {
				return nil, err
			}
			publicKey, err := service.GetPublicKey(ctx, corpId)
			if err != nil {
				return nil, err
			}
			claims, err := parseJwtToken(ctx, service, dto.RefreshTokenTyp, l, publicKey, dto.UserAuthType)
			if err != nil {
				return nil, err
			}
			ctx = common.SetClaim(ctx, claims)
			return handler(ctx, req)
		}
	}
}
