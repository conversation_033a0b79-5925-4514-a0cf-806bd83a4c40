CommonError = "服务端异常"

Success = "操作成功"

OperateError = "操作失败， 请重试!"
ParamInvalidError = "非法的请求参数!"
UnknownError = "服务端未知异常"
DBOperateError = "数据操作异常"

# 认证
AuthFailedError = "认证失败， 请重新登录!"
PrivacyError = "隐私协议校验失败，请重新勾选！"
CaptchaExpireError = "验证码已过期！"
CaptchaVerifyError = "验证码不正确！"
CaptchaNoExistError = "认证失败，请输入验证码"
AccountOrPwdError = "账号或密码错误！您还有 {{.LeftRetries}} 次重试机会"
UserDisable = "该管理员被禁用！"
CsrfTokenValidateError = "csrf校验失败！"
PermissionError = "对不起，您没有权限执行该操作！"
AccountLockedError = "您的账户已被锁定！剩余锁定时间：{{.Limit}} 秒"
LimitLoginIpError = "已配置限制允许登录平台的源IP地址"
MaxSessionError = "登录会话数已达到最大限制（{{.LimitMax}}）"
TotpAuthError = "认证码错误！您还有 {{.LeftRetries}} 次重试机会"

# 用户
DuplicateNameError = "管理员名称重复！"
UserNotExistError = "管理员不存在！"
ConfirmPasswordError = "确认密码不一致！"
DeleteAdminError = "不允许删除admin管理员！"
UpdateAdminPwdError = "您没有修改admin管理员密码的权限！"
UsersNumberExceedError = "管理员数量已经达到最大值（{{.LimitMax}}）！"
UserNamePrefixInvalidError = "用户名前缀不合法"

# 密码
PasswordLengthError = "密码长度必须在{{.LimitMin}} 和 {{.LimitMax}}之间！"
PasswordHanziError = "密码中不能包含中文！"
PasswordWidthCharError = "密码中不能包含全角字符！"
PasswordBlankError = "密码中不能包含空格！"
PasswordWeak = "密码被检测为弱密码！"
PasswordNeedStrength = "密码中需要包含小写字母、大写字母、特殊字符以及数字中的3种，且不包含中文，中文标点，长度在8-63字符之间！"
PasswordNotCorrect = "用户名或密码错误！"
PasswordContainUserName = "密码不能包含用户名！"
PasswordUsed = "新密码不能与旧密码相同！"

#授权
AuthorizationError = "获取授权信息失败"

# 权限
RolesNumExceed = "用户仅被允许拥有一种角色"
RolesCountError = "1个用户必须且只能拥有{{.LimitNum}}个角色"

# 报表
ReportSendEmailsDuplicateError = "推送邮件地址请勿重复"
SameReportNameError = "不允许同名报表"
ReportCanNotDelete = "内置报表不允许删除"
ReachReportsLimit = "报表数量达到了最大限制：{{.LimitNum}}"
ReportCanNotEdit = "内置报表不允许编辑"
ReportSendSettingsNotSet = "未设置推送参数"

# 邮件
AuthMailError = "用户名必须和发件人邮箱一致"
MailSmtpSendError = "发送SMTP邮件失败，请检查配置是否准确"
SmtpServerNotSet = "未设置SMTP邮箱服务器"
SmtpServerAddrError = "smtp服务器地址校验失败，必须为有效的域名或者ipv4地址"

# 资产中心
AddGroupError = "添加分组失败!"
AddGroupSameFloorExceedError = "添加分组失败，同级分组数量超过上限：{{.SameFloorMax}}！"
AddGroupSonFloorExceedError = "添加分组失败，子级分组数量超过上限：{{.SonFloorMax}}！"
AddGroupMaxNumExceedError = "添加分组失败，超出分组总数限制：{{.GroupNumMax}}！"
AddGroupSameNameExistsError = "添加分组失败，已存在同名分组！"
EditGroupSameNameExistsError = "编辑分组失败，存在同名分组！"
AddNoGroupError = "添加分组失败，无法在未分组主机下创建分组！"
ModifyGroupError = "编辑分组失败！"
QueryGroupError = "查询分组失败！"
DelGroupError = "删除分组失败！"
MoveGroupError = "移动分组失败！"
AssetNumberInvalid = "非法的资产编号！"
LocationInvalid = "非法的机房位置！"
MailInvalid = "非法的邮箱格式！"
TelInvalid = "非法的电话格式！"
UserInvalid = "非法的用户名！"
SpecialCharCheckError = "不能输入特殊字符"
AutoGroupSettingError = "当前IP段与已设置的自动分组IP段存在冲突，已存在的ip段为：{{.ConflictIpRange}}"
AutoGroupIpRangeError = "错误的IP范围！"
IpRangeMaxError = "IP段超过数量限制！"
IpV4Error = "非法的IP地址:"
GetAssetInfoError = "获取主机基本信息失败！"
GetAssetListError = "获取主机列表失败！"
OprUnauthHostError = "未授权主机：{{.agent}}仅支持移除操作，请检查授权数量是否充足"
DelAgentRecycleAuthError = "移除失败，原因：主机授权回收失败"
DelAgentPolicyRunError = "移除失败，原因：主机基线策略正在执行"

PortExcel = "指纹调查监听端口"
SingleAssetPortExcel = "主机监听端口"
SinglePortAssetExcel = "监听端口主机列表"
AgentInfoExcel = "主机列表"
DatabaseExcel = "指纹调查数据库"

HighLoadRemark = "{{.hour}}小时内，CPU或内存使用率持续为高负载时，将主机判定为高负载资产"
HostLoadUnknownTag = "未知"


FormatDemo = "格式化 demo {{.StringVarTest}} -- {{.IntVarTest}}"

TlsConfigError = "配置信息读取失败！"
SshConfigError = "后台接入信息获取失败！"
OpenSshError = "用户开启SSH后台接入服务失败！"
CloseSshError = "用户关闭SSH后台接入服务失败！"
OpenSshSuccess = "用户开启SSH后台接入服务成功！"
CloseSshSuccess = "用户关闭SSH后台接入服务成功！"
ParamError = "参数错误！"
ParamTls2NotOpen = "参数错误，tls1.2协议默认开启！"
SslTlsSetError = "SSL/TLS协议设置失败！"
NotOvaSystem = "系统不符合要求！"
SystemTimeSetError = "系统时间设置失败！"
NtpNetError = "与NTP服务器同步失败,请检查网络状态！"
NtpSaveError = "保存ntp配置失败,请检查配置内容与mgr状态！"
NtpAddrEmpty = "NTP服务器不能为空！"
NtpAddrOverLong = "NTP服务器地址长度不能超过256位！"
NtpNotAllNum = "域名不能是全数字！"
NtpNotIpv4 = "NTP服务器ip地址不符合IPV4格式！"
NtpNotFount = "找不到域名对应的ip地址！"
CloudBrainNetError = "开启云脑接入网络不可达！"
CloudBrainSetError = "云脑接入设置失败！"
CloudBrainOpenSuccess = "开启云脑接入服务成功！"
CloudBrainCloseSuccess = "关闭云脑接入服务成功！"
ConflictRangeError = "设置的阈值范围冲突"
CloudBrainNotOpen = "请先打开云脑开关"
CloudBrainNoPkg = "云脑无可用规则库包"
CloudBrainReqErr = "请求云脑失败，请联系管理员"
DownCloudBrainPkgErr = "从云脑获取下载安装包失败，请检查网络情况"
AutoUpgradeError = "设置自动升级失败"
LatestRuleVersion = "规则库已经是最新版本，无需更新"

GetIpAddressFail = "获取ip地址失败"
GetLoadThreshError = "获取资源阈值配置失败"
GetMemTopError = "获取内存使用排行失败"
GetCpuTopError = "获取cpu使用排行失败"
GetMemRangeError = "获取内存使用范围统计值失败"
GetCpuRangeError = "获取cpu使用范围统计值失败"

# 平台升级模块
FilenameNotSuitable = "当前文件名不符合要求"
FilenameTooLong = "当前文件名太长，不能超过{{.charNum}}个字节"
FiletypeNotSuitable = "您上传的文件类型不符合"
FileSizeErr = "安装包大小错误，不支持升级"
PackageError = "安装包内容格式不正确或者安装包已损坏"
UploadSuccess = "文件上传成功!"
CheckSignErr = "签名校验失败"
UpgradeSuccess = "升级成功"
OtherIsUpdating = "其他程序正在升级"
GetPkgNameErr = "获取包名失败"
CheckPkgError1 = "升级包格式或者内容非法"
CheckPkgError2 = "升级包版本过低"
CheckPkgError3 = "升级包不支持在此平台升级，请联系售后服务："
UpgradeFailed = "升级失败"
CheckPkgErr = "此平台版本不满足升级条件"
CheckAuthError = "平台当前授权状态仅支持导入补丁包升级"
UpgradeStateFailed = "获取升级状态信息失败"
PkgDecompressionFail = "升级包解压失败"
GetVersionFail = "获取版本失败"
UpgradeCmdSuccess = "正在升级，请稍后"
NoDiskSpace = "磁盘空间不足"
CancelSuccess = "取消升级成功"
PackageTypeError = "获取安装包类型失败"
ReadmeInfoErr = "获取readme信息失败"
CreateFileErr = "文件创建失败,权限不足或目录不存在!"
OpenFileError = "文件打开失败"
GetInstCodeErr = "获取安装码失败"

OprLogExcelName = "操作日志"


# 授权模块业务
InnerError = "内部错误"
InvalidLicFile = "无效的授权文件"
ParseLicError = "解析授权文件失败"
LicSnExpiredError = "授权码已过期"
MachineTimeError = "平台时间与北京时间不一致，请修改时间后重试"
InitAuthError = "初始化授权失败"
GetAuthInfoError = "获取授权信息失败"
DownloadDevInfoError = "下载设备信息文件失败"

UploadFileError = "文件上传失败"
UploadFileNameError = "文件名长度超过255字节"

# 终端升级模块
GetUpgradeCfgError = "获取升级设置失败"
UpdUpgradeCfgError = "保存升级设置失败"
SearchAgentCPError = "查询终端程序列表失败"

DeviceNotFound = "获取设备信息失败"
UserOrPwdError = "用户或密码错误"
LoginError = "登录失败"
LoginLimit = "超过单个用户登录地点上限，请稍候重试！登录失败"
LoginAfter5Minus = "登录失败次数超限, 请您在5分钟后再重新尝试登录!"

# 配置中心模块
SwitchModelFail = "模块开关设置失败!"
QueryConfigError = "查询配置中心配置失败！"

# 代理服务器配置
DetailExists = "已有相同描述，请不要重复添加"
DataOverLimit = "代理服务器数据超过128条"

# open API
OpenApiConfError = "jwt 配置不存在"
OpenApiAccountError = "该账户不存在"
OpenApiAuthError = "您的账号或密码错误"
OpenApiIpError = "ip白名单限制，请求ip不在设置白名单范围"

QueryDeviceErr = "查询设备信息失败"
AddAppErr = "添加应用失败"
AddAppGroupErr = "添加应用分组失败"
AppAddrRepeat = "应用地址重复，请重新添加"
UpdateAppErr = "更新应用失败"
AppNameRepeat = "应用名称重复"
AppNameRepeatDetail = "应用名称【{{.app_name}}】重复"
GateWayEmpty = "请先添加网关"
TemplateError = "EXCEL模板错误"
AppAndPolicyAss = "该应用已与{{.strategy_names}}访问策略关联,请进入访问策略中取消关联。"
AppDelErr = "删除应用失败"
# 告警事件模块
QueryDataTypeErr = "查询数据类型失败"
QueryOutboundWayErr = "查询外发通道失败"

#敏感数据策略
QuerySensitiveElemTagErr = "查询敏感元素标签失败"
AddSensitiveElemTagErr = "添加敏感元素标签失败,请确认是否有重名标签"
DeleteSensitiveElemTagErr = "删除敏感元素标签失败"
QuerySensitiveElemErr = "查询敏感元素失败"
AddSensitiveElemErr = "添加敏感元素失败,请先确认是否有重名元素"
DeleteSensitiveElemErr = "删除敏感元素失败"
DeleteSensitiveElemAssocErr = "删除敏感元素失败,该敏感元素被策略引用"
ChangeSensitiveElemErr = "更改敏感元素失败"
SensitiveElemTotalErr = "查询敏感元素总数失败"
QuerySensitiveStrategyErr = "查询敏感策略失败"
AddSensitiveStrategyErr = "添加敏感策略失败,请先确认是否有重名策略"
DeleteSensitiveStrategyErr = "删除敏感策略失败,请先确认是否关联告警规则"
ChangeSensitiveStrategyErr = "更改敏感策略失败"
QueryDefaultSensitiveStrategyErr = "查询内置敏感策略失败"
CheckAddStrategyReqError = "请为你选择的识别方式设置识别条件"
CheckAddStrategyReqRequiredError = "请添加识别方式"
CheckFileNameRuleErr = "敏感名称规则参数错误,确认满足部分数量大于0"

#敏感分类
CreateSensitiveCategoryErr = "新增失败,请确认是否有重名分类"
UpdateSensitiveCategoryErr = "修改失败,请确认是否有重名分类"
DelSensitiveCategoryErr = "删除失败,请先检查是否被终端策略引用"

#DLP策略
CreateDlpStrategyErr = "新增DLP策略失败,请先确认是否有重名策略"
UpdateDlpStrategyErr = "修改DLP策略失败,请先确认是否有重名策略"

#行为分析
QueryIncidentListErr = "查询事件列表失败"
QueryUEBAStrategySumErr = "UEBA策略总结失败"
QueryEventStateSumErr = "事件状态总结失败"
QueryUserTopErr = "行为分析查询用户排行失败"
ChangeIncidentStateErr = "更改事件状态失败"
QueryIncidentSummaryErr = "查询事件摘要失败"
QueryIncidentPartialSumErr = "查询事件摘要上部分失败"
IncidentExportErr = "导出UEBA事件列表失败"


CidrInvalidError = "网段不能大于24位，请重新添加"
# 策略
StrategyNameRepeat = "策略名称重复，请重新添加"
# 调查分析
DeleteHistoryError = "删除历史记录失败"
AddHistoryError = "添加历史记录失败"
GetHistoryError = "获取历史记录失败"
GetEventsError = "获取调查分析事件失败"
GetConditionError = "获取调查分析过滤条件失败"

#模块开关
UpsetAgentModuleSwitchErr = "打开/关闭终端模块开关失败"
UpdateModuleSwitchErr = "打开/关闭开关失败"
GetModuleSwitchErr = "获取模块开关信息失败"

# 下载安装
GetFileFromOss = "获取安装包失败，请检查网络情况"
FileParseError = "安装包解析失败，请联系管理员!"
FileCheckError = "安装包校验失败，请联系管理员!"
GetDeployChannelErr = "获取安装包通道信息，请联系管理员!"

#平台首页
IncidentTypeTopNErr = "获取事件类型TOP失败"
SendTopNErr = "获取外发总览TOP失败"

#平台地址
CreatePlatformIPInfoErr = "获取平台地址信息失败"
DeletePlatformIPInfoErr = "删除平台地址信息失败"
ChangePlatformIPInfoErr = "更改平台地址信息失败"

#查询ES256密钥
QueryES256KeyErr = "查询密钥失败"

#查询系统版本失败
QuerySystemVersionErr = "查询系统版本失败"
QueryLicenseInfoErr = "查询授权信息失败"

# 数据来源
CreateDdrSourceErr = "创建数据来源失败"
CreateDdrSourceDuplicateErr = "创建来源失败:重复的来源名称"
NotSupportedProcessName = "不受支持的进程名"
DelSourceErr = "删除数据来源失败"
UpdateSourceErr = "编辑数据来源失败"
SourceListErr = "查询来源列表失败"
DetailSourceErr = "查询来源详情失败"
CheckSensitiveQuoteErr = "删除来源失败,被删除来源关联如下敏感数据策略:{{.rule_names}}"

# ueba策略
UebaStrategyNameRepeat = "策略名称重复，请重新添加"
UebaStrategyEditNotAllowed = "该策略不允许编辑"

# 动态策略
DefaultDynamicGroupErr = "不允许对默认分组进行操作"
DuplicateNameErr = "名称重复"
OperatorRecordNotFoundErr = "操作对象不存在"
TimeIdErr = "时间间隔不存在"
CheckFactorQuoteErr = "删除配置失败,配置被如下策略引用:{{.rule_names}}"
CheckFactorTimePolicyErr = "删除配置失败,配置被如下认证策略引用:{{.name}}"
MoveAfterDefaultGroupErr = "不允许移动到默认分组之后"
NetLocationReqNilErr = "网络位置参数不能都为空"

#OSS
PingOssError = "连通失败"

#证书
VerifyCertificateError = "证书校验失败，请检查证书合法性"
SdpInValidError = "请先添加组件并且检查组件是否可用"
DomainDuplicateNameError = "添加证书失败，名称不能和其他证书重复"

#应用
AppGroupNameDuplicateErr = "标签名称重复，操作失败"
AppNotExistErr = "应用不存在"
AppDomainInvalidErr = "Ip或域名地址输入格式有误，请检查输入是否正确"
AppDomainInvalidErrDetail = "应用地址【{{.app_site}}】输入格式有误，请检查输入是否正确"
AppPublishAddressDuplicateErr = "发布地址和路径重复，请检查输入地址和路径"

DownloadLogDuplicateErr = "日志采集中,请等待采集完成"

#黄金眼
UserTagNameRepeat = "标签名称重复,操作失败"
FocusOnNoTagsErr = "至少选择一个标签"

#文件类型
FileTypeNameDuplicateErr = "文件类型名称重复，操作失败"

#扫描策略
ScanStgNameRepeatErr = "策略名称重复，操作失败"

#自定义通道
ChannelNameRepeatErr = "通道类型名重复，操作失败"
ChannelRepeatErr = "通道名重复，操作失败"

[Test]
abc = "testAbc----- {{.TestVar}}"

