syntax = "proto3";

package api.conf;
option go_package = "asdsec.com/asec/platform/api/conf/v1;v1";

// 截图相关配置
message EvidenceScreenshotConf{
  // 截图开关
  bool screenshot_switch = 1;
  // 取证策略 1(滚动) 2(瞬时)
  uint32 evidence_strategy = 2;
  // 取证事件发生前时间区段
  uint32 pre_time = 3;
  // 取证事件发生后时间区段
  uint32 post_time = 4;
  // 帧率(frame/second)
  float fps = 5;
  // 截图留存最大限制 (K)
  uint64 evidence_file_size_limit = 6;
  // 分辨率宽
  uint32 resolution_ratio_w = 7;
  // 分辨率高
  uint32 resolution_ratio_h = 8;
  // 截图格式（png，jpg）
  string pic_format = 9;
}

// 原始事件取证相关配置
message EvidenceRetainFileConf{
  // 文件存储开关
  bool file_store_switch = 1;
  // 单个原始原件最大大小限制 (K)
  uint64 single_file_size_limit = 2;
  // 原始原件最大存储限制 (K)
  uint64 store_file_size_limit = 3;
}

message AdditionConf{
  string ak = 1;
  string sk = 2;
  string corp_id = 3;
  string oss_type = 4;
  string endpoint = 5;
  string bucket = 6;
  uint32 clean_mode = 7;
  uint32 expire_day = 8;
  uint32 enable = 9;
  string  key = 10;
  string  random = 11;
  string  bucket_type = 12;
  string  region = 13;
  uint32  max_retain_file_size = 14;
  string deploy_type = 15;
}
