package dingtalk

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dingtalkcontact "github.com/alibabacloud-go/dingtalk/contact_1_0"
	dingtalkoauth "github.com/alibabacloud-go/dingtalk/oauth2_1_0"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

type userInfo struct {
	UserId string
	Name   string
	Mobile string
	Email  string
}

/**
 * 使用 Token 初始化账号Client
 * @return Client
 * @throws Exception
 */
func CreateOauthClient() (_result *dingtalkoauth.Client, _err error) {
	config := &openapi.Config{}
	config.Protocol = tea.String("https")
	config.RegionId = tea.String("central")
	_result = &dingtalkoauth.Client{}
	_result, _err = dingtalkoauth.NewClient(config)
	return _result, _err
}

/**
 * 使用 Token 初始化账号Client
 * @return Client
 * @throws Exception
 */
func CreateContactClient() (_result *dingtalkcontact.Client, _err error) {
	config := &openapi.Config{}
	config.Protocol = tea.String("https")
	config.RegionId = tea.String("central")
	_result = &dingtalkcontact.Client{}
	_result, _err = dingtalkcontact.NewClient(config)
	return _result, _err
}

func GetUserToken(appKey, appSecret, code string) (accessToken string, err error) {
	client, _err := CreateOauthClient()
	if _err != nil {
		return accessToken, _err
	}

	getUserTokenRequest := &dingtalkoauth.GetUserTokenRequest{
		ClientSecret: tea.String(appSecret),
		ClientId:     tea.String(appKey),
		Code:         tea.String(code),
		GrantType:    tea.String("authorization_code"),
	}
	accessToken, tryErr := func() (accessToken string, _e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		body, _err := client.GetUserToken(getUserTokenRequest)
		if _err != nil {
			return accessToken, _err
		}

		return *body.Body.AccessToken, nil
	}()

	if tryErr != nil {
		var err = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			err = _t
		} else {
			err.Message = tea.String(tryErr.Error())
		}
		return "", err
	}
	return accessToken, _err
}

func GetUserInfo(appKey, appSecret, code string) (user userInfo, err error) {
	accessToken, err := GetUserToken(appKey, appSecret, code)
	client, _err := CreateContactClient()
	if _err != nil {
		return user, _err
	}

	getUserHeaders := &dingtalkcontact.GetUserHeaders{}
	getUserHeaders.XAcsDingtalkAccessToken = tea.String(accessToken)
	user, tryErr := func() (user userInfo, _e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		body, _err := client.GetUserWithOptions(tea.String("me"), getUserHeaders, &util.RuntimeOptions{})
		if _err != nil {
			return user, _err
		}
		if body.Body == nil {
			return user, _err
		}
		user.UserId = *body.Body.UnionId
		if body.Body.Nick != nil {
			user.Name = *body.Body.Nick
		}
		if body.Body.Mobile != nil {
			user.Mobile = *body.Body.Mobile
		}
		if body.Body.Email != nil {
			user.Email = *body.Body.Email
		}
		return user, nil
	}()

	if tryErr != nil {
		var err = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			err = _t
		} else {
			err.Message = tea.String(tryErr.Error())
		}
		return user, err
	}
	return user, _err
}

// GetAppAccessToken 获取企业内部应用的access_token（用于免登码流程）
func GetAppAccessToken(appKey, appSecret string) (accessToken string, err error) {
	fmt.Printf("GetAppAccessToken 开始: appKey=%s, appSecret存在=%t\n", appKey, appSecret != "")

	client, _err := CreateOauthClient()
	if _err != nil {
		fmt.Printf("CreateOauthClient 失败: %v\n", _err)
		return accessToken, _err
	}

	// 企业内部应用获取access_token的请求参数
	getAccessTokenRequest := &dingtalkoauth.GetAccessTokenRequest{
		AppKey:    tea.String(appKey),
		AppSecret: tea.String(appSecret),
	}
	fmt.Printf("钉钉GetSsoAccessToken请求参数: AppKey=%s, AppSecret存在=%t\n", appKey, appSecret != "")

	accessToken, tryErr := func() (accessToken string, _e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()
		body, _err := client.GetAccessToken(getAccessTokenRequest)
		if _err != nil {
			fmt.Printf("钉钉GetAccessToken API调用失败: %v\n", _err)
			return accessToken, _err
		}

		fmt.Printf("钉钉GetAccessToken API调用成功, AccessToken长度: %d\n", len(*body.Body.AccessToken))
		return *body.Body.AccessToken, nil
	}()

	if tryErr != nil {
		var err = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			err = _t
		} else {
			err.Message = tea.String(tryErr.Error())
		}
		return "", err
	}
	return accessToken, _err
}

// GetUserInfoByCode 通过免登码和企业access_token获取用户信息
func GetUserInfoByCode(accessToken, code string) (user userInfo, err error) {
	fmt.Printf("GetUserInfoByCode 开始: accessToken长度=%d, code=%s (免登码)\n", len(accessToken), code)

	// 构建请求URL
	url := fmt.Sprintf("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo?access_token=%s", accessToken)

	// 构建请求体
	requestBody := map[string]string{
		"code": code,
	}
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return user, err
	}

	fmt.Printf("调用钉钉TopAPI: URL=%s, 请求体=%s\n", url, string(jsonData))

	// 发送HTTP POST请求
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("HTTP请求失败: %v\n", err)
		return user, err
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return user, err
	}

	fmt.Printf("钉钉API响应: %s\n", string(respBody))

	// 解析响应
	var response struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
		Result  struct {
			Unionid  string `json:"unionid"`
			Userid   string `json:"userid"`
			Name     string `json:"name"`
			DeviceId string `json:"deviceId"`
			SysLevel int    `json:"sys_level"`
		} `json:"result"`
	}

	err = json.Unmarshal(respBody, &response)
	if err != nil {
		fmt.Printf("JSON解析失败: %v\n", err)
		return user, err
	}

	if response.Errcode != 0 {
		fmt.Printf("钉钉API错误: errcode=%d, errmsg=%s\n", response.Errcode, response.Errmsg)
		return user, fmt.Errorf("钉钉API错误: %s", response.Errmsg)
	}

	// 填充用户信息
	user.UserId = response.Result.Unionid
	user.Name = response.Result.Name

	fmt.Printf("钉钉用户信息解析成功: userId=%s, name=%s\n", user.UserId, user.Name)
	return user, nil
}

// GetUserInfoByFreeLoginCode 通过免登码获取用户信息（完整流程）
func GetUserInfoByFreeLoginCode(appKey, appSecret, code string) (user userInfo, err error) {
	fmt.Printf("GetUserInfoByFreeLoginCode 开始: appKey=%s, appSecret存在=%t, code=%s (免登码)\n", appKey, appSecret != "", code)

	// 第一步：获取企业内部应用的access_token
	accessToken, err := GetAppAccessToken(appKey, appSecret)
	if err != nil {
		fmt.Printf("GetAppAccessToken 失败: %v\n", err)
		return user, err
	}
	if accessToken == "" {
		fmt.Printf("GetAppAccessToken 返回空的access_token\n")
		return user, fmt.Errorf("获取应用access_token失败，返回空值")
	}

	fmt.Printf("GetAppAccessToken 成功, access_token长度: %d\n", len(accessToken))

	// 第二步：使用应用access_token + 免登码获取用户信息
	user, err = GetUserInfoByCode(accessToken, code)
	if err != nil {
		fmt.Printf("GetUserInfoByCode 失败: %v\n", err)
		return user, err
	}

	fmt.Printf("GetUserInfoByFreeLoginCode 完成: userId=%s, name=%s\n", user.UserId, user.Name)
	return user, nil
}

// SNS相关的结构体定义
type snsUserInfoResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	UserInfo snsUserInfo `json:"user_info"`
}

type snsUserInfo struct {
	Nick     string `json:"nick"`
	UnionId  string `json:"unionid"`
	OpenId   string `json:"openid"`
	MainOrgAuthHighLevel bool `json:"main_org_auth_high_level"`
}

// GetUserInfoBySNSCode 通过SNS临时授权码获取用户信息
// 用于扫码登录第三方网站场景
// 使用github.com/alibabacloud-go/dingtalk库实现
// 接口文档: https://open.dingtalk.com/document/orgapp/obtain-the-user-information-based-on-the-sns-temporary-authorization
func GetUserInfoBySNSCode(appId, appSecret, snsCode string) (user userInfo, err error) {
	fmt.Printf("GetUserInfoBySNSCode 开始: appId=%s, appSecret存在=%t, snsCode=%s (SNS临时授权码)\n", appId, appSecret != "", snsCode)

	// 创建客户端
	client, err := CreateOauthClient()
	if err != nil {
		fmt.Printf("创建钉钉客户端失败: %v\n", err)
		return user, err
	}

	// 使用阿里云SDK的通用方式调用SNS接口
	// 由于官方SDK没有直接的SNS接口，我们使用HTTP方式调用
	return callSNSAPIWithSDK(client, appId, appSecret, snsCode)
}

// callSNSAPIWithSDK 使用SDK风格调用SNS接口
// 虽然官方SDK没有直接的SNS接口，但我们使用SDK的配置和错误处理风格
func callSNSAPIWithSDK(client *dingtalkoauth.Client, appId, appSecret, snsCode string) (user userInfo, err error) {
	// 使用SDK风格的错误处理
	user, tryErr := func() (user userInfo, _e error) {
		defer func() {
			if r := tea.Recover(recover()); r != nil {
				_e = r
			}
		}()

		// 构建请求URL
		baseURL := "https://oapi.dingtalk.com/sns/getuserinfo_bycode"

		// 构建请求体
		requestBody := map[string]string{
			"tmp_auth_code": snsCode,
		}
		jsonData, err := json.Marshal(requestBody)
		if err != nil {
			fmt.Printf("JSON序列化失败: %v\n", err)
			return user, err
		}

		fmt.Printf("调用钉钉SNS API: URL=%s, 请求体=%s\n", baseURL, string(jsonData))

		// 获取当前时间戳
		timestamp := getCurrentTimestamp()

		// 计算签名
		signature := calculateSNSSignature(appSecret, timestamp)

		// 构建完整的URL
		fullURL := fmt.Sprintf("%s?accessKey=%s&timestamp=%d&signature=%s",
			baseURL, appId, timestamp, signature)

		fmt.Printf("最终请求URL: %s\n", fullURL)

		// 创建HTTP请求
		req, err := http.NewRequest("POST", fullURL, bytes.NewBuffer(jsonData))
		if err != nil {
			fmt.Printf("创建HTTP请求失败: %v\n", err)
			return user, err
		}

		// 设置请求头
		req.Header.Set("Content-Type", "application/json")

		// 使用标准HTTP客户端发送请求
		httpClient := &http.Client{}
		resp, err := httpClient.Do(req)
		if err != nil {
			fmt.Printf("HTTP请求失败: %v\n", err)
			return user, err
		}
		defer resp.Body.Close()

		// 读取响应
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Printf("读取响应失败: %v\n", err)
			return user, err
		}

		fmt.Printf("钉钉SNS API响应: %s\n", string(body))

		// 解析响应
		var response snsUserInfoResponse
		err = json.Unmarshal(body, &response)
		if err != nil {
			fmt.Printf("解析响应失败: %v\n", err)
			return user, err
		}

		// 检查错误码
		if response.ErrCode != 0 {
			fmt.Printf("钉钉SNS API返回错误: errcode=%d, errmsg=%s\n", response.ErrCode, response.ErrMsg)
			return user, fmt.Errorf("钉钉SNS API错误: %s (错误码: %d)", response.ErrMsg, response.ErrCode)
		}

		// 填充用户信息
		user.UserId = response.UserInfo.UnionId
		user.Name = response.UserInfo.Nick
		// SNS接口不返回手机号和邮箱信息

		fmt.Printf("钉钉SNS用户信息解析成功: userId=%s, name=%s, openId=%s\n",
			user.UserId, user.Name, response.UserInfo.OpenId)
		return user, nil
	}()

	// 使用SDK风格的错误处理
	if tryErr != nil {
		var sdkErr = &tea.SDKError{}
		if _t, ok := tryErr.(*tea.SDKError); ok {
			sdkErr = _t
		} else {
			sdkErr.Message = tea.String(tryErr.Error())
		}
		return user, sdkErr
	}

	return user, nil
}

// getCurrentTimestamp 获取当前时间戳（毫秒）
func getCurrentTimestamp() int64 {
	return time.Now().UnixMilli()
}

// calculateSNSSignature 计算SNS接口的签名
// 根据钉钉官方文档，签名算法为：
// 1. 使用HmacSHA256算法，密钥是appSecret，签名数据是timestamp
// 2. 对签名结果进行Base64编码
// 3. 对Base64结果进行URL编码
func calculateSNSSignature(appSecret string, timestamp int64) string {
	// 构建待签名字符串（时间戳）
	stringToSign := fmt.Sprintf("%d", timestamp)

	// 使用HmacSHA256计算签名，密钥是appSecret
	h := hmac.New(sha256.New, []byte(appSecret))
	h.Write([]byte(stringToSign))
	signature := h.Sum(nil)

	// Base64编码
	base64Signature := base64.StdEncoding.EncodeToString(signature)

	// URL编码
	return url.QueryEscape(base64Signature)
}
