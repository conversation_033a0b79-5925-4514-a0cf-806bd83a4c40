package model

import (
	"time"

	"gorm.io/gorm"
)

// WechatVerify 企业微信域名验证文件模型
type WechatVerify struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	FileName    string         `json:"fileName" gorm:"type:varchar(255);not null;uniqueIndex" binding:"required"` // 验证文件名，如：WW_verify_b9To9JT945BLlfF5.txt
	Content     string         `json:"content" gorm:"type:text;not null" binding:"required"`                      // 文件内容
	IsEnabled   bool           `json:"isEnabled" gorm:"type:boolean;default:true"`                                // 是否启用
	Description string         `json:"description" gorm:"type:varchar(500)"`                                      // 描述信息
	CreatedAt   time.Time      `json:"createdAt"`
	UpdatedAt   time.Time      `json:"updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"deletedAt" gorm:"index"`
}

// TableName 指定表名
func (WechatVerify) TableName() string {
	return "wechat_verify"
}
