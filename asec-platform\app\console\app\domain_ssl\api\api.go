package api

import (
	"asdsec.com/asec/platform/app/console/app/domain_ssl/dto"
	"asdsec.com/asec/platform/app/console/app/domain_ssl/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

// GetDomainCertificateList godoc
// @Summary 获取域名证书列表
// @Schemes
// @Description 获取域名证书列表
// @Tags        DdrSource
// @Produce     application/json
// @Success     200
// @Router      /v1/domain_ssl/list [GET]
// @success     200 {object} common.Response{data=dto.GetDomainCertificateListRsp} "ok"
func GetDomainCertificateList(c *gin.Context) {
	limit, err := strconv.Atoi(c.Query("limit"))
	if err != nil {
		common.Fail(c, common.ParamError)
		return
	}
	offset, err := strconv.Atoi(c.Query("offset"))
	if err != nil {
		common.Fail(c, common.ParamError)
		return
	}
	search := c.Query("search")
	req := model.Pagination{Limit: limit, Offset: offset, Search: search}
	data, err := service.GetDomainService().GetDomainCertificateList(c, req)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetCertificateList err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetDomainCertificateDetail godoc
// @Summary 获取证书详情
// @Schemes
// @Description 获取证书详情
// @Tags        DdrSource
// @Produce     application/json
// @Success     200
// @Router      /v1/domain_ssl/detail [GET]
// @success     200 {object} common.Response{data=dto.GetCertificateDetailRsp} "ok"
func GetDomainCertificateDetail(c *gin.Context) {
	id := c.Query("id")
	data, err := service.GetDomainService().GetCertificateDetail(c, id)
	if err != nil {
		global.SysLog.Sugar().Errorf("GetCertificateDetail err:%v", err)
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// AddDomainCertificate godoc
// @Summary 新增证书
// @Schemes
// @Description 新增证书
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body dto.CreateCertificateReq true "新增证书"
// @Success     200
// @Router      /v1/domain_ssl [POST]
// @success     200 {object} common.Response{} "ok"
func AddDomainCertificate(c *gin.Context) {
	var req dto.CreateCertificateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	id, aeErr := service.GetDomainService().AddDomainCertificate(c, req)
	if aeErr != nil {
		global.SysLog.Sugar().Errorf("AddCertificate err:%v", err)
		common.FailAError(c, aeErr)
		return
	}

	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.SslCertificate,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	common.OkWithData(c, id)
}

// DelDomainCertificate godoc
// @Summary 删除证书
// @Schemes
// @Description 删除证书
// @Tags        DdrSource
// @Produce     application/json
// @Success     200
// @Router      /v1/domain_ssl [DELETE]
// @success     200 {object} common.Response{} "ok"
func DelDomainCertificate(c *gin.Context) {
	var req dto.DelCertificateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.SslCertificate,
			OperationType:  common.OperateDelete,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	aeErr := service.GetDomainService().DelDomainCertificate(c, req.Ids)
	if aeErr != nil {
		global.SysLog.Sugar().Errorf("DelCertificate err:%v", aeErr)
		common.FailAError(c, aeErr)
		return
	}
	common.Ok(c)
}

// UpdateDomainCertificate godoc
// @Summary 编辑证书
// @Schemes
// @Description 编辑证书
// @Tags        DdrSource
// @Produce     application/json
// @Param       req body dto.CreateCertificateReq true "编辑证书"
// @Success     200
// @Router      /v1/domain_ssl [PUT]
// @success     200 {object} common.Response{} "ok"
func UpdateDomainCertificate(c *gin.Context) {
	var req dto.UpdateCertificateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	// 记录操作日志
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.SslCertificate,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	id, aeErr := service.GetDomainService().UpdateDomainCertificate(c, req)
	if aeErr != nil {
		global.SysLog.Sugar().Errorf("UpdateCertificate err:%v", err)
		common.FailAError(c, aeErr)
		return
	}
	common.OkWithData(c, id)
}
