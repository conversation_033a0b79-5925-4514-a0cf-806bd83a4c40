// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: accesslog/v1/accesslog.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Symbols defined in public import of google/protobuf/timestamp.proto.

type Timestamp = timestamppb.Timestamp

type AccessLogReply_StatusCode int32

const (
	AccessLogReply_SUCCESS AccessLogReply_StatusCode = 0
	AccessLogReply_FAILED  AccessLogReply_StatusCode = 1
)

// Enum value maps for AccessLogReply_StatusCode.
var (
	AccessLogReply_StatusCode_name = map[int32]string{
		0: "SUCCESS",
		1: "FAILED",
	}
	AccessLogReply_StatusCode_value = map[string]int32{
		"SUCCESS": 0,
		"FAILED":  1,
	}
)

func (x AccessLogReply_StatusCode) Enum() *AccessLogReply_StatusCode {
	p := new(AccessLogReply_StatusCode)
	*p = x
	return p
}

func (x AccessLogReply_StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessLogReply_StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_accesslog_v1_accesslog_proto_enumTypes[0].Descriptor()
}

func (AccessLogReply_StatusCode) Type() protoreflect.EnumType {
	return &file_accesslog_v1_accesslog_proto_enumTypes[0]
}

func (x AccessLogReply_StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessLogReply_StatusCode.Descriptor instead.
func (AccessLogReply_StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_accesslog_v1_accesslog_proto_rawDescGZIP(), []int{2, 0}
}

// AccessLogMsg 释义见 tb_access_log字段
type AccessLogMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcIp          string                 `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	SrcPort        uint32                 `protobuf:"varint,2,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`
	DstIp          string                 `protobuf:"bytes,3,opt,name=dst_ip,json=dstIp,proto3" json:"dst_ip,omitempty"`
	DstPort        uint32                 `protobuf:"varint,4,opt,name=dst_port,json=dstPort,proto3" json:"dst_port,omitempty"`
	Protocol       string                 `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
	DestinationUrl string                 `protobuf:"bytes,6,opt,name=destination_url,json=destinationUrl,proto3" json:"destination_url,omitempty"`
	Status         uint32                 `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	DenyReason     string                 `protobuf:"bytes,8,opt,name=deny_reason,json=denyReason,proto3" json:"deny_reason,omitempty"`
	AccessUserId   string                 `protobuf:"bytes,9,opt,name=access_user_id,json=accessUserId,proto3" json:"access_user_id,omitempty"`
	AccessUsername string                 `protobuf:"bytes,10,opt,name=access_username,json=accessUsername,proto3" json:"access_username,omitempty"`
	AppId          uint64                 `protobuf:"varint,11,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName        string                 `protobuf:"bytes,12,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	StrategyId     uint64                 `protobuf:"varint,13,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id,omitempty"`
	StrategyName   string                 `protobuf:"bytes,14,opt,name=strategy_name,json=strategyName,proto3" json:"strategy_name,omitempty"`
	Iss            string                 `protobuf:"bytes,15,opt,name=iss,proto3" json:"iss,omitempty"`
	AccessTime     *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=access_time,json=accessTime,proto3" json:"access_time,omitempty"`
	ClientId       uint64                 `protobuf:"varint,17,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientName     string                 `protobuf:"bytes,18,opt,name=client_name,json=clientName,proto3" json:"client_name,omitempty"`
	// 策略命中时间(ms)
	StrategyCheckTime uint32 `protobuf:"varint,19,opt,name=strategy_check_time,json=strategyCheckTime,proto3" json:"strategy_check_time,omitempty"`
	// tunserver出站IP (实际出站时使用的源IP，包括虚拟IP)
	OutboundIp string `protobuf:"bytes,20,opt,name=outbound_ip,json=outboundIp,proto3" json:"outbound_ip,omitempty"`
}

func (x *AccessLogMsg) Reset() {
	*x = AccessLogMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_accesslog_v1_accesslog_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessLogMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessLogMsg) ProtoMessage() {}

func (x *AccessLogMsg) ProtoReflect() protoreflect.Message {
	mi := &file_accesslog_v1_accesslog_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessLogMsg.ProtoReflect.Descriptor instead.
func (*AccessLogMsg) Descriptor() ([]byte, []int) {
	return file_accesslog_v1_accesslog_proto_rawDescGZIP(), []int{0}
}

func (x *AccessLogMsg) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *AccessLogMsg) GetSrcPort() uint32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *AccessLogMsg) GetDstIp() string {
	if x != nil {
		return x.DstIp
	}
	return ""
}

func (x *AccessLogMsg) GetDstPort() uint32 {
	if x != nil {
		return x.DstPort
	}
	return 0
}

func (x *AccessLogMsg) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *AccessLogMsg) GetDestinationUrl() string {
	if x != nil {
		return x.DestinationUrl
	}
	return ""
}

func (x *AccessLogMsg) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AccessLogMsg) GetDenyReason() string {
	if x != nil {
		return x.DenyReason
	}
	return ""
}

func (x *AccessLogMsg) GetAccessUserId() string {
	if x != nil {
		return x.AccessUserId
	}
	return ""
}

func (x *AccessLogMsg) GetAccessUsername() string {
	if x != nil {
		return x.AccessUsername
	}
	return ""
}

func (x *AccessLogMsg) GetAppId() uint64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *AccessLogMsg) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AccessLogMsg) GetStrategyId() uint64 {
	if x != nil {
		return x.StrategyId
	}
	return 0
}

func (x *AccessLogMsg) GetStrategyName() string {
	if x != nil {
		return x.StrategyName
	}
	return ""
}

func (x *AccessLogMsg) GetIss() string {
	if x != nil {
		return x.Iss
	}
	return ""
}

func (x *AccessLogMsg) GetAccessTime() *timestamppb.Timestamp {
	if x != nil {
		return x.AccessTime
	}
	return nil
}

func (x *AccessLogMsg) GetClientId() uint64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *AccessLogMsg) GetClientName() string {
	if x != nil {
		return x.ClientName
	}
	return ""
}

func (x *AccessLogMsg) GetStrategyCheckTime() uint32 {
	if x != nil {
		return x.StrategyCheckTime
	}
	return 0
}

func (x *AccessLogMsg) GetOutboundIp() string {
	if x != nil {
		return x.OutboundIp
	}
	return ""
}

// AccessLogMsgList AccessLogMsg集合用于批量发送
type AccessLogMsgList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessLogMsg []*AccessLogMsg `protobuf:"bytes,1,rep,name=access_log_msg,json=accessLogMsg,proto3" json:"access_log_msg,omitempty"`
	ApplianceId  uint64          `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
}

func (x *AccessLogMsgList) Reset() {
	*x = AccessLogMsgList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_accesslog_v1_accesslog_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessLogMsgList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessLogMsgList) ProtoMessage() {}

func (x *AccessLogMsgList) ProtoReflect() protoreflect.Message {
	mi := &file_accesslog_v1_accesslog_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessLogMsgList.ProtoReflect.Descriptor instead.
func (*AccessLogMsgList) Descriptor() ([]byte, []int) {
	return file_accesslog_v1_accesslog_proto_rawDescGZIP(), []int{1}
}

func (x *AccessLogMsgList) GetAccessLogMsg() []*AccessLogMsg {
	if x != nil {
		return x.AccessLogMsg
	}
	return nil
}

func (x *AccessLogMsgList) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

type AccessLogReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status AccessLogReply_StatusCode `protobuf:"varint,1,opt,name=status,proto3,enum=api.accesslog.AccessLogReply_StatusCode" json:"status,omitempty"`
}

func (x *AccessLogReply) Reset() {
	*x = AccessLogReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_accesslog_v1_accesslog_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessLogReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessLogReply) ProtoMessage() {}

func (x *AccessLogReply) ProtoReflect() protoreflect.Message {
	mi := &file_accesslog_v1_accesslog_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessLogReply.ProtoReflect.Descriptor instead.
func (*AccessLogReply) Descriptor() ([]byte, []int) {
	return file_accesslog_v1_accesslog_proto_rawDescGZIP(), []int{2}
}

func (x *AccessLogReply) GetStatus() AccessLogReply_StatusCode {
	if x != nil {
		return x.Status
	}
	return AccessLogReply_SUCCESS
}

var File_accesslog_v1_accesslog_proto protoreflect.FileDescriptor

var file_accesslog_v1_accesslog_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6c, 0x6f, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6c, 0x6f, 0x67, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95,
	0x05, 0x0a, 0x0c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x4d, 0x73, 0x67, 0x12,
	0x15, 0x0a, 0x06, 0x73, 0x72, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x72, 0x63, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50, 0x6f, 0x72,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x64, 0x73, 0x74, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12,
	0x27, 0x0a, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6e, 0x79, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x6e, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x11, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x69, 0x70, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x62,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x70, 0x22, 0x78, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x4c, 0x6f, 0x67, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0e, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6c,
	0x6f, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x4d, 0x73, 0x67, 0x52,
	0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x22, 0x79, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6c,
	0x6f, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x25, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x32, 0x61, 0x0a, 0x10, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x4d, 0x0a, 0x09, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x12, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x28, 0x01, 0x42, 0x2e,
	0x5a, 0x2c, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65,
	0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x6c, 0x6f, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x50, 0x00,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_accesslog_v1_accesslog_proto_rawDescOnce sync.Once
	file_accesslog_v1_accesslog_proto_rawDescData = file_accesslog_v1_accesslog_proto_rawDesc
)

func file_accesslog_v1_accesslog_proto_rawDescGZIP() []byte {
	file_accesslog_v1_accesslog_proto_rawDescOnce.Do(func() {
		file_accesslog_v1_accesslog_proto_rawDescData = protoimpl.X.CompressGZIP(file_accesslog_v1_accesslog_proto_rawDescData)
	})
	return file_accesslog_v1_accesslog_proto_rawDescData
}

var file_accesslog_v1_accesslog_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_accesslog_v1_accesslog_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_accesslog_v1_accesslog_proto_goTypes = []interface{}{
	(AccessLogReply_StatusCode)(0), // 0: api.accesslog.AccessLogReply.StatusCode
	(*AccessLogMsg)(nil),           // 1: api.accesslog.AccessLogMsg
	(*AccessLogMsgList)(nil),       // 2: api.accesslog.AccessLogMsgList
	(*AccessLogReply)(nil),         // 3: api.accesslog.AccessLogReply
	(*timestamppb.Timestamp)(nil),  // 4: google.protobuf.Timestamp
}
var file_accesslog_v1_accesslog_proto_depIdxs = []int32{
	4, // 0: api.accesslog.AccessLogMsg.access_time:type_name -> google.protobuf.Timestamp
	1, // 1: api.accesslog.AccessLogMsgList.access_log_msg:type_name -> api.accesslog.AccessLogMsg
	0, // 2: api.accesslog.AccessLogReply.status:type_name -> api.accesslog.AccessLogReply.StatusCode
	2, // 3: api.accesslog.AccessLogService.AccessLog:input_type -> api.accesslog.AccessLogMsgList
	3, // 4: api.accesslog.AccessLogService.AccessLog:output_type -> api.accesslog.AccessLogReply
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_accesslog_v1_accesslog_proto_init() }
func file_accesslog_v1_accesslog_proto_init() {
	if File_accesslog_v1_accesslog_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_accesslog_v1_accesslog_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessLogMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_accesslog_v1_accesslog_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessLogMsgList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_accesslog_v1_accesslog_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessLogReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_accesslog_v1_accesslog_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_accesslog_v1_accesslog_proto_goTypes,
		DependencyIndexes: file_accesslog_v1_accesslog_proto_depIdxs,
		EnumInfos:         file_accesslog_v1_accesslog_proto_enumTypes,
		MessageInfos:      file_accesslog_v1_accesslog_proto_msgTypes,
	}.Build()
	File_accesslog_v1_accesslog_proto = out.File
	file_accesslog_v1_accesslog_proto_rawDesc = nil
	file_accesslog_v1_accesslog_proto_goTypes = nil
	file_accesslog_v1_accesslog_proto_depIdxs = nil
}
