/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}System.register(["./index-legacy.b871e767.js","./index-browser-esm-legacy.6966c248.js"],(function(e,r){"use strict";var a,u,o,i,l,s,c,f,v,m,p,y,d,b,g,h,O,S,x,w,j,I,k,q,P,C,N=document.createElement("style");return N.textContent='@charset "UTF-8";.contextmenu{width:100px;margin:0;border:1px solid #ccc;background:#fff;z-index:3000;position:absolute;list-style-type:none;padding:5px 0;border-radius:4px;font-size:14px;color:#333;box-shadow:2px 2px 3px rgba(0,0,0,.2)}.el-tabs__item .el-icon-close{color:initial!important}.el-tabs__item .dot{content:"";width:9px;height:9px;margin-right:8px;display:inline-block;border-radius:50%;transition:background-color .2s}.contextmenu li{margin:0;padding:7px 16px}.contextmenu li:hover{background:#f2f2f2;cursor:pointer}\n',document.head.appendChild(N),{setters:[function(e){a=e.u,u=e.E,o=e.r,i=e.f,l=e.c,s=e.v,c=e.P,f=e.K,v=e.h,m=e.a,p=e.b,y=e.j,d=e.w,b=e.F,g=e.A,h=e.k,O=e.d,S=e.n,x=e.y,w=e.i,j=e.t,I=e.Z,k=e.O,q=e.I,P=e.J},function(e){C=e.J}],execute:function(){var r={class:"router-history"},N=["tab"];e("default",Object.assign({name:"HistoryComponent"},{setup:function(e){var J=a(),E=u(),V=function(e){return e.name+JSON.stringify(e.query)+JSON.stringify(e.params)},T=o([]),_=o(""),A=o(!1),D=i(),L=function(e){return e.name+JSON.stringify(e.query)+JSON.stringify(e.params)},R=o(0),z=o(0),F=o(!1),U=o(!1),$=o(""),H=l((function(){return C("$..defaultRouter[0]",D.userInfo)[0]||"dashboard"})),K=function(){T.value=[{name:H.value,meta:{title:"总览"},query:{},params:{}}],E.push({name:H.value}),A.value=!1,sessionStorage.setItem("historys",JSON.stringify(T.value))},X=function(){var e,t=T.value.findIndex((function(t){return V(t)===$.value&&(e=t),V(t)===$.value})),n=T.value.findIndex((function(e){return V(e)===_.value}));T.value.splice(0,t),t>n&&E.push(e),sessionStorage.setItem("historys",JSON.stringify(T.value))},Y=function(){var e,t=T.value.findIndex((function(t){return V(t)===$.value&&(e=t),V(t)===$.value})),n=T.value.findIndex((function(e){return V(e)===_.value}));T.value.splice(t+1,T.value.length),t<n&&E.push(e),sessionStorage.setItem("historys",JSON.stringify(T.value))},Z=function(){var e;T.value=T.value.filter((function(t){return V(t)===$.value&&(e=t),V(t)===$.value})),E.push(e),sessionStorage.setItem("historys",JSON.stringify(T.value))},B=function(e){if(!T.value.some((function(t){return function(e,t){if(e.name!==t.name)return!1;if(Object.keys(e.query).length!==Object.keys(t.query).length||Object.keys(e.params).length!==Object.keys(t.params).length)return!1;for(var n in e.query)if(e.query[n]!==t.query[n])return!1;for(var r in e.params)if(e.params[r]!==t.params[r])return!1;return!0}(t,e)}))){var r={};r.name=e.name,r.meta=function(e){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?t(Object(a),!0).forEach((function(t){n(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}({},e.meta),delete r.meta.matched,r.query=e.query,r.params=e.params,T.value.push(r)}window.sessionStorage.setItem("activeValue",V(e))},G=o({});s((function(){return T.value}),(function(){G.value={},T.value.forEach((function(e){G.value[V(e)]=e}))}));var M=function(e){var t=G.value[e];E.push({name:t.name,query:t.query,params:t.params})},Q=function(e){var t=T.value.findIndex((function(t){return V(t)===e}));V(J)===e&&(1===T.value.length?E.push({name:H.value}):t<T.value.length-1?E.push({name:T.value[t+1].name,query:T.value[t+1].query,params:T.value[t+1].params}):E.push({name:T.value[t-1].name,query:T.value[t-1].query,params:T.value[t-1].params})),T.value.splice(t,1)};s((function(){return A.value}),(function(){A.value?document.body.addEventListener("click",(function(){A.value=!1})):document.body.removeEventListener("click",(function(){A.value=!1}))})),s((function(){return J}),(function(e,t){"Login"!==e.name&&"Reload"!==e.name&&(T.value=T.value.filter((function(e){return!e.meta.closeTab})),B(e),sessionStorage.setItem("historys",JSON.stringify(T.value)),_.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),s((function(){return T.value}),(function(){sessionStorage.setItem("historys",JSON.stringify(T.value))}),{deep:!0});return function(){f.on("closeThisPage",(function(){Q(L(J))})),f.on("closeAllPage",(function(){K()})),f.on("mobile",(function(e){U.value=e})),f.on("collapse",(function(e){F.value=e}));var e=[{name:H.value,meta:{title:"总览"},query:{},params:{}}];T.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?_.value=window.sessionStorage.getItem("activeValue"):_.value=V(J),B(J),"true"===window.sessionStorage.getItem("needCloseAll")&&(K(),window.sessionStorage.removeItem("needCloseAll"))}(),c((function(){f.off("collapse"),f.off("mobile")})),function(e,t){var n=v("base-tab-pane"),a=v("base-tabs");return m(),p("div",r,[y(a,{modelValue:_.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return _.value=e}),closable:!(1===T.value.length&&e.$route.name===H.value),type:"card",onContextmenu:t[1]||(t[1]=k((function(e){return function(e){if(1===T.value.length&&J.name===H.value)return!1;var t,n="";(n="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id)&&(A.value=!0,t=F.value?54:220,U.value&&(t=0),R.value=e.clientX-t,z.value=e.clientY+10,$.value=n.substring(4))}(e)}),["prevent"])),onTabChange:M,onTabRemove:Q},{default:d((function(){return[(m(!0),p(b,null,g(T.value,(function(e){return m(),h(n,{key:L(e),label:e.meta.title,name:L(e),tab:e,class:"gva-tab"},{label:d((function(){return[O("span",{tab:e,style:S({color:_.value===L(e)?x(D).activeColor:"#333"})},[O("i",{class:"dot",style:S({backgroundColor:_.value===L(e)?x(D).activeColor:"#ddd"})},null,4),w(" "+j(x(I)(e.meta.title,e)),1)],12,N)]})),_:2},1032,["label","name","tab"])})),128))]})),_:1},8,["modelValue","closable"]),q(O("ul",{style:S({left:R.value+"px",top:z.value+"px"}),class:"contextmenu"},[O("li",{onClick:K},"关闭所有"),O("li",{onClick:X},"关闭左侧"),O("li",{onClick:Y},"关闭右侧"),O("li",{onClick:Z},"关闭其他")],4),[[P,A.value]])])}}}))}}}))}();
