package api

import (
	"asdsec.com/asec/platform/app/console/app/channel_type/dto"
	"asdsec.com/asec/platform/app/console/app/channel_type/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateChannelType godoc
// @Summary 新增通道类型
// @Schemes
// @Description 新增通道类型
// @Tags        ChannelType
// @Produce     application/json
// @Success     200
// @Router      /v1/channel [POST]
// @success     200 {object} common.Response{} "response"
func CreateChannelType(c *gin.Context) {
	var req dto.CreateChannelTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetChannelTypeService().CreateChannelType(c, req)
	if err != nil {
		global.SysLog.Error("create channel type err", zap.Error(err))
		common.FailAError(c, err)
		return
	}
	common.Ok(c)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		var maxType string
		if req.Pid == "0" {
			maxType = common.ChannelTYpe
		} else {
			maxType = common.ChannelDefineTYpe
		}
		oplog := modelTable.Oprlog{
			ResourceType:   maxType,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}

// UpdateChannelType godoc
// @Summary 修改通道类型
// @Schemes
// @Description 修改文件类型
// @Tags        ChannelType
// @Produce     application/json
// @Success     200
// @Router      /v1/channel [PUT]
// @success     200 {object} common.Response{} "response"
func UpdateChannelType(c *gin.Context) {
	var req dto.UpdateChannelTypeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetChannelTypeService().UpdateChannelType(c, req)
	if err != nil {
		global.SysLog.Error("create channel type err", zap.Error(err))
		common.FailAError(c, err)
		return
	}
	common.Ok(c)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		var maxType string
		if req.Pid == "0" {
			maxType = common.ChannelTYpe
		} else {
			maxType = common.ChannelDefineTYpe
		}
		oplog := modelTable.Oprlog{
			ResourceType:   maxType,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}

// GetChannelTypeList godoc
// @Summary 获取通道类型列表
// @Schemes
// @Description 获取通道类型列表
// @Tags        ChannelType
// @Produce     application/json
// @Success     200
// @Router      /v1/channel/type_list [GET]
// @success     200 {object} common.Response{} "response"
func GetChannelTypeList(c *gin.Context) {
	var req dto.GetChannelTypeListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetChannelTypeService().GetChannelTypeList(c, req)
	if err != nil {
		global.SysLog.Error("get channel type list err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// GetChannelList godoc
// @Summary 获取通道列表
// @Schemes
// @Description 获取通道列表
// @Tags        ChannelType
// @Produce     application/json
// @Success     200
// @Router      /v1/channel/list [POST]
// @success     200 {object} common.Response{} "response"
func GetChannelList(c *gin.Context) {
	var req dto.GetChannelListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	data, err := service.GetChannelTypeService().GetChannelList(c, req)
	if err != nil {
		global.SysLog.Error("get channel list err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, data)
}

// DeleteChannel godoc
// @Summary 删除通道
// @Schemes
// @Description 删除通道
// @Tags        ChannelType
// @Produce     application/json
// @Success     200
// @Router      /v1/channel [DELETE]
// @success     200 {object} common.Response{} "response"
func DeleteChannel(c *gin.Context) {
	var req dto.DeleteChannelReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err := service.GetChannelTypeService().DeleteChannel(c, req.Id)
	if err != nil {
		global.SysLog.Error("del channel type tree err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		var maxType string
		if req.Pid == "0" {
			maxType = common.ChannelTYpe
		} else {
			maxType = common.ChannelDefineTYpe
		}
		oplog := modelTable.Oprlog{
			ResourceType:   maxType,
			OperationType:  common.OperateDelete,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
}
