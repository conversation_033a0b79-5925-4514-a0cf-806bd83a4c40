package repository

import (
	"asdsec.com/asec/platform/app/console/app/domain_ssl/constants"
	"asdsec.com/asec/platform/app/console/app/domain_ssl/dto"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	"asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	"context"
	"crypto/ecdsa"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"strconv"
	"time"
)

type domainRepository struct {
}

func (d domainRepository) QueryDomainCertificateByName(ctx context.Context, name string) (dto.DomainCertificate, aerrors.AError) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.DomainCertificate{}, aerrors.NewWithError(err, common.OperateError)
	}
	var res dto.DomainCertificate
	err = db.Model(dto.DomainCertificate{}).Where("name = ?", name).Find(&res).Error
	if err != nil {
		return dto.DomainCertificate{}, aerrors.NewWithError(err, common.OperateError)
	}
	return res, nil
}

func (d domainRepository) GetDomainCertificateList(ctx context.Context, pagination model.Pagination) (dto.GetDomainCertificateListRsp, error) {
	if pagination.Search != "" {
		pagination.SearchColumns = []string{"name"}
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetDomainCertificateListRsp{}, err
	}
	db = db.Model(dto.DomainCertificate{}).
		Select("id,name,array_to_string(domain, ',') AS domain,TO_CHAR(expire_time,'YYYY-MM-DD HH24:MI:SS') AS expire_time,private_key,certificate")
	var rsp []dto.DomainListItem
	pagination, err = model.Paginate(&rsp, &pagination, db)
	if err != nil {
		return dto.GetDomainCertificateListRsp{}, err
	}
	return dto.GetDomainCertificateListRsp{
		CommonPage: model.CommonPage{CurrentPage: pagination.Page, PageSize: pagination.Limit, TotalNum: int(pagination.TotalRows)},
		Data:       rsp,
	}, nil
}

func (d domainRepository) GetCertificateDetail(ctx context.Context, id string) (dto.GetCertificateDetailRsp, error) {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return dto.GetCertificateDetailRsp{}, err
	}
	var res dto.GetCertificateDetailRsp
	err = db.Model(dto.DomainCertificate{}).
		Select("id,name,signature_algorithm,certificate_fingerprint,status,issue_agency,public_key_algorithm,"+
			"TO_CHAR(issue_time,'YYYY-MM-DD HH24:MI:SS') AS issue_time,"+
			"TO_CHAR(expire_time,'YYYY-MM-DD HH24:MI:SS') AS expire_time,"+
			"TO_CHAR(updated_at,'YYYY-MM-DD HH24:MI:SS') AS updated_at,"+
			"array_to_string(domain, ',') AS domain").
		Where("id = ?", id).Find(&res).Error
	if err != nil {
		return dto.GetCertificateDetailRsp{}, err
	}
	return res, nil
}

func parseCert(certStr string) (*x509.Certificate, error) {
	//解码PEM格式证书
	block, _ := pem.Decode([]byte(certStr))
	if block == nil {
		return nil, errors.New("failed to parse certificate PEM")
	}

	//解析证书内容
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, err
	}

	return cert, nil
}

func parsePrivateKey(cert *x509.Certificate, keyStr string) error {
	//解码PEM格式私钥
	block, _ := pem.Decode([]byte(keyStr))
	if block == nil {
		return errors.New("failed to parse private key PEM")
	}
	var err error
	//解析不同类型私钥
	switch cert.PublicKeyAlgorithm {
	case x509.ECDSA:
		_, err = x509.ParseECPrivateKey(block.Bytes)
	case x509.RSA:
		if block.Type == constants.RSAPrivateKeyType {
			_, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		} else if block.Type == constants.PrivateKeyType {
			_, err = x509.ParsePKCS8PrivateKey(block.Bytes)
		}
	default:
		err = errors.New("unknow algorithm")
	}
	return err
}

func (d domainRepository) AddDomainCertificate(ctx context.Context, req dto.CreateCertificateReq) (string, aerrors.AError) {
	// 证书解析
	cert, err := parseCert(req.Certificate)
	if err != nil {
		global.SysLog.Sugar().Errorf("parse certificate failed. err=%v", err)
		return "", aerrors.New("Verify Certificate Error", constants.VerifyCertificateError)
	}
	err = parsePrivateKey(cert, req.PrivateKey)
	if err != nil {
		global.SysLog.Sugar().Errorf("parse private key failed. err=%v", err)
		return "", aerrors.New("Verify Private Key Error", constants.VerifyCertificateError)
	}

	id, err := snowflake.Sf.GetId()
	if err != nil {
		return "", aerrors.NewWithError(err, common.OperateError)
	}
	status := constants.NotExpireCertStatus
	if cert.NotAfter.Before(time.Now()) {
		status = constants.ExpireCertStatus
	}
	var pubAlg string
	switch cert.PublicKey.(type) {
	case *rsa.PublicKey:
		pubAlg = constants.RsaAlgorithm
	case *ecdsa.PublicKey:
		pubAlg = constants.EcdsaAlgorithm
	}
	//指纹
	hash := sha1.Sum(cert.Raw)
	fingerprint := hex.EncodeToString(hash[:])

	idStr := strconv.FormatUint(id, 10)
	certificate := dto.DomainCertificate{
		Id:          idStr,
		Certificate: req.Certificate,
		PrivateKey:  req.PrivateKey,
		Name:        req.Name,
		//Domain:             cert.DNSNames,
		Domain:                 []string{cert.Subject.CommonName},
		IssueTime:              cert.NotBefore,
		ExpireTime:             cert.NotAfter,
		IssueAgency:            cert.Issuer.CommonName,
		SignatureAlgorithm:     cert.SignatureAlgorithm.String(),
		CertificateFingerprint: fingerprint,
		Status:                 status,
		PublicKeyAlgorithm:     pubAlg,
	}
	if len(cert.DNSNames) > 0 {
		certificate.Domain = cert.DNSNames
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return "", aerrors.NewWithError(err, common.OperateError)
	}

	err = db.Model(dto.DomainCertificate{}).Create(&certificate).Error

	if err != nil {
		return "", aerrors.NewWithError(err, common.OperateError)
	}

	return idStr, nil
}

func (d domainRepository) UpdateDomainCertificate(ctx context.Context, req dto.UpdateCertificateReq) (string, aerrors.AError) {
	// 证书解析
	cert, err := parseCert(req.Certificate)
	if err != nil {
		global.SysLog.Sugar().Errorf("parse certificate failed. err=%v", err)
		return "", aerrors.New("Verify Certificate Error", constants.VerifyCertificateError)
	}
	err = parsePrivateKey(cert, req.PrivateKey)
	if err != nil {
		global.SysLog.Sugar().Errorf("parse private key failed. err=%v", err)
		return "", aerrors.New("Verify Private Key Error", constants.VerifyCertificateError)
	}

	status := constants.NotExpireCertStatus
	if cert.NotAfter.Before(time.Now()) {
		status = constants.ExpireCertStatus
	}
	var pubAlg string
	switch cert.PublicKey.(type) {
	case *rsa.PublicKey:
		pubAlg = constants.RsaAlgorithm
	case *ecdsa.PublicKey:
		pubAlg = constants.EcdsaAlgorithm
	}
	//指纹
	hash := sha1.Sum(cert.Raw)
	fingerprint := hex.EncodeToString(hash[:])

	certificate := dto.DomainCertificate{
		Id:          req.Id,
		Certificate: req.Certificate,
		PrivateKey:  req.PrivateKey,
		Name:        req.Name,
		//Domain:             cert.DNSNames,
		Domain:                 []string{cert.Subject.CommonName},
		IssueTime:              cert.NotBefore,
		ExpireTime:             cert.NotAfter,
		IssueAgency:            cert.Issuer.CommonName,
		SignatureAlgorithm:     cert.SignatureAlgorithm.String(),
		CertificateFingerprint: fingerprint,
		Status:                 status,
		PublicKeyAlgorithm:     pubAlg,
		UpdatedAt:              time.Now(),
	}
	if len(cert.DNSNames) > 0 {
		certificate.Domain = cert.DNSNames
	}
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return "", aerrors.NewWithError(err, common.OperateError)
	}

	err = db.Model(dto.DomainCertificate{}).Where("id = ?", req.Id).Updates(&certificate).Error

	if err != nil {
		return "", aerrors.NewWithError(err, common.OperateError)
	}
	return req.Id, nil
}

func (d domainRepository) DelDomainCertificate(ctx context.Context, ids []string) aerrors.AError {
	db, err := global.GetDBClient(ctx)
	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	err = db.Where("id in ?", ids).Delete(dto.DomainCertificate{}).Error

	if err != nil {
		return aerrors.NewWithError(err, common.OperateError)
	}
	return nil
}

type DomainRepository interface {
	GetDomainCertificateList(ctx context.Context, pagination model.Pagination) (dto.GetDomainCertificateListRsp, error)
	GetCertificateDetail(ctx context.Context, id string) (dto.GetCertificateDetailRsp, error)
	AddDomainCertificate(ctx context.Context, req dto.CreateCertificateReq) (string, aerrors.AError)
	UpdateDomainCertificate(ctx context.Context, req dto.UpdateCertificateReq) (string, aerrors.AError)
	DelDomainCertificate(ctx context.Context, ids []string) aerrors.AError
	QueryDomainCertificateByName(ctx context.Context, name string) (dto.DomainCertificate, aerrors.AError)
}

func NewDomainRepository() DomainRepository {
	return domainRepository{}
}
