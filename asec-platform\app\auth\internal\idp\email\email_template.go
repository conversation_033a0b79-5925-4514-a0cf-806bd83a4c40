package email

const (
	// 现有常量...

	// 内置邮件模板
	DefaultTemplateType = "default"

	// 默认模板 - 简洁现代风格
	DefaultHTMLTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>验证码</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; padding: 20px 0; }
        .code-container { background-color: #f7f9fa; border-radius: 8px; padding: 30px; text-align: center; margin: 20px 0; }
        .verification-code { font-size: 32px; font-weight: bold; letter-spacing: 5px; color: #2c3e50; }
        .message { text-align: center; margin: 20px 0; font-size: 14px; color: #7f8c8d; }
        .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #95a5a6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>身份验证</h2>
        </div>
        <div class="code-container">
            <p>您的验证码是</p>
            <div class="verification-code">{code}</div>
            <p>有效期 {minutes} 分钟</p>
        </div>
        <div class="message">
            {message}
        </div>
        <div class="footer">
            此邮件由系统自动发送，请勿回复
        </div>
    </div>
</body>
</html>
`

	// 蓝色主题模板
	BlueHTMLTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>验证码</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f9f9f9; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); }
        .header { background-color: #3498db; padding: 20px; text-align: center; color: white; }
        .content { padding: 30px; }
        .code-box { background-color: #f1f8fe; border: 1px dashed #3498db; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }
        .verification-code { font-size: 38px; font-weight: bold; letter-spacing: 8px; color: #3498db; }
        .message { text-align: center; margin: 20px 0; color: #555; line-height: 1.8; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; color: #999; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>安全验证</h1>
        </div>
        <div class="content">
            <h2 style="text-align: center; color: #3498db;">您的验证码</h2>
            <div class="code-box">
                <div class="verification-code">{code}</div>
                <p>此验证码 {minutes} 分钟内有效</p>
            </div>
            <div class="message">
                {message}
            </div>
        </div>
        <div class="footer">
            此邮件由系统自动发送，请勿回复<br>
            安全提示：请勿将验证码提供给他人
        </div>
    </div>
</body>
</html>
`

	// 企业风格模板
	CorporateHTMLTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>验证码</title>
    <style>
        body { font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 650px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e0e0e0; }
        .header { background-color: #2c3e50; padding: 15px; text-align: center; }
        .logo { font-size: 24px; font-weight: bold; color: white; letter-spacing: 1px; }
        .content { padding: 30px 40px; }
        .code-section { margin: 25px 0; padding: 20px; background-color: #f8f9fa; border-left: 4px solid #2c3e50; }
        .verification-code { font-size: 32px; font-weight: bold; color: #2c3e50; text-align: center; letter-spacing: 5px; }
        .message { margin: 20px 0; color: #555; }
        .note { margin-top: 30px; padding-top: 15px; border-top: 1px solid #e0e0e0; font-size: 14px; color: #777; }
        .footer { background-color: #f5f5f5; padding: 15px; text-align: center; color: #999; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">安全认证</div>
        </div>
        <div class="content">
            <h2>验证码通知</h2>
            <p>尊敬的用户：</p>
            <p>您正在进行身份验证操作，请在相应页面输入以下验证码完成验证：</p>
            <div class="code-section">
                <div class="verification-code">{code}</div>
                <p style="text-align: center;">验证码有效期 {minutes} 分钟</p>
            </div>
            <div class="message">
                {message}
            </div>
            <div class="note">
                <p>安全提示：</p>
                <ul>
                    <li>请勿将验证码泄露给他人</li>
                    <li>系统不会以任何理由向您索要验证码</li>
                    <li>如非本人操作，请忽略此邮件</li>
                </ul>
            </div>
        </div>
        <div class="footer">
            此邮件由系统自动发送，请勿回复
        </div>
    </div>
</body>
</html>
`

	// 简约风格模板
	SimpleHTMLTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>验证码</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; margin: 0; padding: 40px 0; background: #f5f5f5; color: #333; }
        .container { max-width: 500px; margin: 0 auto; background: #fff; border-radius: 4px; padding: 30px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08); }
        .title { font-size: 22px; font-weight: 500; color: #333; margin-top: 0; margin-bottom: 25px; text-align: center; }
        .code { font-size: 28px; letter-spacing: 6px; font-weight: 600; background: #f7f7f7; padding: 15px; border-radius: 4px; text-align: center; margin: 20px 0; }
        .message { font-size: 14px; line-height: 1.6; margin: 20px 0; color: #555; }
        .expiry { font-size: 14px; color: #888; margin-top: 15px; text-align: center; }
        .footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #eee; font-size: 12px; color: #999; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">您的验证码</h1>
        <div class="code">{code}</div>
        <p class="expiry">此验证码将于 {minutes} 分钟后过期</p>
        <div class="message">
            {message}
        </div>
        <div class="footer">
            请勿将验证码泄露给他人<br>
            此邮件由系统自动发送
        </div>
    </div>
</body>
</html>
`
)
