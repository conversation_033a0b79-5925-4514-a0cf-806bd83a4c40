package app_config_sync

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	v1 "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/constants"
	"asdsec.com/asec/platform/pkg/types"
	"github.com/jinzhu/copier"
)

func getConfigFilePath() string {
	return constants.GetAppConfigPath()
}

var (
	defaultPullInterval = time.Minute
	pullMutex           sync.Mutex
	ticker              *time.Ticker
	stopChan            chan struct{}
)

func Start() {
	if ticker != nil {
		ticker.Stop()
	}
	// TODO: get interval from config
	ticker = time.NewTicker(defaultPullInterval)
	stopChan = make(chan struct{})
	go func() {
		global.Logger.Sugar().Info("Starting application config sync...")
		fetchAndSaveAppConfig() // run once immediately

		for {
			select {
			case <-ticker.C:
				fetchAndSaveAppConfig()
			case <-stopChan:
				ticker.Stop()
				global.Logger.Sugar().Info("Stopping application config sync.")
				return
			}
		}
	}()
}

func Stop() {
	if stopChan != nil {
		close(stopChan)
	}
}

func fetchAndSaveAppConfig() {
	pullMutex.Lock()
	defer pullMutex.Unlock()

	global.Logger.Sugar().Debug("Fetching application config from platform...")

	userId := ""
	publicIp := ""
	privateIp := ""
	ssid := ""
	dns := ""

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	conn, err := connection.GetPlatformConnection(ctx)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to get platform connection: %v", err)
		return
	}
	defer conn.Close()

	client := v1.NewGetAppClient(conn)
	req := v1.AgentGetAppReq{
		UserId:    userId,
		PublicIp:  publicIp,
		PrivateIp: []string{privateIp},
		Ssid:      ssid,
		Dns:       []string{dns},
	}

	appResp, err := client.AgentGetApp(ctx, &req)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to get app config from platform: %v", err)
		// 可以考虑添加重试逻辑或者保持现有配置文件不变
		return
	}

	var seList []types.SeList
	var appNetInfo []types.AppNetInfo
	copier.Copy(&seList, appResp.GetSe())
	copier.Copy(&appNetInfo, appResp.GetApps())

	config := types.SyncedConfig{
		AppNetInfo:   appNetInfo,
		SeList:       seList,
		FakeIpRange:  appResp.GetFakeIpRange(),
		DnsConfig:    appResp.GetDnsConfig(),
		LastSyncTime: time.Now(),
	}

	// 验证配置有效性
	if err := validateConfig(config); err != nil {
		global.Logger.Sugar().Errorf("Invalid config received from platform: %v", err)
		return
	}

	if err := writeConfigToFile(config); err != nil {
		global.Logger.Sugar().Errorf("Failed to write app config to file: %v", err)
	} else {
		global.Logger.Sugar().Infof("Successfully fetched and saved application config to %s", getConfigFilePath())
	}
}

func writeConfigToFile(config types.SyncedConfig) error {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	// 检查文件是否存在，如果存在则比较内容
	configFilePath := getConfigFilePath()
	if stat, err := os.Stat(configFilePath); err == nil {
		// 检查文件修改时间，如果文件是在最近30秒内修改的，跳过写入
		// 这样可以避免频繁的无意义写入
		if time.Since(stat.ModTime()) < 30*time.Second {
			global.Logger.Sugar().Debug("Configuration file was recently modified, skipping write")
			return nil
		}

		// 文件存在，读取现有内容进行比较
		existingData, err := os.ReadFile(configFilePath)
		if err != nil {
			global.Logger.Sugar().Warnf("Failed to read existing config file: %v", err)
		} else {
			// 解析现有配置
			var existingConfig types.SyncedConfig
			if err := json.Unmarshal(existingData, &existingConfig); err != nil {
				global.Logger.Sugar().Warnf("Failed to parse existing config file: %v", err)
			} else {
				// 比较配置内容（忽略时间字段）
				if configsEqual(config, existingConfig) {
					global.Logger.Sugar().Debug("Configuration unchanged, skipping file write")
					return nil
				} else {
					global.Logger.Sugar().Debugf("Configuration changed - AppNetInfo: %d vs %d, SeList: %d vs %d, FakeIpRange: %s vs %s",
						len(config.AppNetInfo), len(existingConfig.AppNetInfo),
						len(config.SeList), len(existingConfig.SeList),
						config.FakeIpRange, existingConfig.FakeIpRange)
				}
			}
		}
	}

	dir := filepath.Dir(configFilePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 原子写入：先写临时文件，再重命名
	tempFile := configFilePath + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return err
	}

	if err := os.Rename(tempFile, configFilePath); err != nil {
		return err
	}

	global.Logger.Sugar().Debug("Configuration file updated successfully")
	return nil
}

// configsEqual 比较两个配置是否相同（忽略时间字段）
func configsEqual(a, b types.SyncedConfig) bool {
	// 比较 AppNetInfo
	if len(a.AppNetInfo) != len(b.AppNetInfo) {
		global.Logger.Sugar().Debugf("AppNetInfo length differs: %d vs %d", len(a.AppNetInfo), len(b.AppNetInfo))
		return false
	}
	for i, app := range a.AppNetInfo {
		if app != b.AppNetInfo[i] {
			global.Logger.Sugar().Debugf("AppNetInfo[%d] differs: %+v vs %+v", i, app, b.AppNetInfo[i])
			return false
		}
	}

	// 比较 SeList
	if len(a.SeList) != len(b.SeList) {
		global.Logger.Sugar().Debugf("SeList length differs: %d vs %d", len(a.SeList), len(b.SeList))
		return false
	}
	for i, se := range a.SeList {
		if se != b.SeList[i] {
			global.Logger.Sugar().Debugf("SeList[%d] differs: %+v vs %+v", i, se, b.SeList[i])
			return false
		}
	}

	// 比较其他字段
	if a.FakeIpRange != b.FakeIpRange {
		global.Logger.Sugar().Debugf("FakeIpRange differs: %s vs %s", a.FakeIpRange, b.FakeIpRange)
		return false
	}

	if !dnsConfigsEqual(a.DnsConfig, b.DnsConfig) {
		global.Logger.Sugar().Debugf("DnsConfig differs")
		return false
	}

	return true
}

// dnsConfigsEqual 比较DNS配置是否相同
func dnsConfigsEqual(a, b *v1.DNSConfig) bool {
	if a == nil && b == nil {
		return true
	}
	if a == nil || b == nil {
		return false
	}

	// 比较主要字段
	if a.PrimaryDns != b.PrimaryDns ||
		a.SecondaryDns != b.SecondaryDns ||
		a.FallbackDns != b.FallbackDns {
		return false
	}

	// 比较切片字段
	if !stringSlicesEqual(a.FallbackDomains, b.FallbackDomains) ||
		!stringSlicesEqual(a.FakeIpExcludeDomains, b.FakeIpExcludeDomains) {
		return false
	}

	// 比较 NameserverPolicies
	if !nameserverPoliciesEqual(a.NameserverPolicies, b.NameserverPolicies) {
		return false
	}

	return true
}

// nameserverPoliciesEqual 比较 NameserverPolicy 切片是否相等
func nameserverPoliciesEqual(a, b []*v1.NameserverPolicy) bool {
	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i].Nameserver != b[i].Nameserver {
			return false
		}
		if !stringSlicesEqual(a[i].Domains, b[i].Domains) {
			return false
		}
	}

	return true
}

// stringSlicesEqual 比较字符串切片是否相等
func stringSlicesEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

// validateConfig 验证配置的有效性
func validateConfig(config types.SyncedConfig) error {
	// 验证 SeList 中的必要字段
	for _, se := range config.SeList {
		if se.SeId == 0 {
			return fmt.Errorf("invalid SeId: %d", se.SeId)
		}
		if se.SeIp == "" {
			return fmt.Errorf("empty SeIp for SeId: %d", se.SeId)
		}
		if se.SePort == 0 {
			return fmt.Errorf("invalid SePort: %d for SeId: %d", se.SePort, se.SeId)
		}
	}

	// 验证 AppNetInfo 中的必要字段
	for _, app := range config.AppNetInfo {
		if app.Address == "" {
			continue // 空地址是允许的
		}
		if app.SeId == 0 {
			continue // SeId为0表示需要清理的IP，是允许的
		}
	}

	return nil
}
