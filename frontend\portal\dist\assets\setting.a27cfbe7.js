/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import{_ as e,r as a,f as l,o as t,S as s,C as o,M as n,v as u,h as i,a as r,b as c,d as v,e as g,j as d,l as p,w as m,t as y,U as b,G as f,L as h,i as w}from"./index.a794166c.js";const x={class:"setting-page"},k={class:"main-content"},C={class:"setting-container"},A={class:"tabs-header"},_={class:"tabs-content"},T={key:0,class:"tab-panel"},U={key:0,class:"loading-placeholder"},V={key:1,class:"setting-section"},S={class:"setting-item setting-platformAddress"},B={style:{display:"flex","align-items":"center"}},E={key:0,style:{"margin-top":"12px",width:"320px"}},$={class:"spa-code-input-wrapper"},I={key:0,d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",stroke:"currentColor","stroke-width":"2"},P={key:1,cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2"},M={key:2,d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24",stroke:"currentColor","stroke-width":"2"},j={key:3,stroke:"currentColor","stroke-width":"2",x1:"1",x2:"23",y1:"1",y2:"23"},L={class:"setting-item"},q={class:"checkbox-group"},z={key:1,class:"tab-panel"},F={key:0,class:"loading-placeholder"},G={key:1},H={class:"setting-section setting-update"},R={class:"setting-item"},W={class:"checkbox-group"},D={key:0,class:"setting-item"},J={class:"setting-section"},K={class:"setting-item",style:{"margin-bottom":"8px"}},N={class:"version-item"},O={class:"version-value-group"},Q={class:"version-value",style:{"margin-left":"68px"}},X={class:"version-item"},Y={class:"version-value",style:{"margin-left":"68px"}},Z={key:0,class:"version-item"},ee={class:"version-value",style:{"margin-left":"40px"}},ae=e({__name:"setting",setup(e){const ae=a("general"),le=a({}),te=a(""),se=l(),oe=a(""),ne=a(!1),ue=a(!1),ie=a(!0),re=a(!0),ce=a("daily"),ve=a(!1),ge=a(""),de=a(""),pe=a(!1),me=a(!1),ye=a(""),be=a(""),fe=a(""),he=a(0),we=a(null),xe=a(!0);t((async()=>{await ke(),await s(),xe.value=!1}));const ke=async()=>{try{logger.log("开始加载客户端设置...");const e=await o.getClientConfig();if(logger.log("加载到的设置:",e),e){te.value=e.ServerUrl||"",oe.value=e.ServerUrl||"",ue.value=e.IsAutoStartEnabled||!1,ie.value=void 0===e.AutoConnectAfterStartup||e.AutoConnectAfterStartup,re.value=void 0===e.IsAutoUpdateEnabled||e.IsAutoUpdateEnabled;const a=["1800","3600","86400","604800","2592000"],l=e.UpdateFrequency||"";ce.value=a.includes(l)?l:"1800",ye.value=e.CurrentVersion||"",be.value=e.BuildTimestamp||"",fe.value=e.LastUpdatedTime||"",le.value=e,ge.value=e.activation_code||"",de.value=ge.value,ve.value=!!ge.value}logger.log("设置加载完成")}catch(e){console.error("加载设置失败:",e),n.warning("加载设置失败，使用默认设置")}},Ce=async(e=!1)=>{if(!xe.value)if(e||!ve.value||/^[0-9]{6}$/.test(ge.value))try{const e=le.value;e.IsAutoStartEnabled=ue.value,e.AutoConnectAfterStartup=ie.value,e.IsAutoUpdateEnabled=re.value,e.UpdateFrequency=ce.value,e.activation_code=ve.value?ge.value:"",logger.log("保存其他设置:",e),await o.setClientConfig(e),logger.log("其他设置保存成功"),n.success("设置已保存")}catch(a){console.error("保存其他设置失败:",a),n.error("保存设置失败，请重试")}else n.error("请输入6位数字访问码")},Ae=async()=>{if(!ne.value&&te.value!==oe.value){ne.value=!0;try{if(!(await b(te.value)))throw new Error("服务器地址格式错误");await f.confirm(`确定要将平台地址修改为：${te.value} 吗？`,"确认修改平台地址",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"});const t=h.service({fullscreen:!0,text:"正在连接服务器..."});try{const l=new URL(te.value),s=`${l.protocol}//${l.host}`;if(te.value=s,ve.value)console.log("SPA模式已启用，跳过连接验证，直接保存配置"),t.updateText("正在保存配置..."),await _e(),t.close(),n.success("服务器地址已保存（SPA模式）");else{t.updateText("正在验证服务器连接...");try{const e=new AbortController,a=setTimeout((()=>{e.abort()}),5e3),l=await fetch(`${te.value}/auth/login/v1/user/main_idp/list`,{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal});if(clearTimeout(a),!l.ok||200!==l.status)throw new Error(`服务器响应错误: ${l.status}`);t.updateText("正在保存配置..."),await _e(),t.close(),n.success("服务器连接成功！")}catch(e){console.warn("服务器连接测试失败:",e),t.close();let l="连接测试失败";"AbortError"===e.name?l="连接超时，请检查网络或服务器状态":"TypeError"===e.name&&(l="网络错误，请检查服务器地址是否正确");try{await f.confirm(`${l}，是否仍要保存此服务器地址？\n\n注意：前端将通过本地代理访问，后台会处理实际连接。`,"连接测试失败",{type:"warning",confirmButtonText:"仍要保存",cancelButtonText:"取消"}),console.log("用户选择保存服务器地址（将由后台代理使用）:",te.value),await _e(),n.success("服务器地址已保存（将由后台代理处理连接）")}catch(a){throw new Error("已取消保存服务器地址")}}}}catch(l){t&&t.close(),te.value=oe.value,n.error(l.message||"配置服务器地址失败")}}catch(l){logger.log("用户取消修改平台地址，还原原值"),te.value=oe.value,"cancel"===l?n.info("已取消修改"):n.error(l.message)}finally{ne.value=!1}}},_e=async()=>{try{const e=le.value;e.ServerUrl=te.value,logger.log("保存平台地址:",e),await o.setClientConfig(e),oe.value=te.value,globalUrlHashParams.set("WebUrl",te.value),logger.log("平台地址保存成功"),n.success("平台地址已保存")}catch(e){throw console.error("保存平台地址失败:",e),te.value=oe.value,new Error("保存平台地址失败，请重试")}},Te=async()=>{if(ve.value)try{await f.confirm("确定要关闭访问码吗？这将清空当前的访问码配置。","确认关闭访问码",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"}),ve.value=!ve.value}catch(e){logger.log("用户取消")}else ve.value=!ve.value,pe.value=!0},Ue=e=>{const a=e.replace(/[^0-9]/g,"");a!==e&&(ge.value=a)},Ve=()=>{pe.value=!pe.value},Se=async()=>{if(me.value)return;if(ge.value===de.value)return;me.value=!0;const e=!de.value&&ve.value;try{if(!/^[0-9]{6}$/.test(ge.value))return n.error("请输入6位数字访问码"),void(ge.value=de.value);e?(le.value.activation_code=ge.value,await o.setClientConfig(le.value),de.value=ge.value,n.success("访问码已保存"),await se.ClearStorage(),console.log("访问码已保存，用户状态已刷新")):(await f.confirm(`确定要将访问码修改为：${ge.value} 吗？`,"确认修改访问码",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"}),le.value.activation_code=ge.value,await o.setClientConfig(le.value),de.value=ge.value,n.success("访问码已保存"),await se.ClearStorage(),console.log("访问码已修改，用户状态已刷新")),pe.value=!1}catch(a){ge.value=de.value,"cancel"===a?n.info("已取消修改"):n.error(a.message||"保存访问码失败")}finally{me.value=!1}};u(ve,(async e=>{xe.value||(!e&&de.value?(ge.value="",de.value="",await Ce(),n.success("访问码已关闭"),await se.ClearStorage(),logger.log("访问码已关闭，用户状态已刷新")):e&&(ge.value?await Ce():n.info("请在下方输入6位数字访问码，输入完成后会自动保存")))}));const Be=()=>{if(logger.log("构建时间被点击"),he.value++,1===he.value&&(we.value=setTimeout((()=>{he.value=0,we.value=null,logger.log("构建时间点击计数已重置")}),1e3)),he.value>=5){logger.log("检测到1秒内连续点击构建时间，调用 agentApi.openAsecPage"),we.value&&(clearTimeout(we.value),we.value=null),he.value=0;try{o.openAsecPage("http://127.0.0.1:19998/"),logger.log("成功调用 agentApi.openAsecPage")}catch(e){logger.error("调用 agentApi.openAsecPage 失败:",e)}}},Ee=async()=>{try{n.info("正在检查更新..."),setTimeout((()=>{n.success("当前已是最新版本")}),1e3)}catch(e){console.error("检查更新失败:",e),n.error("检查更新失败，请稍后重试")}};return u([ue,ie,re,ce],(()=>{xe.value||(logger.log("检测到其他设置变化，立即保存..."),Ce(!0))}),{deep:!0}),(e,a)=>{const l=i("base-input"),t=i("base-checkbox"),s=i("base-option"),o=i("base-select"),n=i("base-button");return r(),c("div",x,[v("div",k,[v("div",C,[v("div",A,[v("div",{class:g(["tab-item",{active:"general"===ae.value}]),onClick:a[0]||(a[0]=e=>ae.value="general")},a[8]||(a[8]=[v("span",{class:"tab-item-text"},"通用设置",-1)]),2),v("div",{class:g(["tab-item",{active:"version"===ae.value}]),onClick:a[1]||(a[1]=e=>ae.value="version")},a[9]||(a[9]=[v("span",{class:"tab-item-text"},"版本信息",-1)]),2)]),v("div",_,["general"===ae.value?(r(),c("div",T,[xe.value?(r(),c("div",U)):(r(),c("div",V,[v("div",S,[a[11]||(a[11]=v("label",{class:"setting-label"},"平台地址",-1)),v("div",B,[d(l,{modelValue:te.value,"onUpdate:modelValue":a[2]||(a[2]=e=>te.value=e),placeholder:"输入您连接的平台服务器地址",class:"setting-input",clearable:"",onBlur:Ae},null,8,["modelValue"]),v("span",{class:"spa-label",onClick:Te},a[10]||(a[10]=[v("svg",{class:"icon","aria-hidden":"true"},[v("use",{"xlink:href":"#icon-spa"})],-1)]))]),ve.value?(r(),c("div",E,[v("div",$,[d(l,{modelValue:ge.value,"onUpdate:modelValue":a[3]||(a[3]=e=>ge.value=e),type:pe.value?"text":"password",maxlength:"6",placeholder:"请输入6位数字访问码",class:"setting-input spa-code-input",onBlur:Se,onInput:Ue},null,8,["modelValue","type"]),(r(),c("svg",{class:"input-icon-right spa-code-toggle",fill:"none",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",onClick:Ve},[pe.value?p("",!0):(r(),c("path",I)),pe.value?p("",!0):(r(),c("circle",P)),pe.value?(r(),c("path",M)):p("",!0),pe.value?(r(),c("line",j)):p("",!0)]))])])):p("",!0)]),v("div",L,[a[14]||(a[14]=v("label",{class:"setting-label"},"启动选项",-1)),v("div",q,[d(t,{modelValue:ue.value,"onUpdate:modelValue":a[4]||(a[4]=e=>ue.value=e),class:"setting-checkbox"},{default:m((()=>a[12]||(a[12]=[w(" 开机自启动 ")]))),_:1,__:[12]},8,["modelValue"]),d(t,{modelValue:ie.value,"onUpdate:modelValue":a[5]||(a[5]=e=>ie.value=e),class:"setting-checkbox"},{default:m((()=>a[13]||(a[13]=[w(" 启动后自动连接 ")]))),_:1,__:[13]},8,["modelValue"])])])]))])):p("",!0),"version"===ae.value?(r(),c("div",z,[xe.value?(r(),c("div",F)):(r(),c("div",G,[v("div",H,[v("div",R,[a[16]||(a[16]=v("label",{class:"setting-label"},"更新选项",-1)),v("div",W,[d(t,{modelValue:re.value,"onUpdate:modelValue":a[6]||(a[6]=e=>re.value=e),class:"setting-checkbox"},{default:m((()=>a[15]||(a[15]=[w(" 自动检查更新 ")]))),_:1,__:[15]},8,["modelValue"])])]),re.value?(r(),c("div",D,[a[17]||(a[17]=v("label",{class:"setting-label"},"更新检查频率",-1)),d(o,{modelValue:ce.value,"onUpdate:modelValue":a[7]||(a[7]=e=>ce.value=e),class:"setting-select",placeholder:"请选择"},{default:m((()=>[d(s,{label:"每30分钟",value:"1800"}),d(s,{label:"每小时",value:"3600"}),d(s,{label:"每天",value:"86400"}),d(s,{label:"每周",value:"604800"}),d(s,{label:"每月",value:"2592000"})])),_:1},8,["modelValue"])])):p("",!0)]),v("div",J,[v("div",K,[a[22]||(a[22]=v("label",{class:"setting-label"},"关于安全客户端",-1)),v("div",N,[a[19]||(a[19]=v("span",{class:"version-label"},"当前版本",-1)),v("div",O,[v("span",Q,y(ye.value),1),d(n,{class:"version-update-button",style:{display:"none"},text:"",type:"primary",size:"small",onClick:Ee},{default:m((()=>a[18]||(a[18]=[w(" 检查更新 ")]))),_:1,__:[18]})])]),v("div",X,[a[20]||(a[20]=v("span",{class:"version-label"},"构建时间",-1)),v("span",Y,y(be.value),1),v("span",{class:"version-value",style:{width:"50px",height:"20px"},onClick:Be})]),""!==fe.value?(r(),c("div",Z,[a[21]||(a[21]=v("span",{class:"version-label"},"上次更新时间",-1)),v("span",ee,y(fe.value),1)])):p("",!0)]),a[23]||(a[23]=v("div",{class:"copyright"},[v("p",null,"© 2025 Security Systems Inc. 保留所有权利")],-1))])]))])):p("",!0)])])])])}}},[["__scopeId","data-v-a035f85e"]]);export{ae as default};
