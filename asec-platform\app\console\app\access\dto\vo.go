package dto

import (
	"asdsec.com/asec/platform/pkg/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/lib/pq"
	"gorm.io/datatypes"
	"strings"
)

type CreateStrategyReq struct {
	// 策略名称
	StrategyName string `json:"strategy_name" binding:"required"`
	// 策略状态 1启用 2禁用
	StrategyStatus int `json:"strategy_status" binding:"required,min=1,max=2"`
	// 策略描述
	StrategyDetail string `json:"strategy_detail"`
	// 用户ids
	UserIds []string `json:"user_ids,omitempty"`
	// 用户分组ids
	UserGroupIds []string `json:"user_group_ids,omitempty"`
	// 应用ids
	AppIds []string `json:"app_ids,omitempty"`
	// 应用分组ids
	AppGroupIds []string `json:"app_group_ids,omitempty"`
	//角色ids
	RoleIds []string `json:"role_ids,omitempty"`
	// 生效时间起 15:04:05
	StartTime string `json:"start_time,omitempty"`
	// 生效时间止 15:04:05
	EndTime string `json:"end_time,omitempty"`
	// 是否启用日志 1启用 2禁用
	EnableLog int `json:"enable_log" binding:"required,min=1,max=2"`
	// 是否启用全部用户 1是 2否
	EnableAllUser int `json:"enable_all_user" binding:"min=0,max=2"`
	// 是否启用全部应用 1是 2否
	EnableAllApp int `json:"enable_all_app" binding:"min=0,max=2"`
}

type StrategyListReq struct {
	Limit  int    `form:"limit" json:"limit" binding:"required,min=1,max=1000"`
	Offset int    `form:"offset" json:"offset" binding:"min=0"`
	Search string `form:"search"  json:"search"`
}

type StrategyListResp struct {
	CurrentPage      int                `json:"current_page"`
	PageSize         int                `json:"page_size"`
	TotalNum         int                `json:"total_num"`
	StrategyListData []StrategyListData `json:"strategy_list_data"`
}

type StrategyListData struct {
	model.TenantModel
	// 策略名称
	StrategyName string `json:"strategy_name"`
	// 策略状态 1启用 2禁用
	StrategyStatus int `json:"strategy_status"`
	// 策略描述
	StrategyDetail string `json:"strategy_detail"`
	// 用户信息
	UserInStrategy []UserInStrategy `json:"user_in_strategy" gorm:"-"`
	// 用户分组信息
	UserGroupInStrategy []UserGroupInStrategy `json:"user_group_in_strategy" gorm:"-"`
	// 应用信息
	AppInStrategy []AppInStrategy `json:"app_in_strategy" gorm:"-"`
	//角色信息
	RolesInStrategy []RoleInStrategy `json:"roles_in_strategy" gorm:"-"`
	// 应用分组信息
	AppGroupInStrategy []AppGroupInStrategy `json:"app_group_in_strategy" gorm:"-"`
	// 生效时间起
	StartTime string `json:"start_time"`
	// 生效时间止
	EndTime string `json:"end_time"`
	// 是否启用日志 1启用 2禁用
	EnableLog    int                 `json:"enable_log"`
	UserInfo     jsoniter.RawMessage `gorm:"column:user_info" json:"-"`
	GroupInfo    jsoniter.RawMessage `gorm:"column:group_info" json:"-"`
	AppInfo      jsoniter.RawMessage `gorm:"column:app_info" json:"-"`
	RoleInfo     jsoniter.RawMessage `gorm:"column:role_info" json:"-"`
	AppGroupInfo jsoniter.RawMessage `gorm:"column:app_group_info" json:"-"`
	// 是否启用全部用户 1是 2否
	EnableAllUser int `json:"enable_all_user"`
	// 是否启用全部应用 1是 2否
	EnableAllApp int `json:"enable_all_app"`
}

func (s *StrategyListData) UnmarshalUserGroupInStrategy() error {
	var strategies []UserGroupInStrategy
	err := jsoniter.Unmarshal(s.GroupInfo, &strategies)
	s.UserGroupInStrategy = strategies
	return err
}
func (s *StrategyListData) UnmarshalRoleInStrategy() error {
	var roles []RoleInStrategy
	err := jsoniter.Unmarshal(s.RoleInfo, &roles)
	s.RolesInStrategy = roles
	return err
}

func (s *StrategyListData) UnmarshalAppGroupInStrategy() error {
	var strategies []AppGroupInStrategy
	err := jsoniter.Unmarshal(s.AppGroupInfo, &strategies)
	s.AppGroupInStrategy = strategies
	return err
}

func (s *StrategyListData) UnmarshalAppInStrategy() error {
	var strategies []AppInStrategy
	err := jsoniter.Unmarshal(s.AppInfo, &strategies)
	s.AppInStrategy = strategies
	return err
}

func (s *StrategyListData) UnmarshalUserInStrategy() error {
	var strategies []UserInStrategy
	err := jsoniter.Unmarshal(s.UserInfo, &strategies)
	s.UserInStrategy = strategies
	return err
}

type UserInStrategy struct {
	UserName string `json:"user_name"`
	UserId   string `json:"user_id"`
	Type     string `json:"type"`
}

type UserGroupInStrategy struct {
	UserGroupName string `json:"user_group_name"`
	UserGroupId   string `json:"user_group_id"`
	Type          string `json:"type"`
}

type RoleInStrategy struct {
	RoleId   string `json:"role_id"`
	RoleName string `json:"role_name"`
	Type     string `json:"type"`
}

type AppInStrategy struct {
	AppName string `json:"app_name"`
	AppId   string `json:"app_id"`
	Type    string `json:"type"`
}

type AppGroupInStrategy struct {
	AppGroupName string `json:"app_group_name"`
	AppGroupId   string `json:"app_group_id"`
	Type         string `json:"type"`
}

type StrategyUpdateReq struct {
	Id uint64 `json:"id,string" binding:"required"`
	CreateStrategyReq
}

type NullInt64Array struct {
	Int64s []int64
	Valid  bool
}

func (n *NullInt64Array) Scan(value interface{}) error {
	vS := value.(string)
	if strings.Contains(vS, "NULL") || vS == "" || value == nil {
		n.Int64s, n.Valid = nil, false
		return nil
	}
	n.Valid = true
	return pq.Array(&n.Int64s).Scan(value)
}

type NullStringArray struct {
	Strings []string
	Valid   bool
}

func (n *NullStringArray) Scan(value interface{}) error {
	vS := value.(string)
	if strings.Contains(vS, "NULL") || vS == "" || value == nil {
		n.Strings, n.Valid = nil, false
		return nil
	}
	n.Valid = true
	return pq.Array(&n.Strings).Scan(value)
}

type StrategyEnableReq struct {
	Id     []string `json:"id" binding:"required"`
	Status int      `json:"status" binding:"required,min=1,max=2"`
}

type StrategyDelReq struct {
	Id []string `json:"id" binding:"required"`
}

type ListAccessLogReq struct {
	model.Pagination
	// 生效时间起
	StartTime string `json:"start_time"`
	// 生效时间止
	EndTime string `json:"end_time"`
	AppId   string `json:"app_id"`
}
type AccessCount struct {
	TotalCount int64 `json:"total_count"`
	TodayCount int64 `json:"today_count"`
}

type DateItem struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

type AccessTopData struct {
	AppID   uint64     `json:"app_id,string"`
	AppName string     `json:"app_name"`
	Total   int        `json:"total"`
	Data    []DateItem `gorm:"type:json" json:"data"`
}

type AccessTopN struct {
	AppID   uint64         `json:"app_id,string"`
	AppName string         `json:"app_name"`
	Total   int            `json:"total"`
	Data    datatypes.JSON `gorm:"type:json" json:"data"`
}
type DateData struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

type SyncStrategyReq struct {
	AppIds       []uint64 `json:"app_ids"`
	AppGroupIds  []uint64 `json:"app_group_ids"`
	UserIds      []string `json:"user_ids"`
	UserGroupIds []string `json:"user_group_ids"`
}

type CheckStrategyQuoteReq struct {
	// 应用id
	AppId string `json:"app_id"`
	// 应用分组id
	AppGroupId  uint64   `json:"app_group_id"`
	RoleId      string   `json:"role_id"`
	AppGroupIds []string `json:"app_group_ids"`
	AppIds      []string `json:"app_ids"`
}

type CheckStrategyQuoteResp struct {
	// 策略id
	StrategyId uint64 `json:"strategy_id"`
	// 策略名称
	StrategyName string `json:"strategy_name"`
}
