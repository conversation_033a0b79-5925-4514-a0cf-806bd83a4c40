package alert

import (
	"asdsec.com/asec/platform/app/console/app/alert/api"
	"github.com/gin-gonic/gin"
)

func AlertApi(r *gin.RouterGroup) {
	// 路由组
	v := r.Group("/v1/alert")
	{
		v.GET("robot/list", api.RobotList)          //机器人列表
		v.POST("robot/add", api.RobotAdd)           //添加机器人
		v.POST("robot/edit", api.RobotEdit)         //编辑机器人
		v.POST("robot/del", api.RobotDel)           //删除机器人
		v.POST("notify/test", api.NotifyTest)       //发送测试通知
		v.GET("setting/list", api.SettingList)      //告警设置列表
		v.POST("setting/update", api.SettingUpdate) //更新告警设置
	}
}
