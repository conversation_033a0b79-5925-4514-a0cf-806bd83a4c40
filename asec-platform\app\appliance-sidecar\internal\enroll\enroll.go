package enroll

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"os/user"
	"path/filepath"
	"runtime"

	"go.uber.org/zap"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/machine"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/network"
	"asdsec.com/asec/platform/app/console/utils/crypt"
	"asdsec.com/asec/platform/pkg/utils"
)

const sePort = 8443

// TODO 把重试逻辑放进来，把读取系统信息放重试外面，避免反复读取
func AppEnroll(ctx context.Context, appType v1.ApplianceType, installId uint64) (uint64, error) {
	// 1.采集ip和mac地址,cpuid,操作系统,硬件id等
	conn, err := connection.GetPlatformConnection(ctx)
	if err != nil {
		global.Logger.Sugar().Errorf("AppEnroll GetPlatformConnection error : %s", err)
		return 0, err
	}
	client := v1.NewApplianceMgtClient(conn)
	cpuId, err := machine.GetCpuId()
	if err != nil {
		global.Logger.Sugar().Errorf("get cpuid error : %s", err)
		return 0, err
	}
	platUuid, err := machine.GetPlatformUUID()
	if err != nil {
		global.Logger.Sugar().Errorf("get platUuid error : %s", err)
	}
	firstMac, err := machine.GetMACAddress()
	if err != nil {
		global.Logger.Sugar().Errorf("get firstMac error : %s", err)
	}
	ips, macAddrs := network.GetIpsAndMac()
	hostIP := os.Getenv("HOST_IP")
	if hostIP != "" {
		ips = append(ips, hostIP)
	}
	in := v1.ApplianceEnrollReq{Type: appType, Plat: runtime.GOOS, Mac: macAddrs, Uuid: platUuid,
		Cpuid: cpuId, AgentIp: ips, Version: "1.1", LoginUser: GetCurUser(), ApplianceName: global.ApplianceName,
		FirstMac: firstMac,
	}
	//connector，gateway通过命令安装场景，指定设备ID
	if installId > 0 {
		in.ApplianceId = installId
	}
	if in.Type == v1.ApplianceType_SECURITY_EDGE || in.Type == v1.ApplianceType_GATEWAY {
		outIp, err := utils.GetOutboundIP()
		if err != nil {
			global.Logger.Sugar().Error("get OutboundIP error:", err)
		}
		in.SeIp = outIp
		in.SePort = sePort
	}
	stream, err := client.Enroll(ctx, &in)
	if err != nil {
		global.Logger.Sugar().Errorf("client.Enroll error:%v in:%+v", err, in)
		return 0, err
	}
	//如果平台已经删除了该客户端并且授权超限,则删除本地
	errCode := stream.GetErrorCode()
	if errCode == v1.EnrollErrorCode_AgentDeletedAndLicenseReachLimit {
		global.ApplianceID = 0
		deleteIDFile()
		return 0, errors.New("gen appId err, license reached limit and platform delete this agent record")
	}
	appId := stream.GetApplianceId()
	if appId == 0 {
		return 0, errors.New("gen appId err")
	}
	writeIDFile(global.PrivateIp, appId)
	if in.Type == v1.ApplianceType_SECURITY_EDGE || in.Type == v1.ApplianceType_GATEWAY {
		err = writePublicKeyFile([]byte(stream.GetPublicKey()))
		if err != nil {
			return 0, err
		}
	}
	connection.CloseConnection(conn)
	return appId, nil
}

func writePublicKeyFile(publicKey []byte) error {
	dir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		global.Logger.Sugar().Errorf("Get cur dir error: %v", err)
		return err
	}
	publicKeyFile := filepath.Join(dir, "config/ecd256-public.pem")
	exit, _ := utils.FileExists(publicKeyFile)
	if !exit {
		global.Logger.Sugar().Errorf("=====no file======:%v", publicKeyFile)
		os.Create(publicKeyFile)
	}
	// 更换平台地址就会重新走接入流程将文件进行覆盖,所以这里不需要在判断
	err = os.WriteFile(publicKeyFile, publicKey, 0640)
	if err != nil {
		global.Logger.Sugar().Errorf("wirte PublicKey File error: %s", err)
		return err
	}
	return nil
}

func writeIDFile(host string, ID uint64) {
	tmp := global.HostId{
		Host: host,
		Id:   ID,
	}
	hostJson, err := json.Marshal(tmp)
	encrypted := crypt.AesEncryptCBC(hostJson, []byte("wertyuiop~!@#$%^&*90-+ZXCVBNM<>?"))
	err = os.WriteFile(global.IDFilePath, encrypted, 0600)
	if err != nil {
		global.Logger.Sugar().Errorf("wirte IDFile error: %s", err)
	}
}

func deleteIDFile() {
	err := os.Remove(global.IDFilePath)
	if err != nil {
		zap.L().Sugar().Errorf("delete error: %s", err)
	}
}

// GetCurUser 获取当前登录用户
func GetCurUser() string {
	current, err := user.Current()
	if err != nil {
		return ""
	}
	return current.Username
}
