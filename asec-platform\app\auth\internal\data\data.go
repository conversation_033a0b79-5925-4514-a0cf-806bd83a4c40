package data

import (
	"context"
	stdlog "log"
	"os"
	"time"

	"asdsec.com/asec/platform/pkg/ip2region"
	"asdsec.com/asec/platform/pkg/ip2region/common"
	"github.com/go-redis/redis/extra/redisotel"
	"github.com/go-redis/redis/v8"

	"asdsec.com/asec/platform/app/auth/internal/conf"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	glogger "gorm.io/gorm/logger"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewUserRepo, NewCorpRepo, NewRoleRepo, NewAuthPolicyRepo, NewUserSourceRepo, NewIdpRepo, NewUserGroupRepo, NewAuthRepo, NewCommonRepo, NewOIDCRepo, NewAppRepo, NewAuthAccountPolicyRepo, NewSessionTrackerRepo, NewJWTBlacklistRepo)

// Data .
type Data struct {
	db            *gorm.DB
	rdb           *redis.Client
	specialConfig map[string]string
	ip2region     common.Ip2region
}

type SpecialConfig struct {
	Key   string
	Value string
}

const (
	driverPostgres = "postgres"
)

// NewData .
func NewData(c *conf.Data, logger log.Logger) (*Data, func(), error) {
	l := log.NewHelper(logger)

	var d gorm.Dialector
	switch c.Database.Driver {
	case driverPostgres:
		d = postgres.Open(c.Database.Dsn)
	default:
		l.Fatalf("driver not support")
	}
	newLogger := glogger.New(
		stdlog.New(os.Stdout, "\r\n", stdlog.LstdFlags), // io writer
		glogger.Config{
			SlowThreshold:             3 * time.Second, // Slow SQL threshold
			LogLevel:                  glogger.Error,   // Log level
			IgnoreRecordNotFoundError: true,            // Ignore ErrRecordNotFound error for logger
		},
	)

	db, err := gorm.Open(d, &gorm.Config{Logger: newLogger})
	if err != nil {
		l.Fatalf("connect db err=%v", err)
	}
	// 启用数据连接池
	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDB, err := db.DB()
	if err != nil {
		l.Fatalf("failed set db: %v", err)
	}
	sqlDB.SetMaxIdleConns(5)

	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(10)

	// SetConnMaxLifetime 设置了连接可复用的最大时间。
	sqlDB.SetConnMaxLifetime(time.Hour)

	rdb := redis.NewClient(&redis.Options{
		Addr:         c.Redis.Addr,
		Password:     c.Redis.Password,
		DB:           int(c.Redis.Db),
		DialTimeout:  c.Redis.DialTimeout.AsDuration(),
		WriteTimeout: c.Redis.WriteTimeout.AsDuration(),
		ReadTimeout:  c.Redis.ReadTimeout.AsDuration(),
	})
	rdb.AddHook(redisotel.TracingHook{})
	if err = rdb.Ping(context.Background()).Err(); err != nil {
		l.Fatalf("failed connect redis. err=%v", err)
	}

	//初始化特殊配置
	var specialConfig []SpecialConfig
	userSpecialSql := `select key,value from tb_special_config where type = 'USER'`
	err = db.Raw(userSpecialSql).Find(&specialConfig).Error
	if err != nil {
		l.Fatalf("failed init special config. err=%v", err)
	}
	specialMap := make(map[string]string)
	for _, v := range specialConfig {
		specialMap[v.Key] = v.Value
	}
	var ip2regionOnce common.Ip2region
	ip2regionOnce, _ = ip2region.New(common.OpenSourceImpl)

	return &Data{db: db, rdb: rdb, specialConfig: specialMap, ip2region: ip2regionOnce}, func() {
		l.Info("closing the data resources")
		if err := sqlDB.Close(); err != nil {
			l.Errorf("close sqlDB failed. err=%v", err)
		}
		if err := rdb.Close(); err != nil {
			l.Errorf("close rdb failed. err=%v", err)
		}
	}, nil
}
