// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbWxDepartment = "tb_wx_department"

// TbWxDepartment mapped from table <tb_wx_department>
type TbWxDepartment struct {
	WxCorpID         string    `gorm:"column:wx_corp_id;not null" json:"wx_corp_id"`
	LocalRootGroupID string    `gorm:"column:local_root_group_id;not null" json:"local_root_group_id"`
	LocalGroupID     string    `gorm:"column:local_group_id;not null" json:"local_group_id"`
	ID               int64     `gorm:"column:id;not null" json:"id"`
	Name             string    `gorm:"column:name" json:"name"`
	NameEn           string    `gorm:"column:name_en" json:"name_en"`
	Parentid         int64     `gorm:"column:parentid;not null" json:"parentid"`
	Order            int64     `gorm:"column:order;not null" json:"order"`
	CreatedAt        time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbWxDepartment's table name
func (*TbWxDepartment) TableName() string {
	return TableNameTbWxDepartment
}
