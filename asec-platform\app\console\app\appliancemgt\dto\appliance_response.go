package dto

import (
	"asdsec.com/asec/platform/app/console/common/utils"
	"asdsec.com/asec/platform/pkg/model"
	"github.com/lib/pq"
)

type AgentResp struct {
	CorpId       int64           `gorm:"column:corp_id" db:"corp_id" json:"corp_id"`
	ApplianceId  uint64          `gorm:"column:appliance_id" db:"appliance_id" json:"appliance_id,string"`
	AppName      string          `gorm:"column:app_name" db:"app_name" json:"app_name"`
	FirstMac     string          `gorm:"column:first_mac" db:"first_mac" json:"first_mac"`
	MacInfo      pq.StringArray  `gorm:"column:mac_info;type:text[]" db:"mac_info" json:"mac_info"`
	AppIps       pq.StringArray  `gorm:"column:app_ips;type:text[]" db:"app_ips" json:"app_ips"`
	AppPlat      string          `gorm:"column:app_plat" db:"app_plat" json:"app_plat"`
	AppType      int32           `gorm:"column:app_type" db:"app_type" json:"app_type"`
	AppVersion   string          `gorm:"column:app_version" db:"app_version" json:"app_version"`
	AppUuid      string          `gorm:"column:app_uuid" db:"app_uuid" json:"app_uuid"`
	CpuId        string          `gorm:"column:cpu_id" db:"cpu_id" json:"cpu_id"`
	Online       bool            `json:"online"`
	OsInfo       string          `json:"os_info"`
	LoginUser    string          `gorm:"column:login_user" db:"login_user" json:"login_user"`
	UserName     string          `gorm:"column:user_name" db:"user_name" json:"user_name"`
	Path         string          `gorm:"column:path" db:"path" json:"path"`
	UserId       string          `gorm:"column:user_id" db:"user_id" json:"user_id"`
	DisplayName  string          `gorm:"column:display_name" db:"display_name" json:"display_name"`
	CreateTime   utils.FrontTime `gorm:"column:create_time" db:"create_time" json:"create_time"`
	UpdateTime   utils.FrontTime `gorm:"column:update_time" db:"update_time" json:"update_time"`
	UpdatedAt    utils.FrontTime `json:"updated_at"`
	Version      string          `json:"version"`
	UpgradeTime  string          `json:"upgrade_time"`
	BindUserId   string          `json:"bind_userid" gorm:"column:bind_userid"`
	BindUsername string          `json:"bind_username" gorm:"column:bind_username"`
	UserType     int32           `json:"user_type" gorm:"column:user_type"`
}
type AgentCountRsp struct {
	TotalApplianceCount   int `gorm:"total_appliance_count" json:"total_appliance_count"`
	OnlineApplianceCount  int `gorm:"online_appliance_count" json:"online_appliance_count"`
	OfflineApplianceCount int `gorm:"offline_appliance_count" json:"offline_appliance_count"`
}

type GetAgentListRsp struct {
	model.Pagination
	AgentCountRsp AgentCountRsp `json:"agent_count_rsp"`
}

type ApplianceResp struct {
	model.Appliance
	Platform        string `gorm:"column:platform" json:"platform"`
	CommandLinux    string `gorm:"column:command_linux;type:varchar" json:"command_linux"`
	CommandWindows  string `gorm:"column:command_windows;type:varchar" json:"command_windows"`
	Name            string `gorm:"column:name;type:varchar" json:"name"`
	Type            int32  `gorm:"column:type;" json:"type"`
	Online          bool   `json:"online"`
	Installed       bool   `json:"installed"`
	DisplayAppType  int32  `json:"display_app_type"`
	GatewayIP       string `gorm:"column:gateway_ip" json:"gateway_ip"`
	GatewayPort     string `gorm:"column:gateway_port" json:"gateway_port"`
	GatewayProtocol string `gorm:"column:gateway_protocol" json:"gateway_protocol"`
	GatewayLocalIP  string `gorm:"column:gateway_local_ip" json:"gateway_local_ip"`
	PlatformDomain  string `gorm:"column:platform_domain" json:"platform_domain"`
}

type ApplianceAPPRelation struct {
	model.SeAppRelation
	AppName string `gorm:"column:app_name" json:"app_name"`
}

type DeviceCount struct {
	Count    int    `gorm:"column:count" json:"count"`
	PlatType string `gorm:"column:plat_type" json:"plat_type"`
}

type AgentVersionCheckReq struct {
	Version     string `json:"version" binding:"omitempty"` //当前版本
	CorpId      string `json:"corp_id" binding:"omitempty"`
	ApplianceId string `json:"appliance_id" binding:"omitempty"` //客户端ID
	Beta        bool   `json:"beta" binding:"omitempty"`         //是否参与beta
	Platform    string `json:"platform" binding:"required,oneof=windows linux darwin ios android"`
}

type AgentVersionCheckResp struct {
	LatestVersion  string   `json:"latest_version"`
	NextVersion    string   `json:"next_version"`
	LatestFilename string   `json:"latest_filename"`
	DownloadUrl    string   `json:"download_url"`
	DiffUrl        string   `json:"diff_url"`
	ForceUpdate    bool     `json:"force_update"`
	ReleaseNotes   []string `json:"release_notes"` //版本发布描述
	SHA256SUM      string   `json:"sha_256_sum"`
	MD5SUM         string   `json:"md5_sum"`
	NeedUpgrade    bool     `json:"need_upgrade"`
}

type UpdateApplianceInstallReq struct {
	ApplianceId     string `json:"appliance_id"`
	Name            string `json:"name"`
	Desc            string `json:"desc"`
	GatewayIP       string `json:"gateway_ip"`
	GatewayPort     uint16 `json:"gateway_port"`
	GatewayProtocol string `json:"gateway_protocol"`
	GatewayLocalIP  string `json:"gateway_local_ip"`
	PlatformDomain  string `json:"platform_domain"`
}

type ApplianceInstallReq struct {
	Name            string `json:"name"`
	Desc            string `json:"desc"`
	Type            int32  `json:"type" binding:"required,oneof=1 2 3"`
	Platform        string `json:"platform" binding:"required,oneof=windows linux"`
	ValidDays       uint   `json:"valid_days"`
	GatewayIP       string `json:"gateway_ip"`
	GatewayPort     uint16 `json:"gateway_port"`
	GatewayProtocol string `json:"gateway_protocol"`
	GatewayLocalIP  string `json:"gateway_local_ip"`
	PlatformDomain  string `json:"platform_domain"`
}

type SetAgentUpgradeStatusReq struct {
	// last_version 和 next_version 按理说进入并发队列开始处理后就会记录
	// 但和 @xy 沟通可能存在版本检查时没有传agent_id，升级成功又传了agent_id的情况
	// 所以需要传对应版本信息记录日志
	LastVersion  string `json:"last_version" binding:"omitempty"`                 // 升级前版本
	NextVersion  string `json:"next_version" binding:"required"`                  // 升级后版本
	ApplianceId  string `json:"appliance_id" binding:"required"`                  // 客户端id
	Platform     string `json:"platform" binding:"required,oneof=windows darwin"` // 平台 windows/darwin
	Status       string `json:"status" binding:"required,oneof=success failed"`   // 升级结果 success: 成功，failed: 失败
	FailedReason string `json:"failed_reason" binding:"omitempty"`                // 失败原因
}

type AgentBindUserReq struct {
	AgentId  string `json:"agent_id"`
	UserId   string `json:"user_id"`
	UserName string `json:"user_name"`
	Name     string `json:"name"`
}

type ReqIds struct {
	Ids  []string `json:"ids"`
	Name string   `json:"name"`
}

type GetUpgradePolicyDetailReq struct {
	Platform string `json:"platform" form:"platform" binding:"required"` // 平台 windows/darwin
}

// GroupEntity 组织实体
type GroupEntity struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Type       string `json:"type"`
	Path       string `json:"path,omitempty"`
	SourceType string `json:"source_type,omitempty"`
}

// UserEntity 用户实体
type UserEntity struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	DisplayName string `json:"display_name,omitempty"`
	Path        string `json:"path,omitempty"`
	SourceType  string `json:"source_type,omitempty"`
}

// AgentEntity 终端实体
type AgentEntity struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
	Path string `json:"path,omitempty"`
	Icon string `json:"icon,omitempty"` // 可以根据平台类型设置
}

type GetUpgradePolicyDetailResp struct {
	ID               string        `json:"id"`                // 策略id
	LatestVersion    string        `json:"latest_version"`    // 最新版本号
	UpgradeTime      string        `json:"upgrade_time"`      // 升级时间
	UpgradeMode      string        `json:"upgrade_mode"`      // 升级模式 silence: 静默升级 hint: 弹窗提示
	ConcurrencyCount int32         `json:"concurrency_count"` // 并发数量
	GrayEnable       bool          `json:"gray_enable"`       // 是否开启灰度
	GrayMode         string        `json:"gray_mode"`         // 灰度模式 random: 全范围随机 custom: 自定义范围
	RandomCount      int32         `json:"random_count"`      // mode=random时随机灰度的数量
	GroupIds         []GroupEntity `json:"group_ids"`         // mode=custom时生效的分组id列表
	UserIds          []UserEntity  `json:"user_ids"`          // mode=custom时生效的用户id列表
	AgentIds         []AgentEntity `json:"agent_ids"`         // mode=custom时生效的终端id列表
	Duration         int32         `json:"duration"`          // 灰度持续时间长度
	DurationUnit     string        `json:"duration_unit"`     // 灰度持续时间单位
}

type SetUpgradePolicyReq struct {
	ID               string   `json:"id" binding:"required"`                              // 策略id
	UpgradeMode      string   `json:"upgrade_mode" binding:"required,oneof=silence"`      // 升级模式 目前只支持静默 silence: 静默升级 hint: 弹窗提示
	ConcurrencyCount int32    `json:"concurrency_count" binding:"required,min=1,max=500"` // 并发数量
	GrayEnable       bool     `json:"gray_enable"`                                        // 是否开启灰度
	GrayMode         string   `json:"gray_mode" binding:"omitempty,oneof=random custom"`  // 灰度模式 random: 全范围随机 custom: 自定义范围
	RandomCount      int32    `json:"random_count" binding:"omitempty,min=1,max=100000"`  // mode=random时随机灰度的数量
	GroupIds         []string `json:"group_ids"`                                          // mode=custom时生效的分组列表
	UserIds          []string `json:"user_ids"`                                           // mode=custom时生效的用户列表
	AgentIds         []string `json:"agent_ids"`                                          // mode=custom时生效的终端列表
	Duration         int32    `json:"duration"  binding:"omitempty,min=1,max=180"`        // 灰度持续时间长度
	DurationUnit     string   `json:"duration_unit" binding:"omitempty,oneof=day"`        // 灰度持续时间单位 目前只支持天
	Platform         string   `json:"platform" form:"platform"`                           // 平台 windows/darwin
}

type GetUpgradeOverviewReq struct {
	Platform string `json:"platform" form:"platform"` // 平台 windows/darwin
}

type VersionCount struct {
	Version string `json:"version"`
	Count   int    `json:"count"`
}

type GetUpgradeOverviewResp struct {
	Versions []VersionCount `json:"versions"`
}

type AgentUpgradeConfig struct {
	Note        []string `json:"note"`
	Md5         string   `json:"md5"`
	Sha256      string   `json:"sha256"`
	Version     string   `json:"version"`
	NextVersion string   `json:"next_version"`
	DiffUpgrade int      `json:"diff_upgrade"`
	DevMode     string
}
