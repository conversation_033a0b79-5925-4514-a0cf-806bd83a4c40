/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package main

import (
	"flag"
	"fmt"
	"log"
	"runtime"
	"sync"

	v1 "asdsec.com/asec/platform/api/appliance/v1"
	sidecar "asdsec.com/asec/platform/app/appliance-sidecar/cmd/start"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	proxy "asdsec.com/asec/platform/app/appliance-sidecar/proxy/https"
	"asdsec.com/asec/platform/pkg/utils"
	single "asdsec.com/asec/platform/pkg/utils/single_process"
)

var flagconf string
var applianceType int
var installId uint64 //指定的接入ID,用于通过平台命令生成ID安装的场景
var genAgentId bool

func init() {
	flag.StringVar(&flagconf, "f", "../config.yaml", "config path, eg: -f config.yaml")
	flag.IntVar(&applianceType, "t", 0, "applianceType, eg: 0")
	flag.StringVar(&global.InnerHost, "ih", "", "Inner Host, eg: localhost:9002")
	flag.StringVar(&global.PublicHost, "ph", "", "pulibc Host, eg: asdsec.com:443")
	flag.StringVar(&global.PrivateHost, "plataddr", "", "private Host, eg: asdsec.com:443")
	flag.StringVar(&global.Version, "version", "", "asec agent version eg: 1.0.1")
	flag.StringVar(&global.UpgradeTime, "update_time", "", "asec agent version eg: 1.0.1")
	flag.Uint64Var(&installId, "ins_id", 0, "")
	flag.IntVar(&global.RpcPort, "port", 50000, "set rpc port")
	flag.IntVar(&global.HttpsPort, "https_port", 443, "set rpc port")
	flag.BoolVar(&genAgentId, "generate", false, "gen agent id")
	flag.StringVar(&global.LogServerHost, "log_addr", "", "logServer Host, eg: asdsec.com:443")
	flag.Parse()
}

func main() {

	//开启spa敲门 - 只在Windows客户端启动
	if global.ApplianceType == v1.ApplianceType_AGENT && runtime.GOOS == "windows" {
		if err := global.StartSPA(); err != nil {
			if global.Logger != nil {
				global.Logger.Sugar().Warnf("spa start failed: %v", err)
			}
		}
		// 启动HTTP反向代理服务
		go proxy.StartReverseProxy()
	}
	//先敲门再生成ID不能搞反了
	if genAgentId && global.PrivateHost != "" {
		agentId := sidecar.GenAgentId(int32(applianceType))
		fmt.Print(agentId)
		return
	}
	lockName := "sidecar"
	lockPath := utils.GetConfigDir()
	// single process instance
	lock, err := single.NewWithLock(lockName, single.WithLockPath(lockPath))

	defer func() {
		if lock != nil {
			lock.Unlock()
		}
	}()
	if err != nil {
		log.Fatalf("single process lock err:%v,lock file :%s\\%s", err, lockPath, lockName)
	}

	sidecar.StartSidecar(flagconf, int32(applianceType), installId, &sync.WaitGroup{})
}
