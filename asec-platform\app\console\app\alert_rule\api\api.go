package api

import (
	"asdsec.com/asec/platform/app/console/app/alert_rule/model"
	"asdsec.com/asec/platform/app/console/app/alert_rule/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	modelTable "asdsec.com/asec/platform/pkg/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetAlertRuleList godoc
// @Summary 告警规则列表
// @Schemes
// @Description 根据名称返回告警规则列表
// @Tags        alert_rule
// @Produce     application/json
// @Param       req body model.GetAlertRuleListReq true "获取告警规则列表"
// @Success     200
// @Router      /v1/alert/rule/list [POST]
// @success     200 {object} common.Response{data=model.Pagination} "ok"
func GetAlertRuleList(c *gin.Context) {
	req := model.GetAlertRuleListReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	AlertRules, err := service.GetAlertRuleService().GetAlertRuleList(c, req)
	if err != nil {
		global.SysLog.Error("GetAlertRule err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, AlertRules)
}

// GetAlertRuleTemplateList godoc
// @Summary 告警规则模版列表
// @Schemes
// @Description 告警规则模版列表
// @Tags        alert_rule_template
// @Produce     application/json
// @Success     200
// @Router      /v1/alert/rule/template/list [GET]
// @success     200 {object} common.Response{data=[]model.AlertRuleTemplate} "ok"
func GetAlertRuleTemplateList(c *gin.Context) {
	AlertRuleTemplateList, err := service.GetAlertRuleService().GetAlertRuleTemplateList(c)
	if err != nil {
		global.SysLog.Error("GetAlertRuleTemplate err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, AlertRuleTemplateList)
}

// Create godoc
// @Summary 新增告警规则
// @Schemes
// @Description 新增告警规则
// @Tags        alert_rule
// @Produce     application/json
// @Success     200
// @Router      /v1/alert/rule [POST]
// @Param       req body model.CreateAlertRuleReq true "新增告警规则"
// @success     200 {object} common.Response{data=model.CreateAlertRuleReq} "ok"
func Create(c *gin.Context) {
	req := modelTable.AlertRule{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.Name == "" {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.AlertRuleResourceType,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	err = service.GetAlertRuleService().Create(c, &req)
	if err != nil {
		global.SysLog.Error("Add AlertRule err", zap.Error(err))
		common.Fail(c, common.CreateDlpStrategyErr)
		return
	}
	common.OkWithData(c, req)
}

// CreateTemplate godoc
// @Summary 导入告警规则模版
// @Schemes
// @Description 导入告警规则模版
// @Tags        alert_rule_template
// @Produce     application/json
// @Success     200
// @Router      /v1/alert/rule/template [POST]
// @Param       req body model.AlertRuleTemplate true "导入告警规则模版"
// @success     200 {object} common.Response{data=model.AlertRuleTemplate} "ok"
func CreateTemplate(c *gin.Context) {
	req := model.AlertRuleTemplate{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.Name == "" {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.AlertRuleResourceType,
			OperationType:  common.OperateCreate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err = service.GetAlertRuleService().CreateTemplate(c, &req)
	if err != nil {
		global.SysLog.Error("GetAlertRule err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, req)
}

// Update godoc
// @Summary 修改告警规则
// @Schemes
// @Description 修改告警规则
// @Tags        alert_rule
// @Produce     application/json
// @Success     200
// @Router      /v1/alert/rule [PUT]
// @Param       req body model.CreateAlertRuleReq true "修改告警规则"
// @success     200 {object} common.Response{data=model.CreateAlertRuleReq} "ok"
func Update(c *gin.Context) {
	req := modelTable.AlertRule{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if req.Id == "" {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.AlertRuleResourceType,
			OperationType:  common.OperateUpdate,
			Representation: req.Name,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err = service.GetAlertRuleService().Update(c, &req)
	if err != nil {
		global.SysLog.Error("Update Dlp Strategy err", zap.Error(err))
		common.Fail(c, common.UpdateDlpStrategyErr)
		return
	}
	common.OkWithData(c, req)
}

// Delete godoc
// @Summary 删除告警规则
// @Schemes
// @Description 删除告警规则
// @Tags        alert_rule
// @Produce     application/json
// @Success     200
// @Router      /v1/alert/rule [DELETE]
// @Param       id query string true "告警规则ID" - "删除告警规则"
// @success     200 {object} common.Response{} "ok"
func Delete(c *gin.Context) {
	idStr := c.Query("id")
	nameStr := c.Query("name")
	//日志操作
	err := service.GetAlertRuleService().Delete(c, idStr)
	var errorLog = ""
	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			ResourceType:   common.AlertRuleResourceType,
			OperationType:  common.OperateDelete,
			Representation: nameStr,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()
	if err != nil {
		global.SysLog.Error("GetAlertRule err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}
