// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: auth/v1/error.proto

package v1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	// 为某个枚举单独设置错误码
	ErrorReason_RECORD_NOT_FOUND              ErrorReason = 0
	ErrorReason_NAME_CONFLICT                 ErrorReason = 1
	ErrorReason_SOURCE_TYPE_CONFLICT          ErrorReason = 2
	ErrorReason_IDP_SOURCE_NUM_LIMIT          ErrorReason = 3
	ErrorReason_ONLY_ROOT_GROUP_CAN_BIND_IDP  ErrorReason = 4
	ErrorReason_MAIN_IDP_ONLY_BIND_ONE_SOURCE ErrorReason = 5
	ErrorReason_USER_ENTITY_UNIQUE_CONFLICT   ErrorReason = 6
	ErrorReason_SOURCE_TYPE_NOT_SUPPORT       ErrorReason = 7
	ErrorReason_IDP_SOURCE_TYPE_ERROR         ErrorReason = 8
	ErrorReason_ONLY_MAIN_IDP_CAN_BIND        ErrorReason = 9
	ErrorReason_USER_SOURCE_CONFLICT          ErrorReason = 10
	ErrorReason_USER_GROUP_DUPLICATE          ErrorReason = 11
	ErrorReason_USER_GROUP_CIRCLE             ErrorReason = 12
	ErrorReason_GROUP_NOT_FOUND               ErrorReason = 13
	ErrorReason_USER_GROUP_CONFLICT           ErrorReason = 14
	ErrorReason_GROUP_NOT_SUPPORT_IDP         ErrorReason = 15
	ErrorReason_AUTH_OBJECT_EMPTY             ErrorReason = 16
	ErrorReason_CRED_ERROR                    ErrorReason = 17
	ErrorReason_LOGIN_ERROR                   ErrorReason = 18
	ErrorReason_AUTH_POLICY_ERROR             ErrorReason = 19
	ErrorReason_TYPE_ERROR                    ErrorReason = 20
	ErrorReason_PARAM_ERROR                   ErrorReason = 21
	ErrorReason_TOKEN_EXPIRE                  ErrorReason = 22
	ErrorReason_TOKEN_INVALID                 ErrorReason = 23
	ErrorReason_TOKEN_PARSE_FAILED            ErrorReason = 24
	ErrorReason_PASS_WORD_NOT_CORRECT         ErrorReason = 25
	ErrorReason_ROOT_SOURCE_ERROR             ErrorReason = 26
	ErrorReason_DISABLE_ERROR                 ErrorReason = 27
	ErrorReason_DEFAULT_DATA_CONFLICT         ErrorReason = 28
	ErrorReason_QIYEWX_TRUSTED_IP_ERROR       ErrorReason = 29
	ErrorReason_QIYEWX_CONFIG_ERROR           ErrorReason = 30
	ErrorReason_ACCOUNT_OR_PASSWORD_ERROR     ErrorReason = 31
	ErrorReason_CACHE_ERROR                   ErrorReason = 32
	ErrorReason_EXPIRE_ERROR                  ErrorReason = 33
	ErrorReason_FIELD_MAP_CONFIG_ERROR        ErrorReason = 34
	ErrorReason_SMS_CODE_INVALID_ERROR        ErrorReason = 35
	ErrorReason_SMS_CODE_ERROR                ErrorReason = 36
	ErrorReason_AUTH_CHAIN_FAILURE            ErrorReason = 37
	ErrorReason_NOT_MAIN_AUTH                 ErrorReason = 38
	ErrorReason_NOT_PHONE                     ErrorReason = 39
	ErrorReason_FEISHU_SYNC_ERROR             ErrorReason = 40
	ErrorReason_SEND_SMS_ERROR                ErrorReason = 41
	ErrorReason_TOKEN_VERIFY                  ErrorReason = 42
	ErrorReason_CODE_VERIFY_ERROR             ErrorReason = 43
	ErrorReason_PARAM_VERIFY_ERROR            ErrorReason = 44
	ErrorReason_OAUTH2_AUTHORIZE_ERROR        ErrorReason = 45
	ErrorReason_OAUTH2_CALLBACK_ERROR         ErrorReason = 46
	ErrorReason_USER_NOT_FOUND                ErrorReason = 47
	ErrorReason_EMAIL_NOT_EXISTS              ErrorReason = 48
	ErrorReason_EMAIL_FORMAT_INVALID          ErrorReason = 49
	ErrorReason_EMAIL_CODE_INVALID_ERROR      ErrorReason = 50
	ErrorReason_EMAIL_CODE_ERROR              ErrorReason = 51
	ErrorReason_EMAIL_SEND_ERROR              ErrorReason = 52
	ErrorReason_EMAIL_AUTH_FAILURE            ErrorReason = 53
	ErrorReason_AUTH_SERVER_CONFIG_ERROR      ErrorReason = 55 // 认证服务器配置错误
	ErrorReason_AUTH_SERVER_CONNECT_ERROR     ErrorReason = 56 // 认证服务器连接错误
	ErrorReason_NETWORK_CONNECTION_ERROR      ErrorReason = 57 // 网络连接错误
	ErrorReason_AUTH_SEARCH_NOT_UNIQUE        ErrorReason = 58
	ErrorReason_AUTH_USER_LOCK                ErrorReason = 59
	ErrorReason_SECURITY_CODE_ERROR           ErrorReason = 60
	// 客户端登录限制相关错误
	ErrorReason_CLIENT_TYPE_FORBIDDEN ErrorReason = 61 // 客户端类型被禁止
	ErrorReason_CLIENT_LIMIT_EXCEEDED ErrorReason = 62 // 客户端数量超限
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0:  "RECORD_NOT_FOUND",
		1:  "NAME_CONFLICT",
		2:  "SOURCE_TYPE_CONFLICT",
		3:  "IDP_SOURCE_NUM_LIMIT",
		4:  "ONLY_ROOT_GROUP_CAN_BIND_IDP",
		5:  "MAIN_IDP_ONLY_BIND_ONE_SOURCE",
		6:  "USER_ENTITY_UNIQUE_CONFLICT",
		7:  "SOURCE_TYPE_NOT_SUPPORT",
		8:  "IDP_SOURCE_TYPE_ERROR",
		9:  "ONLY_MAIN_IDP_CAN_BIND",
		10: "USER_SOURCE_CONFLICT",
		11: "USER_GROUP_DUPLICATE",
		12: "USER_GROUP_CIRCLE",
		13: "GROUP_NOT_FOUND",
		14: "USER_GROUP_CONFLICT",
		15: "GROUP_NOT_SUPPORT_IDP",
		16: "AUTH_OBJECT_EMPTY",
		17: "CRED_ERROR",
		18: "LOGIN_ERROR",
		19: "AUTH_POLICY_ERROR",
		20: "TYPE_ERROR",
		21: "PARAM_ERROR",
		22: "TOKEN_EXPIRE",
		23: "TOKEN_INVALID",
		24: "TOKEN_PARSE_FAILED",
		25: "PASS_WORD_NOT_CORRECT",
		26: "ROOT_SOURCE_ERROR",
		27: "DISABLE_ERROR",
		28: "DEFAULT_DATA_CONFLICT",
		29: "QIYEWX_TRUSTED_IP_ERROR",
		30: "QIYEWX_CONFIG_ERROR",
		31: "ACCOUNT_OR_PASSWORD_ERROR",
		32: "CACHE_ERROR",
		33: "EXPIRE_ERROR",
		34: "FIELD_MAP_CONFIG_ERROR",
		35: "SMS_CODE_INVALID_ERROR",
		36: "SMS_CODE_ERROR",
		37: "AUTH_CHAIN_FAILURE",
		38: "NOT_MAIN_AUTH",
		39: "NOT_PHONE",
		40: "FEISHU_SYNC_ERROR",
		41: "SEND_SMS_ERROR",
		42: "TOKEN_VERIFY",
		43: "CODE_VERIFY_ERROR",
		44: "PARAM_VERIFY_ERROR",
		45: "OAUTH2_AUTHORIZE_ERROR",
		46: "OAUTH2_CALLBACK_ERROR",
		47: "USER_NOT_FOUND",
		48: "EMAIL_NOT_EXISTS",
		49: "EMAIL_FORMAT_INVALID",
		50: "EMAIL_CODE_INVALID_ERROR",
		51: "EMAIL_CODE_ERROR",
		52: "EMAIL_SEND_ERROR",
		53: "EMAIL_AUTH_FAILURE",
		55: "AUTH_SERVER_CONFIG_ERROR",
		56: "AUTH_SERVER_CONNECT_ERROR",
		57: "NETWORK_CONNECTION_ERROR",
		58: "AUTH_SEARCH_NOT_UNIQUE",
		59: "AUTH_USER_LOCK",
		60: "SECURITY_CODE_ERROR",
		61: "CLIENT_TYPE_FORBIDDEN",
		62: "CLIENT_LIMIT_EXCEEDED",
	}
	ErrorReason_value = map[string]int32{
		"RECORD_NOT_FOUND":              0,
		"NAME_CONFLICT":                 1,
		"SOURCE_TYPE_CONFLICT":          2,
		"IDP_SOURCE_NUM_LIMIT":          3,
		"ONLY_ROOT_GROUP_CAN_BIND_IDP":  4,
		"MAIN_IDP_ONLY_BIND_ONE_SOURCE": 5,
		"USER_ENTITY_UNIQUE_CONFLICT":   6,
		"SOURCE_TYPE_NOT_SUPPORT":       7,
		"IDP_SOURCE_TYPE_ERROR":         8,
		"ONLY_MAIN_IDP_CAN_BIND":        9,
		"USER_SOURCE_CONFLICT":          10,
		"USER_GROUP_DUPLICATE":          11,
		"USER_GROUP_CIRCLE":             12,
		"GROUP_NOT_FOUND":               13,
		"USER_GROUP_CONFLICT":           14,
		"GROUP_NOT_SUPPORT_IDP":         15,
		"AUTH_OBJECT_EMPTY":             16,
		"CRED_ERROR":                    17,
		"LOGIN_ERROR":                   18,
		"AUTH_POLICY_ERROR":             19,
		"TYPE_ERROR":                    20,
		"PARAM_ERROR":                   21,
		"TOKEN_EXPIRE":                  22,
		"TOKEN_INVALID":                 23,
		"TOKEN_PARSE_FAILED":            24,
		"PASS_WORD_NOT_CORRECT":         25,
		"ROOT_SOURCE_ERROR":             26,
		"DISABLE_ERROR":                 27,
		"DEFAULT_DATA_CONFLICT":         28,
		"QIYEWX_TRUSTED_IP_ERROR":       29,
		"QIYEWX_CONFIG_ERROR":           30,
		"ACCOUNT_OR_PASSWORD_ERROR":     31,
		"CACHE_ERROR":                   32,
		"EXPIRE_ERROR":                  33,
		"FIELD_MAP_CONFIG_ERROR":        34,
		"SMS_CODE_INVALID_ERROR":        35,
		"SMS_CODE_ERROR":                36,
		"AUTH_CHAIN_FAILURE":            37,
		"NOT_MAIN_AUTH":                 38,
		"NOT_PHONE":                     39,
		"FEISHU_SYNC_ERROR":             40,
		"SEND_SMS_ERROR":                41,
		"TOKEN_VERIFY":                  42,
		"CODE_VERIFY_ERROR":             43,
		"PARAM_VERIFY_ERROR":            44,
		"OAUTH2_AUTHORIZE_ERROR":        45,
		"OAUTH2_CALLBACK_ERROR":         46,
		"USER_NOT_FOUND":                47,
		"EMAIL_NOT_EXISTS":              48,
		"EMAIL_FORMAT_INVALID":          49,
		"EMAIL_CODE_INVALID_ERROR":      50,
		"EMAIL_CODE_ERROR":              51,
		"EMAIL_SEND_ERROR":              52,
		"EMAIL_AUTH_FAILURE":            53,
		"AUTH_SERVER_CONFIG_ERROR":      55,
		"AUTH_SERVER_CONNECT_ERROR":     56,
		"NETWORK_CONNECTION_ERROR":      57,
		"AUTH_SEARCH_NOT_UNIQUE":        58,
		"AUTH_USER_LOCK":                59,
		"SECURITY_CODE_ERROR":           60,
		"CLIENT_TYPE_FORBIDDEN":         61,
		"CLIENT_LIMIT_EXCEEDED":         62,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_auth_v1_error_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_auth_v1_error_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_auth_v1_error_proto_rawDescGZIP(), []int{0}
}

var File_auth_v1_error_proto protoreflect.FileDescriptor

var file_auth_v1_error_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x76, 0x31, 0x1a, 0x13, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0xa2, 0x0c, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x44,
	0x50, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x52, 0x4f, 0x4f,
	0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x42, 0x49, 0x4e, 0x44,
	0x5f, 0x49, 0x44, 0x50, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x49,
	0x44, 0x50, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x4f, 0x4e, 0x45,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x49, 0x51, 0x55, 0x45, 0x5f,
	0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55,
	0x50, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x44, 0x50, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x5f,
	0x49, 0x44, 0x50, 0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x10, 0x09, 0x12, 0x18,
	0x0a, 0x14, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x10, 0x0a, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45,
	0x10, 0x0b, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x5f, 0x43, 0x49, 0x52, 0x43, 0x4c, 0x45, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x47, 0x52, 0x4f,
	0x55, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x0d, 0x12, 0x17,
	0x0a, 0x13, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x4c, 0x49, 0x43, 0x54, 0x10, 0x0e, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x49, 0x44, 0x50,
	0x10, 0x0f, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x4f, 0x42, 0x4a, 0x45, 0x43,
	0x54, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x10, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x52, 0x45,
	0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x11, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x4f, 0x47,
	0x49, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x12, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x13, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x14, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x15, 0x12, 0x16, 0x0a, 0x0c, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x10, 0x16, 0x1a, 0x04, 0xa8, 0x45, 0x91, 0x03, 0x12, 0x17, 0x0a, 0x0d, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x17, 0x1a, 0x04, 0xa8,
	0x45, 0x91, 0x03, 0x12, 0x1c, 0x0a, 0x12, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x50, 0x41, 0x52,
	0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x18, 0x1a, 0x04, 0xa8, 0x45, 0x91,
	0x03, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x41, 0x53, 0x53, 0x5f, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x10, 0x19, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x4f, 0x4f, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x1a, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x1b, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c,
	0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43, 0x54, 0x10,
	0x1c, 0x12, 0x1b, 0x0a, 0x17, 0x51, 0x49, 0x59, 0x45, 0x57, 0x58, 0x5f, 0x54, 0x52, 0x55, 0x53,
	0x54, 0x45, 0x44, 0x5f, 0x49, 0x50, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x1d, 0x12, 0x17,
	0x0a, 0x13, 0x51, 0x49, 0x59, 0x45, 0x57, 0x58, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x1e, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x1f, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x20, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x21, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x50, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x22, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x4d, 0x53, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x23, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4d, 0x53, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x24, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x43, 0x48,
	0x41, 0x49, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x25, 0x12, 0x11, 0x0a,
	0x0d, 0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x10, 0x26,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x27, 0x12,
	0x15, 0x0a, 0x11, 0x46, 0x45, 0x49, 0x53, 0x48, 0x55, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x28, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x53,
	0x4d, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x29, 0x12, 0x16, 0x0a, 0x0c, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x10, 0x2a, 0x1a, 0x04, 0xa8, 0x45,
	0xae, 0x02, 0x12, 0x1b, 0x0a, 0x11, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x59, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x2b, 0x1a, 0x04, 0xa8, 0x45, 0xae, 0x02, 0x12,
	0x1c, 0x0a, 0x12, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x2c, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x20, 0x0a,
	0x16, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x32, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a,
	0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x2d, 0x1a, 0x04, 0xa8, 0x45, 0xae, 0x02, 0x12,
	0x1f, 0x0a, 0x15, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x32, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x42, 0x41,
	0x43, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x2e, 0x1a, 0x04, 0xa8, 0x45, 0xae, 0x02,
	0x12, 0x18, 0x0a, 0x0e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x2f, 0x1a, 0x04, 0xa8, 0x45, 0xc8, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x30,
	0x12, 0x18, 0x0a, 0x14, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54,
	0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x31, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x32, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x33, 0x12, 0x14,
	0x0a, 0x10, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x34, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x35, 0x12, 0x1c, 0x0a, 0x18,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x37, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43,
	0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x38, 0x12, 0x1c, 0x0a, 0x18, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x39, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x55, 0x54, 0x48, 0x5f,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x51, 0x55,
	0x45, 0x10, 0x3a, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x3b, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x3c,
	0x12, 0x19, 0x0a, 0x15, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x44, 0x45, 0x4e, 0x10, 0x3d, 0x12, 0x19, 0x0a, 0x15, 0x43,
	0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45,
	0x45, 0x44, 0x45, 0x44, 0x10, 0x3e, 0x1a, 0x04, 0xa0, 0x45, 0xc8, 0x01, 0x42, 0x29, 0x5a, 0x27,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_auth_v1_error_proto_rawDescOnce sync.Once
	file_auth_v1_error_proto_rawDescData = file_auth_v1_error_proto_rawDesc
)

func file_auth_v1_error_proto_rawDescGZIP() []byte {
	file_auth_v1_error_proto_rawDescOnce.Do(func() {
		file_auth_v1_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_auth_v1_error_proto_rawDescData)
	})
	return file_auth_v1_error_proto_rawDescData
}

var file_auth_v1_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_auth_v1_error_proto_goTypes = []interface{}{
	(ErrorReason)(0), // 0: api.auth.v1.ErrorReason
}
var file_auth_v1_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_auth_v1_error_proto_init() }
func file_auth_v1_error_proto_init() {
	if File_auth_v1_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_auth_v1_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_auth_v1_error_proto_goTypes,
		DependencyIndexes: file_auth_v1_error_proto_depIdxs,
		EnumInfos:         file_auth_v1_error_proto_enumTypes,
	}.Build()
	File_auth_v1_error_proto = out.File
	file_auth_v1_error_proto_rawDesc = nil
	file_auth_v1_error_proto_goTypes = nil
	file_auth_v1_error_proto_depIdxs = nil
}
