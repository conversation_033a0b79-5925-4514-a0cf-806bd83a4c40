// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.20.0
// source: appliance/v1/appliance_mgt.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Symbols defined in public import of google/protobuf/timestamp.proto.

type Timestamp = timestamppb.Timestamp

type ApplianceType int32

const (
	// 客户端
	ApplianceType_AGENT ApplianceType = 0
	// 安全边缘代理
	ApplianceType_SECURITY_EDGE ApplianceType = 1
	// 连接器
	ApplianceType_CONNECTOR ApplianceType = 2
	// 网关
	ApplianceType_GATEWAY ApplianceType = 3
	// 日志服务器
	ApplianceType_LOGSERVER ApplianceType = 4
	// 认证服务器
	ApplianceType_AUTHSERVER ApplianceType = 5
)

// Enum value maps for ApplianceType.
var (
	ApplianceType_name = map[int32]string{
		0: "AGENT",
		1: "SECURITY_EDGE",
		2: "CONNECTOR",
		3: "GATEWAY",
		4: "LOGSERVER",
		5: "AUTHSERVER",
	}
	ApplianceType_value = map[string]int32{
		"AGENT":         0,
		"SECURITY_EDGE": 1,
		"CONNECTOR":     2,
		"GATEWAY":       3,
		"LOGSERVER":     4,
		"AUTHSERVER":    5,
	}
)

func (x ApplianceType) Enum() *ApplianceType {
	p := new(ApplianceType)
	*p = x
	return p
}

func (x ApplianceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApplianceType) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_appliance_mgt_proto_enumTypes[0].Descriptor()
}

func (ApplianceType) Type() protoreflect.EnumType {
	return &file_appliance_v1_appliance_mgt_proto_enumTypes[0]
}

func (x ApplianceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApplianceType.Descriptor instead.
func (ApplianceType) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{0}
}

type EnrollErrorCode int32

const (
	EnrollErrorCode_SUCCEED EnrollErrorCode = 0
	// 新客户端接入,授权不足
	EnrollErrorCode_LicenseReachLimit  EnrollErrorCode = -1
	EnrollErrorCode_LicenseQueryFailed EnrollErrorCode = -2
	// 授权不足并且客户端存在ID但是平台已经删除,这个情况下通知客户端清除ID缓存
	EnrollErrorCode_AgentDeletedAndLicenseReachLimit EnrollErrorCode = -3
	EnrollErrorCode_DBOperateErr                     EnrollErrorCode = -4
)

// Enum value maps for EnrollErrorCode.
var (
	EnrollErrorCode_name = map[int32]string{
		0:  "SUCCEED",
		-1: "LicenseReachLimit",
		-2: "LicenseQueryFailed",
		-3: "AgentDeletedAndLicenseReachLimit",
		-4: "DBOperateErr",
	}
	EnrollErrorCode_value = map[string]int32{
		"SUCCEED":                          0,
		"LicenseReachLimit":                -1,
		"LicenseQueryFailed":               -2,
		"AgentDeletedAndLicenseReachLimit": -3,
		"DBOperateErr":                     -4,
	}
)

func (x EnrollErrorCode) Enum() *EnrollErrorCode {
	p := new(EnrollErrorCode)
	*p = x
	return p
}

func (x EnrollErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnrollErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_appliance_mgt_proto_enumTypes[1].Descriptor()
}

func (EnrollErrorCode) Type() protoreflect.EnumType {
	return &file_appliance_v1_appliance_mgt_proto_enumTypes[1]
}

func (x EnrollErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnrollErrorCode.Descriptor instead.
func (EnrollErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{1}
}

type CommandType int32

const (
	// none
	CommandType_NONE CommandType = 0
	// 执行命令
	CommandType_CMD       CommandType = 1
	CommandType_UNINSTALL CommandType = 2
	CommandType_ENROLL    CommandType = 3
)

// Enum value maps for CommandType.
var (
	CommandType_name = map[int32]string{
		0: "NONE",
		1: "CMD",
		2: "UNINSTALL",
		3: "ENROLL",
	}
	CommandType_value = map[string]int32{
		"NONE":      0,
		"CMD":       1,
		"UNINSTALL": 2,
		"ENROLL":    3,
	}
)

func (x CommandType) Enum() *CommandType {
	p := new(CommandType)
	*p = x
	return p
}

func (x CommandType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommandType) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_appliance_mgt_proto_enumTypes[2].Descriptor()
}

func (CommandType) Type() protoreflect.EnumType {
	return &file_appliance_v1_appliance_mgt_proto_enumTypes[2]
}

func (x CommandType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommandType.Descriptor instead.
func (CommandType) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{2}
}

type StatusCode int32

const (
	StatusCode_SUCCESS StatusCode = 0
	StatusCode_FAIL    StatusCode = -1
)

// Enum value maps for StatusCode.
var (
	StatusCode_name = map[int32]string{
		0:  "SUCCESS",
		-1: "FAIL",
	}
	StatusCode_value = map[string]int32{
		"SUCCESS": 0,
		"FAIL":    -1,
	}
)

func (x StatusCode) Enum() *StatusCode {
	p := new(StatusCode)
	*p = x
	return p
}

func (x StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_appliance_mgt_proto_enumTypes[3].Descriptor()
}

func (StatusCode) Type() protoreflect.EnumType {
	return &file_appliance_v1_appliance_mgt_proto_enumTypes[3]
}

func (x StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusCode.Descriptor instead.
func (StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{3}
}

type SetUpgradeStatusRes_StatusCode int32

const (
	SetUpgradeStatusRes_SUCCESS SetUpgradeStatusRes_StatusCode = 0
)

// Enum value maps for SetUpgradeStatusRes_StatusCode.
var (
	SetUpgradeStatusRes_StatusCode_name = map[int32]string{
		0: "SUCCESS",
	}
	SetUpgradeStatusRes_StatusCode_value = map[string]int32{
		"SUCCESS": 0,
	}
)

func (x SetUpgradeStatusRes_StatusCode) Enum() *SetUpgradeStatusRes_StatusCode {
	p := new(SetUpgradeStatusRes_StatusCode)
	*p = x
	return p
}

func (x SetUpgradeStatusRes_StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SetUpgradeStatusRes_StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_appliance_v1_appliance_mgt_proto_enumTypes[4].Descriptor()
}

func (SetUpgradeStatusRes_StatusCode) Type() protoreflect.EnumType {
	return &file_appliance_v1_appliance_mgt_proto_enumTypes[4]
}

func (x SetUpgradeStatusRes_StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SetUpgradeStatusRes_StatusCode.Descriptor instead.
func (SetUpgradeStatusRes_StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{13, 0}
}

type ApplianceEnrollReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type ApplianceType `protobuf:"varint,1,opt,name=type,proto3,enum=api.appliance.ApplianceType" json:"type,omitempty"`
	// agent版本
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// agent操作系统平台类型
	Plat string `protobuf:"bytes,3,opt,name=plat,proto3" json:"plat,omitempty"`
	// agent mac地址，考虑多网卡情况
	Mac           []string `protobuf:"bytes,4,rep,name=mac,proto3" json:"mac,omitempty"`
	Uuid          string   `protobuf:"bytes,5,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Cpuid         string   `protobuf:"bytes,6,opt,name=cpuid,proto3" json:"cpuid,omitempty"`
	AgentIp       []string `protobuf:"bytes,7,rep,name=agent_ip,json=agentIp,proto3" json:"agent_ip,omitempty"` //把ip信息也带上来
	LoginUser     string   `protobuf:"bytes,8,opt,name=login_user,json=loginUser,proto3" json:"login_user,omitempty"`
	ApplianceName string   `protobuf:"bytes,9,opt,name=appliance_name,json=applianceName,proto3" json:"appliance_name,omitempty"`
	FirstMac      string   `protobuf:"bytes,10,opt,name=first_mac,json=firstMac,proto3" json:"first_mac,omitempty"`
	SeIp          string   `protobuf:"bytes,11,opt,name=se_ip,json=seIp,proto3" json:"se_ip,omitempty"`
	SePort        int32    `protobuf:"varint,12,opt,name=se_port,json=sePort,proto3" json:"se_port,omitempty"`
	ApplianceId   uint64   `protobuf:"varint,13,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"` //connector等使用命令安装的设备，会从安装命令获取平台预先生成的ID
	PrivateIp     string   `protobuf:"bytes,14,opt,name=private_ip,json=privateIp,proto3" json:"private_ip,omitempty"`        //内网地址
	PublicIp      string   `protobuf:"bytes,15,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`           //公网地址
}

func (x *ApplianceEnrollReq) Reset() {
	*x = ApplianceEnrollReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplianceEnrollReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplianceEnrollReq) ProtoMessage() {}

func (x *ApplianceEnrollReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplianceEnrollReq.ProtoReflect.Descriptor instead.
func (*ApplianceEnrollReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{0}
}

func (x *ApplianceEnrollReq) GetType() ApplianceType {
	if x != nil {
		return x.Type
	}
	return ApplianceType_AGENT
}

func (x *ApplianceEnrollReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ApplianceEnrollReq) GetPlat() string {
	if x != nil {
		return x.Plat
	}
	return ""
}

func (x *ApplianceEnrollReq) GetMac() []string {
	if x != nil {
		return x.Mac
	}
	return nil
}

func (x *ApplianceEnrollReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ApplianceEnrollReq) GetCpuid() string {
	if x != nil {
		return x.Cpuid
	}
	return ""
}

func (x *ApplianceEnrollReq) GetAgentIp() []string {
	if x != nil {
		return x.AgentIp
	}
	return nil
}

func (x *ApplianceEnrollReq) GetLoginUser() string {
	if x != nil {
		return x.LoginUser
	}
	return ""
}

func (x *ApplianceEnrollReq) GetApplianceName() string {
	if x != nil {
		return x.ApplianceName
	}
	return ""
}

func (x *ApplianceEnrollReq) GetFirstMac() string {
	if x != nil {
		return x.FirstMac
	}
	return ""
}

func (x *ApplianceEnrollReq) GetSeIp() string {
	if x != nil {
		return x.SeIp
	}
	return ""
}

func (x *ApplianceEnrollReq) GetSePort() int32 {
	if x != nil {
		return x.SePort
	}
	return 0
}

func (x *ApplianceEnrollReq) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

func (x *ApplianceEnrollReq) GetPrivateIp() string {
	if x != nil {
		return x.PrivateIp
	}
	return ""
}

func (x *ApplianceEnrollReq) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

type ApplianceEnrollRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        ApplianceType   `protobuf:"varint,1,opt,name=type,proto3,enum=api.appliance.ApplianceType" json:"type,omitempty"`
	ApplianceId uint64          `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	PublicKey   string          `protobuf:"bytes,3,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	ErrorCode   EnrollErrorCode `protobuf:"varint,4,opt,name=error_code,json=errorCode,proto3,enum=api.appliance.EnrollErrorCode" json:"error_code,omitempty"`
	ErrorMsg    string          `protobuf:"bytes,5,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
}

func (x *ApplianceEnrollRes) Reset() {
	*x = ApplianceEnrollRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplianceEnrollRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplianceEnrollRes) ProtoMessage() {}

func (x *ApplianceEnrollRes) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplianceEnrollRes.ProtoReflect.Descriptor instead.
func (*ApplianceEnrollRes) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{1}
}

func (x *ApplianceEnrollRes) GetType() ApplianceType {
	if x != nil {
		return x.Type
	}
	return ApplianceType_AGENT
}

func (x *ApplianceEnrollRes) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

func (x *ApplianceEnrollRes) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *ApplianceEnrollRes) GetErrorCode() EnrollErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return EnrollErrorCode_SUCCEED
}

func (x *ApplianceEnrollRes) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type HeartbeatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        ApplianceType `protobuf:"varint,1,opt,name=type,proto3,enum=api.appliance.ApplianceType" json:"type,omitempty"`
	ApplianceId uint64        `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	// 进程的状态监控，因为agent可能有多个进程，这里使用数组
	ProcsStat       []*ProcStat `protobuf:"bytes,3,rep,name=procsStat,proto3" json:"procsStat,omitempty"`
	SysCpu          string      `protobuf:"bytes,4,opt,name=sys_cpu,json=sysCpu,proto3" json:"sys_cpu,omitempty"`
	SysMem          string      `protobuf:"bytes,5,opt,name=sys_mem,json=sysMem,proto3" json:"sys_mem,omitempty"`
	KernelVersion   string      `protobuf:"bytes,6,opt,name=kernel_version,json=kernelVersion,proto3" json:"kernel_version,omitempty"`
	Arch            string      `protobuf:"bytes,7,opt,name=arch,proto3" json:"arch,omitempty"`
	Platform        string      `protobuf:"bytes,8,opt,name=platform,proto3" json:"platform,omitempty"`
	PlatformFamily  string      `protobuf:"bytes,9,opt,name=platform_family,json=platformFamily,proto3" json:"platform_family,omitempty"`
	PlatformVersion string      `protobuf:"bytes,10,opt,name=platform_version,json=platformVersion,proto3" json:"platform_version,omitempty"`
	Version         string      `protobuf:"bytes,11,opt,name=version,proto3" json:"version,omitempty"`                            //客户端版本
	UpgradeTime     string      `protobuf:"bytes,12,opt,name=upgrade_time,json=upgradeTime,proto3" json:"upgrade_time,omitempty"` //客户端更新时间
	UserId          string      `protobuf:"bytes,13,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *HeartbeatReq) Reset() {
	*x = HeartbeatReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatReq) ProtoMessage() {}

func (x *HeartbeatReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatReq.ProtoReflect.Descriptor instead.
func (*HeartbeatReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{2}
}

func (x *HeartbeatReq) GetType() ApplianceType {
	if x != nil {
		return x.Type
	}
	return ApplianceType_AGENT
}

func (x *HeartbeatReq) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

func (x *HeartbeatReq) GetProcsStat() []*ProcStat {
	if x != nil {
		return x.ProcsStat
	}
	return nil
}

func (x *HeartbeatReq) GetSysCpu() string {
	if x != nil {
		return x.SysCpu
	}
	return ""
}

func (x *HeartbeatReq) GetSysMem() string {
	if x != nil {
		return x.SysMem
	}
	return ""
}

func (x *HeartbeatReq) GetKernelVersion() string {
	if x != nil {
		return x.KernelVersion
	}
	return ""
}

func (x *HeartbeatReq) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *HeartbeatReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *HeartbeatReq) GetPlatformFamily() string {
	if x != nil {
		return x.PlatformFamily
	}
	return ""
}

func (x *HeartbeatReq) GetPlatformVersion() string {
	if x != nil {
		return x.PlatformVersion
	}
	return ""
}

func (x *HeartbeatReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *HeartbeatReq) GetUpgradeTime() string {
	if x != nil {
		return x.UpgradeTime
	}
	return ""
}

func (x *HeartbeatReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// 进程状态监控，用于定位运行状态，性能等问题
type ProcStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid        int32  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`
	Cpu        string `protobuf:"bytes,2,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Rss        string `protobuf:"bytes,3,opt,name=rss,proto3" json:"rss,omitempty"`
	ReadSpeed  string `protobuf:"bytes,4,opt,name=read_speed,json=readSpeed,proto3" json:"read_speed,omitempty"`
	WriteSpeed string `protobuf:"bytes,5,opt,name=write_speed,json=writeSpeed,proto3" json:"write_speed,omitempty"`
	FdCnt      int32  `protobuf:"varint,6,opt,name=fd_cnt,json=fdCnt,proto3" json:"fd_cnt,omitempty"`
	StartAt    int64  `protobuf:"varint,7,opt,name=start_at,json=startAt,proto3" json:"start_at,omitempty"`
	TxTps      string `protobuf:"bytes,8,opt,name=tx_tps,json=txTps,proto3" json:"tx_tps,omitempty"`
	RxTps      string `protobuf:"bytes,9,opt,name=rx_tps,json=rxTps,proto3" json:"rx_tps,omitempty"`
	// 进程目录占用空间
	Du string `protobuf:"bytes,10,opt,name=du,proto3" json:"du,omitempty"`
	// 进程名称
	ProcName string `protobuf:"bytes,11,opt,name=proc_name,json=procName,proto3" json:"proc_name,omitempty"`
}

func (x *ProcStat) Reset() {
	*x = ProcStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcStat) ProtoMessage() {}

func (x *ProcStat) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcStat.ProtoReflect.Descriptor instead.
func (*ProcStat) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{3}
}

func (x *ProcStat) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ProcStat) GetCpu() string {
	if x != nil {
		return x.Cpu
	}
	return ""
}

func (x *ProcStat) GetRss() string {
	if x != nil {
		return x.Rss
	}
	return ""
}

func (x *ProcStat) GetReadSpeed() string {
	if x != nil {
		return x.ReadSpeed
	}
	return ""
}

func (x *ProcStat) GetWriteSpeed() string {
	if x != nil {
		return x.WriteSpeed
	}
	return ""
}

func (x *ProcStat) GetFdCnt() int32 {
	if x != nil {
		return x.FdCnt
	}
	return 0
}

func (x *ProcStat) GetStartAt() int64 {
	if x != nil {
		return x.StartAt
	}
	return 0
}

func (x *ProcStat) GetTxTps() string {
	if x != nil {
		return x.TxTps
	}
	return ""
}

func (x *ProcStat) GetRxTps() string {
	if x != nil {
		return x.RxTps
	}
	return ""
}

func (x *ProcStat) GetDu() string {
	if x != nil {
		return x.Du
	}
	return ""
}

func (x *ProcStat) GetProcName() string {
	if x != nil {
		return x.ProcName
	}
	return ""
}

type HeartbeatReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        ApplianceType `protobuf:"varint,1,opt,name=type,proto3,enum=api.appliance.ApplianceType" json:"type,omitempty"`
	ApplianceId uint64        `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	Version     string        `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                            //客户端版本
	UpgradeTime string        `protobuf:"bytes,4,opt,name=upgrade_time,json=upgradeTime,proto3" json:"upgrade_time,omitempty"` //客户端更新时间
	UserId      string        `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 新增字段
	ServiceStatus uint32 `protobuf:"varint,6,opt,name=service_status,json=serviceStatus,proto3" json:"service_status,omitempty"` //服务状态 0x1 - 服务异常
	// 终端类型
	Platform      string `protobuf:"bytes,7,opt,name=platform,proto3" json:"platform,omitempty"`
	UserName      string `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	UserType      int32  `protobuf:"varint,9,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
	SourceProcess string `protobuf:"bytes,10,opt,name=source_process,json=sourceProcess,proto3" json:"source_process,omitempty"`
	Flush         bool   `protobuf:"varint,11,opt,name=flush,proto3" json:"flush,omitempty"` //是否立即刷新数据库信息
}

func (x *HeartbeatReportReq) Reset() {
	*x = HeartbeatReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatReportReq) ProtoMessage() {}

func (x *HeartbeatReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatReportReq.ProtoReflect.Descriptor instead.
func (*HeartbeatReportReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{4}
}

func (x *HeartbeatReportReq) GetType() ApplianceType {
	if x != nil {
		return x.Type
	}
	return ApplianceType_AGENT
}

func (x *HeartbeatReportReq) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

func (x *HeartbeatReportReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *HeartbeatReportReq) GetUpgradeTime() string {
	if x != nil {
		return x.UpgradeTime
	}
	return ""
}

func (x *HeartbeatReportReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *HeartbeatReportReq) GetServiceStatus() uint32 {
	if x != nil {
		return x.ServiceStatus
	}
	return 0
}

func (x *HeartbeatReportReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *HeartbeatReportReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *HeartbeatReportReq) GetUserType() int32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *HeartbeatReportReq) GetSourceProcess() string {
	if x != nil {
		return x.SourceProcess
	}
	return ""
}

func (x *HeartbeatReportReq) GetFlush() bool {
	if x != nil {
		return x.Flush
	}
	return false
}

// 逃生指令通过心跳信息返回
type HeartbeatReportRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 下发给设备的指令，比如重启，升级，停止等
	Command int32 `protobuf:"varint,1,opt,name=command,proto3" json:"command,omitempty"`
	// 命令执行参数
	Args string `protobuf:"bytes,2,opt,name=args,proto3" json:"args,omitempty"`
	// 新增字段
	CommandType CommandType `protobuf:"varint,3,opt,name=command_type,json=commandType,proto3,enum=api.appliance.CommandType" json:"command_type,omitempty"` //心跳返回命令类型，见枚举
	TaskInfo    *TaskInfo   `protobuf:"bytes,4,opt,name=task_info,json=taskInfo,proto3" json:"task_info,omitempty"`
}

func (x *HeartbeatReportRes) Reset() {
	*x = HeartbeatReportRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatReportRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatReportRes) ProtoMessage() {}

func (x *HeartbeatReportRes) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatReportRes.ProtoReflect.Descriptor instead.
func (*HeartbeatReportRes) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{5}
}

func (x *HeartbeatReportRes) GetCommand() int32 {
	if x != nil {
		return x.Command
	}
	return 0
}

func (x *HeartbeatReportRes) GetArgs() string {
	if x != nil {
		return x.Args
	}
	return ""
}

func (x *HeartbeatReportRes) GetCommandType() CommandType {
	if x != nil {
		return x.CommandType
	}
	return CommandType_NONE
}

func (x *HeartbeatReportRes) GetTaskInfo() *TaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId  string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`     // 任务id
	TaskCmd string `protobuf:"bytes,2,opt,name=task_cmd,json=taskCmd,proto3" json:"task_cmd,omitempty"`  // 任务执行命令
	TimeOut uint32 `protobuf:"varint,3,opt,name=time_out,json=timeOut,proto3" json:"time_out,omitempty"` // 任务执行超时时间，单位/秒， 如5分钟超时则是300
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{6}
}

func (x *TaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskInfo) GetTaskCmd() string {
	if x != nil {
		return x.TaskCmd
	}
	return ""
}

func (x *TaskInfo) GetTimeOut() uint32 {
	if x != nil {
		return x.TimeOut
	}
	return 0
}

// 逃生指令通过心跳信息返回
type HeartbeatRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 下发给设备的指令，比如重启，升级，停止等
	Command int32 `protobuf:"varint,1,opt,name=command,proto3" json:"command,omitempty"`
	// 命令执行参数
	Args string `protobuf:"bytes,2,opt,name=args,proto3" json:"args,omitempty"`
}

func (x *HeartbeatRes) Reset() {
	*x = HeartbeatRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatRes) ProtoMessage() {}

func (x *HeartbeatRes) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatRes.ProtoReflect.Descriptor instead.
func (*HeartbeatRes) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{7}
}

func (x *HeartbeatRes) GetCommand() int32 {
	if x != nil {
		return x.Command
	}
	return 0
}

func (x *HeartbeatRes) GetArgs() string {
	if x != nil {
		return x.Args
	}
	return ""
}

type GetConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        ApplianceType `protobuf:"varint,1,opt,name=type,proto3,enum=api.appliance.ApplianceType" json:"type,omitempty"`
	ApplianceId uint64        `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	CorpId      uint64        `protobuf:"varint,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	OsType      uint64        `protobuf:"varint,4,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	AgentType   uint64        `protobuf:"varint,5,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"` //网关,终端
	CfgItems    []*CfgSurvey  `protobuf:"bytes,6,rep,name=cfgItems,proto3" json:"cfgItems,omitempty"`
}

func (x *GetConfigReq) Reset() {
	*x = GetConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigReq) ProtoMessage() {}

func (x *GetConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigReq.ProtoReflect.Descriptor instead.
func (*GetConfigReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{8}
}

func (x *GetConfigReq) GetType() ApplianceType {
	if x != nil {
		return x.Type
	}
	return ApplianceType_AGENT
}

func (x *GetConfigReq) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

func (x *GetConfigReq) GetCorpId() uint64 {
	if x != nil {
		return x.CorpId
	}
	return 0
}

func (x *GetConfigReq) GetOsType() uint64 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *GetConfigReq) GetAgentType() uint64 {
	if x != nil {
		return x.AgentType
	}
	return 0
}

func (x *GetConfigReq) GetCfgItems() []*CfgSurvey {
	if x != nil {
		return x.CfgItems
	}
	return nil
}

type CfgSurvey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigType    string `protobuf:"bytes,3,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	ConfigVersion string `protobuf:"bytes,6,opt,name=config_version,json=configVersion,proto3" json:"config_version,omitempty"`
}

func (x *CfgSurvey) Reset() {
	*x = CfgSurvey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CfgSurvey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CfgSurvey) ProtoMessage() {}

func (x *CfgSurvey) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CfgSurvey.ProtoReflect.Descriptor instead.
func (*CfgSurvey) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{9}
}

func (x *CfgSurvey) GetConfigType() string {
	if x != nil {
		return x.ConfigType
	}
	return ""
}

func (x *CfgSurvey) GetConfigVersion() string {
	if x != nil {
		return x.ConfigVersion
	}
	return ""
}

type Configs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        ApplianceType `protobuf:"varint,1,opt,name=type,proto3,enum=api.appliance.ApplianceType" json:"type,omitempty"`
	ApplianceId uint64        `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	CorpId      uint64        `protobuf:"varint,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	OsType      uint64        `protobuf:"varint,4,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	AgentType   uint64        `protobuf:"varint,5,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"`
	DataItems   []*CfgResult  `protobuf:"bytes,6,rep,name=dataItems,proto3" json:"dataItems,omitempty"`
}

func (x *Configs) Reset() {
	*x = Configs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Configs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Configs) ProtoMessage() {}

func (x *Configs) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Configs.ProtoReflect.Descriptor instead.
func (*Configs) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{10}
}

func (x *Configs) GetType() ApplianceType {
	if x != nil {
		return x.Type
	}
	return ApplianceType_AGENT
}

func (x *Configs) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

func (x *Configs) GetCorpId() uint64 {
	if x != nil {
		return x.CorpId
	}
	return 0
}

func (x *Configs) GetOsType() uint64 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *Configs) GetAgentType() uint64 {
	if x != nil {
		return x.AgentType
	}
	return 0
}

func (x *Configs) GetDataItems() []*CfgResult {
	if x != nil {
		return x.DataItems
	}
	return nil
}

type CfgResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigName    string `protobuf:"bytes,1,opt,name=config_name,json=configName,proto3" json:"config_name,omitempty"`
	ConfigType    string `protobuf:"bytes,2,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	ConfigData    []byte `protobuf:"bytes,3,opt,name=config_data,json=configData,proto3" json:"config_data,omitempty"`
	ConfigVersion string `protobuf:"bytes,4,opt,name=config_version,json=configVersion,proto3" json:"config_version,omitempty"`
}

func (x *CfgResult) Reset() {
	*x = CfgResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CfgResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CfgResult) ProtoMessage() {}

func (x *CfgResult) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CfgResult.ProtoReflect.Descriptor instead.
func (*CfgResult) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{11}
}

func (x *CfgResult) GetConfigName() string {
	if x != nil {
		return x.ConfigName
	}
	return ""
}

func (x *CfgResult) GetConfigType() string {
	if x != nil {
		return x.ConfigType
	}
	return ""
}

func (x *CfgResult) GetConfigData() []byte {
	if x != nil {
		return x.ConfigData
	}
	return nil
}

func (x *CfgResult) GetConfigVersion() string {
	if x != nil {
		return x.ConfigVersion
	}
	return ""
}

type SetUpgradeStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastVersion  string `protobuf:"bytes,1,opt,name=last_version,json=lastVersion,proto3" json:"last_version,omitempty"`
	NextVersion  string `protobuf:"bytes,2,opt,name=next_version,json=nextVersion,proto3" json:"next_version,omitempty"`
	ApplianceId  string `protobuf:"bytes,3,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	Platform     string `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`
	Status       string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	FailedReason string `protobuf:"bytes,6,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`
}

func (x *SetUpgradeStatusReq) Reset() {
	*x = SetUpgradeStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUpgradeStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUpgradeStatusReq) ProtoMessage() {}

func (x *SetUpgradeStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUpgradeStatusReq.ProtoReflect.Descriptor instead.
func (*SetUpgradeStatusReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{12}
}

func (x *SetUpgradeStatusReq) GetLastVersion() string {
	if x != nil {
		return x.LastVersion
	}
	return ""
}

func (x *SetUpgradeStatusReq) GetNextVersion() string {
	if x != nil {
		return x.NextVersion
	}
	return ""
}

func (x *SetUpgradeStatusReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

func (x *SetUpgradeStatusReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *SetUpgradeStatusReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SetUpgradeStatusReq) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

type SetUpgradeStatusRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status SetUpgradeStatusRes_StatusCode `protobuf:"varint,1,opt,name=status,proto3,enum=api.appliance.SetUpgradeStatusRes_StatusCode" json:"status,omitempty"`
}

func (x *SetUpgradeStatusRes) Reset() {
	*x = SetUpgradeStatusRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUpgradeStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUpgradeStatusRes) ProtoMessage() {}

func (x *SetUpgradeStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUpgradeStatusRes.ProtoReflect.Descriptor instead.
func (*SetUpgradeStatusRes) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{13}
}

func (x *SetUpgradeStatusRes) GetStatus() SetUpgradeStatusRes_StatusCode {
	if x != nil {
		return x.Status
	}
	return SetUpgradeStatusRes_SUCCESS
}

type TaskReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId        string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                         // 任务id
	TaskStartTime uint64 `protobuf:"varint,2,opt,name=task_start_time,json=taskStartTime,proto3" json:"task_start_time,omitempty"` // 任务开始时间，客户端可能存在任务排队情况
	TaskEndTime   uint64 `protobuf:"varint,3,opt,name=task_end_time,json=taskEndTime,proto3" json:"task_end_time,omitempty"`       // 任务结束时间，客户端可能存在任务排队情况
	TaskResult    string `protobuf:"bytes,4,opt,name=task_result,json=taskResult,proto3" json:"task_result,omitempty"`             // 任务返回结果，成功返回输出，失败返回异常，超时可不返回
	TaskStatus    string `protobuf:"bytes,5,opt,name=task_status,json=taskStatus,proto3" json:"task_status,omitempty"`             // 任务状态 success-成功，fail-失败，timeout-超时
}

func (x *TaskReportReq) Reset() {
	*x = TaskReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskReportReq) ProtoMessage() {}

func (x *TaskReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskReportReq.ProtoReflect.Descriptor instead.
func (*TaskReportReq) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{14}
}

func (x *TaskReportReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskReportReq) GetTaskStartTime() uint64 {
	if x != nil {
		return x.TaskStartTime
	}
	return 0
}

func (x *TaskReportReq) GetTaskEndTime() uint64 {
	if x != nil {
		return x.TaskEndTime
	}
	return 0
}

func (x *TaskReportReq) GetTaskResult() string {
	if x != nil {
		return x.TaskResult
	}
	return ""
}

func (x *TaskReportReq) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

type TaskReportResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 汇报结果 0 - 汇报成功 -1 - 汇报失败
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 成功 - 无内容，失败 - 失败原因
}

func (x *TaskReportResp) Reset() {
	*x = TaskReportResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskReportResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskReportResp) ProtoMessage() {}

func (x *TaskReportResp) ProtoReflect() protoreflect.Message {
	mi := &file_appliance_v1_appliance_mgt_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskReportResp.ProtoReflect.Descriptor instead.
func (*TaskReportResp) Descriptor() ([]byte, []int) {
	return file_appliance_v1_appliance_mgt_proto_rawDescGZIP(), []int{15}
}

func (x *TaskReportResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TaskReportResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_appliance_v1_appliance_mgt_proto protoreflect.FileDescriptor

var file_appliance_v1_appliance_mgt_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x67, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xbb, 0x03, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e,
	0x72, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x70, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x70,
	0x75, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1d,
	0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6d, 0x61,
	0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4d, 0x61,
	0x63, 0x12, 0x13, 0x0a, 0x05, 0x73, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x73, 0x65, 0x49, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x70,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49,
	0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x49, 0x70, 0x22, 0xe4,
	0x01, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x72, 0x6f,
	0x6c, 0x6c, 0x52, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x0a, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x45, 0x6e,
	0x72, 0x6f, 0x6c, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22, 0xcd, 0x03, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x09, 0x70,
	0x72, 0x6f, 0x63, 0x73, 0x53, 0x74, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x74, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x79, 0x73, 0x5f, 0x63, 0x70, 0x75, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x73, 0x43, 0x70, 0x75, 0x12, 0x17, 0x0a, 0x07, 0x73,
	0x79, 0x73, 0x5f, 0x6d, 0x65, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79,
	0x73, 0x4d, 0x65, 0x6d, 0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x65,
	0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x61,
	0x72, 0x63, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x46, 0x61,
	0x6d, 0x69, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x8d, 0x02, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x64,
	0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x61, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x64, 0x5f, 0x63,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x66, 0x64, 0x43, 0x6e, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78,
	0x5f, 0x74, 0x70, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x54, 0x70,
	0x73, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x78, 0x5f, 0x74, 0x70, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x78, 0x54, 0x70, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x75, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x75, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf9, 0x02, 0x0a, 0x12, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x75,
	0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x6c, 0x75, 0x73, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6c, 0x75, 0x73,
	0x68, 0x22, 0xb7, 0x01, 0x0a, 0x12, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x59, 0x0a, 0x08, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6d, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6d, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74,
	0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x22, 0x3c, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x52, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x61, 0x72, 0x67, 0x73, 0x22, 0xea, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f,
	0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f, 0x72,
	0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x63,
	0x66, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x66,
	0x67, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x08, 0x63, 0x66, 0x67, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0x53, 0x0a, 0x09, 0x43, 0x66, 0x67, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xe7, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x66, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0x95, 0x01, 0x0a, 0x09, 0x43, 0x66, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd7, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x74,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0x77, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x19, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x22, 0xb6, 0x01, 0x0a, 0x0d,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0d, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x36, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x2a, 0x68, 0x0a, 0x0d,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a,
	0x05, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x45, 0x44, 0x47, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43,
	0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x41,
	0x54, 0x45, 0x57, 0x41, 0x59, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x4f, 0x47, 0x53, 0x45,
	0x52, 0x56, 0x45, 0x52, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x55, 0x54, 0x48, 0x53, 0x45,
	0x52, 0x56, 0x45, 0x52, 0x10, 0x05, 0x2a, 0xa9, 0x01, 0x0a, 0x0f, 0x45, 0x6e, 0x72, 0x6f, 0x6c,
	0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x11, 0x4c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x52, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x1f, 0x0a, 0x12, 0x4c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0xfe, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x2d, 0x0a, 0x20, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x6e, 0x64, 0x4c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x52, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0xfd, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x19, 0x0a, 0x0c, 0x44, 0x42, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x45, 0x72, 0x72, 0x10, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0x01, 0x2a, 0x3b, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x43,
	0x4d, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x4e, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0x03, 0x2a,
	0x2c, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x04, 0x46, 0x41,
	0x49, 0x4c, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x32, 0xe3, 0x04,
	0x0a, 0x0c, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x67, 0x74, 0x12, 0x4e,
	0x0a, 0x06, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x12, 0x47,
	0x0a, 0x09, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x72,
	0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65,
	0x61, 0x74, 0x52, 0x65, 0x73, 0x28, 0x01, 0x12, 0x40, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x89, 0x01, 0x0a, 0x10, 0x53, 0x65,
	0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x53,
	0x65, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22, 0x22,
	0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x6f, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x19, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x7b, 0x0a, 0x0f, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x48, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x3a, 0x01, 0x2a, 0x42, 0x2e, 0x5a, 0x2c, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x50, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_appliance_v1_appliance_mgt_proto_rawDescOnce sync.Once
	file_appliance_v1_appliance_mgt_proto_rawDescData = file_appliance_v1_appliance_mgt_proto_rawDesc
)

func file_appliance_v1_appliance_mgt_proto_rawDescGZIP() []byte {
	file_appliance_v1_appliance_mgt_proto_rawDescOnce.Do(func() {
		file_appliance_v1_appliance_mgt_proto_rawDescData = protoimpl.X.CompressGZIP(file_appliance_v1_appliance_mgt_proto_rawDescData)
	})
	return file_appliance_v1_appliance_mgt_proto_rawDescData
}

var file_appliance_v1_appliance_mgt_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_appliance_v1_appliance_mgt_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_appliance_v1_appliance_mgt_proto_goTypes = []interface{}{
	(ApplianceType)(0),                  // 0: api.appliance.ApplianceType
	(EnrollErrorCode)(0),                // 1: api.appliance.EnrollErrorCode
	(CommandType)(0),                    // 2: api.appliance.CommandType
	(StatusCode)(0),                     // 3: api.appliance.StatusCode
	(SetUpgradeStatusRes_StatusCode)(0), // 4: api.appliance.SetUpgradeStatusRes.StatusCode
	(*ApplianceEnrollReq)(nil),          // 5: api.appliance.ApplianceEnrollReq
	(*ApplianceEnrollRes)(nil),          // 6: api.appliance.ApplianceEnrollRes
	(*HeartbeatReq)(nil),                // 7: api.appliance.HeartbeatReq
	(*ProcStat)(nil),                    // 8: api.appliance.ProcStat
	(*HeartbeatReportReq)(nil),          // 9: api.appliance.HeartbeatReportReq
	(*HeartbeatReportRes)(nil),          // 10: api.appliance.HeartbeatReportRes
	(*TaskInfo)(nil),                    // 11: api.appliance.TaskInfo
	(*HeartbeatRes)(nil),                // 12: api.appliance.HeartbeatRes
	(*GetConfigReq)(nil),                // 13: api.appliance.GetConfigReq
	(*CfgSurvey)(nil),                   // 14: api.appliance.CfgSurvey
	(*Configs)(nil),                     // 15: api.appliance.Configs
	(*CfgResult)(nil),                   // 16: api.appliance.CfgResult
	(*SetUpgradeStatusReq)(nil),         // 17: api.appliance.SetUpgradeStatusReq
	(*SetUpgradeStatusRes)(nil),         // 18: api.appliance.SetUpgradeStatusRes
	(*TaskReportReq)(nil),               // 19: api.appliance.TaskReportReq
	(*TaskReportResp)(nil),              // 20: api.appliance.TaskReportResp
}
var file_appliance_v1_appliance_mgt_proto_depIdxs = []int32{
	0,  // 0: api.appliance.ApplianceEnrollReq.type:type_name -> api.appliance.ApplianceType
	0,  // 1: api.appliance.ApplianceEnrollRes.type:type_name -> api.appliance.ApplianceType
	1,  // 2: api.appliance.ApplianceEnrollRes.error_code:type_name -> api.appliance.EnrollErrorCode
	0,  // 3: api.appliance.HeartbeatReq.type:type_name -> api.appliance.ApplianceType
	8,  // 4: api.appliance.HeartbeatReq.procsStat:type_name -> api.appliance.ProcStat
	0,  // 5: api.appliance.HeartbeatReportReq.type:type_name -> api.appliance.ApplianceType
	2,  // 6: api.appliance.HeartbeatReportRes.command_type:type_name -> api.appliance.CommandType
	11, // 7: api.appliance.HeartbeatReportRes.task_info:type_name -> api.appliance.TaskInfo
	0,  // 8: api.appliance.GetConfigReq.type:type_name -> api.appliance.ApplianceType
	14, // 9: api.appliance.GetConfigReq.cfgItems:type_name -> api.appliance.CfgSurvey
	0,  // 10: api.appliance.Configs.type:type_name -> api.appliance.ApplianceType
	16, // 11: api.appliance.Configs.dataItems:type_name -> api.appliance.CfgResult
	4,  // 12: api.appliance.SetUpgradeStatusRes.status:type_name -> api.appliance.SetUpgradeStatusRes.StatusCode
	5,  // 13: api.appliance.ApplianceMgt.Enroll:input_type -> api.appliance.ApplianceEnrollReq
	7,  // 14: api.appliance.ApplianceMgt.Heartbeat:input_type -> api.appliance.HeartbeatReq
	13, // 15: api.appliance.ApplianceMgt.GetConfig:input_type -> api.appliance.GetConfigReq
	17, // 16: api.appliance.ApplianceMgt.SetUpgradeStatus:input_type -> api.appliance.SetUpgradeStatusReq
	19, // 17: api.appliance.ApplianceMgt.TaskReport:input_type -> api.appliance.TaskReportReq
	9,  // 18: api.appliance.ApplianceMgt.HeartbeatReport:input_type -> api.appliance.HeartbeatReportReq
	6,  // 19: api.appliance.ApplianceMgt.Enroll:output_type -> api.appliance.ApplianceEnrollRes
	12, // 20: api.appliance.ApplianceMgt.Heartbeat:output_type -> api.appliance.HeartbeatRes
	15, // 21: api.appliance.ApplianceMgt.GetConfig:output_type -> api.appliance.Configs
	18, // 22: api.appliance.ApplianceMgt.SetUpgradeStatus:output_type -> api.appliance.SetUpgradeStatusRes
	20, // 23: api.appliance.ApplianceMgt.TaskReport:output_type -> api.appliance.TaskReportResp
	10, // 24: api.appliance.ApplianceMgt.HeartbeatReport:output_type -> api.appliance.HeartbeatReportRes
	19, // [19:25] is the sub-list for method output_type
	13, // [13:19] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_appliance_v1_appliance_mgt_proto_init() }
func file_appliance_v1_appliance_mgt_proto_init() {
	if File_appliance_v1_appliance_mgt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_appliance_v1_appliance_mgt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplianceEnrollReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplianceEnrollRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatReportRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CfgSurvey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Configs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CfgResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUpgradeStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUpgradeStatusRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_appliance_v1_appliance_mgt_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskReportResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_appliance_v1_appliance_mgt_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_appliance_v1_appliance_mgt_proto_goTypes,
		DependencyIndexes: file_appliance_v1_appliance_mgt_proto_depIdxs,
		EnumInfos:         file_appliance_v1_appliance_mgt_proto_enumTypes,
		MessageInfos:      file_appliance_v1_appliance_mgt_proto_msgTypes,
	}.Build()
	File_appliance_v1_appliance_mgt_proto = out.File
	file_appliance_v1_appliance_mgt_proto_rawDesc = nil
	file_appliance_v1_appliance_mgt_proto_goTypes = nil
	file_appliance_v1_appliance_mgt_proto_depIdxs = nil
}
