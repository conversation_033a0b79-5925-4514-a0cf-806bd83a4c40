// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/dlp_conf.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Symbols defined in public import of google/protobuf/timestamp.proto.

type Timestamp = timestamppb.Timestamp

// DdrSource 数据来源配置
// 将所有来源类型的数据返回在了一起,通过不同的类型取字段即可
type DdrSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 来源id
	SourceId string `protobuf:"bytes,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// 来源名称
	SourceName string `protobuf:"bytes,2,opt,name=source_name,json=sourceName,proto3" json:"source_name,omitempty"`
	// 来源类型枚举(web/vcs/software)
	SourceType string `protobuf:"bytes,3,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	// 来源优先级(扩展字段,当前默认0)
	SourcePriority uint32 `protobuf:"varint,4,opt,name=source_priority,json=sourcePriority,proto3" json:"source_priority,omitempty"`
	// 来源web信息
	SourceWeb []*SourceWeb `protobuf:"bytes,6,rep,name=source_web,json=sourceWeb,proto3" json:"source_web,omitempty"`
	// 来源软件信息
	SourceSoftware []*SourceSoftware `protobuf:"bytes,7,rep,name=source_software,json=sourceSoftware,proto3" json:"source_software,omitempty"`
	// vcs地址  对应vcs来源类型
	VcsUrl string `protobuf:"bytes,8,opt,name=vcs_url,json=vcsUrl,proto3" json:"vcs_url,omitempty"`
	// 状态 1-启用 2-禁用
	Status uint32 `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DdrSource) Reset() {
	*x = DdrSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_dlp_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DdrSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DdrSource) ProtoMessage() {}

func (x *DdrSource) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_dlp_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DdrSource.ProtoReflect.Descriptor instead.
func (*DdrSource) Descriptor() ([]byte, []int) {
	return file_conf_v1_dlp_conf_proto_rawDescGZIP(), []int{0}
}

func (x *DdrSource) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *DdrSource) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

func (x *DdrSource) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

func (x *DdrSource) GetSourcePriority() uint32 {
	if x != nil {
		return x.SourcePriority
	}
	return 0
}

func (x *DdrSource) GetSourceWeb() []*SourceWeb {
	if x != nil {
		return x.SourceWeb
	}
	return nil
}

func (x *DdrSource) GetSourceSoftware() []*SourceSoftware {
	if x != nil {
		return x.SourceSoftware
	}
	return nil
}

func (x *DdrSource) GetVcsUrl() string {
	if x != nil {
		return x.VcsUrl
	}
	return ""
}

func (x *DdrSource) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type SourceWeb struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// web地址 对应web来源类型
	UrlAddr string `protobuf:"bytes,5,opt,name=url_addr,json=urlAddr,proto3" json:"url_addr,omitempty"`
	// web端口 对应web来源类型
	UrlPort string `protobuf:"bytes,6,opt,name=url_port,json=urlPort,proto3" json:"url_port,omitempty"`
	// web路径 对应web来源类型
	UrlRoute string `protobuf:"bytes,7,opt,name=url_route,json=urlRoute,proto3" json:"url_route,omitempty"`
}

func (x *SourceWeb) Reset() {
	*x = SourceWeb{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_dlp_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceWeb) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceWeb) ProtoMessage() {}

func (x *SourceWeb) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_dlp_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceWeb.ProtoReflect.Descriptor instead.
func (*SourceWeb) Descriptor() ([]byte, []int) {
	return file_conf_v1_dlp_conf_proto_rawDescGZIP(), []int{1}
}

func (x *SourceWeb) GetUrlAddr() string {
	if x != nil {
		return x.UrlAddr
	}
	return ""
}

func (x *SourceWeb) GetUrlPort() string {
	if x != nil {
		return x.UrlPort
	}
	return ""
}

func (x *SourceWeb) GetUrlRoute() string {
	if x != nil {
		return x.UrlRoute
	}
	return ""
}

type SourceSoftware struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 软件名称  对应software来源类型
	SoftwareName string `protobuf:"bytes,8,opt,name=software_name,json=softwareName,proto3" json:"software_name,omitempty"`
	// 进程名称(以逗号分割多个)  对应software来源类型
	ProcessName string `protobuf:"bytes,9,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	// 包含目录
	IncludeFilePath []string `protobuf:"bytes,10,rep,name=include_file_path,json=includeFilePath,proto3" json:"include_file_path,omitempty"`
	// 排除目录
	ExcludeFilePath []string `protobuf:"bytes,11,rep,name=exclude_file_path,json=excludeFilePath,proto3" json:"exclude_file_path,omitempty"`
}

func (x *SourceSoftware) Reset() {
	*x = SourceSoftware{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_dlp_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceSoftware) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceSoftware) ProtoMessage() {}

func (x *SourceSoftware) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_dlp_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceSoftware.ProtoReflect.Descriptor instead.
func (*SourceSoftware) Descriptor() ([]byte, []int) {
	return file_conf_v1_dlp_conf_proto_rawDescGZIP(), []int{2}
}

func (x *SourceSoftware) GetSoftwareName() string {
	if x != nil {
		return x.SoftwareName
	}
	return ""
}

func (x *SourceSoftware) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *SourceSoftware) GetIncludeFilePath() []string {
	if x != nil {
		return x.IncludeFilePath
	}
	return nil
}

func (x *SourceSoftware) GetExcludeFilePath() []string {
	if x != nil {
		return x.ExcludeFilePath
	}
	return nil
}

// SensitiveStrategy 敏感数据策略
type SensitiveStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 规则Id
	RuleId string `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	// 规则名称
	RuleName string `protobuf:"bytes,2,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// 规则优先级(扩展字段,当前默认0)
	RulePriority uint32 `protobuf:"varint,3,opt,name=rule_priority,json=rulePriority,proto3" json:"rule_priority,omitempty"`
	// 规则等级(1,2,3,4)
	SensitiveLevel uint32 `protobuf:"varint,4,opt,name=sensitive_level,json=sensitiveLevel,proto3" json:"sensitive_level,omitempty"`
	// 检查加密文件(1是;2否)
	CheckFileEncrypted uint32 `protobuf:"varint,5,opt,name=check_file_encrypted,json=checkFileEncrypted,proto3" json:"check_file_encrypted,omitempty"`
	// 检查文件隐藏(1是;2否)
	CheckFileSuffix uint32 `protobuf:"varint,6,opt,name=check_file_suffix,json=checkFileSuffix,proto3" json:"check_file_suffix,omitempty"`
	// 最小文件大小
	MinFileSize int32 `protobuf:"varint,7,opt,name=min_file_size,json=minFileSize,proto3" json:"min_file_size,omitempty"`
	// 最大文件大小
	MaxFileSize int32 `protobuf:"varint,8,opt,name=max_file_size,json=maxFileSize,proto3" json:"max_file_size,omitempty"`
	// 最小文件大小 单位(kb/mb)
	MinFileSizeUnit string `protobuf:"bytes,9,opt,name=min_file_size_unit,json=minFileSizeUnit,proto3" json:"min_file_size_unit,omitempty"`
	// 最大文件大小 单位(kb/mb)
	MaxFileSizeUnit string `protobuf:"bytes,10,opt,name=max_file_size_unit,json=maxFileSizeUnit,proto3" json:"max_file_size_unit,omitempty"`
	// 文件类型(数组,存储文件类型的code) [0]标识所有文件
	FileTypeCode []uint32 `protobuf:"varint,11,rep,packed,name=file_type_code,json=fileTypeCode,proto3" json:"file_type_code,omitempty"`
	// 规则匹配符(and;or)
	RuleOperator string `protobuf:"bytes,12,opt,name=rule_operator,json=ruleOperator,proto3" json:"rule_operator,omitempty"`
	// 文件名称匹配符(and;or)
	FilenameOperator string `protobuf:"bytes,13,opt,name=filename_operator,json=filenameOperator,proto3" json:"filename_operator,omitempty"`
	// 文件名匹配规则列表
	FilenameRule []*SensitiveElementRule `protobuf:"bytes,14,rep,name=filename_rule,json=filenameRule,proto3" json:"filename_rule,omitempty"`
	// 文件内容匹配符(and;or)
	ContentOperator string `protobuf:"bytes,15,opt,name=content_operator,json=contentOperator,proto3" json:"content_operator,omitempty"`
	// 文件内容匹配规则列表
	ContentRule []*SensitiveElementRule `protobuf:"bytes,16,rep,name=content_rule,json=contentRule,proto3" json:"content_rule,omitempty"`
	// 数据规则类型id
	CategoryId string `protobuf:"bytes,17,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 数据规则内容
	Category string `protobuf:"bytes,18,opt,name=category,proto3" json:"category,omitempty"`
	// 来源id
	SourceId []string `protobuf:"bytes,19,rep,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// 识别方式
	IdentifyWay []uint32 `protobuf:"varint,20,rep,packed,name=identify_way,json=identifyWay,proto3" json:"identify_way,omitempty"`
	// 是否开启 (1启用;2禁用)
	Enable uint32 `protobuf:"varint,21,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *SensitiveStrategy) Reset() {
	*x = SensitiveStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_dlp_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensitiveStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensitiveStrategy) ProtoMessage() {}

func (x *SensitiveStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_dlp_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensitiveStrategy.ProtoReflect.Descriptor instead.
func (*SensitiveStrategy) Descriptor() ([]byte, []int) {
	return file_conf_v1_dlp_conf_proto_rawDescGZIP(), []int{3}
}

func (x *SensitiveStrategy) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *SensitiveStrategy) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *SensitiveStrategy) GetRulePriority() uint32 {
	if x != nil {
		return x.RulePriority
	}
	return 0
}

func (x *SensitiveStrategy) GetSensitiveLevel() uint32 {
	if x != nil {
		return x.SensitiveLevel
	}
	return 0
}

func (x *SensitiveStrategy) GetCheckFileEncrypted() uint32 {
	if x != nil {
		return x.CheckFileEncrypted
	}
	return 0
}

func (x *SensitiveStrategy) GetCheckFileSuffix() uint32 {
	if x != nil {
		return x.CheckFileSuffix
	}
	return 0
}

func (x *SensitiveStrategy) GetMinFileSize() int32 {
	if x != nil {
		return x.MinFileSize
	}
	return 0
}

func (x *SensitiveStrategy) GetMaxFileSize() int32 {
	if x != nil {
		return x.MaxFileSize
	}
	return 0
}

func (x *SensitiveStrategy) GetMinFileSizeUnit() string {
	if x != nil {
		return x.MinFileSizeUnit
	}
	return ""
}

func (x *SensitiveStrategy) GetMaxFileSizeUnit() string {
	if x != nil {
		return x.MaxFileSizeUnit
	}
	return ""
}

func (x *SensitiveStrategy) GetFileTypeCode() []uint32 {
	if x != nil {
		return x.FileTypeCode
	}
	return nil
}

func (x *SensitiveStrategy) GetRuleOperator() string {
	if x != nil {
		return x.RuleOperator
	}
	return ""
}

func (x *SensitiveStrategy) GetFilenameOperator() string {
	if x != nil {
		return x.FilenameOperator
	}
	return ""
}

func (x *SensitiveStrategy) GetFilenameRule() []*SensitiveElementRule {
	if x != nil {
		return x.FilenameRule
	}
	return nil
}

func (x *SensitiveStrategy) GetContentOperator() string {
	if x != nil {
		return x.ContentOperator
	}
	return ""
}

func (x *SensitiveStrategy) GetContentRule() []*SensitiveElementRule {
	if x != nil {
		return x.ContentRule
	}
	return nil
}

func (x *SensitiveStrategy) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *SensitiveStrategy) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *SensitiveStrategy) GetSourceId() []string {
	if x != nil {
		return x.SourceId
	}
	return nil
}

func (x *SensitiveStrategy) GetIdentifyWay() []uint32 {
	if x != nil {
		return x.IdentifyWay
	}
	return nil
}

func (x *SensitiveStrategy) GetEnable() uint32 {
	if x != nil {
		return x.Enable
	}
	return 0
}

// SensitiveElementRule 文件名/文件内容敏感规则
type SensitiveElementRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 计数count,operator为count时生效
	Count int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// 判断操作(满足部分count / 满足任一or / 满足所有 and)
	Operator string `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	// 敏感元素code
	SensitiveElementCodes []uint32 `protobuf:"varint,3,rep,packed,name=sensitive_element_codes,json=sensitiveElementCodes,proto3" json:"sensitive_element_codes,omitempty"`
}

func (x *SensitiveElementRule) Reset() {
	*x = SensitiveElementRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_dlp_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SensitiveElementRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensitiveElementRule) ProtoMessage() {}

func (x *SensitiveElementRule) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_dlp_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensitiveElementRule.ProtoReflect.Descriptor instead.
func (*SensitiveElementRule) Descriptor() ([]byte, []int) {
	return file_conf_v1_dlp_conf_proto_rawDescGZIP(), []int{4}
}

func (x *SensitiveElementRule) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SensitiveElementRule) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *SensitiveElementRule) GetSensitiveElementCodes() []uint32 {
	if x != nil {
		return x.SensitiveElementCodes
	}
	return nil
}

// DlpRule DLP告警规则
type DlpRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 规则ID
	RuleId string `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	// 规则名称
	RuleName string `protobuf:"bytes,2,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// 启用行为分析(1启用;2禁用)
	EnableAnalysis uint32 `protobuf:"varint,3,opt,name=enable_analysis,json=enableAnalysis,proto3" json:"enable_analysis,omitempty"`
	// 外发通道(数组) [“0”]标识所有分组
	ChannelTypes []string `protobuf:"bytes,4,rep,name=channel_types,json=channelTypes,proto3" json:"channel_types,omitempty"`
	// 敏感数据等级 [0]标识所有等级
	SensitiveIds []string `protobuf:"bytes,5,rep,name=sensitive_ids,json=sensitiveIds,proto3" json:"sensitive_ids,omitempty"`
	// 敏感数据类型(数组) [0]标识所有数据
	SensitiveLevel []int64 `protobuf:"varint,6,rep,packed,name=sensitive_level,json=sensitiveLevel,proto3" json:"sensitive_level,omitempty"`
	// 告警生效时间
	Time *TimeSequence `protobuf:"bytes,7,opt,name=time,proto3" json:"time,omitempty"`
	// 告警类型(1代码/2数据)
	AlertType uint32 `protobuf:"varint,8,opt,name=alert_type,json=alertType,proto3" json:"alert_type,omitempty"`
	// git包含逻辑(1包含/2不包含)
	GitContainOption uint32 `protobuf:"varint,9,opt,name=git_contain_option,json=gitContainOption,proto3" json:"git_contain_option,omitempty"`
	// git_path
	GitPath []string `protobuf:"bytes,10,rep,name=git_path,json=gitPath,proto3" json:"git_path,omitempty"`
	// svn包含逻辑(1包含/2不包含)
	SvnContainOption uint32 `protobuf:"varint,11,opt,name=svn_contain_option,json=svnContainOption,proto3" json:"svn_contain_option,omitempty"`
	// svn_path
	SvnPath []string `protobuf:"bytes,12,rep,name=svn_path,json=svnPath,proto3" json:"svn_path,omitempty"`
	// 处置动作 1审批/2阻断/3放通
	DisposeAction uint32 `protobuf:"varint,13,opt,name=dispose_action,json=disposeAction,proto3" json:"dispose_action,omitempty"`
	// 规则优先级(扩展字段,当前默认0)
	RulePriority uint32 `protobuf:"varint,14,opt,name=rule_priority,json=rulePriority,proto3" json:"rule_priority,omitempty"`
	// 是否开启 (1启用;2禁用)
	Enable uint32 `protobuf:"varint,15,opt,name=enable,proto3" json:"enable,omitempty"`
	// 敏感数据分类["0"]标识所有数据
	SensitiveCategory []string `protobuf:"bytes,16,rep,name=sensitive_category,json=sensitiveCategory,proto3" json:"sensitive_category,omitempty"`
	// 引用的消息通知模板ID
	NotificationId     string `protobuf:"bytes,17,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	EnableNotification bool   `protobuf:"varint,18,opt,name=enable_notification,json=enableNotification,proto3" json:"enable_notification,omitempty"`
}

func (x *DlpRule) Reset() {
	*x = DlpRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_dlp_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DlpRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DlpRule) ProtoMessage() {}

func (x *DlpRule) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_dlp_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DlpRule.ProtoReflect.Descriptor instead.
func (*DlpRule) Descriptor() ([]byte, []int) {
	return file_conf_v1_dlp_conf_proto_rawDescGZIP(), []int{5}
}

func (x *DlpRule) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *DlpRule) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *DlpRule) GetEnableAnalysis() uint32 {
	if x != nil {
		return x.EnableAnalysis
	}
	return 0
}

func (x *DlpRule) GetChannelTypes() []string {
	if x != nil {
		return x.ChannelTypes
	}
	return nil
}

func (x *DlpRule) GetSensitiveIds() []string {
	if x != nil {
		return x.SensitiveIds
	}
	return nil
}

func (x *DlpRule) GetSensitiveLevel() []int64 {
	if x != nil {
		return x.SensitiveLevel
	}
	return nil
}

func (x *DlpRule) GetTime() *TimeSequence {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *DlpRule) GetAlertType() uint32 {
	if x != nil {
		return x.AlertType
	}
	return 0
}

func (x *DlpRule) GetGitContainOption() uint32 {
	if x != nil {
		return x.GitContainOption
	}
	return 0
}

func (x *DlpRule) GetGitPath() []string {
	if x != nil {
		return x.GitPath
	}
	return nil
}

func (x *DlpRule) GetSvnContainOption() uint32 {
	if x != nil {
		return x.SvnContainOption
	}
	return 0
}

func (x *DlpRule) GetSvnPath() []string {
	if x != nil {
		return x.SvnPath
	}
	return nil
}

func (x *DlpRule) GetDisposeAction() uint32 {
	if x != nil {
		return x.DisposeAction
	}
	return 0
}

func (x *DlpRule) GetRulePriority() uint32 {
	if x != nil {
		return x.RulePriority
	}
	return 0
}

func (x *DlpRule) GetEnable() uint32 {
	if x != nil {
		return x.Enable
	}
	return 0
}

func (x *DlpRule) GetSensitiveCategory() []string {
	if x != nil {
		return x.SensitiveCategory
	}
	return nil
}

func (x *DlpRule) GetNotificationId() string {
	if x != nil {
		return x.NotificationId
	}
	return ""
}

func (x *DlpRule) GetEnableNotification() bool {
	if x != nil {
		return x.EnableNotification
	}
	return false
}

// 时间序列
type TimeSequence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否全选生效 (1全选;2不全选)
	All uint32 `protobuf:"varint,1,opt,name=all,proto3" json:"all,omitempty"`
	// 数组 选中为"1" 不选中为"0"
	Rule []string `protobuf:"bytes,2,rep,name=rule,proto3" json:"rule,omitempty"`
}

func (x *TimeSequence) Reset() {
	*x = TimeSequence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_dlp_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeSequence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeSequence) ProtoMessage() {}

func (x *TimeSequence) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_dlp_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeSequence.ProtoReflect.Descriptor instead.
func (*TimeSequence) Descriptor() ([]byte, []int) {
	return file_conf_v1_dlp_conf_proto_rawDescGZIP(), []int{6}
}

func (x *TimeSequence) GetAll() uint32 {
	if x != nil {
		return x.All
	}
	return 0
}

func (x *TimeSequence) GetRule() []string {
	if x != nil {
		return x.Rule
	}
	return nil
}

var File_conf_v1_dlp_conf_proto protoreflect.FileDescriptor

var file_conf_v1_dlp_conf_proto_rawDesc = []byte{
	0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x6c, 0x70, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xbb, 0x02, 0x0a, 0x09, 0x44, 0x64, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x32, 0x0a, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x57,
	0x65, 0x62, 0x52, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x57, 0x65, 0x62, 0x12, 0x41, 0x0a,
	0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x76, 0x63, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x76, 0x63, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x5e, 0x0a, 0x09, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x57, 0x65, 0x62, 0x12, 0x19,
	0x0a, 0x08, 0x75, 0x72, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x75, 0x72, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x72, 0x6c,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x72, 0x6c,
	0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x72, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x72, 0x6c, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x22, 0xb0, 0x01, 0x0a, 0x0e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x22, 0xd7, 0x06, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x30,
	0x0a, 0x14, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x12, 0x22, 0x0a, 0x0d,
	0x6d, 0x69, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x46, 0x69, 0x6c, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x55, 0x6e, 0x69,
	0x74, 0x12, 0x2b, 0x0a, 0x12, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d,
	0x61, 0x78, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x24,
	0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x75, 0x6c,
	0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x66, 0x69, 0x6c,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x43, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0c, 0x66,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0b, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x5f,
	0x77, 0x61, 0x79, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x79, 0x57, 0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x80,
	0x01, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x17, 0x73, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x15, 0x73, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x73, 0x22, 0xa5, 0x05, 0x0a, 0x07, 0x44, 0x6c, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x49, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x2a, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x67, 0x69,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x67, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x69, 0x74, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x67, 0x69, 0x74, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x76, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x10, 0x73, 0x76, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x76, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x76, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x0e,
	0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x75, 0x6c, 0x65,
	0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x2d, 0x0a, 0x12, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x73, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x27, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x13, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x0c, 0x54, 0x69, 0x6d,
	0x65, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6c, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x6c, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x42,
	0x29, 0x5a, 0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73,
	0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x50, 0x00, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_dlp_conf_proto_rawDescOnce sync.Once
	file_conf_v1_dlp_conf_proto_rawDescData = file_conf_v1_dlp_conf_proto_rawDesc
)

func file_conf_v1_dlp_conf_proto_rawDescGZIP() []byte {
	file_conf_v1_dlp_conf_proto_rawDescOnce.Do(func() {
		file_conf_v1_dlp_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_dlp_conf_proto_rawDescData)
	})
	return file_conf_v1_dlp_conf_proto_rawDescData
}

var file_conf_v1_dlp_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_conf_v1_dlp_conf_proto_goTypes = []interface{}{
	(*DdrSource)(nil),            // 0: api.conf.DdrSource
	(*SourceWeb)(nil),            // 1: api.conf.SourceWeb
	(*SourceSoftware)(nil),       // 2: api.conf.SourceSoftware
	(*SensitiveStrategy)(nil),    // 3: api.conf.SensitiveStrategy
	(*SensitiveElementRule)(nil), // 4: api.conf.SensitiveElementRule
	(*DlpRule)(nil),              // 5: api.conf.DlpRule
	(*TimeSequence)(nil),         // 6: api.conf.TimeSequence
}
var file_conf_v1_dlp_conf_proto_depIdxs = []int32{
	1, // 0: api.conf.DdrSource.source_web:type_name -> api.conf.SourceWeb
	2, // 1: api.conf.DdrSource.source_software:type_name -> api.conf.SourceSoftware
	4, // 2: api.conf.SensitiveStrategy.filename_rule:type_name -> api.conf.SensitiveElementRule
	4, // 3: api.conf.SensitiveStrategy.content_rule:type_name -> api.conf.SensitiveElementRule
	6, // 4: api.conf.DlpRule.time:type_name -> api.conf.TimeSequence
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_conf_v1_dlp_conf_proto_init() }
func file_conf_v1_dlp_conf_proto_init() {
	if File_conf_v1_dlp_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_dlp_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DdrSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_dlp_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceWeb); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_dlp_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceSoftware); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_dlp_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensitiveStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_dlp_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SensitiveElementRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_dlp_conf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DlpRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_dlp_conf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeSequence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_dlp_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_dlp_conf_proto_goTypes,
		DependencyIndexes: file_conf_v1_dlp_conf_proto_depIdxs,
		MessageInfos:      file_conf_v1_dlp_conf_proto_msgTypes,
	}.Build()
	File_conf_v1_dlp_conf_proto = out.File
	file_conf_v1_dlp_conf_proto_rawDesc = nil
	file_conf_v1_dlp_conf_proto_goTypes = nil
	file_conf_v1_dlp_conf_proto_depIdxs = nil
}
