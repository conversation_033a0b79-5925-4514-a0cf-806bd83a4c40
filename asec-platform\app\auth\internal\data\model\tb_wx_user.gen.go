// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbWxUser = "tb_wx_user"

// TbWxUser mapped from table <tb_wx_user>
type TbWxUser struct {
	WxCorpID         string    `gorm:"column:wx_corp_id;not null" json:"wx_corp_id"`
	LocalRootGroupID string    `gorm:"column:local_root_group_id;not null" json:"local_root_group_id"`
	LocalUserID      string    `gorm:"column:local_user_id;not null" json:"local_user_id"`
	MainDepartment   int64     `gorm:"column:main_department;not null" json:"main_department"`
	Userid           string    `gorm:"column:userid" json:"userid"`
	Name             string    `gorm:"column:name" json:"name"`
	CreatedAt        time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbWxUser's table name
func (*TbWxUser) TableName() string {
	return TableNameTbWxUser
}
