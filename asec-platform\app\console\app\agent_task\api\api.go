package api

import (
	"asdsec.com/asec/platform/app/console/app/agent_task/dto"
	"asdsec.com/asec/platform/app/console/app/agent_task/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateAgentTask godoc
// @Summary 创建agent_task
// @Schemes
// @Description 创建agent_task
// @Tags        Logs
// @Produce     application/json
// @Param       req body dto.ListAccessLogReq true "获取应用访问日志参数"
// @Success     200
// @Router      /v1/agent_task [POST]
// @success     200 {object} common.Response{data=model.Pagination} "ok"
func CreateAgentTask(c *gin.Context) {
	var req dto.CreateAgentTaskReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	err = service.GetAgentTaskService().CreateAgentTask(c, req)
	if err != nil {
		common.FailWithMessage(c, -1, "新增任务失败")
		return
	}
	common.Ok(c)
}

// GetAgentTaskDetail godoc
// @Summary 获取agent_task详细信息
// @Schemes
// @Description 获取agent_task详细信息
// @Tags        Logs
// @Produce     application/json
// @Param       true "获取agent_task信息"
// @Success     200
// @Router      /v1/agent_task/detail [GET]
// @success     200 {object} common.Response{data=model.Pagination} "ok"
func GetAgentTaskDetail(c *gin.Context) {
	id := c.Query("id")
	logs, err := service.GetAgentTaskService().GetAgentTaskDetail(c, id)
	if err != nil {
		common.FailWithMessage(c, -1, "查询失败")
		return
	}
	common.OkWithData(c, logs)
}

// GetAgentTaskList godoc
// @Summary 获取agent_task列表
// @Schemes
// @Description 获取agent_task列表
// @Tags        Logs
// @Produce     application/json
// @Param       true "获取agent_task列表"
// @Success     200
// @Router      /v1/agent_task/list [GET]
// @success     200 {object} common.Response{data=model.Pagination} "ok"
func GetAgentTaskList(c *gin.Context) {
	agentId := c.Query("agent_id")
	status := c.Query("status")
	logs, err := service.GetAgentTaskService().GetAgentTaskList(c, agentId, status)
	if err != nil {
		common.FailWithMessage(c, -1, "查询失败")
		return
	}
	common.OkWithData(c, logs)
}
