syntax = "proto3";

package api.accesslog;
import public "google/protobuf/timestamp.proto";
option go_package = "asdsec.com/asec/platform/api/accesslog/v1;v1";

service AccessLogService {
  // AccessLog 网关接入日志
  rpc AccessLog(stream AccessLogMsgList) returns (AccessLogReply);
}


// AccessLogMsg 释义见 tb_access_log字段
message AccessLogMsg{
  string src_ip = 1;
  uint32 src_port = 2;
  string dst_ip = 3;
  uint32 dst_port = 4;
  string protocol = 5;
  string destination_url = 6;
  uint32 status = 7;
  string deny_reason = 8;
  string access_user_id = 9;
  string access_username = 10;
  uint64 app_id = 11;
  string app_name = 12;
  uint64 strategy_id = 13;
  string strategy_name = 14;
  string iss = 15;
  google.protobuf.Timestamp access_time = 16;
  uint64 client_id = 17;
  string client_name = 18;
  // 策略命中时间(ms)
  uint32 strategy_check_time = 19;
  // tunserver出站IP (实际出站时使用的源IP，包括虚拟IP)
  string outbound_ip = 20;
}


// AccessLogMsgList AccessLogMsg集合用于批量发送
message AccessLogMsgList{
  repeated AccessLogMsg access_log_msg = 1;
  uint64 appliance_id = 2;
}

message AccessLogReply{
  enum StatusCode {
    SUCCESS = 0;
    FAILED = 1;
  }
  StatusCode status = 1;
}