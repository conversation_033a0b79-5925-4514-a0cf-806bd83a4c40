package healthCheck

import (
	pb "asdsec.com/asec/platform/api/application/v1"
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/pkg/apisix"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"google.golang.org/grpc"
	"sort"
	"strings"
	"sync"
)

// ========== 使用 common.Send 框架的上报服务 ==========

// 用于存储上一次的hash值
var (
	lastResultsHash string
	hashMutex       = &sync.Mutex{}
)

// 计算resultsMap的hash值
func calculateResultsHash(results map[string]int32) string {
	// 创建一个排序的键列表，确保hash计算的一致性
	keys := make([]string, 0, len(results))
	for k := range results {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建一个确定性的字符串表示
	var sb strings.Builder
	for _, k := range keys {
		sb.WriteString(k)
		sb.WriteString(":")
		sb.WriteString(fmt.Sprintf("%d", results[k]))
		sb.WriteString(";")
	}

	// 计算MD5 hash
	hash := md5.Sum([]byte(sb.String()))
	return hex.EncodeToString(hash[:])
}

// ========== 使用 common.Send 框架的上报服务 ==========

// StartHealthCheckResult 健康检查结果上报
func StartHealthCheckResult(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:          ctx,
		Wg:           wg,
		DoSendFunc:   ReportCheckResultPlat,
		RunType:      common.SimpleSend,
		WaitSecond:   60, // 每分钟上报一次
		RandomOffset: 2,
	}
	common.Send(param)
}

// ReportCheckResultPlat 上报
func ReportCheckResultPlat(conn *grpc.ClientConn, ctx context.Context) error {
	endpoint := fmt.Sprintf("%s%s", apisix.ApisixCheckRoute, apisix.HealthCheckResult)
	res, code, err := apisix.SendRequestToApisix(endpoint, apisix.GetMethod, apisix.XApisixKey, nil)
	if err != nil {
		global.Logger.Sugar().Errorf("send request to apisix failed.endpoint=%v, response_code=%d, reqres=%s,err=%v", endpoint, code, res, err)
		return err
	}
	var healthCheckResults []apisix.ApiHealthCheckResult
	if err := json.Unmarshal(res, &healthCheckResults); err != nil { //返回结果里面  Type 和name 是必有的，解析不出来的情况 应该是还没有配置的有健康检查的资源
		global.Logger.Sugar().Errorf("failed to unmarshal health check results: %v, res: %v", err, res)
		return nil
	}
	resultsMap := make(map[string]int32)
	// 使用解析后的数据
	for _, result := range healthCheckResults {
		appID := strings.Split(result.Name, "/")[len(strings.Split(result.Name, "/"))-1]

		// 解析 nodes 字段
		var nodes []apisix.ApiNode
		if len(result.Nodes) > 0 && string(result.Nodes) != "{}" {
			status := int32(1) // 默认为健康
			if err := json.Unmarshal(result.Nodes, &nodes); err != nil {
				global.Logger.Sugar().Errorf("failed to unmarshal nodes for %s: %v", result.Name, err)
				continue
			}
			for _, node := range nodes {
				if node.Status == "unhealthy" || node.Status == "mostly_unhealthy" {
					status = 2
					break
				}
			}
			resultsMap[appID] = status
		}

	}

	// 计算当前结果的hash值
	currentHash := calculateResultsHash(resultsMap)

	// 检查hash是否发生变化
	hashMutex.Lock()
	hashChanged := currentHash != lastResultsHash
	if hashChanged {
		lastResultsHash = currentHash
	}
	hashMutex.Unlock()

	// 只有当hash发生变化时才上报
	if hashChanged {
		client := pb.NewAppClient(conn)
		req := &pb.ReportHealthCheckResultReq{
			HealthResults: resultsMap,
		}
		_, err = client.ReportHealthCheckResult(ctx, req)
		if err != nil {
			global.Logger.Sugar().Errorf("上报健康检查结果失败: %v", err)
			// 如果上报失败，恢复之前的hash值，以便下次重试
			hashMutex.Lock()
			lastResultsHash = ""
			hashMutex.Unlock()
			return err
		}
		global.Logger.Sugar().Infof("成功上报资源健康检查结果，hash: %s", currentHash)
	} else {
		global.Logger.Sugar().Infof("健康检查结果未发生变化，跳过上报")
	}

	return nil
}
