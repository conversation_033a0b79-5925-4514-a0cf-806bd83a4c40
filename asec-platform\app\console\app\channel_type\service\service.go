package service

import (
	"asdsec.com/asec/platform/app/console/app/channel_type/dto"
	"asdsec.com/asec/platform/app/console/app/channel_type/repository"
	"asdsec.com/asec/platform/app/console/common"
	"asdsec.com/asec/platform/pkg/aerrors"
	pkgModel "asdsec.com/asec/platform/pkg/model"
	"asdsec.com/asec/platform/pkg/snowflake"
	pkgUtils "asdsec.com/asec/platform/pkg/utils"
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"
)

var ChannelTypeImpl ChannelTypeService

// ChannelTypeInit 单例对象
var ChannelTypeInit sync.Once

type ChannelTypeService interface {
	CreateChannelType(ctx context.Context, req dto.CreateChannelTypeReq) aerrors.AError
	UpdateChannelType(ctx context.Context, req dto.UpdateChannelTypeReq) aerrors.AError
	GetChannelTypeList(ctx context.Context, req dto.GetChannelTypeListReq) (dto.GetChannelTypeListRsp, error)
	GetChannelList(ctx context.Context, req dto.GetChannelListReq) (dto.GetChannelListRsp, error)
	DeleteChannel(ctx context.Context, id string) error
}

type channelTypeService struct {
	db repository.ChannelTypeRepository
}

const (
	CustomBuiltIn        = 2
	DefaultStatus        = 1
	FPid                 = "0"
	ChannelNameRepeatErr = "ChannelNameRepeatErr"
	ChannelRepeatErr     = "ChannelRepeatErr"
	CustomDefinePrefix   = "udef"
)

func (c channelTypeService) CreateChannelType(ctx context.Context, req dto.CreateChannelTypeReq) aerrors.AError {
	data, err := c.db.FindChannelByName(ctx, req.Name)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	if data.Id != "" {
		if req.Pid == FPid {
			return aerrors.New("channel name repeated.", ChannelNameRepeatErr)
		}
		return aerrors.New("channel repeated.", ChannelRepeatErr)
	}
	id, err := snowflake.Sf.GetId()
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}

	channel := pkgModel.ChannelType{
		Id:          strconv.FormatUint(id, 10),
		Pid:         req.Pid,
		Channel:     fmt.Sprintf("%s_%s", CustomDefinePrefix, req.Name),
		ChannelName: req.Name,
		CreateTime:  time.Now(),
		ProcessList: make([]string, 0),
		Status:      DefaultStatus,
		BuiltIn:     CustomBuiltIn,
	}
	channel.IncludeFilePath, channel.IncludeFilePathInfo, err = pkgUtils.ParseFilePathReq(req.IncludeFilePath)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	channel.ExcludeFilePath, channel.ExcludeFilePathInfo, err = pkgUtils.ParseFilePathReq(req.ExcludeFilePath)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}

	channel.ProcessList = append(channel.ProcessList, req.ProcessList...)
	err = c.db.CreateChannelType(ctx, channel)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	return nil
}

func (c channelTypeService) UpdateChannelType(ctx context.Context, req dto.UpdateChannelTypeReq) aerrors.AError {
	data, err := c.db.FindChannelByName(ctx, req.Name)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	if data.Id != "" && data.Id != req.Id {
		return aerrors.New("channel name repeated.", ChannelNameRepeatErr)
	}
	channel := pkgModel.ChannelType{
		Id:          req.Id,
		Pid:         req.Pid,
		Channel:     fmt.Sprintf("%s_%s", CustomDefinePrefix, req.Name),
		ChannelName: req.Name,
		ProcessList: make([]string, 0),
		Status:      req.Status,
	}
	channel.ProcessList = append(channel.ProcessList, req.ProcessList...)
	channel.IncludeFilePath, channel.IncludeFilePathInfo, err = pkgUtils.ParseFilePathReq(req.IncludeFilePath)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	channel.ExcludeFilePath, channel.ExcludeFilePathInfo, err = pkgUtils.ParseFilePathReq(req.ExcludeFilePath)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	err = c.db.UpdateChannelType(ctx, channel)
	if err != nil {
		return aerrors.ReturnWithError(err, common.OperateError)
	}
	return nil
}

func (c channelTypeService) GetChannelTypeList(ctx context.Context, req dto.GetChannelTypeListReq) (dto.GetChannelTypeListRsp, error) {
	return c.db.GetChannelTypeList(ctx, req)
}

func (c channelTypeService) GetChannelList(ctx context.Context, req dto.GetChannelListReq) (dto.GetChannelListRsp, error) {
	return c.db.GetChannelList(ctx, req)
}

func (c channelTypeService) DeleteChannel(ctx context.Context, id string) error {
	return c.db.DeleteChannel(ctx, id)
}

func GetChannelTypeService() ChannelTypeService {
	ChannelTypeInit.Do(func() {
		ChannelTypeImpl = &channelTypeService{db: repository.NewChannelTypeRepository()}
	})
	return ChannelTypeImpl
}
