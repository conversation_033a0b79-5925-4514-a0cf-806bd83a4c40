package service

import (
	"context"
	"net/url"
	"time"

	"github.com/google/uuid"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/jinzhu/copier"

	v1 "asdsec.com/asec/platform/api/auth/v1"
	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"github.com/go-kratos/kratos/v2/log"

	pb "asdsec.com/asec/platform/api/auth/v1/user"
)

type UserService struct {
	pb.UnimplementedUserServer
	auth *biz.AuthUsecase
	user *biz.UserUsecase

	log *log.Helper
}

func NewUserService(auth *biz.AuthUsecase, user *biz.UserUsecase, logger log.Logger) *UserService {
	return &UserService{
		auth: auth,
		user: user,

		log: log.NewHelper(logger),
	}
}

const AsecCodeName = "asec_code"

func (s *UserService) RedirectVerify(ctx context.Context, req *pb.RedirectVerifyRequest) (*pb.RedirectVerifyReply, error) {
	code, err := s.auth.RedirectVerify(ctx)
	if err != nil {
		s.log.Errorf("Generate auth code failed. err=%v", err)
		return &pb.RedirectVerifyReply{}, err
	}
	redirectUrl, err := url.Parse(req.RedirectUrl)
	if err != nil {
		s.log.Errorf("Parse redirect url failed. err=%v", err)
		return &pb.RedirectVerifyReply{}, err
	}
	//将code作为url的参数加到重定向应用地址上
	queryParams := redirectUrl.Query()
	queryParams.Set(AsecCodeName, code)
	redirectUrl.RawQuery = queryParams.Encode()
	return &pb.RedirectVerifyReply{Url: redirectUrl.String()}, nil
}

func (s *UserService) GetLoginUserInfo(ctx context.Context, req *pb.GetLoginUserInfoReq) (*pb.GetLoginUserInfoReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		s.log.Errorf("GetCorpId failed. err=%v", err)
		return &pb.GetLoginUserInfoReply{}, v1.ErrorParamError("param err")
	}
	userId, err := common.GetUserId(ctx)
	if err != nil {
		s.log.Errorf("GetUserId failed. err=%v", err)
		return &pb.GetLoginUserInfoReply{}, v1.ErrorParamError("param err")
	}

	user, err := s.user.GetLoginUserInfo(ctx, corpId, userId)
	if err != nil {
		return &pb.GetLoginUserInfoReply{}, err
	}
	var result pb.GetLoginUserInfoReply_LoginUserInfo
	if err := copier.Copy(&result, &user); err != nil {
		s.log.Errorf("get user copier err: %v", err)
		return &pb.GetLoginUserInfoReply{}, err
	}
	result.Id = user.ID
	result.GroupId = user.GroupID
	result.SourceId = user.SourceID
	result.AuthType = user.AuthType
	if !user.ExpireEnd.IsZero() && !(user.ExpireEnd.Local().Format(dto.CommonTimeFormat) == dto.TimeZero) {
		result.ExpireEnd = user.ExpireEnd.Local().Format(dto.CommonTimeFormat)
	}
	for _, r := range user.Roles {
		result.Roles = append(result.Roles, &pb.RoleInfo{
			Id:   r.ID,
			Name: r.Name,
		})
	}
	return &pb.GetLoginUserInfoReply{UserInfo: &result}, nil
}

func (s *UserService) GetPublicKey(ctx context.Context, corpId string) (any, error) {
	return s.auth.GetPublicKeyOfCorp(ctx, corpId)
}

func (s *UserService) GetAdminPublicKey(ctx context.Context) (any, error) {
	return s.auth.GetAdminEcdsaPublicKey(ctx)
}

func (s *UserService) UpdatePasswd(ctx context.Context, req *pb.UpdatePasswordReq) (*pb.UpdatePasswordReply, error) {
	var param dto.UpdateCredParam
	if err := copier.Copy(&param, req); err != nil {
		return &pb.UpdatePasswordReply{}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdatePasswordReply{}, err
	}
	userId, err := common.GetUserId(ctx)
	if err != nil {
		return &pb.UpdatePasswordReply{}, err
	}

	param.CorpId = corpId
	param.UserId = userId
	param.CredType = dto.CredTypePassword

	if err := s.user.UpdateCredential(ctx, param); err != nil {
		return &pb.UpdatePasswordReply{}, err
	}
	return &pb.UpdatePasswordReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *UserService) Logout(ctx context.Context, req *pb.LogoutReq) (*pb.LogoutReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.LogoutReply{Status: pb.StatusCode_FAILED}, err
	}
	userName, err := common.GetUserName(ctx)
	if err != nil {
		return &pb.LogoutReply{Status: pb.StatusCode_FAILED}, err
	}
	token, err := common.GetToken(ctx)
	if err != nil {
		return &pb.LogoutReply{Status: pb.StatusCode_FAILED}, err
	}

	// 登录日志
	defer func() {
		errMsg := ""
		loginLogType := dto.LoginOutLogType
		loginLogParam := dto.CreateLoginLogParam{
			Id:        uuid.New().String(),
			CorpId:    corpId,
			Error:     errMsg,
			IpAddress: common.GetClientHost(ctx),
			Type:      loginLogType,
			ClientId:  dto.AuthLoginLog,
			EventTime: time.Now().UnixMilli(),
			UserName:  userName,
		}
		err = s.auth.CreateUserLoginLog(ctx, loginLogParam)
		if err != nil {
			s.log.Errorf("CreateLoginLogFailed err=%v", err)
			return
		}
	}()

	// 执行登出
	redirectURL, err := s.auth.Logout(ctx, corpId, token)
	if err != nil {
		return &pb.LogoutReply{Status: pb.StatusCode_FAILED}, err
	}

	// 如果有重定向URL，返回URL
	if redirectURL != "" {
		return &pb.LogoutReply{
			Status:      pb.StatusCode_SUCCESS,
			RedirectUrl: redirectURL, // 直接在响应中返回URL
		}, nil
	}

	return &pb.LogoutReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *UserService) CheckToken(ctx context.Context, token, authType string) (bool, error) {
	return s.auth.CheckTokenOfRedisByType(ctx, token, authType)
}

func (s *UserService) CheckUserIdExist(ctx context.Context, userId string, userType string) (bool, error) {
	return s.auth.CheckUserIdExist(ctx, userId, userType)
}
