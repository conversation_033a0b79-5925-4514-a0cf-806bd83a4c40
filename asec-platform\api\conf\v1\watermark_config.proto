syntax = "proto3";

package api.conf;
option go_package = "asdsec.com/asec/platform/api/conf/v1;v1";

message WatermarkContent{
  repeated string content_type = 1;
  string custom = 2;
}

message WatermarkConfig{
  bool watermark_switch = 1;
  WatermarkContent watermark_content = 2;
  string watermark_color = 3;
  int32 watermark_angle = 4;
  uint32 watermark_alpha = 5;
  uint32 watermark_font_size = 6;
  uint32 watermark_row_spacing = 7;
  uint32 watermark_col_spacing = 8;
}