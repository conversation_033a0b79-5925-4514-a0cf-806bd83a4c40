package api

import (
	"asdsec.com/asec/platform/app/console/app/alert_event/model"
	"asdsec.com/asec/platform/app/console/app/alert_event/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/app/console/utils/excel"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetAlertEventTotal godoc
// @Summary 告警事件列表,根据告警事件类型返回对应分组
// @Schemes
// @Description 根据时间返回告警事件列表
// @Tags        alert
// @Produce     application/json
// @Param       req body model.GetAlertEventReq true "获取告警事件列表"
// @Success     200
// @Router      /v1/alert/event/total [POST]
// @success     200 {object} common.Response{data=model.GetAlertEventRsp} "ok"
func GetAlertEventTotal(c *gin.Context) {
	req := model.GetAlertEventReq{}
	err := c.Should<PERSON>ind<PERSON>(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	alertEvents, err := service.GetAlertEventService().GetAlertEventTotal(c, req)

	if err != nil {
		global.SysLog.Error("GetAlertEvent err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, alertEvents)
}

// GetAlertEventDetail godoc
// @Summary 查看告警事件详情接口
// @Schemes
// @Description 获取告警事件详情
// @Tags        alert_event
// @Produce     application/json
// @Param       id query string true "告警事件ID" - "获取告警事件详情"
// @Success     200
// @Router      /v1/alert/event/detail/{eventId} [GET]
// @success     200 {object} common.Response{data=[]model.GetAlertEventDetailResp} "ok"
func GetAlertEventDetail(c *gin.Context) {
	eventIdStr := c.Query("id")
	users, err := service.GetAlertEventService().GetAlertEventDetail(c, eventIdStr)
	if err != nil {
		global.SysLog.Error("GetUsers err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, users)
}

// GetAlertEventList godoc
// @Summary 获取告警事件列表
// @Schemes
// @Description 根据筛选条件获取告警事件列表
// @Tags        alert_event
// @Produce     application/json
// @Success     200
// @Router      /v1/alert/event/list [POST]
// @Param       req body model.GetAlertEventListReq true "获取告警事件列表和概述"
// @success     200 {object} common.Response{data=[]model.GetAlertEventListResp} "ok"
func GetAlertEventList(c *gin.Context) {
	req := model.GetAlertEventListReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	alertEventList, err := service.GetAlertEventService().GetAlertEventList(c, req)
	if err != nil {
		global.SysLog.Error("GetAlertEvent err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, alertEventList)
}

// Export godoc
// @Summary 导出告警事件列表
// @Schemes
// @Description 根据筛选条件导出告警事件列表
// @Tags        alert_event
// @Produce     application/json
// @Success     200
// @Router      /v1/alert/event/export [POST]
// @Param       req body model.GetAlertEventListReq true "导出告警事件列表和概述"
// @success     200 {object} common.Response{} "ok"
func Export(c *gin.Context) {
	var req model.GetAlertEventListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	title, searchCondition, events, err := service.GetAlertEventService().Export(c, req)
	if err != nil {
		global.SysLog.Error(err.Error())
		common.Fail(c, common.UnknownError)
		return
	}
	excel.ExportExcel(title, searchCondition, events, title, c, "", true)
	return
}
