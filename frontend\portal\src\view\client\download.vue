<template>
  <div>
    <div class="client">
      <base-main>
        <div class="download-container">
          <!-- 桌面端显示所有下载选项 -->
          <template v-if="!isMobile">
            <!-- Windows 客户端 -->
            <div
              class="download-card desktop-only"
              @click="download('windows')"
            >
              <svg class="icon window-show download-icon" aria-hidden="true">
                <use xlink:href="#icon-windows" />
              </svg>
              <svg class="icon window-hidden download-icon" aria-hidden="true">
                <use xlink:href="#icon-xiazai" />
              </svg>
              <br>
              <base-link class="window-show download-text" :underline="false">Windows客户端</base-link>
              <base-link class="window-hidden download-text" :underline="false">点击下载Windows客户端</base-link>
              <base-progress v-if="windowsloading" :percentage="downloadProgress.windows" :format="progressFormat" class="download-progress" />
            </div>

            <!-- Mac 客户端 -->
            <div
              class="download-card desktop-only"
              @click="download('darwin')"
            >
              <svg class="icon window-show download-icon" aria-hidden="true">
                <use xlink:href="#icon-mac" />
              </svg>
              <svg class="icon window-hidden download-icon" aria-hidden="true">
                <use xlink:href="#icon-xiazai" />
              </svg>
              <br>
              <base-link class="window-show download-text" :underline="false">Mac客户端</base-link>
              <base-link class="window-hidden download-text" :underline="false">点击下载Mac客户端</base-link>
              <base-progress v-if="macloading" :percentage="downloadProgress.darwin" :format="progressFormat" class="download-progress" />
            </div>
          </template>

          <!-- iOS 客户端 (所有设备显示) -->
          <div
            class="download-card ios-container"
            :class="{ 'loading': iosloading }"
            @click="isMobile ? download('ios') : null"
            @mousemove="!isMobile ? download('ios') : null"
            @mouseleave="hasExecuted = false"
          >
            <div v-if="iosloading" class="loading-overlay">
              <div class="loading-spinner">
                <div class="spinner" />
                <div class="loading-text">下载码生成中...</div>
              </div>
            </div>
            <svg class="icon window-show download-icon" aria-hidden="true">
              <use xlink:href="#icon-ios" />
            </svg>
            <br>
            <base-link class="window-show download-text" :underline="false">iOS客户端</base-link>
            <div id="ios" class="window-hidden qr-container">
              <canvas id="ioscanvas" class="qr-canvas" />
            </div>
          </div>

          <!-- Android 客户端 (所有设备显示) -->
          <div
            class="download-card android-container"
            :class="{ 'loading': androidloading }"
            @click="isMobile ? download('android') : null"
            @mousemove="!isMobile ? download('android') : null"
            @mouseleave="hasExecuted = false"
          >
            <div v-if="androidloading" class="loading-overlay">
              <div class="loading-spinner">
                <div class="spinner" />
                <div class="loading-text">下载码生成中...</div>
              </div>
            </div>
            <svg class="icon window-show download-icon" aria-hidden="true">
              <use xlink:href="#icon-android" />
            </svg>
            <br>
            <base-link class="window-show download-text" :underline="false">Android客户端</base-link>
            <div id="android" class="window-hidden qr-container">
              <canvas id="canvas" class="qr-canvas" />
            </div>
          </div>
        </div>
      </base-main>
    </div>
  </div>
</template>

<script setup>
// 使用轻量级 SVG 图标，已在 main.js 中全局加载
import { ref, onMounted, onUnmounted } from 'vue'
import { getDownloadUrl } from '@/api/system'
import { Message } from '@/components/base'
import QRCode from 'qrcode'

// 移动端检测
const isMobile = ref(false)

// 检测移动端设备
const checkMobile = () => {
  const screenWidth = window.innerWidth
  const userAgent = navigator.userAgent.toLowerCase()
  const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)

  // 屏幕宽度小于768px或者是移动设备
  isMobile.value = screenWidth < 768 || isMobileDevice
}

// 响应式处理
const handleResize = () => {
  checkMobile()
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const androidQRCode = ref('')
const iosQRCode = ref('')

const windowsloading = ref(false)
const macloading = ref(false)
const iosloading = ref(false)
const androidloading = ref(false)

const downloadProgress = ref({
  windows: 0,
  darwin: 0
})

const progressFormat = (percentage) => {
  return percentage === 100 ? '完成' : `${percentage}%`
}

const getBolb = (url, type) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'

    xhr.onprogress = (event) => {
      if (event.lengthComputable) {
        const percentComplete = (event.loaded / event.total) * 100
        downloadProgress.value[type] = Math.round(percentComplete)
      }
    }

    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      } else {
        reject(new Error('下载失败'))
      }
    }

    xhr.onerror = () => {
      reject(new Error('网络错误'))
    }

    xhr.send()
  })
}

const saveAs = (blob, filename) => {
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename)
  } else {
    const link = document.createElement('a')
    const body = document.querySelector('body')

    link.href = window.URL.createObjectURL(blob)
    link.download = filename

    link.style.display = 'none'
    body.appendChild(link)

    link.click()
    body.removeChild(link)

    window.URL.revokeObjectURL(link.href)
  }
  resetLoading()
}

const resetLoading = () => {
  windowsloading.value = false
  macloading.value = false
  iosloading.value = false
  androidloading.value = false
  Object.keys(downloadProgress.value).forEach(key => {
    downloadProgress.value[key] = 0
  })
}

const hasExecuted = ref(false)
const download = async(type) => {
  if ((type === 'android' || type === 'ios') && hasExecuted.value) {
    return
  }
  hasExecuted.value = true
  const loadingRef = {
    windows: windowsloading,
    darwin: macloading,
    ios: iosloading,
    android: androidloading
  }[type]
  loadingRef.value = true

  try {
    const res = await getDownloadUrl({ platform: type })
    if (res.data.code === 0) {
      if (type === 'ios') {
        const qrcode = await QRCode.toDataURL(res.data.data.download_url)
        const canvas = document.getElementById('ioscanvas')
        iosQRCode.value = qrcode

        if (canvas) {
          const ctx = canvas.getContext('2d')
          const img = new Image()
          img.onload = () => {
            canvas.width = img.width
            canvas.height = img.height
            ctx.drawImage(img, 0, 0)
          }
          img.src = qrcode
        }
      } else if (type === 'android') {
        const newPort = window.location.port
        const serverUrl = new URL(res.data.data.download_url)

        let modifiedDownloadUrl

        if (newPort) {
          // asec-deploy.oss-cn-guangzhou.aliyuncs.com 公有云下载不需要修改端口
          if (serverUrl.toString().includes('asec-deploy')) {
            modifiedDownloadUrl = res.data.data.download_url
          } else {
            serverUrl.port = newPort
            modifiedDownloadUrl = serverUrl.toString()
          }
        } else {
          serverUrl.port = '' // 移除端口
          modifiedDownloadUrl = serverUrl.toString()
        }

        const qrcode = await QRCode.toDataURL(modifiedDownloadUrl)
        const canvas = document.getElementById('canvas')
        androidQRCode.value = qrcode

        if (canvas) {
          const ctx = canvas.getContext('2d')
          const img = new Image()
          img.onload = () => {
            canvas.width = img.width
            canvas.height = img.height
            ctx.drawImage(img, 0, 0)
          }
          img.src = qrcode
        }
      } else {
        const newPort = window.location.port
        const serverUrl = new URL(res.data.data.download_url)

        let modifiedDownloadUrl
        let modifiedFilename

        if (newPort) {
          // asec-deploy.oss-cn-guangzhou.aliyuncs.com 公有云下载不需要修改端口
          if (serverUrl.toString().includes('asec-deploy')) {
            modifiedDownloadUrl = res.data.data.download_url
          } else {
            serverUrl.port = newPort
            modifiedDownloadUrl = serverUrl.toString()
          }

          modifiedFilename = res.data.data.latest_filename.replace(/@(\d+)/, `@${newPort}`)
        } else {
          serverUrl.port = '' // 移除端口
          modifiedDownloadUrl = serverUrl.toString()
          modifiedFilename = res.data.data.latest_filename
        }

        const blob = await getBolb(modifiedDownloadUrl, type)
        saveAs(blob, modifiedFilename)
      }
    } else {
      throw new Error(res.data.msg)
    }
  } catch (error) {
    Message({
      type: 'error',
      message: error.message || '下载失败，请联系管理员',
    })
  } finally {
    loadingRef.value = false
  }
}
</script>

<style lang="scss" scoped>
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.client {
  height: 100vh;
  text-align: center;
  background: #FFFFFF;
  max-height: calc(100vh - 84px);
  border-radius: 4px;
  padding: 20px;

  .base-main {
    height: 100%;
    padding: 0;
  }

  .download-container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px 0;
  }

  // 下载卡片样式
  .download-card {
    width: 209px;
    height: 209px;
    background: #F1F8FF;
    position: relative;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

      .window-show {
        display: none;
      }

      .window-hidden {
        display: block !important;

        // 特殊处理二维码容器
        &.qr-container {
          display: flex !important;
          align-items: center;
          justify-content: center;
        }
      }
    }

    // 图标样式
    .download-icon {
      font-size: 43px;
      color: #4D70FF;
      pointer-events: none;
      margin: 0;
      display: block;

      &.window-hidden {
        display: none;
      }
    }

    // 文字样式
    .download-text {
      color: #333;
      font-size: 14px;
      font-weight: 500;
      text-decoration: none;
      margin: 16px 0 0 0;
      display: block;
      text-align: center;

      &.window-hidden {
        display: none;
      }
    }

    // 进度条样式
    .download-progress {
      margin-top: 10px;
      width: 80%;
    }

    // 二维码容器
    .qr-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: none;
      align-items: center;
      justify-content: center;
      background: #F1F8FF;
      border-radius: 8px;

      .qr-canvas {
        width: auto;
        height: auto;
        max-width: 150px;
        max-height: 150px;
        display: block;
        margin: 0 auto;
      }
    }
  }

  // 移动端提示信息
  .mobile-notice {
    width: 100%;
    max-width: 500px;
    margin: 20px auto 0;

    .mobile-notice-content {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 30px 20px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      .mobile-notice-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 16px;
        display: block;
      }

      .mobile-notice-title {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        margin-top: 0;
      }

      .mobile-notice-text {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 20px;
      }

      .mobile-notice-tips {
        background: #fff;
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid #4D70FF;
        text-align: left;

        p {
          font-size: 13px;
          font-weight: 600;
          color: #4D70FF;
          margin-bottom: 10px;
          margin-top: 0;
        }

        ul {
          margin: 0;
          padding-left: 18px;

          li {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 6px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

/* Loading 样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(241, 248, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #536ce6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: #606266;
}

// 移动端适配
@media screen and (max-width: 768px) {
  .client {
    padding: 10px;
    max-height: 100vh;

    .download-container {
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      gap: 16px;
      padding: 10px 0;
      overflow-y: auto;
    }

    .download-card {
      width: 280px;
      height: 200px;
      margin: 0;

      .download-icon {
        font-size: 36px;
      }

      .download-text {
        font-size: 15px;
        margin-top: 12px;
      }

      .download-progress {
        margin-top: 8px;
        width: 85%;
      }

      .qr-container {
        .qr-canvas {
          max-width: 120px;
          max-height: 120px;
        }
      }
    }

    .mobile-notice {
      margin-top: 10px;

      .mobile-notice-content {
        padding: 20px 16px;

        .mobile-notice-icon {
          font-size: 40px;
          margin-bottom: 12px;
        }

        .mobile-notice-title {
          font-size: 18px;
          margin-bottom: 10px;
        }

        .mobile-notice-text {
          font-size: 13px;
          margin-bottom: 16px;
        }

        .mobile-notice-tips {
          padding: 12px;

          p {
            font-size: 12px;
            margin-bottom: 8px;
          }

          ul li {
            font-size: 12px;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}

// 小屏幕手机适配
@media screen and (max-width: 480px) {
  .client {
    padding: 8px;

    .download-card {
      width: 260px;
      height: 180px;

      .download-icon {
        font-size: 32px;
      }

      .download-text {
        font-size: 14px;
        margin-top: 10px;
      }

      .qr-container {
        .qr-canvas {
          max-width: 100px;
          max-height: 100px;
        }
      }
    }

    .mobile-notice {
      .mobile-notice-content {
        padding: 16px 12px;

        .mobile-notice-icon {
          font-size: 36px;
          margin-bottom: 10px;
        }

        .mobile-notice-title {
          font-size: 16px;
          margin-bottom: 8px;
        }

        .mobile-notice-text {
          font-size: 12px;
          margin-bottom: 12px;
        }
      }
    }
  }
}

// 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .client .download-card {
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }

    // 移动端点击显示二维码或下载
    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 横屏手机适配
@media screen and (max-height: 500px) and (orientation: landscape) {
  .client {
    .download-container {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
      gap: 12px;
    }

    .download-card {
      width: 180px;
      height: 160px;

      .download-icon {
        font-size: 28px;
      }

      .download-text {
        font-size: 12px;
        margin-top: 8px;
      }

      .qr-container {
        .qr-canvas {
          max-width: 80px;
          max-height: 80px;
        }
      }
    }

    .mobile-notice {
      display: none; // 横屏时隐藏提示信息
    }
  }
}

// 平板适配
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .client {
    .download-container {
      gap: 16px;
    }

    .download-card {
      width: 200px;
      height: 200px;

      .download-icon {
        font-size: 40px;
      }

      .download-text {
        font-size: 14px;
        margin-top: 14px;
      }

      .qr-container {
        .qr-canvas {
          max-width: 130px;
          max-height: 130px;
        }
      }
    }
  }
}
</style>
