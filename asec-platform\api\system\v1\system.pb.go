// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: system/v1/system.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ES256KeyMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"` //jwt加密算法provider名称
}

func (x *ES256KeyMsg) Reset() {
	*x = ES256KeyMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_system_v1_system_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ES256KeyMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ES256KeyMsg) ProtoMessage() {}

func (x *ES256KeyMsg) ProtoReflect() protoreflect.Message {
	mi := &file_system_v1_system_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ES256KeyMsg.ProtoReflect.Descriptor instead.
func (*ES256KeyMsg) Descriptor() ([]byte, []int) {
	return file_system_v1_system_proto_rawDescGZIP(), []int{0}
}

func (x *ES256KeyMsg) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_system_v1_system_proto protoreflect.FileDescriptor

var file_system_v1_system_proto_rawDesc = []byte{
	0x0a, 0x16, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x23, 0x0a, 0x0b, 0x45, 0x53, 0x32, 0x35, 0x36, 0x4b, 0x65, 0x79, 0x4d, 0x73, 0x67,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x54, 0x0a, 0x10, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x47,
	0x65, 0x74, 0x45, 0x53, 0x32, 0x35, 0x36, 0x4b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x0d, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x45, 0x53, 0x32, 0x35, 0x36, 0x4b, 0x65, 0x79, 0x12, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x2e, 0x45, 0x53, 0x32, 0x35, 0x36, 0x4b, 0x65, 0x79, 0x4d, 0x73, 0x67, 0x42, 0x2b, 0x5a, 0x29,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_system_v1_system_proto_rawDescOnce sync.Once
	file_system_v1_system_proto_rawDescData = file_system_v1_system_proto_rawDesc
)

func file_system_v1_system_proto_rawDescGZIP() []byte {
	file_system_v1_system_proto_rawDescOnce.Do(func() {
		file_system_v1_system_proto_rawDescData = protoimpl.X.CompressGZIP(file_system_v1_system_proto_rawDescData)
	})
	return file_system_v1_system_proto_rawDescData
}

var file_system_v1_system_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_system_v1_system_proto_goTypes = []interface{}{
	(*ES256KeyMsg)(nil),   // 0: api.system.ES256KeyMsg
	(*emptypb.Empty)(nil), // 1: google.protobuf.Empty
}
var file_system_v1_system_proto_depIdxs = []int32{
	1, // 0: api.system.AgentGetES256Key.QueryES256Key:input_type -> google.protobuf.Empty
	0, // 1: api.system.AgentGetES256Key.QueryES256Key:output_type -> api.system.ES256KeyMsg
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_system_v1_system_proto_init() }
func file_system_v1_system_proto_init() {
	if File_system_v1_system_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_system_v1_system_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ES256KeyMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_system_v1_system_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_system_v1_system_proto_goTypes,
		DependencyIndexes: file_system_v1_system_proto_depIdxs,
		MessageInfos:      file_system_v1_system_proto_msgTypes,
	}.Build()
	File_system_v1_system_proto = out.File
	file_system_v1_system_proto_rawDesc = nil
	file_system_v1_system_proto_goTypes = nil
	file_system_v1_system_proto_depIdxs = nil
}
