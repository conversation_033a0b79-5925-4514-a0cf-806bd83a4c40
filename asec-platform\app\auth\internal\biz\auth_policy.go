package biz

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/jinzhu/copier"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/common"

	"asdsec.com/asec/platform/app/auth/internal/data/model"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/google/uuid"

	"github.com/go-kratos/kratos/v2/log"
)

type AuthPolicyRepo interface {
	CreatePolicy(ctx context.Context, param dto.CreateAuthPolicyDaoParam) error
	UpdatePolicy(ctx context.Context, param dto.UpdateAuthPolicyParam) error
	ListPolicyInRootGroup(ctx context.Context, corpId, groupId string, limit, offset int) ([]*model.TbAuthPolicy, error)
	GetDefaultPolicyInRootGroup(ctx context.Context, corpId, groupId string) (*model.TbAuthPolicy, error)
	CountPolicyInRootGroup(ctx context.Context, corpId, groupId string) (int64, error)
	QueryPolicyByName(ctx context.Context, corpId, groupId, name string, id string) (*model.TbAuthPolicy, error)
	QueryPolicyById(ctx context.Context, corpId, id string) (*model.TbAuthPolicy, error)
	ListIdpInfo(ctx context.Context, corpId, policyId string) ([]dto.BindIDPInfo, error)
	DeleteAuthPolicy(ctx context.Context, corpId, policyId, name string) error
	AddAuthPolicyIdpMapper(ctx context.Context, param model.TbAuthPolicyIdpMapper) error
	GetClientLimits(ctx context.Context, corpId, policyId string) (*dto.ClientLimits, error)
	UpdateClientLimits(ctx context.Context, corpId, policyId string, clientLimits *dto.ClientLimits) error
}

type AuthPolicyUsecase struct {
	repo          AuthPolicyRepo
	userRepo      UserRepo
	ugRepo        UserGroupRepo
	trackerRepo   SessionTrackerRepo // 新增：会话跟踪
	blacklistRepo JWTBlacklistRepo   // 新增：JWT黑名单
	log           *log.Helper
}

func NewAuthPolicyUsecase(repo AuthPolicyRepo, userRepo UserRepo, ugRepo UserGroupRepo, trackerRepo SessionTrackerRepo, blacklistRepo JWTBlacklistRepo, logger log.Logger) *AuthPolicyUsecase {
	return &AuthPolicyUsecase{
		repo:          repo,
		ugRepo:        ugRepo,
		userRepo:      userRepo,
		trackerRepo:   trackerRepo,
		blacklistRepo: blacklistRepo,
		log:           log.NewHelper(logger),
	}
}

func (a AuthPolicyUsecase) CreateAuthPolicy(ctx context.Context, param dto.CreateAuthPolicyParam) error {
	// 校验一下根目录
	rootGroup, err := a.ugRepo.GetUserGroup(ctx, param.CorpId, param.RootGroupId)
	if err != nil {
		a.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}
	if rootGroup.ParentGroupID != dto.FakeRootUserGroupId {
		a.log.Errorf("root_group_id=%v error.", param.RootGroupId)
		return pb.ErrorParamError("root group error")
	}

	// 不允许同名
	_, err = a.repo.QueryPolicyByName(ctx, param.CorpId, rootGroup.ID, param.Name, "")
	if err != nil && !pb.IsRecordNotFound(err) {
		a.log.Errorf("QueryPolicyByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict", param.Name)
	}

	// 所有用户绑定当前根目录组
	if param.EnableAllUser {
		param.GroupIds = []string{param.RootGroupId}
	}
	// 认证组和认证用户不能同时为空
	if len(param.UserIds) == 0 && len(param.GroupIds) == 0 {
		return pb.ErrorAuthObjectEmpty("auth object is empty")
	}
	// 获取租户下的所有group
	groups, err := a.ugRepo.ListUserGroup(ctx, param.CorpId)
	if err != nil {
		a.log.Errorf("ListUserGroup failed. err=%v", err)
		return err
	}
	connect, err := common.BuildGroupToRootConnect(groups)
	if err != nil {
		a.log.Errorf("BuildGroupToRootConnect failed. err=%v", err)
		return err
	}

	// 需要绑定的组以及用户对应的根目录，限制认证策略只能在根目录下生效（因为不同根目录可能存在不同的idp绑定情况）
	groupOfUsers, err := a.userRepo.GetGroupOfUsers(ctx, param.CorpId, param.UserIds)
	if err != nil {
		a.log.Errorf("GetGroupOfUsers failed. err=%v", err)
		return err
	}
	groupIds := append(param.GroupIds, groupOfUsers...)
	rootGroupId, err := a.getRootGroupId(connect, groupIds)
	if err != nil {
		return err
	}

	// 获取根目录的绑定idp
	idpIds, err := a.ugRepo.GetIDPIdsOfGroup(ctx, param.CorpId, rootGroupId)
	if err != nil {
		a.log.Errorf("GetIDPIdsOfGroup failed. err=%v", err)
		return err
	}

	// 确认绑定的idp是对应根目录已绑定的idp
	if len(param.IdpList) == 0 || !common.IsSubset(param.IdpList, idpIds) {
		//辅助认证是不需要绑定用户目录的，该方法存在问题，暂时屏蔽，理论上无影响
		//return pb.ErrorGroupNotSupportIdp("group bind idp=%v, input=%v", idpIds, param.IdpList)
	}

	var daoParam dto.CreateAuthPolicyDaoParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		a.log.Errorf("Copy failed. err=%v", err)
		return err
	}
	daoParam.Id = uuid.New().String()
	err = a.repo.CreatePolicy(ctx, daoParam)
	if err != nil {
		a.log.Errorf("CreatePolicy failed. err=%v", err)
		return err
	}
	return nil
}

func (a AuthPolicyUsecase) UpdateAuthPolicy(ctx context.Context, param dto.UpdateAuthPolicyParam) error {
	// 获取策略所在的组
	rootGroup, err := a.ugRepo.GetUserGroup(ctx, param.CorpId, param.RootGroupId)
	if err != nil {
		a.log.Errorf("GetUserGroup failed. err=%v", err)
		return err
	}
	if rootGroup.ParentGroupID != dto.FakeRootUserGroupId {
		a.log.Errorf("root_group_id=%v error.", param.RootGroupId)
		return pb.ErrorParamError("root group error")
	}
	//检测除当前策略外，认证策略名是否存在
	_, err = a.repo.QueryPolicyByName(ctx, param.CorpId, rootGroup.ID, param.Name, param.PolicyId)
	if err != nil && !pb.IsRecordNotFound(err) {
		a.log.Errorf("QueryPolicyByName failed. err=%v", err)
		return err
	}
	if err == nil {
		return pb.ErrorNameConflict("name=%v conflict", param.Name)
	}

	// 所有用户绑定当前根目录组
	if param.EnableAllUser {
		param.GroupIds = []string{param.RootGroupId}
	}
	// 认证组和认证用户不能同时为空
	if len(param.UserIds) == 0 && len(param.GroupIds) == 0 {
		return pb.ErrorAuthObjectEmpty("auth object is empty")
	}

	var daoParam dto.UpdateAuthPolicyParam
	if err := copier.Copy(&daoParam, &param); err != nil {
		a.log.Errorf("Copy failed. err=%v", err)
		return err
	}
	err = a.repo.UpdatePolicy(ctx, daoParam)
	if err != nil {
		a.log.Errorf("UpdatePolicy failed. err=%v", err)
		return err
	}
	return nil
}

func (a AuthPolicyUsecase) getRootGroupId(connect map[string]string, groups []string) (string, error) {
	if len(groups) == 0 {
		return "", pb.ErrorGroupNotFound("groups is empty")
	}

	if len(groups) == 1 {
		rootGroupId, err := common.SearchGroupRoot(connect, groups[0])
		if err != nil {
			a.log.Errorf("SearchGroupRoot failed. err=%v, groupId=%v", err, groups[0])
			return "", err
		}
		return rootGroupId, nil
	}

	var rootGroupId string
	for id := range groups {
		if id == 0 {
			continue
		}
		before, current := groups[id-1], groups[id]
		rootBefore, err := common.SearchGroupRoot(connect, before)
		if err != nil {
			a.log.Errorf("SearchGroupRoot failed. err=%v, groupId=%v", err, before)
			return "", err
		}
		rootCurrent, err := common.SearchGroupRoot(connect, current)
		if err != nil {
			a.log.Errorf("SearchGroupRoot failed. err=%v, groupId=%v", err, current)
			return "", err
		}
		if rootBefore != rootCurrent {
			return "", pb.ErrorUserGroupConflict("group conflict. id1=%v -> root1=%v, id2=%v -> root2=%v", before, rootBefore, current, rootCurrent)
		}
		rootGroupId = rootCurrent
	}
	return rootGroupId, nil
}

func (a AuthPolicyUsecase) ListAuthPolicy(ctx context.Context, corpId, rootGroupId string, limit, offset int) (dto.ListAuthPolicyResp, error) {
	policies, err := a.repo.ListPolicyInRootGroup(ctx, corpId, rootGroupId, limit, offset)
	if err != nil {
		a.log.Errorf("ListPolicyInRootGroup failed. err=%v", err)
		return dto.ListAuthPolicyResp{}, err
	}
	// 这里在循环中获取数据，原因：1.策略数据都是用户手动添加的，不会很大，后续有性能问题再优化吧；2. 代码简单些，方便阅读
	var authPolicies []dto.AuthPolicyInfo
	for _, p := range policies {
		var info dto.AuthPolicyInfo
		if err := copier.Copy(&info, p); err != nil {
			a.log.Errorf("Copy failed. err=%v", err)
			return dto.ListAuthPolicyResp{}, nil
		}
		if info.GroupInfoList, err = a.getBindGroupInfo(ctx, corpId, p.GroupIds); err != nil {
			return dto.ListAuthPolicyResp{}, err
		}
		if info.UserInfoList, err = a.getBindUserInfo(ctx, corpId, p.UserIds); err != nil {
			return dto.ListAuthPolicyResp{}, err
		}
		if info.IdpInfoMap, err = a.getBindIdpInfo(ctx, corpId, p.ID); err != nil {
			return dto.ListAuthPolicyResp{}, err
		}
		if len(p.AuthEnhancement.Bytes) > 0 {
			err = json.Unmarshal(p.AuthEnhancement.Bytes, &info.AuthEnhancement)
			if err != nil {
				return dto.ListAuthPolicyResp{}, err
			}
		}

		// 处理客户端限制配置
		if len(p.ClientLimits.Bytes) > 0 {
			var clientLimits dto.ClientLimits
			err = json.Unmarshal(p.ClientLimits.Bytes, &clientLimits)
			if err != nil {
				a.log.Errorf("Unmarshal ClientLimits failed. err=%v", err)
				return dto.ListAuthPolicyResp{}, err
			}
			info.ClientLimits = &clientLimits
		}

		authPolicies = append(authPolicies, info)
	}
	count, err := a.repo.CountPolicyInRootGroup(ctx, corpId, rootGroupId)
	if err != nil {
		a.log.Errorf("CountPolicyInRootGroup failed. err=%v", err)
		return dto.ListAuthPolicyResp{}, err
	}

	return dto.ListAuthPolicyResp{AuthPolicies: authPolicies, Count: count}, nil
}

func (a AuthPolicyUsecase) getBindGroupInfo(ctx context.Context, corpId string, groupIds []string) ([]dto.BindGroupInfo, error) {
	if len(groupIds) == 0 {
		return []dto.BindGroupInfo{}, nil
	}
	result, err := a.ugRepo.ListBindGroupInfo(ctx, corpId, groupIds)
	if err != nil {
		a.log.Errorf("ListBindGroupInfo failed. err=%v", err)
		return []dto.BindGroupInfo{}, err
	}
	return result, nil
}

func (a AuthPolicyUsecase) getBindUserInfo(ctx context.Context, corpId string, userIds []string) ([]dto.BindUserInfo, error) {
	if len(userIds) == 0 {
		return []dto.BindUserInfo{}, nil
	}
	result, err := a.userRepo.ListUserBindInfo(ctx, corpId, userIds)
	if err != nil {
		a.log.Errorf("ListUserBindInfo failed. err=%v", err)
		return []dto.BindUserInfo{}, err
	}
	return result, nil
}

func (a AuthPolicyUsecase) getBindIdpInfo(ctx context.Context, corpId string, policyId string) (dto.BindIDPInfoMap, error) {
	idpInfos, err := a.repo.ListIdpInfo(ctx, corpId, policyId)
	if err != nil {
		a.log.Errorf("ListIdpInfo failed. err=%v", err)
		return dto.BindIDPInfoMap{}, err
	}
	var result dto.BindIDPInfoMap
	for _, idp := range idpInfos {
		if _, ok := dto.MainIDPTypeName[dto.IDPType(idp.Type)]; ok {
			result.MainIdpList = append(result.MainIdpList, idp)
		} else {
			result.AssistIdpList = append(result.AssistIdpList, idp)
		}
	}
	return result, nil
}

func (a AuthPolicyUsecase) DeleteAuthPolicy(ctx context.Context, corpId, id, name string) error {
	policy, err := a.repo.QueryPolicyById(ctx, corpId, id)
	if err != nil {
		a.log.Errorf("QueryPolicyById failed. err=%v", err)
		return err
	}
	if policy.IsDefault {
		return pb.ErrorDefaultDataConflict("default policy is not allowed to delete")
	}

	if err := a.repo.DeleteAuthPolicy(ctx, corpId, id, name); err != nil {
		a.log.Errorf("DeleteAuthPolicy failed. err=%v", err)
		return err
	}
	return nil
}

// GetClientLimits 获取客户端限制配置
func (a *AuthPolicyUsecase) GetClientLimits(ctx context.Context, corpId, policyId string) (*dto.ClientLimits, error) {
	// 调用Repository层获取客户端限制配置
	clientLimits, err := a.repo.GetClientLimits(ctx, corpId, policyId)
	if err != nil {
		a.log.Errorf("GetClientLimits failed. err=%v", err)
		return nil, err
	}

	// 如果没有配置，返回默认配置
	if clientLimits == nil {
		defaultLimits := dto.DefaultClientLimits()
		return &defaultLimits, nil
	}

	return clientLimits, nil
}

// UpdateClientLimits 更新客户端限制配置
func (a *AuthPolicyUsecase) UpdateClientLimits(ctx context.Context, corpId, policyId string, clientLimits *dto.ClientLimits) error {
	// 验证参数
	if clientLimits == nil {
		return fmt.Errorf("客户端限制配置不能为空")
	}

	// 验证策略是否存在
	policy, err := a.repo.QueryPolicyById(ctx, corpId, policyId)
	if err != nil {
		a.log.Errorf("QueryPolicyById failed. err=%v", err)
		return err
	}
	if policy == nil {
		return fmt.Errorf("认证策略不存在")
	}

	// 调用Repository层更新客户端限制配置
	err = a.repo.UpdateClientLimits(ctx, corpId, policyId, clientLimits)
	if err != nil {
		a.log.Errorf("UpdateClientLimits failed. err=%v", err)
		return err
	}

	return nil
}
