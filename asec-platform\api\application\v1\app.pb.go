// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: application/v1/app.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SeGetAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId uint64 `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
}

func (x *SeGetAppReq) Reset() {
	*x = SeGetAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeGetAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeGetAppReq) ProtoMessage() {}

func (x *SeGetAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeGetAppReq.ProtoReflect.Descriptor instead.
func (*SeGetAppReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{0}
}

func (x *SeGetAppReq) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

type GetAppResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Apps []*AppInfo `protobuf:"bytes,1,rep,name=apps,proto3" json:"apps,omitempty"`
}

func (x *GetAppResp) Reset() {
	*x = GetAppResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppResp) ProtoMessage() {}

func (x *GetAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppResp.ProtoReflect.Descriptor instead.
func (*GetAppResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{1}
}

func (x *GetAppResp) GetApps() []*AppInfo {
	if x != nil {
		return x.Apps
	}
	return nil
}

type AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    uint64 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName  string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	Port     string `protobuf:"bytes,3,opt,name=port,proto3" json:"port,omitempty"`
	Address  string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Protocol string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
}

func (x *AppInfo) Reset() {
	*x = AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfo) ProtoMessage() {}

func (x *AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfo.ProtoReflect.Descriptor instead.
func (*AppInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{2}
}

func (x *AppInfo) GetAppId() uint64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *AppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AppInfo) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *AppInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AppInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

type SERouteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CorpId      uint64 `protobuf:"varint,1,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	ApplianceId uint64 `protobuf:"varint,2,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
}

func (x *SERouteReq) Reset() {
	*x = SERouteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SERouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SERouteReq) ProtoMessage() {}

func (x *SERouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SERouteReq.ProtoReflect.Descriptor instead.
func (*SERouteReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{3}
}

func (x *SERouteReq) GetCorpId() uint64 {
	if x != nil {
		return x.CorpId
	}
	return 0
}

func (x *SERouteReq) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

type SERoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Connectors []uint64 `protobuf:"varint,1,rep,packed,name=connectors,proto3" json:"connectors,omitempty"`
	AppIds     []uint64 `protobuf:"varint,2,rep,packed,name=app_ids,json=appIds,proto3" json:"app_ids,omitempty"`
	Address    []string `protobuf:"bytes,3,rep,name=address,proto3" json:"address,omitempty"`
}

func (x *SERoute) Reset() {
	*x = SERoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SERoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SERoute) ProtoMessage() {}

func (x *SERoute) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SERoute.ProtoReflect.Descriptor instead.
func (*SERoute) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{4}
}

func (x *SERoute) GetConnectors() []uint64 {
	if x != nil {
		return x.Connectors
	}
	return nil
}

func (x *SERoute) GetAppIds() []uint64 {
	if x != nil {
		return x.AppIds
	}
	return nil
}

func (x *SERoute) GetAddress() []string {
	if x != nil {
		return x.Address
	}
	return nil
}

type SERoutes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeRoute []*SERoute `protobuf:"bytes,1,rep,name=se_route,json=seRoute,proto3" json:"se_route,omitempty"`
	//connector id数组，去重，用于配置反向代理
	Connectors []uint64 `protobuf:"varint,2,rep,packed,name=connectors,proto3" json:"connectors,omitempty"`
}

func (x *SERoutes) Reset() {
	*x = SERoutes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SERoutes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SERoutes) ProtoMessage() {}

func (x *SERoutes) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SERoutes.ProtoReflect.Descriptor instead.
func (*SERoutes) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{5}
}

func (x *SERoutes) GetSeRoute() []*SERoute {
	if x != nil {
		return x.SeRoute
	}
	return nil
}

func (x *SERoutes) GetConnectors() []uint64 {
	if x != nil {
		return x.Connectors
	}
	return nil
}

type SeGetStrategyResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeStrategy []*SeStrategy `protobuf:"bytes,1,rep,name=se_strategy,json=seStrategy,proto3" json:"se_strategy,omitempty"`
}

func (x *SeGetStrategyResp) Reset() {
	*x = SeGetStrategyResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeGetStrategyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeGetStrategyResp) ProtoMessage() {}

func (x *SeGetStrategyResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeGetStrategyResp.ProtoReflect.Descriptor instead.
func (*SeGetStrategyResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{6}
}

func (x *SeGetStrategyResp) GetSeStrategy() []*SeStrategy {
	if x != nil {
		return x.SeStrategy
	}
	return nil
}

type SeStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrategyId    uint64   `protobuf:"varint,1,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id,omitempty"`
	AppIds        []uint64 `protobuf:"varint,2,rep,packed,name=app_ids,json=appIds,proto3" json:"app_ids,omitempty"`
	UserIds       []string `protobuf:"bytes,3,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	StartTime     string   `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string   `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	EnableAllUser uint32   `protobuf:"varint,6,opt,name=enable_all_user,json=enableAllUser,proto3" json:"enable_all_user,omitempty"`
	EnableAllApp  uint32   `protobuf:"varint,7,opt,name=enable_all_app,json=enableAllApp,proto3" json:"enable_all_app,omitempty"`
	EnableLog     uint32   `protobuf:"varint,8,opt,name=enable_log,json=enableLog,proto3" json:"enable_log,omitempty"`
	StrategyName  string   `protobuf:"bytes,9,opt,name=strategy_name,json=strategyName,proto3" json:"strategy_name,omitempty"`
}

func (x *SeStrategy) Reset() {
	*x = SeStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeStrategy) ProtoMessage() {}

func (x *SeStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeStrategy.ProtoReflect.Descriptor instead.
func (*SeStrategy) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{7}
}

func (x *SeStrategy) GetStrategyId() uint64 {
	if x != nil {
		return x.StrategyId
	}
	return 0
}

func (x *SeStrategy) GetAppIds() []uint64 {
	if x != nil {
		return x.AppIds
	}
	return nil
}

func (x *SeStrategy) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *SeStrategy) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SeStrategy) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SeStrategy) GetEnableAllUser() uint32 {
	if x != nil {
		return x.EnableAllUser
	}
	return 0
}

func (x *SeStrategy) GetEnableAllApp() uint32 {
	if x != nil {
		return x.EnableAllApp
	}
	return 0
}

func (x *SeStrategy) GetEnableLog() uint32 {
	if x != nil {
		return x.EnableLog
	}
	return 0
}

func (x *SeStrategy) GetStrategyName() string {
	if x != nil {
		return x.StrategyName
	}
	return ""
}

type WebAccessInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WebAccessInfoReq) Reset() {
	*x = WebAccessInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebAccessInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebAccessInfoReq) ProtoMessage() {}

func (x *WebAccessInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebAccessInfoReq.ProtoReflect.Descriptor instead.
func (*WebAccessInfoReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{8}
}

type WebAccessInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrategyInfo []*StrategyInfo `protobuf:"bytes,1,rep,name=StrategyInfo,proto3" json:"StrategyInfo,omitempty"`
}

func (x *WebAccessInfoResp) Reset() {
	*x = WebAccessInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebAccessInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebAccessInfoResp) ProtoMessage() {}

func (x *WebAccessInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebAccessInfoResp.ProtoReflect.Descriptor instead.
func (*WebAccessInfoResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{9}
}

func (x *WebAccessInfoResp) GetStrategyInfo() []*StrategyInfo {
	if x != nil {
		return x.StrategyInfo
	}
	return nil
}

type WebAppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         uint64 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName       string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	Port          int32  `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	Address       string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Protocol      string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Uri           string `protobuf:"bytes,6,opt,name=uri,proto3" json:"uri,omitempty"`
	ServerAddress string `protobuf:"bytes,7,opt,name=server_address,json=serverAddress,proto3" json:"server_address,omitempty"`
	IdpId         string `protobuf:"bytes,8,opt,name=idp_id,json=idpId,proto3" json:"idp_id,omitempty"`
	AppStatus     int32  `protobuf:"varint,9,opt,name=app_status,json=appStatus,proto3" json:"app_status,omitempty"` // 应用状态：1=启用，2=维护中，3=禁用
}

func (x *WebAppInfo) Reset() {
	*x = WebAppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebAppInfo) ProtoMessage() {}

func (x *WebAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebAppInfo.ProtoReflect.Descriptor instead.
func (*WebAppInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{10}
}

func (x *WebAppInfo) GetAppId() uint64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *WebAppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *WebAppInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *WebAppInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *WebAppInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *WebAppInfo) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *WebAppInfo) GetServerAddress() string {
	if x != nil {
		return x.ServerAddress
	}
	return ""
}

func (x *WebAppInfo) GetIdpId() string {
	if x != nil {
		return x.IdpId
	}
	return ""
}

func (x *WebAppInfo) GetAppStatus() int32 {
	if x != nil {
		return x.AppStatus
	}
	return 0
}

type StrategyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrategyId    uint64   `protobuf:"varint,1,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id,omitempty"`
	AppIds        []uint64 `protobuf:"varint,2,rep,packed,name=app_ids,json=appIds,proto3" json:"app_ids,omitempty"`
	UserIds       []string `protobuf:"bytes,3,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	StartTime     string   `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string   `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	EnableAllUser uint32   `protobuf:"varint,6,opt,name=enable_all_user,json=enableAllUser,proto3" json:"enable_all_user,omitempty"`
	EnableAllApp  uint32   `protobuf:"varint,7,opt,name=enable_all_app,json=enableAllApp,proto3" json:"enable_all_app,omitempty"`
	EnableLog     uint32   `protobuf:"varint,8,opt,name=enable_log,json=enableLog,proto3" json:"enable_log,omitempty"`
	StrategyName  string   `protobuf:"bytes,9,opt,name=strategy_name,json=strategyName,proto3" json:"strategy_name,omitempty"`
	Action        string   `protobuf:"bytes,10,opt,name=action,proto3" json:"action,omitempty"`
	Priority      int32    `protobuf:"varint,11,opt,name=priority,proto3" json:"priority,omitempty"`
	RegoFile      []byte   `protobuf:"bytes,12,opt,name=rego_file,json=regoFile,proto3" json:"rego_file,omitempty"`
	TimeGap       *TimeGap `protobuf:"bytes,13,opt,name=time_gap,json=timeGap,proto3" json:"time_gap,omitempty"`
	UserRiskRule  string   `protobuf:"bytes,14,opt,name=user_risk_rule,json=userRiskRule,proto3" json:"user_risk_rule,omitempty"`
}

func (x *StrategyInfo) Reset() {
	*x = StrategyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StrategyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StrategyInfo) ProtoMessage() {}

func (x *StrategyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StrategyInfo.ProtoReflect.Descriptor instead.
func (*StrategyInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{11}
}

func (x *StrategyInfo) GetStrategyId() uint64 {
	if x != nil {
		return x.StrategyId
	}
	return 0
}

func (x *StrategyInfo) GetAppIds() []uint64 {
	if x != nil {
		return x.AppIds
	}
	return nil
}

func (x *StrategyInfo) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *StrategyInfo) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *StrategyInfo) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *StrategyInfo) GetEnableAllUser() uint32 {
	if x != nil {
		return x.EnableAllUser
	}
	return 0
}

func (x *StrategyInfo) GetEnableAllApp() uint32 {
	if x != nil {
		return x.EnableAllApp
	}
	return 0
}

func (x *StrategyInfo) GetEnableLog() uint32 {
	if x != nil {
		return x.EnableLog
	}
	return 0
}

func (x *StrategyInfo) GetStrategyName() string {
	if x != nil {
		return x.StrategyName
	}
	return ""
}

func (x *StrategyInfo) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *StrategyInfo) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *StrategyInfo) GetRegoFile() []byte {
	if x != nil {
		return x.RegoFile
	}
	return nil
}

func (x *StrategyInfo) GetTimeGap() *TimeGap {
	if x != nil {
		return x.TimeGap
	}
	return nil
}

func (x *StrategyInfo) GetUserRiskRule() string {
	if x != nil {
		return x.UserRiskRule
	}
	return ""
}

type TimeGap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeId         string `protobuf:"bytes,1,opt,name=time_id,json=timeId,proto3" json:"time_id,omitempty"`
	GapName        string `protobuf:"bytes,2,opt,name=gap_name,json=gapName,proto3" json:"gap_name,omitempty"`
	IntervalType   int32  `protobuf:"varint,3,opt,name=interval_type,json=intervalType,proto3" json:"interval_type,omitempty"`
	Sunday         bool   `protobuf:"varint,4,opt,name=sunday,proto3" json:"sunday,omitempty"`
	Monday         bool   `protobuf:"varint,5,opt,name=monday,proto3" json:"monday,omitempty"`
	Tuesday        bool   `protobuf:"varint,6,opt,name=tuesday,proto3" json:"tuesday,omitempty"`
	Wednesday      bool   `protobuf:"varint,7,opt,name=wednesday,proto3" json:"wednesday,omitempty"`
	Thursday       bool   `protobuf:"varint,8,opt,name=thursday,proto3" json:"thursday,omitempty"`
	Friday         bool   `protobuf:"varint,9,opt,name=friday,proto3" json:"friday,omitempty"`
	Saturday       bool   `protobuf:"varint,10,opt,name=saturday,proto3" json:"saturday,omitempty"`
	DailyStartTime string `protobuf:"bytes,11,opt,name=daily_start_time,json=dailyStartTime,proto3" json:"daily_start_time,omitempty"`
	DailyEndTime   string `protobuf:"bytes,12,opt,name=daily_end_time,json=dailyEndTime,proto3" json:"daily_end_time,omitempty"`
	AllDayEnable   bool   `protobuf:"varint,13,opt,name=all_day_enable,json=allDayEnable,proto3" json:"all_day_enable,omitempty"`
}

func (x *TimeGap) Reset() {
	*x = TimeGap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeGap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeGap) ProtoMessage() {}

func (x *TimeGap) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeGap.ProtoReflect.Descriptor instead.
func (*TimeGap) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{12}
}

func (x *TimeGap) GetTimeId() string {
	if x != nil {
		return x.TimeId
	}
	return ""
}

func (x *TimeGap) GetGapName() string {
	if x != nil {
		return x.GapName
	}
	return ""
}

func (x *TimeGap) GetIntervalType() int32 {
	if x != nil {
		return x.IntervalType
	}
	return 0
}

func (x *TimeGap) GetSunday() bool {
	if x != nil {
		return x.Sunday
	}
	return false
}

func (x *TimeGap) GetMonday() bool {
	if x != nil {
		return x.Monday
	}
	return false
}

func (x *TimeGap) GetTuesday() bool {
	if x != nil {
		return x.Tuesday
	}
	return false
}

func (x *TimeGap) GetWednesday() bool {
	if x != nil {
		return x.Wednesday
	}
	return false
}

func (x *TimeGap) GetThursday() bool {
	if x != nil {
		return x.Thursday
	}
	return false
}

func (x *TimeGap) GetFriday() bool {
	if x != nil {
		return x.Friday
	}
	return false
}

func (x *TimeGap) GetSaturday() bool {
	if x != nil {
		return x.Saturday
	}
	return false
}

func (x *TimeGap) GetDailyStartTime() string {
	if x != nil {
		return x.DailyStartTime
	}
	return ""
}

func (x *TimeGap) GetDailyEndTime() string {
	if x != nil {
		return x.DailyEndTime
	}
	return ""
}

func (x *TimeGap) GetAllDayEnable() bool {
	if x != nil {
		return x.AllDayEnable
	}
	return false
}

type WebAppInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WebAppInfoReq) Reset() {
	*x = WebAppInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebAppInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebAppInfoReq) ProtoMessage() {}

func (x *WebAppInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebAppInfoReq.ProtoReflect.Descriptor instead.
func (*WebAppInfoReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{13}
}

type WebAppInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WebAppInfo []*WebAppInfo `protobuf:"bytes,1,rep,name=web_app_info,json=webAppInfo,proto3" json:"web_app_info,omitempty"`
}

func (x *WebAppInfoResp) Reset() {
	*x = WebAppInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebAppInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebAppInfoResp) ProtoMessage() {}

func (x *WebAppInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebAppInfoResp.ProtoReflect.Descriptor instead.
func (*WebAppInfoResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{14}
}

func (x *WebAppInfoResp) GetWebAppInfo() []*WebAppInfo {
	if x != nil {
		return x.WebAppInfo
	}
	return nil
}

type UciUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UciUserInfoReq) Reset() {
	*x = UciUserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UciUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UciUserInfoReq) ProtoMessage() {}

func (x *UciUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UciUserInfoReq.ProtoReflect.Descriptor instead.
func (*UciUserInfoReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{15}
}

type UciUserInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UciUserInfo []*UciUserInfo `protobuf:"bytes,1,rep,name=uci_user_info,json=uciUserInfo,proto3" json:"uci_user_info,omitempty"`
}

func (x *UciUserInfoResp) Reset() {
	*x = UciUserInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UciUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UciUserInfoResp) ProtoMessage() {}

func (x *UciUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UciUserInfoResp.ProtoReflect.Descriptor instead.
func (*UciUserInfoResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{16}
}

func (x *UciUserInfoResp) GetUciUserInfo() []*UciUserInfo {
	if x != nil {
		return x.UciUserInfo
	}
	return nil
}

type UciUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Score     uint32 `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	RiskLevel uint32 `protobuf:"varint,3,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`
}

func (x *UciUserInfo) Reset() {
	*x = UciUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UciUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UciUserInfo) ProtoMessage() {}

func (x *UciUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UciUserInfo.ProtoReflect.Descriptor instead.
func (*UciUserInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{17}
}

func (x *UciUserInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UciUserInfo) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *UciUserInfo) GetRiskLevel() uint32 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

type WebGatewayRsAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId string `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
}

func (x *WebGatewayRsAppReq) Reset() {
	*x = WebGatewayRsAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayRsAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayRsAppReq) ProtoMessage() {}

func (x *WebGatewayRsAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayRsAppReq.ProtoReflect.Descriptor instead.
func (*WebGatewayRsAppReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{18}
}

func (x *WebGatewayRsAppReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

type WebGatewayRsAppResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Apps []*ApplicationGatewayInfo `protobuf:"bytes,1,rep,name=apps,proto3" json:"apps,omitempty"`
}

func (x *WebGatewayRsAppResp) Reset() {
	*x = WebGatewayRsAppResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayRsAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayRsAppResp) ProtoMessage() {}

func (x *WebGatewayRsAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayRsAppResp.ProtoReflect.Descriptor instead.
func (*WebGatewayRsAppResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{19}
}

func (x *WebGatewayRsAppResp) GetApps() []*ApplicationGatewayInfo {
	if x != nil {
		return x.Apps
	}
	return nil
}

type WebGatewayRsCrtReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WebGatewayRsCrtReq) Reset() {
	*x = WebGatewayRsCrtReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayRsCrtReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayRsCrtReq) ProtoMessage() {}

func (x *WebGatewayRsCrtReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayRsCrtReq.ProtoReflect.Descriptor instead.
func (*WebGatewayRsCrtReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{20}
}

type WebGatewayRsCrtResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Crts []*Crt `protobuf:"bytes,1,rep,name=crts,proto3" json:"crts,omitempty"`
}

func (x *WebGatewayRsCrtResp) Reset() {
	*x = WebGatewayRsCrtResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayRsCrtResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayRsCrtResp) ProtoMessage() {}

func (x *WebGatewayRsCrtResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayRsCrtResp.ProtoReflect.Descriptor instead.
func (*WebGatewayRsCrtResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{21}
}

func (x *WebGatewayRsCrtResp) GetCrts() []*Crt {
	if x != nil {
		return x.Crts
	}
	return nil
}

type Crt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cert   string   `protobuf:"bytes,1,opt,name=cert,proto3" json:"cert,omitempty"`
	Key    string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Id     string   `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Domain []string `protobuf:"bytes,4,rep,name=domain,proto3" json:"domain,omitempty"`
}

func (x *Crt) Reset() {
	*x = Crt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Crt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Crt) ProtoMessage() {}

func (x *Crt) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Crt.ProtoReflect.Descriptor instead.
func (*Crt) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{22}
}

func (x *Crt) GetCert() string {
	if x != nil {
		return x.Cert
	}
	return ""
}

func (x *Crt) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Crt) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Crt) GetDomain() []string {
	if x != nil {
		return x.Domain
	}
	return nil
}

type WebGatewayWatermarkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WebGatewayWatermarkReq) Reset() {
	*x = WebGatewayWatermarkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayWatermarkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayWatermarkReq) ProtoMessage() {}

func (x *WebGatewayWatermarkReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayWatermarkReq.ProtoReflect.Descriptor instead.
func (*WebGatewayWatermarkReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{23}
}

type WebGatewayWatermarkResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WatermarkConf *Watermark `protobuf:"bytes,1,opt,name=WatermarkConf,proto3" json:"WatermarkConf,omitempty"`
}

func (x *WebGatewayWatermarkResp) Reset() {
	*x = WebGatewayWatermarkResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayWatermarkResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayWatermarkResp) ProtoMessage() {}

func (x *WebGatewayWatermarkResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayWatermarkResp.ProtoReflect.Descriptor instead.
func (*WebGatewayWatermarkResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{24}
}

func (x *WebGatewayWatermarkResp) GetWatermarkConf() *Watermark {
	if x != nil {
		return x.WatermarkConf
	}
	return nil
}

type Watermark struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable            bool       `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	AppIds            []string   `protobuf:"bytes,2,rep,name=app_ids,json=appIds,proto3" json:"app_ids,omitempty"`
	EnableAllApp      bool       `protobuf:"varint,3,opt,name=enable_all_app,json=enableAllApp,proto3" json:"enable_all_app,omitempty"`
	EnableAllAppGroup bool       `protobuf:"varint,4,opt,name=enable_all_app_group,json=enableAllAppGroup,proto3" json:"enable_all_app_group,omitempty"`
	UserAll           bool       `protobuf:"varint,5,opt,name=user_all,json=userAll,proto3" json:"user_all,omitempty"`
	UserIds           []string   `protobuf:"bytes,6,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	GroupIds          []string   `protobuf:"bytes,7,rep,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	ExcludeUserAll    bool       `protobuf:"varint,8,opt,name=exclude_user_all,json=excludeUserAll,proto3" json:"exclude_user_all,omitempty"`
	ExcludeUserIds    []string   `protobuf:"bytes,9,rep,name=exclude_user_ids,json=excludeUserIds,proto3" json:"exclude_user_ids,omitempty"`
	ExcludeGroupIds   []string   `protobuf:"bytes,10,rep,name=exclude_group_ids,json=excludeGroupIds,proto3" json:"exclude_group_ids,omitempty"`
	ContentConfig     []string   `protobuf:"bytes,11,rep,name=content_config,json=contentConfig,proto3" json:"content_config,omitempty"`
	FontStyle         *FontStyle `protobuf:"bytes,12,opt,name=font_style,json=fontStyle,proto3" json:"font_style,omitempty"`
	Secret            bool       `protobuf:"varint,13,opt,name=secret,proto3" json:"secret,omitempty"`
}

func (x *Watermark) Reset() {
	*x = Watermark{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Watermark) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Watermark) ProtoMessage() {}

func (x *Watermark) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Watermark.ProtoReflect.Descriptor instead.
func (*Watermark) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{25}
}

func (x *Watermark) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Watermark) GetAppIds() []string {
	if x != nil {
		return x.AppIds
	}
	return nil
}

func (x *Watermark) GetEnableAllApp() bool {
	if x != nil {
		return x.EnableAllApp
	}
	return false
}

func (x *Watermark) GetEnableAllAppGroup() bool {
	if x != nil {
		return x.EnableAllAppGroup
	}
	return false
}

func (x *Watermark) GetUserAll() bool {
	if x != nil {
		return x.UserAll
	}
	return false
}

func (x *Watermark) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *Watermark) GetGroupIds() []string {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

func (x *Watermark) GetExcludeUserAll() bool {
	if x != nil {
		return x.ExcludeUserAll
	}
	return false
}

func (x *Watermark) GetExcludeUserIds() []string {
	if x != nil {
		return x.ExcludeUserIds
	}
	return nil
}

func (x *Watermark) GetExcludeGroupIds() []string {
	if x != nil {
		return x.ExcludeGroupIds
	}
	return nil
}

func (x *Watermark) GetContentConfig() []string {
	if x != nil {
		return x.ContentConfig
	}
	return nil
}

func (x *Watermark) GetFontStyle() *FontStyle {
	if x != nil {
		return x.FontStyle
	}
	return nil
}

func (x *Watermark) GetSecret() bool {
	if x != nil {
		return x.Secret
	}
	return false
}

type FontStyle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Color          string `protobuf:"bytes,1,opt,name=color,proto3" json:"color,omitempty"`
	Alpha          uint32 `protobuf:"varint,2,opt,name=alpha,proto3" json:"alpha,omitempty"`
	Size           uint32 `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Rotate         uint32 `protobuf:"varint,4,opt,name=rotate,proto3" json:"rotate,omitempty"`
	LineSpacing    uint32 `protobuf:"varint,5,opt,name=line_spacing,json=lineSpacing,proto3" json:"line_spacing,omitempty"`
	ColumnsSpacing uint32 `protobuf:"varint,6,opt,name=columns_spacing,json=columnsSpacing,proto3" json:"columns_spacing,omitempty"`
}

func (x *FontStyle) Reset() {
	*x = FontStyle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FontStyle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FontStyle) ProtoMessage() {}

func (x *FontStyle) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FontStyle.ProtoReflect.Descriptor instead.
func (*FontStyle) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{26}
}

func (x *FontStyle) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *FontStyle) GetAlpha() uint32 {
	if x != nil {
		return x.Alpha
	}
	return 0
}

func (x *FontStyle) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FontStyle) GetRotate() uint32 {
	if x != nil {
		return x.Rotate
	}
	return 0
}

func (x *FontStyle) GetLineSpacing() uint32 {
	if x != nil {
		return x.LineSpacing
	}
	return 0
}

func (x *FontStyle) GetColumnsSpacing() uint32 {
	if x != nil {
		return x.ColumnsSpacing
	}
	return 0
}

type ApplicationGatewayInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AppName             string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	ServerAddress       string `protobuf:"bytes,3,opt,name=server_address,json=serverAddress,proto3" json:"server_address,omitempty"`
	ServerSchema        string `protobuf:"bytes,4,opt,name=server_schema,json=serverSchema,proto3" json:"server_schema,omitempty"`
	PublishAddress      string `protobuf:"bytes,5,opt,name=publish_address,json=publishAddress,proto3" json:"publish_address,omitempty"`
	PublishSchema       string `protobuf:"bytes,6,opt,name=publish_schema,json=publishSchema,proto3" json:"publish_schema,omitempty"`
	WebCompatibleConfig []byte `protobuf:"bytes,7,opt,name=web_compatible_config,json=webCompatibleConfig,proto3" json:"web_compatible_config,omitempty"`
	HealthConfig        []byte `protobuf:"bytes,9,opt,name=health_config,json=healthConfig,proto3" json:"health_config,omitempty"`
	Uri                 string `protobuf:"bytes,8,opt,name=uri,proto3" json:"uri,omitempty"`
	AppType             string `protobuf:"bytes,10,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	AppStatus           int32  `protobuf:"varint,11,opt,name=app_status,json=appStatus,proto3" json:"app_status,omitempty"`
}

func (x *ApplicationGatewayInfo) Reset() {
	*x = ApplicationGatewayInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationGatewayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationGatewayInfo) ProtoMessage() {}

func (x *ApplicationGatewayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationGatewayInfo.ProtoReflect.Descriptor instead.
func (*ApplicationGatewayInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{27}
}

func (x *ApplicationGatewayInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetServerAddress() string {
	if x != nil {
		return x.ServerAddress
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetServerSchema() string {
	if x != nil {
		return x.ServerSchema
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetPublishAddress() string {
	if x != nil {
		return x.PublishAddress
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetPublishSchema() string {
	if x != nil {
		return x.PublishSchema
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetWebCompatibleConfig() []byte {
	if x != nil {
		return x.WebCompatibleConfig
	}
	return nil
}

func (x *ApplicationGatewayInfo) GetHealthConfig() []byte {
	if x != nil {
		return x.HealthConfig
	}
	return nil
}

func (x *ApplicationGatewayInfo) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *ApplicationGatewayInfo) GetAppStatus() int32 {
	if x != nil {
		return x.AppStatus
	}
	return 0
}

type WebGatewayHostsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WebGatewayHostsReq) Reset() {
	*x = WebGatewayHostsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayHostsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayHostsReq) ProtoMessage() {}

func (x *WebGatewayHostsReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayHostsReq.ProtoReflect.Descriptor instead.
func (*WebGatewayHostsReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{28}
}

type WebGatewayHostsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hosts []*WebGatewayHosts `protobuf:"bytes,1,rep,name=hosts,proto3" json:"hosts,omitempty"`
}

func (x *WebGatewayHostsResp) Reset() {
	*x = WebGatewayHostsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayHostsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayHostsResp) ProtoMessage() {}

func (x *WebGatewayHostsResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayHostsResp.ProtoReflect.Descriptor instead.
func (*WebGatewayHostsResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{29}
}

func (x *WebGatewayHostsResp) GetHosts() []*WebGatewayHosts {
	if x != nil {
		return x.Hosts
	}
	return nil
}

type WebGatewayHosts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host    string   `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Address []string `protobuf:"bytes,2,rep,name=address,proto3" json:"address,omitempty"`
	Remark  string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *WebGatewayHosts) Reset() {
	*x = WebGatewayHosts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayHosts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayHosts) ProtoMessage() {}

func (x *WebGatewayHosts) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayHosts.ProtoReflect.Descriptor instead.
func (*WebGatewayHosts) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{30}
}

func (x *WebGatewayHosts) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *WebGatewayHosts) GetAddress() []string {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *WebGatewayHosts) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type WebGatewayVirtualIPPoolsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId string `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
}

func (x *WebGatewayVirtualIPPoolsReq) Reset() {
	*x = WebGatewayVirtualIPPoolsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayVirtualIPPoolsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayVirtualIPPoolsReq) ProtoMessage() {}

func (x *WebGatewayVirtualIPPoolsReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayVirtualIPPoolsReq.ProtoReflect.Descriptor instead.
func (*WebGatewayVirtualIPPoolsReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{31}
}

func (x *WebGatewayVirtualIPPoolsReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

type WebGatewayVirtualIPPoolsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pools          []*VirtualIPPoolConfig   `protobuf:"bytes,1,rep,name=pools,proto3" json:"pools,omitempty"`
	GlobalSettings *VirtualIPGlobalSettings `protobuf:"bytes,2,opt,name=global_settings,json=globalSettings,proto3" json:"global_settings,omitempty"` // 添加全局设置
}

func (x *WebGatewayVirtualIPPoolsResp) Reset() {
	*x = WebGatewayVirtualIPPoolsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayVirtualIPPoolsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayVirtualIPPoolsResp) ProtoMessage() {}

func (x *WebGatewayVirtualIPPoolsResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayVirtualIPPoolsResp.ProtoReflect.Descriptor instead.
func (*WebGatewayVirtualIPPoolsResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{32}
}

func (x *WebGatewayVirtualIPPoolsResp) GetPools() []*VirtualIPPoolConfig {
	if x != nil {
		return x.Pools
	}
	return nil
}

func (x *WebGatewayVirtualIPPoolsResp) GetGlobalSettings() *VirtualIPGlobalSettings {
	if x != nil {
		return x.GlobalSettings
	}
	return nil
}

type VirtualIPGlobalSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled           bool   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`                                                // 全局功能开关
	GlobalMaxDuration int32  `protobuf:"varint,2,opt,name=global_max_duration,json=globalMaxDuration,proto3" json:"global_max_duration,omitempty"` // 全局最大分配时长(小时)
	Description       string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                                         // 功能描述
}

func (x *VirtualIPGlobalSettings) Reset() {
	*x = VirtualIPGlobalSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualIPGlobalSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualIPGlobalSettings) ProtoMessage() {}

func (x *VirtualIPGlobalSettings) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualIPGlobalSettings.ProtoReflect.Descriptor instead.
func (*VirtualIPGlobalSettings) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{33}
}

func (x *VirtualIPGlobalSettings) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *VirtualIPGlobalSettings) GetGlobalMaxDuration() int32 {
	if x != nil {
		return x.GlobalMaxDuration
	}
	return 0
}

func (x *VirtualIPGlobalSettings) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type VirtualIPPoolConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IpRange          string               `protobuf:"bytes,3,opt,name=ip_range,json=ipRange,proto3" json:"ip_range,omitempty"`
	PoolType         string               `protobuf:"bytes,4,opt,name=pool_type,json=poolType,proto3" json:"pool_type,omitempty"` // "shared" 或 "dedicated"
	IpExpiryDuration int32                `protobuf:"varint,5,opt,name=ip_expiry_duration,json=ipExpiryDuration,proto3" json:"ip_expiry_duration,omitempty"`
	CleanupInterval  int32                `protobuf:"varint,6,opt,name=cleanup_interval,json=cleanupInterval,proto3" json:"cleanup_interval,omitempty"`
	AllocationPolicy *AllocationPolicy    `protobuf:"bytes,7,opt,name=allocation_policy,json=allocationPolicy,proto3" json:"allocation_policy,omitempty"`
	ReleasePolicy    *ReleasePolicy       `protobuf:"bytes,8,opt,name=release_policy,json=releasePolicy,proto3" json:"release_policy,omitempty"`
	DirectoryConfigs []*DirectoryConfig   `protobuf:"bytes,9,rep,name=directory_configs,json=directoryConfigs,proto3" json:"directory_configs,omitempty"`
	DedicatedConfigs []*DedicatedIPConfig `protobuf:"bytes,10,rep,name=dedicated_configs,json=dedicatedConfigs,proto3" json:"dedicated_configs,omitempty"` // 独享IP配置
}

func (x *VirtualIPPoolConfig) Reset() {
	*x = VirtualIPPoolConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualIPPoolConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualIPPoolConfig) ProtoMessage() {}

func (x *VirtualIPPoolConfig) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualIPPoolConfig.ProtoReflect.Descriptor instead.
func (*VirtualIPPoolConfig) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{34}
}

func (x *VirtualIPPoolConfig) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VirtualIPPoolConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VirtualIPPoolConfig) GetIpRange() string {
	if x != nil {
		return x.IpRange
	}
	return ""
}

func (x *VirtualIPPoolConfig) GetPoolType() string {
	if x != nil {
		return x.PoolType
	}
	return ""
}

func (x *VirtualIPPoolConfig) GetIpExpiryDuration() int32 {
	if x != nil {
		return x.IpExpiryDuration
	}
	return 0
}

func (x *VirtualIPPoolConfig) GetCleanupInterval() int32 {
	if x != nil {
		return x.CleanupInterval
	}
	return 0
}

func (x *VirtualIPPoolConfig) GetAllocationPolicy() *AllocationPolicy {
	if x != nil {
		return x.AllocationPolicy
	}
	return nil
}

func (x *VirtualIPPoolConfig) GetReleasePolicy() *ReleasePolicy {
	if x != nil {
		return x.ReleasePolicy
	}
	return nil
}

func (x *VirtualIPPoolConfig) GetDirectoryConfigs() []*DirectoryConfig {
	if x != nil {
		return x.DirectoryConfigs
	}
	return nil
}

func (x *VirtualIPPoolConfig) GetDedicatedConfigs() []*DedicatedIPConfig {
	if x != nil {
		return x.DedicatedConfigs
	}
	return nil
}

type AllocationPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Strategy      string `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"` // "round_robin", "random", "least_used"
	MaxIpsPerUser int32  `protobuf:"varint,2,opt,name=max_ips_per_user,json=maxIpsPerUser,proto3" json:"max_ips_per_user,omitempty"`
}

func (x *AllocationPolicy) Reset() {
	*x = AllocationPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllocationPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocationPolicy) ProtoMessage() {}

func (x *AllocationPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocationPolicy.ProtoReflect.Descriptor instead.
func (*AllocationPolicy) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{35}
}

func (x *AllocationPolicy) GetStrategy() string {
	if x != nil {
		return x.Strategy
	}
	return ""
}

func (x *AllocationPolicy) GetMaxIpsPerUser() int32 {
	if x != nil {
		return x.MaxIpsPerUser
	}
	return 0
}

type ReleasePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseOnLogout     bool  `protobuf:"varint,1,opt,name=release_on_logout,json=releaseOnLogout,proto3" json:"release_on_logout,omitempty"`
	IdleTimeoutHours    int32 `protobuf:"varint,2,opt,name=idle_timeout_hours,json=idleTimeoutHours,proto3" json:"idle_timeout_hours,omitempty"`
	ForceReleaseOnLimit bool  `protobuf:"varint,3,opt,name=force_release_on_limit,json=forceReleaseOnLimit,proto3" json:"force_release_on_limit,omitempty"`
}

func (x *ReleasePolicy) Reset() {
	*x = ReleasePolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleasePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleasePolicy) ProtoMessage() {}

func (x *ReleasePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleasePolicy.ProtoReflect.Descriptor instead.
func (*ReleasePolicy) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{36}
}

func (x *ReleasePolicy) GetReleaseOnLogout() bool {
	if x != nil {
		return x.ReleaseOnLogout
	}
	return false
}

func (x *ReleasePolicy) GetIdleTimeoutHours() int32 {
	if x != nil {
		return x.IdleTimeoutHours
	}
	return 0
}

func (x *ReleasePolicy) GetForceReleaseOnLimit() bool {
	if x != nil {
		return x.ForceReleaseOnLimit
	}
	return false
}

type DirectoryConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DirectoryId   string   `protobuf:"bytes,1,opt,name=directory_id,json=directoryId,proto3" json:"directory_id,omitempty"`
	DirectoryName string   `protobuf:"bytes,2,opt,name=directory_name,json=directoryName,proto3" json:"directory_name,omitempty"`
	UserGroups    []string `protobuf:"bytes,3,rep,name=user_groups,json=userGroups,proto3" json:"user_groups,omitempty"`
	Config        string   `protobuf:"bytes,4,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *DirectoryConfig) Reset() {
	*x = DirectoryConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectoryConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryConfig) ProtoMessage() {}

func (x *DirectoryConfig) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryConfig.ProtoReflect.Descriptor instead.
func (*DirectoryConfig) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{37}
}

func (x *DirectoryConfig) GetDirectoryId() string {
	if x != nil {
		return x.DirectoryId
	}
	return ""
}

func (x *DirectoryConfig) GetDirectoryName() string {
	if x != nil {
		return x.DirectoryName
	}
	return ""
}

func (x *DirectoryConfig) GetUserGroups() []string {
	if x != nil {
		return x.UserGroups
	}
	return nil
}

func (x *DirectoryConfig) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

type DedicatedIPConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName   string   `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	VirtualIps []string `protobuf:"bytes,3,rep,name=virtual_ips,json=virtualIps,proto3" json:"virtual_ips,omitempty"`
	Priority   int32    `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *DedicatedIPConfig) Reset() {
	*x = DedicatedIPConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedicatedIPConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedicatedIPConfig) ProtoMessage() {}

func (x *DedicatedIPConfig) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedicatedIPConfig.ProtoReflect.Descriptor instead.
func (*DedicatedIPConfig) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{38}
}

func (x *DedicatedIPConfig) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DedicatedIPConfig) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *DedicatedIPConfig) GetVirtualIps() []string {
	if x != nil {
		return x.VirtualIps
	}
	return nil
}

func (x *DedicatedIPConfig) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

// 网关上报IP分配状态
type ReportVirtualIPAllocationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId string                `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	Allocations []*IPAllocation       `protobuf:"bytes,2,rep,name=allocations,proto3" json:"allocations,omitempty"`
	PoolStats   map[string]*PoolStats `protobuf:"bytes,3,rep,name=pool_stats,json=poolStats,proto3" json:"pool_stats,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ReportTime  int64                 `protobuf:"varint,4,opt,name=report_time,json=reportTime,proto3" json:"report_time,omitempty"` // Unix时间戳
}

func (x *ReportVirtualIPAllocationsReq) Reset() {
	*x = ReportVirtualIPAllocationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportVirtualIPAllocationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportVirtualIPAllocationsReq) ProtoMessage() {}

func (x *ReportVirtualIPAllocationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportVirtualIPAllocationsReq.ProtoReflect.Descriptor instead.
func (*ReportVirtualIPAllocationsReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{39}
}

func (x *ReportVirtualIPAllocationsReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

func (x *ReportVirtualIPAllocationsReq) GetAllocations() []*IPAllocation {
	if x != nil {
		return x.Allocations
	}
	return nil
}

func (x *ReportVirtualIPAllocationsReq) GetPoolStats() map[string]*PoolStats {
	if x != nil {
		return x.PoolStats
	}
	return nil
}

func (x *ReportVirtualIPAllocationsReq) GetReportTime() int64 {
	if x != nil {
		return x.ReportTime
	}
	return 0
}

type ReportHealthCheckResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HealthResults map[string]int32 `protobuf:"bytes,1,rep,name=health_results,json=healthResults,proto3" json:"health_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 存储应用ID和健康状态的映射
}

func (x *ReportHealthCheckResultReq) Reset() {
	*x = ReportHealthCheckResultReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportHealthCheckResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportHealthCheckResultReq) ProtoMessage() {}

func (x *ReportHealthCheckResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportHealthCheckResultReq.ProtoReflect.Descriptor instead.
func (*ReportHealthCheckResultReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{40}
}

func (x *ReportHealthCheckResultReq) GetHealthResults() map[string]int32 {
	if x != nil {
		return x.HealthResults
	}
	return nil
}

type ReportVirtualIPAllocationsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success   bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message   string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ErrorCode int32    `protobuf:"varint,3,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	FailedIps []string `protobuf:"bytes,4,rep,name=failed_ips,json=failedIps,proto3" json:"failed_ips,omitempty"`
}

func (x *ReportVirtualIPAllocationsResp) Reset() {
	*x = ReportVirtualIPAllocationsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportVirtualIPAllocationsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportVirtualIPAllocationsResp) ProtoMessage() {}

func (x *ReportVirtualIPAllocationsResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportVirtualIPAllocationsResp.ProtoReflect.Descriptor instead.
func (*ReportVirtualIPAllocationsResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{41}
}

func (x *ReportVirtualIPAllocationsResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ReportVirtualIPAllocationsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ReportVirtualIPAllocationsResp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *ReportVirtualIPAllocationsResp) GetFailedIps() []string {
	if x != nil {
		return x.FailedIps
	}
	return nil
}

type ReportHealthCheckResultResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success   bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message   string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ErrorCode int32  `protobuf:"varint,4,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
}

func (x *ReportHealthCheckResultResp) Reset() {
	*x = ReportHealthCheckResultResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportHealthCheckResultResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportHealthCheckResultResp) ProtoMessage() {}

func (x *ReportHealthCheckResultResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportHealthCheckResultResp.ProtoReflect.Descriptor instead.
func (*ReportHealthCheckResultResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{42}
}

func (x *ReportHealthCheckResultResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ReportHealthCheckResultResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ReportHealthCheckResultResp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

type IPAllocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpAddress   string `protobuf:"bytes,1,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	UserId      string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DeviceId    string `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	PoolName    string `protobuf:"bytes,4,opt,name=pool_name,json=poolName,proto3" json:"pool_name,omitempty"`
	AllocatedAt int64  `protobuf:"varint,5,opt,name=allocated_at,json=allocatedAt,proto3" json:"allocated_at,omitempty"`
	ExpiresAt   int64  `protobuf:"varint,6,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	LastUsedAt  int64  `protobuf:"varint,7,opt,name=last_used_at,json=lastUsedAt,proto3" json:"last_used_at,omitempty"`
	Status      string `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	// 当前报告周期内的流量增量
	PeriodUpstreamBytes   int64 `protobuf:"varint,9,opt,name=period_upstream_bytes,json=periodUpstreamBytes,proto3" json:"period_upstream_bytes,omitempty"`
	PeriodDownstreamBytes int64 `protobuf:"varint,10,opt,name=period_downstream_bytes,json=periodDownstreamBytes,proto3" json:"period_downstream_bytes,omitempty"`
	// 总累计流量（整个分配期间的总量）
	TotalUpstreamBytes   int64 `protobuf:"varint,11,opt,name=total_upstream_bytes,json=totalUpstreamBytes,proto3" json:"total_upstream_bytes,omitempty"`
	TotalDownstreamBytes int64 `protobuf:"varint,12,opt,name=total_downstream_bytes,json=totalDownstreamBytes,proto3" json:"total_downstream_bytes,omitempty"`
	// 统计信息
	SessionCount    int32 `protobuf:"varint,13,opt,name=session_count,json=sessionCount,proto3" json:"session_count,omitempty"`
	LastTrafficTime int64 `protobuf:"varint,14,opt,name=last_traffic_time,json=lastTrafficTime,proto3" json:"last_traffic_time,omitempty"` // 最后有流量的时间
}

func (x *IPAllocation) Reset() {
	*x = IPAllocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPAllocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPAllocation) ProtoMessage() {}

func (x *IPAllocation) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPAllocation.ProtoReflect.Descriptor instead.
func (*IPAllocation) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{43}
}

func (x *IPAllocation) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *IPAllocation) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *IPAllocation) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *IPAllocation) GetPoolName() string {
	if x != nil {
		return x.PoolName
	}
	return ""
}

func (x *IPAllocation) GetAllocatedAt() int64 {
	if x != nil {
		return x.AllocatedAt
	}
	return 0
}

func (x *IPAllocation) GetExpiresAt() int64 {
	if x != nil {
		return x.ExpiresAt
	}
	return 0
}

func (x *IPAllocation) GetLastUsedAt() int64 {
	if x != nil {
		return x.LastUsedAt
	}
	return 0
}

func (x *IPAllocation) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *IPAllocation) GetPeriodUpstreamBytes() int64 {
	if x != nil {
		return x.PeriodUpstreamBytes
	}
	return 0
}

func (x *IPAllocation) GetPeriodDownstreamBytes() int64 {
	if x != nil {
		return x.PeriodDownstreamBytes
	}
	return 0
}

func (x *IPAllocation) GetTotalUpstreamBytes() int64 {
	if x != nil {
		return x.TotalUpstreamBytes
	}
	return 0
}

func (x *IPAllocation) GetTotalDownstreamBytes() int64 {
	if x != nil {
		return x.TotalDownstreamBytes
	}
	return 0
}

func (x *IPAllocation) GetSessionCount() int32 {
	if x != nil {
		return x.SessionCount
	}
	return 0
}

func (x *IPAllocation) GetLastTrafficTime() int64 {
	if x != nil {
		return x.LastTrafficTime
	}
	return 0
}

type PoolStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalIps     int32 `protobuf:"varint,1,opt,name=total_ips,json=totalIps,proto3" json:"total_ips,omitempty"`
	AllocatedIps int32 `protobuf:"varint,2,opt,name=allocated_ips,json=allocatedIps,proto3" json:"allocated_ips,omitempty"`
	AvailableIps int32 `protobuf:"varint,3,opt,name=available_ips,json=availableIps,proto3" json:"available_ips,omitempty"`
	LastUpdated  int64 `protobuf:"varint,4,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"` // Unix时间戳
}

func (x *PoolStats) Reset() {
	*x = PoolStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PoolStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolStats) ProtoMessage() {}

func (x *PoolStats) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolStats.ProtoReflect.Descriptor instead.
func (*PoolStats) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{44}
}

func (x *PoolStats) GetTotalIps() int32 {
	if x != nil {
		return x.TotalIps
	}
	return 0
}

func (x *PoolStats) GetAllocatedIps() int32 {
	if x != nil {
		return x.AllocatedIps
	}
	return 0
}

func (x *PoolStats) GetAvailableIps() int32 {
	if x != nil {
		return x.AvailableIps
	}
	return 0
}

func (x *PoolStats) GetLastUpdated() int64 {
	if x != nil {
		return x.LastUpdated
	}
	return 0
}

type TrafficStatsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId      string           `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	TrafficBuckets   []*TrafficBucket `protobuf:"bytes,2,rep,name=traffic_buckets,json=trafficBuckets,proto3" json:"traffic_buckets,omitempty"`
	ReportTime       int64            `protobuf:"varint,3,opt,name=report_time,json=reportTime,proto3" json:"report_time,omitempty"`
	ProcessedBuckets int32            `protobuf:"varint,4,opt,name=processed_buckets,json=processedBuckets,proto3" json:"processed_buckets,omitempty"`
	FailedBuckets    int32            `protobuf:"varint,5,opt,name=failed_buckets,json=failedBuckets,proto3" json:"failed_buckets,omitempty"`
}

func (x *TrafficStatsReq) Reset() {
	*x = TrafficStatsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficStatsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficStatsReq) ProtoMessage() {}

func (x *TrafficStatsReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficStatsReq.ProtoReflect.Descriptor instead.
func (*TrafficStatsReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{45}
}

func (x *TrafficStatsReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

func (x *TrafficStatsReq) GetTrafficBuckets() []*TrafficBucket {
	if x != nil {
		return x.TrafficBuckets
	}
	return nil
}

func (x *TrafficStatsReq) GetReportTime() int64 {
	if x != nil {
		return x.ReportTime
	}
	return 0
}

func (x *TrafficStatsReq) GetProcessedBuckets() int32 {
	if x != nil {
		return x.ProcessedBuckets
	}
	return 0
}

func (x *TrafficStatsReq) GetFailedBuckets() int32 {
	if x != nil {
		return x.FailedBuckets
	}
	return 0
}

type TrafficBucket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpAddress       string `protobuf:"bytes,1,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	UserId          string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BucketStartTime int64  `protobuf:"varint,3,opt,name=bucket_start_time,json=bucketStartTime,proto3" json:"bucket_start_time,omitempty"` // 时间桶开始时间
	BucketEndTime   int64  `protobuf:"varint,4,opt,name=bucket_end_time,json=bucketEndTime,proto3" json:"bucket_end_time,omitempty"`       // 时间桶结束时间
	UpstreamBytes   int64  `protobuf:"varint,5,opt,name=upstream_bytes,json=upstreamBytes,proto3" json:"upstream_bytes,omitempty"`         // 该时间段内的上行流量
	DownstreamBytes int64  `protobuf:"varint,6,opt,name=downstream_bytes,json=downstreamBytes,proto3" json:"downstream_bytes,omitempty"`   // 该时间段内的下行流量
	SessionCount    int32  `protobuf:"varint,7,opt,name=session_count,json=sessionCount,proto3" json:"session_count,omitempty"`            // 该时间段内的会话数
}

func (x *TrafficBucket) Reset() {
	*x = TrafficBucket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficBucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficBucket) ProtoMessage() {}

func (x *TrafficBucket) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficBucket.ProtoReflect.Descriptor instead.
func (*TrafficBucket) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{46}
}

func (x *TrafficBucket) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *TrafficBucket) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TrafficBucket) GetBucketStartTime() int64 {
	if x != nil {
		return x.BucketStartTime
	}
	return 0
}

func (x *TrafficBucket) GetBucketEndTime() int64 {
	if x != nil {
		return x.BucketEndTime
	}
	return 0
}

func (x *TrafficBucket) GetUpstreamBytes() int64 {
	if x != nil {
		return x.UpstreamBytes
	}
	return 0
}

func (x *TrafficBucket) GetDownstreamBytes() int64 {
	if x != nil {
		return x.DownstreamBytes
	}
	return 0
}

func (x *TrafficBucket) GetSessionCount() int32 {
	if x != nil {
		return x.SessionCount
	}
	return 0
}

type TrafficStatsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *TrafficStatsResp) Reset() {
	*x = TrafficStatsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficStatsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficStatsResp) ProtoMessage() {}

func (x *TrafficStatsResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficStatsResp.ProtoReflect.Descriptor instead.
func (*TrafficStatsResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{47}
}

func (x *TrafficStatsResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TrafficStatsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 网关命令管理
type GatewayCommandReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId string `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
}

func (x *GatewayCommandReq) Reset() {
	*x = GatewayCommandReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayCommandReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayCommandReq) ProtoMessage() {}

func (x *GatewayCommandReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayCommandReq.ProtoReflect.Descriptor instead.
func (*GatewayCommandReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{48}
}

func (x *GatewayCommandReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

type GatewayCommandResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commands []*GatewayCommand `protobuf:"bytes,1,rep,name=commands,proto3" json:"commands,omitempty"`
}

func (x *GatewayCommandResp) Reset() {
	*x = GatewayCommandResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayCommandResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayCommandResp) ProtoMessage() {}

func (x *GatewayCommandResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayCommandResp.ProtoReflect.Descriptor instead.
func (*GatewayCommandResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{49}
}

func (x *GatewayCommandResp) GetCommands() []*GatewayCommand {
	if x != nil {
		return x.Commands
	}
	return nil
}

type GatewayCommand struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommandId      string            `protobuf:"bytes,1,opt,name=command_id,json=commandId,proto3" json:"command_id,omitempty"`                                                                          // 命令ID
	CommandType    string            `protobuf:"bytes,2,opt,name=command_type,json=commandType,proto3" json:"command_type,omitempty"`                                                                    // 命令类型: "virtual_ip", "network", "security"等
	Action         string            `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`                                                                                                 // 命令动作: "manual_release", "sync_release", "update_config"等
	TargetResource string            `protobuf:"bytes,4,opt,name=target_resource,json=targetResource,proto3" json:"target_resource,omitempty"`                                                           // 目标资源标识符(如IP地址、用户ID等)
	Parameters     map[string]string `protobuf:"bytes,5,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 命令参数(key-value格式，支持扩展)
	Reason         string            `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                                                                                                 // 执行原因
	CreatedAt      int64             `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                                         // 命令创建时间
	ExpiresAt      int64             `protobuf:"varint,8,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`                                                                         // 命令过期时间
	Status         string            `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`                                                                                                 // 命令状态: "pending", "processing", "completed", "failed"
	CreatedBy      string            `protobuf:"bytes,10,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                                         // 创建者
}

func (x *GatewayCommand) Reset() {
	*x = GatewayCommand{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayCommand) ProtoMessage() {}

func (x *GatewayCommand) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayCommand.ProtoReflect.Descriptor instead.
func (*GatewayCommand) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{50}
}

func (x *GatewayCommand) GetCommandId() string {
	if x != nil {
		return x.CommandId
	}
	return ""
}

func (x *GatewayCommand) GetCommandType() string {
	if x != nil {
		return x.CommandType
	}
	return ""
}

func (x *GatewayCommand) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *GatewayCommand) GetTargetResource() string {
	if x != nil {
		return x.TargetResource
	}
	return ""
}

func (x *GatewayCommand) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *GatewayCommand) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *GatewayCommand) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GatewayCommand) GetExpiresAt() int64 {
	if x != nil {
		return x.ExpiresAt
	}
	return 0
}

func (x *GatewayCommand) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GatewayCommand) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type GatewayCommandResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId string                  `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	Results     []*GatewayCommandResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *GatewayCommandResultReq) Reset() {
	*x = GatewayCommandResultReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayCommandResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayCommandResultReq) ProtoMessage() {}

func (x *GatewayCommandResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayCommandResultReq.ProtoReflect.Descriptor instead.
func (*GatewayCommandResultReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{51}
}

func (x *GatewayCommandResultReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

func (x *GatewayCommandResultReq) GetResults() []*GatewayCommandResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type GatewayCommandResultResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *GatewayCommandResultResp) Reset() {
	*x = GatewayCommandResultResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayCommandResultResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayCommandResultResp) ProtoMessage() {}

func (x *GatewayCommandResultResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayCommandResultResp.ProtoReflect.Descriptor instead.
func (*GatewayCommandResultResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{52}
}

func (x *GatewayCommandResultResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GatewayCommandResultResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GatewayCommandResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommandId  string            `protobuf:"bytes,1,opt,name=command_id,json=commandId,proto3" json:"command_id,omitempty"`                                                                                            // 命令ID
	Status     string            `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`                                                                                                                   // 执行状态: "completed", "failed"
	Message    string            `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`                                                                                                                 // 执行结果消息
	ExecutedAt int64             `protobuf:"varint,4,opt,name=executed_at,json=executedAt,proto3" json:"executed_at,omitempty"`                                                                                        // 执行时间
	ResultData map[string]string `protobuf:"bytes,5,rep,name=result_data,json=resultData,proto3" json:"result_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 执行结果数据
}

func (x *GatewayCommandResult) Reset() {
	*x = GatewayCommandResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayCommandResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayCommandResult) ProtoMessage() {}

func (x *GatewayCommandResult) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayCommandResult.ProtoReflect.Descriptor instead.
func (*GatewayCommandResult) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{53}
}

func (x *GatewayCommandResult) GetCommandId() string {
	if x != nil {
		return x.CommandId
	}
	return ""
}

func (x *GatewayCommandResult) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GatewayCommandResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GatewayCommandResult) GetExecutedAt() int64 {
	if x != nil {
		return x.ExecutedAt
	}
	return 0
}

func (x *GatewayCommandResult) GetResultData() map[string]string {
	if x != nil {
		return x.ResultData
	}
	return nil
}

// 网关拉取SSO IDP配置请求
type WebGatewayRsSSOIDPReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CorpId string `protobuf:"bytes,1,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"` // 企业ID
	Type   string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`                   // IDP类型: dingtalk/feishu/qiyewx
}

func (x *WebGatewayRsSSOIDPReq) Reset() {
	*x = WebGatewayRsSSOIDPReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayRsSSOIDPReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayRsSSOIDPReq) ProtoMessage() {}

func (x *WebGatewayRsSSOIDPReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayRsSSOIDPReq.ProtoReflect.Descriptor instead.
func (*WebGatewayRsSSOIDPReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{54}
}

func (x *WebGatewayRsSSOIDPReq) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *WebGatewayRsSSOIDPReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// 网关拉取SSO IDP配置响应
type WebGatewayRsSSOIDPResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Idps []*SSOIDPInfo `protobuf:"bytes,1,rep,name=idps,proto3" json:"idps,omitempty"`
}

func (x *WebGatewayRsSSOIDPResp) Reset() {
	*x = WebGatewayRsSSOIDPResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayRsSSOIDPResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayRsSSOIDPResp) ProtoMessage() {}

func (x *WebGatewayRsSSOIDPResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayRsSSOIDPResp.ProtoReflect.Descriptor instead.
func (*WebGatewayRsSSOIDPResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{55}
}

func (x *WebGatewayRsSSOIDPResp) GetIdps() []*SSOIDPInfo {
	if x != nil {
		return x.Idps
	}
	return nil
}

// SSO IDP配置信息
type SSOIDPInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                      // IDP ID
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  // IDP名称
	Type        string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                                  // IDP类型
	CorpId      string `protobuf:"bytes,4,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`                // 企业ID
	AppId       string `protobuf:"bytes,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                   // 应用ID
	AppKey      string `protobuf:"bytes,6,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`                // 应用Key
	AppSecret   string `protobuf:"bytes,7,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`       // 应用Secret (加密)
	RedirectUri string `protobuf:"bytes,8,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"` // 回调地址
	Enable      bool   `protobuf:"varint,9,opt,name=enable,proto3" json:"enable,omitempty"`                             // 是否启用
	CreatedAt   int64  `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`     // 创建时间
	UpdatedAt   int64  `protobuf:"varint,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`     // 更新时间
}

func (x *SSOIDPInfo) Reset() {
	*x = SSOIDPInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SSOIDPInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSOIDPInfo) ProtoMessage() {}

func (x *SSOIDPInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSOIDPInfo.ProtoReflect.Descriptor instead.
func (*SSOIDPInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{56}
}

func (x *SSOIDPInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SSOIDPInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SSOIDPInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SSOIDPInfo) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *SSOIDPInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SSOIDPInfo) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *SSOIDPInfo) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

func (x *SSOIDPInfo) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *SSOIDPInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *SSOIDPInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SSOIDPInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 网关获取平台域名请求
type WebGatewayPlatformDomainReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId uint64 `protobuf:"varint,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"` // 网关ID
}

func (x *WebGatewayPlatformDomainReq) Reset() {
	*x = WebGatewayPlatformDomainReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayPlatformDomainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayPlatformDomainReq) ProtoMessage() {}

func (x *WebGatewayPlatformDomainReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayPlatformDomainReq.ProtoReflect.Descriptor instead.
func (*WebGatewayPlatformDomainReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{57}
}

func (x *WebGatewayPlatformDomainReq) GetApplianceId() uint64 {
	if x != nil {
		return x.ApplianceId
	}
	return 0
}

// 网关获取平台域名响应
type WebGatewayPlatformDomainResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformDomain string `protobuf:"bytes,1,opt,name=platform_domain,json=platformDomain,proto3" json:"platform_domain,omitempty"` // 平台域名
	Success        bool   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`                                    // 是否成功获取
	Message        string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`                                     // 错误信息
}

func (x *WebGatewayPlatformDomainResp) Reset() {
	*x = WebGatewayPlatformDomainResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayPlatformDomainResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayPlatformDomainResp) ProtoMessage() {}

func (x *WebGatewayPlatformDomainResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayPlatformDomainResp.ProtoReflect.Descriptor instead.
func (*WebGatewayPlatformDomainResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{58}
}

func (x *WebGatewayPlatformDomainResp) GetPlatformDomain() string {
	if x != nil {
		return x.PlatformDomain
	}
	return ""
}

func (x *WebGatewayPlatformDomainResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *WebGatewayPlatformDomainResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 网关拉取微信验证文件请求
type WebGatewayWechatVerifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplianceId string `protobuf:"bytes,1,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"` // 网关ID
}

func (x *WebGatewayWechatVerifyReq) Reset() {
	*x = WebGatewayWechatVerifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayWechatVerifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayWechatVerifyReq) ProtoMessage() {}

func (x *WebGatewayWechatVerifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayWechatVerifyReq.ProtoReflect.Descriptor instead.
func (*WebGatewayWechatVerifyReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{59}
}

func (x *WebGatewayWechatVerifyReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

// 网关拉取微信验证文件响应
type WebGatewayWechatVerifyResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files         []*WechatVerifyFile `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"`                                       // 验证文件列表
	GlobalEnabled bool                `protobuf:"varint,2,opt,name=global_enabled,json=globalEnabled,proto3" json:"global_enabled,omitempty"` // 全局是否启用
}

func (x *WebGatewayWechatVerifyResp) Reset() {
	*x = WebGatewayWechatVerifyResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebGatewayWechatVerifyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebGatewayWechatVerifyResp) ProtoMessage() {}

func (x *WebGatewayWechatVerifyResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebGatewayWechatVerifyResp.ProtoReflect.Descriptor instead.
func (*WebGatewayWechatVerifyResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{60}
}

func (x *WebGatewayWechatVerifyResp) GetFiles() []*WechatVerifyFile {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *WebGatewayWechatVerifyResp) GetGlobalEnabled() bool {
	if x != nil {
		return x.GlobalEnabled
	}
	return false
}

// 微信验证文件信息
type WechatVerifyFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                 // 文件ID
	FileName    string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`     // 文件名，如：WW_verify_xxx.txt
	Content     string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`                       // 文件内容
	IsEnabled   bool   `protobuf:"varint,4,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"` // 是否启用
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`               // 描述信息
	CreatedAt   int64  `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	UpdatedAt   int64  `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间
}

func (x *WechatVerifyFile) Reset() {
	*x = WechatVerifyFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatVerifyFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatVerifyFile) ProtoMessage() {}

func (x *WechatVerifyFile) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatVerifyFile.ProtoReflect.Descriptor instead.
func (*WechatVerifyFile) Descriptor() ([]byte, []int) {
	return file_application_v1_app_proto_rawDescGZIP(), []int{61}
}

func (x *WechatVerifyFile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WechatVerifyFile) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *WechatVerifyFile) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *WechatVerifyFile) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *WechatVerifyFile) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WechatVerifyFile) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *WechatVerifyFile) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_application_v1_app_proto protoreflect.FileDescriptor

var file_application_v1_app_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x73, 0x64, 0x73,
	0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x22,
	0x30, 0x0a, 0x0b, 0x53, 0x65, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x3e, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x30, 0x0a, 0x04, 0x61, 0x70, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x61, 0x70, 0x70,
	0x73, 0x22, 0x85, 0x01, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x48, 0x0a, 0x0a, 0x53, 0x45, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x22, 0x5c, 0x0a, 0x07, 0x53, 0x45, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x17,
	0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x06, 0x61, 0x70, 0x70, 0x49, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0x63, 0x0a, 0x08, 0x53, 0x45, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x12, 0x37, 0x0a,
	0x08, 0x73, 0x65, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x45, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x07, 0x73,
	0x65, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x22, 0x55, 0x0a, 0x11, 0x53, 0x65, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x0b, 0x73,
	0x65, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x52, 0x0a, 0x73, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x22, 0xad, 0x02,
	0x0a, 0x0a, 0x53, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6c,
	0x6c, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x12, 0x0a,
	0x10, 0x57, 0x65, 0x62, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x22, 0x5a, 0x0a, 0x11, 0x57, 0x65, 0x62, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0c, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xf7, 0x01,
	0x0a, 0x0a, 0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x64, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x64, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x70,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xdf, 0x03, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0d, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x70,
	0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41,
	0x6c, 0x6c, 0x41, 0x70, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x6c, 0x6f, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x4c, 0x6f, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x67, 0x6f, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x72, 0x65, 0x67, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x47, 0x61, 0x70, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x47, 0x61, 0x70, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x90, 0x03, 0x0a, 0x07, 0x54, 0x69,
	0x6d, 0x65, 0x47, 0x61, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x61, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x67, 0x61, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x64, 0x61, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x64, 0x61, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x74, 0x75, 0x65, 0x73, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x74, 0x75, 0x65, 0x73, 0x64, 0x61, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x65, 0x64, 0x6e,
	0x65, 0x73, 0x64, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x77, 0x65, 0x64,
	0x6e, 0x65, 0x73, 0x64, 0x61, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68, 0x75, 0x72, 0x73, 0x64,
	0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x68, 0x75, 0x72, 0x73, 0x64,
	0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x61,
	0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x61,
	0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x6c, 0x6c, 0x5f, 0x64, 0x61,
	0x79, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x61, 0x6c, 0x6c, 0x44, 0x61, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x0f, 0x0a, 0x0d,
	0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x53, 0x0a,
	0x0e, 0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x41, 0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x41,
	0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x10, 0x0a, 0x0e, 0x55, 0x63, 0x69, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x22, 0x57, 0x0a, 0x0f, 0x55, 0x63, 0x69, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x0d, 0x75, 0x63, 0x69, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x55, 0x63, 0x69, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x75, 0x63, 0x69, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5b, 0x0a,
	0x0b, 0x55, 0x63, 0x69, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x37, 0x0a, 0x12, 0x57, 0x65,
	0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x13, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x52, 0x73, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x04, 0x61, 0x70,
	0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x57,
	0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x43, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x22, 0x43, 0x0a, 0x13, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52,
	0x73, 0x43, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x04, 0x63, 0x72, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x43, 0x72, 0x74,
	0x52, 0x04, 0x63, 0x72, 0x74, 0x73, 0x22, 0x53, 0x0a, 0x03, 0x43, 0x72, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x65, 0x72,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x18, 0x0a, 0x16, 0x57,
	0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x57, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61,
	0x72, 0x6b, 0x52, 0x65, 0x71, 0x22, 0x5f, 0x0a, 0x17, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x57, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x44, 0x0a, 0x0d, 0x57, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6e,
	0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x61,
	0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x0d, 0x57, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61,
	0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x22, 0xe4, 0x03, 0x0a, 0x09, 0x57, 0x61, 0x74, 0x65, 0x72,
	0x6d, 0x61, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x12, 0x2f, 0x0a, 0x14, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x41, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x19, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12,
	0x28, 0x0a, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x6c, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0a, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x79, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x09, 0x66, 0x6f, 0x6e, 0x74,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0xaf, 0x01,
	0x0a, 0x09, 0x46, 0x6f, 0x6e, 0x74, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x6f, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x72, 0x6f, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6c, 0x69, 0x6e, 0x65, 0x53,
	0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x73, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x53, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x22,
	0x84, 0x03, 0x0a, 0x16, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x32, 0x0a, 0x15, 0x77, 0x65, 0x62, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69,
	0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x13, 0x77, 0x65, 0x62, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x70, 0x70,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x22, 0x51, 0x0a, 0x13,
	0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x22,
	0x57, 0x0a, 0x0f, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x48, 0x6f, 0x73,
	0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0x40, 0x0a, 0x1b, 0x57, 0x65, 0x62, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x50,
	0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x1c, 0x57,
	0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x49, 0x50, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x05, 0x70,
	0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x70, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x55, 0x0a, 0x0f, 0x67,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75,
	0x61, 0x6c, 0x49, 0x50, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x0e, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x17, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50,
	0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4d, 0x61, 0x78,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x91, 0x04, 0x0a, 0x13, 0x56,
	0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x70, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x70, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x69, 0x70, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x69, 0x70, 0x45, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x52, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x49, 0x0a, 0x0e, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x51, 0x0a, 0x11, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x53, 0x0a, 0x11, 0x64, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x44, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x49, 0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x64, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x22, 0x57,
	0x0a, 0x10, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x27,
	0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x70, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x49, 0x70, 0x73,
	0x50, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x22, 0x9e, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x6f, 0x6e, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4f, 0x6e, 0x4c,
	0x6f, 0x67, 0x6f, 0x75, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x64, 0x6c, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x69, 0x64, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x48, 0x6f,
	0x75, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x13, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x4f, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x94, 0x01, 0x0a, 0x0f, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x86, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x49, 0x50, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76,
	0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x70, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0xe8, 0x02, 0x0a, 0x1d, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x41, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x49, 0x50, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x60, 0x0a, 0x0a, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x41, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x70, 0x6f, 0x6f, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x5c, 0x0a, 0x0e, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x50,
	0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xc9, 0x01, 0x0a, 0x1a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x69, 0x0a, 0x0e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x40, 0x0a,
	0x12, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x92, 0x01, 0x0a, 0x1e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x49, 0x50, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f,
	0x69, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x49, 0x70, 0x73, 0x22, 0x70, 0x0a, 0x1b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xa1, 0x04, 0x0a, 0x0c, 0x49, 0x50, 0x41, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x70, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f,
	0x75, 0x70, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x55, 0x70, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x44, 0x6f, 0x77, 0x6e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x75, 0x70, 0x73, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x55, 0x70, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x77,
	0x6e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x6f, 0x77, 0x6e, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x54,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x09, 0x50,
	0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x49, 0x70, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x6c,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x49, 0x70, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x70, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x22, 0xf6, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0f, 0x74, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x42, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x22, 0x92, 0x02, 0x0a, 0x0d,
	0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x70, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x75, 0x70, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x29, 0x0a, 0x10, 0x64, 0x6f, 0x77, 0x6e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x64, 0x6f, 0x77, 0x6e,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x46, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x36, 0x0a, 0x11, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x22, 0x55, 0x0a, 0x12, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x08, 0x63,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x73, 0x22, 0xb4, 0x03, 0x0a, 0x0e, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x53, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x1a,
	0x3d, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x81,
	0x01, 0x0a, 0x17, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x22, 0x4e, 0x0a, 0x18, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0xa3, 0x02, 0x0a, 0x14, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x5a, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3d, 0x0a, 0x0f, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x44, 0x0a, 0x15, 0x57, 0x65, 0x62, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x53, 0x53, 0x4f, 0x49, 0x44, 0x50, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x4d,
	0x0a, 0x16, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x53, 0x53,
	0x4f, 0x49, 0x44, 0x50, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x04, 0x69, 0x64, 0x70, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x53, 0x4f,
	0x49, 0x44, 0x50, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x64, 0x70, 0x73, 0x22, 0xa5, 0x02,
	0x0a, 0x0a, 0x53, 0x53, 0x4f, 0x49, 0x44, 0x50, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x69, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x40, 0x0a, 0x1b, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x7b, 0x0a, 0x1c, 0x57, 0x65, 0x62, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x3e, 0x0a, 0x19, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x22, 0x80, 0x01, 0x0a, 0x1a, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0xd8, 0x01, 0x0a, 0x10, 0x57, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x32, 0xe2, 0x0f, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x4d, 0x0a, 0x08, 0x53, 0x65,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x12, 0x20, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x65, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x07, 0x53, 0x45, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x45, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x45, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x0d, 0x53, 0x65, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x20, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x65, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x65,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x55, 0x0a, 0x0a, 0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x2e,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x23, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5e, 0x0a, 0x0d, 0x57, 0x65, 0x62, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65,
	0x62, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x26,
	0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x0b, 0x55, 0x63, 0x69, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x55, 0x63, 0x69, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x55, 0x63, 0x69, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x64, 0x0a, 0x0f, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73,
	0x41, 0x70, 0x70, 0x12, 0x27, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x61,
	0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x41,
	0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x64, 0x0a, 0x0f, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x43, 0x72, 0x74, 0x12, 0x27, 0x2e, 0x61, 0x73, 0x64, 0x73,
	0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e,
	0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x43, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x28, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x52, 0x73, 0x43, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x70, 0x0a, 0x13,
	0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x57, 0x61, 0x74, 0x65, 0x72, 0x6d,
	0x61, 0x72, 0x6b, 0x12, 0x2b, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x57, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x2c, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x57, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x64,
	0x0a, 0x0f, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x48, 0x6f, 0x73, 0x74,
	0x73, 0x12, 0x27, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x48, 0x6f, 0x73, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x7f, 0x0a, 0x18, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x50, 0x6f, 0x6f, 0x6c, 0x73,
	0x12, 0x30, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x31, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x50, 0x6f, 0x6f, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x85, 0x01, 0x0a, 0x1a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x49, 0x50, 0x41, 0x6c,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x61, 0x0a,
	0x12, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x12, 0x24, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69,
	0x63, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x73, 0x64, 0x73,
	0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e,
	0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x61, 0x0a, 0x0e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x12, 0x26, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x61, 0x73, 0x64,
	0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x79, 0x0a, 0x1a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x2c, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x2d, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x6d,
	0x0a, 0x12, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x53, 0x53,
	0x4f, 0x49, 0x44, 0x50, 0x12, 0x2a, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x73, 0x53, 0x53, 0x4f, 0x49, 0x44, 0x50, 0x52, 0x65, 0x71,
	0x1a, 0x2b, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x52, 0x73, 0x53, 0x53, 0x4f, 0x49, 0x44, 0x50, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7f, 0x0a,
	0x18, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x30, 0x2e, 0x61, 0x73, 0x64, 0x73,
	0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e,
	0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x61, 0x73,
	0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x79,
	0x0a, 0x16, 0x57, 0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x57, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x2e, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57,
	0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x57,
	0x65, 0x62, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7c, 0x0a, 0x17, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x2f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x42, 0x28, 0x5a, 0x26, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_application_v1_app_proto_rawDescOnce sync.Once
	file_application_v1_app_proto_rawDescData = file_application_v1_app_proto_rawDesc
)

func file_application_v1_app_proto_rawDescGZIP() []byte {
	file_application_v1_app_proto_rawDescOnce.Do(func() {
		file_application_v1_app_proto_rawDescData = protoimpl.X.CompressGZIP(file_application_v1_app_proto_rawDescData)
	})
	return file_application_v1_app_proto_rawDescData
}

var file_application_v1_app_proto_msgTypes = make([]protoimpl.MessageInfo, 66)
var file_application_v1_app_proto_goTypes = []interface{}{
	(*SeGetAppReq)(nil),                    // 0: asdsec.core.api.app.SeGetAppReq
	(*GetAppResp)(nil),                     // 1: asdsec.core.api.app.GetAppResp
	(*AppInfo)(nil),                        // 2: asdsec.core.api.app.AppInfo
	(*SERouteReq)(nil),                     // 3: asdsec.core.api.app.SERouteReq
	(*SERoute)(nil),                        // 4: asdsec.core.api.app.SERoute
	(*SERoutes)(nil),                       // 5: asdsec.core.api.app.SERoutes
	(*SeGetStrategyResp)(nil),              // 6: asdsec.core.api.app.SeGetStrategyResp
	(*SeStrategy)(nil),                     // 7: asdsec.core.api.app.SeStrategy
	(*WebAccessInfoReq)(nil),               // 8: asdsec.core.api.app.WebAccessInfoReq
	(*WebAccessInfoResp)(nil),              // 9: asdsec.core.api.app.WebAccessInfoResp
	(*WebAppInfo)(nil),                     // 10: asdsec.core.api.app.WebAppInfo
	(*StrategyInfo)(nil),                   // 11: asdsec.core.api.app.StrategyInfo
	(*TimeGap)(nil),                        // 12: asdsec.core.api.app.TimeGap
	(*WebAppInfoReq)(nil),                  // 13: asdsec.core.api.app.WebAppInfoReq
	(*WebAppInfoResp)(nil),                 // 14: asdsec.core.api.app.WebAppInfoResp
	(*UciUserInfoReq)(nil),                 // 15: asdsec.core.api.app.UciUserInfoReq
	(*UciUserInfoResp)(nil),                // 16: asdsec.core.api.app.UciUserInfoResp
	(*UciUserInfo)(nil),                    // 17: asdsec.core.api.app.UciUserInfo
	(*WebGatewayRsAppReq)(nil),             // 18: asdsec.core.api.app.WebGatewayRsAppReq
	(*WebGatewayRsAppResp)(nil),            // 19: asdsec.core.api.app.WebGatewayRsAppResp
	(*WebGatewayRsCrtReq)(nil),             // 20: asdsec.core.api.app.WebGatewayRsCrtReq
	(*WebGatewayRsCrtResp)(nil),            // 21: asdsec.core.api.app.WebGatewayRsCrtResp
	(*Crt)(nil),                            // 22: asdsec.core.api.app.Crt
	(*WebGatewayWatermarkReq)(nil),         // 23: asdsec.core.api.app.WebGatewayWatermarkReq
	(*WebGatewayWatermarkResp)(nil),        // 24: asdsec.core.api.app.WebGatewayWatermarkResp
	(*Watermark)(nil),                      // 25: asdsec.core.api.app.Watermark
	(*FontStyle)(nil),                      // 26: asdsec.core.api.app.FontStyle
	(*ApplicationGatewayInfo)(nil),         // 27: asdsec.core.api.app.ApplicationGatewayInfo
	(*WebGatewayHostsReq)(nil),             // 28: asdsec.core.api.app.WebGatewayHostsReq
	(*WebGatewayHostsResp)(nil),            // 29: asdsec.core.api.app.WebGatewayHostsResp
	(*WebGatewayHosts)(nil),                // 30: asdsec.core.api.app.WebGatewayHosts
	(*WebGatewayVirtualIPPoolsReq)(nil),    // 31: asdsec.core.api.app.WebGatewayVirtualIPPoolsReq
	(*WebGatewayVirtualIPPoolsResp)(nil),   // 32: asdsec.core.api.app.WebGatewayVirtualIPPoolsResp
	(*VirtualIPGlobalSettings)(nil),        // 33: asdsec.core.api.app.VirtualIPGlobalSettings
	(*VirtualIPPoolConfig)(nil),            // 34: asdsec.core.api.app.VirtualIPPoolConfig
	(*AllocationPolicy)(nil),               // 35: asdsec.core.api.app.AllocationPolicy
	(*ReleasePolicy)(nil),                  // 36: asdsec.core.api.app.ReleasePolicy
	(*DirectoryConfig)(nil),                // 37: asdsec.core.api.app.DirectoryConfig
	(*DedicatedIPConfig)(nil),              // 38: asdsec.core.api.app.DedicatedIPConfig
	(*ReportVirtualIPAllocationsReq)(nil),  // 39: asdsec.core.api.app.ReportVirtualIPAllocationsReq
	(*ReportHealthCheckResultReq)(nil),     // 40: asdsec.core.api.app.ReportHealthCheckResultReq
	(*ReportVirtualIPAllocationsResp)(nil), // 41: asdsec.core.api.app.ReportVirtualIPAllocationsResp
	(*ReportHealthCheckResultResp)(nil),    // 42: asdsec.core.api.app.ReportHealthCheckResultResp
	(*IPAllocation)(nil),                   // 43: asdsec.core.api.app.IPAllocation
	(*PoolStats)(nil),                      // 44: asdsec.core.api.app.PoolStats
	(*TrafficStatsReq)(nil),                // 45: asdsec.core.api.app.TrafficStatsReq
	(*TrafficBucket)(nil),                  // 46: asdsec.core.api.app.TrafficBucket
	(*TrafficStatsResp)(nil),               // 47: asdsec.core.api.app.TrafficStatsResp
	(*GatewayCommandReq)(nil),              // 48: asdsec.core.api.app.GatewayCommandReq
	(*GatewayCommandResp)(nil),             // 49: asdsec.core.api.app.GatewayCommandResp
	(*GatewayCommand)(nil),                 // 50: asdsec.core.api.app.GatewayCommand
	(*GatewayCommandResultReq)(nil),        // 51: asdsec.core.api.app.GatewayCommandResultReq
	(*GatewayCommandResultResp)(nil),       // 52: asdsec.core.api.app.GatewayCommandResultResp
	(*GatewayCommandResult)(nil),           // 53: asdsec.core.api.app.GatewayCommandResult
	(*WebGatewayRsSSOIDPReq)(nil),          // 54: asdsec.core.api.app.WebGatewayRsSSOIDPReq
	(*WebGatewayRsSSOIDPResp)(nil),         // 55: asdsec.core.api.app.WebGatewayRsSSOIDPResp
	(*SSOIDPInfo)(nil),                     // 56: asdsec.core.api.app.SSOIDPInfo
	(*WebGatewayPlatformDomainReq)(nil),    // 57: asdsec.core.api.app.WebGatewayPlatformDomainReq
	(*WebGatewayPlatformDomainResp)(nil),   // 58: asdsec.core.api.app.WebGatewayPlatformDomainResp
	(*WebGatewayWechatVerifyReq)(nil),      // 59: asdsec.core.api.app.WebGatewayWechatVerifyReq
	(*WebGatewayWechatVerifyResp)(nil),     // 60: asdsec.core.api.app.WebGatewayWechatVerifyResp
	(*WechatVerifyFile)(nil),               // 61: asdsec.core.api.app.WechatVerifyFile
	nil,                                    // 62: asdsec.core.api.app.ReportVirtualIPAllocationsReq.PoolStatsEntry
	nil,                                    // 63: asdsec.core.api.app.ReportHealthCheckResultReq.HealthResultsEntry
	nil,                                    // 64: asdsec.core.api.app.GatewayCommand.ParametersEntry
	nil,                                    // 65: asdsec.core.api.app.GatewayCommandResult.ResultDataEntry
}
var file_application_v1_app_proto_depIdxs = []int32{
	2,  // 0: asdsec.core.api.app.GetAppResp.apps:type_name -> asdsec.core.api.app.AppInfo
	4,  // 1: asdsec.core.api.app.SERoutes.se_route:type_name -> asdsec.core.api.app.SERoute
	7,  // 2: asdsec.core.api.app.SeGetStrategyResp.se_strategy:type_name -> asdsec.core.api.app.SeStrategy
	11, // 3: asdsec.core.api.app.WebAccessInfoResp.StrategyInfo:type_name -> asdsec.core.api.app.StrategyInfo
	12, // 4: asdsec.core.api.app.StrategyInfo.time_gap:type_name -> asdsec.core.api.app.TimeGap
	10, // 5: asdsec.core.api.app.WebAppInfoResp.web_app_info:type_name -> asdsec.core.api.app.WebAppInfo
	17, // 6: asdsec.core.api.app.UciUserInfoResp.uci_user_info:type_name -> asdsec.core.api.app.UciUserInfo
	27, // 7: asdsec.core.api.app.WebGatewayRsAppResp.apps:type_name -> asdsec.core.api.app.ApplicationGatewayInfo
	22, // 8: asdsec.core.api.app.WebGatewayRsCrtResp.crts:type_name -> asdsec.core.api.app.Crt
	25, // 9: asdsec.core.api.app.WebGatewayWatermarkResp.WatermarkConf:type_name -> asdsec.core.api.app.Watermark
	26, // 10: asdsec.core.api.app.Watermark.font_style:type_name -> asdsec.core.api.app.FontStyle
	30, // 11: asdsec.core.api.app.WebGatewayHostsResp.hosts:type_name -> asdsec.core.api.app.WebGatewayHosts
	34, // 12: asdsec.core.api.app.WebGatewayVirtualIPPoolsResp.pools:type_name -> asdsec.core.api.app.VirtualIPPoolConfig
	33, // 13: asdsec.core.api.app.WebGatewayVirtualIPPoolsResp.global_settings:type_name -> asdsec.core.api.app.VirtualIPGlobalSettings
	35, // 14: asdsec.core.api.app.VirtualIPPoolConfig.allocation_policy:type_name -> asdsec.core.api.app.AllocationPolicy
	36, // 15: asdsec.core.api.app.VirtualIPPoolConfig.release_policy:type_name -> asdsec.core.api.app.ReleasePolicy
	37, // 16: asdsec.core.api.app.VirtualIPPoolConfig.directory_configs:type_name -> asdsec.core.api.app.DirectoryConfig
	38, // 17: asdsec.core.api.app.VirtualIPPoolConfig.dedicated_configs:type_name -> asdsec.core.api.app.DedicatedIPConfig
	43, // 18: asdsec.core.api.app.ReportVirtualIPAllocationsReq.allocations:type_name -> asdsec.core.api.app.IPAllocation
	62, // 19: asdsec.core.api.app.ReportVirtualIPAllocationsReq.pool_stats:type_name -> asdsec.core.api.app.ReportVirtualIPAllocationsReq.PoolStatsEntry
	63, // 20: asdsec.core.api.app.ReportHealthCheckResultReq.health_results:type_name -> asdsec.core.api.app.ReportHealthCheckResultReq.HealthResultsEntry
	46, // 21: asdsec.core.api.app.TrafficStatsReq.traffic_buckets:type_name -> asdsec.core.api.app.TrafficBucket
	50, // 22: asdsec.core.api.app.GatewayCommandResp.commands:type_name -> asdsec.core.api.app.GatewayCommand
	64, // 23: asdsec.core.api.app.GatewayCommand.parameters:type_name -> asdsec.core.api.app.GatewayCommand.ParametersEntry
	53, // 24: asdsec.core.api.app.GatewayCommandResultReq.results:type_name -> asdsec.core.api.app.GatewayCommandResult
	65, // 25: asdsec.core.api.app.GatewayCommandResult.result_data:type_name -> asdsec.core.api.app.GatewayCommandResult.ResultDataEntry
	56, // 26: asdsec.core.api.app.WebGatewayRsSSOIDPResp.idps:type_name -> asdsec.core.api.app.SSOIDPInfo
	61, // 27: asdsec.core.api.app.WebGatewayWechatVerifyResp.files:type_name -> asdsec.core.api.app.WechatVerifyFile
	44, // 28: asdsec.core.api.app.ReportVirtualIPAllocationsReq.PoolStatsEntry.value:type_name -> asdsec.core.api.app.PoolStats
	0,  // 29: asdsec.core.api.app.App.SeGetApp:input_type -> asdsec.core.api.app.SeGetAppReq
	3,  // 30: asdsec.core.api.app.App.SERoute:input_type -> asdsec.core.api.app.SERouteReq
	0,  // 31: asdsec.core.api.app.App.SeGetStrategy:input_type -> asdsec.core.api.app.SeGetAppReq
	13, // 32: asdsec.core.api.app.App.WebAppInfo:input_type -> asdsec.core.api.app.WebAppInfoReq
	8,  // 33: asdsec.core.api.app.App.WebAccessInfo:input_type -> asdsec.core.api.app.WebAccessInfoReq
	15, // 34: asdsec.core.api.app.App.UciUserInfo:input_type -> asdsec.core.api.app.UciUserInfoReq
	18, // 35: asdsec.core.api.app.App.WebGatewayRsApp:input_type -> asdsec.core.api.app.WebGatewayRsAppReq
	20, // 36: asdsec.core.api.app.App.WebGatewayRsCrt:input_type -> asdsec.core.api.app.WebGatewayRsCrtReq
	23, // 37: asdsec.core.api.app.App.WebGatewayWatermark:input_type -> asdsec.core.api.app.WebGatewayWatermarkReq
	28, // 38: asdsec.core.api.app.App.WebGatewayHosts:input_type -> asdsec.core.api.app.WebGatewayHostsReq
	31, // 39: asdsec.core.api.app.App.WebGatewayVirtualIPPools:input_type -> asdsec.core.api.app.WebGatewayVirtualIPPoolsReq
	39, // 40: asdsec.core.api.app.App.ReportVirtualIPAllocations:input_type -> asdsec.core.api.app.ReportVirtualIPAllocationsReq
	45, // 41: asdsec.core.api.app.App.ReportTrafficStats:input_type -> asdsec.core.api.app.TrafficStatsReq
	48, // 42: asdsec.core.api.app.App.GatewayCommand:input_type -> asdsec.core.api.app.GatewayCommandReq
	51, // 43: asdsec.core.api.app.App.ReportGatewayCommandResult:input_type -> asdsec.core.api.app.GatewayCommandResultReq
	54, // 44: asdsec.core.api.app.App.WebGatewayRsSSOIDP:input_type -> asdsec.core.api.app.WebGatewayRsSSOIDPReq
	57, // 45: asdsec.core.api.app.App.WebGatewayPlatformDomain:input_type -> asdsec.core.api.app.WebGatewayPlatformDomainReq
	59, // 46: asdsec.core.api.app.App.WebGatewayWechatVerify:input_type -> asdsec.core.api.app.WebGatewayWechatVerifyReq
	40, // 47: asdsec.core.api.app.App.ReportHealthCheckResult:input_type -> asdsec.core.api.app.ReportHealthCheckResultReq
	1,  // 48: asdsec.core.api.app.App.SeGetApp:output_type -> asdsec.core.api.app.GetAppResp
	5,  // 49: asdsec.core.api.app.App.SERoute:output_type -> asdsec.core.api.app.SERoutes
	6,  // 50: asdsec.core.api.app.App.SeGetStrategy:output_type -> asdsec.core.api.app.SeGetStrategyResp
	14, // 51: asdsec.core.api.app.App.WebAppInfo:output_type -> asdsec.core.api.app.WebAppInfoResp
	9,  // 52: asdsec.core.api.app.App.WebAccessInfo:output_type -> asdsec.core.api.app.WebAccessInfoResp
	16, // 53: asdsec.core.api.app.App.UciUserInfo:output_type -> asdsec.core.api.app.UciUserInfoResp
	19, // 54: asdsec.core.api.app.App.WebGatewayRsApp:output_type -> asdsec.core.api.app.WebGatewayRsAppResp
	21, // 55: asdsec.core.api.app.App.WebGatewayRsCrt:output_type -> asdsec.core.api.app.WebGatewayRsCrtResp
	24, // 56: asdsec.core.api.app.App.WebGatewayWatermark:output_type -> asdsec.core.api.app.WebGatewayWatermarkResp
	29, // 57: asdsec.core.api.app.App.WebGatewayHosts:output_type -> asdsec.core.api.app.WebGatewayHostsResp
	32, // 58: asdsec.core.api.app.App.WebGatewayVirtualIPPools:output_type -> asdsec.core.api.app.WebGatewayVirtualIPPoolsResp
	41, // 59: asdsec.core.api.app.App.ReportVirtualIPAllocations:output_type -> asdsec.core.api.app.ReportVirtualIPAllocationsResp
	47, // 60: asdsec.core.api.app.App.ReportTrafficStats:output_type -> asdsec.core.api.app.TrafficStatsResp
	49, // 61: asdsec.core.api.app.App.GatewayCommand:output_type -> asdsec.core.api.app.GatewayCommandResp
	52, // 62: asdsec.core.api.app.App.ReportGatewayCommandResult:output_type -> asdsec.core.api.app.GatewayCommandResultResp
	55, // 63: asdsec.core.api.app.App.WebGatewayRsSSOIDP:output_type -> asdsec.core.api.app.WebGatewayRsSSOIDPResp
	58, // 64: asdsec.core.api.app.App.WebGatewayPlatformDomain:output_type -> asdsec.core.api.app.WebGatewayPlatformDomainResp
	60, // 65: asdsec.core.api.app.App.WebGatewayWechatVerify:output_type -> asdsec.core.api.app.WebGatewayWechatVerifyResp
	42, // 66: asdsec.core.api.app.App.ReportHealthCheckResult:output_type -> asdsec.core.api.app.ReportHealthCheckResultResp
	48, // [48:67] is the sub-list for method output_type
	29, // [29:48] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_application_v1_app_proto_init() }
func file_application_v1_app_proto_init() {
	if File_application_v1_app_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_application_v1_app_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeGetAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SERouteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SERoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SERoutes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeGetStrategyResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebAccessInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebAccessInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebAppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StrategyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeGap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebAppInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebAppInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UciUserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UciUserInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UciUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayRsAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayRsAppResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayRsCrtReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayRsCrtResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Crt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayWatermarkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayWatermarkResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Watermark); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FontStyle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationGatewayInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayHostsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayHostsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayHosts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayVirtualIPPoolsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayVirtualIPPoolsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualIPGlobalSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualIPPoolConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllocationPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleasePolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectoryConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedicatedIPConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportVirtualIPAllocationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportHealthCheckResultReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportVirtualIPAllocationsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportHealthCheckResultResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPAllocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PoolStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficStatsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficBucket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficStatsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayCommandReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayCommandResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayCommand); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayCommandResultReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayCommandResultResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayCommandResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayRsSSOIDPReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayRsSSOIDPResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SSOIDPInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayPlatformDomainReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayPlatformDomainResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayWechatVerifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebGatewayWechatVerifyResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatVerifyFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_application_v1_app_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   66,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_application_v1_app_proto_goTypes,
		DependencyIndexes: file_application_v1_app_proto_depIdxs,
		MessageInfos:      file_application_v1_app_proto_msgTypes,
	}.Build()
	File_application_v1_app_proto = out.File
	file_application_v1_app_proto_rawDesc = nil
	file_application_v1_app_proto_goTypes = nil
	file_application_v1_app_proto_depIdxs = nil
}
