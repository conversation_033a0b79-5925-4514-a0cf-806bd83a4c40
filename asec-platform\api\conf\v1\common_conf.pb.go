// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/common_conf.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfType string `protobuf:"bytes,1,opt,name=conf_type,json=confType,proto3" json:"conf_type,omitempty"`
	ConfData string `protobuf:"bytes,2,opt,name=conf_data,json=confData,proto3" json:"conf_data,omitempty"`
}

func (x *CommonConf) Reset() {
	*x = CommonConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_common_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonConf) ProtoMessage() {}

func (x *CommonConf) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_common_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonConf.ProtoReflect.Descriptor instead.
func (*CommonConf) Descriptor() ([]byte, []int) {
	return file_conf_v1_common_conf_proto_rawDescGZIP(), []int{0}
}

func (x *CommonConf) GetConfType() string {
	if x != nil {
		return x.ConfType
	}
	return ""
}

func (x *CommonConf) GetConfData() string {
	if x != nil {
		return x.ConfData
	}
	return ""
}

var File_conf_v1_common_conf_proto protoreflect.FileDescriptor

var file_conf_v1_common_conf_proto_rawDesc = []byte{
	0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0x46, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x44, 0x61, 0x74, 0x61, 0x42, 0x29, 0x5a,
	0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63,
	0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_common_conf_proto_rawDescOnce sync.Once
	file_conf_v1_common_conf_proto_rawDescData = file_conf_v1_common_conf_proto_rawDesc
)

func file_conf_v1_common_conf_proto_rawDescGZIP() []byte {
	file_conf_v1_common_conf_proto_rawDescOnce.Do(func() {
		file_conf_v1_common_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_common_conf_proto_rawDescData)
	})
	return file_conf_v1_common_conf_proto_rawDescData
}

var file_conf_v1_common_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_conf_v1_common_conf_proto_goTypes = []interface{}{
	(*CommonConf)(nil), // 0: api.conf.CommonConf
}
var file_conf_v1_common_conf_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_conf_v1_common_conf_proto_init() }
func file_conf_v1_common_conf_proto_init() {
	if File_conf_v1_common_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_common_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_common_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_common_conf_proto_goTypes,
		DependencyIndexes: file_conf_v1_common_conf_proto_depIdxs,
		MessageInfos:      file_conf_v1_common_conf_proto_msgTypes,
	}.Build()
	File_conf_v1_common_conf_proto = out.File
	file_conf_v1_common_conf_proto_rawDesc = nil
	file_conf_v1_common_conf_proto_goTypes = nil
	file_conf_v1_common_conf_proto_depIdxs = nil
}
