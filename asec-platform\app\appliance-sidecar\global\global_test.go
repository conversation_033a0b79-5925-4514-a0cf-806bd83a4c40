package global

import (
	"fmt"
	"go.uber.org/zap"
	"path/filepath"
	"testing"
)

func TestInitConfTable(t *testing.T) {
	tests := []struct {
		name string
	}{
		{name: "test"},
	}
	logger := zap.NewExample()
	defer logger.Sync()
	zap.ReplaceGlobals(logger)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			InitConfTable()
		})
	}
}

func TestFromIDFile(t *testing.T) {
	IDFilePath = filepath.Join("D:\\Downloads\\", "appliance_id")
	PrivateIp = "cs.asdsec.com"
	gotId, err := FromIDFile(IDFilePath, PrivateIp)
	//print
	fmt.Printf("got %d,err %v", gotId, err)
}
