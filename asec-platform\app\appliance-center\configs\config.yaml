server:
  http:
    addr: 0.0.0.0:9011
    timeout: 300s
  grpc:
    addr: 0.0.0.0:9002
    timeout: 15s
data:
  database:
    host: **************
    port: 5432
    user: asec
    password: pg@asd@1234!
    dbname: asec_platform
  redis:
    addr: **************:6379
    password: redis@asd@1234!
    db: 0
    dial_timeout: 1s
    read_timeout: 0.4s
    write_timeout: 0.6s
  kafka:
    brokers:
      - ***********:19092
    sasl_enable: false
    producer:
      max_msg_bytes: 4194304
      flush_bytes: 10
      flush_max_messages: 4194304
      flush_frequency: 1s
      timeout: 10s
      return_success: false

