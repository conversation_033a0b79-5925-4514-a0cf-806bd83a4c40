/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
import e from"./menuItem.8ec33eca.js";import t from"./asyncSubmenu.e5dea301.js";import{c as o,h as n,a as r,k as s,w as a,b as l,F as u,A as i,l as f,z as c}from"./index.a794166c.js";const m=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(m){const d=m,h=o((()=>d.routerInfo.children&&d.routerInfo.children.filter((e=>!e.hidden)).length?t:e));return(e,t)=>{const o=n("AsideComponent");return m.routerInfo.hidden?f("",!0):(r(),s(c(h.value),{key:0,"is-collapse":m.isCollapse,theme:m.theme,"router-info":m.routerInfo},{default:a((()=>[m.routerInfo.children&&m.routerInfo.children.length?(r(!0),l(u,{key:0},i(m.routerInfo.children,(e=>(r(),s(o,{key:e.name,"is-collapse":!1,"router-info":e,theme:m.theme},null,8,["router-info","theme"])))),128)):f("",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}});export{m as default};
