// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/scan_task.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ScanTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务id
	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// 任务优先级
	TaskPriority int32 `protobuf:"varint,2,opt,name=task_priority,json=taskPriority,proto3" json:"task_priority,omitempty"`
	// 任务启用状态 1启用 2禁用
	TaskEnable int32 `protobuf:"varint,3,opt,name=task_enable,json=taskEnable,proto3" json:"task_enable,omitempty"`
	// 扫描指定的文件code
	FileTypeCode []int32 `protobuf:"varint,4,rep,packed,name=file_type_code,json=fileTypeCode,proto3" json:"file_type_code,omitempty"`
	// 扫描位置类型 1全盘 ; 2指定
	ScanPositionType int32 `protobuf:"varint,5,opt,name=scan_position_type,json=scanPositionType,proto3" json:"scan_position_type,omitempty"`
	// 自定义目录
	ScanDirectory []string `protobuf:"bytes,6,rep,name=scan_directory,json=scanDirectory,proto3" json:"scan_directory,omitempty"`
	// 自定义排除目录
	ScanExcludeDirectory []string `protobuf:"bytes,7,rep,name=scan_exclude_directory,json=scanExcludeDirectory,proto3" json:"scan_exclude_directory,omitempty"`
	// 扫描时间
	ScanTime *TimePolicy `protobuf:"bytes,8,opt,name=scan_time,json=scanTime,proto3" json:"scan_time,omitempty"`
	// 扫描文件最小大小 kb
	ScanMinFileSize int32 `protobuf:"varint,9,opt,name=scan_min_file_size,json=scanMinFileSize,proto3" json:"scan_min_file_size,omitempty"`
	// 扫描文件最大大小 kb
	ScanMaxFileSize int32 `protobuf:"varint,10,opt,name=scan_max_file_size,json=scanMaxFileSize,proto3" json:"scan_max_file_size,omitempty"`
	// 仅空闲状态扫描
	IdleScanSwitch bool `protobuf:"varint,11,opt,name=idle_scan_switch,json=idleScanSwitch,proto3" json:"idle_scan_switch,omitempty"`
	// 仅充电状态扫描
	ChargingScanSwitch bool `protobuf:"varint,12,opt,name=charging_scan_switch,json=chargingScanSwitch,proto3" json:"charging_scan_switch,omitempty"`
	// 每分钟扫描阈值
	MinuteCountLimit int32 `protobuf:"varint,13,opt,name=minute_count_limit,json=minuteCountLimit,proto3" json:"minute_count_limit,omitempty"`
}

func (x *ScanTask) Reset() {
	*x = ScanTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_scan_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanTask) ProtoMessage() {}

func (x *ScanTask) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_scan_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanTask.ProtoReflect.Descriptor instead.
func (*ScanTask) Descriptor() ([]byte, []int) {
	return file_conf_v1_scan_task_proto_rawDescGZIP(), []int{0}
}

func (x *ScanTask) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ScanTask) GetTaskPriority() int32 {
	if x != nil {
		return x.TaskPriority
	}
	return 0
}

func (x *ScanTask) GetTaskEnable() int32 {
	if x != nil {
		return x.TaskEnable
	}
	return 0
}

func (x *ScanTask) GetFileTypeCode() []int32 {
	if x != nil {
		return x.FileTypeCode
	}
	return nil
}

func (x *ScanTask) GetScanPositionType() int32 {
	if x != nil {
		return x.ScanPositionType
	}
	return 0
}

func (x *ScanTask) GetScanDirectory() []string {
	if x != nil {
		return x.ScanDirectory
	}
	return nil
}

func (x *ScanTask) GetScanExcludeDirectory() []string {
	if x != nil {
		return x.ScanExcludeDirectory
	}
	return nil
}

func (x *ScanTask) GetScanTime() *TimePolicy {
	if x != nil {
		return x.ScanTime
	}
	return nil
}

func (x *ScanTask) GetScanMinFileSize() int32 {
	if x != nil {
		return x.ScanMinFileSize
	}
	return 0
}

func (x *ScanTask) GetScanMaxFileSize() int32 {
	if x != nil {
		return x.ScanMaxFileSize
	}
	return 0
}

func (x *ScanTask) GetIdleScanSwitch() bool {
	if x != nil {
		return x.IdleScanSwitch
	}
	return false
}

func (x *ScanTask) GetChargingScanSwitch() bool {
	if x != nil {
		return x.ChargingScanSwitch
	}
	return false
}

func (x *ScanTask) GetMinuteCountLimit() int32 {
	if x != nil {
		return x.MinuteCountLimit
	}
	return 0
}

type TimePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 每日时间起
	DailyStartTime string `protobuf:"bytes,1,opt,name=dailyStartTime,proto3" json:"dailyStartTime,omitempty"`
	// 每日事件止
	DailyEndTime string `protobuf:"bytes,2,opt,name=dailyEndTime,proto3" json:"dailyEndTime,omitempty"`
	// 是否全天生效
	AllDayEnable bool `protobuf:"varint,3,opt,name=allDayEnable,proto3" json:"allDayEnable,omitempty"`
}

func (x *TimePolicy) Reset() {
	*x = TimePolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_scan_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimePolicy) ProtoMessage() {}

func (x *TimePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_scan_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimePolicy.ProtoReflect.Descriptor instead.
func (*TimePolicy) Descriptor() ([]byte, []int) {
	return file_conf_v1_scan_task_proto_rawDescGZIP(), []int{1}
}

func (x *TimePolicy) GetDailyStartTime() string {
	if x != nil {
		return x.DailyStartTime
	}
	return ""
}

func (x *TimePolicy) GetDailyEndTime() string {
	if x != nil {
		return x.DailyEndTime
	}
	return ""
}

func (x *TimePolicy) GetAllDayEnable() bool {
	if x != nil {
		return x.AllDayEnable
	}
	return false
}

var File_conf_v1_scan_task_proto protoreflect.FileDescriptor

var file_conf_v1_scan_task_proto_rawDesc = []byte{
	0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x22, 0xb1, 0x04, 0x0a, 0x08, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x24, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x73, 0x63, 0x61, 0x6e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x63, 0x61,
	0x6e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x34, 0x0a, 0x16, 0x73, 0x63,
	0x61, 0x6e, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x73, 0x63, 0x61, 0x6e,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x12, 0x31, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x73, 0x63, 0x61, 0x6e, 0x4d, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x2b, 0x0a, 0x12, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x63,
	0x61, 0x6e, 0x4d, 0x61, 0x78, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x69, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x64, 0x6c, 0x65, 0x53, 0x63, 0x61,
	0x6e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x63, 0x68, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x53,
	0x63, 0x61, 0x6e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x69, 0x6e,
	0x75, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x7c, 0x0a, 0x0a, 0x54, 0x69, 0x6d, 0x65, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6c, 0x6c, 0x44, 0x61, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x44, 0x61, 0x79, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x29, 0x5a, 0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_scan_task_proto_rawDescOnce sync.Once
	file_conf_v1_scan_task_proto_rawDescData = file_conf_v1_scan_task_proto_rawDesc
)

func file_conf_v1_scan_task_proto_rawDescGZIP() []byte {
	file_conf_v1_scan_task_proto_rawDescOnce.Do(func() {
		file_conf_v1_scan_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_scan_task_proto_rawDescData)
	})
	return file_conf_v1_scan_task_proto_rawDescData
}

var file_conf_v1_scan_task_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_conf_v1_scan_task_proto_goTypes = []interface{}{
	(*ScanTask)(nil),   // 0: api.conf.ScanTask
	(*TimePolicy)(nil), // 1: api.conf.TimePolicy
}
var file_conf_v1_scan_task_proto_depIdxs = []int32{
	1, // 0: api.conf.ScanTask.scan_time:type_name -> api.conf.TimePolicy
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_conf_v1_scan_task_proto_init() }
func file_conf_v1_scan_task_proto_init() {
	if File_conf_v1_scan_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_scan_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_scan_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimePolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_scan_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_scan_task_proto_goTypes,
		DependencyIndexes: file_conf_v1_scan_task_proto_depIdxs,
		MessageInfos:      file_conf_v1_scan_task_proto_msgTypes,
	}.Build()
	File_conf_v1_scan_task_proto = out.File
	file_conf_v1_scan_task_proto_rawDesc = nil
	file_conf_v1_scan_task_proto_goTypes = nil
	file_conf_v1_scan_task_proto_depIdxs = nil
}
