package service

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"asdsec.com/asec/platform/app/sys-panel/dto"
	"asdsec.com/asec/platform/app/sys-panel/model"
	"gorm.io/gorm"
)

const (
	WECHAT_VERIFY_DIR       = "/opt/asdsec-compose/openresty/wechat-verify" // 企业微信验证文件存储目录（宿主机路径）
	WECHAT_VERIFY_NGINX_DIR = "/usr/local/openresty/nginx/wechat-verify"    // Openresty容器内路径
)

type WechatVerifyService struct {
	db *gorm.DB
}

func NewWechatVerifyService(db *gorm.DB) *WechatVerifyService {
	// 确保验证文件目录存在
	if err := os.MkdirAll(WECHAT_VERIFY_DIR, 0755); err != nil {
		panic(fmt.Sprintf("创建企业微信验证文件目录失败: %v", err))
	}
	return &WechatVerifyService{db: db}
}

// GetWechatVerifyList 获取企业微信验证文件列表
func (s *WechatVerifyService) GetWechatVerifyList() (*dto.WechatVerifyListResponse, error) {
	var verifies []model.WechatVerify
	var total int64

	// 获取总数
	if err := s.db.Model(&model.WechatVerify{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取验证文件总数失败: %v", err)
	}

	// 获取列表
	if err := s.db.Find(&verifies).Error; err != nil {
		return nil, fmt.Errorf("获取验证文件列表失败: %v", err)
	}

	// 转换为响应格式
	var list []dto.WechatVerifyResponse
	for _, verify := range verifies {
		list = append(list, dto.WechatVerifyResponse{
			ID:          verify.ID,
			FileName:    verify.FileName,
			Content:     verify.Content,
			IsEnabled:   verify.IsEnabled,
			Description: verify.Description,
			CreatedAt:   verify.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   verify.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return &dto.WechatVerifyListResponse{
		Total: total,
		List:  list,
	}, nil
}

// CreateWechatVerify 创建企业微信验证文件
func (s *WechatVerifyService) CreateWechatVerify(req *dto.CreateWechatVerifyRequest) error {
	// 验证文件名格式
	if err := s.validateFileName(req.FileName); err != nil {
		return err
	}

	// 检查文件名是否已存在（包括软删除的记录）
	var count int64
	if err := s.db.Unscoped().Model(&model.WechatVerify{}).Where("file_name = ?", req.FileName).Count(&count).Error; err != nil {
		return fmt.Errorf("检查文件名重复失败: %v", err)
	}
	if count > 0 {
		// 检查是否为软删除的记录，如果是则可以恢复
		var existingVerify model.WechatVerify
		if err := s.db.Unscoped().Where("file_name = ?", req.FileName).First(&existingVerify).Error; err != nil {
			return fmt.Errorf("查询已存在的验证文件失败: %v", err)
		}

		if existingVerify.DeletedAt.Valid {
			// 恢复软删除的记录
			existingVerify.Content = req.Content
			existingVerify.IsEnabled = true
			existingVerify.Description = req.Description
			existingVerify.DeletedAt = gorm.DeletedAt{}

			if err := s.db.Unscoped().Save(&existingVerify).Error; err != nil {
				return fmt.Errorf("恢复验证文件记录失败: %v", err)
			}

			// 写入物理文件
			if err := s.writeVerifyFile(existingVerify.FileName, existingVerify.Content); err != nil {
				return fmt.Errorf("写入验证文件失败: %v", err)
			}

			// 更新Nginx配置
			if err := s.updateNginxConfig(); err != nil {
				return fmt.Errorf("更新Nginx配置失败: %v", err)
			}

			return nil
		} else {
			return fmt.Errorf("验证文件 %s 已存在", req.FileName)
		}
	}

	// 创建数据库记录
	verify := &model.WechatVerify{
		FileName:    req.FileName,
		Content:     req.Content,
		IsEnabled:   true,
		Description: req.Description,
	}

	if err := s.db.Create(verify).Error; err != nil {
		return fmt.Errorf("创建验证文件记录失败: %v", err)
	}

	// 写入物理文件
	if err := s.writeVerifyFile(verify.FileName, verify.Content); err != nil {
		// 如果写入文件失败，删除数据库记录
		s.db.Delete(verify)
		return fmt.Errorf("写入验证文件失败: %v", err)
	}

	// 更新Nginx配置
	if err := s.updateNginxConfig(); err != nil {
		// 如果更新Nginx失败，清理文件和数据库记录
		s.removeVerifyFile(verify.FileName)
		s.db.Delete(verify)
		return fmt.Errorf("更新Nginx配置失败: %v", err)
	}

	return nil
}

// UpdateWechatVerify 更新企业微信验证文件
func (s *WechatVerifyService) UpdateWechatVerify(id uint, req *dto.UpdateWechatVerifyRequest) error {
	var verify model.WechatVerify
	if err := s.db.First(&verify, id).Error; err != nil {
		return fmt.Errorf("验证文件不存在: %v", err)
	}

	// 更新字段
	updates := map[string]interface{}{
		"content":     req.Content,
		"description": req.Description,
	}

	if req.IsEnabled != nil {
		updates["is_enabled"] = *req.IsEnabled
	}

	if err := s.db.Model(&verify).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新验证文件记录失败: %v", err)
	}

	// 重新加载记录以获取最新状态
	if err := s.db.First(&verify, id).Error; err != nil {
		return fmt.Errorf("重新加载验证文件失败: %v", err)
	}

	// 根据启用状态决定是否写入文件
	if verify.IsEnabled {
		if err := s.writeVerifyFile(verify.FileName, verify.Content); err != nil {
			return fmt.Errorf("写入验证文件失败: %v", err)
		}
	} else {
		s.removeVerifyFile(verify.FileName)
	}

	// 更新Nginx配置
	if err := s.updateNginxConfig(); err != nil {
		return fmt.Errorf("更新Nginx配置失败: %v", err)
	}

	return nil
}

// DeleteWechatVerify 删除企业微信验证文件
func (s *WechatVerifyService) DeleteWechatVerify(id uint) error {
	var verify model.WechatVerify
	if err := s.db.First(&verify, id).Error; err != nil {
		return fmt.Errorf("验证文件不存在: %v", err)
	}

	// 删除物理文件
	s.removeVerifyFile(verify.FileName)

	// 删除数据库记录
	if err := s.db.Delete(&verify).Error; err != nil {
		return fmt.Errorf("删除验证文件记录失败: %v", err)
	}

	// 更新Nginx配置
	if err := s.updateNginxConfig(); err != nil {
		return fmt.Errorf("更新Nginx配置失败: %v", err)
	}

	return nil
}

// GetWechatVerify 获取单个企业微信验证文件
func (s *WechatVerifyService) GetWechatVerify(id uint) (*dto.WechatVerifyResponse, error) {
	var verify model.WechatVerify
	if err := s.db.First(&verify, id).Error; err != nil {
		return nil, fmt.Errorf("验证文件不存在: %v", err)
	}

	return &dto.WechatVerifyResponse{
		ID:          verify.ID,
		FileName:    verify.FileName,
		Content:     verify.Content,
		IsEnabled:   verify.IsEnabled,
		Description: verify.Description,
		CreatedAt:   verify.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   verify.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// validateFileName 验证文件名格式
func (s *WechatVerifyService) validateFileName(fileName string) error {
	// 企业微信验证文件名格式：WW_verify_xxxxx.txt
	pattern := `^WW_verify_[a-zA-Z0-9]+\.txt$`
	matched, err := regexp.MatchString(pattern, fileName)
	if err != nil {
		return fmt.Errorf("验证文件名格式失败: %v", err)
	}
	if !matched {
		return fmt.Errorf("文件名格式不正确，应为：WW_verify_xxxxx.txt")
	}
	return nil
}

// writeVerifyFile 写入验证文件到磁盘
func (s *WechatVerifyService) writeVerifyFile(fileName, content string) error {
	filePath := filepath.Join(WECHAT_VERIFY_DIR, fileName)
	return os.WriteFile(filePath, []byte(content), 0644)
}

// removeVerifyFile 删除验证文件
func (s *WechatVerifyService) removeVerifyFile(fileName string) {
	filePath := filepath.Join(WECHAT_VERIFY_DIR, fileName)
	os.Remove(filePath) // 忽略错误，文件可能不存在
}

// updateNginxConfig 更新Nginx配置以支持验证文件访问
func (s *WechatVerifyService) updateNginxConfig() error {
	// 获取所有启用的验证文件
	var verifies []model.WechatVerify
	if err := s.db.Where("is_enabled = ?", true).Find(&verifies).Error; err != nil {
		return fmt.Errorf("获取启用的验证文件失败: %v", err)
	}

	// 生成Nginx location配置片段
	var locations []string
	for _, verify := range verifies {
		// 为每个验证文件生成一个location配置
		location := fmt.Sprintf(`        location = /%s {
            alias %s;
            access_log off;
            add_header Content-Type text/plain;
        }`, verify.FileName, filepath.Join(WECHAT_VERIFY_NGINX_DIR, verify.FileName))
		locations = append(locations, location)
	}

	// 创建包含文件内容
	configContent := strings.Join(locations, "\n")
	if configContent == "" {
		configContent = "# No active WeChat verification files"
	}

	includeFile := "/opt/asdsec-compose/openresty/conf.d/wechat-verify-locations.conf"

	// 确保配置目录存在
	if err := os.MkdirAll(filepath.Dir(includeFile), 0755); err != nil {
		return fmt.Errorf("创建Nginx配置目录失败: %v", err)
	}

	if err := os.WriteFile(includeFile, []byte(configContent), 0644); err != nil {
		return fmt.Errorf("写入Nginx配置文件失败: %v", err)
	}

	// 重新加载Nginx配置
	nginxService := NewNginxService()
	return nginxService.ReloadOpenresty()
}
