syntax = "proto3";
package asdsec.core.api.app;

option go_package = "asdsec.com/asec/platform/api/app/v1;v1";


message AgentGetAppReq{
  string user_id = 2;
  string appliance_id = 3;
  string ssid = 4;
  repeated string private_ip = 5;
  string public_ip = 6;
  repeated string dns = 7;
}

message GetAgentAppResp{
  repeated AppNetInfo apps = 1;
  repeated SeList se = 2;
  string fake_ip_range = 3;
  DNSConfig dns_config = 4;
  repeated string route_excludes = 5;
}

message DNSConfig{
  string primary_dns = 1; //首选dns
  string secondary_dns = 2; //备选dns
  string fallback_dns = 3; //特定域名的fallback dns(不使用首选以及备选dns,使用该dns解析特定域名)
  repeated string fallback_domains = 4; //特定域名列表
  repeated string fake_ip_exclude_domains = 5;//fake ip排除域名列表
  repeated NameserverPolicy nameserver_policies = 6; //域名解析策略
}
message NameserverPolicy {
  string nameserver = 1;
  repeated string domains = 2;
}

message AppNetInfo{
  uint64 app_id = 1;
  uint64 se_id = 2;
  string port = 3;
  string address = 4;
  string protocol = 5;
}

message SeList{
  uint64 se_id = 1;
  string se_ip = 2;
  int32 se_port = 3;
  string app_name = 4;
  bool tls = 5;
  bool skip_cert_verify = 6;
  string min_tls_version = 7;
  string max_tls_version = 8;
}

service GetApp{
  rpc AgentGetApp(AgentGetAppReq) returns (GetAgentAppResp);
}

