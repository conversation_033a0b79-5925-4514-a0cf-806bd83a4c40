package service

import (
	"asdsec.com/asec/platform/app/console/app/dynamic_strategy/dto"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/biz/strategy_engine"
	"asdsec.com/asec/platform/pkg/utils"
	"fmt"
	"github.com/jackc/pgtype"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
)

func DynamicRuleToRego(dynamicRule pgtype.JSONB) ([]byte, error) {
	// 先从前端数据中转换为结构体
	bytes := dynamicRule.Bytes
	rule := dto.DynamicRule{}

	err := jsoniter.Unmarshal(bytes, &rule)
	if err != nil {
		return nil, err
	}

	// 单个规则条件拼接
	var ruleArray []string
	// 整体Allow 条件拼接
	var allowArray []string

	for _, r := range rule.Rule {
		// 通过不同的因子名称转换
		switch r.FactorType {
		case dto.Browser:
			singleRule, singleAllow := SimpleTrans(r, dto.BrowserRuleTemplate, dto.BrowserAllow, dto.BrowserKey)
			ruleArray = append(ruleArray, singleRule)
			allowArray = append(allowArray, singleAllow)
		case dto.Os:
			singleRule, singleAllow := osTrans(r)
			ruleArray = append(ruleArray, singleRule)
			allowArray = append(allowArray, singleAllow)
		case dto.Region:
			singleRule, singleAllow := regionTrans(r)
			ruleArray = append(ruleArray, singleRule)
			allowArray = append(allowArray, singleAllow)
		case dto.Process:
			singleRule, singleAllow := processTrans(r)
			ruleArray = append(ruleArray, singleRule)
			allowArray = append(allowArray, singleAllow)
		case dto.Ip:
			singleRule, singleAllow := ipTrans(r)
			ruleArray = append(ruleArray, singleRule)
			allowArray = append(allowArray, singleAllow)
		case dto.NetLocation:
			singleRule, singleAllow := netLocationTrans(r)
			ruleArray = append(ruleArray, singleRule)
			allowArray = append(allowArray, singleAllow)
		default:
			global.SysLog.Sugar().Errorf("wrong factor type when trans:%s", r.FactorType)
			continue
		}
	}
	totalRules := strings.Join(ruleArray, "\n")
	totalRes := strings.Join(allowArray, "\n")
	regoFile := fmt.Sprintf(dto.TemplateRego, totalRules, totalRes)
	return []byte(regoFile), nil
}

func osTrans(r dto.Rule) (rule string, allow string) {
	if r.FactorOp == dto.NotIn {
		allow = fmt.Sprintf(dto.OsAllow, dto.Not)
	} else {
		allow = fmt.Sprintf(dto.OsAllow, "")
	}
	rule = fmt.Sprintf(dto.OsRuleTemplate, r.FactorAttr, getStringSliceRule(r.Condition, dto.OsKey))
	return
}

func netLocationTrans(r dto.Rule) (rule string, allow string) {
	allow = fmt.Sprintf(dto.NetLocationAllow, r.FactorOp)
	var dns []string
	var wifiSsd []string
	var publicIp []string
	var privateIp []string
	for _, m := range r.Condition {
		dns = append(dns, utils.MapGetStringSlice(m, "dns")...)
		wifiSsd = append(wifiSsd, utils.MapGetStringSlice(m, "wifi_ssd")...)
		publicIp = append(publicIp, utils.MapGetStringSlice(m, "public_ip")...)
		privateIp = append(privateIp, utils.MapGetStringSlice(m, "private_ip")...)
	}
	condition := strategy_engine.NetLocationRule{
		Dns:       dns,
		WifiSsd:   wifiSsd,
		PublicIp:  publicIp,
		PrivateIp: privateIp,
	}

	// 封装成json 因为rego方法入参限制
	jsonRules, _ := jsoniter.MarshalToString(condition)
	jsonRules = strconv.Quote(jsonRules)
	if r.FactorOp == dto.NotIn {
		rule = fmt.Sprintf(dto.NetLocationTemplate, r.FactorOp, dto.Not, r.FactorAttr, jsonRules)
	} else {
		rule = fmt.Sprintf(dto.NetLocationTemplate, r.FactorOp, dto.Empty, r.FactorAttr, jsonRules)
	}
	return
}

func regionTrans(r dto.Rule) (rule string, allow string) {
	allow = fmt.Sprintf(dto.RegionAllow, r.FactorOp)
	rule = fmt.Sprintf(dto.RegionTemplate, r.FactorOp, r.FactorAttr, `"`+r.FactorOp+`"`,
		getStringSliceRule(r.Condition, dto.ConditionNameKey))
	return
}

func ipTrans(r dto.Rule) (rule string, allow string) {
	var i []string
	for _, m := range r.Condition {
		i = append(i, utils.MapGetStringSlice(m, "ip_content")...)
	}
	conditionSet := QuoteStrings(i)
	if r.FactorOp == dto.NotIn {
		rule = fmt.Sprintf(dto.IpTemplate, r.FactorOp, dto.Not, r.FactorAttr, conditionSet)
	} else {
		rule = fmt.Sprintf(dto.IpTemplate, r.FactorOp, dto.Empty, r.FactorAttr, conditionSet)
	}
	allow = fmt.Sprintf(dto.IpAllow, r.FactorOp)
	return
}

func processTrans(r dto.Rule) (rule string, allow string) {
	var i []string
	for _, m := range r.Condition {
		i = append(i, utils.MapGetStringSlice(m, "process_name")...)
	}
	conditionSet := QuoteStrings(i)
	if r.FactorOp == dto.NotIn {
		rule = fmt.Sprintf(dto.ProcessTemplate, r.FactorOp, dto.Not, r.FactorAttr, conditionSet)
	} else {
		rule = fmt.Sprintf(dto.ProcessTemplate, r.FactorOp, dto.Empty, r.FactorAttr, conditionSet)
	}
	allow = fmt.Sprintf(dto.ProcessAllow, r.FactorOp)
	return
}

// SimpleTrans 简单的数组 in 规则转换
func SimpleTrans(r dto.Rule, ruleTemplate string, allowTemplate string, mapKey string) (rule string, allow string) {
	// not in 要使用  not <value> in <list> 语法
	if r.FactorOp == dto.NotIn {
		rule = fmt.Sprintf(ruleTemplate, r.FactorOp, dto.Not, r.FactorAttr, getStringSliceRule(r.Condition, mapKey))
	} else {
		rule = fmt.Sprintf(ruleTemplate, r.FactorOp, dto.Empty, r.FactorAttr, getStringSliceRule(r.Condition, mapKey))
	}
	allow = fmt.Sprintf(allowTemplate, r.FactorOp)
	return
}

// getStringSliceRule 获取规则中的简单数组
func getStringSliceRule(condition []map[string]interface{}, key string) string {
	var i []string
	for _, m := range condition {
		i = append(i, m[key].(string))
	}
	return QuoteStrings(i)

}

// QuoteStrings 为数组中的数据加上引号 , 再加上中括号 成为rego中数组
func QuoteStrings(input []string) string {
	var quoted []string
	for _, str := range input {
		quoted = append(quoted, `"`+str+`"`)
	}
	return "{" + strings.Join(quoted, ",") + "}"
}
