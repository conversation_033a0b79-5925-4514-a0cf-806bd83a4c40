package api

import (
	"asdsec.com/asec/platform/app/console/app/access/dto"
	"asdsec.com/asec/platform/app/console/app/access/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ListAccessLogs godoc
// @Summary 获取应用访问日志
// @Schemes
// @Description 获取应用访问日志
// @Tags        Logs
// @Produce     application/json
// @Param       req body dto.ListAccessLogReq true "获取应用访问日志参数"
// @Success     200
// @Router      /v1/access_log/list [POST]
// @success     200 {object} common.Response{data=model.Pagination} "ok"
func ListAccessLogs(c *gin.Context) {
	var req dto.ListAccessLogReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	logs, err := service.GetAccessLogService().ListAccessLogs(c, req)
	if err != nil {
		common.FailWithMessage(c, -1, "查询失败")
		return
	}
	common.OkWithData(c, logs)
}
