package dto

type UserSourceType string

const (
	UserSourceZZDMobile  UserSourceType = "zhezhendingmobile"
	UserSourceZZDScan    UserSourceType = "zhezhendingscan"
	UserSourceQiYeWx     UserSourceType = "qiyewx"
	UserSourceFeiShu     UserSourceType = "feishu"
	UserSourceDingtalk   UserSourceType = "dingtalk"
	UserSourceLdap       UserSourceType = "ldap"
	UserSourceMsad       UserSourceType = "msad"
	UserSourceLocal      UserSourceType = "local"
	UserSourceSMS        UserSourceType = "sms"
	UserSourceAD         UserSourceType = "msad"
	UserSourceInfogo     UserSourceType = "infogo"
	UserSourceOAuth2     UserSourceType = "oauth2" // 添加: OAuth2.0用户身份源
	UserSourceCas        UserSourceType = "cas"
	UserSourceWebOAuth   UserSourceType = "web"
	UserSourceEmail      UserSourceType = "email"
	UserSourceVerifyCode UserSourceType = "verify_code"

	//Oauth2派生用户源
	UserSourcePaila        UserSourceType = "paila"
	UserSourceZhuyun       UserSourceType = "zhuyun"
	UserSourceFanwei       UserSourceType = "fanwei"
	UserSourceZhezhengding UserSourceType = "zhezhending"
)

var UserSourceMap = map[UserSourceType]string{
	UserSourceQiYeWx:       "企业微信",
	UserSourceFeiShu:       "飞书",
	UserSourceDingtalk:     "钉钉",
	UserSourceLdap:         "LDAP",
	UserSourceMsad:         "MS ActiveDirectory",
	UserSourceLocal:        "本地",
	UserSourceInfogo:       "盈高身份源",
	UserSourceOAuth2:       "OAuth2.0票据", // 添加: OAuth2.0票据
	UserSourceCas:          "CAS票据",
	UserSourceWebOAuth:     "WEB服务器",
	UserSourcePaila:        "派拉",
	UserSourceZhuyun:       "竹云",
	UserSourceFanwei:       "泛微",
	UserSourceZhezhengding: "浙政钉",
	UserSourceEmail:        "邮箱",
	UserSourceVerifyCode:   "验证码",
}

// UserSourceMainIDPNumLimit 用户来源下主认证服务器的限制
var UserSourceMainIDPNumLimit = map[UserSourceType]int64{
	//UserSourceQiYeWx: 1,
	UserSourceLocal: 1, // 租户下只允许默认一个全局本地认证
}

type SourceClassifyType string

const (
	ThirdSource  SourceClassifyType = "third"
	CustomSource SourceClassifyType = "custom"
)

type SourceInfo struct {
	Type   SourceClassifyType
	Enable bool
}

var UserSourceClassify = map[UserSourceType]SourceInfo{
	UserSourceQiYeWx:       {Type: ThirdSource, Enable: true},
	UserSourceDingtalk:     {Type: ThirdSource, Enable: true},
	UserSourceFeiShu:       {Type: ThirdSource, Enable: true},
	UserSourceLdap:         {Type: ThirdSource, Enable: true},
	UserSourceMsad:         {Type: ThirdSource, Enable: true},
	UserSourceInfogo:       {Type: ThirdSource, Enable: true},
	UserSourceLocal:        {Type: CustomSource, Enable: true},
	UserSourceOAuth2:       {Type: ThirdSource, Enable: true}, // 添加: OAuth2.0票据
	UserSourceCas:          {Type: ThirdSource, Enable: true},
	UserSourceWebOAuth:     {Type: ThirdSource, Enable: true},
	UserSourcePaila:        {Type: ThirdSource, Enable: true},
	UserSourceZhuyun:       {Type: ThirdSource, Enable: true},
	UserSourceFanwei:       {Type: ThirdSource, Enable: true},
	UserSourceZhezhengding: {Type: ThirdSource, Enable: true},
}

type UserSource struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	SourceType   string `json:"source_type"`
	CorpID       string `json:"corp_id"`
	TemplateType string `json:"template_type"`
}

type SourceGroupResult struct {
	SourceID   string
	SourceType string
}
