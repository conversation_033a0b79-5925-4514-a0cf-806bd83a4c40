package access

import (
	"asdsec.com/asec/platform/app/console/app/access/api"
	"github.com/gin-gonic/gin"
)

func AccessStrategyApi(r *gin.RouterGroup) {
	v := r.Group("/v1/access_strategy")
	{
		v.POST("/strategy", api.CreateStrategy)
		v.PUT("/strategy", api.StrategyUpdate)
		v.DELETE("/strategy", api.StrategyDel)
		v.POST("/strategy_list", api.StrategyList)
		v.POST("/strategy_enable", api.StrategyEnable)
		v.POST("/sync_strategy", api.SyncStrategy)
		v.POST("/check_strategy_quote", api.CheckStrategyQuote)
	}
	v = r.Group("/v1/access_log")
	{
		v.POST("/list", api.ListAccessLogs)
	}
}
