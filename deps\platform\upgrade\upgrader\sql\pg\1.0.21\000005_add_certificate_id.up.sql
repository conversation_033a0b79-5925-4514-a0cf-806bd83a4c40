-- 添加证书ID字段到应用表（tb_application）

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'tb_application' 
        AND column_name = 'certificate_id'
    ) THEN
        ALTER TABLE public.tb_application ADD COLUMN certificate_id varchar(36) NULL;
    END IF;
END $$;

COMMENT ON COLUMN public.tb_application.certificate_id IS 'SSL证书ID，关联tb_certificate表的主键';

-- 添加外键约束（可选，如果tb_certificate表存在的话）
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'tb_certificate'
    ) THEN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE table_name = 'tb_application' 
            AND constraint_name = 'fk_tb_application_certificate_id'
        ) THEN
            ALTER TABLE public.tb_application 
            ADD CONSTRAINT fk_tb_application_certificate_id 
            FOREIGN KEY (certificate_id) REFERENCES public.tb_certificate(id) 
            ON DELETE SET NULL ON UPDATE CASCADE;
        END IF;
    END IF;
END $$;
