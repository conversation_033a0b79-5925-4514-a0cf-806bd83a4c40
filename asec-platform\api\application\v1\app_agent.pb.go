// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: application/v1/app_agent.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AgentGetAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string   `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ApplianceId string   `protobuf:"bytes,3,opt,name=appliance_id,json=applianceId,proto3" json:"appliance_id,omitempty"`
	Ssid        string   `protobuf:"bytes,4,opt,name=ssid,proto3" json:"ssid,omitempty"`
	PrivateIp   []string `protobuf:"bytes,5,rep,name=private_ip,json=privateIp,proto3" json:"private_ip,omitempty"`
	PublicIp    string   `protobuf:"bytes,6,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	Dns         []string `protobuf:"bytes,7,rep,name=dns,proto3" json:"dns,omitempty"`
}

func (x *AgentGetAppReq) Reset() {
	*x = AgentGetAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_agent_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentGetAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentGetAppReq) ProtoMessage() {}

func (x *AgentGetAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_agent_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentGetAppReq.ProtoReflect.Descriptor instead.
func (*AgentGetAppReq) Descriptor() ([]byte, []int) {
	return file_application_v1_app_agent_proto_rawDescGZIP(), []int{0}
}

func (x *AgentGetAppReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AgentGetAppReq) GetApplianceId() string {
	if x != nil {
		return x.ApplianceId
	}
	return ""
}

func (x *AgentGetAppReq) GetSsid() string {
	if x != nil {
		return x.Ssid
	}
	return ""
}

func (x *AgentGetAppReq) GetPrivateIp() []string {
	if x != nil {
		return x.PrivateIp
	}
	return nil
}

func (x *AgentGetAppReq) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *AgentGetAppReq) GetDns() []string {
	if x != nil {
		return x.Dns
	}
	return nil
}

type GetAgentAppResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Apps          []*AppNetInfo `protobuf:"bytes,1,rep,name=apps,proto3" json:"apps,omitempty"`
	Se            []*SeList     `protobuf:"bytes,2,rep,name=se,proto3" json:"se,omitempty"`
	FakeIpRange   string        `protobuf:"bytes,3,opt,name=fake_ip_range,json=fakeIpRange,proto3" json:"fake_ip_range,omitempty"`
	DnsConfig     *DNSConfig    `protobuf:"bytes,4,opt,name=dns_config,json=dnsConfig,proto3" json:"dns_config,omitempty"`
	RouteExcludes []string      `protobuf:"bytes,5,rep,name=route_excludes,json=routeExcludes,proto3" json:"route_excludes,omitempty"`
}

func (x *GetAgentAppResp) Reset() {
	*x = GetAgentAppResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_agent_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgentAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentAppResp) ProtoMessage() {}

func (x *GetAgentAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_agent_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentAppResp.ProtoReflect.Descriptor instead.
func (*GetAgentAppResp) Descriptor() ([]byte, []int) {
	return file_application_v1_app_agent_proto_rawDescGZIP(), []int{1}
}

func (x *GetAgentAppResp) GetApps() []*AppNetInfo {
	if x != nil {
		return x.Apps
	}
	return nil
}

func (x *GetAgentAppResp) GetSe() []*SeList {
	if x != nil {
		return x.Se
	}
	return nil
}

func (x *GetAgentAppResp) GetFakeIpRange() string {
	if x != nil {
		return x.FakeIpRange
	}
	return ""
}

func (x *GetAgentAppResp) GetDnsConfig() *DNSConfig {
	if x != nil {
		return x.DnsConfig
	}
	return nil
}

func (x *GetAgentAppResp) GetRouteExcludes() []string {
	if x != nil {
		return x.RouteExcludes
	}
	return nil
}

type DNSConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryDns           string              `protobuf:"bytes,1,opt,name=primary_dns,json=primaryDns,proto3" json:"primary_dns,omitempty"`                                   //首选dns
	SecondaryDns         string              `protobuf:"bytes,2,opt,name=secondary_dns,json=secondaryDns,proto3" json:"secondary_dns,omitempty"`                             //备选dns
	FallbackDns          string              `protobuf:"bytes,3,opt,name=fallback_dns,json=fallbackDns,proto3" json:"fallback_dns,omitempty"`                                //特定域名的fallback dns(不使用首选以及备选dns,使用该dns解析特定域名)
	FallbackDomains      []string            `protobuf:"bytes,4,rep,name=fallback_domains,json=fallbackDomains,proto3" json:"fallback_domains,omitempty"`                    //特定域名列表
	FakeIpExcludeDomains []string            `protobuf:"bytes,5,rep,name=fake_ip_exclude_domains,json=fakeIpExcludeDomains,proto3" json:"fake_ip_exclude_domains,omitempty"` //fake ip排除域名列表
	NameserverPolicies   []*NameserverPolicy `protobuf:"bytes,6,rep,name=nameserver_policies,json=nameserverPolicies,proto3" json:"nameserver_policies,omitempty"`           //域名解析策略
}

func (x *DNSConfig) Reset() {
	*x = DNSConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_agent_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DNSConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSConfig) ProtoMessage() {}

func (x *DNSConfig) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_agent_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSConfig.ProtoReflect.Descriptor instead.
func (*DNSConfig) Descriptor() ([]byte, []int) {
	return file_application_v1_app_agent_proto_rawDescGZIP(), []int{2}
}

func (x *DNSConfig) GetPrimaryDns() string {
	if x != nil {
		return x.PrimaryDns
	}
	return ""
}

func (x *DNSConfig) GetSecondaryDns() string {
	if x != nil {
		return x.SecondaryDns
	}
	return ""
}

func (x *DNSConfig) GetFallbackDns() string {
	if x != nil {
		return x.FallbackDns
	}
	return ""
}

func (x *DNSConfig) GetFallbackDomains() []string {
	if x != nil {
		return x.FallbackDomains
	}
	return nil
}

func (x *DNSConfig) GetFakeIpExcludeDomains() []string {
	if x != nil {
		return x.FakeIpExcludeDomains
	}
	return nil
}

func (x *DNSConfig) GetNameserverPolicies() []*NameserverPolicy {
	if x != nil {
		return x.NameserverPolicies
	}
	return nil
}

type NameserverPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nameserver string   `protobuf:"bytes,1,opt,name=nameserver,proto3" json:"nameserver,omitempty"`
	Domains    []string `protobuf:"bytes,2,rep,name=domains,proto3" json:"domains,omitempty"`
}

func (x *NameserverPolicy) Reset() {
	*x = NameserverPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_agent_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NameserverPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NameserverPolicy) ProtoMessage() {}

func (x *NameserverPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_agent_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NameserverPolicy.ProtoReflect.Descriptor instead.
func (*NameserverPolicy) Descriptor() ([]byte, []int) {
	return file_application_v1_app_agent_proto_rawDescGZIP(), []int{3}
}

func (x *NameserverPolicy) GetNameserver() string {
	if x != nil {
		return x.Nameserver
	}
	return ""
}

func (x *NameserverPolicy) GetDomains() []string {
	if x != nil {
		return x.Domains
	}
	return nil
}

type AppNetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    uint64 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	SeId     uint64 `protobuf:"varint,2,opt,name=se_id,json=seId,proto3" json:"se_id,omitempty"`
	Port     string `protobuf:"bytes,3,opt,name=port,proto3" json:"port,omitempty"`
	Address  string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Protocol string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
}

func (x *AppNetInfo) Reset() {
	*x = AppNetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_agent_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppNetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppNetInfo) ProtoMessage() {}

func (x *AppNetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_agent_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppNetInfo.ProtoReflect.Descriptor instead.
func (*AppNetInfo) Descriptor() ([]byte, []int) {
	return file_application_v1_app_agent_proto_rawDescGZIP(), []int{4}
}

func (x *AppNetInfo) GetAppId() uint64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *AppNetInfo) GetSeId() uint64 {
	if x != nil {
		return x.SeId
	}
	return 0
}

func (x *AppNetInfo) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *AppNetInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AppNetInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

type SeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeId           uint64 `protobuf:"varint,1,opt,name=se_id,json=seId,proto3" json:"se_id,omitempty"`
	SeIp           string `protobuf:"bytes,2,opt,name=se_ip,json=seIp,proto3" json:"se_ip,omitempty"`
	SePort         int32  `protobuf:"varint,3,opt,name=se_port,json=sePort,proto3" json:"se_port,omitempty"`
	AppName        string `protobuf:"bytes,4,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	Tls            bool   `protobuf:"varint,5,opt,name=tls,proto3" json:"tls,omitempty"`
	SkipCertVerify bool   `protobuf:"varint,6,opt,name=skip_cert_verify,json=skipCertVerify,proto3" json:"skip_cert_verify,omitempty"`
	MinTlsVersion  string `protobuf:"bytes,7,opt,name=min_tls_version,json=minTlsVersion,proto3" json:"min_tls_version,omitempty"`
	MaxTlsVersion  string `protobuf:"bytes,8,opt,name=max_tls_version,json=maxTlsVersion,proto3" json:"max_tls_version,omitempty"`
}

func (x *SeList) Reset() {
	*x = SeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_application_v1_app_agent_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeList) ProtoMessage() {}

func (x *SeList) ProtoReflect() protoreflect.Message {
	mi := &file_application_v1_app_agent_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeList.ProtoReflect.Descriptor instead.
func (*SeList) Descriptor() ([]byte, []int) {
	return file_application_v1_app_agent_proto_rawDescGZIP(), []int{5}
}

func (x *SeList) GetSeId() uint64 {
	if x != nil {
		return x.SeId
	}
	return 0
}

func (x *SeList) GetSeIp() string {
	if x != nil {
		return x.SeIp
	}
	return ""
}

func (x *SeList) GetSePort() int32 {
	if x != nil {
		return x.SePort
	}
	return 0
}

func (x *SeList) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *SeList) GetTls() bool {
	if x != nil {
		return x.Tls
	}
	return false
}

func (x *SeList) GetSkipCertVerify() bool {
	if x != nil {
		return x.SkipCertVerify
	}
	return false
}

func (x *SeList) GetMinTlsVersion() string {
	if x != nil {
		return x.MinTlsVersion
	}
	return ""
}

func (x *SeList) GetMaxTlsVersion() string {
	if x != nil {
		return x.MaxTlsVersion
	}
	return ""
}

var File_application_v1_app_agent_proto protoreflect.FileDescriptor

var file_application_v1_app_agent_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x13, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x22, 0xae, 0x01, 0x0a, 0x0e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x73, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x73, 0x73, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x5f, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x49, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x64, 0x6e, 0x73, 0x22, 0xfd, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x70,
	0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41,
	0x70, 0x70, 0x4e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x12,
	0x2b, 0x0a, 0x02, 0x73, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x73,
	0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x53, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x02, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x66, 0x61, 0x6b, 0x65, 0x5f, 0x69, 0x70, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6b, 0x65, 0x49, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x3d, 0x0a, 0x0a, 0x64, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x44, 0x4e, 0x53, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x64, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x45, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x73, 0x22, 0xae, 0x02, 0x0a, 0x09, 0x44, 0x4e, 0x53, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f,
	0x64, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x44, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61,
	0x72, 0x79, 0x5f, 0x64, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x64, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x6e, 0x73, 0x12, 0x29, 0x0a,
	0x10, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x66, 0x61, 0x6b, 0x65,
	0x5f, 0x69, 0x70, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x66, 0x61, 0x6b, 0x65, 0x49,
	0x70, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x12,
	0x56, 0x0a, 0x13, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61,
	0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x52, 0x12, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x22, 0x4c, 0x0a, 0x10, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x4e, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0xf2, 0x01, 0x0a, 0x06, 0x53,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x65, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x73, 0x65,
	0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x65, 0x49, 0x70, 0x12,
	0x17, 0x0a, 0x07, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x03, 0x74, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x63, 0x65,
	0x72, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x73, 0x6b, 0x69, 0x70, 0x43, 0x65, 0x72, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12,
	0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x6c, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x54, 0x6c, 0x73,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x74,
	0x6c, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6d, 0x61, 0x78, 0x54, 0x6c, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x32,
	0x62, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x12, 0x58, 0x0a, 0x0b, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x12, 0x23, 0x2e, 0x61, 0x73, 0x64, 0x73, 0x65,
	0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e,
	0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x42, 0x28, 0x5a, 0x26, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_application_v1_app_agent_proto_rawDescOnce sync.Once
	file_application_v1_app_agent_proto_rawDescData = file_application_v1_app_agent_proto_rawDesc
)

func file_application_v1_app_agent_proto_rawDescGZIP() []byte {
	file_application_v1_app_agent_proto_rawDescOnce.Do(func() {
		file_application_v1_app_agent_proto_rawDescData = protoimpl.X.CompressGZIP(file_application_v1_app_agent_proto_rawDescData)
	})
	return file_application_v1_app_agent_proto_rawDescData
}

var file_application_v1_app_agent_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_application_v1_app_agent_proto_goTypes = []interface{}{
	(*AgentGetAppReq)(nil),   // 0: asdsec.core.api.app.AgentGetAppReq
	(*GetAgentAppResp)(nil),  // 1: asdsec.core.api.app.GetAgentAppResp
	(*DNSConfig)(nil),        // 2: asdsec.core.api.app.DNSConfig
	(*NameserverPolicy)(nil), // 3: asdsec.core.api.app.NameserverPolicy
	(*AppNetInfo)(nil),       // 4: asdsec.core.api.app.AppNetInfo
	(*SeList)(nil),           // 5: asdsec.core.api.app.SeList
}
var file_application_v1_app_agent_proto_depIdxs = []int32{
	4, // 0: asdsec.core.api.app.GetAgentAppResp.apps:type_name -> asdsec.core.api.app.AppNetInfo
	5, // 1: asdsec.core.api.app.GetAgentAppResp.se:type_name -> asdsec.core.api.app.SeList
	2, // 2: asdsec.core.api.app.GetAgentAppResp.dns_config:type_name -> asdsec.core.api.app.DNSConfig
	3, // 3: asdsec.core.api.app.DNSConfig.nameserver_policies:type_name -> asdsec.core.api.app.NameserverPolicy
	0, // 4: asdsec.core.api.app.GetApp.AgentGetApp:input_type -> asdsec.core.api.app.AgentGetAppReq
	1, // 5: asdsec.core.api.app.GetApp.AgentGetApp:output_type -> asdsec.core.api.app.GetAgentAppResp
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_application_v1_app_agent_proto_init() }
func file_application_v1_app_agent_proto_init() {
	if File_application_v1_app_agent_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_application_v1_app_agent_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentGetAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_agent_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgentAppResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_agent_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DNSConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_agent_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NameserverPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_agent_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppNetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_application_v1_app_agent_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_application_v1_app_agent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_application_v1_app_agent_proto_goTypes,
		DependencyIndexes: file_application_v1_app_agent_proto_depIdxs,
		MessageInfos:      file_application_v1_app_agent_proto_msgTypes,
	}.Build()
	File_application_v1_app_agent_proto = out.File
	file_application_v1_app_agent_proto_rawDesc = nil
	file_application_v1_app_agent_proto_goTypes = nil
	file_application_v1_app_agent_proto_depIdxs = nil
}
