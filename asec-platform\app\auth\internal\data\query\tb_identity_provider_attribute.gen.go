// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbIdentityProviderAttribute(db *gorm.DB, opts ...gen.DOOption) tbIdentityProviderAttribute {
	_tbIdentityProviderAttribute := tbIdentityProviderAttribute{}

	_tbIdentityProviderAttribute.tbIdentityProviderAttributeDo.UseDB(db, opts...)
	_tbIdentityProviderAttribute.tbIdentityProviderAttributeDo.UseModel(&model.TbIdentityProviderAttribute{})

	tableName := _tbIdentityProviderAttribute.tbIdentityProviderAttributeDo.TableName()
	_tbIdentityProviderAttribute.ALL = field.NewAsterisk(tableName)
	_tbIdentityProviderAttribute.Key = field.NewString(tableName, "key")
	_tbIdentityProviderAttribute.Value = field.NewString(tableName, "value")
	_tbIdentityProviderAttribute.ProviderID = field.NewString(tableName, "provider_id")
	_tbIdentityProviderAttribute.CreatedAt = field.NewTime(tableName, "created_at")
	_tbIdentityProviderAttribute.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tbIdentityProviderAttribute.fillFieldMap()

	return _tbIdentityProviderAttribute
}

type tbIdentityProviderAttribute struct {
	tbIdentityProviderAttributeDo tbIdentityProviderAttributeDo

	ALL        field.Asterisk
	Key        field.String
	Value      field.String
	ProviderID field.String
	CreatedAt  field.Time
	UpdatedAt  field.Time

	fieldMap map[string]field.Expr
}

func (t tbIdentityProviderAttribute) Table(newTableName string) *tbIdentityProviderAttribute {
	t.tbIdentityProviderAttributeDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbIdentityProviderAttribute) As(alias string) *tbIdentityProviderAttribute {
	t.tbIdentityProviderAttributeDo.DO = *(t.tbIdentityProviderAttributeDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbIdentityProviderAttribute) updateTableName(table string) *tbIdentityProviderAttribute {
	t.ALL = field.NewAsterisk(table)
	t.Key = field.NewString(table, "key")
	t.Value = field.NewString(table, "value")
	t.ProviderID = field.NewString(table, "provider_id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tbIdentityProviderAttribute) WithContext(ctx context.Context) *tbIdentityProviderAttributeDo {
	return t.tbIdentityProviderAttributeDo.WithContext(ctx)
}

func (t tbIdentityProviderAttribute) TableName() string {
	return t.tbIdentityProviderAttributeDo.TableName()
}

func (t tbIdentityProviderAttribute) Alias() string { return t.tbIdentityProviderAttributeDo.Alias() }

func (t *tbIdentityProviderAttribute) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbIdentityProviderAttribute) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["key"] = t.Key
	t.fieldMap["value"] = t.Value
	t.fieldMap["provider_id"] = t.ProviderID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tbIdentityProviderAttribute) clone(db *gorm.DB) tbIdentityProviderAttribute {
	t.tbIdentityProviderAttributeDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbIdentityProviderAttribute) replaceDB(db *gorm.DB) tbIdentityProviderAttribute {
	t.tbIdentityProviderAttributeDo.ReplaceDB(db)
	return t
}

type tbIdentityProviderAttributeDo struct{ gen.DO }

func (t tbIdentityProviderAttributeDo) Debug() *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Debug())
}

func (t tbIdentityProviderAttributeDo) WithContext(ctx context.Context) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbIdentityProviderAttributeDo) ReadDB() *tbIdentityProviderAttributeDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbIdentityProviderAttributeDo) WriteDB() *tbIdentityProviderAttributeDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbIdentityProviderAttributeDo) Session(config *gorm.Session) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbIdentityProviderAttributeDo) Clauses(conds ...clause.Expression) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbIdentityProviderAttributeDo) Returning(value interface{}, columns ...string) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbIdentityProviderAttributeDo) Not(conds ...gen.Condition) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbIdentityProviderAttributeDo) Or(conds ...gen.Condition) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbIdentityProviderAttributeDo) Select(conds ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbIdentityProviderAttributeDo) Where(conds ...gen.Condition) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbIdentityProviderAttributeDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbIdentityProviderAttributeDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbIdentityProviderAttributeDo) Order(conds ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbIdentityProviderAttributeDo) Distinct(cols ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbIdentityProviderAttributeDo) Omit(cols ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbIdentityProviderAttributeDo) Join(table schema.Tabler, on ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbIdentityProviderAttributeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbIdentityProviderAttributeDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbIdentityProviderAttributeDo) Group(cols ...field.Expr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbIdentityProviderAttributeDo) Having(conds ...gen.Condition) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbIdentityProviderAttributeDo) Limit(limit int) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbIdentityProviderAttributeDo) Offset(offset int) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbIdentityProviderAttributeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbIdentityProviderAttributeDo) Unscoped() *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbIdentityProviderAttributeDo) Create(values ...*model.TbIdentityProviderAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbIdentityProviderAttributeDo) CreateInBatches(values []*model.TbIdentityProviderAttribute, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbIdentityProviderAttributeDo) Save(values ...*model.TbIdentityProviderAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbIdentityProviderAttributeDo) First() (*model.TbIdentityProviderAttribute, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderAttribute), nil
	}
}

func (t tbIdentityProviderAttributeDo) Take() (*model.TbIdentityProviderAttribute, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderAttribute), nil
	}
}

func (t tbIdentityProviderAttributeDo) Last() (*model.TbIdentityProviderAttribute, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderAttribute), nil
	}
}

func (t tbIdentityProviderAttributeDo) Find() ([]*model.TbIdentityProviderAttribute, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbIdentityProviderAttribute), err
}

func (t tbIdentityProviderAttributeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbIdentityProviderAttribute, err error) {
	buf := make([]*model.TbIdentityProviderAttribute, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbIdentityProviderAttributeDo) FindInBatches(result *[]*model.TbIdentityProviderAttribute, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbIdentityProviderAttributeDo) Attrs(attrs ...field.AssignExpr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbIdentityProviderAttributeDo) Assign(attrs ...field.AssignExpr) *tbIdentityProviderAttributeDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbIdentityProviderAttributeDo) Joins(fields ...field.RelationField) *tbIdentityProviderAttributeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbIdentityProviderAttributeDo) Preload(fields ...field.RelationField) *tbIdentityProviderAttributeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbIdentityProviderAttributeDo) FirstOrInit() (*model.TbIdentityProviderAttribute, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderAttribute), nil
	}
}

func (t tbIdentityProviderAttributeDo) FirstOrCreate() (*model.TbIdentityProviderAttribute, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbIdentityProviderAttribute), nil
	}
}

func (t tbIdentityProviderAttributeDo) FindByPage(offset int, limit int) (result []*model.TbIdentityProviderAttribute, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbIdentityProviderAttributeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbIdentityProviderAttributeDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbIdentityProviderAttributeDo) Delete(models ...*model.TbIdentityProviderAttribute) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbIdentityProviderAttributeDo) withDO(do gen.Dao) *tbIdentityProviderAttributeDo {
	t.DO = *do.(*gen.DO)
	return t
}
