// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"asdsec.com/asec/platform/app/auth/internal/data/model"
)

func newTbWxDepartment(db *gorm.DB, opts ...gen.DOOption) tbWxDepartment {
	_tbWxDepartment := tbWxDepartment{}

	_tbWxDepartment.tbWxDepartmentDo.UseDB(db, opts...)
	_tbWxDepartment.tbWxDepartmentDo.UseModel(&model.TbWxDepartment{})

	tableName := _tbWxDepartment.tbWxDepartmentDo.TableName()
	_tbWxDepartment.ALL = field.NewAsterisk(tableName)
	_tbWxDepartment.WxCorpID = field.NewString(tableName, "wx_corp_id")
	_tbWxDepartment.LocalRootGroupID = field.NewString(tableName, "local_root_group_id")
	_tbWxDepartment.ID = field.NewInt64(tableName, "id")
	_tbWxDepartment.Name = field.NewString(tableName, "name")
	_tbWxDepartment.NameEn = field.NewString(tableName, "name_en")
	_tbWxDepartment.Parentid = field.NewInt64(tableName, "parentid")
	_tbWxDepartment.Order = field.NewInt64(tableName, "order")
	_tbWxDepartment.CreatedAt = field.NewTime(tableName, "created_at")
	_tbWxDepartment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tbWxDepartment.LocalGroupID = field.NewString(tableName, "local_group_id")

	_tbWxDepartment.fillFieldMap()

	return _tbWxDepartment
}

type tbWxDepartment struct {
	tbWxDepartmentDo tbWxDepartmentDo

	ALL              field.Asterisk
	WxCorpID         field.String
	LocalRootGroupID field.String
	ID               field.Int64
	Name             field.String
	NameEn           field.String
	Parentid         field.Int64
	Order            field.Int64
	CreatedAt        field.Time
	UpdatedAt        field.Time
	LocalGroupID     field.String

	fieldMap map[string]field.Expr
}

func (t tbWxDepartment) Table(newTableName string) *tbWxDepartment {
	t.tbWxDepartmentDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tbWxDepartment) As(alias string) *tbWxDepartment {
	t.tbWxDepartmentDo.DO = *(t.tbWxDepartmentDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tbWxDepartment) updateTableName(table string) *tbWxDepartment {
	t.ALL = field.NewAsterisk(table)
	t.WxCorpID = field.NewString(table, "wx_corp_id")
	t.LocalRootGroupID = field.NewString(table, "local_root_group_id")
	t.ID = field.NewInt64(table, "id")
	t.Name = field.NewString(table, "name")
	t.NameEn = field.NewString(table, "name_en")
	t.Parentid = field.NewInt64(table, "parentid")
	t.Order = field.NewInt64(table, "order")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.LocalGroupID = field.NewString(table, "local_group_id")

	t.fillFieldMap()

	return t
}

func (t *tbWxDepartment) WithContext(ctx context.Context) *tbWxDepartmentDo {
	return t.tbWxDepartmentDo.WithContext(ctx)
}

func (t tbWxDepartment) TableName() string { return t.tbWxDepartmentDo.TableName() }

func (t tbWxDepartment) Alias() string { return t.tbWxDepartmentDo.Alias() }

func (t *tbWxDepartment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tbWxDepartment) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["wx_corp_id"] = t.WxCorpID
	t.fieldMap["local_root_group_id"] = t.LocalRootGroupID
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["name_en"] = t.NameEn
	t.fieldMap["parentid"] = t.Parentid
	t.fieldMap["order"] = t.Order
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["local_group_id"] = t.LocalGroupID
}

func (t tbWxDepartment) clone(db *gorm.DB) tbWxDepartment {
	t.tbWxDepartmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tbWxDepartment) replaceDB(db *gorm.DB) tbWxDepartment {
	t.tbWxDepartmentDo.ReplaceDB(db)
	return t
}

type tbWxDepartmentDo struct{ gen.DO }

func (t tbWxDepartmentDo) Debug() *tbWxDepartmentDo {
	return t.withDO(t.DO.Debug())
}

func (t tbWxDepartmentDo) WithContext(ctx context.Context) *tbWxDepartmentDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tbWxDepartmentDo) ReadDB() *tbWxDepartmentDo {
	return t.Clauses(dbresolver.Read)
}

func (t tbWxDepartmentDo) WriteDB() *tbWxDepartmentDo {
	return t.Clauses(dbresolver.Write)
}

func (t tbWxDepartmentDo) Session(config *gorm.Session) *tbWxDepartmentDo {
	return t.withDO(t.DO.Session(config))
}

func (t tbWxDepartmentDo) Clauses(conds ...clause.Expression) *tbWxDepartmentDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tbWxDepartmentDo) Returning(value interface{}, columns ...string) *tbWxDepartmentDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tbWxDepartmentDo) Not(conds ...gen.Condition) *tbWxDepartmentDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tbWxDepartmentDo) Or(conds ...gen.Condition) *tbWxDepartmentDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tbWxDepartmentDo) Select(conds ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tbWxDepartmentDo) Where(conds ...gen.Condition) *tbWxDepartmentDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tbWxDepartmentDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tbWxDepartmentDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tbWxDepartmentDo) Order(conds ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tbWxDepartmentDo) Distinct(cols ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tbWxDepartmentDo) Omit(cols ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tbWxDepartmentDo) Join(table schema.Tabler, on ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tbWxDepartmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tbWxDepartmentDo) RightJoin(table schema.Tabler, on ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tbWxDepartmentDo) Group(cols ...field.Expr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tbWxDepartmentDo) Having(conds ...gen.Condition) *tbWxDepartmentDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tbWxDepartmentDo) Limit(limit int) *tbWxDepartmentDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tbWxDepartmentDo) Offset(offset int) *tbWxDepartmentDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tbWxDepartmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tbWxDepartmentDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tbWxDepartmentDo) Unscoped() *tbWxDepartmentDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tbWxDepartmentDo) Create(values ...*model.TbWxDepartment) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tbWxDepartmentDo) CreateInBatches(values []*model.TbWxDepartment, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tbWxDepartmentDo) Save(values ...*model.TbWxDepartment) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tbWxDepartmentDo) First() (*model.TbWxDepartment, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxDepartment), nil
	}
}

func (t tbWxDepartmentDo) Take() (*model.TbWxDepartment, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxDepartment), nil
	}
}

func (t tbWxDepartmentDo) Last() (*model.TbWxDepartment, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxDepartment), nil
	}
}

func (t tbWxDepartmentDo) Find() ([]*model.TbWxDepartment, error) {
	result, err := t.DO.Find()
	return result.([]*model.TbWxDepartment), err
}

func (t tbWxDepartmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TbWxDepartment, err error) {
	buf := make([]*model.TbWxDepartment, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tbWxDepartmentDo) FindInBatches(result *[]*model.TbWxDepartment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tbWxDepartmentDo) Attrs(attrs ...field.AssignExpr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tbWxDepartmentDo) Assign(attrs ...field.AssignExpr) *tbWxDepartmentDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tbWxDepartmentDo) Joins(fields ...field.RelationField) *tbWxDepartmentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tbWxDepartmentDo) Preload(fields ...field.RelationField) *tbWxDepartmentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tbWxDepartmentDo) FirstOrInit() (*model.TbWxDepartment, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxDepartment), nil
	}
}

func (t tbWxDepartmentDo) FirstOrCreate() (*model.TbWxDepartment, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TbWxDepartment), nil
	}
}

func (t tbWxDepartmentDo) FindByPage(offset int, limit int) (result []*model.TbWxDepartment, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tbWxDepartmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tbWxDepartmentDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tbWxDepartmentDo) Delete(models ...*model.TbWxDepartment) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tbWxDepartmentDo) withDO(do gen.Dao) *tbWxDepartmentDo {
	t.DO = *do.(*gen.DO)
	return t
}
