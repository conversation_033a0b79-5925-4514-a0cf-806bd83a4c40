package api

import (
	"asdsec.com/asec/platform/app/console/app/access/dto"
	"asdsec.com/asec/platform/app/console/app/access/service"
	oprService "asdsec.com/asec/platform/app/console/app/oprlog/service"
	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/model"
	utils "asdsec.com/asec/platform/pkg/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strings"
)

// CreateStrategy godoc
// @Summary 新增访问策略
// @Schemes
// @Description 新增访问策略
// @Tags        AccessStrategy
// @Produce     application/json
// @Param       req body dto.CreateStrategyReq true "新增访问策略"
// @Success     200
// @Router      /v1/access_strategy/strategy [POST]
// @success     200 {object} common.Response{} "ok"
func CreateStrategy(c *gin.Context) {
	req := dto.CreateStrategyReq{}
	err := c.ShouldBindJSON(&req)
	if req.EnableAllUser == 0 {
		req.EnableAllUser = 2
	}
	if req.EnableAllApp == 0 {
		req.EnableAllApp = 2
	}

	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	//日志操作
	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateCreate,
			Representation: req.StrategyName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	// 查询名称是否存在
	count, err := service.GetStrategyService().StrategyCountByName(c, req.StrategyName)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("find strategy by name err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	if count > 0 {
		errorLog = "strategy already exists"
		common.Fail(c, common.StrategyNameRepeat)
		return
	}
	err = service.GetStrategyService().CreateStrategy(c, req)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("CreateStrategy err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// StrategyList godoc
// @Summary 获取访问策略列表
// @Schemes
// @Description 获取访问策略列表
// @Tags        AccessStrategy
// @Produce     application/json
// @Param       req body dto.StrategyListReq true "获取访问策略列表"
// @Success     200
// @Router      /v1/access_strategy/strategy_list [POST]
// @success     200 {object} common.Response{data=dto.StrategyListResp} "ok"
func StrategyList(c *gin.Context) {
	req := dto.StrategyListReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	resp, err := service.GetStrategyService().StrategyList(c, req)
	if err != nil {
		global.SysLog.Error("CreateStrategy err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, resp)
}

// StrategyUpdate godoc
// @Summary 编辑策略
// @Schemes
// @Description 编辑策略
// @Tags        AccessStrategy
// @Produce     application/json
// @Param       req body dto.StrategyUpdateReq true "编辑策略"
// @Success     200
// @Router      /v1/access_strategy/strategy [PUT]
// @success     200 {object} common.Response{} "ok"
func StrategyUpdate(c *gin.Context) {
	req := dto.StrategyUpdateReq{}
	err := c.ShouldBindJSON(&req)
	if req.EnableAllUser == 0 {
		req.EnableAllUser = 2
	}
	if req.EnableAllApp == 0 {
		req.EnableAllApp = 2
	}

	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateUpdate,
			Representation: req.StrategyName,
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err = service.GetStrategyService().StrategyUpdate(c, req)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("StrategyUpdate err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// StrategyDel godoc
// @Summary 删除策略
// @Schemes
// @Description 删除策略
// @Tags        AccessStrategy
// @Produce     application/json
// @Param       req body dto.StrategyDelReq true "删除策略"
// @Success     200
// @Router      /v1/access_strategy/strategy [DELETE]
// @success     200 {object} common.Response{} "ok"
func StrategyDel(c *gin.Context) {
	req := dto.StrategyDelReq{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	int64s, err := utils.StringsToInt64s(req.Id)
	if err != nil || len(int64s) == 0 {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	var errorLog = ""
	defer func() {
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateUpdate,
			Representation: strings.Join(req.Id, ""),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err = service.GetStrategyService().StrategyDel(c, int64s)
	if err != nil {
		errorLog = err.Error()
		global.SysLog.Error("StrategyUpdate err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// StrategyEnable godoc
// @Summary 启停策略
// @Schemes
// @Description 启停策略
// @Tags        AccessStrategy
// @Produce     application/json
// @Param       req body dto.StrategyEnableReq true "启停策略"
// @Success     200
// @Router      /v1/access_strategy/strategy_enable [POST]
// @success     200 {object} common.Response{} "ok"
func StrategyEnable(c *gin.Context) {
	vo := dto.StrategyEnableReq{}

	err := c.ShouldBindJSON(&vo)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	int64s, err := utils.StringsToInt64s(vo.Id)
	if err != nil || len(int64s) == 0 {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	var errorLog = ""
	defer func() {
		var operation_desc string
		if vo.Status == 1 {
			operation_desc = "Enable:"
		} else {
			operation_desc = "Disable:"
		}
		oplog := model.Oprlog{
			ResourceType:   common.AccessStrategyResourceType,
			OperationType:  common.OperateUpdate,
			Representation: operation_desc + strings.Join(vo.Id, " "),
			Error:          errorLog,
		}
		_, err := oprService.GetOprlogService().Create(c, oplog)
		if err != nil {
			global.SysLog.Error("record operate log failed", zap.Error(err))
		}
	}()

	err = service.GetStrategyService().StrategyEnable(c, int64s, vo.Status)
	if err != nil {
		global.SysLog.Error("StrategyUpdate err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// SyncStrategy godoc
// @Summary 同步策略
// @Schemes
// @Description 同步策略
// @Tags        AccessStrategy
// @Produce     application/json
// @Param       req body dto.SyncStrategyReq true "同步策略"
// @Success     200
// @Router      /v1/access_strategy/sync_strategy [POST]
// @success     200 {object} common.Response{} "ok"
// 删除用户或应用 / 或分组时调用
func SyncStrategy(c *gin.Context) {
	vo := dto.SyncStrategyReq{}

	err := c.ShouldBindJSON(&vo)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	err = service.GetStrategyService().SyncStrategy(c, vo)
	if err != nil {
		global.SysLog.Error("SyncStrategy err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.Ok(c)
}

// CheckStrategyQuote godoc
// @Summary 检查策略引用
// @Schemes
// @Description 检查策略引用
// @Tags        AccessStrategy
// @Produce     application/json
// @Param       req body dto.CheckStrategyQuoteReq true "检查策略引用"
// @Success     200
// @Router      /v1/access_strategy/check_strategy_quote [POST]
// @success     200 {object} common.Response{data=[]dto.CheckStrategyQuoteResp} "ok"
// 检查策略引用
func CheckStrategyQuote(c *gin.Context) {
	vo := dto.CheckStrategyQuoteReq{}

	err := c.ShouldBindJSON(&vo)
	if err != nil {
		global.SysLog.Error("param err", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}
	quoteResp, err := service.GetStrategyService().CheckStrategyQuote(c, vo)
	if err != nil {
		global.SysLog.Error("SyncStrategy err", zap.Error(err))
		common.Fail(c, common.OperateError)
		return
	}
	common.OkWithData(c, quoteResp)
}
