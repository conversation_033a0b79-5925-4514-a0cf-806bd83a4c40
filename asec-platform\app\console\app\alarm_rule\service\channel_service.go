package service

import (
	"asdsec.com/asec/platform/app/console/app/alarm_rule/model"
	"asdsec.com/asec/platform/app/console/app/alarm_rule/repository"
	"context"
	"sync"
	"time"
)

type ChannelTypeServiceImpl interface {
	GetChannelType(ctx context.Context, startT time.Time, endTime time.Time) ([]model.ChannelTypeDB, error)
	GetChannelName(ctx context.Context) (map[string]string, error)
}

var channelInit sync.Once
var channelInstance ChannelTypeServiceImpl

type channelServicePriv struct {
	db repository.ChannelRepositoryImpl
}

func (c *channelServicePriv) GetChannelName(ctx context.Context) (map[string]string, error) {
	return c.db.GetChannelName(ctx)
}

func (c *channelServicePriv) GetChannelType(ctx context.Context, startT time.Time, endTime time.Time) ([]model.ChannelTypeDB, error) {
	return c.db.GetChannelType(ctx, startT, endTime)
}

func ChannelTypeService() ChannelTypeServiceImpl {
	channelInit.Do(func() {
		channelInstance = &channelServicePriv{repository.NewChannelRepository()}
	})
	return channelInstance
}
