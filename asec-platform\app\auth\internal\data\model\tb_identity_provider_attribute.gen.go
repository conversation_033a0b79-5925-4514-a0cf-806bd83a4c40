// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbIdentityProviderAttribute = "tb_identity_provider_attribute"

// TbIdentityProviderAttribute mapped from table <tb_identity_provider_attribute>
type TbIdentityProviderAttribute struct {
	Key        string    `gorm:"column:key;not null" json:"key"`
	Value      string    `gorm:"column:value" json:"value"`
	ProviderID string    `gorm:"column:provider_id" json:"provider_id"`
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbIdentityProviderAttribute's table name
func (*TbIdentityProviderAttribute) TableName() string {
	return TableNameTbIdentityProviderAttribute
}
