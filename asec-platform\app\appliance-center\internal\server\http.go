package server

import (
	control "asdsec.com/asec/platform/api/agentcontrol/v1"
	appliance "asdsec.com/asec/platform/api/appliance/v1"
	"asdsec.com/asec/platform/app/appliance-center/internal/conf"
	"asdsec.com/asec/platform/pkg/service"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
)

// NewHTTPServer new a HTTP server.
func NewHTTPServer(c *conf.Server,
	controlService *service.ControlService,
	applianceMgtService *service.ApplianceMgtService,
	logger log.Logger,
) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
		),
		http.Filter(handlers.CORS(
			handlers.AllowedOrigins([]string{"*"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "DELETE"}),
			handlers.AllowedHeaders([]string{"Content-Type", "Authorization"}),
		),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	control.RegisterAgentControlHTTPServer(srv, controlService)
	appliance.RegisterApplianceMgtHTTPServer(srv, applianceMgtService)
	return srv
}
