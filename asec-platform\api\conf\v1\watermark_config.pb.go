// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/watermark_config.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WatermarkContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContentType []string `protobuf:"bytes,1,rep,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	Custom      string   `protobuf:"bytes,2,opt,name=custom,proto3" json:"custom,omitempty"`
}

func (x *WatermarkContent) Reset() {
	*x = WatermarkContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_watermark_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatermarkContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatermarkContent) ProtoMessage() {}

func (x *WatermarkContent) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_watermark_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatermarkContent.ProtoReflect.Descriptor instead.
func (*WatermarkContent) Descriptor() ([]byte, []int) {
	return file_conf_v1_watermark_config_proto_rawDescGZIP(), []int{0}
}

func (x *WatermarkContent) GetContentType() []string {
	if x != nil {
		return x.ContentType
	}
	return nil
}

func (x *WatermarkContent) GetCustom() string {
	if x != nil {
		return x.Custom
	}
	return ""
}

type WatermarkConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WatermarkSwitch     bool              `protobuf:"varint,1,opt,name=watermark_switch,json=watermarkSwitch,proto3" json:"watermark_switch,omitempty"`
	WatermarkContent    *WatermarkContent `protobuf:"bytes,2,opt,name=watermark_content,json=watermarkContent,proto3" json:"watermark_content,omitempty"`
	WatermarkColor      string            `protobuf:"bytes,3,opt,name=watermark_color,json=watermarkColor,proto3" json:"watermark_color,omitempty"`
	WatermarkAngle      int32             `protobuf:"varint,4,opt,name=watermark_angle,json=watermarkAngle,proto3" json:"watermark_angle,omitempty"`
	WatermarkAlpha      uint32            `protobuf:"varint,5,opt,name=watermark_alpha,json=watermarkAlpha,proto3" json:"watermark_alpha,omitempty"`
	WatermarkFontSize   uint32            `protobuf:"varint,6,opt,name=watermark_font_size,json=watermarkFontSize,proto3" json:"watermark_font_size,omitempty"`
	WatermarkRowSpacing uint32            `protobuf:"varint,7,opt,name=watermark_row_spacing,json=watermarkRowSpacing,proto3" json:"watermark_row_spacing,omitempty"`
	WatermarkColSpacing uint32            `protobuf:"varint,8,opt,name=watermark_col_spacing,json=watermarkColSpacing,proto3" json:"watermark_col_spacing,omitempty"`
}

func (x *WatermarkConfig) Reset() {
	*x = WatermarkConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_watermark_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatermarkConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatermarkConfig) ProtoMessage() {}

func (x *WatermarkConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_watermark_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatermarkConfig.ProtoReflect.Descriptor instead.
func (*WatermarkConfig) Descriptor() ([]byte, []int) {
	return file_conf_v1_watermark_config_proto_rawDescGZIP(), []int{1}
}

func (x *WatermarkConfig) GetWatermarkSwitch() bool {
	if x != nil {
		return x.WatermarkSwitch
	}
	return false
}

func (x *WatermarkConfig) GetWatermarkContent() *WatermarkContent {
	if x != nil {
		return x.WatermarkContent
	}
	return nil
}

func (x *WatermarkConfig) GetWatermarkColor() string {
	if x != nil {
		return x.WatermarkColor
	}
	return ""
}

func (x *WatermarkConfig) GetWatermarkAngle() int32 {
	if x != nil {
		return x.WatermarkAngle
	}
	return 0
}

func (x *WatermarkConfig) GetWatermarkAlpha() uint32 {
	if x != nil {
		return x.WatermarkAlpha
	}
	return 0
}

func (x *WatermarkConfig) GetWatermarkFontSize() uint32 {
	if x != nil {
		return x.WatermarkFontSize
	}
	return 0
}

func (x *WatermarkConfig) GetWatermarkRowSpacing() uint32 {
	if x != nil {
		return x.WatermarkRowSpacing
	}
	return 0
}

func (x *WatermarkConfig) GetWatermarkColSpacing() uint32 {
	if x != nil {
		return x.WatermarkColSpacing
	}
	return 0
}

var File_conf_v1_watermark_config_proto protoreflect.FileDescriptor

var file_conf_v1_watermark_config_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d,
	0x61, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0x4d, 0x0a, 0x10, 0x57, 0x61,
	0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x22, 0x98, 0x03, 0x0a, 0x0f, 0x57, 0x61,
	0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x29, 0x0a,
	0x10, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61,
	0x72, 0x6b, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x47, 0x0a, 0x11, 0x77, 0x61, 0x74, 0x65,
	0x72, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x57,
	0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x10, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x77, 0x61, 0x74, 0x65,
	0x72, 0x6d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x61,
	0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x61, 0x6e, 0x67, 0x6c, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x41, 0x6e,
	0x67, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b,
	0x5f, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x77, 0x61,
	0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x41, 0x6c, 0x70, 0x68, 0x61, 0x12, 0x2e, 0x0a, 0x13,
	0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x77, 0x61, 0x74, 0x65, 0x72,
	0x6d, 0x61, 0x72, 0x6b, 0x46, 0x6f, 0x6e, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x32, 0x0a, 0x15,
	0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x70,
	0x61, 0x63, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x77, 0x61, 0x74,
	0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x6f, 0x77, 0x53, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67,
	0x12, 0x32, 0x0a, 0x15, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x63, 0x6f,
	0x6c, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x13, 0x77, 0x61, 0x74, 0x65, 0x72, 0x6d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6c, 0x53, 0x70, 0x61,
	0x63, 0x69, 0x6e, 0x67, 0x42, 0x29, 0x5a, 0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_watermark_config_proto_rawDescOnce sync.Once
	file_conf_v1_watermark_config_proto_rawDescData = file_conf_v1_watermark_config_proto_rawDesc
)

func file_conf_v1_watermark_config_proto_rawDescGZIP() []byte {
	file_conf_v1_watermark_config_proto_rawDescOnce.Do(func() {
		file_conf_v1_watermark_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_watermark_config_proto_rawDescData)
	})
	return file_conf_v1_watermark_config_proto_rawDescData
}

var file_conf_v1_watermark_config_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_conf_v1_watermark_config_proto_goTypes = []interface{}{
	(*WatermarkContent)(nil), // 0: api.conf.WatermarkContent
	(*WatermarkConfig)(nil),  // 1: api.conf.WatermarkConfig
}
var file_conf_v1_watermark_config_proto_depIdxs = []int32{
	0, // 0: api.conf.WatermarkConfig.watermark_content:type_name -> api.conf.WatermarkContent
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_conf_v1_watermark_config_proto_init() }
func file_conf_v1_watermark_config_proto_init() {
	if File_conf_v1_watermark_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_watermark_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatermarkContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_v1_watermark_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatermarkConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_watermark_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_watermark_config_proto_goTypes,
		DependencyIndexes: file_conf_v1_watermark_config_proto_depIdxs,
		MessageInfos:      file_conf_v1_watermark_config_proto_msgTypes,
	}.Build()
	File_conf_v1_watermark_config_proto = out.File
	file_conf_v1_watermark_config_proto_rawDesc = nil
	file_conf_v1_watermark_config_proto_goTypes = nil
	file_conf_v1_watermark_config_proto_depIdxs = nil
}
