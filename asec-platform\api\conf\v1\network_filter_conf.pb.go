// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/network_filter_conf.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NetworkFilterSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable            bool     `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	GlobalBypass      bool     `protobuf:"varint,2,opt,name=global_bypass,json=globalBypass,proto3" json:"global_bypass,omitempty"`
	AggregateDuration uint32   `protobuf:"varint,3,opt,name=aggregate_duration,json=aggregateDuration,proto3" json:"aggregate_duration,omitempty"`
	ProcessList       []string `protobuf:"bytes,4,rep,name=process_list,json=processList,proto3" json:"process_list,omitempty"`
}

func (x *NetworkFilterSettings) Reset() {
	*x = NetworkFilterSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_network_filter_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkFilterSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkFilterSettings) ProtoMessage() {}

func (x *NetworkFilterSettings) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_network_filter_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkFilterSettings.ProtoReflect.Descriptor instead.
func (*NetworkFilterSettings) Descriptor() ([]byte, []int) {
	return file_conf_v1_network_filter_conf_proto_rawDescGZIP(), []int{0}
}

func (x *NetworkFilterSettings) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *NetworkFilterSettings) GetGlobalBypass() bool {
	if x != nil {
		return x.GlobalBypass
	}
	return false
}

func (x *NetworkFilterSettings) GetAggregateDuration() uint32 {
	if x != nil {
		return x.AggregateDuration
	}
	return 0
}

func (x *NetworkFilterSettings) GetProcessList() []string {
	if x != nil {
		return x.ProcessList
	}
	return nil
}

var File_conf_v1_network_filter_conf_proto protoreflect.FileDescriptor

var file_conf_v1_network_filter_conf_proto_rawDesc = []byte{
	0x0a, 0x21, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x22, 0xa6, 0x01,
	0x0a, 0x15, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x70, 0x61, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x79,
	0x70, 0x61, 0x73, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x11, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x29, 0x5a, 0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_network_filter_conf_proto_rawDescOnce sync.Once
	file_conf_v1_network_filter_conf_proto_rawDescData = file_conf_v1_network_filter_conf_proto_rawDesc
)

func file_conf_v1_network_filter_conf_proto_rawDescGZIP() []byte {
	file_conf_v1_network_filter_conf_proto_rawDescOnce.Do(func() {
		file_conf_v1_network_filter_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_network_filter_conf_proto_rawDescData)
	})
	return file_conf_v1_network_filter_conf_proto_rawDescData
}

var file_conf_v1_network_filter_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_conf_v1_network_filter_conf_proto_goTypes = []interface{}{
	(*NetworkFilterSettings)(nil), // 0: api.conf.NetworkFilterSettings
}
var file_conf_v1_network_filter_conf_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_conf_v1_network_filter_conf_proto_init() }
func file_conf_v1_network_filter_conf_proto_init() {
	if File_conf_v1_network_filter_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_network_filter_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkFilterSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_network_filter_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_network_filter_conf_proto_goTypes,
		DependencyIndexes: file_conf_v1_network_filter_conf_proto_depIdxs,
		MessageInfos:      file_conf_v1_network_filter_conf_proto_msgTypes,
	}.Build()
	File_conf_v1_network_filter_conf_proto = out.File
	file_conf_v1_network_filter_conf_proto_rawDesc = nil
	file_conf_v1_network_filter_conf_proto_goTypes = nil
	file_conf_v1_network_filter_conf_proto_depIdxs = nil
}
