// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.20.1
// source: auth/v1/oidc/oidc.proto

package oidc

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationOIDCAuthorize = "/api.auth.v1.oidc.OIDC/Authorize"
const OperationOIDCDiscovery = "/api.auth.v1.oidc.OIDC/Discovery"
const OperationOIDCJWKS = "/api.auth.v1.oidc.OIDC/JWKS"
const OperationOIDCLogout = "/api.auth.v1.oidc.OIDC/Logout"
const OperationOIDCRevoke = "/api.auth.v1.oidc.OIDC/Revoke"
const OperationOIDCToken = "/api.auth.v1.oidc.OIDC/Token"
const OperationOIDCUserInfo = "/api.auth.v1.oidc.OIDC/UserInfo"

type OIDCHTTPServer interface {
	// Authorize 授权端点 - OIDC授权码流程的起始点
	Authorize(context.Context, *AuthorizeRequest) (*AuthorizeReply, error)
	// Discovery 发现端点 - 提供OIDC提供者的元数据
	Discovery(context.Context, *DiscoveryRequest) (*DiscoveryReply, error)
	// JWKS JWKS端点 - 提供用于验证JWT签名的公钥集
	JWKS(context.Context, *JWKSRequest) (*JWKSReply, error)
	// Logout 登出端点 - 结束用户会话
	Logout(context.Context, *LogoutRequest) (*LogoutReply, error)
	// Revoke 令牌撤销端点 - 撤销访问令牌或刷新令牌
	Revoke(context.Context, *RevokeRequest) (*RevokeReply, error)
	// Token 令牌端点 - 用授权码交换访问令牌和ID令牌
	Token(context.Context, *TokenRequest) (*TokenReply, error)
	// UserInfo 用户信息端点 - 使用访问令牌获取用户信息
	UserInfo(context.Context, *UserInfoRequest) (*UserInfoReply, error)
}

func RegisterOIDCHTTPServer(s *http.Server, srv OIDCHTTPServer) {
	r := s.Route("/")
	r.GET("/auth/login/v1/authorize/oidc/authorize", _OIDC_Authorize0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/authorize/oidc/token", _OIDC_Token0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/authorize/oidc/userinfo", _OIDC_UserInfo0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/authorize/oidc/.well-known/openid_configuration", _OIDC_Discovery0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/authorize/oidc/jwks", _OIDC_JWKS0_HTTP_Handler(srv))
	r.POST("/auth/login/v1/authorize/oidc/revoke", _OIDC_Revoke0_HTTP_Handler(srv))
	r.GET("/auth/login/v1/authorize/oidc/logout", _OIDC_Logout1_HTTP_Handler(srv))
}

func _OIDC_Authorize0_HTTP_Handler(srv OIDCHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AuthorizeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOIDCAuthorize)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Authorize(ctx, req.(*AuthorizeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AuthorizeReply)
		return ctx.Result(200, reply)
	}
}

func _OIDC_Token0_HTTP_Handler(srv OIDCHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TokenRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOIDCToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Token(ctx, req.(*TokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TokenReply)
		return ctx.Result(200, reply)
	}
}

func _OIDC_UserInfo0_HTTP_Handler(srv OIDCHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOIDCUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserInfo(ctx, req.(*UserInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserInfoReply)
		return ctx.Result(200, reply)
	}
}

func _OIDC_Discovery0_HTTP_Handler(srv OIDCHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DiscoveryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOIDCDiscovery)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Discovery(ctx, req.(*DiscoveryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DiscoveryReply)
		return ctx.Result(200, reply)
	}
}

func _OIDC_JWKS0_HTTP_Handler(srv OIDCHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JWKSRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOIDCJWKS)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JWKS(ctx, req.(*JWKSRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*JWKSReply)
		return ctx.Result(200, reply)
	}
}

func _OIDC_Revoke0_HTTP_Handler(srv OIDCHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RevokeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOIDCRevoke)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Revoke(ctx, req.(*RevokeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RevokeReply)
		return ctx.Result(200, reply)
	}
}

func _OIDC_Logout1_HTTP_Handler(srv OIDCHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LogoutRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationOIDCLogout)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Logout(ctx, req.(*LogoutRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LogoutReply)
		return ctx.Result(200, reply)
	}
}

type OIDCHTTPClient interface {
	Authorize(ctx context.Context, req *AuthorizeRequest, opts ...http.CallOption) (rsp *AuthorizeReply, err error)
	Discovery(ctx context.Context, req *DiscoveryRequest, opts ...http.CallOption) (rsp *DiscoveryReply, err error)
	JWKS(ctx context.Context, req *JWKSRequest, opts ...http.CallOption) (rsp *JWKSReply, err error)
	Logout(ctx context.Context, req *LogoutRequest, opts ...http.CallOption) (rsp *LogoutReply, err error)
	Revoke(ctx context.Context, req *RevokeRequest, opts ...http.CallOption) (rsp *RevokeReply, err error)
	Token(ctx context.Context, req *TokenRequest, opts ...http.CallOption) (rsp *TokenReply, err error)
	UserInfo(ctx context.Context, req *UserInfoRequest, opts ...http.CallOption) (rsp *UserInfoReply, err error)
}

type OIDCHTTPClientImpl struct {
	cc *http.Client
}

func NewOIDCHTTPClient(client *http.Client) OIDCHTTPClient {
	return &OIDCHTTPClientImpl{client}
}

func (c *OIDCHTTPClientImpl) Authorize(ctx context.Context, in *AuthorizeRequest, opts ...http.CallOption) (*AuthorizeReply, error) {
	var out AuthorizeReply
	pattern := "/auth/login/v1/authorize/oidc/authorize"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOIDCAuthorize))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OIDCHTTPClientImpl) Discovery(ctx context.Context, in *DiscoveryRequest, opts ...http.CallOption) (*DiscoveryReply, error) {
	var out DiscoveryReply
	pattern := "/auth/login/v1/authorize/oidc/.well-known/openid_configuration"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOIDCDiscovery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OIDCHTTPClientImpl) JWKS(ctx context.Context, in *JWKSRequest, opts ...http.CallOption) (*JWKSReply, error) {
	var out JWKSReply
	pattern := "/auth/login/v1/authorize/oidc/jwks"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOIDCJWKS))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OIDCHTTPClientImpl) Logout(ctx context.Context, in *LogoutRequest, opts ...http.CallOption) (*LogoutReply, error) {
	var out LogoutReply
	pattern := "/auth/login/v1/authorize/oidc/logout"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOIDCLogout))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OIDCHTTPClientImpl) Revoke(ctx context.Context, in *RevokeRequest, opts ...http.CallOption) (*RevokeReply, error) {
	var out RevokeReply
	pattern := "/auth/login/v1/authorize/oidc/revoke"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOIDCRevoke))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OIDCHTTPClientImpl) Token(ctx context.Context, in *TokenRequest, opts ...http.CallOption) (*TokenReply, error) {
	var out TokenReply
	pattern := "/auth/login/v1/authorize/oidc/token"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationOIDCToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *OIDCHTTPClientImpl) UserInfo(ctx context.Context, in *UserInfoRequest, opts ...http.CallOption) (*UserInfoReply, error) {
	var out UserInfoReply
	pattern := "/auth/login/v1/authorize/oidc/userinfo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationOIDCUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
