package dto

type ForwardAuthPlugin struct {
	ClientHeaders  []string `json:"client_headers"`
	RequestHeaders []string `json:"request_headers"`
	SslVerify      bool     `json:"ssl_verify"`
	Uri            string   `json:"uri"`
}

type ResponseRewritePlugin struct {
	Disable    bool                        `json:"disable"`
	Headers    ResponseRewriteHeaderConfig `json:"headers,omitempty"`
	Filters    []FilterConfig              `json:"filters,omitempty"`
	BodyBase64 bool                        `json:"body_base64"`
}

type ResponseProcessPlugin struct {
	Disable bool `json:"disable"`
}

type ResponseRewriteHeaderConfig struct {
	Update []UpdateConfig    `json:"update,omitempty"`
	Set    map[string]string `json:"set,omitempty"`
	Add    []string          `json:"add,omitempty"`
	Remove []string          `json:"remove,omitempty"`
}
type UpdateConfig struct {
	HeaderKey     string `json:"header_key"`
	HeaderOrigin  string `json:"header_origin"`
	HeaderReplace string `json:"header_replace"`
}

type FilterConfig struct {
	Options string `json:"options"`
	Regex   string `json:"regex"`
	Replace string `json:"replace"`
	Scope   string `json:"scope"`
}

type ProxyRewritePlugin struct {
	Headers ProxyRewriteHeaderCfg `json:"headers"`
}
type ProxyRewriteHeaderCfg struct {
	Add map[string]string `json:"add,omitempty"`
	Set map[string]string `json:"set,omitempty"`
}

type ExtPluginPostReqPlugin struct {
	Conf []ExtPluginConf `json:"conf,omitempty"`
}
type ExtPluginConf struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type StrategyPluginConf struct {
	Enable           bool `json:"enable"`
	AllowDegradation bool `json:"allow_degradation,omitempty"` // 是否允许降级模式，当认证服务异常时默认放行
}
