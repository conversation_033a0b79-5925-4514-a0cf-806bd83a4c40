package model

import "time"

type AlertRecord struct {
	ID          uint64    `gorm:"column:id" json:"id"`
	TenantId    uint64    `gorm:"column:tenant_id;default:1200" json:"tenant_id"`
	SendPlat    string    `gorm:"column:send_plat" json:"send_plat"`
	SendMsg     string    `gorm:"column:send_msg" json:"send_msg"`
	WebhookAddr string    `gorm:"column:webhook_addr" json:"webhook_addr"`
	ApiSecret   string    `gorm:"column:api_secret" json:"api_secret"`
	SendResult  int       `gorm:"column:api_secret" json:"send_result"`
	CreateTime  time.Time `gorm:"column:create_time;default:null" json:"create_time"`
	UpdateTime  time.Time `gorm:"column:update_time;default:null" json:"update_time"`
}

func (AlertRecord) TableName() string {
	return "alert_record"
}
