package model

import (
	"strings"
	"time"
)

// OAuth2Client OAuth2客户端信息
type OAuth2Client struct {
	ID           int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	CorpID       int64     `json:"corp_id" gorm:"not null"`
	AppID        *int64    `json:"app_id" gorm:"index"`
	ClientID     string    `json:"client_id" gorm:"unique;not null;size:64"`
	ClientSecret string    `json:"client_secret" gorm:"not null;size:128"`
	Name         string    `json:"name" gorm:"not null;size:128"`
	RedirectURI  string    `json:"redirect_uri" gorm:"not null;size:255"`
	Scope        string    `json:"scope" gorm:"size:255"`
	Status       int16     `json:"status" gorm:"default:1"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (OAuth2Client) TableName() string {
	return "tb_oauth2_clients"
}

// GetRedirectURIList 获取回调地址列表
func (c *OAuth2Client) GetRedirectURIList() []string {
	if c.RedirectURI == "" {
		return []string{}
	}

	uris := strings.Split(c.RedirectURI, "\n")
	var result []string
	for _, uri := range uris {
		uri = strings.TrimSpace(uri)
		if uri != "" {
			result = append(result, uri)
		}
	}
	return result
}

// SetRedirectURIList 设置回调地址列表
func (c *OAuth2Client) SetRedirectURIList(uris []string) {
	var validURIs []string
	for _, uri := range uris {
		uri = strings.TrimSpace(uri)
		if uri != "" {
			validURIs = append(validURIs, uri)
		}
	}
	c.RedirectURI = strings.Join(validURIs, "\n")
}

// HasRedirectURI 检查是否包含指定的回调地址
func (c *OAuth2Client) HasRedirectURI(uri string) bool {
	uris := c.GetRedirectURIList()
	for _, registeredURI := range uris {
		if registeredURI == uri {
			return true
		}
	}
	return false
}
