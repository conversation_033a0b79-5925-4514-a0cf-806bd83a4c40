package qiyewx

import (
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp"
	"fmt"

	"github.com/mozillazg/go-pinyin"
	"github.com/wenerme/go-wecom/wecom"
)

type WeComClient struct {
	client *wecom.Client
}

func NewWeComClient(corpId, secret string, agentId int) *WeComClient {
	client := wecom.NewClient(wecom.Conf{
		CorpID:     corpId,
		AgentID:    agentId,
		CorpSecret: secret,
	})
	return &WeComClient{client: client}
}

// 官方文档： https://developer.work.weixin.qq.com/document/path/90208
// GetAllDepts 获取所有部门
func (wc WeComClient) GetAllDepts() (ret []*dto.ExternalDepartment, err error) {
	depts, err := wc.client.ListDepartment(
		&wecom.ListDepartmentRequest{},
	)
	if err != nil {
		return nil, err
	}
	for _, dept := range depts.Department {
		var ele dto.ExternalDepartment
		ele.Name = dept.Name
		ele.ID = fmt.Sprintf("%d", dept.ID)
		ele.NameEn = dept.NameEn
		ele.Parentid = fmt.Sprintf("%d", dept.ParentID)
		ele.UniqKey = idp.GetKey(ele)
		ret = append(ret, &ele)
	}
	return ret, nil
}

// 官方文档： https://developer.work.weixin.qq.com/document/path/90201
// GetAllUsers 获取所有员工信息
func (wc WeComClient) GetAllUsers(rootDept string, fetchChild string) (ret []*dto.ExternalUser, err error) {
	users, err := wc.client.ListUser(
		&wecom.ListUserRequest{
			DepartmentID: rootDept,
			FetchChild:   fetchChild,
		},
	)
	if err != nil {
		return nil, err
	}
	for _, user := range users.UserList {
		var ele dto.ExternalUser
		ele.Name = user.Name
		ele.Userid = user.UserID
		ele.MainDepartment = fmt.Sprintf("%d", user.MainDepartment)
		ele.Mobile = user.Mobile
		ele.Email = user.Email
		ele.UniqKey = idp.GetKey(ele)
		ret = append(ret, &ele)
	}
	return ret, nil
}

func ConvertToPinYin(src string) (dst string) {
	args := pinyin.NewArgs()
	args.Fallback = func(r rune, args pinyin.Args) []string {
		return []string{string(r)}
	}

	for _, singleResult := range pinyin.Pinyin(src, args) {
		for _, result := range singleResult {
			dst = dst + result
		}
	}
	return
}
