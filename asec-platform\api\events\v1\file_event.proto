syntax = "proto3";
package api.asdsec.file_event;
option go_package = "asdsec.com/asec/platform/api/events/v1;v1";
option java_package = "com.asdsec.core.api";

service Event {
  rpc CreateLog (stream FileEventReq) returns (Reply);

}
message FileEventReq{
  repeated FileEvent events = 1;
}
enum Severity {
  UNUSED = 0;
  INFO = 1;
  LOW = 2;
  MEDIUM = 3;
  HIGH = 4;
  CRITICAL = 5;
}

enum ChannelType {
  IM = 0;
  EMAIL = 1;
  FTP = 2;
  USB = 3;
  UNKNOWN = 4;
}

message FileEvent {
  string uuid = 1;
  string corp_id = 2;
  string event_type = 3;
  string event_sub_type = 4;
  string event_source = 5;
  string user_id = 6;
  string user_name = 7;
  uint64 agent_id = 8;
  string agent_name = 9;
  repeated string agent_ip = 10;
  repeated string agent_mac = 11;
  string file_name = 12;
  string file_type = 13;
  string file_path = 14;
  string original_file_name = 15;
  string original_file_path = 16;
  int64 file_size = 17;
  string owner = 18;
  int64 file_create_time = 19;
  int64 last_change_time = 20;
  string extension_name = 21;
  int64 file_category_id = 22;
  string real_extension_name = 23;
  string name_match_info = 24;
  string content_match_info = 25;
  string md5 = 26;
  string sha256 = 27;
  string activity = 28;
  int64 occur_time = 29;
  string channel = 30;
  string channel_type = 31;
  string software_path = 32;
  string dst_path = 33;
  int32 score = 34;
  string sensitive_rule_id = 35;
  string sensitive_rule_name = 36;
  int32 sensitive_level = 37;
  string data_category = 38;
  Severity severity = 39;
  int32 severity_id = 40;
  string plat_type = 41;
  repeated string trace_id = 42;
  int32 compress_encrypt = 43;
  repeated string user_tags = 44;
  int64  ingestion_time = 45;
  int64  create_time = 46;
  repeated string  file_properties = 47;
  // 网关接入日志信息
  string src_ip = 48;
  int32 src_port = 49;
  string src_country = 50;
  string src_country_name = 51;
  string src_province = 52;
  string src_city = 53;
  string src_latitude = 54;
  string src_longitude = 55;
  string dst_ip = 56;
  int32 dst_port = 57;
  string dst_url = 58;
  int64 app_id = 59;
  string app_name = 60;
  string protocol = 61;
  int64 strategy_id = 62;
  string strategy_name = 63;
  int32  access_status = 64;
  string access_method = 65;
  string deny_reason = 66;
  string auth_error = 67;
  string auth_error_detail = 68;
  repeated string sub_src_trace_id = 69;
  string public_ip = 70;
  string score_reason = 71;
  string src_path = 72;
  string source_id = 73;
  string source_name = 74;
  string source_type = 75;
  bool check_file_suffix = 76;
  bool check_file_compress = 77;
  string sensitive_info = 78;
  uint32 file_hide_suffix=79;
  string data_category_name = 80;
  // 策略命中时间(ms)
  uint32 strategy_check_time = 81;
  string process_info = 82;
}
message MatchInfoList{
  repeated MatchInfo matchInfo = 1;
}

message MatchInfo {
  int64 rule_id = 1;
  string desc = 2;
  string sensitive_data = 3;
}
message Reply{

}
