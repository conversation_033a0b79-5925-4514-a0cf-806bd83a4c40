package service

import (
	"context"

	"github.com/jinzhu/copier"

	"asdsec.com/asec/platform/app/auth/internal/dto"

	"asdsec.com/asec/platform/app/auth/internal/common"

	pb "asdsec.com/asec/platform/api/auth/v1/admin"
)

func (s *AdminService) CreateRole(ctx context.Context, req *pb.CreateRoleRequest) (*pb.CreateRoleReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateRoleReply{Status: pb.StatusCode_FAILED}, err
	}

	if err := s.role.CreateRole(ctx, req.Name, corpId, req.Description, req.UserIdList); err != nil {
		return &pb.CreateRoleReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateRoleReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) ListRole(ctx context.Context, req *pb.ListRoleRequest) (*pb.ListRoleReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListRoleReply{}, err
	}

	resp, err := s.role.ListRole(ctx, corpId, req.Limit, req.Offset, req.Search)
	if err != nil {
		return &pb.ListRoleReply{}, err
	}
	var result []*pb.Role
	for _, r := range resp.Roles {
		var users []*pb.UserInfo
		for _, u := range r.Users {
			var temp pb.UserInfo
			if err := copier.Copy(&temp, &u); err != nil {
				return &pb.ListRoleReply{}, err
			}
			temp.Id = u.ID
			users = append(users, &temp)
		}

		result = append(result, &pb.Role{
			Id:          r.ID,
			Name:        r.Name,
			Description: r.Description,
			Users:       users,
		})
	}
	return &pb.ListRoleReply{Role: result, Count: resp.Count}, nil
}

func (s *AdminService) UpdateRole(ctx context.Context, req *pb.UpdateRoleRequest) (*pb.UpdateRoleReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateRoleReply{Status: pb.StatusCode_FAILED}, err
	}
	var userInfos []dto.UserInfo
	for _, v := range req.UserIdList {
		userInfos = append(userInfos, dto.UserInfo{RoleID: req.Id, ID: v})
	}
	if err := s.role.UpdateRole(ctx, req.Id, req.Name, corpId, req.Description, userInfos); err != nil {
		return &pb.UpdateRoleReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateRoleReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) DeleteRole(ctx context.Context, req *pb.DeleteRoleRequest) (*pb.DeleteRoleReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.DeleteRoleReply{Status: pb.StatusCode_FAILED}, err
	}

	if err := s.role.DeleteRole(ctx, corpId, req.Id, req.Name); err != nil {
		return &pb.DeleteRoleReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.DeleteRoleReply{Status: pb.StatusCode_SUCCESS}, nil
}
