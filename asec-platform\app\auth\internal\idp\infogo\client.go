package infogo

import (
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
)

type InfogoClient struct {
	Endpoint string
	Login    string
	Password string
}

func NewInfogoClient(endpoint, login, password string) *InfogoClient {
	client := InfogoClient{
		Endpoint: endpoint,
		Login:    login,
		Password: password,
	}
	return &client
}

type Dept struct {
	DepartID      string `json:"DepartID"`
	DepartName    string `json:"DepartName"`
	UpID          string `json:"UpID"`
	AllDepartName string `json:"AllDepartName"`
	OrderIndex    string `json:"OrderIndex"`
}

type Account struct {
	ID        json.Number `json:"ID,omitempty"`
	UserName  string      `json:"UserName,omitempty"`
	TrueNames string      `json:"TrueNames,omitempty"`
	DepartID  string      `json:"DepartID,omitempty"`
	Tel       string      `json:"Tel,omitempty"`
	Email     string      `json:"Email,omitempty"`
}

type ApiResponse[T Dept | Account] struct {
	Page     int    `json:"page"`
	Total    int    `json:"total"`
	PageSize int    `json:"pagesize"`
	Rows     []T    `json:"rows"`
	Status   string `json:"status"`
	Info     string `json:"info"`
}

const departmentsPath = "/phpdir/trade_third.php"

func (c InfogoClient) GetAllDepts() (ret []*dto.ExternalDepartment, err error) {
	depts, err := fetchDepartments(c.Endpoint, c.Login, c.Password, 0, 0)
	if err != nil {
		return nil, err
	}
	for _, deptRow := range depts.Rows {
		var ele dto.ExternalDepartment
		ele.Name = deptRow.DepartName
		ele.ID = deptRow.DepartID
		ele.NameEn = deptRow.AllDepartName
		ele.Parentid = deptRow.UpID
		ele.UniqKey = idp.GetKey(ele)
		ret = append(ret, &ele)
	}
	return ret, nil
}

func (c InfogoClient) GetAllUsers() (ret []*dto.ExternalUser, err error) {
	users, err := fetchAccounts(c.Endpoint, c.Login, c.Password, 0, 0)
	if err != nil {
		return nil, err
	}
	for _, user := range users.Rows {
		var ele dto.ExternalUser
		ele.Name = user.UserName
		ele.Userid = user.ID.String()
		ele.MainDepartment = user.DepartID
		ele.Mobile = user.Tel
		ele.Email = user.Email
		ele.UniqKey = idp.GetKey(ele)
		ret = append(ret, &ele)
	}
	return ret, nil
}

// 查询部门信息函数
func fetchDepartments(endpoint, login, pass string, page, pageSize int) (*ApiResponse[Dept], error) {
	// 构建请求 URL 和查询参数
	apiUrl := endpoint + departmentsPath // 替换为实际 API URL
	params := url.Values{}
	//params.Add("page", strconv.Itoa(page))
	//params.Add("pagesize", strconv.Itoa(pageSize))
	params.Add("con_type", "1") //1 代表为json结构
	params.Add("login", login)
	params.Add("pass", pass)
	params.Add("tradecode", "departlist") //固定值,代表部门列表
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// 发起 HTTP 请求
	resp, err := client.Get(apiUrl + "?" + params.Encode())
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取并解析响应数据
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var apiResponse ApiResponse[Dept]
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w ; possible reason: 认证错误等. 响应内容%s", err, string(body))
	}
	if apiResponse.Status != "" {
		return nil, fmt.Errorf("failed to fetchDepartments: error msg %s", apiResponse.Info)
	}
	return &apiResponse, nil
}

func fetchAccounts(endpoint, login, pass string, page, pageSize int) (*ApiResponse[Account], error) {
	apiUrl := endpoint + departmentsPath // 替换为实际 API URL
	params := url.Values{}
	params.Add("page", strconv.Itoa(page))
	params.Add("pagesize", strconv.Itoa(pageSize))
	params.Add("con_type", "1") //1 代表为json结构
	params.Add("login", login)
	params.Add("pass", pass)
	params.Add("tradecode", "accountlist") //固定值,代表部门列表
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	// 发起 HTTP 请求
	resp, err := client.Get(apiUrl + "?" + params.Encode())
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取并解析响应数据
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var apiResponse ApiResponse[Account]
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}
	if apiResponse.Status != "" {
		return nil, fmt.Errorf("failed to fetchAccounts: error msg %s", apiResponse.Info)
	}
	return &apiResponse, nil
}
