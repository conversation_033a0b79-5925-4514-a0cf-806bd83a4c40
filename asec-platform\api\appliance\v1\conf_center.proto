syntax = "proto3";

package api.appliance;
option go_package = "asdsec.com/asec/platform/api/appliance/v1;v1";

service ConfCenter{

  // 配置是否更新
  rpc SniffUpdate(SniffUpdateReq) returns (SniffUpdateResp);

  // 拉取配置
  rpc PollConf(PollConfReq) returns (PollConfResp);

  // 同步配置类型
  rpc SyncConfType(SyncConfTypeReq) returns (SyncConfTypeResp);

  // 删除某个配置
  rpc DelConf(DelConfReq) returns (DelConfResp);
}

message DelConfReq{
  string conf_type = 1;
  string conf_biz_id = 2;
}

message DelConfResp{
}

message SyncConfTypeResp{
  repeated string confTypes = 1;
}

message SyncConfTypeReq{

}

message SniffUpdateReq{
  string conf_type = 1;
  uint32 conf_version = 2;
}

message SniffUpdateResp{
  bool update_signal = 1;
  int32 conf_version = 2;
}

message ExistConf{
  string conf_id = 1;
  string conf_md5 = 2;
}

message PollConfReq{
  string appliance_id = 1;
  string user_id = 2;
  string conf_type = 3;
  repeated ExistConf exist_conf = 4;
}

message PollConfResp{
  bool need_update = 1;
  repeated AgentConf add_conf_list = 2 ;
  repeated string del_conf_ids = 3;
  repeated AgentConf update_conf_list = 4 ;
}
enum DataFormat{
  Protobuf = 0;
  Json = 1;
}
message AgentConf{
  string conf_id = 1;
  string conf_type = 2;
  string conf_md5 = 3;
  bytes conf_data = 4;
  DataFormat data_format = 5;
  string conf_text = 6;
}
