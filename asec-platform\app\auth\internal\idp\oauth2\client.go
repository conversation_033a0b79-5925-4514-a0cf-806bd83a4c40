package oauth2

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"asdsec.com/asec/platform/app/auth/internal/dto"
	"asdsec.com/asec/platform/app/auth/internal/idp/webauth"
	"github.com/go-kratos/kratos/v2/log"
)

// OAuth2Client 提供OAuth2认证和用户同步的客户端
type OAuth2Client struct {
	GlobalData string
	CodeData   string
	UserData   string
	LogoutOpen string
	LogoutData string
	OpenType   string
	logger     *log.Helper
}

// NewOAuth2Client 创建一个新的OAuth2客户端
func NewOAuth2Client(globalData, codeData, userData, logoutOpen, logoutData, openType string) *OAuth2Client {
	logger := log.With(log.GetLogger(), "module", "oauth2_client")
	return &OAuth2Client{
		GlobalData: globalData,
		CodeData:   codeData,
		UserData:   userData,
		LogoutOpen: logoutOpen,
		LogoutData: logoutData,
		OpenType:   openType,
		logger:     log.NewHelper(logger),
	}
}

// 部门API响应类型
type ApiResponse[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Total   int  `json:"total"`
		List    []T  `json:"list"`
		HasMore bool `json:"has_more"`
	} `json:"data"`
}

// 部门结构
type Dept struct {
	DepartID      string `json:"departId"`
	DepartName    string `json:"departName"`
	UpID          string `json:"upId"`
	AllDepartName string `json:"allDepartName"`
	OrderIndex    string `json:"orderIndex"`
}

// 用户结构
type Account struct {
	UserID      string `json:"userId"`
	UserName    string `json:"userName"`
	Email       string `json:"email"`
	Mobile      string `json:"mobile"`
	Avatar      string `json:"avatar"`
	DepartID    string `json:"departId"`
	DepartName  string `json:"departName"`
	DisplayName string `json:"displayName"`
	Status      int    `json:"status"`
}

// GetAllDepts 获取所有部门信息
func (c *OAuth2Client) GetAllDepts() ([]*dto.ExternalDepartment, error) {
	c.logger.Infof("开始从OAuth2服务器获取部门数据")

	// 解析全局变量配置
	var globalVars map[string]string
	if err := json.Unmarshal([]byte(c.GlobalData), &globalVars); err != nil {
		c.logger.Errorf("解析全局变量失败: %v", err)
		return nil, fmt.Errorf("解析全局变量失败: %v", err)
	}

	// 创建部门获取步骤
	var steps []webauth.Step
	if err := json.Unmarshal([]byte(c.UserData), &steps); err != nil {
		c.logger.Errorf("解析部门同步步骤失败: %v", err)
		return nil, fmt.Errorf("解析部门同步步骤失败: %v", err)
	}

	// 使用StepExecutor执行多步请求
	executor := webauth.NewStepExecutor(log.With(log.GetLogger()))
	executor.SetGlobalVars(globalVars)
	executor.SetSteps(steps)

	// 执行请求
	if err := executor.Execute(context.Background()); err != nil {
		c.logger.Errorf("执行部门同步请求失败: %v", err)
		return nil, fmt.Errorf("执行部门同步请求失败: %v", err)
	}

	// 获取结果
	userInfo := executor.GetUserInfo()
	c.logger.Infof("OAuth2部门数据获取完成,开始解析")

	// 从结果中提取部门数据
	var depts []*dto.ExternalDepartment

	if deptsList, ok := userInfo["departments"].([]interface{}); ok {
		for _, item := range deptsList {
			if deptMap, ok := item.(map[string]interface{}); ok {
				// 获取排序值，转换为int64
				var orderVal int64
				if orderStr, ok := deptMap["orderIndex"]; ok {
					orderInt, err := strconv.ParseInt(fmt.Sprint(orderStr), 10, 64)
					if err == nil {
						orderVal = orderInt
					}
				}
				dept := &dto.ExternalDepartment{
					ID:       fmt.Sprint(deptMap["departId"]),
					Name:     fmt.Sprint(deptMap["departName"]),
					Parentid: fmt.Sprint(deptMap["upId"]),
					Order:    orderVal,
				}

				// 生成唯一标识
				dept.UniqKey = fmt.Sprintf("%s:%s:%s", dept.ID, dept.Name, dept.Parentid)
				depts = append(depts, dept)
			}
		}
	}

	// 如果没有获取到部门数据，记录警告但不尝试备用方式
	if len(depts) == 0 {
		c.logger.Warnf("从OAuth2服务器未获取到任何部门数据")
	} else {
		c.logger.Infof("成功获取到%d个部门", len(depts))
	}

	return depts, nil
}

// GetAllUsers 获取所有用户信息
func (c *OAuth2Client) GetAllUsers() ([]*dto.ExternalUser, error) {
	c.logger.Infof("开始从OAuth2服务器获取用户数据")

	// 解析全局变量配置
	var globalVars map[string]string
	if err := json.Unmarshal([]byte(c.GlobalData), &globalVars); err != nil {
		c.logger.Errorf("解析全局变量失败: %v", err)
		return nil, fmt.Errorf("解析全局变量失败: %v", err)
	}

	// 创建用户获取步骤
	var steps []webauth.Step
	if err := json.Unmarshal([]byte(c.UserData), &steps); err != nil {
		c.logger.Errorf("解析用户同步步骤失败: %v", err)
		return nil, fmt.Errorf("解析用户同步步骤失败: %v", err)
	}

	// 使用StepExecutor执行多步请求
	executor := webauth.NewStepExecutor(log.With(log.GetLogger()))
	executor.SetGlobalVars(globalVars)
	executor.SetSteps(steps)

	// 执行请求
	if err := executor.Execute(context.Background()); err != nil {
		c.logger.Errorf("执行用户同步请求失败: %v", err)
		return nil, fmt.Errorf("执行用户同步请求失败: %v", err)
	}

	// 获取结果
	userInfo := executor.GetUserInfo()
	c.logger.Infof("OAuth2用户数据获取完成,开始解析")

	// 从结果中提取用户数据
	var users []*dto.ExternalUser

	if usersList, ok := userInfo["users"].([]interface{}); ok {
		for _, item := range usersList {
			if userMap, ok := item.(map[string]interface{}); ok {
				activated := true
				user := &dto.ExternalUser{
					Userid:         fmt.Sprint(userMap["userId"]),
					Name:           fmt.Sprint(userMap["userName"]),
					Mobile:         fmt.Sprint(userMap["mobile"]),
					Email:          fmt.Sprint(userMap["email"]),
					MainDepartment: fmt.Sprint(userMap["departId"]),
					Status: &dto.UserStatus{
						IsActivated: &activated,
					},
				}

				// 如果有状态字段，检查用户是否启用
				if status, ok := userMap["status"]; ok {
					statusVal, _ := strconv.Atoi(fmt.Sprint(status))
					activated = statusVal == 1
				}

				// 生成唯一标识
				user.UniqKey = fmt.Sprintf("%s:%s:%s", user.Userid, user.Name, user.Mobile)
				users = append(users, user)
			}
		}
	}

	// 如果没有获取到用户数据，记录警告但不尝试备用方式
	if len(users) == 0 {
		c.logger.Warnf("从OAuth2服务器未获取到任何用户数据")
	} else {
		c.logger.Infof("成功获取到%d个用户", len(users))
	}

	return users, nil
}
