package api

import (
	"path/filepath"
	"strings"

	"asdsec.com/asec/platform/app/console/common"
	global "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

const (
	IconSavePath = "/opt/asdsec/icon"
)

// UploadIcon godoc
// @Summary 上传应用图标
// @Schemes
// @Description 上传应用图标
// @Tags Application
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "图标文件(PNG/JPG,小于2MB)"
// @Success 200 {object} common.Response{data=string} "返回图标URL"
// @Router /v1/application/upload-icon [post]
func UploadIcon(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		global.SysLog.Error("get upload file failed", zap.Error(err))
		common.Fail(c, common.ParamInvalidError)
		return
	}

	// 校验文件大小(2MB)
	if file.Size > 2*1024*1024 {
		common.FailWithMessage(c, common.OperateFailCode, "文件大小不能超过2MB")
		return
	}

	// 校验文件类型
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
		common.FailWithMessage(c, common.OperateFailCode, "只支持JPG/PNG格式图片")
		return
	}

	// 生成文件名
	fileName := common.GenUUID() + ext
	savePath := filepath.Join(IconSavePath, fileName)

	// 确保目录存在
	if err := common.EnsureDir(IconSavePath); err != nil {
		global.SysLog.Error("create upload dir failed", zap.Error(err))
		common.Fail(c, common.FileUploadError)
		return
	}

	// 保存文件
	if err := c.SaveUploadedFile(file, savePath); err != nil {
		global.SysLog.Error("save upload file failed", zap.Error(err))
		common.Fail(c, common.FileUploadError)
		return
	}

	// 返回可访问的URL
	iconURL := "/static/icon/" + fileName

	common.OkWithData(c, gin.H{
		"icon_url": iconURL,
	})
}
