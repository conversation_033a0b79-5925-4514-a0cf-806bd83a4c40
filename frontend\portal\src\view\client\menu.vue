<template>
  <div class="layout-aside">
    <ul class="menu-wrapper">
      <li
        v-for="item in computedMenu"
        :key="item.code"
        :class="['menu-item', cutOut(item.code) === currentRouteCode ? 'active-menu-item' : '']"
        @click="changeMenu(item.url, item.params, item.code)"
      >
        <svg class="icon menu-item-icon" aria-hidden="true">
          <use :xlink:href="'#'+ item.icon" />
        </svg>
        <div class="menu-item-title">
          {{ item.name }}
        </div>
      </li>
    </ul>
  </div>
</template>
<script>

import menuList from '@/router/menu'
import agentApi from '@/api/agentApi'
// 使用轻量级 SVG 图标，已在 main.js 中全局加载

export default {
  name: 'ClientMenu',
  data() {
    return {
      currentRouteCode: '101'
    }
  },
  computed: {
    computedMenu() {
      return this.computedMenuFun()
    },
  },
  watch: {
    '$route': {
      handler(to, from) {
        logger.log('路由变化', to, from)

        // 特殊处理：如果跳转到登录页面，菜单应该切换到接入菜单
        if (to.name === 'ClientNewLogin') {
          logger.log('跳转到登录页面，菜单切换到接入')
          this.currentRouteCode = '101' // 接入菜单的code
          return
        }

        if (to.meta && to.meta.code) {
          if (!_.get(to.meta, 'code')) {
            return
          }
          if (to.meta.code === this.currentRouteCode) {
            return
          }
          this.currentRouteCode = this.cutOut(to.meta.code)
        }
      },
      immediate: true
    }
  },

  methods: {
    // 计算菜单
    computedMenuFun() {
      const list = []
      if (menuList) {
        menuList.forEach(item => {
          if (item.meta && item.meta.menu) {
            const { name, icon, uiId } = item.meta.menu
            const sublistItem = { name, icon, code: item.meta.code, requiresAuth: item.meta.requiresAuth, url: item.path, params: item.params || [], uiId }
            list.push(sublistItem)
          }
        })
      }
      return list
    },

    // 切换菜单前做逻辑判断
    changeMenu(path, query = {}, code = 0) {
      logger.log('切换菜单:', path, query)

      // 自动带上客户端参数
      const clientParams = agentApi.getClientParams()
      const finalQuery = { ...query, ...clientParams }

      logger.log('切换菜单携带客户端参数:', finalQuery)
      this.$router.push({ path, query: finalQuery })
      this.currentRouteCode = this.cutOut(code)
    },
    routerInterceptor(path) {
      // 入网状态页点击进入认证、来宾认证提示
      const state = {
        next: false,
        stateMsg: '您好，系统正在检测您的网络环境，请稍候......'
      }
      state['next'] = true
      return state
    },
    cutOut(str) {
      if (str && str.length) {
        return str.substr(0, 3)
      }
      return str
    }
  }
}
</script>
<style lang="scss" scoped>
.layout-aside {
  width: 56px;
  height:100%;
  background: $menu-bg;
  overflow: auto;
  z-index: 10;

  .u-offlineTips {
    width: 100%;
    padding: 10px;
    background: #fceded;
    display: flex;
    justify-content: center;

    .off-tip-content {
      display: flex;
      line-height: 20px;
      font-size: 14px;
      color: rgba(230, 83, 83, 1);

      i {
        padding-right: 10px;
        font-size: 14px;
      }
    }
  }

  .menu-wrapper {
    padding-bottom: 60px;
    padding-top: 24px;
    margin: 0px;

    .menu-item {
      width: 48px;
      height: 61px;
      font-size: 13px;
      color: #686e84;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 2px 4px 2px 4px;
      transition: none !important; // 菜单切换禁用所有过渡动画,点击就有反应

      .menu-item-title {
        height: 12px;
        width: 24px;
        font-size: 12px;
        font-family: PingFang SC, PingFang SC-Medium,"Microsoft YaHei","微软雅黑";
        font-weight: Medium;
        transition: none !important; // 强制禁用文字过渡动画
      }

      .menu-item-icon {
        height: 18px;
        width: 18px;
        margin-bottom: 6px;
        fill: currentColor;
        transition: none !important; // 强制禁用图标过渡动画
      }
    }

    .menu-item:hover {
      background: $default-bg;
      color: #536ce6;
      border-radius: 4px; // 增加圆角半径，让背景更圆润
      cursor: pointer;
      transition: none !important; // 强制禁用hover过渡动画

      .iconfont {
        color: $--color-primary;
      }

      .menu-item-title {
        transition: none !important; // 强制禁用文字过渡动画
      }

      .menu-item-icon {
        transition: none !important; // 强制禁用图标过渡动画
      }
    }

    .active-menu-item {
      background: #536ce6;
      border-radius: 4px; // 增加圆角半径，与hover状态保持一致
      color: #ffffff;
      transition: none !important; // 强制禁用active过渡动画

      .menu-item-title {
        transition: none !important; // 强制禁用文字过渡动画
      }

      .menu-item-icon {
        transition: none !important; // 强制禁用图标过渡动画
      }
    }

    .active-menu-item:hover {
      background: #536ce6;
      border-radius: 4px; // 增加圆角半径，与hover状态保持一致
      color: #ffffff;
      transition: none !important; // 强制禁用active-hover过渡动画
    }
  }

  .version-wrapper {
    position: fixed;
    bottom: 1px;
    left: 1px;
    width: 200px;
    background: $menu-bg;
    font-size: 12px;
    line-height: 33px;
    text-align: center;
    color: #B3B6C1;
    z-index: 11;
  }
}</style>
