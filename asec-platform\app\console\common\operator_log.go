package common

const (
	AlertModuleName    = "Alert.ModuleName"
	AlertAddRobot      = "Alert.AddRobot"
	AlertEditRobot     = "Alert.EditRobot"
	AlertDelRobot      = "Alert.DelRobot"
	AlertUpdateSetting = "Alert.UpdateSetting"
)

const OperateUpdate = "UPDATE"
const OperateCreate = "CREATE"
const OperateImport = "IMPORT"
const OperateDownload = "DOWNLOAD"
const OperateDelete = "DELETE"
const OperateEnable = "ENABLE"
const OperateDisable = "DISABLE"
const OperateStop = "STOP"
const OperateUninstall = "UNINSTALL"
const OperateBind = "BIND"
const OperateUnBind = "UNBIND"

const (
	UserResourceType              = "USER"
	GroupResourceType             = "GROUP"
	AppResourceType               = "APPLICATION"
	AppGroupResourceType          = "APPLICATION_GROUP"
	AccessStrategyResourceType    = "ACCESS_STRATEGY"
	ApplianceResourceType         = "APPLIANCE"
	AuthStrategyResourceType      = "AUTH_STRATEGY"
	RoleResourceType              = "ROLE"
	AuthPolicy                    = "AUTHORIZATION_POLICY"
	SensitiveStrategyElemType     = "SENSITIVE_STRATEGY_ELEM"
	SensitiveStrategyElemTagType  = "SENSITIVE_STRATEGY_ELEM_TAG"
	SensitiveStrategyType         = "SENSITIVE_STRATEGY"
	SystemType                    = "SYSTEM"
	AlertRuleResourceType         = "ALERT_RULE"
	AdminResourceType             = "ADMIN_OPT"
	AdminRoleResourceType         = "ADMIN_ROLE_OPT"
	UserRiskConfigType            = "USER_RISK"
	DdrRiskScoreType              = "DDR_RISK_SCORE"
	UebaStrategyType              = "UEBA_STRATEGY"
	EvidenceConfigType            = "EVIDENCE_CONFIG"
	SensitiveCategory             = "SENSITIVE_CATEGORY"
	SslCertificate                = "SSL_CERTIFICATE"
	ProcessNameConfig             = "PROCESS_RENAME_CONFIG"
	Notification                  = "NOTIFICATION"
	DynamicStrategyWatermarkKType = "DYNAMIC_STRATEGY_WATERMARK"
	AlertWatermarkType            = "ALERT_WATERMARK"
	FactorIpTYpe                  = "FACTOR_IP"
	FactorNetLocationTYpe         = "FACTOR_NET_LOCATION"
	FactorProcessTYpe             = "FACTOR_PROCESS"
	FactorTimeTYpe                = "FACTOR_TIME"
	FactorFileTYpe                = "FACTOR_FILE_TYPE"
	FactorNotificationTYpe        = "FACTOR_NOTIFICATION"
	ChannelDefineTYpe             = "CHANNEL_DEFINE"
	ChannelTYpe                   = "CHANNEL_TYPE"
	UnknownChannelAnalysisTYpe    = "UNKNOWN_CHANNEL_ANALYSIS"
	DataSourceTYpe                = "DATA_SOURCE"
	ScanStrategyTYpe              = "SCAN_STRATEGY"
	ScanStrategyStopTYpe          = "SCAN_STRATEGY_STOP"
	ModuleSwitchTYpe              = "MODULE_SWITCH"
	AgentsSysTYpe                 = "AGENTS_SYS"
	PlatformUpgradeTYpe           = "PLAT_FORM_UPGRADE"
	TerminalManagementTYpe        = "TERMINAL_MANAGEMENT"
	UserGroupType                 = "USERGROUP"
	RoleMangerType                = "ROLEMANGER"
	AuthPolicyType                = "AUTHPOLICY"
	IdpType                       = "IDP"
	VirtualIPResourceType         = "VIRTUAL_IP"
	VirtualIPPoolResourceType     = "VIRTUAL_IP_POOL"
	VirtualIPSettingsResourceType = "VIRTUAL_IP_SETTINGS"
	SPAConfigType                 = "SPA_CONFIG"
	SessionResourceType           = "SESSION"
	IPResourceType                = "IP"
)

const SystemResourceType = "SYSTEM"
