package logbeat

const fluentBitTemplate = `
[SERVICE]
    flush        {{ .Flush }}
    daemon       Off
    log_level    {{ .LogLevel }}
    parsers_file {{ .ParsersFile }}
    plugins_file {{ .PluginsFile }}

{{ range .Inputs }}
[INPUT]
    Name         tail
    Path         {{ .Path }}
    Parser       {{ .Parser }}
    Tag          {{ .Tag }}

{{ end }}
{{ range .Outputs }}
[OUTPUT]
    Name         loki
    Host         {{ .Host }}
    Port         {{ .Port }}
    Match        {{ .Match }}
    Labels       job={{ .Labels.Job }}, mystream={{ .Labels.Stream }}
{{ range $key, $value := .Labels.Extra }}
    {{ $key }}={{ $value }}
{{ end }}

{{ end }}
`

// FluentBitConfig 定义 Fluent Bit 配置文件的结构体
type FluentBitConfig struct {
	Flush       int
	LogLevel    string
	ParsersFile string
	PluginsFile string
	Inputs      []InputConfig
	Outputs     []OutputConfig
}

type InputConfig struct {
	Path   string
	Parser string
	Tag    string
}

type OutputConfig struct {
	Host   string
	Port   int
	Match  string
	Labels struct {
		Job    string
		Stream string
		Extra  map[string]string
	}
}
