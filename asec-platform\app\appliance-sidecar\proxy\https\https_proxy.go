package https

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/pkg/utils"
	"github.com/go-ini/ini"
)

const (
	defaultPort      = 28080
	maxPortRetries   = 3
	localhostAddress = "127.0.0.1"
)

// StartReverseProxy 启动一个健壮的HTTP反向代理
// 该代理将本地的HTTP请求转换为uTLS/SPA请求发送到平台
func StartReverseProxy() {
	targetHost := getTargetHostFromConfig()
	if targetHost == "" {
		log.Fatal("[反向代理] 目标地址未配置")
	}

	targetURL := "https://" + targetHost
	remote, err := url.Parse(targetURL)
	if err != nil {
		log.Fatalf("[反向代理] 无法解析目标URL: %v", err)
	}

	proxy := httputil.NewSingleHostReverseProxy(remote)

	// 自定义Director来修改HOST头为真实的目标地址
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		// 修改HOST头为目标服务器的地址，而不是127.0.0.1:28080
		req.Host = remote.Host
		req.Header.Set("Host", remote.Host)
		log.Printf("[反向代理] 修改HOST头: %s -> %s", req.Header.Get("Host"), remote.Host)
	}

	// 使用我们自定义的 uTLS/SPA RoundTripper
	requester := connection.GetPlatformRequester("https")
	if requester == nil {
		log.Fatalf("[反向代理] 无法获取平台请求器")
	}
	proxy.Transport = requester

	// 自定义错误处理器
	proxy.ErrorHandler = func(rw http.ResponseWriter, req *http.Request, err error) {
		log.Printf("[反向代理] 错误: %v", err)
		rw.WriteHeader(http.StatusBadGateway)
	}

	// 获取配置文件路径
	commonConfigPath := filepath.Join(utils.GetConfigDir(), "config", "common_config.ini")

	// 读取旧端口
	var oldPort int
	cfg, err := ini.Load(commonConfigPath)
	if err == nil {
		oldPort = cfg.Section("common").Key("reverse_proxy_port").MustInt(0)
	}

	// 动态端口尝试
	var listener net.Listener
	var newPort int
	for i := 0; i <= maxPortRetries; i++ {
		port := defaultPort + i
		addr := fmt.Sprintf("%s:%d", localhostAddress, port)
		ln, err := net.Listen("tcp", addr)
		if err == nil {
			listener = ln
			newPort = port
			break
		}
		log.Printf("[反向代理] 端口 %d 监听失败: %v. 正在尝试下一个...", port, err)
	}

	if listener == nil {
		log.Fatalf("[反向代理] 无法在 %d-%d 范围内找到可用端口", defaultPort, defaultPort+maxPortRetries)
	}

	// 如果端口有变化，则写入配置文件
	if newPort != oldPort {
		// 确保配置文件目录存在
		configDir := filepath.Dir(commonConfigPath)
		if err := os.MkdirAll(configDir, 0755); err != nil {
			log.Printf("[反向代理] 创建配置目录失败: %v", err)
		} else {
			if cfg == nil {
				cfg = ini.Empty()
			}
			cfg.Section("common").Key("reverse_proxy_port").SetValue(fmt.Sprintf("%d", newPort))
			if err := cfg.SaveTo(commonConfigPath); err != nil {
				log.Printf("[反向代理] 写入配置文件失败: %v", err)
			} else {
				log.Printf("[反向代理] 代理端口已更新为 %d 并写入配置文件", newPort)
			}
		}
	}

	// 创建一个可配置的服务器
	server := &http.Server{
		Handler:      panicRecovery(handler(proxy)),
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  15 * time.Second,
	}

	// 优雅地关闭
	go func() {
		listenAddr := fmt.Sprintf("%s:%d", localhostAddress, newPort)
		log.Printf("[反向代理] 监听在 %s, 转发到 %s", listenAddr, targetURL)
		if err := server.Serve(listener); err != nil && err != http.ErrServerClosed {
			log.Fatalf("[反向代理] 启动失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("[反向代理] 正在关闭...")

	// 创建一个5秒超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 优雅地关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("[反向代理] 关闭失败: %v", err)
	}
	log.Println("[反向代理] 已关闭.")
}

// getTargetHostFromConfig 从配置中获取目标主机地址
// 优先级：前端配置文件 > 命令行参数 > 配置文件
func getTargetHostFromConfig() string {
	// 1. 尝试从前端配置文件读取服务器地址
	if serverUrl := getServerUrlFromFrontendConfig(); serverUrl != "" {
		log.Printf("[反向代理] 使用前端配置的服务器地址: %s", serverUrl)
		return serverUrl
	}

	// 2. 使用命令行参数
	if global.PrivateHost != "" {
		log.Printf("[反向代理] 使用命令行参数的服务器地址: %s", global.PrivateHost)
		return global.PrivateHost
	}

	// 3. 使用配置文件
	if global.Conf.Endpoints.PrivateHost != "" {
		log.Printf("[反向代理] 使用配置文件的服务器地址: %s", global.Conf.Endpoints.PrivateHost)
		return global.Conf.Endpoints.PrivateHost
	}

	return ""
}

// getServerUrlFromFrontendConfig 从前端配置文件中读取服务器地址
func getServerUrlFromFrontendConfig() string {
	// 前端配置文件路径（相对于可执行文件）
	settingPath := filepath.Join(".", "Setting.json")

	// 检查文件是否存在
	if _, err := os.Stat(settingPath); os.IsNotExist(err) {
		return ""
	}

	// 读取文件内容
	data, err := ioutil.ReadFile(settingPath)
	if err != nil {
		log.Printf("[反向代理] 读取前端配置文件失败: %v", err)
		return ""
	}

	// 解析JSON
	var config map[string]interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		log.Printf("[反向代理] 解析前端配置文件失败: %v", err)
		return ""
	}

	// 获取ServerUrl字段
	if serverUrl, ok := config["ServerUrl"].(string); ok && serverUrl != "" {
		// 移除协议前缀，只保留主机和端口
		if strings.HasPrefix(serverUrl, "http://") {
			serverUrl = strings.TrimPrefix(serverUrl, "http://")
		} else if strings.HasPrefix(serverUrl, "https://") {
			serverUrl = strings.TrimPrefix(serverUrl, "https://")
		}
		return serverUrl
	}

	return ""
}

// handler 记录请求日志
func handler(p *httputil.ReverseProxy) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Printf("[反向代理] %s %s", r.Method, r.URL.String())
		p.ServeHTTP(w, r)
	})
}

// panicRecovery 是一个中间件，用于恢复panic
func panicRecovery(h http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("[反向代理] Panic恢复: %v", err)
				http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			}
		}()
		h.ServeHTTP(w, r)
	})
}
