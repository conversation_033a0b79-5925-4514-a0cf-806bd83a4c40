package data

import (
	"context"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"github.com/go-kratos/kratos/v2/log"
)

type authAccountPolicyRepo struct {
	data *Data
	log  *log.Helper
}

// NewAuthAccountPolicyRepo 创建账户认证策略仓库
func NewAuthAccountPolicyRepo(data *Data, logger log.Logger) biz.AuthAccountPolicyRepo {
	return &authAccountPolicyRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *authAccountPolicyRepo) CreateAccountPolicy(ctx context.Context, policy *model.TbAuthAccountPolicy) error {
	return r.data.db.WithContext(ctx).Create(policy).Error
}

func (r *authAccountPolicyRepo) UpdateAccountPolicy(ctx context.Context, policy *model.TbAuthAccountPolicy) error {
	return r.data.db.WithContext(ctx).Where("id = ? AND corp_id = ?", policy.ID, policy.CorpID).Updates(policy).Error
}

func (r *authAccountPolicyRepo) DeleteAccountPolicy(ctx context.Context, corpID, id string) error {
	return r.data.db.WithContext(ctx).Where("id = ? AND corp_id = ?", id, corpID).Delete(&model.TbAuthAccountPolicy{}).Error
}

func (r *authAccountPolicyRepo) GetAccountPolicy(ctx context.Context, corpID, id string) (*model.TbAuthAccountPolicy, error) {
	var policy model.TbAuthAccountPolicy
	err := r.data.db.WithContext(ctx).Where("id = ? AND corp_id = ? AND enable = true", id, corpID).First(&policy).Error
	if err != nil {
		return nil, err
	}
	return &policy, nil
}

func (r *authAccountPolicyRepo) GetDefaultAccountPolicy(ctx context.Context, corpID string) (*model.TbAuthAccountPolicy, error) {
	var policy model.TbAuthAccountPolicy
	err := r.data.db.WithContext(ctx).Where(
		"corp_id = ? AND enable = true AND is_default = true",
		corpID,
	).Order("priority DESC").First(&policy).Error
	if err != nil {
		return nil, err
	}
	return &policy, nil
}

func (r *authAccountPolicyRepo) GetAccountPolicyByPriority(ctx context.Context, corpID string) (*model.TbAuthAccountPolicy, error) {
	var policy model.TbAuthAccountPolicy
	err := r.data.db.WithContext(ctx).Where(
		"corp_id = ? AND enable = true",
		corpID,
	).Order("priority DESC, created_at DESC").First(&policy).Error
	if err != nil {
		return nil, err
	}
	return &policy, nil
}

func (r *authAccountPolicyRepo) ListAccountPolicies(ctx context.Context, corpID string) ([]*model.TbAuthAccountPolicy, error) {
	var policies []*model.TbAuthAccountPolicy
	err := r.data.db.WithContext(ctx).Where(
		"corp_id = ? AND enable = true",
		corpID,
	).Order("priority DESC, created_at DESC").Find(&policies).Error
	return policies, err
}

func (r *authAccountPolicyRepo) GetPasswordLockoutPolicy(ctx context.Context, corpID string) (*dto.AccountPolicyPasswordLockout, error) {
	// 获取优先级最高的账户策略
	policy, err := r.GetAccountPolicyByPriority(ctx, corpID)
	if err != nil {
		return nil, err
	}

	// 如果未启用密码失败限制，返回nil
	if !policy.PasswordFailureEnabled {
		return nil, nil
	}

	return &dto.AccountPolicyPasswordLockout{
		Enabled:               policy.PasswordFailureEnabled,
		MaxFailureCount:       policy.PasswordMaxFailureCount,
		LockoutDurationSec:    policy.PasswordLockoutDurationSec,
		ResetFailureWindowSec: policy.PasswordResetFailureWindowSec,
	}, nil
}

func (r *authAccountPolicyRepo) GetIPLockoutPolicy(ctx context.Context, corpID string) (*dto.AccountPolicyIPLockout, error) {
	// 获取优先级最高的账户策略
	policy, err := r.GetAccountPolicyByPriority(ctx, corpID)
	if err != nil {
		return nil, err
	}

	// 如果未启用IP失败限制，返回nil
	if !policy.IPFailureEnabled {
		return nil, nil
	}

	return &dto.AccountPolicyIPLockout{
		Enabled:               policy.IPFailureEnabled,
		MaxFailureCount:       policy.IPMaxFailureCount,
		LockoutDurationSec:    policy.IPLockoutDurationSec,
		ResetFailureWindowSec: policy.IPResetFailureWindowSec,
	}, nil
}
