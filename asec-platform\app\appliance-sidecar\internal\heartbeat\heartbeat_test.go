package heartbeat

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"time"
)

func Test_getHeartbeatPacket(t *testing.T) {
	type args struct {
		now  time.Time
		pids []int32
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test",
			args: args{
				now:  time.Now(),
				pids: []int32{int32(os.Getpid())},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rec := GetHeartbeatPacket(tt.args.now, tt.args.pids)
			ast := assert.New(t)
			ast.NotNil(rec.SysCpu)
			ast.NotNil(rec.SysMem)
			fmt.Printf("%v", rec)
		})
	}
}
