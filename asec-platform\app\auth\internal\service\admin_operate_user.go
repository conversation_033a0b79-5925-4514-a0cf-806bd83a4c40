package service

import (
	pb "asdsec.com/asec/platform/api/auth/v1/admin"
	"asdsec.com/asec/platform/app/auth/internal/common"
	"asdsec.com/asec/platform/app/auth/internal/dto"
	"context"
	"github.com/jinzhu/copier"
	"strconv"
	"time"
)

func (s *AdminService) CreateUser(ctx context.Context, req *pb.CreateUserRequest) (*pb.CreateUserReply, error) {
	// todo ExpireType 需要做枚举值校验
	var param dto.CreateUserParam
	err := copier.Copy(&param, req)
	if err != nil {
		return &pb.CreateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateUserReply{Status: pb.StatusCode_FAILED}, err
	}

	param.CorpId = corpId
	param.Enable = req.Enable
	err = s.user.CreateUser(ctx, param)
	if err != nil {
		return &pb.CreateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateUserReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) CreateUserCustom(ctx context.Context, req *pb.CreateUserRequest) (*pb.CreateUserReply, error) {
	// todo ExpireType 需要做枚举值校验
	var param dto.CreateUserParam
	err := copier.Copy(&param, req)
	if err != nil {
		return &pb.CreateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.CreateUserReply{Status: pb.StatusCode_FAILED}, err
	}

	param.CorpId = corpId
	param.Enable = req.Enable
	err = s.user.CreateUserCustom(ctx, param)
	if err != nil {
		return &pb.CreateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.CreateUserReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) IdleAccountList(ctx context.Context, req *pb.IdleAccountRequest) (*pb.IdleAccountReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.IdleAccountReply{}, err
	}
	limit, offset := req.Limit, req.Offset
	if offset < 0 {
		offset = dto.DefaultOffset
	}
	if limit <= 0 {
		limit = dto.DefaultLimit
	}
	now := time.Now()
	resp, err := s.user.ListIdleUser(ctx, corpId, int(limit), int(offset), req.Search)
	if err != nil {
		return &pb.IdleAccountReply{Users: nil}, err
	}
	var result []*pb.UserEntity
	for _, u := range resp.Users {
		var roleInfo []*pb.RoleInfo
		for _, r := range u.Roles {
			roleInfo = append(roleInfo, &pb.RoleInfo{
				Id:   r.ID,
				Name: r.Name,
			})
		}
		var temp pb.UserEntity
		if err := copier.Copy(&temp, &u); err != nil {
			return &pb.IdleAccountReply{}, err
		}
		temp.Id = u.ID
		temp.GroupId = u.GroupID
		var baseTime time.Time
		if u.ActiveTime.IsZero() {
			baseTime = u.CreatedAt
		} else {
			baseTime = u.ActiveTime
		}
		duration := now.Sub(baseTime)
		days := int(duration.Hours() / 24) // 换算成天数，每24小时计为一天
		temp.IdleDay = strconv.Itoa(days)
		temp.ActiveTime = u.ActiveTime.Local().Format(dto.CommonTimeFormat)
		temp.SourceId = u.SourceID
		temp.Roles = roleInfo
		temp.LockStatus = u.LockStatus
		temp.Identifier = u.Identify
		temp.AuthType = u.AuthType
		if !u.ExpireEnd.IsZero() && !(u.ExpireEnd.Local().Format(dto.CommonTimeFormat) == dto.TimeZero) {
			temp.ExpireEnd = u.ExpireEnd.Local().Format(dto.CommonTimeFormat)
		}
		result = append(result, &temp)
	}
	return &pb.IdleAccountReply{Users: result, Count: uint32(resp.Count), Time: resp.IdleTime}, nil
}
func (s *AdminService) UpdateIdleTime(ctx context.Context, req *pb.UpdateIdleTimeRequest) (*pb.UpdateIdleTimeReply, error) {
	err := s.user.UpdateIdleTime(ctx, req.Time)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateIdleTimeReply{Time: req.Time}, nil
}

func (s *AdminService) UpdateLockStatus(ctx context.Context, req *pb.UpdateLockStatusRequest) (*pb.UpdateLockStatusReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateLockStatusReply{}, err
	}
	err = s.user.UpdateLockStatus(ctx, corpId, req.Uid, req.Status)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateLockStatusReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) ListUser(ctx context.Context, req *pb.ListUserRequest) (*pb.ListUserReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.ListUserReply{}, err
	}

	groupId := dto.FakeRootUserGroupId
	if req.GroupId != "" {
		groupId = req.GroupId
	}
	limit, offset := req.Limit, req.Offset
	if offset < 0 {
		offset = dto.DefaultOffset
	}
	if limit <= 0 {
		limit = dto.DefaultLimit
	}

	resp, err := s.user.ListUser(ctx, corpId, groupId, int(limit), int(offset), req.Search)
	var result []*pb.UserEntity
	for _, u := range resp.Users {
		var roleInfo []*pb.RoleInfo
		for _, r := range u.Roles {
			roleInfo = append(roleInfo, &pb.RoleInfo{
				Id:   r.ID,
				Name: r.Name,
			})
		}
		var temp pb.UserEntity
		if err := copier.Copy(&temp, &u); err != nil {
			return &pb.ListUserReply{}, err
		}
		temp.Id = u.ID
		temp.GroupId = u.GroupID
		temp.SourceId = u.SourceID
		temp.Roles = roleInfo
		temp.Identifier = u.Identify
		temp.AuthType = u.AuthType
		if !u.ExpireEnd.IsZero() && !(u.ExpireEnd.Local().Format(dto.CommonTimeFormat) == dto.TimeZero) {
			temp.ExpireEnd = u.ExpireEnd.Local().Format(dto.DefaultDay)
		}
		result = append(result, &temp)
	}
	if err != nil {
		return &pb.ListUserReply{Users: nil}, err
	}
	return &pb.ListUserReply{Users: result, Count: uint32(resp.Count)}, nil
}

func (s *AdminService) GetUserCount(ctx context.Context, req *pb.GetUserCountRequest) (*pb.GetUserCountReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.GetUserCountReply{}, err
	}
	userCount, err := s.auth.GetUserCount(ctx, corpId)
	if err != nil {
		return &pb.GetUserCountReply{}, err
	}
	return &pb.GetUserCountReply{
		TotalCount:   userCount.TotalCount,
		OnlineCount:  userCount.OnlineCount,
		OfflineCount: userCount.OfflineCount,
	}, nil
}

func (s *AdminService) DeleteUser(ctx context.Context, req *pb.DeleteUserRequest) (*pb.DeleteUserReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.DeleteUserReply{Status: pb.StatusCode_FAILED}, err
	}
	if err := s.user.DeleteUser(ctx, corpId, req.Id, req.Name); err != nil {
		return &pb.DeleteUserReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.DeleteUserReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) DeleteUserCustom(ctx context.Context, req *pb.CustomDeleteUserRequest) (*pb.DeleteUserReply, error) {
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.DeleteUserReply{Status: pb.StatusCode_FAILED}, err
	}
	if err := s.user.DeleteUserCustom(ctx, corpId, req.Name); err != nil {
		return &pb.DeleteUserReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.DeleteUserReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) UpdateUser(ctx context.Context, req *pb.UpdateUserRequest) (*pb.UpdateUserReply, error) {
	// todo ExpireType 需要做枚举值校验
	var param dto.UpdateUserParam
	err := copier.Copy(&param, req)
	if err != nil {
		return &pb.UpdateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId
	err = s.user.UpdateUser(ctx, param)
	if err != nil {
		return &pb.UpdateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateUserReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) UpdateUserCustom(ctx context.Context, req *pb.CustomUpdateUserRequest) (*pb.UpdateUserReply, error) {
	// todo ExpireType 需要做枚举值校验
	var param dto.UpdateUserParam
	err := copier.Copy(&param, req)
	if err != nil {
		return &pb.UpdateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	corpId, err := common.GetCorpId(ctx)
	if err != nil {
		return &pb.UpdateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	param.CorpId = corpId
	err = s.user.UpdateUserCustom(ctx, param)
	if err != nil {
		return &pb.UpdateUserReply{Status: pb.StatusCode_FAILED}, err
	}
	return &pb.UpdateUserReply{Status: pb.StatusCode_SUCCESS}, nil
}

func (s *AdminService) GetFieldOptions(ctx context.Context, req *pb.GetFieldOptionsRequest) (*pb.GetFieldOptionsReply, error) {
	var result []*pb.KV
	if err := copier.Copy(&result, &dto.FieldOptions); err != nil {
		return &pb.GetFieldOptionsReply{}, err
	}
	return &pb.GetFieldOptionsReply{Options: result}, nil
}

func (s *AdminService) GetFieldMap(ctx context.Context, req *pb.GetFieldMapRequest) (*pb.GetFieldMapReply, error) {
	fieldMap, err := s.user.GetFieldMap(ctx, dto.UserSourceType(req.SrcType), dto.FieldMapType(req.FieldMapType))
	if err != nil {
		return &pb.GetFieldMapReply{}, err
	}
	var result []*pb.KV
	if err := copier.Copy(&result, &fieldMap); err != nil {
		return &pb.GetFieldMapReply{}, err
	}
	return &pb.GetFieldMapReply{FieldMap: result}, nil
}

func (s *AdminService) TotpUnbind(ctx context.Context, req *pb.TotpUnbindRequest) (*pb.TotpUnbindTimeReply, error) {
	err := s.user.TotpUnbind(ctx, req.Id)
	if err != nil {
		return &pb.TotpUnbindTimeReply{}, err
	}
	return &pb.TotpUnbindTimeReply{Status: 0}, nil
}
