#!/bin/bash
# etcd 环境设置脚本
# 用于配置 etcd 容器的运行环境，包括软件安装、用户设置、权限配置等
set -e

echo "正在设置 etcd 环境..."

# 配置阿里云软件源，提高下载速度
echo "正在更新软件包源..."
echo "deb http://mirrors.aliyun.com/debian-archive/debian/ buster main" > /etc/apt/sources.list
echo "deb http://mirrors.aliyun.com/debian-archive/debian/ buster-updates main" >> /etc/apt/sources.list
echo "deb http://mirrors.aliyun.com/debian-archive/debian-security/ buster/updates main" >> /etc/apt/sources.list

echo "正在安装必要的软件包..."
apt-get update
apt-get install -y --no-install-recommends cron gosu
rm -rf /var/lib/apt/lists/*  # 清理缓存，减小镜像大小

# 确保 etcd 用户存在（用户ID: 1001）
echo "正在确保 etcd 用户存在..."
if ! getent passwd 1001 > /dev/null 2>&1; then
    echo "etcd:x:1001:1001:etcd user:/bitnami/etcd:/bin/bash" >> /etc/passwd
    echo "已创建 etcd 用户"
fi

echo "正在修复脚本文件换行符..."
sed -i 's/\r$//' /defrag.sh
sed -i 's/\r$//' /start-etcd-with-defrag.sh

# 设置脚本执行权限
echo "正在设置脚本权限..."
chmod +x /defrag.sh
chmod +x /start-etcd-with-defrag.sh

# 创建 etcd 数据目录并设置正确的权限
# 确保 etcd 用户有权限读写数据目录
echo "正在设置 etcd 数据目录..."
mkdir -p /bitnami/etcd/data
chown -R 1001:1001 /bitnami/etcd
chmod -R 700 /bitnami/etcd/data

echo "etcd 环境设置完成！"
