/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(t,n,i){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i,t}System.register(["./index-legacy.b871e767.js"],(function(e,t){"use strict";var i,r,o,a,u,c,d,m,p,l=document.createElement("style");return l.textContent='@charset "UTF-8";.layout-aside[data-v-eea213d2]{width:56px;height:100%;background:#F5F5F7;overflow:auto;z-index:10}.layout-aside .u-offlineTips[data-v-eea213d2]{width:100%;padding:10px;background:#fceded;display:flex;justify-content:center}.layout-aside .u-offlineTips .off-tip-content[data-v-eea213d2]{display:flex;line-height:20px;font-size:14px;color:#e65353}.layout-aside .u-offlineTips .off-tip-content i[data-v-eea213d2]{padding-right:10px;font-size:14px}.layout-aside .menu-wrapper[data-v-eea213d2]{padding-bottom:60px;padding-top:24px;margin:0}.layout-aside .menu-wrapper .menu-item[data-v-eea213d2]{width:48px;height:61px;font-size:13px;color:#686e84;font-weight:400;display:flex;flex-direction:column;align-items:center;justify-content:center;margin:2px 4px;transition:none!important}.layout-aside .menu-wrapper .menu-item .menu-item-title[data-v-eea213d2]{height:12px;width:24px;font-size:12px;font-family:PingFang SC,PingFang SC-Medium,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:Medium;transition:none!important}.layout-aside .menu-wrapper .menu-item .menu-item-icon[data-v-eea213d2]{height:18px;width:18px;margin-bottom:6px;fill:currentColor;transition:none!important}.layout-aside .menu-wrapper .menu-item[data-v-eea213d2]:hover{background:#EBEBED;color:#536ce6;border-radius:4px;cursor:pointer;transition:none!important}.layout-aside .menu-wrapper .menu-item:hover .iconfont[data-v-eea213d2]{color:#536ce6}.layout-aside .menu-wrapper .menu-item:hover .menu-item-title[data-v-eea213d2],.layout-aside .menu-wrapper .menu-item:hover .menu-item-icon[data-v-eea213d2]{transition:none!important}.layout-aside .menu-wrapper .active-menu-item[data-v-eea213d2]{background:#536ce6;border-radius:4px;color:#fff;transition:none!important}.layout-aside .menu-wrapper .active-menu-item .menu-item-title[data-v-eea213d2],.layout-aside .menu-wrapper .active-menu-item .menu-item-icon[data-v-eea213d2]{transition:none!important}.layout-aside .menu-wrapper .active-menu-item[data-v-eea213d2]:hover{background:#536ce6;border-radius:4px;color:#fff;transition:none!important}.layout-aside .version-wrapper[data-v-eea213d2]{position:fixed;bottom:1px;left:1px;width:200px;background:#F5F5F7;font-size:12px;line-height:33px;text-align:center;color:#b3b6c1;z-index:11}\n',document.head.appendChild(l),{setters:[function(e){i=e._,r=e.C,o=e.a,a=e.b,u=e.d,c=e.F,d=e.A,m=e.e,p=e.t}],execute:function(){var t=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],l={name:"ClientMenu",data:function(){return{currentRouteCode:"101"}},computed:{computedMenu:function(){return this.computedMenuFun()}},watch:{$route:{handler:function(e,t){if(logger.log("路由变化",e,t),"ClientNewLogin"===e.name)return logger.log("跳转到登录页面，菜单切换到接入"),void(this.currentRouteCode="101");if(e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun:function(){var e=[];return t&&t.forEach((function(t){if(t.meta&&t.meta.menu){var n=t.meta.menu,i=n.name,r=n.icon,o=n.uiId,a={name:i,icon:r,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:o};e.push(a)}})),e},changeMenu:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;logger.log("切换菜单:",e,t);var o=r.getClientParams(),a=n(n({},t),o);logger.log("切换菜单携带客户端参数:",a),this.$router.push({path:e,query:a}),this.currentRouteCode=this.cutOut(i)},routerInterceptor:function(e){var t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:function(e){return e&&e.length?e.substr(0,3):e}}},s={class:"layout-aside"},f={class:"menu-wrapper"},g=["onClick"],h={class:"icon menu-item-icon","aria-hidden":"true"},y=["xlink:href"],v={class:"menu-item-title"};e("default",i(l,[["render",function(e,t,n,i,r,l){return o(),a("div",s,[u("ul",f,[(o(!0),a(c,null,d(l.computedMenu,(function(e){return o(),a("li",{key:e.code,class:m(["menu-item",l.cutOut(e.code)===r.currentRouteCode?"active-menu-item":""]),onClick:function(t){return l.changeMenu(e.url,e.params,e.code)}},[(o(),a("svg",h,[u("use",{"xlink:href":"#"+e.icon},null,8,y)])),u("div",v,p(e.name),1)],10,g)})),128))])])}],["__scopeId","data-v-eea213d2"]]))}}}))}();
