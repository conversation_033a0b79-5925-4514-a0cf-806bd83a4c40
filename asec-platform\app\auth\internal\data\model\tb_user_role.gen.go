// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTbUserRole = "tb_user_role"

// TbUserRole mapped from table <tb_user_role>
type TbUserRole struct {
	UserID    string    `gorm:"column:user_id;not null" json:"user_id"`
	RoleID    string    `gorm:"column:role_id;not null" json:"role_id"`
	CorpID    string    `gorm:"column:corp_id" json:"corp_id"`
	CreatedAt time.Time `gorm:"column:created_at;not null;default:now()" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:now()" json:"updated_at"`
}

// TableName TbUserRole's table name
func (*TbUserRole) TableName() string {
	return TableNameTbUserRole
}
