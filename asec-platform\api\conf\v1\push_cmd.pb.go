// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.20.1
// source: conf/v1/push_cmd.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PushType int32

const (
	PushType_UnknownType PushType = 0
	PushType_UploadLog   PushType = 1
)

// Enum value maps for PushType.
var (
	PushType_name = map[int32]string{
		0: "UnknownType",
		1: "UploadLog",
	}
	PushType_value = map[string]int32{
		"UnknownType": 0,
		"UploadLog":   1,
	}
)

func (x PushType) Enum() *PushType {
	p := new(PushType)
	*p = x
	return p
}

func (x PushType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PushType) Descriptor() protoreflect.EnumDescriptor {
	return file_conf_v1_push_cmd_proto_enumTypes[0].Descriptor()
}

func (PushType) Type() protoreflect.EnumType {
	return &file_conf_v1_push_cmd_proto_enumTypes[0]
}

func (x PushType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PushType.Descriptor instead.
func (PushType) EnumDescriptor() ([]byte, []int) {
	return file_conf_v1_push_cmd_proto_rawDescGZIP(), []int{0}
}

type PushCmd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PushType PushType `protobuf:"varint,1,opt,name=push_type,json=pushType,proto3,enum=api.conf.PushType" json:"push_type,omitempty"`
	CmdData  []byte   `protobuf:"bytes,2,opt,name=cmd_data,json=cmdData,proto3" json:"cmd_data,omitempty"`
}

func (x *PushCmd) Reset() {
	*x = PushCmd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_v1_push_cmd_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushCmd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushCmd) ProtoMessage() {}

func (x *PushCmd) ProtoReflect() protoreflect.Message {
	mi := &file_conf_v1_push_cmd_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushCmd.ProtoReflect.Descriptor instead.
func (*PushCmd) Descriptor() ([]byte, []int) {
	return file_conf_v1_push_cmd_proto_rawDescGZIP(), []int{0}
}

func (x *PushCmd) GetPushType() PushType {
	if x != nil {
		return x.PushType
	}
	return PushType_UnknownType
}

func (x *PushCmd) GetCmdData() []byte {
	if x != nil {
		return x.CmdData
	}
	return nil
}

var File_conf_v1_push_cmd_proto protoreflect.FileDescriptor

var file_conf_v1_push_cmd_proto_rawDesc = []byte{
	0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x63,
	0x6d, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x22, 0x55, 0x0a, 0x07, 0x50, 0x75, 0x73, 0x68, 0x43, 0x6d, 0x64, 0x12, 0x2f, 0x0a,
	0x09, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x50, 0x75, 0x73, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x75, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x63, 0x6d, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x63, 0x6d, 0x64, 0x44, 0x61, 0x74, 0x61, 0x2a, 0x2a, 0x0a, 0x08, 0x50, 0x75, 0x73,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x4c, 0x6f, 0x67, 0x10, 0x01, 0x42, 0x29, 0x5a, 0x27, 0x61, 0x73, 0x64, 0x73, 0x65, 0x63, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x73, 0x65, 0x63, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_v1_push_cmd_proto_rawDescOnce sync.Once
	file_conf_v1_push_cmd_proto_rawDescData = file_conf_v1_push_cmd_proto_rawDesc
)

func file_conf_v1_push_cmd_proto_rawDescGZIP() []byte {
	file_conf_v1_push_cmd_proto_rawDescOnce.Do(func() {
		file_conf_v1_push_cmd_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_v1_push_cmd_proto_rawDescData)
	})
	return file_conf_v1_push_cmd_proto_rawDescData
}

var file_conf_v1_push_cmd_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_conf_v1_push_cmd_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_conf_v1_push_cmd_proto_goTypes = []interface{}{
	(PushType)(0),   // 0: api.conf.PushType
	(*PushCmd)(nil), // 1: api.conf.PushCmd
}
var file_conf_v1_push_cmd_proto_depIdxs = []int32{
	0, // 0: api.conf.PushCmd.push_type:type_name -> api.conf.PushType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_conf_v1_push_cmd_proto_init() }
func file_conf_v1_push_cmd_proto_init() {
	if File_conf_v1_push_cmd_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_v1_push_cmd_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushCmd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_v1_push_cmd_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_v1_push_cmd_proto_goTypes,
		DependencyIndexes: file_conf_v1_push_cmd_proto_depIdxs,
		EnumInfos:         file_conf_v1_push_cmd_proto_enumTypes,
		MessageInfos:      file_conf_v1_push_cmd_proto_msgTypes,
	}.Build()
	File_conf_v1_push_cmd_proto = out.File
	file_conf_v1_push_cmd_proto_rawDesc = nil
	file_conf_v1_push_cmd_proto_goTypes = nil
	file_conf_v1_push_cmd_proto_depIdxs = nil
}
