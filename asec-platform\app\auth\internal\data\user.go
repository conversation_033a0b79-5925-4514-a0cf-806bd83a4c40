package data

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	modelTable "asdsec.com/asec/platform/pkg/model"
	"gorm.io/gorm/clause"

	"asdsec.com/asec/platform/app/auth/internal/common"
	"github.com/google/uuid"

	"github.com/jinzhu/copier"

	pb "asdsec.com/asec/platform/api/auth/v1"

	"asdsec.com/asec/platform/app/auth/internal/dto"

	"gorm.io/gorm"

	"asdsec.com/asec/platform/app/auth/internal/biz"
	"asdsec.com/asec/platform/app/auth/internal/data/model"
	"asdsec.com/asec/platform/app/auth/internal/data/query"
	"github.com/go-kratos/kratos/v2/log"
)

type userRepo struct {
	data *Data
	log  *log.Helper
}

func (u userRepo) UpdateLockStatus(ctx context.Context, corpId, userId string, lockStatus bool) error {
	ue := query.Use(u.data.db).TbUserEntity
	userIds := strings.Split(strings.TrimSpace(userId), ",")

	_, err := ue.WithContext(ctx).
		Where(ue.CorpID.Eq(corpId)).
		Where(ue.ID.In(userIds...)).
		Update(ue.LockStatus, !lockStatus)
	return err
}

func (u userRepo) UpdateIdleTime(ctx context.Context, idleTime string) error {
	conf := query.Use(u.data.db).TbSpecialConfig
	_, err := conf.WithContext(ctx).Where(conf.ID.Eq("901")).
		Updates(model.TbSpecialConfig{
			Value: idleTime,
		})
	return err
}

func (u userRepo) GetIdleTime(ctx context.Context) (*model.TbSpecialConfig, error) {
	conf := query.Use(u.data.db).TbSpecialConfig
	return conf.WithContext(ctx).Where(conf.ID.Eq("901")).First()
}

func (u userRepo) CountUserOfIdle(ctx context.Context, corpId string, search string) (int64, error) {
	ue := query.Use(u.data.db).TbUserEntity
	idleTime, err := u.GetIdleTime(ctx)
	if err != nil {
		return 0, err
	}
	days, err := strconv.Atoi(idleTime.Value)
	if err != nil {
		return 0, fmt.Errorf("failed to parse idleTime value to int: %v", err)
	}
	now := time.Now()
	thirtyDaysAgo := now.AddDate(0, 0, -days)
	q := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId)).
		Where(ue.ActiveTime.Lt(thirtyDaysAgo),
			ue.CreatedAt.LtCol(ue.ActiveTime),
		).Or(
		ue.CreatedAt.Lt(thirtyDaysAgo),
		ue.ActiveTime.Lt(thirtyDaysAgo),
	)
	if search != "" {
		searchWithWildCard := "%" + search + "%"
		uec := ue.WithContext(ctx)
		q = q.Where(uec.Where(ue.Name.Like(searchWithWildCard)).Or(ue.DisplayName.Like(searchWithWildCard)))
	}
	return q.Count()
}

func (u userRepo) ListUserOfIdle(ctx context.Context, corpId string, limit, offset int, search string) ([]dto.ListUserRsp, error) {
	ue := query.Use(u.data.db).TbUserEntity
	us := query.Use(u.data.db).TbUserSource
	var res []dto.ListUserRsp
	idleTime, err := u.GetIdleTime(ctx)
	if err != nil {
		return res, err
	}
	days, err := strconv.Atoi(idleTime.Value)
	if err != nil {
		return res, fmt.Errorf("failed to parse idleTime value to int: %v", err)
	}
	now := time.Now()
	thirtyDaysAgo := now.AddDate(0, 0, -days)

	// 构建查询条件
	q := ue.WithContext(ctx).
		LeftJoin(us, us.ID.EqCol(ue.SourceID)).
		Select(ue.ALL, us.SourceType).
		Where(ue.CorpID.Eq(corpId))
	// 添加闲置用户条件：actime_time 在30天前 或者 为NULL且created_at在30天前
	q = q.Where(
		ue.ActiveTime.Lt(thirtyDaysAgo),
		ue.CreatedAt.LtCol(ue.ActiveTime),
	).Or(
		ue.CreatedAt.Lt(thirtyDaysAgo),
		ue.ActiveTime.Lt(thirtyDaysAgo),
	)
	if search != "" {
		searchWithWildCard := "%" + search + "%"
		uec := ue.WithContext(ctx)
		q = q.Where(uec.Where(ue.Name.Like(searchWithWildCard)).Or(ue.DisplayName.Like(searchWithWildCard)))
	}

	// 排序、分页
	q = q.Order(ue.CreatedAt.Desc(), ue.Name.Desc()).Offset(offset).Limit(limit)
	// 执行查询
	err = q.Scan(&res)
	return res, err
}

func (u userRepo) TotpUnbind(ctx context.Context, userId []string) error {
	updateInfo := map[string]interface{}{
		"activation_secret": "",
		"current_secret":    "",
	}
	user := query.Use(u.data.db).TbUserEntity
	_, err := user.WithContext(ctx).Where(user.ID.In(userId...)).
		Updates(updateInfo)
	return err
}

func (u userRepo) UpdateUserRow(ctx context.Context, id string, updateInfo model.TbUserEntity) error {
	userQ := query.Use(u.data.db).TbUserEntity
	_, err := userQ.WithContext(ctx).Where(userQ.ID.Eq(id)).
		Updates(updateInfo)
	return err
}

func (u userRepo) QueryUserByFieldMap(ctx context.Context, corpId, sourceId string, conditions []dto.KV) ([]*model.TbUserEntity, error) {
	var result []*model.TbUserEntity
	if len(conditions) == 0 {
		return result, pb.ErrorParamError("conditions is empty")
	}

	var values = []interface{}{
		corpId, sourceId,
	}
	sql := `SELECT * FROM tb_user_entity WHERE corp_id = ? and source_id= ?`
	for _, condition := range conditions {
		dType := "="
		if condition.Type == "like" {
			dType = "like"
		}
		sql += fmt.Sprintf(` and %s `+dType+` ?`, condition.Key)
		values = append(values, condition.Value)
	}
	err := u.data.db.WithContext(ctx).Raw(sql, values...).Scan(&result).Error
	return result, err
}

func (u userRepo) DeleteUsersOfRoot(ctx context.Context, corpId, rootGroupId string) error {
	ue := query.Use(u.data.db).TbUserEntity
	_, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.RootGroupID.Eq(rootGroupId)).Delete()
	return err
}

func (u userRepo) DeleteExternalDeptsOfRoot(ctx context.Context, rootGroupId string) error {
	eDepts := query.Use(u.data.db).TbExternalDepartment
	_, err := eDepts.WithContext(ctx).Where(eDepts.LocalRootGroupID.Eq(rootGroupId)).Delete()
	return err
}

func (u userRepo) DeleteExternalUserOfRoot(ctx context.Context, rootGroupId string) error {
	eUser := query.Use(u.data.db).TbExternalUser
	_, err := eUser.WithContext(ctx).Where(eUser.LocalRootGroupID.Eq(rootGroupId)).Delete()
	return err
}

func (u userRepo) GetExternalUser(ctx context.Context, rootGroupId, userId, authType string) (*model.TbExternalUser, error) {
	externalUser := query.Use(u.data.db).TbExternalUser
	result, err := externalUser.WithContext(ctx).Where(externalUser.LocalRootGroupID.Eq(rootGroupId), externalUser.Userid.Eq(userId), externalUser.Type.Eq(authType)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbExternalUser{}, pb.ErrorRecordNotFound("externalUser user not found. rootGroupId=%v, userId=%v", rootGroupId, userId)
		}
		return &model.TbExternalUser{}, err
	}
	return result, nil
}

func (u userRepo) GetExternalUserByLocalUserId(ctx context.Context, localUserId string) (*model.TbExternalUser, error) {
	var user model.TbExternalUser
	err := u.data.db.WithContext(ctx).
		Where("local_user_id = ?", localUserId).
		First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, pb.ErrorRecordNotFound("external user not found for local_user_id=%v", localUserId)
		}
		return nil, err
	}
	return &user, nil
}

func (u userRepo) ListExternalDeps(ctx context.Context, rootGroupId string) ([]*model.TbExternalDepartment, error) {
	extDept := query.Use(u.data.db).TbExternalDepartment
	return extDept.WithContext(ctx).Where(extDept.LocalRootGroupID.Eq(rootGroupId)).Find()
}

func (u userRepo) ListExternalUsers(ctx context.Context, rootGroupId string) ([]*model.TbExternalUser, error) {
	extUser := query.Use(u.data.db).TbExternalUser
	return extUser.WithContext(ctx).Where(extUser.LocalRootGroupID.Eq(rootGroupId)).Find()
}

func (u userRepo) BatchInsertExternalDepts(ctx context.Context, depts []*dto.ExternalDepartment) error {
	extDept := query.Use(u.data.db).TbExternalDepartment
	var externalDepts []*model.TbExternalDepartment
	if err := copier.Copy(&externalDepts, &depts); err != nil {
		return err
	}
	return extDept.WithContext(ctx).Create(externalDepts...)
}

func (u userRepo) BatchInsertExternalInfoTx(data dto.SyncDeptAndUser) error {
	return u.data.db.Transaction(func(tx *gorm.DB) error {
		//ext
		err := tx.Where("local_group_id NOT IN ? AND local_root_group_id = ?", data.ExistDeptIds, data.LocalRootGroupId).
			Delete(model.TbExternalDepartment{}).Error
		if err != nil {
			return err
		}
		err = tx.Model(model.TbExternalDepartment{}).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			UpdateAll: true,
		}).CreateInBatches(&data.Depts, 100).Error
		if err != nil {
			return err
		}

		err = tx.Where("local_user_id NOT IN ? AND local_root_group_id = ?", data.ExistUserIds, data.LocalRootGroupId).
			Delete(model.TbExternalUser{}).Error
		if err != nil {
			return err
		}

		// 定义一个 map 来去重 key: userid+rootgroupid
		uniqueKeys := make(map[string]*model.TbExternalUser)

		for _, user := range data.Users {
			key := fmt.Sprintf("%s:%s", user.Userid, user.LocalRootGroupID)
			uniqueKeys[key] = user
		}

		// 构建新的无重复切片
		var deduplicated []*model.TbExternalUser
		for _, user := range uniqueKeys {
			deduplicated = append(deduplicated, user)
		}

		// 使用去重后的数据插入
		err = tx.Model(model.TbExternalUser{}).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "userid"}, {Name: "local_root_group_id"}, {Name: "type"}},
			UpdateAll: true,
		}).CreateInBatches(deduplicated, 100).Error
		if err != nil {
			return err
		}

		// local
		err = tx.Where("id NOT IN ? AND root_group_id = ? AND id != ?", data.ExistDeptIds, data.LocalRootGroupId, data.LocalRootGroupId).
			Delete(model.TbUserGroup{}).Error
		if err != nil {
			return err
		}
		err = tx.Model(model.TbUserGroup{}).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			UpdateAll: true,
		}).CreateInBatches(&data.LocalGroups, 100).Error
		if err != nil {
			return err
		}

		err = tx.Where("id NOT IN ? AND root_group_id = ?", data.ExistUserIds, data.LocalRootGroupId).
			Delete(model.TbUserEntity{}).Error
		if err != nil {
			return err
		}
		err = tx.Model(model.TbUserEntity{}).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			UpdateAll: true,
		}).CreateInBatches(&data.LocalUsers, 100).Error
		if err != nil {
			return err
		}

		pathSql := `WITH gpt as  (WITH RECURSIVE group_path_tree(id, name,parent_group_id,full_path,depth) AS (
                                        SELECT id, name,parent_group_id,name AS full_path, 0 AS depth
                                        FROM tb_user_group
                                        UNION
                                        SELECT gpt.id,gpt.name,tug.parent_group_id,CASE WHEN gpt.depth = 0 THEN tug.name ELSE tug.name || '/' || gpt.full_path end,gpt.depth +1
                                        FROM tb_user_group tug, group_path_tree gpt
                                        WHERE tug.id  = gpt.parent_group_id)
                                        SELECT id, name,parent_group_id,full_path,depth
                                        FROM group_path_tree
                                        where parent_group_id ='0') 
                                        
					 update tb_user_group 
					 set path = case when tb_user_group.id = ? then '/' else  gpt.full_path end
					 from gpt
					 where tb_user_group.id = gpt.id and tb_user_group.root_group_id = ?`

		err = tx.Raw(pathSql, data.LocalRootGroupId, data.LocalRootGroupId).Scan(nil).Error
		if err != nil {
			return err
		}
		return nil
	})
}

func (u userRepo) BatchInsertExternalUsers(ctx context.Context, users []*dto.ExternalUser) error {
	extUser := query.Use(u.data.db).TbExternalUser
	var externalUsers []*model.TbExternalUser
	if err := copier.Copy(&externalUsers, &users); err != nil {
		return err
	}
	return extUser.WithContext(ctx).Create(externalUsers...)
}

func (u userRepo) DeleteUser(ctx context.Context, corpId, userId, name string) error {
	return u.data.db.Transaction(func(tx *gorm.DB) error {
		// 删除凭证
		err := tx.WithContext(ctx).Model(model.TbCredential{}).
			Where("corp_id = ? and user_id = ?", corpId, userId).
			Delete(&model.TbCredential{}).Error
		if err != nil {
			return err
		}
		// 删除角色关系
		err = tx.WithContext(ctx).Model(model.TbUserRole{}).
			Where("corp_id = ? and user_id = ?", corpId, userId).
			Delete(&model.TbUserRole{}).Error
		if err != nil {
			return err
		}
		// 删除用户外部表
		err = tx.WithContext(ctx).Model(model.TbExternalUser{}).
			Where("local_user_id = ?", userId).
			Delete(&model.TbExternalUser{}).Error
		if err != nil {
			return err
		}
		// 删除用户实体
		err = tx.WithContext(ctx).Model(model.TbUserEntity{}).
			Where("corp_id = ? and id = ?", corpId, userId).
			Delete(&model.TbUserEntity{}).Error
		if err != nil {
			return err
		}
		// 删除策略绑定关系
		tx.WithContext(ctx).Model(model.TbAuthPolicy{}).
			Where("corp_id = ? and ? = any(user_ids)", corpId, userId).
			Update("user_ids", gorm.Expr("array_remove(user_ids, ?)", userId))

		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)
		//err := tx.WithContext(ctx).Model(model.TbCredential{})
		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         corpId,
				ResourceType:   common.UserType,
				OperationType:  common.OperateDelete,
				Representation: name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			u.data.db.Create(&oplog)

		}()
		return nil
	})
}

func (u userRepo) QueryUserByName(ctx context.Context, corpId, name string) (*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	//用户ID必然是唯一的所以去掉corpId判断
	//result, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.ID.Eq(userId)).First()
	result, err := ue.WithContext(ctx).Where(ue.Name.Eq(name)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("corpId=%v, name=%v not found.", corpId, name)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (u userRepo) QueryUserEntity(ctx context.Context, corpId, userId string) (*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	//用户ID必然是唯一的所以去掉corpId判断
	//result, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.ID.Eq(userId)).First()
	result, err := ue.WithContext(ctx).Where(ue.ID.Eq(userId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("corpId=%v, userId=%v not found.", corpId, userId)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (u userRepo) QueryUserWithGroupInfo(ctx context.Context, corpId, userId string) (dto.User, error) {
	q := query.Use(u.data.db)
	ue := q.TbUserEntity
	ug := q.TbUserGroup
	var result dto.User
	err := ue.WithContext(ctx).LeftJoin(ug, ug.ID.EqCol(ue.GroupID)).
		Where(ue.CorpID.Eq(corpId), ue.ID.Eq(userId)).Select(ue.ALL, ug.Name.As("GroupName")).Scan(&result)
	return result, err
}

func (u userRepo) QueryUserWithGroupAndSource(ctx context.Context, corpId, userId string) (dto.User, error) {
	q := query.Use(u.data.db)
	ue := q.TbUserEntity
	ug := q.TbUserGroup
	us := q.TbUserSource
	var result dto.User
	err := ue.WithContext(ctx).Join(ug, ug.ID.EqCol(ue.GroupID)).Join(us, us.ID.EqCol(ue.SourceID)).
		Where(ue.CorpID.Eq(corpId), ue.ID.Eq(userId)).
		Select(ue.ALL, ug.Name.As("GroupName"),
			us.SourceType.As("SourceType"), us.Name.As("SourceName")).Scan(&result)
	return result, err
}

func (u userRepo) QueryCredential(ctx context.Context, corpId, userId, credType string) (*model.TbCredential, error) {
	credT := query.Use(u.data.db).TbCredential
	result, err := credT.WithContext(ctx).Where(credT.CorpID.Eq(corpId), credT.UserID.Eq(userId), credT.Type.Eq(credType)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbCredential{}, pb.ErrorRecordNotFound("cred not found. corpId=%v, userId=%v, credType=%v", corpId, userId, credType)
		}
		return &model.TbCredential{}, err
	}
	return result, nil
}

func (u userRepo) QueryUserByNameInSource(ctx context.Context, corpId, sourceId, name string) (*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	result, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.SourceID.Eq(sourceId), ue.Name.Eq(name)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("name=%v", name)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (u userRepo) QueryUserByIdInSource(ctx context.Context, corpId, sourceId, name, id string) (*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	var result *model.TbUserEntity
	var err error
	if id == "" {
		result, err = ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.SourceID.Eq(sourceId), ue.Name.Eq(name)).First()
	} else {
		result, err = ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.SourceID.Eq(sourceId), ue.ID.Eq(id)).First()
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("id=%v", id)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (u userRepo) QueryUserByTrueNameInSource(ctx context.Context, corpId, sourceId, trueName string) (*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	result, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.SourceID.Eq(sourceId), ue.TrueName.Eq(trueName)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("trueName=%v", trueName)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (u userRepo) CreateCredential(ctx context.Context, corpId, id, userId, credType, secretData, credData string) error {
	credT := query.Use(u.data.db).TbCredential
	return credT.WithContext(ctx).Create(&model.TbCredential{
		ID:             id,
		UserID:         userId,
		Type:           credType,
		SecretData:     secretData,
		CredentialData: credData,
		CorpID:         corpId,
	})
}

func (u userRepo) UpdateCredential(ctx context.Context, id, secretData, credData string) error {
	credT := query.Use(u.data.db).TbCredential
	_, err := credT.WithContext(ctx).Where(credT.ID.Eq(id)).
		Updates(model.TbCredential{
			SecretData:     secretData,
			CredentialData: credData,
		})
	return err
}

func (u userRepo) ListUserBindInfo(ctx context.Context, corpId string, userIds []string) ([]dto.BindUserInfo, error) {
	var result []dto.BindUserInfo
	err := u.data.db.Table("tb_user_entity").
		Select("tb_user_entity.id,tb_user_entity.name,tb_user_entity.display_name,CASE WHEN tb_user_group.PATH = '/' THEN concat ( tb_user_group.PATH, tb_user_group.NAME ) ELSE concat ( tb_user_group.PATH, '/', tb_user_group.NAME ) END AS PATH ").
		Joins("INNER JOIN tb_user_group on tb_user_entity.group_id = tb_user_group.id").
		Where("tb_user_entity.corp_id = ? AND tb_user_entity.id in (?)", corpId, userIds).
		Scan(&result)

	return result, err.Error
}

func (u userRepo) GetGroupOfUsers(ctx context.Context, corpId string, ids []string) ([]string, error) {
	ue := query.Use(u.data.db).TbUserEntity
	var result []string
	err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.ID.In(ids...)).Select(ue.GroupID).Group(ue.GroupID).Scan(&result)
	return result, err
}

func (u userRepo) QueryUsersRoles(ctx context.Context, corpId string, userIds []string) ([]dto.RoleInfo, error) {
	var result []dto.RoleInfo
	userRole := query.Use(u.data.db).TbUserRole
	role := query.Use(u.data.db).TbRole
	err := userRole.WithContext(ctx).Select(userRole.UserID, role.ID, role.Name).LeftJoin(role, role.ID.EqCol(userRole.RoleID)).Where(userRole.CorpID.Eq(corpId), userRole.UserID.In(userIds...)).Scan(&result)
	return result, err
}

func (u userRepo) ListUser(ctx context.Context, corpId string) ([]*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	return ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId)).Find()
}

func (u userRepo) ListUserOfGroups(ctx context.Context, corpId string, groupIds []string, limit, offset int, search string) ([]dto.ListUserRsp, error) {
	ue := query.Use(u.data.db).TbUserEntity
	us := query.Use(u.data.db).TbUserSource
	var res []dto.ListUserRsp
	if search != "" {
		uec := ue.WithContext(ctx)
		err := uec.LeftJoin(us, us.ID.EqCol(ue.SourceID)).Select(ue.ALL, us.SourceType).
			Where(ue.CorpID.Eq(corpId), ue.GroupID.In(groupIds...)).
			Where(uec.Where(ue.Name.Like(search)).Or(ue.DisplayName.Like(search))).
			Order(ue.CreatedAt.Desc(), ue.Name.Desc()).Offset(offset).Limit(limit).Scan(&res)
		return res, err
	}
	err := ue.WithContext(ctx).LeftJoin(us, us.ID.EqCol(ue.SourceID)).Select(ue.ALL, us.SourceType).
		Where(ue.CorpID.Eq(corpId), ue.GroupID.In(groupIds...)).
		Order(ue.CreatedAt.Desc(), ue.Name.Desc()).Offset(offset).Limit(limit).Scan(&res)
	return res, err
}

func (u userRepo) CountUserOfGroups(ctx context.Context, corpId string, groupIds []string, search string) (int64, error) {
	ue := query.Use(u.data.db).TbUserEntity
	if search != "" {
		uec := ue.WithContext(ctx)
		return ue.WithContext(ctx).
			Where(ue.CorpID.Eq(corpId), ue.GroupID.In(groupIds...)).
			Where(uec.Where(ue.Name.Like(search)).Or(ue.DisplayName.Like(search))).Count()
	}
	return ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.GroupID.In(groupIds...)).Count()
}

func (u userRepo) QueryUserByPhoneInSource(ctx context.Context, corpId, phone, sourceId string) (*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	result, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.Phone.Eq(phone), ue.SourceID.Eq(sourceId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("phone=%v, sourceId=%v not found.", phone, sourceId)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (u userRepo) QueryUserByEmailInSource(ctx context.Context, corpId, email, sourceId string) (*model.TbUserEntity, error) {
	ue := query.Use(u.data.db).TbUserEntity
	result, err := ue.WithContext(ctx).Where(ue.CorpID.Eq(corpId), ue.Email.Eq(email), ue.SourceID.Eq(sourceId)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.TbUserEntity{}, pb.ErrorRecordNotFound("email=%v, sourceId=%v not found.", email, sourceId)
		}
		return &model.TbUserEntity{}, err
	}
	return result, nil
}

func (u userRepo) CreateUser(ctx context.Context, param dto.CreateUserDaoParam) error {
	ue := query.Use(u.data.db).TbUserEntity
	err := ue.WithContext(ctx).Create(&model.TbUserEntity{
		CorpID:      param.CorpId,
		ID:          param.Id,
		Name:        param.Name,
		NickName:    param.NickName,
		TrueName:    param.TrueName,
		AuthType:    param.AuthType,
		Identify:    param.Identifier,
		GroupID:     param.GroupId,
		SourceID:    param.SourceId,
		Phone:       param.Phone,
		Email:       param.Email,
		ExpireType:  string(param.ExpireType),
		ExpireEnd:   time.Unix(param.ExpireEnd, 0),
		DisplayName: param.DisplayName,
		RootGroupID: param.RootGroupID,
	})
	if err != nil {
		return err
	}
	_, err = ue.WithContext(ctx).Where(ue.CorpID.Eq(param.CorpId), ue.ID.Eq(param.Id)).Update(ue.Enable, param.Enable)
	if err != nil {
		return err
	}
	//日志操作
	var errorLog = ""
	authUserID, _ := common.GetUserId(ctx)

	defer func() {
		if err != nil {
			errorLog = err.Error()
		}
		oplog := modelTable.Oprlog{
			Id:             uuid.New().String(),
			CorpId:         param.CorpId,
			ResourceType:   common.UserType,
			OperationType:  common.OperateCreate,
			Representation: param.Name,
			Error:          errorLog,
			AuthUserID:     authUserID,
			AdminEventTime: time.Now().UnixMilli(),
			IpAddress:      common.GetClientHost(ctx),
		}
		u.data.db.Create(&oplog)

	}()
	return nil
}

func (u userRepo) CreateUserCustom(ctx context.Context, param dto.CreateUserDaoParam) error {
	ue := query.Use(u.data.db).TbUserEntity
	err := ue.WithContext(ctx).Create(&model.TbUserEntity{
		ID:          param.Id,
		Name:        param.Name,
		Identify:    param.Identifier,
		GroupID:     param.GroupId,
		SourceID:    param.SourceId,
		Phone:       param.Phone,
		Email:       param.Email,
		ExpireType:  string(param.ExpireType),
		ExpireEnd:   time.Unix(param.ExpireEnd, 0),
		DisplayName: param.DisplayName,
		RootGroupID: param.RootGroupID,
	})
	if err != nil {
		return err
	}
	_, err = ue.WithContext(ctx).Where(ue.CorpID.Eq(param.CorpId), ue.ID.Eq(param.Id)).Update(ue.Enable, param.Enable)
	if err != nil {
		return err
	}
	return nil
}

func (u userRepo) UpdateUser(ctx context.Context, param dto.CreateUserDaoParam, roleIds []string, pwd string, enable bool) error {
	return u.data.db.Transaction(func(tx *gorm.DB) error {
		ue := query.Use(tx).TbUserEntity
		_, err := ue.WithContext(ctx).Select(ue.CorpID, ue.GroupID, ue.SourceID, ue.Identify, ue.Phone, ue.Email, ue.ExpireType, ue.ExpireEnd, ue.DisplayName).Updates(&model.TbUserEntity{
			CorpID:      param.CorpId,
			ID:          param.Id,
			Name:        param.Name,
			GroupID:     param.GroupId,
			SourceID:    param.SourceId,
			Identify:    param.Identifier,
			Phone:       param.Phone,
			Email:       param.Email,
			ExpireType:  string(param.ExpireType),
			ExpireEnd:   time.Unix(param.ExpireEnd, 0),
			DisplayName: param.DisplayName,
		})
		if err != nil {
			u.log.Errorf("UpdateUserInfo Failed. err=%v", err)
			return err
		}

		_, err = ue.WithContext(ctx).Where(ue.CorpID.Eq(param.CorpId), ue.ID.Eq(param.Id)).UpdateColumnSimple(ue.Enable.Value(enable))
		if err != nil {
			u.log.Errorf("UpdateStatus Failed. err=%v", err)
			return err
		}
		if pwd != "" {
			credParam := dto.CreatCredParam{
				CorpId:   param.CorpId,
				UserId:   param.Id,
				CredType: dto.CredTypePassword,
				Password: pwd,
			}
			manager := common.NewPbkdf2SaltCredManager()
			credential, err := manager.NewCredential(pwd)
			if err != nil {
				u.log.Errorf("NewCredential failed. err=%v", err)
				return err
			}
			if err := tx.WithContext(ctx).Model(model.TbCredential{}).
				Where("corp_id = ? and user_id = ?", credParam.CorpId, credParam.UserId).
				Delete(&model.TbCredential{}).Error; err != nil {
				return err
			}
			id := uuid.New()
			// todo 这里不算完全的事务，这里应该吧tx 传进去
			if err := u.CreateCredential(ctx, param.CorpId, id.String(), credParam.UserId, string(credParam.CredType), credential.SecretDataStr, credential.CredDataStr); err != nil {
				u.log.Errorf("createCredential failed. err=%v, param=%+v", err, credParam)
				return err
			}
		}
		if err := tx.WithContext(ctx).Model(model.TbUserRole{}).
			Where("corp_id = ? and user_id = ?", param.CorpId, param.Id).
			Delete(&model.TbUserRole{}).Error; err != nil {
			return err
		}
		if len(roleIds) != 0 {
			userRole := query.Use(tx).TbUserRole
			var userRoles []*model.TbUserRole
			for _, role := range roleIds {
				userRoles = append(userRoles, &model.TbUserRole{
					UserID: param.Id,
					RoleID: role,
					CorpID: param.CorpId,
				})
			}
			return userRole.WithContext(ctx).Create(userRoles...)
		}
		//日志操作
		var errorLog = ""
		authUserID, _ := common.GetUserId(ctx)

		defer func() {
			if err != nil {
				errorLog = err.Error()
			}
			oplog := modelTable.Oprlog{
				Id:             uuid.New().String(),
				CorpId:         param.CorpId,
				ResourceType:   common.UserType,
				OperationType:  common.OperateUpdate, //common.OperateCreate,
				Representation: param.Name,
				Error:          errorLog,
				AuthUserID:     authUserID,
				AdminEventTime: time.Now().UnixMilli(),
				IpAddress:      common.GetClientHost(ctx),
			}
			u.data.db.Create(&oplog)

		}()
		return nil
	})
}

func (u userRepo) BatchInsertUser(ctx context.Context, users []*dto.CreateUserDaoParam) error {
	ue := query.Use(u.data.db).TbUserEntity
	var usersInsert []*model.TbUserEntity
	for _, user := range users {
		usersInsert = append(usersInsert, &model.TbUserEntity{
			CorpID:      user.CorpId,
			ID:          user.Id,
			Name:        user.Name,
			GroupID:     user.GroupId,
			Identify:    user.Identifier,
			SourceID:    user.SourceId,
			Phone:       user.Phone,
			Email:       user.Email,
			ExpireType:  string(user.ExpireType),
			ExpireEnd:   time.Unix(user.ExpireEnd, 0),
			DisplayName: user.DisplayName,
			RootGroupID: user.RootGroupID,
		})
	}
	return ue.WithContext(ctx).Create(usersInsert...)
}

func NewUserRepo(data *Data, logger log.Logger) biz.UserRepo {
	return &userRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (u userRepo) GetExternalUserByUserId(ctx context.Context, userId string) (*model.TbExternalUser, error) {
	var user model.TbExternalUser
	err := u.data.db.WithContext(ctx).
		Where("userid = ?", userId).
		First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (u userRepo) QueryUserByNameAndGroup(ctx context.Context, corpId, name, groupId string) (*model.TbUserEntity, error) {
	var user model.TbUserEntity
	err := u.data.db.WithContext(ctx).
		Where("corp_id = ? AND name = ? AND group_id = ?", corpId, name, groupId).
		First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateExternalUser 更新外部用户信息
func (u userRepo) UpdateExternalUser(ctx context.Context, user model.TbExternalUser) error {

	// 使用GORM的Model方法指定要更新的记录
	result := u.data.db.WithContext(ctx).
		Model(&model.TbExternalUser{}).
		Where("uniq_key = ?", user.UniqKey).
		Updates(user)

	if result.Error != nil {
		return result.Error
	}

	// 检查是否有记录被更新
	if result.RowsAffected == 0 {
		return pb.ErrorRecordNotFound("external user not found for local_user_id=%v", user.LocalUserID)
	}

	return nil
}

// UpdateUserBasicInfo 更新本地用户基本信息
func (u userRepo) UpdateUserBasicInfo(ctx context.Context, param dto.CreateUserDaoParam) error {
	// 定义要更新的字段
	updates := map[string]interface{}{
		"display_name": param.DisplayName,
		"name":         param.Name,
		"email":        param.Email,
		"phone":        param.Phone,
		"true_name":    param.TrueName,
		"nick_name":    param.NickName,
		"updated_at":   time.Now(),
	}

	u.log.Infof("正在更新用户(%s)信息: %+v", param.Id, updates)

	// 使用GORM更新
	result := u.data.db.WithContext(ctx).
		Model(&model.TbUserEntity{}).
		Where("id = ?", param.Id).
		Updates(updates)

	if result.Error != nil {
		return result.Error
	}

	// 检查是否有记录被更新
	if result.RowsAffected == 0 {
		return pb.ErrorRecordNotFound("user not found for id=%v", param.Id)
	}

	return nil
}
