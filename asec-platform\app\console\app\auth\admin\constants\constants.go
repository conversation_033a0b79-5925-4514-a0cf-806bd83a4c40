package constants

type RedisPrefixKey string

const (
	AdminLoginPwdInCorrectError = "用户名或者密码错误"

	PasswordRegex                 = "^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\\W_!@#$%^&*`~()-+=]+$)(?![0-9\\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\\W_!@#$%^&*`~()-+=]{8,63}$"
	EmptyErrorMsg                 = "get private key empty"
	AdminLoginFailedMaxTimesError = "the maximum number of user login failures has been reached. please try again slightly"
	TokenTypeBear                 = "Bearer"
	AccessTokenDuration           = "access_token_duration"
	RefreshTokenDuration          = "refresh_token_duration"
	AccessTokenTyp                = "access_token"
	RefreshTokenTyp               = "refresh_token"

	ActiveAdminToken RedisPrefixKey = "token"
	ActiveAdminMax                  = 30
	ActiveAdmin                     = "admin"

	LogoutAdmin             RedisPrefixKey = "logout"
	LogoutAdminDefaultValue                = "1"

	PrivateKey = "ecdsaPrivateKey"
	PublicKey  = "ecdsaPublicKey"

	DefaultIter   = 27500
	DefaultKeyLen = 64

	CredTypePassword = "password"
	NeverExpireType  = 1

	LoginLogType       = "LOGIN"
	LogoutLogType      = "LOGOUT"
	LoginLogErrType    = "LOGIN_ERROR"
	LoginClient        = "Admin"
	LoginAdminAuthType = "本地认证"

	TokenAuthKey = "Authorization"
)

const (
	LicenseRedisPrefix  = "Authorization"
	LicenseInfoRedisKey = "day"
	OneMonthDay         = 30

	OneDayLicenseDuration = 3600000000000 * 24 * 1 //缓存时间3天
)
