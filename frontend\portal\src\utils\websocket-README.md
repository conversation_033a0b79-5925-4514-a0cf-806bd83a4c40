# WebSocket 工具类使用说明

## 概述

本工具类封装了项目中所有 WebSocket 连接相关的功能，统一管理与客户端的通信。

## 🔥 新特性：页面级别共享连接 + 登录同步

### 页面级别共享连接
- **单一连接**：每个页面只使用一个 WebSocket 连接，所有业务功能共享
- **唯一标识**：每个页面生成唯一的连接ID，格式：`page-{timestamp}-{random}`
- **自动连接管理**：连接会自动保持，不会在使用后立即关闭
- **智能重连**：连接断开时自动尝试重连（最多3次）
- **消息处理器**：支持为同一连接添加多个消息处理器
- **性能优化**：避免多个连接的资源浪费，提高性能
- **连接隔离**：不同页面/标签页之间的连接完全独立

### 登录同步功能
- **全局同步**：浏览器和客户端之间的登录状态实时同步
- **自动跳转**：任一页面登录成功后，所有页面自动跳转到工作台
- **统一退出**：任一页面退出登录后，所有页面自动退出并跳转到登录页
- **消息去重**：通过唯一ID避免处理自己发送的同步消息
- **客户端集成**：与客户端50001端口保持持续连接

## 主要功能

### 1. Token 获取
```javascript
import { connectForToken } from '@/utils/websocket'

// 获取客户端Token（默认2秒超时）
const tokenInfo = await connectForToken()
if (tokenInfo) {
  console.log('Token:', tokenInfo.accessToken)
  console.log('RefreshToken:', tokenInfo.refreshToken)
} else {
  console.log('未获取到Token（可能是超时或客户端未响应）')
}

// 自定义超时时间
const tokenInfo2 = await connectForToken(50001, 5000) // 5秒超时
```

### 2. 应用启动
```javascript
import { connectForAppLaunch } from '@/utils/websocket'

// 启动应用
try {
  await connectForAppLaunch('应用名称')
  console.log('应用启动成功')
} catch (error) {
  console.error('应用启动失败:', error)
}
```

### 3. 注销登录
```javascript
import { connectForLogout } from '@/utils/websocket'

// 通知客户端注销登录
try {
  await connectForLogout()
  console.log('注销通知发送成功')
} catch (error) {
  console.error('注销通知发送失败:', error)
}
```

### 4. 登录同步功能
```javascript
import {
  initializeLoginSync,
  broadcastLogin,
  broadcastLogout
} from '@/utils/loginSync'

// 初始化登录同步（通常在应用启动时调用）
await initializeLoginSync({
  port: 50001
})

// 登录成功后广播登录信息
const loginData = {
  accessToken: 'your-access-token',
  refreshToken: 'your-refresh-token',
  userInfo: { id: 1, username: 'user' }
}
broadcastLogin(loginData)

// 退出登录时广播退出信息
broadcastLogout({ reason: 'user_logout' })
```

### 5. 页面共享连接管理
```javascript
import {
  getPersistentConnection,
  addPersistentMessageHandler,
  sendToPersistentConnection,
  isWebSocketConnected
} from '@/utils/websocket'

// 获取页面级别的共享连接（所有业务共用）
const connection = await getPersistentConnection(50001)

// 添加消息处理器
const messageHandler = (event) => {
  console.log('收到消息:', event.data)
}
addPersistentMessageHandler(messageHandler)

// 发送消息
sendToPersistentConnection({ type: 'test' })

// 检查连接状态
if (isWebSocketConnected()) {
  console.log('页面连接正常')
}

// 连接会自动保持，无需手动关闭
```

### 6. 直接消息发送（简化API）
```javascript
import {
  sendWebSocketMessage,
  closeWebSocketConnection,
  isPageConnectionAvailable,
  getPageConnectionId
} from '@/utils/websocket'

// 获取当前页面的唯一连接ID
const connectionId = getPageConnectionId()
console.log('页面连接ID:', connectionId) // 例如: page-1704067200000-abc123def

// 直接发送消息到页面共享连接
sendWebSocketMessage({ type: 'test' })

// 检查页面连接是否可用
if (isPageConnectionAvailable()) {
  console.log('页面连接可用')
}

// 关闭页面连接（通常不需要手动调用）
closeWebSocketConnection()
```

## 登录同步机制

### 工作原理
1. **持续连接**：每个浏览器页面都与客户端50001端口保持WebSocket连接
2. **登录广播**：任一页面登录成功后，通过WebSocket广播登录信息
3. **客户端转发**：客户端接收到登录信息后，转发给所有连接的浏览器页面
4. **自动同步**：所有页面接收到登录信息后，自动设置token并跳转到工作台
5. **退出同步**：退出登录时同样通过广播机制同步到所有页面

### 消息格式
```javascript
// 登录同步消息
{
  "action": 0,
  "msg": {
    "token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh-token-value",
    "userInfo": { "id": 1, "username": "user" },
    // 同步控制字段（首字母小写）
    "type": "broadcast",
    "action": "login",
    "senderId": "page-1704067200000-abc123def",
    "timestamp": 1704067200000,
    "source": "browser",
    "pageId": "page-1704067200000-abc123def"
  },
  "platform": "asec.infogo.net.cn"
}

// 退出同步消息
{
  "action": 1,
  "msg": {
    // 同步控制字段（首字母小写）
    "type": "broadcast",
    "action": "logout",
    "senderId": "page-1704067200000-abc123def",
    "timestamp": 1704067200000,
    "reason": "user_logout",
    "source": "browser",
    "pageId": "page-1704067200000-abc123def"
  },
  "platform": "asec.infogo.net.cn"
}
```

### 消息去重机制
- 每个页面都有唯一的连接ID作为SenderId
- 页面接收到消息时，检查SenderId是否为自己
- 如果是自己发送的消息，则忽略不处理
- 避免了消息循环和重复处理

## 唯一连接ID

每个页面会自动生成一个唯一的连接ID，确保不同页面/标签页之间的连接完全独立。

### ID格式
```
page-{timestamp}-{random}
```

- `timestamp`: 页面加载时的时间戳
- `random`: 9位随机字符串

### 示例
```
page-1704067200000-abc123def
page-1704067201234-xyz789ghi
```

### 获取连接ID
```javascript
import { getPageConnectionId } from '@/utils/websocket'

const connectionId = getPageConnectionId()
console.log('当前页面连接ID:', connectionId)
```

## 配置选项

### 连接选项
- `id`: 连接标识符
- `port`: WebSocket端口 (默认: 50001)
- `host`: WebSocket主机 (默认: 127.0.0.1)
- `useSSL`: 是否使用SSL (默认: 根据平台自动判断)
- `timeout`: 连接超时时间 (默认: 2000ms)
- `persistent`: 是否为持久连接 (默认: true)
- `onOpen`: 连接打开回调
- `onMessage`: 消息接收回调
- `onError`: 错误回调
- `onClose`: 连接关闭回调

### Token获取选项
- `port`: WebSocket端口 (默认: 50001)
- `timeout`: 等待Token响应的超时时间 (默认: 2000ms)
  - 超过此时间未收到响应则认为没有获取到Token
  - 返回 `null` 表示获取失败

## 常量

### WebSocket 状态
```javascript
import { WS_STATUS } from '@/utils/websocket'

WS_STATUS.CONNECTING  // 0 - 连接中
WS_STATUS.OPEN        // 1 - 已连接
WS_STATUS.CLOSING     // 2 - 关闭中
WS_STATUS.CLOSED      // 3 - 已关闭
```

### WebSocket 动作类型
```javascript
import { WS_ACTIONS } from '@/utils/websocket'

WS_ACTIONS.LOGOUT     // 1 - 注销登录
WS_ACTIONS.GET_TOKEN  // 2 - 获取Token
WS_ACTIONS.LAUNCH_APP // 3 - 启动应用
```

## 错误处理与重连机制

所有函数都包含完善的错误处理机制：
- **自动重连**：持久连接断开时自动重连（最多3次）
- **递增延迟**：重连延迟递增，避免频繁重连
- **连接超时处理**：连接超时自动重试
- **降级处理**：连接失败时的协议唤起等降级方案
- **详细日志**：完整的错误和状态日志记录

### 重连配置
- 最大重连次数：3次
- 基础重连延迟：1秒
- 延迟递增：每次重连延迟 = 基础延迟 × (重连次数 + 1)

## 架构说明

### 页面级别共享连接
- **单一连接原则**：每个页面只创建一个 WebSocket 连接
- **唯一连接ID**：每个页面生成唯一的连接ID（格式：`page-{timestamp}-{random}`）
- **消息路由**：通过消息处理器实现不同业务的消息分发
- **资源优化**：避免多连接造成的资源浪费
- **连接隔离**：不同页面/标签页之间的连接完全独立

### 迁移说明

#### 原有代码迁移
1. 将原有的 WebSocket 连接代码替换为对应的工具函数调用
2. 移除手动的 WebSocket 连接管理代码
3. 所有业务功能自动共享同一个连接
4. 使用统一的错误处理机制

#### 已迁移的文件
- `src/permission.js` - Token获取功能
- `src/view/layout/index.vue` - 注销登录功能
- `src/view/app/index.vue` - 应用启动和客户端通知功能

## 测试

使用测试文件验证功能：
```javascript
import { runAllTests } from '@/utils/websocket-test'

// 运行所有测试
runAllTests()
```
