package upgrade

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/constants"
	"asdsec.com/asec/platform/pkg/utils"
)

const (
	// 默认升级检查间隔（30分钟）
	defaultUpgradeCheckInterval = 30 * time.Minute
	// 状态上报请求文件检查间隔
	statusCheckInterval = 5 * time.Second
)

// UpgradeCheckInfo 升级查询结果信息，与 LightSentinel 的 CheckUpgradeInfo 结构保持一致
type UpgradeCheckInfo struct {
	UpgradeVersion string `json:"upgrade_version"` // latest_version
	UpgradeEnabled bool   `json:"upgrade_enabled"` // need_upgrade
	ForceUpgrade   bool   `json:"force_upgrade"`   // force_update
	DownloadURL    string `json:"download_url"`    // download_url
	PackageMD5     string `json:"package_md5"`     // md5_sum
	PackagePath    string `json:"package_path"`    // 本地路径，由文件名计算得出
	PackageName    string `json:"package_name"`    // latest_filename
	Timestamp      int64  `json:"timestamp"`       // 查询时间戳
}

// UpgradeCheckResponse 平台返回的升级查询响应结构
type UpgradeCheckResponse struct {
	Code int `json:"code"`
	Data struct {
		LatestVersion  string `json:"latest_version"`
		NeedUpgrade    bool   `json:"need_upgrade"`
		ForceUpdate    bool   `json:"force_update"`
		DownloadURL    string `json:"download_url"`
		MD5Sum         string `json:"md5_sum"`
		LatestFilename string `json:"latest_filename"`
	} `json:"data"`
}

// UpgradeStatusRequest 升级状态上报请求参数
type UpgradeStatusRequest struct {
	LastVersion  string `json:"last_version"`
	NextVersion  string `json:"next_version"`
	ApplianceID  string `json:"appliance_id"`
	Platform     string `json:"platform"`
	Status       string `json:"status"`
	FailedReason string `json:"failed_reason"`
}

// UpgradeStatusResponse 升级状态上报响应
type UpgradeStatusResponse struct {
	Code int `json:"code"`
}

// DownloadRequest 下载请求结构
type DownloadRequest struct {
	DownloadURL    string `json:"download_url"`    // 下载URL
	PackagePath    string `json:"package_path"`    // 本地保存路径
	PackageMD5     string `json:"package_md5"`     // 预期MD5值
	UpgradeVersion string `json:"upgrade_version"` // 升级版本
	Timestamp      int64  `json:"timestamp"`       // 请求时间戳
	RequestID      string `json:"request_id"`      // 唯一请求ID
}

// DownloadResponse 下载响应结构
type DownloadResponse struct {
	Success      bool   `json:"success"`       // 下载是否成功
	ErrorMessage string `json:"error_message"` // 错误信息
	ActualMD5    string `json:"actual_md5"`    // 实际文件MD5
	FilePath     string `json:"file_path"`     // 下载文件路径
	Timestamp    int64  `json:"timestamp"`     // 响应时间戳
	RequestID    string `json:"request_id"`    // 对应的请求ID
}

// UpgradeLock 升级锁结构 - 确保升级流程串行化
type UpgradeLock struct {
	Status           string `json:"status"`                       // 升级状态: checking, downloading, installing, completed
	Version          string `json:"version"`                      // 升级版本
	StartTime        int64  `json:"start_time"`                   // 升级开始时间
	LastUpdate       int64  `json:"last_update"`                  // 最后更新时间
	ProcessID        int    `json:"process_id"`                   // 进程ID
	InstallStartTime int64  `json:"install_start_time,omitempty"` // 安装开始时间（可选）
}

// StartUpgradeService 启动完整的升级服务，包括查询、下载、安装和状态上报
func StartUpgradeService() {
	global.Logger.Info("Starting unified upgrade service with full upgrade logic")

	// 启动时检查未完成的升级流程
	checkIncompleteUpgradeOnStartup()

	// 启动完整的升级管理服务
	go startFullUpgradeService()

	// 启动状态上报监控服务（保留，用于LightSentinel主动上报）
	go startUpgradeStatusService()

	global.Logger.Info("Full upgrade service started successfully")
}

// startFullUpgradeService 启动完整的升级管理服务
func startFullUpgradeService() {
	global.Logger.Info("Starting full upgrade management service")

	// 立即执行一次升级检查
	doFullUpgradeCheck()

	// 启动定时器
	ticker := time.NewTicker(defaultUpgradeCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			doFullUpgradeCheck()
		}
	}
}

// startUpgradeStatusService 启动升级状态上报监控服务
func startUpgradeStatusService() {
	global.Logger.Info("Starting upgrade status monitoring service")

	requestFilePath := filepath.Join(utils.GetConfigDir(), "upgrade_status_request.json")

	// 定期检查状态上报请求文件
	ticker := time.NewTicker(statusCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			processUpgradeStatusRequest(requestFilePath)
		}
	}
}

// doFullUpgradeCheck 执行完整的升级检查、下载和安装流程
func doFullUpgradeCheck() {
	global.Logger.Info("Performing full upgrade check and management")

	// 0. 检查是否已有升级流程在进行
	if isUpgradeInProgress() {
		global.Logger.Info("Upgrade process already in progress, skipping this check")
		return
	}

	// 1. 检查升级
	checkInfo, err := checkUpgrade()
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to check upgrade: %v", err)
		return
	}

	global.Logger.Sugar().Infof("Upgrade check result - Version: %s, Enabled: %t, Force: %t",
		checkInfo.UpgradeVersion, checkInfo.UpgradeEnabled, checkInfo.ForceUpgrade)

	// 2. 如果需要升级，执行下载
	if checkInfo.UpgradeEnabled {
		global.Logger.Info("Upgrade enabled, starting download")

		// 创建升级锁
		if err := createUpgradeLock(checkInfo.UpgradeVersion, "checking"); err != nil {
			global.Logger.Sugar().Errorf("Failed to create upgrade lock: %v", err)
			return
		}

		// 更新锁状态为下载中
		updateUpgradeLockStatus("downloading")

		// 检查是否已存在有效的安装包
		if !isValidPackageExists(checkInfo) {

			// 执行下载
			downloadSuccess := performUpgradeDownload(checkInfo)
			if !downloadSuccess {
				global.Logger.Error("Download failed, skipping upgrade")
				cleanupUpgradeLock()
				return
			}
		} else {
			global.Logger.Info("Valid upgrade package already exists")
		}

		// 3. 如果是强制升级，执行安装
		if checkInfo.ForceUpgrade {
			global.Logger.Info("Force upgrade enabled, starting installation")
			// 更新锁状态为安装中
			updateUpgradeLockStatus("installing")
			performUpgradeInstallation(checkInfo)
			// 安装完成后清理锁
			cleanupUpgradeLock()
		} else {
			global.Logger.Info("Non-force upgrade, package downloaded and ready")
			// 更新锁状态为已完成
			updateUpgradeLockStatus("completed")
		}
	}

	// 4. 写入升级查询结果文件（供LightSentinel读取状态）
	if err := writeUpgradeCheckResult(checkInfo); err != nil {
		global.Logger.Sugar().Errorf("Failed to write upgrade check result: %v", err)
	}
}

// checkUpgrade 执行升级查询网络请求
func checkUpgrade() (*UpgradeCheckInfo, error) {
	requester := connection.GetPlatformRequester("https")
	if requester == nil {
		return nil, fmt.Errorf("no platform requester available for https")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	url := connection.GetPlatformURL(constants.CheckUpgradeEndpoint)

	// 构建请求参数，与 LightSentinel 的逻辑保持一致
	platform := getOSType()
	applianceID := ""
	if global.ApplianceID != 0 {
		applianceID = fmt.Sprintf("%d", global.ApplianceID)
	}

	params := map[string]interface{}{
		"beta":         true,
		"corp_id":      "",
		"version":      global.Version,
		"platform":     platform,
		"appliance_id": applianceID,
	}
	body, _ := json.Marshal(params)

	respBody, err := requester.Do(ctx, "POST", url, body, map[string]string{"Content-Type": "application/json"})
	if err != nil {
		return nil, fmt.Errorf("request to upgrade check endpoint failed: %w", err)
	}

	var resp UpgradeCheckResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("failed to parse upgrade check response: %w, body: %s", err, string(respBody))
	}

	if resp.Code != 0 {
		return nil, fmt.Errorf("upgrade check returned non-zero code: %d", resp.Code)
	}

	// 转换为 LightSentinel 兼容的结构
	checkInfo := &UpgradeCheckInfo{
		UpgradeVersion: resp.Data.LatestVersion,
		UpgradeEnabled: resp.Data.NeedUpgrade,
		ForceUpgrade:   resp.Data.ForceUpdate,
		DownloadURL:    resp.Data.DownloadURL,
		PackageMD5:     resp.Data.MD5Sum,
		PackageName:    resp.Data.LatestFilename,
		PackagePath:    "", // 由 LightSentinel 计算
		Timestamp:      time.Now().Unix(),
	}

	return checkInfo, nil
}

// writeUpgradeCheckResult 写入升级查询结果到配置文件
func writeUpgradeCheckResult(checkInfo *UpgradeCheckInfo) error {
	configFile := filepath.Join(utils.GetConfigDir(), constants.UpgradeCheckConfigFile)

	// 读取现有配置进行比较
	if existingData, err := os.ReadFile(configFile); err == nil {
		var existingInfo UpgradeCheckInfo
		if json.Unmarshal(existingData, &existingInfo) == nil {
			// 比较配置内容，避免不必要的写入
			if upgradeCheckInfoEqual(&existingInfo, checkInfo) {
				global.Logger.Debug("Upgrade check result unchanged, skipping file write")
				return nil
			}
		}
	}

	// 序列化配置
	data, err := json.MarshalIndent(checkInfo, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal upgrade check info: %w", err)
	}

	// 原子写入文件
	tempFile := configFile + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write temp file: %w", err)
	}

	if err := os.Rename(tempFile, configFile); err != nil {
		os.Remove(tempFile)
		return fmt.Errorf("failed to rename temp file: %w", err)
	}

	global.Logger.Sugar().Infof("Upgrade check result written to %s", configFile)
	return nil
}

// upgradeCheckInfoEqual 比较两个升级查询结果是否相同
func upgradeCheckInfoEqual(a, b *UpgradeCheckInfo) bool {
	return a.UpgradeVersion == b.UpgradeVersion &&
		a.UpgradeEnabled == b.UpgradeEnabled &&
		a.ForceUpgrade == b.ForceUpgrade &&
		a.DownloadURL == b.DownloadURL &&
		a.PackageMD5 == b.PackageMD5 &&
		a.PackageName == b.PackageName
}

// processUpgradeStatusRequest 处理升级状态上报请求
func processUpgradeStatusRequest(requestFilePath string) {
	// 检查请求文件是否存在
	if _, err := os.Stat(requestFilePath); os.IsNotExist(err) {
		return
	}

	// 读取请求文件
	data, err := os.ReadFile(requestFilePath)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to read upgrade status request file: %v", err)
		return
	}

	// 解析请求
	var request struct {
		LastVersion  string `json:"last_version"`
		NextVersion  string `json:"next_version"`
		Status       string `json:"status"`
		FailedReason string `json:"failed_reason"`
		Timestamp    int64  `json:"timestamp"`
		RequestID    string `json:"request_id"`
	}

	if err := json.Unmarshal(data, &request); err != nil {
		global.Logger.Sugar().Errorf("Failed to parse upgrade status request: %v", err)
		// 删除无效的请求文件
		os.Remove(requestFilePath)
		return
	}

	// 执行状态上报
	if err := reportUpgradeStatus(request.LastVersion, request.NextVersion, request.Status, request.FailedReason); err != nil {
		global.Logger.Sugar().Errorf("Failed to report upgrade status: %v", err)
		// 可以考虑重试机制，这里暂时只记录错误
	}

	// 删除已处理的请求文件
	if err := os.Remove(requestFilePath); err != nil {
		global.Logger.Sugar().Warnf("Failed to remove processed upgrade status request file: %v", err)
	}
}

// reportUpgradeStatus 执行升级状态上报网络请求
func reportUpgradeStatus(lastVersion, nextVersion, status, failedReason string) error {
	requester := connection.GetPlatformRequester("https")
	if requester == nil {
		return fmt.Errorf("no platform requester available for https")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	url := connection.GetPlatformURL(constants.UpgradeStatusEndpoint)

	request := UpgradeStatusRequest{
		LastVersion:  lastVersion,
		NextVersion:  nextVersion,
		ApplianceID:  fmt.Sprintf("%d", global.ApplianceID),
		Platform:     getOSType(),
		Status:       status,
		FailedReason: failedReason,
	}

	body, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal upgrade status request: %w", err)
	}

	respBody, err := requester.Do(ctx, "POST", url, body, map[string]string{"Content-Type": "application/json"})
	if err != nil {
		return fmt.Errorf("request to upgrade status endpoint failed: %w", err)
	}

	var resp UpgradeStatusResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return fmt.Errorf("failed to parse upgrade status response: %w, body: %s", err, string(respBody))
	}

	if resp.Code != 0 {
		return fmt.Errorf("upgrade status report returned non-zero code: %d", resp.Code)
	}

	global.Logger.Sugar().Infof("Successfully reported upgrade status: %s", status)
	return nil
}

// getOSType 获取操作系统类型，与 LightSentinel 的 AsecString::os_type() 保持一致
func getOSType() string {
	switch runtime.GOOS {
	case "windows":
		return "windows"
	case "darwin":
		return "macos"
	case "linux":
		return "linux"
	default:
		return runtime.GOOS
	}
}

// calculateFileMD5 计算文件MD5值
func calculateFileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 使用标准库计算MD5
	hasher := md5.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}

// isValidPackageExists 检查是否已存在有效的升级包
func isValidPackageExists(checkInfo *UpgradeCheckInfo) bool {
	// 构建升级目录路径 - 使用程序目录
	programDir := filepath.Dir(os.Args[0])
	upgradeDir := filepath.Join(programDir, "upgrade")

	// 确保升级目录存在
	if err := os.MkdirAll(upgradeDir, 0755); err != nil {
		global.Logger.Sugar().Errorf("Failed to create upgrade directory: %v", err)
		return false
	}

	// 构建预期的包路径
	expectedPath := filepath.Join(upgradeDir, checkInfo.PackageName)
	checkInfo.PackagePath = expectedPath

	// 检查文件是否存在
	if _, err := os.Stat(expectedPath); os.IsNotExist(err) {
		return false
	}

	// 验证MD5
	actualMD5, err := calculateFileMD5(expectedPath)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to calculate MD5 for existing package: %v", err)
		return false
	}

	if strings.ToLower(actualMD5) == strings.ToLower(checkInfo.PackageMD5) {
		global.Logger.Sugar().Infof("Valid upgrade package already exists: %s", expectedPath)
		return true
	}

	// MD5不匹配，删除无效文件
	global.Logger.Sugar().Warnf("Existing package MD5 mismatch, removing: %s", expectedPath)
	os.Remove(expectedPath)
	return false
}

// performUpgradeDownload 执行升级包下载
func performUpgradeDownload(checkInfo *UpgradeCheckInfo) bool {
	global.Logger.Sugar().Infof("Starting download for upgrade package: %s", checkInfo.DownloadURL)

	// 使用connection包的PlatformRequester进行下载，支持SPA模式
	requester := connection.GetPlatformRequester("https")
	if requester == nil {
		global.Logger.Sugar().Errorf("Failed to get platform requester")
		return false
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	// 执行流式下载
	body, resp, err := requester.Download(ctx, checkInfo.DownloadURL)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to download upgrade package: %v", err)
		return false
	}
	defer body.Close()

	if resp.StatusCode != http.StatusOK {
		global.Logger.Sugar().Errorf("Download failed with status: %d", resp.StatusCode)
		return false
	}

	// 创建目标文件
	file, err := os.Create(checkInfo.PackagePath)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to create package file: %v", err)
		return false
	}
	defer file.Close()

	// 下载文件内容
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to write package file: %v", err)
		os.Remove(checkInfo.PackagePath) // 清理失败的文件
		return false
	}

	global.Logger.Sugar().Infof("Download completed: %s", checkInfo.PackagePath)

	// 验证MD5
	actualMD5, err := calculateFileMD5(checkInfo.PackagePath)
	if err != nil {
		global.Logger.Sugar().Errorf("Failed to calculate MD5: %v", err)
		os.Remove(checkInfo.PackagePath)
		return false
	}

	if strings.ToLower(actualMD5) != strings.ToLower(checkInfo.PackageMD5) {
		global.Logger.Sugar().Errorf("MD5 verification failed. Expected: %s, Got: %s",
			checkInfo.PackageMD5, actualMD5)
		os.Remove(checkInfo.PackagePath)
		return false
	}

	global.Logger.Sugar().Infof("MD5 verification successful: %s", actualMD5)
	return true
}

// performUpgradeInstallation 执行升级安装
func performUpgradeInstallation(checkInfo *UpgradeCheckInfo) {
	global.Logger.Sugar().Infof("Starting upgrade installation: %s", checkInfo.PackagePath)

	// 上报升级开始状态
	reportUpgradeStatus("", checkInfo.UpgradeVersion, "installing", "Starting upgrade installation")

	// 根据操作系统执行不同的安装命令
	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		// Windows安装命令
		cmd = exec.Command(checkInfo.PackagePath, "/S") // 静默安装
	case "linux":
		// Linux安装命令
		if strings.HasSuffix(checkInfo.PackagePath, ".deb") {
			cmd = exec.Command("dpkg", "-i", checkInfo.PackagePath)
		} else if strings.HasSuffix(checkInfo.PackagePath, ".rpm") {
			cmd = exec.Command("rpm", "-U", checkInfo.PackagePath)
		} else {
			// 通用可执行文件
			cmd = exec.Command("chmod", "+x", checkInfo.PackagePath)
			if err := cmd.Run(); err != nil {
				global.Logger.Sugar().Errorf("Failed to make package executable: %v", err)
				reportUpgradeStatus("", checkInfo.UpgradeVersion, "failed", "Failed to make package executable")
				return
			}
			cmd = exec.Command(checkInfo.PackagePath)
		}
	case "darwin":
		// macOS安装命令
		if strings.HasSuffix(checkInfo.PackagePath, ".pkg") {
			cmd = exec.Command("installer", "-pkg", checkInfo.PackagePath, "-target", "/")
		} else {
			cmd = exec.Command(checkInfo.PackagePath)
		}
	default:
		global.Logger.Sugar().Errorf("Unsupported operating system: %s", runtime.GOOS)
		reportUpgradeStatus("", checkInfo.UpgradeVersion, "failed", "Unsupported operating system")
		return
	}

	// 执行安装命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		global.Logger.Sugar().Errorf("Installation failed: %v, Output: %s", err, string(output))
		reportUpgradeStatus("", checkInfo.UpgradeVersion, "failed",
			fmt.Sprintf("Installation failed: %v", err))
		return
	}

	global.Logger.Sugar().Infof("Installation completed successfully. Output: %s", string(output))
	reportUpgradeStatus("", checkInfo.UpgradeVersion, "success", "Upgrade installation completed")

	// 清理安装包（可选）
	if err := os.Remove(checkInfo.PackagePath); err != nil {
		global.Logger.Sugar().Warnf("Failed to remove installation package: %v", err)
	} else {
		global.Logger.Sugar().Infof("Installation package removed: %s", checkInfo.PackagePath)
	}
}

// isUpgradeInProgress 检查是否有升级流程正在进行
func isUpgradeInProgress() bool {
	lockPath := constants.GetUpgradeLockPath()

	// 检查锁文件是否存在
	if _, err := os.Stat(lockPath); os.IsNotExist(err) {
		return false
	}

	// 读取锁文件内容
	data, err := os.ReadFile(lockPath)
	if err != nil {
		global.Logger.Sugar().Warnf("Failed to read upgrade lock file: %v", err)
		return false
	}

	var lock UpgradeLock
	if err := json.Unmarshal(data, &lock); err != nil {
		global.Logger.Sugar().Warnf("Failed to parse upgrade lock file: %v", err)
		// 删除无效的锁文件
		os.Remove(lockPath)
		return false
	}

	// 检查锁是否过期（超过2小时认为过期）
	if time.Now().Unix()-lock.LastUpdate > 7200 {
		global.Logger.Warn("Upgrade lock expired, removing stale lock")
		os.Remove(lockPath)
		return false
	}

	// 检查状态是否为进行中的状态
	switch lock.Status {
	case "checking", "downloading", "installing":
		return true
	case "completed":
		// 已完成的锁可以清理
		os.Remove(lockPath)
		return false
	default:
		return false
	}
}

// createUpgradeLock 创建升级锁
func createUpgradeLock(version, status string) error {
	lock := UpgradeLock{
		Status:     status,
		Version:    version,
		StartTime:  time.Now().Unix(),
		LastUpdate: time.Now().Unix(),
		ProcessID:  os.Getpid(),
	}

	return writeUpgradeLock(&lock)
}

// updateUpgradeLockStatus 更新升级锁状态
func updateUpgradeLockStatus(status string) error {
	lockPath := constants.GetUpgradeLockPath()

	// 读取现有锁文件
	data, err := os.ReadFile(lockPath)
	if err != nil {
		global.Logger.Sugar().Warnf("Failed to read upgrade lock file for update: %v", err)
		return err
	}

	var lock UpgradeLock
	if err := json.Unmarshal(data, &lock); err != nil {
		global.Logger.Sugar().Warnf("Failed to parse upgrade lock file for update: %v", err)
		return err
	}

	// 更新状态和时间戳
	lock.Status = status
	lock.LastUpdate = time.Now().Unix()

	// 如果是安装状态，记录安装开始时间
	if status == "installing" {
		lock.InstallStartTime = time.Now().Unix()
	}

	return writeUpgradeLock(&lock)
}

// writeUpgradeLock 原子写入升级锁文件
func writeUpgradeLock(lock *UpgradeLock) error {
	lockPath := constants.GetUpgradeLockPath()

	// 序列化锁数据
	data, err := json.MarshalIndent(lock, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal upgrade lock: %w", err)
	}

	// 原子写入
	tempFile := lockPath + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write temp upgrade lock file: %w", err)
	}

	if err := os.Rename(tempFile, lockPath); err != nil {
		os.Remove(tempFile)
		return fmt.Errorf("failed to rename temp upgrade lock file: %w", err)
	}

	global.Logger.Sugar().Infof("Upgrade lock updated: status=%s, version=%s", lock.Status, lock.Version)
	return nil
}

// cleanupUpgradeLock 清理升级锁文件
func cleanupUpgradeLock() {
	lockPath := constants.GetUpgradeLockPath()
	if err := os.Remove(lockPath); err != nil && !os.IsNotExist(err) {
		global.Logger.Sugar().Warnf("Failed to cleanup upgrade lock: %v", err)
	} else {
		global.Logger.Info("Upgrade lock cleaned up successfully")
	}
}

// checkIncompleteUpgradeOnStartup 启动时检查未完成的升级流程
func checkIncompleteUpgradeOnStartup() {
	global.Logger.Info("Checking for incomplete upgrade processes on startup")

	lockPath := constants.GetUpgradeLockPath()

	// 检查锁文件是否存在
	if _, err := os.Stat(lockPath); os.IsNotExist(err) {
		global.Logger.Info("No upgrade lock found, no incomplete upgrade to recover")
		return
	}

	// 读取锁文件内容
	data, err := os.ReadFile(lockPath)
	if err != nil {
		global.Logger.Sugar().Warnf("Failed to read upgrade lock file on startup: %v", err)
		// 删除损坏的锁文件
		os.Remove(lockPath)
		return
	}

	var lock UpgradeLock
	if err := json.Unmarshal(data, &lock); err != nil {
		global.Logger.Sugar().Warnf("Failed to parse upgrade lock file on startup: %v", err)
		// 删除无效的锁文件
		os.Remove(lockPath)
		return
	}

	// 检查锁是否过期（超过2小时认为过期）
	if time.Now().Unix()-lock.LastUpdate > 7200 {
		global.Logger.Warn("Found expired upgrade lock on startup, cleaning up")
		os.Remove(lockPath)
		return
	}

	global.Logger.Sugar().Infof("Found incomplete upgrade on startup: version=%s, status=%s, last_update=%d",
		lock.Version, lock.Status, lock.LastUpdate)

	// 根据状态进行恢复处理
	switch lock.Status {
	case "checking":
		global.Logger.Info("Upgrade was in checking state, will resume on next check cycle")
		// 清理锁，让正常检查流程接管
		os.Remove(lockPath)

	case "downloading":
		global.Logger.Info("Upgrade was in downloading state, cleaning up and will retry")
		// 清理可能的不完整下载文件
		cleanupIncompleteDownload(lock.Version)
		os.Remove(lockPath)

	case "installing":
		global.Logger.Info("Upgrade was in installing state, checking installation result")
		// 检查安装是否实际完成
		if isInstallationCompleted(lock.Version) {
			global.Logger.Info("Installation appears to be completed, cleaning up lock")
			os.Remove(lockPath)
		} else {
			global.Logger.Warn("Installation appears incomplete, cleaning up for retry")
			os.Remove(lockPath)
		}

	case "completed":
		global.Logger.Info("Upgrade was marked as completed, cleaning up lock")
		os.Remove(lockPath)

	default:
		global.Logger.Sugar().Warnf("Unknown upgrade status on startup: %s, cleaning up", lock.Status)
		os.Remove(lockPath)
	}
}

// cleanupIncompleteDownload 清理不完整的下载文件
func cleanupIncompleteDownload(version string) {
	upgradeDir := filepath.Join(utils.GetConfigDir(), "upgrade")

	// 遍历升级目录，删除可能的不完整文件
	files, err := os.ReadDir(upgradeDir)
	if err != nil {
		global.Logger.Sugar().Warnf("Failed to read upgrade directory for cleanup: %v", err)
		return
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filePath := filepath.Join(upgradeDir, file.Name())

		// 删除临时文件或不完整的下载文件
		if strings.HasSuffix(file.Name(), ".tmp") ||
			strings.HasSuffix(file.Name(), ".download") ||
			strings.Contains(file.Name(), version) {

			if err := os.Remove(filePath); err != nil {
				global.Logger.Sugar().Warnf("Failed to remove incomplete download file %s: %v", filePath, err)
			} else {
				global.Logger.Sugar().Infof("Cleaned up incomplete download file: %s", filePath)
			}
		}
	}
}

// isInstallationCompleted 检查安装是否已完成
func isInstallationCompleted(version string) bool {
	// 这里可以通过检查当前程序版本或其他方式判断安装是否完成
	// 简单实现：如果升级包文件仍存在，说明安装可能未完成
	upgradeDir := filepath.Join(utils.GetConfigDir(), "upgrade")

	files, err := os.ReadDir(upgradeDir)
	if err != nil {
		return false
	}

	// 如果升级目录中还有对应版本的安装包，可能安装未完成
	for _, file := range files {
		if strings.Contains(file.Name(), version) &&
			(strings.HasSuffix(file.Name(), ".exe") ||
				strings.HasSuffix(file.Name(), ".msi") ||
				strings.HasSuffix(file.Name(), ".pkg") ||
				strings.HasSuffix(file.Name(), ".deb") ||
				strings.HasSuffix(file.Name(), ".rpm")) {
			return false // 安装包还在，可能未完成安装
		}
	}

	return true // 安装包已被清理，可能安装完成
}
