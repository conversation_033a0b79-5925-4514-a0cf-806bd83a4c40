package common

import (
	"net/http"
	"strings"

	"asdsec.com/asec/platform/pkg/aerrors"

	corego "asdsec.com/asec/platform/app/console/global"
	"github.com/gin-gonic/gin"
)

const (
	OperateSuccessCode = 0
	OperateFailCode    = -1
)

const (
	Success                  = "Success"
	OperateError             = "OperateError"
	ParamInvalidError        = "ParamInvalidError"
	UnknownError             = "UnknownError"
	DBOperateError           = "DBOperateError"
	CsrfTokenValidateError   = "CsrfTokenValidateError"
	PingOssError             = "PingOssError"
	PermissionError          = "PermissionError"
	AuthFailedError          = "AuthFailedError"
	AuthorizationError       = "AuthorizationError"
	AuthorizationExpireError = "AuthorizationExpireError"
)
const ImportAppExcelLen = 12

const (
	ImportAppType = iota
	ImportAppName
	ImportAppStatus
	ImportAppGateway
	ImportAppServerAddress
	ImportAppPublishAddress
	ImportAppUri
	ImportAppSmartRewrite
	ImportAppDependSite
	ImportAppShowStatus
	ImportAppPortalShowName
	ImportAppPortalDesc
)

const (
	AppStatusEnable      = "启用"
	AppStatusMaintenance = "维护"
	AppStatusDisable     = "禁用"
)

const (
	CreateSensitiveCategoryErr = "CreateSensitiveCategoryErr"
	UpdateSensitiveCategoryErr = "UpdateSensitiveCategoryErr"
	DelSensitiveCategoryErr    = "DelSensitiveCategoryErr"
)

const ( // 密码异常提示
	PasswordLengthError     = "PasswordLengthError"
	PasswordHanziError      = "PasswordHanziError"
	PasswordWidthCharError  = "PasswordWidthCharError"
	PasswordBlankError      = "PasswordBlankError"
	PasswordWeak            = "PasswordWeak"
	PasswordNeedStrength    = "PasswordNeedStrength"
	PasswordNotCorrect      = "PasswordNotCorrect"
	PasswordContainUserName = "PasswordContainUserName"
	PasswordUsed            = "PasswordUsed"
)

const (
	DeleteAdminFail  = "DeleteAdminFiled"
	CreateAdminFail  = "CreateAdminFailed"
	GetAdminListFail = "GetAdminListFailed"
	GetAdminFail     = "GetAdminFailed"
)

const (
	OprLogExcelName = "OprLogExcelName"
)

const (
	QueryNotifyErr  = "QueryNotifyErr"
	AddNotifyErr    = "AddNotifyErr"
	EditNotifyErr   = "EditNotifyErr"
	DelNotifyErr    = "DelNotifyErr"
	NotifyExit      = "NotifyExit"
	RobotNotExitErr = "RobotNotExitErr"
	SendDingTalkErr = "SendDingTalkErr"
	SendWxErr       = "SendWxErr"
	QuerySettingErr = "QuerySettingErr"
	EditSettingErr  = "EditSettingErr"
	IllegalAddrErr  = "IllegalAddrErr"
)

const ( //设备管理
	QueryDeviceErr = "QueryDeviceErr"
)

const ( // 系统管理
	CidrInvalidError = "CidrInvalidError"
)

const ( // 调查分析
	DeleteHistoryError = "DeleteHistoryError"
	AddHistoryError    = "AddHistoryError"
	GetHistoryError    = "GetHistoryError"
	GetEventsError     = "GetEventsError"
	GetConditionError  = "GetConditionError"
)

const (
	UpsetAgentModuleSwitchErr = "UpsetAgentModuleSwitchErr"
	UpdateModuleSwitchErr     = "UpdateModuleSwitchErr"
	GetModuleSwitchErr        = "GetModuleSwitchErr"
)

const ( //应用管理
	AddAppErr           = "AddAppErr"
	AddAppGroupErr      = "AddAppGroupErr"
	QueryAppGroupErr    = "QueryGroupError"
	AppAddrRepeat       = "AppAddrRepeat"
	AppNameRepeat       = "AppNameRepeat"
	AppNameRepeatDetail = "AppNameRepeatDetail"
	GateWayEmpty        = "GateWayEmpty"
	TemplateError       = "TemplateError"
	AppAndPolicyAss     = "AppAndPolicyAss"
	AppDelErr           = "AppDelErr"
	UpdateAppErr        = "UpdateAppErr"
)

const ( //告警事件
	QueryOutboundWayErr = "QueryOutboundWayErr"
	QueryDataTypeErr    = "QueryDataTypeErr"
)

const ( //敏感数据策略
	QuerySensitiveElemTagErr         = "QuerySensitiveElemTagErr"
	AddSensitiveElemTagErr           = "AddSensitiveElemTagErr"
	DeleteSensitiveElemTagErr        = "DeleteSensitiveElemTagErr"
	QuerySensitiveElemErr            = "QuerySensitiveElemErr"
	AddSensitiveElemErr              = "AddSensitiveElemErr"
	DeleteSensitiveElemErr           = "DeleteSensitiveElemErr"
	DeleteSensitiveElemAssocErr      = "DeleteSensitiveElemAssocErr"
	ChangeSensitiveElemErr           = "ChangeSensitiveElemErr"
	SensitiveElemTotalErr            = "SensitiveElemTotalErr"
	QuerySensitiveStrategyErr        = "QuerySensitiveStrategyErr"
	AddSensitiveStrategyErr          = "AddSensitiveStrategyErr"
	DeleteSensitiveStrategyErr       = "DeleteSensitiveStrategyErr"
	ChangeSensitiveStrategyErr       = "ChangeSensitiveStrategyErr"
	QueryDefaultSensitiveStrategyErr = "QueryDefaultSensitiveStrategyErr"
	CheckAddStrategyReqError         = "CheckAddStrategyReqError"
	CheckAddStrategyReqRequiredError = "CheckAddStrategyReqRequiredError"
	CheckFileNameRuleErr             = "CheckFileNameRuleErr"
)

const ( //DLP策略
	CreateDlpStrategyErr = "CreateDlpStrategyErr"
	UpdateDlpStrategyErr = "UpdateDlpStrategyErr"
)

const ( // 策略
	StrategyNameRepeat = "StrategyNameRepeat"
)

const ( // ueba 策略
	UebaStrategyNameRepeat     = "UebaStrategyNameRepeat"
	UebaStrategyEditNotAllowed = "UebaStrategyEditNotAllowed"
)

const ( // 下载安装
	GetFileFromServerErr = "GetFileFromServer"
	GetFileFromOssErr    = "GetFileFromOss"
	FileParseError       = "FileParseError"
	FileCheckError       = "FileCheckError"
	GetDeployChannelErr  = "GetDeployChannelErr"
)

const ( //行为分析
	QueryIncidentListErr       = "QueryIncidentListErr"
	QueryUEBAStrategySumErr    = "QueryUEBAStrategySumErr"
	QueryEventStateSumErr      = "QueryEventStateSumErr"
	QueryUserTopErr            = "QueryUserTopErr"
	ChangeIncidentStateErr     = "ChangeIncidentStateErr"
	QueryIncidentSummaryErr    = "QueryIncidentSummaryErr"
	QueryIncidentPartialSumErr = "QueryIncidentPartialSumErr"
	IncidentExportErr          = "IncidentExportErr"
)

const ( //平台地址
	CreatePlatformIPInfoErr = "CreatePlatformIPInfoErr"
	ChangePlatformIPInfoErr = "ChangePlatformIPInfoErr"
	DeletePlatformIPInfoErr = "DeletePlatformIPInfoErr"
)

const ( //密钥
	QueryES256KeyErr = "QueryES256KeyErr"
)

const ( //系统版本
	QuerySystemVersionErr = "QuerySystemVersionErr"
	QueryLicenseInfoErr   = "QueryLicenseInfoErr"
)

const ( //首页
	IncidentTypeTopNErr = "IncidentTypeTopNErr"
	SendTopNErr         = "SendTopNErr"
)

const (
	FileUploadError = "FileUploadError"
	FileSizeError   = "FileSizeError"
	FileTypeError   = "FileTypeError"
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data"`
}

func CreateResponse(c *gin.Context, code int, msg string, data interface{}) {
	var response Response

	response.Code = code
	response.Data = data
	response.Message = msg

	c.JSON(
		http.StatusOK,
		response,
	)
}

func Create401Response(c *gin.Context, code int, msg string, data interface{}) {
	var response Response

	response.Code = code
	response.Data = data
	response.Message = msg

	c.JSON(
		http.StatusUnauthorized,
		response,
	)
}

func Create403Response(c *gin.Context, code int, msg string, data interface{}) {
	var response Response

	response.Code = code
	response.Data = data
	response.Message = msg

	c.JSON(
		http.StatusForbidden,
		response,
	)
}

func CreateStatusResponse(c *gin.Context, httpStatus int, code int, msg string, data interface{}) {
	var response Response

	response.Code = code
	response.Data = data
	response.Message = msg

	c.JSON(
		httpStatus,
		response,
	)
}

func Ok(c *gin.Context) {
	CreateResponse(c, OperateSuccessCode, corego.GetResource(c, Success), map[string]interface{}{})
}

func OkWithMessage(messageID string, c *gin.Context) {
	CreateResponse(c, OperateSuccessCode, corego.GetResource(c, messageID), map[string]interface{}{})
}

func OkWithData(c *gin.Context, data interface{}) {
	CreateResponse(c, OperateSuccessCode, corego.GetResource(c, Success), data)
}

func OkWithMessageAndData(c *gin.Context, message string, data interface{}) { // todo后续考虑国际化
	CreateResponse(c, OperateSuccessCode, message, data)
}

func OkWithDetailed(c *gin.Context, data interface{}, messageID string) {
	CreateResponse(c, OperateSuccessCode, corego.GetResource(c, messageID), data)
}

func OkWithResponse(c *gin.Context, message string) {
	CreateResponse(c, OperateSuccessCode, message, map[string]interface{}{})
}

func Fail(c *gin.Context, messageID string) {
	CreateResponse(c, OperateFailCode, corego.GetResource(c, messageID), map[string]interface{}{})
}

func FailAError(c *gin.Context, aError aerrors.AError) {
	CreateResponse(c, OperateFailCode, corego.GetResource(c, aError.MessageId()), map[string]interface{}{})
}

func FailMulti(c *gin.Context, messageIDs string, sep string) {
	msgList := strings.Split(messageIDs, sep)
	var transMsgList []string
	for i := 0; i < len(msgList); i++ {
		transMsgList = append(transMsgList, corego.GetResourceIfCan(c, msgList[i], false))
	}
	msg := strings.Join(transMsgList, "")
	CreateResponse(c, OperateFailCode, msg, map[string]interface{}{})
}

func FailFormat(c *gin.Context, messageID string, key string, value interface{}) {
	CreateResponse(c, OperateFailCode, corego.GetResourceFormat(c, messageID, key, value), map[string]interface{}{})
}

func FailFormatMap(c *gin.Context, messageID string, templateData map[string]interface{}) {
	CreateResponse(c, OperateFailCode, corego.GetResourceFormatMap(c, messageID, templateData), map[string]interface{}{})
}

func FailWithMessage(c *gin.Context, code int, message string) {
	CreateResponse(c, code, message, map[string]interface{}{})
}

func FailWithData(c *gin.Context, messageID string, data interface{}) {
	CreateResponse(c, OperateFailCode, corego.GetResource(c, messageID), data)
}

func Fail401(c *gin.Context, messageID string) {
	Create401Response(c, OperateFailCode, corego.GetResource(c, messageID), map[string]interface{}{})
}

func Fail403(c *gin.Context, messageID string) {
	Create403Response(c, OperateFailCode, corego.GetResource(c, messageID), map[string]interface{}{})
}

// FailDownload 这里违背了RFC 9110,使用444 用作与前端约定的 下载失败错误码
func FailDownload(c *gin.Context, messageID string) {
	CreateStatusResponse(c, 444, OperateFailCode, corego.GetResource(c, messageID), map[string]interface{}{})
}
