package service

import (
	"asdsec.com/asec/platform/app/console/app/ddr_source/ddr_const"
	"asdsec.com/asec/platform/app/console/app/ddr_source/dto"
	"asdsec.com/asec/platform/app/console/app/ddr_source/repository"
	"asdsec.com/asec/platform/app/console/app/ddr_source/vo"
	global "asdsec.com/asec/platform/app/console/global"
	"asdsec.com/asec/platform/pkg/aerrors"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"sync"
)

var DdrSourceServiceImpl DdrSourceService

var DdrSourceServiceInit sync.Once

type ddrSourceService struct {
	db repository.DdrSourceRepository
}

// CheckSensitiveQuote 这里error 不为nil 的情况下,回传的[]string 也不为nil,返回给前端展示
func (d ddrSourceService) CheckSensitiveQuote(c *gin.Context, req vo.DelSourceReq) ([]string, error) {
	return d.db.CheckSensitiveQuote(c, req)
}

func (d ddrSourceService) DetailSource(c *gin.Context, sourceId string) (vo.DetailSourceResp, error) {
	resp := vo.DetailSourceResp{}
	detailSourceDto, err := d.db.DetailSource(c, sourceId)
	if err != nil {
		return resp, err
	}
	resp = detailDtoTrans(detailSourceDto)
	return resp, nil
}

func detailDtoTrans(sourceDto dto.SourceListDto) vo.DetailSourceResp {
	resp := vo.DetailSourceResp{
		Id: sourceDto.Id,
	}
	resp.SourceName = sourceDto.SourceName
	resp.SourceType = sourceDto.SourceType
	resp.GitUrl = sourceDto.GitUrl
	resp.IncludeFilePath = sourceDto.IncludeFilePath
	resp.ExcludeFilePath = sourceDto.ExcludeFilePath
	resp.Status = sourceDto.Status
	var _softList []vo.SoftwareInfo
	pL := []string(sourceDto.ProcessName)
	sL := []string(sourceDto.SoftwareName)
	for i := range pL {
		_softList = append(_softList, vo.SoftwareInfo{
			SoftwareName: sL[i],
			ProcessName:  pL[i],
		})
	}

	var _infos []vo.WebInfo
	aL := []string(sourceDto.UrlAddr)
	upL := []string(sourceDto.UrlPort)
	rL := []string(sourceDto.UrlRoute)
	for i := range aL {
		_infos = append(_infos, vo.WebInfo{
			UrlAddr:  aL[i],
			UrlPort:  upL[i],
			UrlRoute: rL[i],
		})
	}
	resp.WebInfos = _infos
	resp.SoftwareInfos = _softList
	return resp
}

func (d ddrSourceService) SourceListQuote(c *gin.Context, search string) ([]vo.SourceListQuote, error) {
	return d.db.SourceListQuote(c, search)
}

func (d ddrSourceService) SourceList(c *gin.Context, req vo.SourceListReq) (vo.SourceListResp, error) {
	sourceList, err := d.db.SourceList(c, req)
	resp := vo.SourceListResp{}
	if err != nil {
		return resp, err
	}
	resp.TotalNum = sourceList.TotalNum
	resp.PageSize = sourceList.PageSize
	resp.CurrentPage = sourceList.CurrentPage
	var _voList []vo.SourceListData
	listData := sourceList.SourceListData
	for _, dto := range listData {
		_voList = append(_voList, vo.SourceListData{
			Id:         dto.Id,
			SourceName: dto.SourceName,
			SourceType: dto.SourceType,
			SourceInfo: getSourceInfo(dto), // 处理 不同类型来源信息
			Status:     dto.Status,
		})
	}
	resp.SourceListData = _voList
	return resp, nil
}

func getSourceInfo(dto dto.SourceListDto) []string {
	// SourceInfo 列表
	var sis []string
	switch dto.SourceType {
	case ddr_const.SourceGit:
		sis = append(sis, dto.GitUrl)
		return sis
	case ddr_const.SourceSoftware:
		// nL := []string(dto.SoftwareName)
		pL := []string(dto.ProcessName)
		for _, p := range pL {
			sis = append(sis, p)
		}
		return sis
	case ddr_const.SourceWeb:
		aL := []string(dto.UrlAddr)
		pL := []string(dto.UrlPort)
		rL := []string(dto.UrlRoute)
		for i := range aL {
			sis = append(sis, fmt.Sprintf("%s:%s%s", aL[i], pL[i], rL[i]))
		}
		return sis
	default:
		global.SysLog.Error("miss source type to getSourceInfo", zap.Any("type:", dto.SourceType))
		return sis
	}
}

func (d ddrSourceService) UpdateSource(c *gin.Context, req vo.UpdateSourceReq) error {
	return d.db.UpdateSource(c, req)
}

func (d ddrSourceService) DelSource(c *gin.Context, req vo.DelSourceReq) error {
	if len(req.Id) == 0 {
		return errors.New("err param")
	}
	return d.db.DelSource(c, req)
}

func (d ddrSourceService) CheckParam(req vo.CreateSourceReq) aerrors.AError {
	/*if req.SourceType == "software" {
		softwareInfos := req.SoftwareInfos
		for _, info := range softwareInfos {
			// 进程名不能和下载支持浏览器和vcs 相同
			if strings.Contains(info.ProcessName, ddr_const.Git) ||
				strings.Contains(info.ProcessName, ddr_const.Svn) ||
				strings.Contains(info.ProcessName, ddr_const.Chrome) ||
				strings.Contains(info.ProcessName, ddr_const.Edge) {
				return errors.New("unsupported process name")
			}
		}
	}*/
	return nil
}

func (d ddrSourceService) CreateSource(c *gin.Context, req vo.CreateSourceReq) error {
	return d.db.CreateSource(c, req)
}

type DdrSourceService interface {
	CreateSource(c *gin.Context, req vo.CreateSourceReq) error
	CheckParam(req vo.CreateSourceReq) aerrors.AError
	DelSource(c *gin.Context, req vo.DelSourceReq) error
	UpdateSource(c *gin.Context, req vo.UpdateSourceReq) error
	SourceList(c *gin.Context, req vo.SourceListReq) (vo.SourceListResp, error)
	SourceListQuote(c *gin.Context, search string) ([]vo.SourceListQuote, error)
	DetailSource(c *gin.Context, sourceId string) (vo.DetailSourceResp, error)
	CheckSensitiveQuote(c *gin.Context, req vo.DelSourceReq) ([]string, error)
}

func GetDdrSourceService() DdrSourceService {
	DdrSourceServiceInit.Do(func() {
		DdrSourceServiceImpl = ddrSourceService{db: repository.NewDdrSourceRepository()}
	})
	return DdrSourceServiceImpl
}
