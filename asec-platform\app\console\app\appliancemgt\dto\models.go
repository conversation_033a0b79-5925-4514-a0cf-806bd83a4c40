package dto

import (
	"errors"
	"time"

	"asdsec.com/asec/platform/app/console/app/appliancemgt/common"

	"github.com/lib/pq"
)

const TableNameTbAgentUpgradePolicy = "tb_agent_upgrade_policy"

type TbAgentUpgradePolicy struct {
	ID               string    `gorm:"column:id;primaryKey" json:"id"`
	CorpID           string    `gorm:"column:corp_id;not null" json:"corp_id"`
	Mode             string    `gorm:"column:mode;not null;default:'silence'::character varying" json:"mode"`
	Platform         string    `gorm:"column:platform;not null" json:"platform"`
	ConcurrencyCount int32     `gorm:"column:concurrency_count;not null" json:"concurrency_count"`
	LatestVersion    string    `gorm:"column:latest_version;not null" json:"latest_version"`
	GrayEnable       bool      `gorm:"column:gray_enable;not null" json:"gray_enable"`
	GrayPolicyID     string    `gorm:"column:gray_policy_id" json:"gray_policy_id"`
	UpgradeTime      time.Time `gorm:"column:upgrade_time" json:"upgrade_time"`
	FailedLimit      int32     `gorm:"column:failed_limit;not null;default:5" json:"failed_limit"`
	TaskTimeOutSecs  int32     `gorm:"column:task_time_out_secs;not null;default:300" json:"task_time_out_secs"`
	UpdatedAt        time.Time `gorm:"column:updated_at" json:"updated_at"`
	CreatedAt        time.Time `gorm:"column:created_at" json:"created_at"`
}

// TableName TbAgentUpgradePolicy's table name
func (p *TbAgentUpgradePolicy) TableName() string {
	return TableNameTbAgentUpgradePolicy
}

func (p *TbAgentUpgradePolicy) ToUpgradePolicyDetailResp() (GetUpgradePolicyDetailResp, error) {
	if p == nil {
		return GetUpgradePolicyDetailResp{}, errors.New("policy is nil")
	}
	var result GetUpgradePolicyDetailResp
	result.ID = p.ID
	result.LatestVersion = p.LatestVersion
	if !p.UpgradeTime.IsZero() {
		result.UpgradeTime = p.UpgradeTime.Local().Format(common.CommonTimeFormat)
	} else {
		result.UpgradeTime = p.CreatedAt.Local().Format(common.CommonTimeFormat)
	}
	result.UpgradeMode = p.Mode
	result.ConcurrencyCount = p.ConcurrencyCount
	result.GrayEnable = p.GrayEnable
	return result, nil
}

const TableNameTbAgentUpgradeGrayPolicy = "tb_agent_upgrade_gray_policy"

type TbAgentUpgradeGrayPolicy struct {
	ID              string         `gorm:"column:id;primaryKey" json:"id"`
	Mode            string         `gorm:"column:mode;not null" json:"mode"`
	RandomCount     int32          `gorm:"column:random_count" json:"random_count"`
	GroupIds        pq.StringArray `gorm:"column:group_ids;type:text[]" json:"group_ids"`
	UserIds         pq.StringArray `gorm:"column:user_ids;type:text[]" json:"user_ids"`
	AgentIds        pq.Int64Array  `gorm:"column:agent_ids;type:bigint[]" json:"agent_ids"`
	Duration        int32          `gorm:"column:duration" json:"duration"`
	DurationUnit    string         `gorm:"column:duration_unit" json:"duration_unit"`
	StartTime       time.Time      `gorm:"column:start_time" json:"start_time"`
	EndTime         time.Time      `gorm:"column:end_time" json:"end_time"`
	UpgradePolicyID string         `gorm:"column:upgrade_policy_id;not null" json:"upgrade_policy_id"`
	CreatedAt       time.Time      `gorm:"column:created_at" json:"created_at"`
	UpdatedAt       time.Time      `gorm:"column:updated_at" json:"updated_at"`
}

// TableName TbAgentUpgradeGrayPolicy's table name
func (*TbAgentUpgradeGrayPolicy) TableName() string {
	return TableNameTbAgentUpgradeGrayPolicy
}

const TableNameTbAgentUpgradeLog = "tb_agent_upgrade_log"

// TbAgentUpgradeLog mapped from table <tb_agent_upgrade_log>
type TbAgentUpgradeLog struct {
	ID           int64     `gorm:"column:id;primaryKey" json:"id"`
	PolicyID     string    `gorm:"column:policy_id;not null" json:"policy_id"`
	AgentID      int64     `gorm:"column:agent_id;not null" json:"agent_id"`
	Platform     string    `gorm:"column:platform;not null" json:"platform"`
	Status       string    `gorm:"column:status;not null" json:"status"`
	LastVersion  string    `gorm:"column:last_version" json:"last_version"`
	NextVersion  string    `gorm:"column:next_version;not null" json:"next_version"`
	FailedReason string    `gorm:"column:failed_reason" json:"failed_reason"`
	FailedSource string    `gorm:"column:failed_source" json:"failed_source"`
	IsGray       bool      `gorm:"column:is_gray" json:"is_gray"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName TbAgentUpgradeLog's table name
func (*TbAgentUpgradeLog) TableName() string {
	return TableNameTbAgentUpgradeLog
}

type UploadLogReq struct {
	ApplianceId uint64 `json:"appliance_id,string"`
	Cmd         string `json:"cmd"`
}
