<template>
  <div class="access-main">
    <ul class="content-wrapper">
      <li class="access-proxy-status" :class="connectionStatus">
        <span class="access-proxy-status-text">
          连接状态
        </span>
        <span class="access-proxy-status-span">
          <span class="access-proxy-status-tips">
            <template v-if="connectionStatus === 'connected'">
              <img src="@/assets/success.svg" alt="成功" class="success-icon">
              已建立安全连接
            </template>
            <template v-else>
              点击连接，即可安全便捷地访问应用
            </template>
          </span>
          <base-button
            class="access-proxy-status-btn"
            :class="getButtonClass()"
            :style="getButtonStyle()"
            @click="handleConnect"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
          >
            <span v-if="connectionStatus === 'connecting' || connectionStatus === 'disconnecting'" class="loading-icon">
              <svg class="spinner" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                  <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite" />
                  <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite" />
                </circle>
              </svg>
            </span>
            {{ getButtonText() }}
          </base-button>
        </span>
      </li>
      <li v-if="showAccessStatus" class="access-common-status">
        <span class="access-common-status-span">
          <span>准入状态（企业网络下使用）：</span>
          <span
            :style="{ color: deviceStatus ? '#29cc65' : '#ef4444', marginLeft: '8px' }"
          >
            {{ deviceStatus ? '已入网' : '未入网' }}
          </span>
          <!--<span>（请重新建立连接）</span>-->
        </span>
        <span class="access-common-status-detail" @click="handleViewDetails">
          <span>查看详情</span>
        </span>
      </li>
      <li class="access-app" :class="{ 'access-app-no-access': !showAccessStatus }">
        <AppPage
          class="access-app-page"
          :is-connected="connectionStatus === 'connected'"
        />
      </li>
    </ul>
  </div>
</template>

<script>
import AppPage from '@/view/app/index.vue'
import { Message, Loading } from '@/components/base'
import agentApi from '@/api/agentApi'
import { useUserStore } from '@/pinia/modules/user'
import { useRouter } from 'vue-router'
import { emitter } from '@/utils/bus.js'

export default {
  name: 'Access',
  components: {
    AppPage
  },
  data() {
    const userStore = useUserStore()
    const router = useRouter()
    return {
      userStore,
      router,
      connectionStatus: 'disconnected', // 'disconnected', 'connecting', 'connected', 'disconnecting'
      isHovering: false,
      loadingInstance: null, // 全局loading实例
      statusPollingTimer: null, // 状态轮询定时器
      pollingTimeout: null, // 轮询超时定时器
      showAccessStatus: false, // 是否显示接入状态区域
      deviceStatus: false, // 设备状态：true=已入网，false=未入网
      autoReconnectEnabled: true, // 是否启用自动重连
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 2, // 最大重连尝试次数
      reconnectTimer: null, // 重连定时器
      agentStatusChangedHandler: null // 准入状态变更事件处理器引用
    }
  },
  watch: {
    // 监听路由变化，当从其他页面切换回来时，检查是否需要刷新状态和恢复轮询
    '$route': {
      async handler(to, from) {
        // 只有当从其他页面切换回主页面时才处理
        if (to.path && to.path.includes('main') && from && from.path !== to.path) {
          const cachedState = this.userStore.tunState

          logger.log('路由切换回主页面，检查状态缓存...', cachedState)

          // 如果缓存状态无效，则刷新状态
          if (!cachedState || cachedState === 0) {
            logger.log('状态缓存无效，刷新连接状态...')
            await this.updateTunnelStatus({
              useCache: false,
              clearPolling: false,
              restorePolling: false,
              showSuccessMessage: false,
              logPrefix: '路由切换状态刷新'
            })
          } else if (cachedState === 101 || cachedState === 103) {
            // 如果是连接中或断开中的临时状态，需要特殊处理
            logger.log('检测到中间状态，验证真实状态并恢复轮询...')
            try {
              const response = await this.updateTunnelStatus({
                useCache: false,
                clearPolling: false,
                restorePolling: false,
                showSuccessMessage: false,
                logPrefix: '验证中间状态'
              })

              if (response && response.TunState !== undefined) {
                const realState = response.TunState
                logger.log('真实状态:', realState, '缓存状态:', cachedState)

                // 如果真实状态仍然是中间状态，恢复对应的轮询
                if (realState === cachedState) {
                  if (cachedState === 101) {
                    logger.log('恢复连接状态轮询...')
                    this.startConnectPolling()
                  } else if (cachedState === 103) {
                    logger.log('恢复断开连接状态轮询...')
                    this.startDisconnectPolling()
                  }
                }
              }
            } catch (error) {
              console.error('验证状态失败:', error)
              // 验证失败时，将中间状态重置为稳定状态
              if (cachedState === 101) {
                this.updateConnectionStatus(102) // 连接中失败，重置为断开
              } else if (cachedState === 103) {
                this.updateConnectionStatus(100) // 断开中失败，重置为连接
              }
            }
          } else {
            logger.log('使用有效的状态缓存，跳过刷新')
            // 即使使用缓存，也要确保状态正确显示
            this.updateConnectionStatus(cachedState)
          }
        }
      },
      immediate: false
    }
  },
  async mounted() {
    logger.log('获取用户信息')
    // 获取用户信息
    this.userStore.GetUserInfo()
    // 页面加载时获取初始状态
    await this.loadInitialStatus()
    // 页面加载时获取接入状态
    await this.loadAccessStatus()

    // 检查当前状态是否为中间状态，如果是则恢复轮询
    this.checkAndRestorePolling()

    // 检查是否需要自动连接
    this.checkAutoConnect()

    // 监听来自app组件的隧道状态刷新事件
    emitter.on('refreshTunnelStatus', this.handleTunnelStatusRefresh)

    // 监听准入状态变更事件
    this.setupAgentStatusListener()
  },
  beforeUnmount() {
    // 组件销毁时清理定时器
    this.clearPolling()

    // 清理重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    // 清理重连状态
    this.reconnectAttempts = 0

    // 移除事件监听
    emitter.off('refreshTunnelStatus', this.handleTunnelStatusRefresh)

    // 清理准入状态变更事件监听
    this.cleanupAgentStatusListener()
  },
  methods: {
    // 通用的隧道状态更新方法
    async updateTunnelStatus(options = {}) {
      const {
        useCache = true, // 是否使用缓存
        clearPolling = false, // 是否清理当前轮询
        restorePolling = false, // 是否恢复轮询（针对中间状态）
        showSuccessMessage = false, // 是否显示成功消息
        logPrefix = '更新隧道状态' // 日志前缀
      } = options

      try {
        logger.log(`${logPrefix}...`)

        let response

        if (useCache) {
          // 首先尝试从 userStore 获取缓存的数据
          response = { TunState: this.userStore.tunState }
          logger.log('缓存状态:', response.TunState)

          // 如果 userStore 中没有数据或数据无效，才调用 API 获取
          if (!response.TunState || response.TunState === 0) {
            logger.log('缓存无效，调用 API 获取最新状态...')
            response = await agentApi.getChannelStatus()
          } else {
            logger.log('使用缓存状态数据')
          }
        } else {
          // 强制从API获取最新状态（绕过缓存）
          logger.log('强制从API获取最新状态...')
          response = await agentApi.getChannelStatus()
        }

        logger.log('隧道状态响应:', response)

        if (response && response.TunState !== undefined) {
          // 如果需要清理轮询
          if (clearPolling) {
            this.clearPolling()
          }

          // 更新连接状态
          this.updateConnectionStatus(response.TunState)

          // 如果需要恢复轮询且状态为中间状态，重新启动轮询
          if (restorePolling) {
            if (response.TunState === 101) {
              logger.log('检测到连接中状态，重新启动连接轮询...')
              this.startConnectPolling()
            } else if (response.TunState === 103) {
              logger.log('检测到断开中状态，重新启动断开轮询...')
              this.startDisconnectPolling()
            }
          }

          logger.log('隧道状态已更新为:', response.TunState)

          // 显示成功消息
          if (showSuccessMessage) {
            Message.success('状态已刷新')
          }

          return response
        }

        return null
      } catch (error) {
        console.error(`${logPrefix}失败:`, error)

        // 只有在需要显示成功消息时才显示错误消息（避免初始化时显示错误）
        if (showSuccessMessage) {
          Message.error('刷新状态失败，请重试')
        }

        // 如果是初始化失败，设置默认断开状态
        if (useCache) {
          this.updateConnectionStatus(102)
        }

        throw error
      }
    },

    // 加载初始状态（同时获取登录状态和隧道状态）
    async loadInitialStatus() {
      try {
        const response = await this.updateTunnelStatus({
          useCache: true,
          clearPolling: false,
          restorePolling: false,
          showSuccessMessage: false,
          logPrefix: '加载通道初始状态'
        })

        // 处理登录状态（如果需要的话，可以在这里添加登录状态的处理逻辑）
        if (response && response.Token) {
          logger.log('获取到登录token信息')
        }
      } catch (error) {
        // 错误已在updateTunnelStatus中处理
      }
    },

    // 加载接入状态
    async loadAccessStatus() {
      try {
        logger.log('加载接入状态...')
        const response = await agentApi.getAccessStatus()
        logger.log('接入状态响应:', response)

        if (response) {
          // 根据IsShowAccess字段决定是否显示接入状态区域
          this.showAccessStatus = response.IsShowAccess === 1

          // 根据DeviceStatus字段设置设备状态
          this.deviceStatus = response.DeviceStatus === 1

          logger.log('接入状态设置:', {
            showAccessStatus: this.showAccessStatus,
            deviceStatus: this.deviceStatus
          })
        }
      } catch (error) {
        console.error('获取接入状态失败:', error)
        // 获取状态失败时默认隐藏接入状态区域
        this.showAccessStatus = false
        this.deviceStatus = false
      }
    },

    // 根据TunState更新连接状态
    updateConnectionStatus(tunState) {
      logger.log('更新连接状态，TunState:', tunState, '当前状态:', this.connectionStatus)

      // 同步更新用户存储缓存
      this.userStore.setTunState(tunState)

      switch (tunState) {
        case 100: // 已连接
          this.connectionStatus = 'connected'
          // 连接成功时重置重连相关状态
          this.reconnectAttempts = 0
          if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer)
            this.reconnectTimer = null
          }
          break
        case 101: // 连接中
          this.connectionStatus = 'connecting'
          break
        case 102: // 已断开
          this.connectionStatus = 'disconnected'
          // this.stopLoading() // 不再使用全屏Loading
          break
        case 103: // 断开中
          this.connectionStatus = 'disconnecting'
          break
        default:
          console.warn('未知的隧道状态:', tunState)
          this.connectionStatus = 'disconnected'
          userStore.setTunState(102) // 未知状态默认为断开
      }
    },

    // 处理自动重连
    handleAutoReconnect() {
      // 检查是否超过最大重连次数
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        logger.log('已达到最大重连次数，停止自动重连')
        this.reconnectAttempts = 0
        return
      }

      this.reconnectAttempts++
      logger.log(`开始第${this.reconnectAttempts}次自动重连...`)

      // 延迟重连，避免频繁重连
      const delay = Math.min(2000 * this.reconnectAttempts, 10000) // 递增延迟，最多10秒

      this.reconnectTimer = setTimeout(async() => {
        try {
          logger.log(`执行第${this.reconnectAttempts}次自动重连`)
          // 调用连接方法
          await this.connect()
        } catch (error) {
          console.error(`第${this.reconnectAttempts}次自动重连失败:`, error)
          // 如果还没有达到最大重连次数，会在下次状态变为102时继续重连
          if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            // Message.error('自动重连失败，请手动重新连接')
            this.reconnectAttempts = 0
          }
        }
      }, delay)
    },

    // 检查并恢复轮询（用于mounted和路由切换时）
    checkAndRestorePolling() {
      const status = this.connectionStatus
      logger.log('检查当前状态是否需要恢复轮询:', status)

      if (status === 'connecting') {
        logger.log('检测到连接中状态，恢复连接轮询...')
        this.startConnectPolling()
      } else if (status === 'disconnecting') {
        logger.log('检测到断开中状态，恢复断开轮询...')
        this.startDisconnectPolling()
      }
    },

    // 开始加载中
    startLoading(text) {
      if (this.loadingInstance) {
        return
      }
      this.loadingInstance = Loading.service({
        fullscreen: true,
        text: text
      })
    },
    // 结束加载中
    stopLoading() {
      if (this.loadingInstance) {
        this.loadingInstance.close()
        this.loadingInstance = null
      }
    },

    // 查看详情
    async handleViewDetails() {
      try {
        logger.log('打开接入详情...')
        await agentApi.openAccessDetail()
        logger.log('接入详情打开成功')
      } catch (error) {
        console.error('打开接入详情失败:', error)
        Message.error('打开详情失败，请重试')
      }
    },

    // 清理轮询定时器
    clearPolling() {
      let cleared = false
      if (this.statusPollingTimer) {
        clearInterval(this.statusPollingTimer)
        this.statusPollingTimer = null
        cleared = true
      }
      if (this.pollingTimeout) {
        clearTimeout(this.pollingTimeout)
        this.pollingTimeout = null
        cleared = true
      }
      if (cleared) {
        logger.log('轮询定时器已清理')
      }
    },

    // 获取按钮文案
    getButtonText() {
      if (this.connectionStatus === 'connecting') {
        return '正在连接'
      } else if (this.connectionStatus === 'disconnecting') {
        return '正在断开'
      } else if (this.connectionStatus === 'connected') {
        if (this.isHovering) {
          return '断开连接'
        } else {
          return '连接成功'
        }
      } else {
        return '一键连接'
      }
    },

    // 获取按钮样式类
    getButtonClass() {
      return {
        'btn-disconnected': this.connectionStatus === 'disconnected',
        'btn-connecting': this.connectionStatus === 'connecting',
        'btn-disconnecting': this.connectionStatus === 'disconnecting',
        'btn-connected': this.connectionStatus === 'connected' && !this.isHovering,
        'btn-disconnect': this.connectionStatus === 'connected' && this.isHovering
      }
    },

    // 获取按钮内联样式
    getButtonStyle() {
      if (this.connectionStatus === 'disconnected') {
        return {
          backgroundColor: '#626aef',
          borderColor: '#626aef',
          color: '#ffffff'
        }
      } else if (this.connectionStatus === 'connecting') {
        return {
          backgroundColor: '#a5b4fc',
          borderColor: '#a5b4fc',
          color: '#ffffff'
        }
      } else if (this.connectionStatus === 'disconnecting') {
        return {
          backgroundColor: '#fca5a5',
          borderColor: '#fca5a5',
          color: '#ffffff'
        }
      } else if (this.connectionStatus === 'connected') {
        if (this.isHovering) {
          return {
            backgroundColor: '#ef4444',
            borderColor: '#ef4444',
            color: '#ffffff'
          }
        } else {
          return {
            backgroundColor: '#29cc65',
            borderColor: '#29cc65',
            color: '#ffffff'
          }
        }
      }
      return {}
    },

    // 处理连接按钮点击
    async handleConnect() {
      if (this.connectionStatus === 'connected') {
        // 断开连接
        await this.disconnect()
      } else if (this.connectionStatus === 'disconnected') {
        // 开始连接
        await this.connect()
      }
      // 连接中或断开中状态不响应点击
    },

    // 连接方法
    async connect() {
      try {
        logger.log('开始连接隧道...')
        // 移除全屏Loading，改为按钮状态显示
        // this.startLoading('正在连接中...')

        // 调用连接API
        await agentApi.connectTunnel()
        logger.log('连接API调用成功，开始轮询状态...')

        // 设置连接中状态
        this.updateConnectionStatus(101)

        // 开始轮询状态，等待连接成功
        this.startConnectPolling()
      } catch (error) {
        console.error('连接失败:', error)
        this.updateConnectionStatus(102)
        Message.error('连接失败，会话已超时')
        logger.log('连接失败，会话已超时，执行退出登录')
        try {
          await this.userStore.ClearStorage()
          this.userStore.LoginOut()
          logger.log('连接失败，服务器注销登录成功')
        } catch (error) {
          console.error('连接失败，服务器注销登录失败:', error)
        }

        // 跳转到客户端登录页面
        this.router.push({
          name: 'ClientNewLogin',
          query: agentApi.getClientParams()
        })
      }
    },

    // 开始连接状态轮询
    startConnectPolling() {
      // 清理之前的定时器
      this.clearPolling()

      let pollCount = 0
      const maxPolls = 30 // 30秒 每秒1次

      this.statusPollingTimer = setInterval(async() => {
        try {
          pollCount++
          logger.log(`连接状态轮询第${pollCount}次...`)

          const response = await agentApi.getChannelStatus()
          logger.log('轮询状态响应:', response)

          if (response && response.TunState === 100) {
            // 连接成功
            logger.log('连接成功！')
            this.clearPolling()
            this.updateConnectionStatus(100)
            // Message.success('连接成功')
            return
          }

          // 检查是否超时
          if (pollCount >= maxPolls) {
            logger.log('连接超时，恢复到未连接状态')
            this.clearPolling()
            this.updateConnectionStatus(102)
            // Message.error('连接超时，请重试')
          }
        } catch (error) {
          console.error('轮询状态失败:', error)
          pollCount++

          if (pollCount >= maxPolls) {
            this.clearPolling()
            this.updateConnectionStatus(102)
            Message.error('连接失败，请重试')
          }
        }
      }, 1000) // 每1秒轮询一次
    },

    // 断开连接方法
    async disconnect() {
      try {
        logger.log('开始断开隧道连接...')
        // 移除全屏Loading，改为按钮状态显示
        // this.startLoading('断开连接中...')

        // 调用断开连接API
        await agentApi.disconnectTunnel()
        logger.log('断开连接API调用成功，开始轮询状态...')

        // 设置断开中状态
        this.updateConnectionStatus(103)

        // 开始轮询状态，等待断开成功
        this.startDisconnectPolling()
      } catch (error) {
        console.error('断开连接失败:', error)
        Message.error('断开连接失败')
      }
    },

    // 开始断开连接状态轮询
    startDisconnectPolling() {
      // 清理之前的定时器
      this.clearPolling()

      let pollCount = 0
      const maxPolls = 10 // 10秒 / 每秒1次

      this.statusPollingTimer = setInterval(async() => {
        try {
          pollCount++
          logger.log(`断开连接状态轮询第${pollCount}次...`)

          const response = await agentApi.getChannelStatus()
          logger.log('轮询状态响应:', response)

          if (response && response.TunState === 102) {
            // 断开成功
            logger.log('断开连接成功！')
            this.clearPolling()
            this.updateConnectionStatus(102)
            // Message.success('已断开连接')
            return
          }

          // 检查是否超时
          if (pollCount >= maxPolls) {
            logger.log('断开连接超时，恢复到已连接状态')
            this.clearPolling()
            this.updateConnectionStatus(100)
            Message.error('断开连接超时')
          }
        } catch (error) {
          console.error('轮询状态失败:', error)
          pollCount++

          if (pollCount >= maxPolls) {
            this.clearPolling()
            this.updateConnectionStatus(100)
            Message.error('断开连接失败')
          }
        }
      }, 1000) // 每1秒轮询一次
    },

    // 鼠标进入事件
    handleMouseEnter() {
      if (this.connectionStatus === 'connected') {
        this.isHovering = true
      }
    },

    // 鼠标离开事件
    handleMouseLeave() {
      this.isHovering = false
    },

    // 检查是否需要自动连接（只在打开页面时执行，页面内切换不执行）
    checkAutoConnect() {
      // 检查是否已经执行过自动连接检查（使用sessionStorage，会话期间有效）
      const autoConnectChecked = sessionStorage.getItem('autoConnectChecked')
      if (autoConnectChecked === 'true') {
        logger.log('本次会话已执行过自动连接检查，跳过')
        return
      }
      // 定时100毫秒后检查自动连接条件，避免页面加载时触发检查
      setTimeout(async() => {
        try {
          logger.log('检查自动连接条件：', autoConnectChecked)

          // 判断当前连接状态为未连接
          if (this.connectionStatus !== 'disconnected') {
            logger.log('当前连接状态不是未连接，跳过自动连接检查')
            // 标记已执行过检查
            sessionStorage.setItem('autoConnectChecked', 'true')
            return
          }

          // 判断是否为登录后自动连接
          if (autoConnectChecked === 'login') {
            logger.log('登录后进行自动连接')
            await this.connect()
            // 标记已执行过检查
            sessionStorage.setItem('autoConnectChecked', 'true')
            return
          }

          // 获取客户端配置，检查"启动后自动连接"开关
          const clientConfig = await agentApi.getClientConfig()
          logger.log('客户端配置:', clientConfig)

          // 检查AutoConnectAfterStartup配置
          let autoConnectAfterStartup = true // 默认值
          if (clientConfig && clientConfig.AutoConnectAfterStartup !== undefined) {
            autoConnectAfterStartup = clientConfig.AutoConnectAfterStartup
          }

          logger.log('启动后自动连接配置:', autoConnectAfterStartup)

          if (autoConnectAfterStartup === true) {
            logger.log('启动后自动连接开关已开启，开始自动连接...')
            // 自动点击一键连接
            await this.connect()
          } else {
            logger.log('启动后自动连接开关未开启，跳过自动连接')
          }

          // 标记已执行过检查
          sessionStorage.setItem('autoConnectChecked', 'true')
        } catch (error) {
          console.error('检查自动连接失败:', error)
          // 即使出错也标记已执行过，避免重复尝试
          sessionStorage.setItem('autoConnectChecked', 'true')
        }
      }, 100) // 100毫秒后执行
    },

    // 处理来自app组件的隧道状态刷新请求
    async handleTunnelStatusRefresh(eventData) {
      logger.log('收到隧道状态刷新请求:', eventData)

      // 检查是否包含客户端主动通知的数据
      if (eventData.data && eventData.data.TunState !== undefined) {
        const newTunState = eventData.data.TunState
        const currentStatus = this.connectionStatus

        logger.log('客户端主动通知隧道状态变化:', {
          newTunState,
          currentStatus,
          source: '客户端主动通知'
        })

        // 1. 如果状态和当前状态一样，就不更新
        const currentTunStateMap = {
          'disconnected': 102,
          'connecting': 101,
          'connected': 100,
          'disconnecting': 103
        }

        if (currentTunStateMap[currentStatus] === newTunState) {
          logger.log('状态未变化，跳过更新')
          return
        }

        // 2. 如果收到102（已断开）
        if (newTunState === 102) {
          if (currentStatus === 'disconnecting') {
            // 当前状态是103（断开中），说明是手动断开完成
            logger.log('手动断开完成，清理轮询，不触发自动重连')
            this.clearPolling()
            this.updateConnectionStatus(newTunState)
          } else {
            // 当前状态不是103，说明是异常断开
            logger.log('检测到异常断开，更新状态并触发自动重连')
            this.updateConnectionStatus(newTunState)

            // 触发自动重连（如果启用）
            if (this.autoReconnectEnabled) {
              this.handleAutoReconnect()
            }
          }
        } else {
          // 其他状态直接更新
          this.updateConnectionStatus(newTunState)
        }
      } else {
        // 如果没有具体的状态数据，则刷新状态
        logger.log('手动刷新隧道状态')
        await this.updateTunnelStatus({
          useCache: false, // 强制刷新，绕过缓存
          clearPolling: true, // 清理当前轮询
          restorePolling: true, // 恢复轮询（如果是中间状态）
          showSuccessMessage: false, // 不显示成功消息
          logPrefix: '手动刷新隧道状态'
        })
      }
    },

    // 设置准入状态变更事件监听
    setupAgentStatusListener() {
      try {
        // 创建事件处理器
        this.agentStatusChangedHandler = (event) => {
          logger.log('收到准入状态变更事件 agentStatusChanged:', event.detail)

          // 从事件详情中提取设备状态
          const deviceStatus = event.detail?.deviceStatus
          if (typeof deviceStatus !== 'undefined') {
            logger.log('从事件中提取到设备状态:', deviceStatus)

            // 更新设备状态：1=已入网，0=未入网
            this.deviceStatus = deviceStatus === 1

            logger.log('设备状态已更新:', {
              原始值: deviceStatus,
              转换后: this.deviceStatus,
              显示文本: this.deviceStatus ? '已入网' : '未入网'
            })
          } else {
            logger.warn('无法从事件数据中提取设备状态:', event.detail)
          }
        }

        // 监听 window 上的自定义事件
        window.addEventListener('agentStatusChanged', this.agentStatusChangedHandler)
        logger.log('准入状态变更事件监听已设置')
      } catch (error) {
        console.error('设置准入状态变更事件监听失败:', error)
      }
    },

    // 清理准入状态变更事件监听
    cleanupAgentStatusListener() {
      try {
        if (this.agentStatusChangedHandler) {
          window.removeEventListener('agentStatusChanged', this.agentStatusChangedHandler)
          this.agentStatusChangedHandler = null
          logger.log('准入状态变更事件监听已清理')
        }
      } catch (error) {
        console.error('清理准入状态变更事件监听失败:', error)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.access-main {
  padding: 16px 16px 16px 0px;
  width: 100%;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  border-left: 16px solid #f2f2f6;
  box-sizing: border-box;
  border-image: linear-gradient(to right, #fcfcfc, #fafafa, #ffffff);
  background-color: #fcfcfc;

  .content-wrapper {
    margin: 0px;
    padding: 0; /* 清除ul默认的padding */
    list-style: none; /* 清除ul默认的list-style */
    width: 100%;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .access-proxy-status {
      height: 102px;
      background-size: cover;
      position: relative;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 4px;

      // 根据连接状态动态设置背景图片
      &.disconnected {
        background: url('@/assets/noproxy_background.png') no-repeat center center;
        background-size: cover;
      }

      &.connected {
        background: url('@/assets/proxy_background.png') no-repeat center center;
        background-size: cover;
      }

      &.connecting, &.disconnecting {
        background: url('@/assets/noproxy_background.png') no-repeat center center;
        background-size: cover;
      }

      .access-proxy-status-text {
        position: absolute;
        font-size: 16px;
        font-weight: 600;
        height: 22px;
        width: 64px;
        top: 12px;
        left: 16px;
      }

      .access-proxy-status-span {
        position: absolute;
        left: 50%;
        transform: translateX(-260px);
        width: 300px;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .access-proxy-status-tips {
          display: flex;
          align-items: center;
          justify-content: center;

          .success-icon {
            width: 14px;
            height: 14px;
            margin-right: 6px;
          }
        }

        .access-proxy-status-btn {
          padding: 0px;
          margin-top: 10px;
          height: 35px;
          width: 167px;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.3s ease;
          cursor: pointer;
          border: none !important;
          outline: none !important;
          box-shadow: 0px 2px 8px 0px rgba(46,60,128,0.10);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 6px; // 使用margin替代gap

          // QT环境特殊修复
          &:focus {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
          }

          &:active {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
          }

          // 移除所有可能的默认样式
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
          }

          // 未连接状态
          &.btn-disconnected {
            background-color: #626aef !important;
            border: 1px solid #626aef !important;
            color: #ffffff !important;
            outline: none !important;
            box-shadow: none !important;

            &:hover {
              background-color: #5a63e8 !important;
              border: 1px solid #5a63e8 !important;
              outline: none !important;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            }

            &:focus {
              outline: none !important;
              border: 1px solid #626aef !important;
              box-shadow: none !important;
            }

            &:active {
              outline: none !important;
              border: 1px solid #626aef !important;
              box-shadow: none !important;
            }
          }

          // 连接中状态
          &.btn-connecting {
            background-color: #a5b4fc !important;
            border: 1px solid #a5b4fc !important;
            color: #ffffff !important;
            cursor: not-allowed;
            outline: none !important;
            box-shadow: none !important;

            &:hover {
              transform: none;
              box-shadow: none !important;
              background-color: #a5b4fc !important;
              border: 1px solid #a5b4fc !important;
              outline: none !important;
            }

            &:focus {
              outline: none !important;
              border: 1px solid #a5b4fc !important;
              box-shadow: none !important;
            }

            &:active {
              outline: none !important;
              border: 1px solid #a5b4fc !important;
              box-shadow: none !important;
            }
          }

          // 连接成功状态
          &.btn-connected {
            background-color: #29cc65 !important;
            border: 1px solid #29cc65 !important;
            color: #ffffff !important;
            outline: none !important;
            box-shadow: none !important;

            &:hover {
              background-color: #16a34a !important;
              border: 1px solid #16a34a !important;
              outline: none !important;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            }

            &:focus {
              outline: none !important;
              border: 1px solid #29cc65 !important;
              box-shadow: none !important;
            }

            &:active {
              outline: none !important;
              border: 1px solid #29cc65 !important;
              box-shadow: none !important;
            }
          }

          // 断开连接状态（悬停时）
          &.btn-disconnect {
            background-color: #ef4444 !important;
            border: 1px solid #ef4444 !important;
            color: #ffffff !important;
            outline: none !important;
            box-shadow: none !important;

            &:hover {
              background-color: #ff4d4d !important;
              border: 1px solid #ff4d4d !important;
              outline: none !important;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            }

            &:focus {
              outline: none !important;
              border: 1px solid #ef4444 !important;
              box-shadow: none !important;
            }

            &:active {
              outline: none !important;
              border: 1px solid #ef4444 !important;
              box-shadow: none !important;
            }
          }

          // 加载图标样式
          .loading-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            margin-right: 6px;

            .spinner {
              width: 16px;
              height: 16px;
              animation: spin 1s linear infinite;
            }
          }
        }
      }
    }

    .access-common-status {
      height: 41px;
      font-size: 13px;
      font-weight: 400;
      display: flex;
      align-items: center; /* 垂直居中 */
      justify-content: space-between; /* 左右贴边布局 */
      margin-top: 12px;
      background-color: #fff;
      box-sizing: border-box;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 4px;

      .access-common-status-span {
        margin-left: 16px;
      }
      .access-common-status-detail {
        margin-right: 16px;
        color: #536ce6;
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: #3370ff;
          text-decoration: underline;
        }
      }
    }

    .access-app {
      flex: 1;
      min-height: 0;
      font-size: 13px;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      margin-top: 12px;
      background-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      overflow: hidden;
      .access-app-page {
        width: 100%;
        height: 100%;
        flex: 1;
        min-height: 0;
      }
    }

    // 不展示准入状态时
    .access-app-no-access {
      flex: 1;
      min-height: 0;
    }
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
