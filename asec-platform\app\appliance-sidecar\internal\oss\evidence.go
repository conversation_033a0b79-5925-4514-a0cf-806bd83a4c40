package oss

import (
	"asdsec.com/asec/platform/app/appliance-sidecar/common"
	"asdsec.com/asec/platform/app/appliance-sidecar/global"
	"asdsec.com/asec/platform/app/appliance-sidecar/global/connection"
	"asdsec.com/asec/platform/app/appliance-sidecar/internal/agent_conf"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/dustin/go-humanize"
	"google.golang.org/grpc"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

const (
	Duration               = 3
	OssUploadType          = "oss-upload"
	DelOssUploadType       = "oss-del-upload"
	Limit                  = 500
	ScreenShotEvidenceType = "screenshot"
	RetainFileEvidenceType = "retainfile"
	SpecialConfigType      = "CONFIG"
	SpecialConfigKey       = "OSS_CONFIG"
	MaxUploadFileSize      = 50 * 1000 * 1024
	MaxCleanFileCacheSize  = 50 * 1000 * 1024
	MaxNoEvidenceFileSize  = 200 * 1000 * 1024
	TemporaryOssType       = "temp"
	NoneOssType            = "none"
	TemporaryBucketPrefix  = "asec_evidence"
	EvidenceBucketPrefix   = "evidence"
	FileBucketPath         = "file"
	ImageBucketPath        = "image"
	FileCachePath          = "C:\\ProgramData\\Asec\\filecache"
)

var evidenceQuerySql = `select id,uuid,evidence_type,file_path,file_real_name,occur_time,save_time,file_size  
						from tb_file_evidence where id > ? order by id ASC limit ?`

type FileEvidence struct {
	Id           int64
	Uuid         string
	EvidenceType string
	FilePath     string
	FileRealName string
	OccurTime    int64
	SaveTime     int64
	FileSize     int64
}

func UploadFile(ctx context.Context, wg *sync.WaitGroup) {
	param := common.SendParam{
		Ctx:              ctx,
		Wg:               wg,
		DoStreamSendFunc: doUploadFile,
		RunType:          common.StreamSend,
		GetConnFun:       connection.GetLogCenterConnection,
		WaitSecond:       Duration,
		RandomOffset:     2,
	}
	// 定时轮询删除文件和sqlLite里面的记录
	go cleanFileEvidence(30, 5000, 3000)
	common.Send(param)
}

func doUploadFile(conn *grpc.ClientConn, ctx context.Context, wg *sync.WaitGroup) error {
	if !common.DdrSwitch {
		return nil
	}
	// 获取取证offset
	currentOffset, err := getEvidenceCurrentOffset(global.EvidenceSqliteClient, OssUploadType)
	if err != nil {
		global.Logger.Sugar().Errorln("get current offset for oss upload err:%v", err)
		return err
	}
	// 获取取证事件
	stmt, err := global.EvidenceSqliteClient.Prepare(evidenceQuerySql)
	if stmt != nil {
		defer stmt.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("doSendEvents SqliteClient Prepare:%v", err)
		return err
	}
	rows, err := stmt.Query(currentOffset, Limit)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("stmt Query file evidences err:%v", err)
		return err
	}
	// 从配置中心获得oss配置
	ossConfig, err := agent_conf.GetOssConf(conn)
	if err != nil {
		global.Logger.Sugar().Warnf("get oss conf error : %v", err)
		return err
	}
	if ossConfig.OssType == NoneOssType || ossConfig.Enable == 0 {
		return nil
	}
	awsConfig := StructAwsConfig(ossConfig)
	s3Service, err := GetOssS3Service(awsConfig, ossConfig.Key, ossConfig.Random)
	if err != nil {
		global.Logger.Sugar().Errorf("get oss service error : %v", err)
		return err
	}

	// 获取Oss文件路径
	ossFilePathPrefix := EvidenceBucketPrefix
	if ossConfig.OssType == TemporaryOssType {
		ossFilePathPrefix = fmt.Sprintf("%s_%s/%s", TemporaryBucketPrefix, ossConfig.CorpId, EvidenceBucketPrefix)
	}
	var fileEvidenceList []FileEvidence
	for rows.Next() {
		var evidence FileEvidence
		err = rows.Scan(&evidence.Id, &evidence.Uuid, &evidence.EvidenceType, &evidence.FilePath,
			&evidence.FileRealName, &evidence.OccurTime, &evidence.SaveTime, &evidence.FileSize)
		if err != nil {
			global.Logger.Sugar().Errorf("stmt Scan userId err:%v", err)
			return err
		}
		fileEvidenceList = append(fileEvidenceList, evidence)
	}
	for _, evidence := range fileEvidenceList {
		// 初始化文件上传配置
		uploadConfig := UploadConfig{
			Bucket:         ossConfig.Bucket,
			OriginFilePath: evidence.FilePath, //当前需要上传的文件绝对路径
		}
		// 时间格式化
		timeStr := time.Unix(evidence.OccurTime, 0).Format("20060102")
		switch evidence.EvidenceType {
		case RetainFileEvidenceType:
			// 设置上传文件路径
			uploadConfig.OssFilePath = fmt.Sprintf("%s/%s/%s/%s/%s", ossFilePathPrefix, FileBucketPath, timeStr, evidence.Uuid, evidence.FileRealName)
			// 文件上传content-type
			uploadConfig.ContentType = FileContentType
			break
		case ScreenShotEvidenceType:
			// 截取时间戳
			filePathList := strings.Split(evidence.FilePath, ScreenShotEvidenceType)
			var fileName string
			if len(filePathList) > 0 {
				fileName = strings.Split(filePathList[len(filePathList)-1], "_")[0]
				fileName = strings.Replace(fileName, "\\", "", -1)
			}
			// 设置上传截图路径
			uploadConfig.OssFilePath = fmt.Sprintf("%s/%s/%s/%s/%s.png", ossFilePathPrefix, ImageBucketPath, timeStr, evidence.Uuid, fileName)
			// 图片上传content-type
			uploadConfig.ContentType = ImageContentType
			break
		default:
			global.Logger.Sugar().Errorf("get not support evidence type: %s", evidence.EvidenceType)
			return errors.New("not support evidence type")
		}
		//上传文件/图片
		err = UploadFileToOss(s3Service, uploadConfig, evidence.FileSize)
		if err != nil && !errors.Is(err, os.ErrNotExist) {
			global.Logger.Sugar().Warnf("upload file failed:%v ,s3 endpoint: %s", err, s3Service.Endpoint)
			return err
		}
		if errors.Is(err, os.ErrNotExist) {
			global.Logger.Sugar().Warnf("upload file failed:%v", err)
		}
		// update offset
		err = updateEvidenceOffset(global.EvidenceSqliteClient, evidence.Id, OssUploadType)
		if err != nil {
			global.Logger.Sugar().Errorln("updateSqlLite offset failed:%v", err)
			return err
		}
	}
	return nil
}

func UploadFileToOss(s3 *s3.S3, uploadConfig UploadConfig, fileSize int64) error {
	// 分段上传
	if fileSize > MaxUploadFileSize {
		return UploadByPart(s3, uploadConfig, fileSize, MaxUploadFileSize)
	}
	// 直接上传
	return Upload(s3, uploadConfig)
}

func getEvidenceCurrentOffset(cli *sql.DB, offsetType string) (int64, error) {
	query, err := cli.Prepare("select current_offset from tb_events_offset where event_type = ?")
	defer global.CloseStatement(query)
	if err != nil {
		return 0, err
	}
	var offset int64
	err = query.QueryRow(offsetType).Scan(&offset)
	if err != nil {
		//记录不存在
		if err == sql.ErrNoRows {
			//插入的错误忽略掉
			_, err = cli.Exec("INSERT into tb_events_offset (current_offset,event_type) values (? ,?)", 0, offsetType)
			global.Logger.Sugar().Errorf("INSERT init offset err %v", err)
			return 0, nil
		}
		return 0, err
	}

	return offset, err
}

func getEvidenceCurrentOffsetNoInsert(cli *sql.DB, offsetType string) (int64, error) {
	query, err := cli.Prepare("select current_offset from tb_events_offset where event_type = ?")
	defer global.CloseStatement(query)
	if err != nil {
		return 0, err
	}
	var offset int64
	err = query.QueryRow(offsetType).Scan(&offset)
	if err != nil {
		//记录不存在
		global.Logger.Sugar().Errorf("del groutine get offset err %v", err)
		return 0, err
	}

	return offset, err
}

func updateEvidenceOffset(cli *sql.DB, offset int64, offsetType string) error {
	prepare, err := cli.Prepare("update tb_events_offset set current_offset = ? where event_type = ?")
	defer global.CloseStatement(prepare)
	if err != nil {
		return err
	}

	_, err = prepare.Exec(offset, offsetType)
	return err
}

func updateOffsetTx(cli *sql.Tx, offset int64) error {
	prepare, err := cli.Prepare("update tb_events_offset set current_offset = ? where event_type = ?")
	defer global.CloseStatement(prepare)
	if err != nil {
		return err
	}

	_, err = prepare.Exec(offset, OssUploadType)
	return err
}

/*
1.cleanFileEvidence 定时清理evidence
2.cleanSecond 扫描间隔/秒
3.keepMaxCounts 删除阈值
*/
func cleanFileEvidence(cleanSecond int64, keepMaxCounts int64, keepCount int64) {
	var err error
	for {
		var count int64
		var nowOffset int64
		var removeIndex int64
		var committedOffset int64
		var size int64
		if !common.DdrSwitch {
			goto WAIT
		}
		// 当前事件表数量
		count, err = selectOneCount(global.EvidenceSqliteClient, "select count(1) from tb_file_evidence")
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvidence getCurrentCount err:%v", err)
			goto WAIT
		}

		// 已提交位移
		committedOffset, err = getEvidenceCurrentOffsetNoInsert(global.EvidenceSqliteClient, OssUploadType)
		if err != nil && err != sql.ErrNoRows {
			global.Logger.Sugar().Errorln("cleanFileEvidence getCurrentOffset err:%v", err)
			goto WAIT
		}

		// 当前已经缓存的文件总大小超过最大存储数量
		size, err = getFileCacheSize(FileCachePath)
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvidence getTotalCount err:%v", err)
			goto WAIT
		}
		if size >= MaxCleanFileCacheSize {
			global.Logger.Sugar().Infof("start clean file cache,nowFileSize=%v,commitOffset:%v",
				humanize.Bytes(uint64(size)), committedOffset)
			err = deleteFileCacheFile(global.EvidenceSqliteClient, committedOffset)
			if err != nil {
				global.Logger.Sugar().Errorln("cleanFileCache err:%v", err)
				goto WAIT
			}
		}

		// 根据sqlite 记录删除过后
		size, err = getFileCacheSize(FileCachePath)
		if size >= MaxNoEvidenceFileSize {
			global.Logger.Sugar().Infof("File size is still larger than 200MB ,nowFileSize=%v,commitOffset:%v",
				humanize.Bytes(uint64(size)), committedOffset)
			err = delFilePathWhenNotWork()
			if err != nil {
				global.Logger.Sugar().Errorln("cleanFileCache err:%v", err)
				goto WAIT
			}
		}

		if count < keepMaxCounts {
			// no need del sql lite
			goto WAIT
		}

		// 最新事件表位移
		nowOffset, err = selectOneCount(global.EvidenceSqliteClient, "select id from tb_file_evidence order by id desc limit 1")
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvidence getTotalCount err:%v", err)
			goto WAIT
		}

		// 超过最大数量直接删除
		removeIndex = nowOffset - keepCount
		global.Logger.Sugar().Infof("start clean file evidence sqllite,nowFileSize=%v,count:%v,removeIndex:%v,keepMaxCounts:%v,keepCount:%v",
			humanize.Bytes(uint64(size)), count, removeIndex, keepMaxCounts, keepCount)
		err = deleteEvidence(global.EvidenceSqliteClient, removeIndex, committedOffset)
		if err != nil {
			global.Logger.Sugar().Errorln("cleanFileEvidence deleteEvent err:%v", err)
		}

	WAIT:
		time.Sleep(time.Second * time.Duration(cleanSecond))
	}
}

func deleteFileCacheFile(cli *sql.DB, committedOffset int64) error {
	delOffset, err := getEvidenceCurrentOffset(cli, DelOssUploadType)
	if err != nil {
		return err
	}
	oneMinuteBeforeTimestamp := time.Now().Add(time.Minute * -1).Unix()
	// 获取需要删除的文件路径
	needDeleteFileList, err := getNeedDeleteFilePath(cli, delOffset, committedOffset, oneMinuteBeforeTimestamp)
	if err != nil {
		global.Logger.Sugar().Errorln("getNeedDeleteFilePath err:%v", err)
		return err
	}
	// 删除文件
	err = deleteFileByFilePath(cli, needDeleteFileList)
	if err != nil {
		return err
	}
	return nil
}
func delFilePathWhenNotWork() error {
	// 文件目录
	filePathList := []string{
		"C:\\ProgramData\\Asec\\filecache\\file",
		"C:\\ProgramData\\Asec\\filecache\\screenshot",
	}
	// 文件过滤时间,只保留10分钟内创建的文件
	cutoff := time.Now().Add(-10 * time.Minute)
	for _, filePath := range filePathList {
		err := filepath.Walk(filePath, func(filename string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// 判断文件是否超过10分钟
			if info.ModTime().Before(cutoff) {
				// 删除过期文件
				os.Remove(filename)
			}

			return nil
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// 删除取证
func deleteEvidence(cli *sql.DB, removeIndex int64, committedOffset int64) error {
	tx, err := cli.BeginTx(context.Background(), &sql.TxOptions{Isolation: sql.LevelSerializable})
	if err != nil {
		return err
	}
	oneMinuteBeforeTimestamp := time.Now().Add(time.Minute * -1).Unix()
	// 删除数据
	exec, err := tx.Exec("delete from tb_file_evidence where id<=? and save_time<?", removeIndex, oneMinuteBeforeTimestamp)
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	affected, err := exec.RowsAffected()
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	// 如果删除了还未提交记录,则更新已提交offset
	if committedOffset < removeIndex {
		err = updateOffsetTx(tx, removeIndex)
		if err != nil {
			global.Logger.Sugar().Errorln("updateOffset err:%v", err)
			_ = tx.Rollback()
			return err
		}
	}
	err = tx.Commit()
	if err != nil {
		global.Logger.Sugar().Errorln("deleteEvidence commit err:%v", err)
		return err
	}
	global.Logger.Sugar().Infof("clean file evidence counts:%v", affected)
	return nil
}

func selectOneCount(cli *sql.DB, sql string) (int64, error) {
	var count int64
	err := cli.QueryRow(sql).Scan(&count)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// 获取需要删除的文件绝对路径
func getNeedDeleteFilePath(cli *sql.DB, delOffset, offset int64, threeMinuteBeforeTimestamp int64) ([]FileEvidence, error) {
	query, err := cli.Prepare("select id, file_path from tb_file_evidence where id <= ? and id > ? and save_time<?")
	defer global.CloseStatement(query)
	if err != nil {
		return nil, err
	}
	rows, err := query.Query(offset, delOffset, threeMinuteBeforeTimestamp)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil {
		global.Logger.Sugar().Errorln("stmt Query file evidence err:%v", err)
		return nil, err
	}
	var ret []FileEvidence
	for rows.Next() {
		var filePath FileEvidence
		err = rows.Scan(&filePath.Id, &filePath.FilePath)
		if err != nil {
			global.Logger.Sugar().Errorln("scan row of file evidence err:%v", err)
			return nil, err
		}
		ret = append(ret, filePath)
	}
	return ret, nil
}

// 删除文件根据文件路径
func deleteFileByFilePath(cli *sql.DB, filePath []FileEvidence) error {
	var deleteFileOffset int64
	for _, path := range filePath {
		if _, err := os.Stat(path.FilePath); os.IsNotExist(err) {
			deleteFileOffset = path.Id
			continue
		} else {
			err := os.Remove(path.FilePath)
			if err != nil {
				updateOffsetErr := updateEvidenceOffset(cli, deleteFileOffset, DelOssUploadType)
				if err != nil {
					global.Logger.Sugar().Errorf("update del file failed, deleteFileOffset:%d, err:%v", deleteFileOffset, updateOffsetErr)
				}
				global.Logger.Sugar().Infof("delete file failed, file_path:%s, err:%v", path.FilePath, err)
				return err
			}
			deleteFileOffset = path.Id
		}
	}
	return updateEvidenceOffset(cli, deleteFileOffset, DelOssUploadType)
}

func getFileCacheSize(dirname string) (int64, error) {
	op, err := filepath.Abs(dirname) //获取目录的绝对路径
	if nil != err {
		fmt.Println(err)
		return 0, err
	}
	files, err := ioutil.ReadDir(op) //获取目录下所有文件的信息，包括文件和文件夹
	if nil != err {
		fmt.Println(err)
		return 0, err
	}

	var fileSize int64 //返回值，存储读取的文件信息
	for _, f := range files {
		if f.IsDir() { // 如果是目录，那么就递归调用
			fs, err := getFileCacheSize(op + `/` + f.Name()) //路径分隔符，linux 和 windows 不同
			if err != nil {
				return 0, err
			}
			fileSize = fileSize + fs //将 slice 添加到 slice
		} else {
			fileSize = fileSize + f.Size()
		}
	}

	return fileSize, nil
}
