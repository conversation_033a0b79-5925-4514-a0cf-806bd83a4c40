package common

import (
	"context"
	"crypto/rand"
	"time"

	"github.com/go-redis/redis/v8"
)

type GrantType string

const (
	AuthCodeLength               = 16                                                               // 授权码长度
	AuthCodeExpires              = time.Minute * 5                                                  // 授权码过期时间
	AuthCodeChars                = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789" // 授权码字符集
	GrantTypeAuthzCode GrantType = "authorization_code"
)

const OperateUpdate = "UPDATE"
const OperateCreate = "CREATE"
const OperateDelete = "DELETE"
const OperateUnlock = "UNLOCK"
const OperateKickSession = "KICK_SESSION"
const OperateUnlockIP = "UNLOCK_IP"
const UserType = "USER"
const UserGroupType = "USERGROUP"
const RoleMangerType = "ROLEMANGER"
const AuthPolicyType = "AUTHPOLICY"
const IdpType = "IDP"
const SessionType = "SESSION"
const IPType = "IP"

func generateAuthCode() (string, error) {
	// 生成随机授权码
	authCode := make([]byte, AuthCodeLength)
	_, err := rand.Read(authCode)
	if err != nil {
		return "", err
	}

	// 使用字符集将字节数组转换为字符串
	for i := 0; i < AuthCodeLength; i++ {
		authCode[i] = AuthCodeChars[authCode[i]%byte(len(AuthCodeChars))]
	}

	return string(authCode), nil
}

// GenerateUniqueAuthCode 生成一个唯一的随机 code
func GenerateUniqueAuthCode(ctx context.Context, client *redis.Client) (string, error) {
	code, err := generateAuthCode()
	if err != nil {
		return "", err
	}
	exists, err := client.Exists(ctx, code).Result()
	if err != nil {
		return "", err
	}
	// 如果生成的随机 Code 已存在，则递归生成直到生成唯一的随机 Code
	if exists == 1 {
		return GenerateUniqueAuthCode(ctx, client)
	}
	return code, nil
}
