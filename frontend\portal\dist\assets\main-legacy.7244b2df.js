/*! 
 Build based on gin-vue-admin 
 Time : 1754993243000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,o,a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",c=a.toStringTag||"@@toStringTag";function r(t,a,s,c){var r=a&&a.prototype instanceof u?a:u,l=Object.create(r.prototype);return e(l,"_invoke",function(t,e,a){var s,c,r,u=0,l=a||[],p=!1,g={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(t,e){return s=t,c=0,r=n,g.n=e,i}};function d(t,e){for(c=t,r=e,o=0;!p&&u&&!a&&o<l.length;o++){var a,s=l[o],d=g.p,f=s[2];t>3?(a=f===e)&&(r=s[(c=s[4])?5:(c=3,3)],s[4]=s[5]=n):s[0]<=d&&((a=t<2&&d<s[1])?(c=0,g.v=e,g.n=s[1]):d<f&&(a=t<3||s[0]>e||e>f)&&(s[4]=t,s[5]=e,g.n=f,c=0))}if(a||t>1)return i;throw p=!0,e}return function(a,l,f){if(u>1)throw TypeError("Generator is already running");for(p&&1===l&&d(l,f),c=l,r=f;(o=c<2?n:r)||!p;){s||(c?c<3?(c>1&&(g.n=-1),d(c,r)):g.n=r:g.v=r);try{if(u=2,s){if(c||(a="next"),o=s[a]){if(!(o=o.call(s,r)))throw TypeError("iterator result is not an object");if(!o.done)return o;r=o.value,c<2&&(c=0)}else 1===c&&(o=s.return)&&o.call(s),c<2&&(r=TypeError("The iterator does not provide a '"+a+"' method"),c=1);s=n}else if((o=(p=g.n<0)?r:t.call(e,g))!==i)break}catch(o){s=n,c=1,r=o}finally{u=1}}return{value:o,done:p}}}(t,s,c),!0),l}var i={};function u(){}function l(){}function p(){}o=Object.getPrototypeOf;var g=[][s]?o(o([][s]())):(e(o={},s,(function(){return this})),o),d=p.prototype=u.prototype=Object.create(g);function f(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,e(t,c,"GeneratorFunction")),t.prototype=Object.create(d),t}return l.prototype=p,e(d,"constructor",p),e(p,"constructor",l),l.displayName="GeneratorFunction",e(p,c,"GeneratorFunction"),e(d),e(d,c,"Generator"),e(d,s,(function(){return this})),e(d,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:r,m:f}})()}function e(t,n,o,a){var s=Object.defineProperty;try{s({},"",{})}catch(t){s=0}e=function(t,n,o,a){if(n)s?s(t,n,{value:o,enumerable:!a,configurable:!a,writable:!a}):t[n]=o;else{var c=function(n,o){e(t,n,(function(t){return this._invoke(n,o,t)}))};c("next",0),c("throw",1),c("return",2)}},e(t,n,o,a)}function n(t,e,n,o,a,s,c){try{var r=t[s](c),i=r.value}catch(t){return void n(t)}r.done?e(i):Promise.resolve(i).then(o,a)}function o(t){return function(){var e=this,o=arguments;return new Promise((function(a,s){var c=t.apply(e,o);function r(t){n(c,a,s,r,i,"next",t)}function i(t){n(c,a,s,r,i,"throw",t)}r(void 0)}))}}System.register(["./index-legacy.43fdb757.js","./index-legacy.b871e767.js"],(function(e,n){"use strict";var a,s,c,r,i,u,l,p,g,d,f,h,m,x,b,v,w,S,y,k,C=document.createElement("style");return C.textContent='@charset "UTF-8";.access-main[data-v-0099c131]{padding:16px 16px 16px 0;width:100%;flex:1;min-height:0;display:flex;flex-direction:column;border-left:16px solid #f2f2f6;box-sizing:border-box;border-image:linear-gradient(to right,#fcfcfc,#fafafa,#ffffff);background-color:#fcfcfc}.access-main .content-wrapper[data-v-0099c131]{margin:0;padding:0;list-style:none;width:100%;flex:1;min-height:0;display:flex;flex-direction:column}.access-main .content-wrapper .access-proxy-status[data-v-0099c131]{height:102px;background-size:cover;position:relative;box-shadow:0 2px 8px rgba(0,0,0,.1);border-radius:4px}.access-main .content-wrapper .access-proxy-status.disconnected[data-v-0099c131]{background:url('+new URL("noproxy_background.906c3206.png",n.meta.url).href+") no-repeat center center;background-size:cover}.access-main .content-wrapper .access-proxy-status.connected[data-v-0099c131]{background:url("+new URL("proxy_background.51cd78fd.png",n.meta.url).href+") no-repeat center center;background-size:cover}.access-main .content-wrapper .access-proxy-status.connecting[data-v-0099c131],.access-main .content-wrapper .access-proxy-status.disconnecting[data-v-0099c131]{background:url("+new URL("noproxy_background.906c3206.png",n.meta.url).href+") no-repeat center center;background-size:cover}.access-main .content-wrapper .access-proxy-status .access-proxy-status-text[data-v-0099c131]{position:absolute;font-size:16px;font-weight:600;height:22px;width:64px;top:12px;left:16px}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span[data-v-0099c131]{position:absolute;left:50%;transform:translate(-260px);width:300px;height:100px;display:flex;flex-direction:column;align-items:center;justify-content:center}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-tips[data-v-0099c131]{display:flex;align-items:center;justify-content:center}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-tips .success-icon[data-v-0099c131]{width:14px;height:14px;margin-right:6px}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn[data-v-0099c131]{padding:0;margin-top:10px;height:35px;width:167px;border-radius:4px;font-size:14px;font-weight:500;transition:all .3s ease;cursor:pointer;border:none!important;outline:none!important;box-shadow:0 2px 8px rgba(46,60,128,.1);display:flex;align-items:center;justify-content:center;margin-right:6px;-webkit-appearance:none;-moz-appearance:none;appearance:none}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn[data-v-0099c131]:focus{outline:none!important;border:none!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn[data-v-0099c131]:active{outline:none!important;border:none!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn[data-v-0099c131]:hover{transform:translateY(-1px);box-shadow:0 4px 12px rgba(0,0,0,.15)}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn[data-v-0099c131]:active{transform:translateY(0)}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnected[data-v-0099c131]{background-color:#626aef!important;border:1px solid #626aef!important;color:#fff!important;outline:none!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnected[data-v-0099c131]:hover{background-color:#5a63e8!important;border:1px solid #5a63e8!important;outline:none!important;box-shadow:0 4px 12px rgba(0,0,0,.15)!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnected[data-v-0099c131]:focus{outline:none!important;border:1px solid #626aef!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnected[data-v-0099c131]:active{outline:none!important;border:1px solid #626aef!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connecting[data-v-0099c131]{background-color:#a5b4fc!important;border:1px solid #a5b4fc!important;color:#fff!important;cursor:not-allowed;outline:none!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connecting[data-v-0099c131]:hover{transform:none;box-shadow:none!important;background-color:#a5b4fc!important;border:1px solid #a5b4fc!important;outline:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connecting[data-v-0099c131]:focus{outline:none!important;border:1px solid #a5b4fc!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connecting[data-v-0099c131]:active{outline:none!important;border:1px solid #a5b4fc!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connected[data-v-0099c131]{background-color:#29cc65!important;border:1px solid #29cc65!important;color:#fff!important;outline:none!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connected[data-v-0099c131]:hover{background-color:#16a34a!important;border:1px solid #16a34a!important;outline:none!important;box-shadow:0 4px 12px rgba(0,0,0,.15)!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connected[data-v-0099c131]:focus{outline:none!important;border:1px solid #29cc65!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-connected[data-v-0099c131]:active{outline:none!important;border:1px solid #29cc65!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnect[data-v-0099c131]{background-color:#ef4444!important;border:1px solid #ef4444!important;color:#fff!important;outline:none!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnect[data-v-0099c131]:hover{background-color:#ff4d4d!important;border:1px solid #ff4d4d!important;outline:none!important;box-shadow:0 4px 12px rgba(0,0,0,.15)!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnect[data-v-0099c131]:focus{outline:none!important;border:1px solid #ef4444!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn.btn-disconnect[data-v-0099c131]:active{outline:none!important;border:1px solid #ef4444!important;box-shadow:none!important}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn .loading-icon[data-v-0099c131]{display:flex;align-items:center;justify-content:center;width:16px;height:16px;margin-right:6px}.access-main .content-wrapper .access-proxy-status .access-proxy-status-span .access-proxy-status-btn .loading-icon .spinner[data-v-0099c131]{width:16px;height:16px;animation:spin-0099c131 1s linear infinite}.access-main .content-wrapper .access-common-status[data-v-0099c131]{height:41px;font-size:13px;font-weight:400;display:flex;align-items:center;justify-content:space-between;margin-top:12px;background-color:#fff;box-sizing:border-box;box-shadow:0 2px 8px rgba(0,0,0,.1);border-radius:4px}.access-main .content-wrapper .access-common-status .access-common-status-span[data-v-0099c131]{margin-left:16px}.access-main .content-wrapper .access-common-status .access-common-status-detail[data-v-0099c131]{margin-right:16px;color:#536ce6;cursor:pointer;transition:color .2s ease}.access-main .content-wrapper .access-common-status .access-common-status-detail[data-v-0099c131]:hover{color:#3370ff;text-decoration:underline}.access-main .content-wrapper .access-app[data-v-0099c131]{flex:1;min-height:0;font-size:13px;font-weight:400;display:flex;flex-direction:column;margin-top:12px;background-color:#fff;box-shadow:0 2px 8px rgba(0,0,0,.1);border-radius:4px;overflow:hidden}.access-main .content-wrapper .access-app .access-app-page[data-v-0099c131]{width:100%;height:100%;flex:1;min-height:0}.access-main .content-wrapper .access-app-no-access[data-v-0099c131]{flex:1;min-height:0}@keyframes spin-0099c131{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n",document.head.appendChild(C),{setters:[function(t){a=t.default},function(t){s=t._,c=t.f,r=t.E,i=t.K,u=t.C,l=t.M,p=t.L,g=t.h,d=t.a,f=t.b,h=t.d,m=t.e,x=t.F,b=t.i,v=t.j,w=t.w,S=t.n,y=t.t,k=t.l}],execute:function(){var C=""+new URL("success.72d17a7a.svg",n.meta.url).href,T={name:"Access",components:{AppPage:a},data:function(){return{userStore:c(),router:r(),connectionStatus:"disconnected",isHovering:!1,loadingInstance:null,statusPollingTimer:null,pollingTimeout:null,showAccessStatus:!1,deviceStatus:!1,autoReconnectEnabled:!0,reconnectAttempts:0,maxReconnectAttempts:2,reconnectTimer:null,agentStatusChangedHandler:null}},watch:{$route:{handler:function(e,n){var a=this;return o(t().m((function o(){var s,c,r,i;return t().w((function(t){for(;;)switch(t.n){case 0:if(!(e.path&&e.path.includes("main")&&n&&n.path!==e.path)){t.n=8;break}if(s=a.userStore.tunState,logger.log("路由切换回主页面，检查状态缓存...",s),s&&0!==s){t.n=2;break}return logger.log("状态缓存无效，刷新连接状态..."),t.n=1,a.updateTunnelStatus({useCache:!1,clearPolling:!1,restorePolling:!1,showSuccessMessage:!1,logPrefix:"路由切换状态刷新"});case 1:t.n=8;break;case 2:if(101!==s&&103!==s){t.n=7;break}return logger.log("检测到中间状态，验证真实状态并恢复轮询..."),t.p=3,t.n=4,a.updateTunnelStatus({useCache:!1,clearPolling:!1,restorePolling:!1,showSuccessMessage:!1,logPrefix:"验证中间状态"});case 4:(c=t.v)&&void 0!==c.TunState&&(r=c.TunState,logger.log("真实状态:",r,"缓存状态:",s),r===s&&(101===s?(logger.log("恢复连接状态轮询..."),a.startConnectPolling()):103===s&&(logger.log("恢复断开连接状态轮询..."),a.startDisconnectPolling()))),t.n=6;break;case 5:t.p=5,i=t.v,console.error("验证状态失败:",i),101===s?a.updateConnectionStatus(102):103===s&&a.updateConnectionStatus(100);case 6:t.n=8;break;case 7:logger.log("使用有效的状态缓存，跳过刷新"),a.updateConnectionStatus(s);case 8:return t.a(2)}}),o,null,[[3,5]])})))()},immediate:!1}},mounted:function(){var e=this;return o(t().m((function n(){return t().w((function(t){for(;;)switch(t.n){case 0:return logger.log("获取用户信息"),e.userStore.GetUserInfo(),t.n=1,e.loadInitialStatus();case 1:return t.n=2,e.loadAccessStatus();case 2:e.checkAndRestorePolling(),e.checkAutoConnect(),i.on("refreshTunnelStatus",e.handleTunnelStatusRefresh),e.setupAgentStatusListener();case 3:return t.a(2)}}),n)})))()},beforeUnmount:function(){this.clearPolling(),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.reconnectAttempts=0,i.off("refreshTunnelStatus",this.handleTunnelStatusRefresh),this.cleanupAgentStatusListener()},methods:{updateTunnelStatus:function(){var e=arguments,n=this;return o(t().m((function o(){var a,s,c,r,i,p,g,d,f,h,m,x,b;return t().w((function(t){for(;;)switch(t.n){case 0:if(s=(a=e.length>0&&void 0!==e[0]?e[0]:{}).useCache,c=void 0===s||s,r=a.clearPolling,i=void 0!==r&&r,p=a.restorePolling,g=void 0!==p&&p,d=a.showSuccessMessage,f=void 0!==d&&d,h=a.logPrefix,m=void 0===h?"更新隧道状态":h,t.p=1,logger.log("".concat(m,"...")),!c){t.n=5;break}if(x={TunState:n.userStore.tunState},logger.log("缓存状态:",x.TunState),x.TunState&&0!==x.TunState){t.n=3;break}return logger.log("缓存无效，调用 API 获取最新状态..."),t.n=2,u.getChannelStatus();case 2:x=t.v,t.n=4;break;case 3:logger.log("使用缓存状态数据");case 4:t.n=7;break;case 5:return logger.log("强制从API获取最新状态..."),t.n=6,u.getChannelStatus();case 6:x=t.v;case 7:if(logger.log("隧道状态响应:",x),!x||void 0===x.TunState){t.n=8;break}return i&&n.clearPolling(),n.updateConnectionStatus(x.TunState),g&&(101===x.TunState?(logger.log("检测到连接中状态，重新启动连接轮询..."),n.startConnectPolling()):103===x.TunState&&(logger.log("检测到断开中状态，重新启动断开轮询..."),n.startDisconnectPolling())),logger.log("隧道状态已更新为:",x.TunState),f&&l.success("状态已刷新"),t.a(2,x);case 8:return t.a(2,null);case 9:throw t.p=9,b=t.v,console.error("".concat(m,"失败:"),b),f&&l.error("刷新状态失败，请重试"),c&&n.updateConnectionStatus(102),b;case 10:return t.a(2)}}),o,null,[[1,9]])})))()},loadInitialStatus:function(){var e=this;return o(t().m((function n(){var o;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,t.n=1,e.updateTunnelStatus({useCache:!0,clearPolling:!1,restorePolling:!1,showSuccessMessage:!1,logPrefix:"加载通道初始状态"});case 1:(o=t.v)&&o.Token&&logger.log("获取到登录token信息"),t.n=3;break;case 2:t.p=2,t.v;case 3:return t.a(2)}}),n,null,[[0,2]])})))()},loadAccessStatus:function(){var e=this;return o(t().m((function n(){var o,a;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,logger.log("加载接入状态..."),t.n=1,u.getAccessStatus();case 1:o=t.v,logger.log("接入状态响应:",o),o&&(e.showAccessStatus=1===o.IsShowAccess,e.deviceStatus=1===o.DeviceStatus,logger.log("接入状态设置:",{showAccessStatus:e.showAccessStatus,deviceStatus:e.deviceStatus})),t.n=3;break;case 2:t.p=2,a=t.v,console.error("获取接入状态失败:",a),e.showAccessStatus=!1,e.deviceStatus=!1;case 3:return t.a(2)}}),n,null,[[0,2]])})))()},updateConnectionStatus:function(t){switch(logger.log("更新连接状态，TunState:",t,"当前状态:",this.connectionStatus),this.userStore.setTunState(t),t){case 100:this.connectionStatus="connected",this.reconnectAttempts=0,this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null);break;case 101:this.connectionStatus="connecting";break;case 102:this.connectionStatus="disconnected";break;case 103:this.connectionStatus="disconnecting";break;default:console.warn("未知的隧道状态:",t),this.connectionStatus="disconnected",userStore.setTunState(102)}},handleAutoReconnect:function(){var e=this;if(this.reconnectAttempts>=this.maxReconnectAttempts)return logger.log("已达到最大重连次数，停止自动重连"),void(this.reconnectAttempts=0);this.reconnectAttempts++,logger.log("开始第".concat(this.reconnectAttempts,"次自动重连..."));var n=Math.min(2e3*this.reconnectAttempts,1e4);this.reconnectTimer=setTimeout(o(t().m((function n(){var o;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,logger.log("执行第".concat(e.reconnectAttempts,"次自动重连")),t.n=1,e.connect();case 1:t.n=3;break;case 2:t.p=2,o=t.v,console.error("第".concat(e.reconnectAttempts,"次自动重连失败:"),o),e.reconnectAttempts>=e.maxReconnectAttempts&&(e.reconnectAttempts=0);case 3:return t.a(2)}}),n,null,[[0,2]])}))),n)},checkAndRestorePolling:function(){var t=this.connectionStatus;logger.log("检查当前状态是否需要恢复轮询:",t),"connecting"===t?(logger.log("检测到连接中状态，恢复连接轮询..."),this.startConnectPolling()):"disconnecting"===t&&(logger.log("检测到断开中状态，恢复断开轮询..."),this.startDisconnectPolling())},startLoading:function(t){this.loadingInstance||(this.loadingInstance=p.service({fullscreen:!0,text:t}))},stopLoading:function(){this.loadingInstance&&(this.loadingInstance.close(),this.loadingInstance=null)},handleViewDetails:function(){return o(t().m((function e(){var n;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,logger.log("打开接入详情..."),t.n=1,u.openAccessDetail();case 1:logger.log("接入详情打开成功"),t.n=3;break;case 2:t.p=2,n=t.v,console.error("打开接入详情失败:",n),l.error("打开详情失败，请重试");case 3:return t.a(2)}}),e,null,[[0,2]])})))()},clearPolling:function(){var t=!1;this.statusPollingTimer&&(clearInterval(this.statusPollingTimer),this.statusPollingTimer=null,t=!0),this.pollingTimeout&&(clearTimeout(this.pollingTimeout),this.pollingTimeout=null,t=!0),t&&logger.log("轮询定时器已清理")},getButtonText:function(){return"connecting"===this.connectionStatus?"正在连接":"disconnecting"===this.connectionStatus?"正在断开":"connected"===this.connectionStatus?this.isHovering?"断开连接":"连接成功":"一键连接"},getButtonClass:function(){return{"btn-disconnected":"disconnected"===this.connectionStatus,"btn-connecting":"connecting"===this.connectionStatus,"btn-disconnecting":"disconnecting"===this.connectionStatus,"btn-connected":"connected"===this.connectionStatus&&!this.isHovering,"btn-disconnect":"connected"===this.connectionStatus&&this.isHovering}},getButtonStyle:function(){return"disconnected"===this.connectionStatus?{backgroundColor:"#626aef",borderColor:"#626aef",color:"#ffffff"}:"connecting"===this.connectionStatus?{backgroundColor:"#a5b4fc",borderColor:"#a5b4fc",color:"#ffffff"}:"disconnecting"===this.connectionStatus?{backgroundColor:"#fca5a5",borderColor:"#fca5a5",color:"#ffffff"}:"connected"===this.connectionStatus?this.isHovering?{backgroundColor:"#ef4444",borderColor:"#ef4444",color:"#ffffff"}:{backgroundColor:"#29cc65",borderColor:"#29cc65",color:"#ffffff"}:{}},handleConnect:function(){var e=this;return o(t().m((function n(){return t().w((function(t){for(;;)switch(t.n){case 0:if("connected"!==e.connectionStatus){t.n=2;break}return t.n=1,e.disconnect();case 1:t.n=3;break;case 2:if("disconnected"!==e.connectionStatus){t.n=3;break}return t.n=3,e.connect();case 3:return t.a(2)}}),n)})))()},connect:function(){var e=this;return o(t().m((function n(){var o,a;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,logger.log("开始连接隧道..."),t.n=1,u.connectTunnel();case 1:logger.log("连接API调用成功，开始轮询状态..."),e.updateConnectionStatus(101),e.startConnectPolling(),t.n=7;break;case 2:return t.p=2,o=t.v,console.error("连接失败:",o),e.updateConnectionStatus(102),l.error("连接失败，会话已超时"),logger.log("连接失败，会话已超时，执行退出登录"),t.p=3,t.n=4,e.userStore.ClearStorage();case 4:e.userStore.LoginOut(),logger.log("连接失败，服务器注销登录成功"),t.n=6;break;case 5:t.p=5,a=t.v,console.error("连接失败，服务器注销登录失败:",a);case 6:e.router.push({name:"ClientNewLogin",query:u.getClientParams()});case 7:return t.a(2)}}),n,null,[[3,5],[0,2]])})))()},startConnectPolling:function(){var e=this;this.clearPolling();var n=0;this.statusPollingTimer=setInterval(o(t().m((function o(){var a,s;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,n++,logger.log("连接状态轮询第".concat(n,"次...")),t.n=1,u.getChannelStatus();case 1:if(a=t.v,logger.log("轮询状态响应:",a),!a||100!==a.TunState){t.n=2;break}return logger.log("连接成功！"),e.clearPolling(),e.updateConnectionStatus(100),t.a(2);case 2:n>=30&&(logger.log("连接超时，恢复到未连接状态"),e.clearPolling(),e.updateConnectionStatus(102)),t.n=4;break;case 3:t.p=3,s=t.v,console.error("轮询状态失败:",s),++n>=30&&(e.clearPolling(),e.updateConnectionStatus(102),l.error("连接失败，请重试"));case 4:return t.a(2)}}),o,null,[[0,3]])}))),1e3)},disconnect:function(){var e=this;return o(t().m((function n(){var o;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,logger.log("开始断开隧道连接..."),t.n=1,u.disconnectTunnel();case 1:logger.log("断开连接API调用成功，开始轮询状态..."),e.updateConnectionStatus(103),e.startDisconnectPolling(),t.n=3;break;case 2:t.p=2,o=t.v,console.error("断开连接失败:",o),l.error("断开连接失败");case 3:return t.a(2)}}),n,null,[[0,2]])})))()},startDisconnectPolling:function(){var e=this;this.clearPolling();var n=0;this.statusPollingTimer=setInterval(o(t().m((function o(){var a,s;return t().w((function(t){for(;;)switch(t.n){case 0:return t.p=0,n++,logger.log("断开连接状态轮询第".concat(n,"次...")),t.n=1,u.getChannelStatus();case 1:if(a=t.v,logger.log("轮询状态响应:",a),!a||102!==a.TunState){t.n=2;break}return logger.log("断开连接成功！"),e.clearPolling(),e.updateConnectionStatus(102),t.a(2);case 2:n>=10&&(logger.log("断开连接超时，恢复到已连接状态"),e.clearPolling(),e.updateConnectionStatus(100),l.error("断开连接超时")),t.n=4;break;case 3:t.p=3,s=t.v,console.error("轮询状态失败:",s),++n>=10&&(e.clearPolling(),e.updateConnectionStatus(100),l.error("断开连接失败"));case 4:return t.a(2)}}),o,null,[[0,3]])}))),1e3)},handleMouseEnter:function(){"connected"===this.connectionStatus&&(this.isHovering=!0)},handleMouseLeave:function(){this.isHovering=!1},checkAutoConnect:function(){var e=this,n=sessionStorage.getItem("autoConnectChecked");"true"!==n?setTimeout(o(t().m((function o(){var a,s,c;return t().w((function(t){for(;;)switch(t.n){case 0:if(t.p=0,logger.log("检查自动连接条件：",n),"disconnected"===e.connectionStatus){t.n=1;break}return logger.log("当前连接状态不是未连接，跳过自动连接检查"),sessionStorage.setItem("autoConnectChecked","true"),t.a(2);case 1:if("login"!==n){t.n=3;break}return logger.log("登录后进行自动连接"),t.n=2,e.connect();case 2:return sessionStorage.setItem("autoConnectChecked","true"),t.a(2);case 3:return t.n=4,u.getClientConfig();case 4:if(a=t.v,logger.log("客户端配置:",a),s=!0,a&&void 0!==a.AutoConnectAfterStartup&&(s=a.AutoConnectAfterStartup),logger.log("启动后自动连接配置:",s),!0!==s){t.n=6;break}return logger.log("启动后自动连接开关已开启，开始自动连接..."),t.n=5,e.connect();case 5:t.n=7;break;case 6:logger.log("启动后自动连接开关未开启，跳过自动连接");case 7:sessionStorage.setItem("autoConnectChecked","true"),t.n=9;break;case 8:t.p=8,c=t.v,console.error("检查自动连接失败:",c),sessionStorage.setItem("autoConnectChecked","true");case 9:return t.a(2)}}),o,null,[[0,8]])}))),100):logger.log("本次会话已执行过自动连接检查，跳过")},handleTunnelStatusRefresh:function(e){var n=this;return o(t().m((function o(){var a,s;return t().w((function(t){for(;;)switch(t.n){case 0:if(logger.log("收到隧道状态刷新请求:",e),!e.data||void 0===e.data.TunState){t.n=2;break}if(a=e.data.TunState,s=n.connectionStatus,logger.log("客户端主动通知隧道状态变化:",{newTunState:a,currentStatus:s,source:"客户端主动通知"}),{disconnected:102,connecting:101,connected:100,disconnecting:103}[s]!==a){t.n=1;break}return logger.log("状态未变化，跳过更新"),t.a(2);case 1:102===a?"disconnecting"===s?(logger.log("手动断开完成，清理轮询，不触发自动重连"),n.clearPolling(),n.updateConnectionStatus(a)):(logger.log("检测到异常断开，更新状态并触发自动重连"),n.updateConnectionStatus(a),n.autoReconnectEnabled&&n.handleAutoReconnect()):n.updateConnectionStatus(a),t.n=3;break;case 2:return logger.log("手动刷新隧道状态"),t.n=3,n.updateTunnelStatus({useCache:!1,clearPolling:!0,restorePolling:!0,showSuccessMessage:!1,logPrefix:"手动刷新隧道状态"});case 3:return t.a(2)}}),o)})))()},setupAgentStatusListener:function(){var t=this;try{this.agentStatusChangedHandler=function(e){var n;logger.log("收到准入状态变更事件 agentStatusChanged:",e.detail);var o=null===(n=e.detail)||void 0===n?void 0:n.deviceStatus;void 0!==o?(logger.log("从事件中提取到设备状态:",o),t.deviceStatus=1===o,logger.log("设备状态已更新:",{"原始值":o,"转换后":t.deviceStatus,"显示文本":t.deviceStatus?"已入网":"未入网"})):logger.warn("无法从事件数据中提取设备状态:",e.detail)},window.addEventListener("agentStatusChanged",this.agentStatusChangedHandler),logger.log("准入状态变更事件监听已设置")}catch(e){console.error("设置准入状态变更事件监听失败:",e)}},cleanupAgentStatusListener:function(){try{this.agentStatusChangedHandler&&(window.removeEventListener("agentStatusChanged",this.agentStatusChangedHandler),this.agentStatusChangedHandler=null,logger.log("准入状态变更事件监听已清理"))}catch(t){console.error("清理准入状态变更事件监听失败:",t)}}}},P={class:"access-main"},A={class:"content-wrapper"},I={class:"access-proxy-status-span"},L={class:"access-proxy-status-tips"},j={key:0,class:"loading-icon"},R={key:0,class:"access-common-status"},M={class:"access-common-status-span"};e("default",s(T,[["render",function(t,e,n,o,a,s){var c=g("base-button"),r=g("AppPage");return d(),f("div",P,[h("ul",A,[h("li",{class:m(["access-proxy-status",a.connectionStatus])},[e[4]||(e[4]=h("span",{class:"access-proxy-status-text"}," 连接状态 ",-1)),h("span",I,[h("span",L,["connected"===a.connectionStatus?(d(),f(x,{key:0},[e[1]||(e[1]=h("img",{src:C,alt:"成功",class:"success-icon"},null,-1)),e[2]||(e[2]=b(" 已建立安全连接 "))],64)):(d(),f(x,{key:1},[b(" 点击连接，即可安全便捷地访问应用 ")],64))]),v(c,{class:m(["access-proxy-status-btn",s.getButtonClass()]),style:S(s.getButtonStyle()),onClick:s.handleConnect,onMouseenter:s.handleMouseEnter,onMouseleave:s.handleMouseLeave},{default:w((function(){return["connecting"===a.connectionStatus||"disconnecting"===a.connectionStatus?(d(),f("span",j,e[3]||(e[3]=[h("svg",{class:"spinner",viewBox:"0 0 24 24"},[h("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2",fill:"none","stroke-dasharray":"31.416","stroke-dashoffset":"31.416"},[h("animate",{attributeName:"stroke-dasharray",dur:"2s",values:"0 31.416;15.708 15.708;0 31.416",repeatCount:"indefinite"}),h("animate",{attributeName:"stroke-dashoffset",dur:"2s",values:"0;-15.708;-31.416",repeatCount:"indefinite"})])],-1)]))):k("",!0),b(" "+y(s.getButtonText()),1)]})),_:1},8,["class","style","onClick","onMouseenter","onMouseleave"])])],2),a.showAccessStatus?(d(),f("li",R,[h("span",M,[e[5]||(e[5]=h("span",null,"准入状态（企业网络下使用）：",-1)),h("span",{style:S({color:a.deviceStatus?"#29cc65":"#ef4444",marginLeft:"8px"})},y(a.deviceStatus?"已入网":"未入网"),5)]),h("span",{class:"access-common-status-detail",onClick:e[0]||(e[0]=function(){return s.handleViewDetails&&s.handleViewDetails.apply(s,arguments)})},e[6]||(e[6]=[h("span",null,"查看详情",-1)]))])):k("",!0),h("li",{class:m(["access-app",{"access-app-no-access":!a.showAccessStatus}])},[v(r,{class:"access-app-page","is-connected":"connected"===a.connectionStatus},null,8,["is-connected"])],2)])])}],["__scopeId","data-v-0099c131"]]))}}}))}();
