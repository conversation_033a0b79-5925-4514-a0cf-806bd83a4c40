# 登录同步功能使用说明

## 概述

登录同步功能实现了浏览器和客户端之间的登录状态实时同步，确保用户在任一页面登录或退出后，所有页面都能自动同步状态。

## 功能特性

### 🔄 **实时同步**
- 任一页面登录成功后，所有页面自动跳转到工作台
- 任一页面退出登录后，所有页面自动退出并跳转到登录页
- 客户端和浏览器之间的状态完全同步

### 🔗 **持续连接**
- 每个页面都与客户端50001端口保持WebSocket连接
- 自动重连机制确保连接稳定性
- 页面级别的唯一连接ID避免冲突

### 🛡️ **消息去重**
- 通过唯一ID避免处理自己发送的同步消息
- 防止消息循环和重复处理
- 确保同步逻辑的正确性

## 快速开始

### 1. 自动初始化

登录同步功能在应用启动时自动初始化，无需手动配置：

```javascript
// main.js 中已自动配置
import { initializeLoginSync } from '@/utils/loginSync'

setTimeout(async () => {
  try {
    await initializeLoginSync({
      port: 50001
    })
    logger.log('登录同步功能已启动')
  } catch (error) {
    logger.log('登录同步功能启动失败:', error)
  }
}, 1000)
```

### 2. 登录时自动广播

用户登录成功后，系统会自动广播登录信息：

```javascript
// 在 user store 的 LoginIn 方法中已集成
import { broadcastLogin } from '@/utils/loginSync'

// 登录成功后
const loginData = {
  accessToken: res.data.data.accessToken,
  refreshToken: res.data.data.refreshToken,
  userInfo: userInfo.value
}
broadcastLogin(loginData)
```

### 3. 退出时自动广播

用户退出登录时，系统会自动广播退出信息：

```javascript
// 在 user store 的 LoginOut 方法中已集成
import { broadcastLogout } from '@/utils/loginSync'

// 退出登录时
broadcastLogout({
  reason: 'user_logout',
  timestamp: Date.now()
})
```

## 消息循环防护

为了防止退出登录时的消息循环，系统实现了以下防护机制：

### 问题场景
```
用户A退出 -> 发送广播 -> 页面B收到广播 -> 退出并发送广播 -> 页面C收到广播 -> 退出并发送广播 -> 无限循环
```

### 解决方案
```javascript
// 用户主动退出（会发送广播）
await userStore.LoginOut()        // fromBroadcast = false (默认)
await userStore.LoginOut(false)   // 明确指定不是来自广播

// 广播通知的退出（不会发送广播）
await userStore.LoginOut(true)    // fromBroadcast = true
```

### 防护流程
1. 用户A主动退出：`LoginOut()` -> 发送广播
2. 页面B收到广播：调用 `LoginOut(true)` -> 不发送广播
3. 页面C收到广播：调用 `LoginOut(true)` -> 不发送广播
4. 循环终止，所有页面完成退出

## API 参考

### 初始化函数

#### `initializeLoginSync(options)`
初始化登录同步功能

**参数：**
- `options.port` (number): WebSocket端口，默认50001

**返回：** Promise

**示例：**
```javascript
await initializeLoginSync({ port: 50001 })
```

### 广播函数

#### `broadcastLogin(loginData)`
广播登录信息到所有连接

**参数：**
- `loginData.accessToken` (string): 访问令牌
- `loginData.refreshToken` (string): 刷新令牌
- `loginData.userInfo` (object): 用户信息

**返回：** boolean

#### `broadcastLogout(logoutData)`
广播退出信息到所有连接

**参数：**
- `logoutData.reason` (string): 退出原因
- `logoutData.timestamp` (number): 时间戳

**返回：** boolean

### 用户Store方法

#### `userStore.LoginOut(fromBroadcast)`
执行用户退出登录

**参数：**
- `fromBroadcast` (boolean, 可选): 是否来自广播通知，默认 `false`
  - `false`: 用户主动退出，会发送广播通知其他页面
  - `true`: 来自广播通知的退出，不会再发送广播（防止循环）

**返回：** Promise

**示例：**
```javascript
// 用户主动退出（会发送广播）
await userStore.LoginOut()

// 广播通知的退出（不会发送广播）
await userStore.LoginOut(true)
```

### 状态检查函数

#### `isLoginSyncInitialized()`
检查登录同步是否已初始化

**返回：** boolean

#### `destroyLoginSync()`
销毁登录同步功能

## 消息格式

### 登录同步消息
```javascript
{
  "action": 0,
  "msg": {
    "token": "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh-token-value",
    "userInfo": { "id": 1, "username": "user" },
    // 同步控制字段（首字母小写）
    "type": "broadcast",
    "action": "login",
    "senderId": "page-1704067200000-abc123def",
    "timestamp": 1704067200000,
    "source": "browser",
    "pageId": "page-1704067200000-abc123def"
  },
  "platform": "asec.infogo.net.cn"
}
```

### 退出同步消息
```javascript
{
  "action": 1,
  "msg": {
    // 同步控制字段（首字母小写）
    "type": "broadcast",
    "action": "logout",
    "senderId": "page-1704067200000-abc123def",
    "timestamp": 1704067200000,
    "reason": "user_logout",
    "source": "browser",
    "pageId": "page-1704067200000-abc123def"
  },
  "platform": "asec.infogo.net.cn"
}
```

## 工作流程

### 登录同步流程
1. 用户在页面A输入账号密码登录
2. 登录成功后，页面A调用 `broadcastLogin()` 发送登录信息
3. 客户端接收到登录信息，转发给所有连接的浏览器页面
4. 其他页面（B、C、D等）接收到登录信息
5. 各页面自动设置token和用户信息
6. 各页面自动跳转到工作台

### 退出同步流程
1. 用户在任一页面点击退出登录
2. 页面调用 `broadcastLogout()` 发送退出信息
3. 客户端接收到退出信息，转发给所有连接的浏览器页面
4. 所有页面接收到退出信息
5. 各页面调用 `LoginOut(true)` 执行退出（参数 `true` 表示来自广播，不再重复发送广播）
6. 各页面自动清除token和用户信息
7. 各页面自动跳转到登录页

### 消息循环防护
- `LoginOut(fromBroadcast)` 方法新增参数 `fromBroadcast`
- 当 `fromBroadcast = true` 时，表示是广播通知的退出，不再发送广播
- 当 `fromBroadcast = false` 或未传参时，表示用户主动退出，会发送广播
- 这样避免了广播消息的无限循环

## 测试和调试

### 使用测试工具
```javascript
import { runAllLoginSyncTests } from '@/utils/loginSync-test'

// 运行所有测试
const results = await runAllLoginSyncTests()
console.log('测试结果:', results)
```

### 使用演示页面
访问 `/test/login-sync-demo` 页面可以：
- 查看连接状态
- 测试登录/退出广播
- 查看同步消息日志
- 运行完整测试流程

### 调试技巧
1. 打开浏览器开发者工具查看WebSocket连接
2. 查看控制台日志了解同步过程
3. 使用演示页面测试各种场景
4. 检查页面连接ID确保唯一性

## 注意事项

### 客户端要求
- 客户端必须在50001端口提供WebSocket服务
- 客户端需要支持消息转发功能
- 客户端需要处理登录/退出同步消息

### 浏览器兼容性
- 支持现代浏览器的WebSocket功能
- 自动处理连接断开和重连
- 兼容多标签页环境

### 安全考虑
- 消息传输通过本地WebSocket，相对安全
- 建议在生产环境中添加消息加密
- 注意token的安全存储和传输

## 故障排除

### 常见问题

**Q: 登录同步不工作？**
A: 检查客户端50001端口是否正常，查看控制台是否有连接错误

**Q: 页面没有自动跳转？**
A: 检查路由配置，确保 'Workbench' 和 'Login' 路由存在

**Q: 收到重复的同步消息？**
A: 检查消息去重逻辑，确保SenderId正确设置

**Q: 连接频繁断开？**
A: 检查网络状况和客户端稳定性，调整重连参数

### 日志分析
查看控制台日志中的关键信息：
- `登录同步功能已启动` - 初始化成功
- `登录信息已广播到所有页面` - 登录广播成功
- `收到登录同步消息` - 接收到同步消息
- `忽略自己发送的同步消息` - 消息去重正常工作
