syntax = "proto3";
package api.asdsec.agentcontrol;
import "google/api/annotations.proto";
option go_package = "asdsec.com/asec/platform/api/agentcontrol/v1;v1";
// 定义服务
service AgentControl {
  // 拉取平台任务
  rpc PullTask(TaskPullReq)           returns(TaskPullResp) {}

  // 上报任务状态
  rpc ReportState(TaskStateReq)       returns(TaskStateResp) {}

  // 文件上传
  rpc FileUpload(FileUploadReq)       returns(FileUploadResp) {}

  // 获取平台地址
  rpc PullPlatAddr(Empty)             returns(PlatAddrResp) {}

  // 拉取模块开关
  rpc PullModuleSwitch(ModuleSwitchReq)   returns(ModuleSwitchResp) {}

  rpc GetModuleSwitch(ModuleSwitchReq)   returns(ModuleSwitchResp) {
    option (google.api.http) = {
      post: "appliance/v1/module_switch"
      body: "*"
    };
  }
}

// 任务类型
enum TaskType {
  UnknownType          = 0;
  LogUpload            = 1;    // 日志上报
}

// 任务状态
enum TaskState {
  UnknownState         = 0;
  NotPulledState       = 1;
  PulledState          = 2;
  ExecutingState       = 3;
  FinishedState        = 4;
  FailedState          = 5;
  TimeOutState         = 6;
}

enum AgentModule {
  UnknownAgentModule = 0;
  All                = 1;
  Ddr                = 2;
  Sidecar            = 3;
  Tun                = 4;
  UnloadVerify       = 5;
  HideUI             = 6;
  Plugin             = 7;
}

// 地址类型
enum AddrType {
  UnknownAddr          = 0;
  LogAddr              = 1;  // 日志地址
  AuthAddr             = 2;  // 认证地址
}

enum Response {
  UnknownResponse          = 0;
  RespOK                   = 1;
  RespFailed               = 2;
}

// 失败原因
enum TaskFailedReason {
  UnknownReason             = 0;
  TaskTimeOut               = 1;  // 任务超时
  TaskExecFailed            = 2;  // 终端执行失败
  NetTimeOut                = 3;  // 网络超时
  FileMiss                  = 4;  // 文件丢失
  FileCorruption            = 5;  // 文件损坏
  NoAuthority               = 6;  // 终端无访问权限
}

// 上报类型
enum UploadType {
  UnknownReport      = 0;
  LogFileUpload      = 1;    // 日志文件上报
}

enum ConfigType {
  UnknownConfig      = 0;
}

message Empty {
}

// 任务内容
message TaskContext {
  TaskType       type         = 1;
  string         task_id      = 2;
  AgentModule    agent_module = 3;
  bytes          task_info    = 4;
}

// 地址内容
message AddrContext {
  AddrType                     type  = 1;
  repeated      string         addr  = 2;
}

// 拉取任务请求
message TaskPullReq {
  uint64        agent_id  = 1;
}

// 拉取任务回复
message TaskPullResp {
  uint32                  result_code  = 1;
  repeated  TaskContext   task_context = 2;
}

// 上报任务状态请求
message TaskStateReq {
  string              task_id        = 1;
  uint64              agent_id       = 2;
  TaskState           task_state     = 3;
  TaskFailedReason    reason         = 4;
}

// 上报任务状态回复
message TaskStateResp {
  uint32              result_code = 1;
}

// 平台地址请求回复
message PlatAddrResp {
  uint32                  result_code = 1;
  repeated AddrContext    addr_ary = 2;
}

// 日志文件上传请求
message FileUploadReq {
  string          user_name      = 1;
  uint64          agent_id       = 2;
  int32           total_chunk    = 3;
  int32           chunk_index    = 4;
  uint64          chunk_size     = 5;
  string          file_name      = 6;
  UploadType      type           = 7;
  uint64          file_size      = 8;
  string          file_md5       = 9;
  bool            is_last_chunk  = 10;
  bytes           file_context   = 11;
}

// 日志文件上传回复
message FileUploadResp {
  uint32         result_code  = 1;
  int32          chunk_index  = 2;
}

message ModuleSwitchReq {
  uint64 agent_id = 1;
}

message ModuleSwitchResp {
  int32     code            = 1;
  repeated  ModuleData data = 2;
}

message ModuleData {
  AgentModule agent_module = 1;
  bool        module_value = 2;
}